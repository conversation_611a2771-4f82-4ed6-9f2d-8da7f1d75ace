﻿using System;

namespace ElectionAppServer.Model;

public class NamanigarPerformance
{
    public int NamanigarId { get; set; }
    public int ElectionPhaseId { get; set; }

    // Performance Metrics (1-10 scale)
    public int PreElectionPerformance { get; set; }
    public int Reachability { get; set; }
    public int DocumentEfficiency { get; set; }
    public int PreElectionInfoAccuracy { get; set; }
    public int ResultDayPerformance { get; set; }
    public int ResultAccuracy { get; set; }

    // Additional Fields
    public bool WasAvailableDuringResult { get; set; }
    public bool HasBehavioralIssues { get; set; }
    public string BehavioralIssueDescription { get; set; }
    public string Trivia { get; set; }

    public int? ReportingFor { get; set; }

    // Navigation Properties
    public virtual Namanigar Namanigar { get; set; }
    public virtual ElectionPhase ElectionPhase { get; set; }

    // Audit Fields
    public DateTime CreatedDate { get; set; }
    public string CreatedBy { get; set; }
    public DateTime? ModifiedDate { get; set; }
    public string ModifiedBy { get; set; }
}