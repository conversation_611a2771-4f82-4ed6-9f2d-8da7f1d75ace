﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ElectionAppServer.Data;
using ElectionAppServer.DTO;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.SignalR.Client;
using Syncfusion.Blazor.Popups;

namespace ElectionAppServer.Pages.Live;

public partial class OfflineResults
{

	private SfDialog _dlgForm;
	private List<ResultDTO> _results = new();
	private string _tickerText = "";
	
	
	private bool IsDlgVisible { get; set; }
	private LiveResultDetailDTO Detail { get; set; }
	
	[CascadingParameter] private Task<AuthenticationState> authenticationStateTask { get; set; }

	protected override async Task OnInitializedAsync()
	{
		IsDlgVisible = false;
		Detail = new LiveResultDetailDTO { Nominations = new List<LiveResultCandidateDetailDTO>() };

		
		


		

		try
		{
			_results = await Service.GetLiveResults(state.state.PhaseId);
		}
		catch (Exception)
		{
			// ignored
		}

		await this.InvokeAsync(this.StateHasChanged);
		//await Task.CompletedTask;
	}


	private async Task DisplayDetail(int assemblyId, int constituencyId, int seatTypeId)
	{
		//detail = await Service.GetLiveResultDetail(state.state.ElectionId, assemblyId, state.state.PhaseId, constituencyId, seatTypeId);
		_tickerText = await Service.GetLiveResultDetailLB(state.state.ElectionId, assemblyId, state.state.PhaseId,
			constituencyId, seatTypeId);
		await _dlgForm.ShowAsync();
	}

	private async Task MarkPublish(int electionId, int phaseId, int assemblyId, int constituencyId, int seatTypeId)
	{
		var user = (await authenticationStateTask).User;
		var msg = await Service.MarkPublish(electionId, phaseId, assemblyId, constituencyId, seatTypeId,
			user.Identity!.Name);
		if (msg == "OK")
		{
			var res = (from r in _results
						  where r.ConstituencyId == constituencyId &&
								  r.ElectionId == electionId &&
								  r.AssemblyId == assemblyId &&
								  r.PhaseId == phaseId
						  select r).FirstOrDefault();
			//res.Status = "Yes";
			if (res != null)
			{
				res.OnAir = true;
				res.Status = "Yes";
			}

			_results = (from aa in _results
							orderby aa.Status, aa.LastUpdatedOn descending
							select aa).ToList();
			var allowToPush = await confreader.GetSettingValue("AllowToPush");
			if (allowToPush == "Yes")
			{
				var immInfo = await Service.GetImmResult(electionId, phaseId, assemblyId, constituencyId, seatTypeId);
				await Translation.PostData(immInfo);
			}
		}

		await this.InvokeAsync(this.StateHasChanged);
	}


	protected override async Task OnAfterRenderAsync(bool firstRender)
	{
		if (firstRender)
		{
			var user = (await authenticationStateTask).User;
			if (user.Identity!.IsAuthenticated)
			{
				var dd = await localStorage.GetAsync<ElectionStateDTO>(await confreader.GetSettingValue("electionkey"));
				state.SetState(dd.Value);
				var info = await _UserManager.GetUserAsync(user);
				await Task.CompletedTask;

				StateHasChanged();
			}
			else
			{
				try
				{
					await localStorage.DeleteAsync(await confreader.GetSettingValue("electionkey"));
					state.SetState(new ElectionStateDTO
					{ ElectionId = 0, ElectionTitle = "", PhaseId = 0, PhaseTitle = "" });
					StateHasChanged();
				}
				catch (Exception)
				{
					// ignored
				}
			}
		}
	}
}