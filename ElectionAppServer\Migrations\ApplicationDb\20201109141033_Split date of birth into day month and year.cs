﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace ElectionAppServer.Migrations.ApplicationDb
{
	public partial class Splitdateofbirthintodaymonthandyear : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.AddColumn<int>(
				 name: "Day",
				 table: "Candidates",
				 type: "int",
				 nullable: true);

			migrationBuilder.AddColumn<int>(
				 name: "Month",
				 table: "Candidates",
				 type: "int",
				 nullable: true);

			migrationBuilder.AddColumn<int>(
				 name: "Year",
				 table: "Candidates",
				 type: "int",
				 nullable: true);
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropColumn(
				 name: "Day",
				 table: "Candidates");

			migrationBuilder.DropColumn(
				 name: "Month",
				 table: "Candidates");

			migrationBuilder.DropColumn(
				 name: "Year",
				 table: "Candidates");
		}
	}
}
