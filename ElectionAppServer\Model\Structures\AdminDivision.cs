﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace ElectionAppServer.Model.Structures;

public class AdminDivision
{
    public int Id { get; set; }
    public string EnglishTitle { get; set; }
    public string UrduTitle { get; set; }
    public int ProvinceId { get; set; }
    public virtual Province Province { get; set; }
    public virtual List<District> Districts { get; set; }

    #region Audit Log Fields

    public DateTime CreatedDate { get; set; } = DateTime.Now;
    public DateTime? ModifiedDate { get; set; }
    [StringLength(450)] public string CreatedBy { get; set; }
    [StringLength(450)] public string ModifiedBy { get; set; }

    #endregion Audit Log Fields
}