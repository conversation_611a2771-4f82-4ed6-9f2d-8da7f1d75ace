﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace ElectionAppServer.Migrations
{
	public partial class NamanigarElectionWise : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.AddColumn<int>(
				 name: "ElectionId",
				 table: "Namanigars",
				 nullable: false,
				 defaultValue: 0);

			migrationBuilder.AddColumn<string>(
				 name: "Email",
				 table: "Namanigars",
				 maxLength: 250,
				 nullable: true);

			migrationBuilder.AddColumn<string>(
				 name: "Mobile",
				 table: "Namanigars",
				 maxLength: 50,
				 nullable: true);

			migrationBuilder.CreateIndex(
				 name: "IX_Namanigars_ElectionId",
				 table: "Namanigars",
				 column: "ElectionId");

			migrationBuilder.AddForeignKey(
				 name: "FK_Namanigars_Elections_ElectionId",
				 table: "Namanigars",
				 column: "ElectionId",
				 principalTable: "Elections",
				 principalColumn: "Id",
				 onDelete: ReferentialAction.Restrict);
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropForeignKey(
				 name: "FK_Namanigars_Elections_ElectionId",
				 table: "Namanigars");

			migrationBuilder.DropIndex(
				 name: "IX_Namanigars_ElectionId",
				 table: "Namanigars");

			migrationBuilder.DropColumn(
				 name: "ElectionId",
				 table: "Namanigars");

			migrationBuilder.DropColumn(
				 name: "Email",
				 table: "Namanigars");

			migrationBuilder.DropColumn(
				 name: "Mobile",
				 table: "Namanigars");
		}
	}
}
