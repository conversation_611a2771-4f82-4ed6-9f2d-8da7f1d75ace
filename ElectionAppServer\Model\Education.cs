﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Runtime.Serialization;

namespace ElectionAppServer.Model;

public class Education
{
    public int Id { get; set; }
    public string UrduTitle { get; set; }
    public string EnglishTitle { get; set; }

    [IgnoreDataMember] public virtual List<Candidate> Candidates { get; set; }

    #region Audit Log Fields

    public DateTime CreatedDate { get; set; } = DateTime.Now;
    public DateTime? ModifiedDate { get; set; }

    [StringLength(450)] public string CreatedBy { get; set; }

    [StringLength(450)] public string ModifiedBy { get; set; }

    #endregion Audit Log Fields
}