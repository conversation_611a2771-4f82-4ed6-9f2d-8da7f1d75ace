﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Identity;

namespace ElectionAppServer.Model;

public class ApplicationUser : IdentityUser
{
    [StringLength(10)] public string Code { get; set; }

    [StringLength(10)] public string EmpCode { get; set; }

    [StringLength(200)] public string FullName { get; set; }

    [StringLength(500)] public string Address { get; set; }

    public int? Gender { get; set; }

    [StringLength(200)] public string District { get; set; }

    public virtual List<UserConstituency> UserConstituencies { get; set; }

    //public virtual List<ResultLog> AsNamanigarResultLogs { get; set; }
    //public virtual List<ResultLog> AsAppUserResultLogs { get; set; }
    //public virtual List<ConstituencyNamanigar> NamanigarConstituencies { get; set; }
    public bool IsActive { get; set; } = true;

    public string GenderText
    {
        get
        {
            if (Gender == 1) return "Male";
            if (Gender == 2) return "Female";
            if (Gender == 3) return "Transgender";
            return "";
        }
    }
}