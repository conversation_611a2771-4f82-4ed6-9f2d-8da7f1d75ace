﻿using System;
using System.ComponentModel.DataAnnotations;

namespace ElectionAppServer.DTO;

public class NamanigarDTO
{
    [Required] public int? Id { get; set; }

    [Required] public int ConstituencyId { get; set; }

    public string Code { get; set; }
    public string Name { get; set; }
    public string Phone { get; set; }
    public string Email { get; set; }

    [StringLength(500)] public string PSDetail { get; set; }

    public string District { get; set; }
}


public class NamanigarFormDTO
{
    public int Id { get; set; }

    [Required(ErrorMessage = "The Code field is required.")]
    [StringLength(20)]
    public string Code { get; set; }

    [Required(ErrorMessage = "The Name field is required.")]
    [StringLength(200)]
    public string Name { get; set; }

    [Required(ErrorMessage = "The primary phone number is required.")]
    [RegularExpression(@"^(\+92|0)3\d{2}\d{7}$", ErrorMessage = "Please enter a valid Pakistani mobile number (e.g., 03001234567).")]
    [StringLength(15)]
    public string Phone { get; set; }

    [RegularExpression(@"^(\+92|0)3\d{2}\d{7}$", ErrorMessage = "Please enter a valid Pakistani mobile number (e.g., 03001234567).")]
    [StringLength(15)]
    public string Phone2 { get; set; }

    
    [RegularExpression(@"^(\+92|0)3\d{2}\d{7}$", ErrorMessage = "Please enter a valid Pakistani mobile number (e.g., 03001234567).")]
    [StringLength(15)]
    public string Phone3 { get; set; }

    
    [RegularExpression(@"^(\+92|0)3\d{2}\d{7}$", ErrorMessage = "Please enter a valid Pakistani mobile number (e.g., 03001234567).")]
    [StringLength(15)]
    public string Phone4 { get; set; }

    // Enhanced email validation with a custom message
    //[EmailAddress(ErrorMessage = "Please enter a valid email address (e.g., <EMAIL>).")]
    [StringLength(300)]
    public string Email { get; set; }

    [Required(ErrorMessage = "The District is required.")]
    [StringLength(200)]
    public string District { get; set; }

    [StringLength(500)]
    public string Address { get; set; }
    public bool IsActive { get; set; } = true;
    public string Status => IsActive ? "Active" : "Inactive";
    public string IsInCurrentPhase { get; set; } = "";
    
}
public class NNResultsLogDTO
{
    public int Id { get; set; }
    public string Constituency { get; set; }
    public string Code { get; set; }
    public string Namanigar { get; set; }
    public string Action { get; set; }
    public int PS { get; set; }
    public DateTime Date { get; set; }
}

public class NNViewResultDTO
{
    public string Action { get; set; }
    public DateTime ActionDate { get; set; }
    public string Namanigar { get; set; }
    public int PS { get; set; }
    public string Candidate { get; set; }
    public string Party { get; set; }
    public int ChangeVotes { get; set; }
    public int Votes { get; set; }
    public int PSChange { get; set; }
}

public class NamanigarResultsListViewDTO
{
    public int BatchId { get; set; }
    public string Assembly { get; set; }
    public string Constituency { get; set; }
    public string Namanigar { get; set; }
    public string Action { get; set; }
    public DateTime ActionDate { get; set; }
    public int PS { get; set; }
    public int CurrentPS { get; set; }
    public int ConstituencyId { get; set; }
    public int SeatTypeId { get; set; }
    public string SeatType { get; set; }
    public string Status { get; set; }
    public bool IsViewed { get; set; }
}