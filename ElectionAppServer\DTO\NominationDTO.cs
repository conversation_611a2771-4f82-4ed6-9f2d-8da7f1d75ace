﻿using System;
using System.ComponentModel.DataAnnotations;
using ElectionAppServer.Model;
using FluentValidation;

namespace ElectionAppServer.DTO;

public class NominationDTO
{
    public int Id { get; set; }
    public string Assembly { get; set; }
    public int AssemblyId { get; set; }
    public string BilaMokabila { get; set; }
    public int? CandidateId { get; set; }
    public string CandidateName { get; set; }
    public string CandidateURL { get; set; }
    public string Constituency { get; set; }
    public int ConstituencyId { get; set; }
    public string CreatedBy { get; set; }
    public string CreatedOn { get; set; }
    public string Election { get; set; }
    public int ElectionId { get; set; }
    public string EnglishName { get; set; }
    public bool IsNew { get; set; } = true;
    public bool IsWinner { get; set; }
    public bool IsWithdraw { get; set; }
    public string ModifiedBy { get; set; }
    public string ModifiedOn { get; set; }
    public string Party { get; set; }
    public string PartyFlagURL { get; set; }
    public int? PartyId { get; set; }
    public string PartyUrdu { get; set; }
    public int PhaseId { get; set; }
    public string SeatType { get; set; }
    public int SeatTypeId { get; set; }
    public DateTime StartDate { get; set; }
    public string Status { get; set; }
    public string Symbol { get; set; }
    public int? SymbolId { get; set; }
    public string SymbolUrdu { get; set; }
    public string SymbolURL { get; set; }
    public string Type { get; set; }
    public string UrduName { get; set; }
    public int? Votes { get; set; }
    public int Weight { get; set; } = 0;
}

public class NominationDTOValidator : AbstractValidator<NominationDTO>
{
    public NominationDTOValidator()
    {
        RuleFor(c => c.CandidateId).NotNull().WithMessage("Please select candidate");
        RuleFor(c => c.PartyId).NotNull().WithMessage("Please select candidate party");
        When(c => c.PartyId != 21,
            () => { RuleFor(c => c.SymbolId).NotNull().WithMessage("Please select candidate symbol"); });
        RuleFor(c => c.Weight).GreaterThanOrEqualTo(0).LessThanOrEqualTo(300)
            .WithMessage("Please enter valid weight for this nomination");
    }
}

public class ResultDetailDTO
{
    public ResultMode ResultMode { get; set; } = ResultMode.Default;
    public int AddVotes { get; set; }
    public int AssemblyId { get; set; }

    [Required(ErrorMessage = "Please select candidate")]
    public int? CandidateId { get; set; }

    public string candidateImg { get; set; }
    public int ChangeVotes { get; set; }
    public int ConstituencyId { get; set; }
    public int ElectionId { get; set; }
    public string EnglishName { get; set; }
    public int FemaleVoters { get; set; }
    public int FemaleVotersPS { get; set; }
    public string flagImg { get; set; }
    public int Id { get; set; }
    public bool IsBilaMokabilaWinner { get; set; }
    public bool IsNew { get; set; } = true;
    public bool IsWithdraw { get; set; }
    public int MaleVoters { get; set; }
    public int MaleVotersPS { get; set; }
    public string Mode { get; set; }
    public string Party { get; set; }

    [Required(ErrorMessage = "Please select Party")]
    public int? PartyId { get; set; }

    public string PartyUrdu { get; set; }
    public int PhaseId { get; set; }
    public float PollingStationCompletedPS { get; set; }
    public int PSVotes { get; set; }
    public int ResultPollingStations { get; set; }
    public int SeatTypeId { get; set; }
    public string Symbol { get; set; }

    [Required(ErrorMessage = "Please select Symbol")]
    public int? SymbolId { get; set; }

    public string symbolImg { get; set; }
    public string SymbolUrdu { get; set; }
    public int TotalPollingStations { get; set; }
    public int TotalPollingStationsPS { get; set; }
    public bool IsRePoll { get; set; }

    public int TotalVotes
    {
        get
        {
            return Mode switch
            {
                "Add" => Votes + ChangeVotes,
                "Update" => ChangeVotes,
                _ => 0
            };
        }
    }

    public int TotalVotesPS { get; set; }
    public string UrduName { get; set; }
    public int VotersCastePS { get; set; }

    public int VotersCount => MaleVoters + FemaleVoters;

    public int VotersCountPS { get; set; }

    public float VoterTurnOutPS { get; set; }

    public int Votes { get; set; }

    [Range(0, 200, ErrorMessage = "Please Select value between 0 to 200")]
    public int Weight { get; set; } = 0;

    public int? RejectedVotes { get; set; }
}