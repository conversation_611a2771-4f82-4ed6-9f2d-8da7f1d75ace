﻿using System;

namespace ElectionAppServer.Model;

public class NominationPanelist
{
    public int Id { get; set; }
    public int NominationId { get; set; }
    public virtual Nomination Nomination { get; set; }
    public string NameEnglish { get; set; }
    public string NameUrdu { get; set; }
    public Gender Gender { get; set; }
    public int SeatTypeId { get; set; }
    public virtual SeatType SeatType { get; set; }

    #region Audit Log Fields

    public DateTime CreatedDate { get; set; } = DateTime.Now;
    public DateTime? ModifiedDate { get; set; }
    public string CreatedBy { get; set; }
    public string ModifiedBy { get; set; }

    #endregion Audit Log Fields
}