﻿@page "/seattypes"
@attribute [Authorize(Roles = "Administrators")]
@using BeforeOpenEventArgs = Syncfusion.Blazor.Popups.BeforeOpenEventArgs
@using ButtonType = MudBlazor.ButtonType
@using FilterType = Syncfusion.Blazor.Grids.FilterType
@using Size = MudBlazor.Size
@inherits OwningComponentBase<SeatTypeService>


<SfToast @ref="ToastObj" Title="Alert" ShowProgressBar="true" Timeout="3000">
    <ToastPosition X="Right" Y="Bottom"></ToastPosition>
</SfToast>

<SfDialog Width="500px" @bind-Visible="@isDlgVisible" @ref="dlgForm" IsModal="true" ShowCloseIcon="true">
    <DialogEvents OnOpen="beforeOpen"></DialogEvents>
    <DialogTemplates>
        <Header>Seat Type Detail</Header>
        <Content>
            <EditForm Model="@selectedObj" OnValidSubmit="@SaveCasteData">
                <DataAnnotationsValidator/>

                <div class="row">
                    <div class="input-field col">
                        <SfTextBox @bind-Value="selectedObj.EnglishTitle" OnBlur="TranslateToUrdu" Placeholder="EnglishTitle" FloatLabelType="FloatLabelType.Always"></SfTextBox>
                        <ValidationMessage For="@(() => selectedObj.EnglishTitle)"/>
                    </div>
                    <div class="input-field col">
                        <SfTextBox @bind-Value="selectedObj.UrduTitle" Placeholder="EnglishTitle" FloatLabelType="FloatLabelType.Always"></SfTextBox>
                        <ValidationMessage For="@(() => selectedObj.UrduTitle)"/>
                    </div>
                </div>
                <div class="row">
                    <div class="col">
                        <MudButton ButtonType="ButtonType.Submit"
                                   Color="Color.Primary" Size="Size.Small"
                                   Variant="Variant.Filled">
                            @SaveButtonText
                        </MudButton>
                    </div>
                </div>
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>

@if (objList == null)
{
    <p>
        <em>Loading...</em>
    </p>
}
else
{
    @*<PageCaption Title="Seat Types"></PageCaption>*@
    <MudText Typo="Typo.h5">Seat Types</MudText>
    <section>
        <SfButton IsPrimary="true" CssClass="e-primary mb-2" OnClick="OpenCreateForm">Create Seat Type</SfButton>
        <SfGrid DataSource="@objList" AllowFiltering="true" AllowSorting="true" ModelType="@selectedObj" @ref="Grid" AllowTextWrap="true" AllowPaging="true">
            <GridFilterSettings Type="FilterType.Excel"></GridFilterSettings>
            <GridColumns>
                <GridColumn Field="Id" HeaderText="Id" AutoFit="true"></GridColumn>
                <GridColumn Field="@nameof(SeatType2DTO.EnglishTitle)" HeaderText="Seat Type (English)"></GridColumn>
                <GridColumn Field="@nameof(SeatType2DTO.UrduTitle)" HeaderText="Seat Type (اردو)"></GridColumn>
                <GridColumn HeaderText="Actions" AllowFiltering="false" Width="150px">
                    <Template Context="ss">
                        @{
                            var kk = ss as SeatType2DTO;
                            <SfButton IsPrimary="true" CssClass="e-danger" OnClick="@(() => DeleteRecord(kk.Id))">
                                <i class="fas fa-trash-alt"></i>
                            </SfButton>
                            <SfButton IsPrimary="true" CssClass="e-primary" OnClick="@(() => OpenEditForm(kk))">
                                <i class="fas fa-pencil-alt"></i>
                            </SfButton>
                        }
                    </Template>
                </GridColumn>
            </GridColumns>
        </SfGrid>
    </section>
}

@code {

    // AuthenticationState is available as a CascadingParameter
    [CascadingParameter] private Task<AuthenticationState> authenticationStateTask { get; set; }

    private SfDialog dlgForm;
    List<SeatType2DTO> objList;
    private bool isDlgVisible;
    private string SaveButtonText = "Save";
    private SeatType2DTO selectedObj = new();
    private SfToast ToastObj;
    public string ToastContent = "Unable to delete Record, Related Record(s) Exist";
    public SfGrid<SeatType2DTO> Grid { get; set; }

    protected override async Task OnInitializedAsync()
    {
        // Get the current user
        var user = (await authenticationStateTask).User;
        // Get the objList for the current user
        // We access WeatherobjListervice using @Service
        objList = await Service.GetAll();
    }


    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        //return base.OnAfterRenderAsync(firstRender);
        if (firstRender)
        {
            var user = (await authenticationStateTask).User;
            if (user.Identity.IsAuthenticated)
            {
                //var dd =  await ProtectedSessionStore.GetAsync<ElectionStateDTO>("election");
                var dd = await localStorage.GetAsync<ElectionStateDTO>(await confreader.GetSettingValue("electionkey"));
                state.SetState(dd.Value);
            }
            else
            {
                try
                {
                    //await  ProtectedSessionStore.DeleteAsync("election");
                    await localStorage.DeleteAsync(await confreader.GetSettingValue("electionkey"));
                    state.SetState(new ElectionStateDTO { ElectionId = 0, ElectionTitle = "", PhaseId = 0, PhaseTitle = "" });
                }
                catch (Exception)
                {
                    // ignored
                }

                NavigationManager.NavigateTo("");
            }

            StateHasChanged();
        }
    }


    private async Task OpenCreateForm()
    {
        selectedObj = new SeatType2DTO();
        await dlgForm.ShowAsync();
        SaveButtonText = "Create";
    }

    private async Task OpenEditForm(SeatType2DTO st)
    {
        selectedObj = await Service.GetById(st.Id);
        await dlgForm.ShowAsync();
        SaveButtonText = "Update";
    }

    private void ClearData()
    {
        selectedObj = new SeatType2DTO();
    }

    private async Task SaveCasteData()
    {
        var user = (await authenticationStateTask).User;
        try
        {
            await Service.Save(selectedObj, user.Identity.Name);
            objList = await Service.GetAll();
            ClearData();
            await Grid.Refresh();
            await dlgForm.HideAsync();
        }
        catch (Exception ex)
        {
            var m = new ToastModel { Content = ex.Message, Title = "Error" };
            await ToastObj.ShowAsync(m);
        }
    }

    private async Task DeleteRecord(int Id)
    {
        var paras = new[] { "Are you want to delete this record?" };
        var res = await JSRuntime.InvokeAsync<bool>("confirm", paras);
        if (res == false)
            return;

        try
        {
            var msg2 = await Service.Delete(Id);
            objList = await Service.GetAll();
            await Grid.Refresh();
            ClearData();
        }
        catch (Exception ex)
        {
            var m = new ToastModel { Content = ex.Message, Title = "Error" };
            await ToastObj.ShowAsync(m);
        }
    }

    private void beforeOpen(BeforeOpenEventArgs args)
    {
        args.MaxHeight = null;
    }

    public async Task TranslateToUrdu()
    {
        try
        {
            if (string.IsNullOrEmpty(selectedObj.UrduTitle))
                selectedObj.UrduTitle = await Translation.TranslateText(selectedObj.EnglishTitle);
        }
        catch (Exception)
        {
            // ignored
        }
    }

}