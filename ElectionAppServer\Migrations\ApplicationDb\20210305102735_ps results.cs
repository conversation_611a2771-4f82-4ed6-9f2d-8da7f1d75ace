﻿using Microsoft.EntityFrameworkCore.Migrations;
using System;

namespace ElectionAppServer.Migrations.ApplicationDb
{
	public partial class psresults : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.AddColumn<string>(
				 name: "CreatedBy",
				 table: "PollingStations",
				 type: "nvarchar(max)",
				 nullable: true);

			migrationBuilder.AddColumn<DateTime>(
				 name: "CreatedDate",
				 table: "PollingStations",
				 type: "datetime2",
				 nullable: false,
				 defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

			migrationBuilder.AddColumn<string>(
				 name: "ModifiedBy",
				 table: "PollingStations",
				 type: "nvarchar(max)",
				 nullable: true);

			migrationBuilder.AddColumn<DateTime>(
				 name: "ModifiedDate",
				 table: "PollingStations",
				 type: "datetime2",
				 nullable: true);

			migrationBuilder.CreateTable(
				 name: "PSResults",
				 columns: table => new
				 {
					 NominationId = table.Column<int>(type: "int", nullable: false),
					 PollingStationId = table.Column<int>(type: "int", nullable: false),
					 CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "getdate()"),
					 CreatedBy = table.Column<string>(type: "varchar(450)", unicode: false, maxLength: 450, nullable: false),
					 ModifiedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
					 ModifiedBy = table.Column<string>(type: "varchar(450)", unicode: false, maxLength: 450, nullable: true)
				 },
				 constraints: table =>
				 {
					 table.PrimaryKey("PK_PSResults", x => new { x.PollingStationId, x.NominationId });
					 table.ForeignKey(
							  name: "FK_PSResults_Nominations_NominationId",
							  column: x => x.NominationId,
							  principalTable: "Nominations",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Restrict);
					 table.ForeignKey(
							  name: "FK_PSResults_PollingStations_PollingStationId",
							  column: x => x.PollingStationId,
							  principalTable: "PollingStations",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Restrict);
				 });

			migrationBuilder.CreateIndex(
				 name: "IX_PSResults_NominationId",
				 table: "PSResults",
				 column: "NominationId");
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropTable(
				 name: "PSResults");

			migrationBuilder.DropColumn(
				 name: "CreatedBy",
				 table: "PollingStations");

			migrationBuilder.DropColumn(
				 name: "CreatedDate",
				 table: "PollingStations");

			migrationBuilder.DropColumn(
				 name: "ModifiedBy",
				 table: "PollingStations");

			migrationBuilder.DropColumn(
				 name: "ModifiedDate",
				 table: "PollingStations");
		}
	}
}
