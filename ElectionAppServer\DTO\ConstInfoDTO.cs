﻿using System.Collections.Generic;

namespace ElectionAppServer.DTO;

public class ConstElectionPhaseDTO
{
    public string Date { get; set; }
    public int Id { get; set; }
    public List<ConstSeatsDTO> Seats { get; set; }
    public List<ConstSeatsDTO> SubSeats { get; set; }
    public string Title { get; set; }
}

public class ConstInfoDTO
{
    public int? PreviousConstId { get; set; }
    public string PreviousConst { get; set; }
    public string Profile { get; set; }
    public string AssemblyEnglish { get; set; }
    public int? AssemblyId { get; internal set; }
    public string AssemblyUrdu { get; set; }
    public string Code { get; set; }
    public string DistrictEnglish { get; set; }
    public string DistrictUrdu { get; set; }
    public string DivisionEnglish { get; set; }
    public string DivisionUrdu { get; set; }
    public int ElectionId { get; set; }
    public string EnglishTitle { get; set; }
    public int Id { get; set; }
    public List<ConstElectionPhaseDTO> Phases { get; set; }
    public string ProvinceEnglish { get; set; }
    public string ProvinceUrdu { get; set; }
    public string RegionEnglish { get; set; }
    public string RegionUrdu { get; set; }
    public string TownEnglish { get; set; }
    public string TownUrdu { get; set; }
    public string UrduTitle { get; set; }

    public List<PhaseWiseDemographic> AllDemographic { get; set; } = new();

    #region Demographics

    public string AverageHouseHoldIncome { get; set; }
    public string Castes { get; set; }
    public string Economy { get; set; }
    public string ElectionEnglish { get; set; }
    public string ElectionUrdu { get; set; }
    public int FemaleVoters { get; set; }
    public string GeneralTrivia { get; set; }
    public string ImportantAreas { get; set; }
    public string ImportantPoliticalParties { get; set; }
    public string LanguageSpoken { get; set; }
    public string LiteracyRate { get; set; }
    public string MajorIncomeSource { get; set; }
    public int MaleVoters { get; set; }
    public int? YouthVoters { get; set; }
    public int Population { get; set; }
    public string Problems { get; set; }
    public float? RuralArea { get; set; }
    public string TotalArea { get; set; }
    public int TotalPollingStations { get; set; }
    public int TotalVoters => MaleVoters + FemaleVoters;
    public string UCEnglish { get; set; }
    public string UCUrdu { get; set; }
    public float? UrbanArea { get; set; }

    #endregion Demographics
}

public class PhaseWiseDemographic
{
    public string Phase { get; set; }
    public string AverageHouseHoldIncome { get; set; }
    public string Castes { get; set; }
    public string Economy { get; set; }
    public string ElectionEnglish { get; set; }
    public string ElectionUrdu { get; set; }
    public int FemaleVoters { get; set; }
    public string GeneralTrivia { get; set; }
    public string ImportantAreas { get; set; }
    public string ImportantPoliticalParties { get; set; }
    public string LanguageSpoken { get; set; }
    public string LiteracyRate { get; set; }
    public string MajorIncomeSource { get; set; }
    public int MaleVoters { get; set; }
    public int? YouthVoters { get; set; }
    public int Population { get; set; }
    public string Problems { get; set; }
    public float? RuralArea { get; set; }
    public string TotalArea { get; set; }
    public int TotalPollingStations { get; set; }
    public int TotalVoters => MaleVoters + FemaleVoters;
    public string UCEnglish { get; set; }
    public string UCUrdu { get; set; }
    public float? UrbanArea { get; set; }
    public string Profile { get; set; }
}

public class ConstNominationDTO
{
    public int CandidateId { get; set; }
    public string NameEnglish { get; set; }
    public string NameUrdu { get; set; }
    public string PartyEnglish { get; set; }
    public int? PartyId { get; set; }
    public string PartyUrdu { get; set; }
    public string SymbolEnglish { get; set; }
    public int? SymbolId { get; set; }
    public string SymbolUrdu { get; set; }
    public int VoteObtain { get; set; }
    public int Weight { get; set; }
    public string CandidateUrl { get; set; }
    public string PartyFlagUrl { get; set; }
    public string SymbolUrl { get; set; }
}

public class ConstSeatsDTO
{
    public List<ConstNominationDTO> Nominations { get; set; }
    public string SeatTypeEnglish { get; set; }
    public string SeatTypeUrdu { get; set; }
}