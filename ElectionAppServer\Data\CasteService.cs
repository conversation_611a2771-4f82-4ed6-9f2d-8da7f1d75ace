﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ElectionAppServer.Model;
using Microsoft.EntityFrameworkCore;

namespace ElectionAppServer.Data;

public class CasteService(IDbContextFactory<ApplicationDbContext> contextFactory)
{
    public Task<List<Caste>> GetList()
    {
        using var dc = contextFactory.CreateDbContext();
        var res = dc.Castes.OrderBy(c => c.EnglishTitle).ToList();
        return Task.FromResult(res);
    }

    public Task<Caste> CreateCaste(Caste obj)
    {
        using var dc = contextFactory.CreateDbContext();
        dc.Castes.Add(obj);
        dc.SaveChanges();
        return Task.FromResult(obj);
    }

    public Task<Caste> UpdateCaste(Caste obj)
    {
        using var dc = contextFactory.CreateDbContext();
        var st = (from aa in dc.Castes
            where aa.Id == obj.Id
            select aa).First();
        st.UrduTitle = obj.UrduTitle.Trim();
        st.EnglishTitle = obj.EnglishTitle.Trim();
        st.ModifiedBy = obj.ModifiedBy;
        st.ModifiedDate = DateTime.Now;
        dc.SaveChanges();
        return Task.FromResult(obj);
    }

    public async Task<Caste> DeleteCaste(int id)
    {
        await using var dc = await contextFactory.CreateDbContextAsync();
        var canNotDelete = (from aa in dc.Candidates
            where aa.CasteId == id
            select aa).Any();

        if (canNotDelete) throw new Exception("You cannot delete this cast. This is associated with Candidate");

        var st = await (from aa in dc.Castes
            where aa.Id == id
            select aa).FirstOrDefaultAsync();
        dc.Castes.Remove(st);
        await dc.SaveChangesAsync();
        return await Task.FromResult(st);
    }

    public async Task<Caste> Save(Caste obj, string user)
    {
        await using var dc = await contextFactory.CreateDbContextAsync();
        obj.EnglishTitle = obj.EnglishTitle.Trim();
        obj.UrduTitle = obj.UrduTitle.Trim();

        var isExist = (from aa in dc.Castes
            where aa.Id != obj.Id && aa.UrduTitle == obj.UrduTitle
            select aa).Any();

        if (isExist)
            throw new Exception("Title (Urdu) already exist");

        isExist = (from aa in dc.Castes
            where aa.Id != obj.Id && aa.EnglishTitle == obj.EnglishTitle
            select aa).Any();

        if (isExist)
            throw new Exception("Title (English) already exist");

        if (obj.Id == 0)
        {
            var cs = new Caste
            {
                UrduTitle = obj.UrduTitle,
                CreatedBy = user,
                CreatedDate = DateTime.Now,
                EnglishTitle = obj.EnglishTitle
            };
            dc.Add(cs);
            await dc.SaveChangesAsync();
            obj.Id = cs.Id;
            return await Task.FromResult(cs);
        }
        else
        {
            var cs = (from aa in dc.Castes
                where aa.Id == obj.Id
                select aa).First();
            cs.EnglishTitle = obj.EnglishTitle;
            cs.UrduTitle = obj.UrduTitle;
            cs.ModifiedBy = user;
            cs.ModifiedDate = DateTime.Now;
            await dc.SaveChangesAsync();
            return await Task.FromResult(cs);
        }
    }
}