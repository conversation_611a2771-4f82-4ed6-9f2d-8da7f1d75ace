﻿using System.ComponentModel.DataAnnotations;

namespace ElectionAppServer.DTO;

public class PannelNominationsDTO
{
    public int Id { get; internal set; }

    public string SeatTypeUrdu { get; internal set; }
    public string SeatTypeEnglish { get; internal set; }
    public int Order { get; internal set; }

    public int? PanelId { get; set; }
    public int? PhaseId { get; set; }
    public int? AssemblyId { get; set; }
    public string CandidateEnglishName { get; internal set; }
    public string CandidateUrduName { get; internal set; }
}

public class PanelFormDTO
{
    [Required] public int? PanelId { get; set; }

    [Required] public int? PhaseId { get; set; }

    [Required] public int? AssemblyId { get; set; }

    [Required]
    [StringLength(200)]
    [Display(Name = "Candidate English Name")]
    public string CandidateEnglishName { get; internal set; }

    [Required]
    [StringLength(200)]
    [Display(Name = "Candidate Urdu Name")]
    public string CandidateUrduName { get; internal set; }

    [Required]
    [Display(Name = "Seat Type")]
    public int? SeatTypeId { get; set; }

    public int Id { get; internal set; }
}