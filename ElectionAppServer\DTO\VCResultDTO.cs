﻿using System.ComponentModel.DataAnnotations;

namespace ElectionAppServer.DTO;

public class VCResultDTO
{
    [Required] public int? ConstituencyId { get; set; }

    [Required] public int? PhaseId { get; set; }

    [Required] public int? AssemblyId { get; set; }

    [Required] public int? SeatTypeId { get; set; }

    //[Required]
    //public int? MaleVoters { get; set; }
    //[Required]
    //public int? FemaleVoters { get; set; }
    //[Required]
    //public int? TotalPollingStations { get; set; }
    [Required] [StringLength(100)] public string CandidateName { get; set; }

    public int? PartyId { get; set; }

    [Required] public int? SymbolId { get; set; }

    [Required] public int? Votes { get; set; }

    //[Required]
    //public int? TotalPopulation { get; set; }
}