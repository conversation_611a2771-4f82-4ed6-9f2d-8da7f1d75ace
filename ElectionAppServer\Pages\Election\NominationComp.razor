﻿@using ButtonType = MudBlazor.ButtonType
@using FilterType = Syncfusion.Blazor.Grids.FilterType
@using Size = MudBlazor.Size
@inherits OwningComponentBase<NominationDataService>
@attribute [Authorize(Roles = "Administrators")]

<SfToast @ref="ToastObj" Title="Alert" ShowProgressBar="true" Timeout="3000">
    <ToastPosition X="Right"></ToastPosition>
</SfToast>

<SfDialog Width="90%" @bind-Visible="@isDlgSearchCandVisible" @ref="dlgSeachCand" ShowCloseIcon="true" IsModal="true">
    <DialogEvents OnOpen="beforeOpen"></DialogEvents>
    <DialogTemplates>
        <Header>Search Candidate</Header>
        <Content>
            <div class="row">
                <div class="col" style="display:flex">
                    <SfTextBox Placeholder="Search Candidate"
                               @bind-Value="searchText"
                               FloatLabelType="FloatLabelType.Never">
                    </SfTextBox>
                    <SfButton IsPrimary="false" OnClick="SearchCandidate" CssClass="e-primary btnSearch">
                        <i class="fas fa-search"></i>
                    </SfButton>
                </div>
            </div>
            <div class="row">
                <div class="col">
                    <SfGrid DataSource="candListFilter" AllowSorting="true" AllowTextWrap="true" AllowFiltering="true" AllowPaging="true" Width="100%">
                        <GridFilterSettings Type="FilterType.Excel"></GridFilterSettings>
                        <GridColumns>
                            <GridColumn AutoFit="true" HeaderText="Code" Field="Id"></GridColumn>
                            <GridColumn AutoFit="true" HeaderText="Name" AllowFiltering="true">
                                <Template Context="cc">
                                    @{
                                        var mm = cc as SearchCandidateDTO;
                                        <img src="@mm.ImgUrl" style="max-width:80px;margin:4px;border-radius:10px" align="left"/>
                                        <span>@mm.EnglishName</span>
                                        <br/>
                                        <span class="urdu-cap">@mm.UrduName</span>
                                    }
                                </Template>
                            </GridColumn>
                            <GridColumn AutoFit="true" HeaderText="Party" Field="Party"></GridColumn>
                            <GridColumn AutoFit="true" HeaderText="Disrict" Field="Disrict"></GridColumn>
                            <GridColumn AutoFit="true" HeaderText="Last Constituency" Field="LastConstituency" DisableHtmlEncode="false"></GridColumn>
                            <GridColumn Width="150px" HeaderText="Audit">
                                <Template Context="cc">
                                    @{
                                        var mm2 = cc as SearchCandidateDTO;
                                        <span class="alog">Created By: <b>@mm2.CreatedBy</b> on @mm2.CreatedDate</span>
                                        if (mm2.ModifiedBy != "")
                                        {
                                            <br>
                                            <span class="alog">Modified By: <b>@mm2.ModifiedBy</b> on @mm2.ModifiedDate</span>
                                        }
                                    }
                                </Template>
                            </GridColumn>
                            <GridColumn HeaderText="Action" Width="80px" TextAlign="TextAlign.Center">
                                <Template Context="cc">
                                    @{
                                        var mm = cc as SearchCandidateDTO;
                                        <SfButton CssClass="e-primary" IsPrimary="false" OnClick="@(() => SelectCandidate(mm.Id, mm.PartyId, mm.SymbolId))">Select</SfButton>
                                    }
                                </Template>
                            </GridColumn>
                        </GridColumns>
                    </SfGrid>
                </div>
            </div>
        </Content>
    </DialogTemplates>
</SfDialog>

<SfDialog Width="800px" @bind-Visible="@isDlgVisible" @ref="dlgForm" IsModal="true" ShowCloseIcon="true">
    <DialogEvents OnOpen="beforeOpen"></DialogEvents>
    <DialogTemplates>
        <Header>Create Nomination</Header>
        <Content>

            <EditForm Model="@selectedObj" OnValidSubmit="@SaveData">
                <FluentValidationValidator/>
                <div>
                    <div class="row">
                        <div class="col-8">
                            <div class="row">
                                <div class="col">
                                    <button type="button" @onclick="OpenSearchCandidate" class="btn btn-primary btn-sm">Search</button>

                                    <span>Total Candidates: @candListAll.Count</span>
                                    <SfDropDownList @bind-Value="selectedObj.CandidateId" TValue="int?" TItem="SearchCandidateDTO"
                                                    Placeholder="Candidate" FloatLabelType="FloatLabelType.Always" DataSource="candListAll"
                                                    AllowFiltering="true" FilterType="Syncfusion.Blazor.DropDowns.FilterType.Contains">
                                        <DropDownListTemplates TItem="SearchCandidateDTO" Context="kk">
                                            <ItemTemplate>
                                                @{
                                                    <div style="display:flex">
                                                        <img src="@selectedObj.CandidateURL" style="max-height:80px; max-width:70px; margin:2px" alt=""/>
                                                        <span>@selectedObj.EnglishName</span>
                                                    </div>
                                                }
                                            </ItemTemplate>
                                        </DropDownListTemplates>
                                        <DropDownListEvents TValue="int?" TItem="GeneralItemDTO" ValueChange="OnCandidateSelect"></DropDownListEvents>
                                        <DropDownListFieldSettings Text="EnglishName" Value="Id"></DropDownListFieldSettings>
                                    </SfDropDownList>

                                    <ValidationMessage For="@(() => selectedObj.CandidateId)"/>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col">
                                    <SfDropDownList AllowFiltering="true" TItem="GeneralItemDTO" TValue="int?" Placeholder="Select - Party" @bind-Value="@selectedObj.PartyId" DataSource="@partiesList" FloatLabelType="FloatLabelType.Always" FilterType="Syncfusion.Blazor.DropDowns.FilterType.Contains">
                                        <DropDownListEvents TValue="int?" TItem="GeneralItemDTO" ValueChange="OnPartyChange"></DropDownListEvents>
                                        <DropDownListFieldSettings Value="Id" Text="EnglishTitle"></DropDownListFieldSettings>


                                    </SfDropDownList>
                                    <ValidationMessage For="@(() => selectedObj.PartyId)"/>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col">
                                    <SfComboBox AllowFiltering="true" TItem="GeneralItemDTO" TValue="int?" Placeholder="Select - Symbol" @bind-Value="@selectedObj.SymbolId" DataSource="@symbolsList" FloatLabelType="FloatLabelType.Always" FilterType="Syncfusion.Blazor.DropDowns.FilterType.Contains">
                                        <ComboBoxFieldSettings Value="Id" Text="EnglishTitle"></ComboBoxFieldSettings>
                                        <ComboBoxEvents ValueChange="OnSymbolChange" TItem="GeneralItemDTO" TValue="int?"></ComboBoxEvents>
                                    </SfComboBox>
                                    <ValidationMessage For="@(() => selectedObj.SymbolId)"/>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col">
                                    <SfNumericTextBox @bind-Value="selectedObj.Weight" TValue="int" Min="0" Max="200" Format="########" Decimals="0" Placeholder="Weight" FloatLabelType="FloatLabelType.Always"></SfNumericTextBox>
                                    <ValidationMessage For="@(() => selectedObj.Weight)"/>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col">
                                    <div class="hh">Party Flag</div>


                                    <img src="@selectedObj.PartyFlagURL" alt="" width="120px"/>


                                </div>
                                <div class="col">
                                    <div class="hh">Symbol</div>


                                    <img src="@selectedObj.SymbolURL" alt="" width="120px"/>

                                </div>
                            </div>
                            <div class="row mt-2">
                                <div class="col">
                                    <MudButton ButtonType="ButtonType.Submit" Color="Color.Primary" Size="Size.Small" Variant="Variant.Filled" StartIcon="@Icons.Material.Filled.Save">Save</MudButton><SfButton CssClass="e-primary">Save</SfButton>
                                </div>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="row">
                                <div class="col">


                                    <img src="@selectedObj.CandidateURL" class="bimg"/>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>

<section>
    <div class="row">


        <div class="col">
            <label>Seat Type</label>
            @*<DxComboBox Data="seatTypesList"
							NullText="Select Seat Type"
							TextFieldName="EnglishTitle"
							@bind-Value="selectedSeatType"
							SizeMode="SizeMode.Medium"
							SelectedItemChanged="OnChangeSeatType"></DxComboBox>*@
        </div>
    </div>
    @if (selectedSeatType != null)
    {
        <SfButton CssClass="e-primary mb-2 mt-2" IsPrimary="true" OnClick="OpenCreateForm">Create Nomination</SfButton>

        @if (constDetail != null)
        {
            <div class="card bg-info mb-3">
                <div class="card-header">
                    Constituency @constDetail.Code @constDetail.Name - @constDetail.Assembly
                </div>
                <div class="card-body bg-light" style="color:black">
                    <div class="row mb-2">
                        <div class="col">
                            <b>Election:</b> @constDetail.Election
                        </div>
                        <div class="col">
                            <b>Phase:</b> @constDetail.Phase
                        </div>
                        <div class="col">
                            <b>Assembly:</b> @constDetail.Assembly
                        </div>
                    </div>
                    <div class="row mb-2">
                        <div class="col">
                            <b>Total Register Voters: </b>@constDetail.TotalVoters
                        </div>
                        <div class="col">
                            <b>Male Voters: </b>@(constDetail.MaleVoters ?? 0)
                        </div>
                        <div class="col">
                            <b>Female Voters: </b>@(constDetail.FemaleVoters ?? 0)
                        </div>
                    </div>
                    <div class="row mb-2">
                        <div class="col">
                            <b>Total Polling Stations: </b>@constDetail.TotalPollingStations
                        </div>
                        <div class="col">
                            <b>Total Population: </b>@constDetail.Population
                        </div>
                        <div class="col"></div>
                    </div>
                    <div class="row mb-2">
                        <div class="col">
                            <b>Province: </b> @constDetail.Province
                        </div>
                        <div class="col">
                            <b>Division: </b> @constDetail.Division
                        </div>
                        <div class="col">
                            <b>Region: </b> @constDetail.Region
                        </div>
                    </div>
                    <div class="row mb-2">
                        <div class="col-4">
                            <b>District: </b> @constDetail.District
                        </div>
                        @if (!string.IsNullOrEmpty(constDetail.Town))
                        {
                            <div class="col-4">
                                <b>Town: </b> @constDetail.Town
                            </div>
                        }
                        @if (!string.IsNullOrEmpty(constDetail.UnionCouncil))
                        {
                            <div class="col-4">
                                <b>Union Council: </b> @constDetail.UnionCouncil
                            </div>
                        }
                    </div>
                </div>
            </div>
        }

        <div class="card bg-primary text-white mb-3">
            <div class="card-header">
                Nominations for @selectedSeatType.EnglishTitle
            </div>
            <div class="card-body bg-light text-black" style="color:black">
                <table class="table table-sm">
                    <thead>
                    <tr>
                        <th>Pic</th>
                        <td>Code</td>
                        <td>Type</td>
                        <th>Name</th>
                        <th>Party</th>
                        <th>Symbol</th>
                        <th>Weight</th>
                        <th>Audit Info</th>
                        <th>&nbsp;</th>
                    </tr>
                    </thead>
                    <tbody>


                    @if (nominations != null)
                    {
                        @foreach (var n in nominations)
                        {
                            <tr>
                                <td>
                                    <img src="@n.CandidateURL" style="width:75px; height:100px" class="myimg"/>
                                </td>
                                <td>
                                    @n.CandidateId
                                </td>
                                <td>
                                    @n.Type
                                </td>
                                <td>
                                    @n.EnglishName<br/>
                                    <span class="urdu-cap">@n.UrduName</span>
                                </td>
                                <td>
                                    <img src="@n.PartyFlagURL" class="myimg" align="left"/>@n.Party<br>
                                    <span class="urdu-cap">@n.PartyUrdu</span>
                                </td>
                                <td>
                                    <img src="@n.SymbolURL" class="myimg" align="left"/>@n.Symbol<br>
                                    <span class="urdu-cap">@n.SymbolUrdu</span>
                                </td>
                                <td>@n.Weight</td>
                                <td>
                                    <div style="font-size:11px">
                                        <span>Created By: @n.CreatedBy</span><span> On @n.CreatedOn</span>
                                        @if (!string.IsNullOrEmpty(n.ModifiedBy))
                                        {
                                            <br/>
                                            <span>Modified By: @n.ModifiedBy</span>
                                            <span> On @n.ModifiedOn</span>
                                        }
                                    </div>
                                </td>
                                <td>
                                    <SfButton CssClass="e-primary" IsPrimary="true" OnClick="@(() => OpenEditForm(n))">
                                        <i class="fas fa-pencil-alt"></i>
                                    </SfButton>
                                    <SfButton CssClass="e-danger" IsPrimary="false" OnClick="@(() => DeleteRecord(n.Id))">
                                        <i class="fas fa-trash-alt"></i>
                                    </SfButton>
                                    @if (n.Type == "Panel")
                                    {
                                        <SfButton CssClass="e-info" IsPrimary="false" OnClick="@(() => DeleteRecord(n.Id))"><i class="fas fa-user-friends"></i> Panelist</SfButton>
                                    }
                                </td>
                            </tr>
                        }
                    }
                    </tbody>
                </table>
            </div>
        </div>
    }
</section>

@code {

}