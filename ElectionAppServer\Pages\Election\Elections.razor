﻿@page "/elections"
@using ButtonType = MudBlazor.ButtonType
@using FilterType = Syncfusion.Blazor.DropDowns.FilterType
@using Size = MudBlazor.Size
@inherits OwningComponentBase<ElectionService>
@attribute [Authorize(Roles = "Administrators")]
@*<PageCaption Title="Elections"></PageCaption>*@
<MudText Typo="Typo.h5">Elections</MudText>


<SfToast @ref="ToastObj" Title="Adaptive Tiles Meeting" Icon="e-meeting" Content="@ToastContent">
    <ToastPosition X="Right"></ToastPosition>
</SfToast>

<SfDialog Width="500" @bind-Visible="@isPhaseDialogVisible" @ref="dlgPhaseForm" IsModal="true" ShowCloseIcon="true">
    <DialogEvents OnOpen="beforeOpen"></DialogEvents>
    <DialogTemplates>
        <Header>Create Election Phase</Header>
        <Content>
            <EditForm Model="@electionPhaseObj" OnValidSubmit="@SavePhaseData">
                <DataAnnotationsValidator/>
                <ValidationSummary></ValidationSummary>
                <div class="row">
                    <div class="col">
                        <SfTextBox @bind-Value="@electionPhaseObj.Title" Placeholder="Phase Title" FloatLabelType="FloatLabelType.Always"></SfTextBox>
                        <ValidationMessage For="@(() => electionPhaseObj.Title)"/>
                    </div>
                </div>
                <div class="row">
                    <div class="col">
                        <SfDatePicker @bind-Value="electionPhaseObj.ElectionDate" Placeholder="Election Date" FloatLabelType="FloatLabelType.Always"></SfDatePicker>
                        <ValidationMessage For="@(() => electionPhaseObj.ElectionDate)"/>
                    </div>
                </div>

                <div class="row mt-2 mb-2">
                    <div class="col">
                        <MudButton ButtonType="ButtonType.Submit" Size="Size.Small" Variant="Variant.Filled" Color="Color.Primary" StartIcon="@Icons.Material.Filled.Save">Create</MudButton>
                    </div>
                </div>
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>

<SfDialog Width="800px" @bind-Visible="@isDlgVisible" @ref="dlgForm" IsModal="true" ShowCloseIcon="true">
    <DialogEvents OnOpen="beforeOpen"></DialogEvents>
    <DialogTemplates>
        <Header>@FormTitle</Header>
        <Content>
            <EditForm Model="@selectedObj" OnValidSubmit="@SaveData">
                <DataAnnotationsValidator/>
                <div class="row">
                    <div class="input-field col s6">
                        <SfTextBox @bind-Value="selectedObj.EnglishTitle" Placeholder="Title (English)" OnBlur="TranslateToUrdu" FloatLabelType="FloatLabelType.Always"></SfTextBox>
                        <ValidationMessage For="@(() => selectedObj.EnglishTitle)"/>
                    </div>
                    <div class="input-field col s6">
                        <SfTextBox @bind-Value="selectedObj.UrduTitle" Placeholder="Title (Urdu)" FloatLabelType="FloatLabelType.Always"></SfTextBox>
                        <ValidationMessage For="@(() => selectedObj.UrduTitle)"/>
                    </div>
                </div>
                <div class="row">
                    <div class="col">

                        <SfTextBox @bind-Value="selectedObj.Description" Placeholder="Description" Multiline="true" FloatLabelType="FloatLabelType.Always"></SfTextBox>
                        <ValidationMessage For="@(() => selectedObj.Description)"/>
                    </div>
                </div>
                <div class="row">
                    <div class="input-field col s6">
                        <SfNumericTextBox ShowSpinButton="false" Format="0000" Decimals="0" TValue="int" @bind-Value="@selectedObj.YearFrom" FloatLabelType="FloatLabelType.Always" Min="1948" Max="2300" Placeholder="Year (from)">
                        </SfNumericTextBox>
                        <ValidationMessage For="@(() => selectedObj.YearFrom)"/>
                    </div>
                    <div class="input-field col s6">
                        <SfNumericTextBox ShowSpinButton="false" Format="0000" Decimals="0" TValue="int" @bind-Value="@selectedObj.YearTo" FloatLabelType="FloatLabelType.Always" Min="1948" Max="2300" Placeholder="Year (to)">
                        </SfNumericTextBox>
                        <ValidationMessage For="@(() => selectedObj.YearFrom)"/>
                    </div>
                </div>
                <div class="row">
                    <div class="col s6 input-field">
                        @*<InputDate @bind-Value="selectedObj.StartDate"></InputDate>*@
                        <SfDatePicker TValue="DateTime?" FloatLabelType="FloatLabelType.Always" @bind-Value="@selectedObj.StartDate" Format="d MMM, yyyy" Placeholder="Choose a Date"></SfDatePicker>
                    </div>
                    <div class="col s6 input-field"></div>
                </div>
                <div class="row">
                    <div class="col">
                        <label>Election Type: </label>
                        <SfCheckBox @bind-Checked="selectedObj.IsGeneral"></SfCheckBox> @(selectedObj.IsGeneral ? "General" : "Local Body")
                    </div>
                    @*<div class="col">
                            <label>Election Based On:</label>
                            <SfCheckBox @bind-Checked="selectedObj.Based"></SfCheckBox> @(selectedObj.IsBasedOnConstituency ? "Candidates" : "Panels")
                        </div>*@
                </div>
                <div class="row">
                    <div class="col">
                        <SfDropDownList AllowFiltering="true" FilterType="FilterType.Contains"
                                        TItem="GeneralItemDTO" TValue="int?" @bind-Value="selectedObj.CandidatePoolId"
                                        Placeholder="Candidate Pool" DataSource="@candidatesPool" FloatLabelType="FloatLabelType.Always">
                            <DropDownListFieldSettings Value="Id" Text="EnglishTitle"></DropDownListFieldSettings>
                        </SfDropDownList>
                        <ValidationMessage For="@(() => selectedObj.CandidatePoolId)"/>
                    </div>
                </div>

                <div class="row">
                    <div class="col">
                        <MudButton Color="Color.Primary" Size="Size.Small" Variant="Variant.Filled"
                                   ButtonType="ButtonType.Submit"
                                   StartIcon="@Icons.Material.Filled.Save"
                                   Disabled="@(selectedObj.UrduTitle == "" || selectedObj.EnglishTitle == "")">
                            @SaveButtonText
                        </MudButton>
                    </div>
                </div>
                @if (ErrMsg != "")
                {
                    <div class="row">
                        <div class="alert alert-danger mt-2" role="alert">
                            <strong>Error!</strong> @ErrMsg
                        </div>
                    </div>
                }
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>

<section class="content esection">
    @if (objList == null)
    {
        <p>
            <em>Loading...</em>
        </p>
    }
    else
    {
        <div class="row">
            <div class="col">
                <MudButton Size="Size.Small" Color="Color.Primary" Variant="Variant.Filled" StartIcon="@Icons.Material.Filled.Add" Class="mb-2" OnClick="OpenCreateForm">Create Election</MudButton>
            </div>
        </div>
        <div class="row">
            <div class="col">
                <SfGrid @ref="Grid" DataSource="objList" AllowPaging="true" AllowFiltering="true" AllowSorting="true" AllowTextWrap="true" Width="100%">
                    <GridEvents QueryCellInfo="CustomizeCell" TValue="ElectionDTO"></GridEvents>
                    <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Excel"></GridFilterSettings>
                    <GridColumns>
                        <GridColumn AutoFit="true" HeaderText="Title (English)" Field="EnglishTitle"></GridColumn>
                        <GridColumn AutoFit="true" HeaderText="Title (Urdu)" Field="UrduTitle" TextAlign="TextAlign.Right"></GridColumn>
                        <GridColumn AutoFit="true" HeaderText="Phase" Field="PhaseTitle">
                        </GridColumn>
                        <GridColumn AutoFit="true" HeaderText="Date" Field="StartDate" Format="d MMM, yyyy"></GridColumn>
                        <GridColumn HeaderText="" AllowFiltering="false" Width="330px">
                            <Template Context="ec">
                                @{
                                    @if (ec is ElectionDTO obj)
                                    {
                                        if (obj.CanDelete)
                                        {
                                            <MudFab Color="Color.Error" Size="Size.Small" StartIcon="@Icons.Material.Filled.Delete" OnClick="@(() => DeleteRecord(obj.Id))"></MudFab>
                                        }

                                        <MudFab Color="Color.Primary" Size="Size.Small" StartIcon="@Icons.Material.Filled.Edit" OnClick="@(() => OpenEditForm(obj))"></MudFab>
                                        if (obj.PhaseTitle == "Phase 1")
                                        {
                                            <MudButton Color="Color.Info" Variant="Variant.Filled" Size="Size.Small" StartIcon="@Icons.Material.Filled.Add" OnClick="@(() => CreatePhase(obj.ElectionId))">New Phase</MudButton>
                                            <a href="/administrativeunits/@obj.ElectionId">Structures</a>
                                            <span>|</span>
                                            <a href="/assemblies/@obj.ElectionId">Assemblies</a>
                                        }
                                    }
                                }
                            </Template>
                        </GridColumn>
                    </GridColumns>
                </SfGrid>
                @*<table class="table table-sm">
                         <thead>
                              <tr>
                                    <th>Title</th>
                                    <th>Phase</th>
                                    <th>Date</th>
                                    <th>&nbsp;</th>
                              </tr>
                         </thead>
                         <tbody>

                              @foreach (var obj in objList)
                              {
                                    <tr>
                                         <td>@obj.EnglishTitle</td>
                                         <td>@obj.PhaseTitle</td>
                                         <td>@obj.ElectionDate</td>
                                         <td>
                                              @if (obj.CanDelete)
                                              {
                                                    <SfButton IsPrimary="true" CssClass="e-danger" OnClick="@(()=>DeleteRecord(obj.Id))"><i class="fas fa-trash-alt"></i></SfButton>
                                              }

                                              <SfButton IsPrimary="true" CssClass="e-primary" OnClick="@(()=>OpenEditForm(obj))"><i class="fas fa-pencil-alt"></i></SfButton>
                                              @if (obj.PhaseTitle == "Phase 1")
                                              {
                                                    <SfButton IsPrimary="true" CssClass="e-inof" OnClick="@(()=>CreatePhase(obj.ElectionId))">New Phase</SfButton>
                                                    <a href="/administrativeunits/@obj.ElectionId">Structures</a>
                                                    <span>|</span>
                                                    <a href="/assemblies/@obj.ElectionId">Assemblies</a>
                                                    <span> | </span>
                                                    <a href="/nominations/@obj.Id">Nominations</a> <span> | </span>
                                                    <a href="/namanigars/@obj.ElectionId">Namanigars</a>
                                              }
                                         </td>
                                    </tr>
                              }
                         </tbody>
                    </table>*@
            </div>
        </div>
    }
</section>