﻿namespace ElectionAppServer.Model;

//public class ElectionAssemblyConstituency
//{
//   public string Code { get; set; }

//   [ForeignKey("ElectionAssembly")]
//   public int ElectionAssemblyId { get; set; }

//   public virtual ElectionAssembly ElectionAssembly { get; set; }

//   [ForeignKey("Structure")]
//   public int StructureId { get; set; }

//   public virtual Structure Structure { get; set; }

//   #region Audit Log Fields

//   public DateTime CreatedDate { get; set; } = DateTime.Now;
//   public DateTime? ModifiedDate { get; set; }

//   [StringLength(450)]
//   public string CreatedBy { get; set; }

//   [StringLength(450)]
//   public string ModifiedBy { get; set; }

//   #endregion Audit Log Fields
//}