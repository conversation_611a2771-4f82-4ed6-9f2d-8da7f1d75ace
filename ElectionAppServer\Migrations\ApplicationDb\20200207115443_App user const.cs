﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace ElectionAppServer.Migrations.ApplicationDb
{
	public partial class Appuserconst : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropForeignKey(
				 name: "FK_NamanigarConstituencies_AspNetUsers_NamanigarId",
				 table: "NamanigarConstituencies");

			migrationBuilder.AddForeignKey(
				 name: "FK_NamanigarConstituencies_AspNetUsers_NamanigarId",
				 table: "NamanigarConstituencies",
				 column: "NamanigarId",
				 principalTable: "AspNetUsers",
				 principalColumn: "Id",
				 onDelete: ReferentialAction.Restrict);
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropForeignKey(
				 name: "FK_NamanigarConstituencies_AspNetUsers_NamanigarId",
				 table: "NamanigarConstituencies");

			migrationBuilder.AddForeignKey(
				 name: "FK_NamanigarConstituencies_AspNetUsers_NamanigarId",
				 table: "NamanigarConstituencies",
				 column: "NamanigarId",
				 principalTable: "AspNetUsers",
				 principalColumn: "Id",
				 onDelete: ReferentialAction.Cascade);
		}
	}
}
