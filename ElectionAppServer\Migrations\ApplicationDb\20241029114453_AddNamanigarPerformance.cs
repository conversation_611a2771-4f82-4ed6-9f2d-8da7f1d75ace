﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace ElectionAppServer.Migrations.ApplicationDb
{
    /// <inheritdoc />
    public partial class AddNamanigarPerformance : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_AdminDivisions_EnglishTitle_ProvinceId",
                table: "AdminDivisions");

            migrationBuilder.DropIndex(
                name: "IX_AdminDivisions_ProvinceId",
                table: "AdminDivisions");

            migrationBuilder.DropIndex(
                name: "IX_AdminDivisions_UrduTitle_ProvinceId",
                table: "AdminDivisions");

            migrationBuilder.AlterColumn<string>(
                name: "Phone",
                table: "Namanigars",
                type: "varchar(150)",
                unicode: false,
                maxLength: 150,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "varchar(50)",
                oldUnicode: false,
                oldMaxLength: 50);

            migrationBuilder.AddColumn<string>(
                name: "Address",
                table: "Namanigars",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: true);

            migrationBuilder.CreateTable(
                name: "NamanigarPerformances",
                columns: table => new
                {
                    NamanigarId = table.Column<int>(type: "int", nullable: false),
                    ElectionPhaseId = table.Column<int>(type: "int", nullable: false),
                    PreElectionPerformance = table.Column<int>(type: "int", nullable: false),
                    Reachability = table.Column<int>(type: "int", nullable: false),
                    DocumentEfficiency = table.Column<int>(type: "int", nullable: false),
                    PreElectionInfoAccuracy = table.Column<int>(type: "int", nullable: false),
                    ResultDayPerformance = table.Column<int>(type: "int", nullable: false),
                    ResultAccuracy = table.Column<int>(type: "int", nullable: false),
                    WasAvailableDuringResult = table.Column<bool>(type: "bit", nullable: false),
                    HasBehavioralIssues = table.Column<bool>(type: "bit", nullable: false),
                    BehavioralIssueDescription = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: true),
                    Trivia = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatedBy = table.Column<string>(type: "varchar(450)", unicode: false, maxLength: 450, nullable: false),
                    ModifiedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    ModifiedBy = table.Column<string>(type: "varchar(450)", unicode: false, maxLength: 450, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_NamanigarPerformances", x => new { x.NamanigarId, x.ElectionPhaseId });
                    table.ForeignKey(
                        name: "FK_NamanigarPerformances_ElectionPhases_ElectionPhaseId",
                        column: x => x.ElectionPhaseId,
                        principalTable: "ElectionPhases",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_NamanigarPerformances_Namanigars_NamanigarId",
                        column: x => x.NamanigarId,
                        principalTable: "Namanigars",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateIndex(
                name: "IX_AdminDivisions_ProvinceId_EnglishTitle",
                table: "AdminDivisions",
                columns: new[] { "ProvinceId", "EnglishTitle" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_AdminDivisions_ProvinceId_UrduTitle",
                table: "AdminDivisions",
                columns: new[] { "ProvinceId", "UrduTitle" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_NamanigarPerformances_ElectionPhaseId",
                table: "NamanigarPerformances",
                column: "ElectionPhaseId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "NamanigarPerformances");

            migrationBuilder.DropIndex(
                name: "IX_AdminDivisions_ProvinceId_EnglishTitle",
                table: "AdminDivisions");

            migrationBuilder.DropIndex(
                name: "IX_AdminDivisions_ProvinceId_UrduTitle",
                table: "AdminDivisions");

            migrationBuilder.DropColumn(
                name: "Address",
                table: "Namanigars");

            migrationBuilder.AlterColumn<string>(
                name: "Phone",
                table: "Namanigars",
                type: "varchar(50)",
                unicode: false,
                maxLength: 50,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "varchar(150)",
                oldUnicode: false,
                oldMaxLength: 150);

            migrationBuilder.CreateIndex(
                name: "IX_AdminDivisions_EnglishTitle_ProvinceId",
                table: "AdminDivisions",
                columns: new[] { "EnglishTitle", "ProvinceId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_AdminDivisions_ProvinceId",
                table: "AdminDivisions",
                column: "ProvinceId");

            migrationBuilder.CreateIndex(
                name: "IX_AdminDivisions_UrduTitle_ProvinceId",
                table: "AdminDivisions",
                columns: new[] { "UrduTitle", "ProvinceId" },
                unique: true);
        }
    }
}
