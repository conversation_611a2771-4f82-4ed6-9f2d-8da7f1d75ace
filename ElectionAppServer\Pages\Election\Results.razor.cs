﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ElectionAppServer.DTO;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.SignalR.Client;
using Microsoft.JSInterop;
using Syncfusion.Blazor.DropDowns;
using Syncfusion.Blazor.Notifications;

namespace ElectionAppServer.Pages.Election;

public partial class Results
{
    private SfDropDownList<int?, NamanigarDTO> ddlNamanigar;

    private HubConnection hubConnection;
    private SfToast ToastObj;
    private List<GeneralItemDTO> AssemblyTypesList { get; set; }
    private List<GeneralItemDTO> AssembliesList { get; set; }
    [CascadingParameter] private Task<AuthenticationState> AuthenticationStateTask { get; set; }
    private int ChangePollingStations { get; set; }
    private List<GeneralItemDTO> ConstituenciesList { get; set; }
    private string EditMode { get; set; } = "";
    private bool IsConnected => hubConnection.State == HubConnectionState.Connected;
    private bool IsPSWiseResultsExist { get; set; }
    private List<NamanigarDTO> NamanigarList { get; set; }

    private int NewResPollingStations
    {
        get
        {
            return EditMode switch
            {
                "Add" => ResultDetail[0].ResultPollingStations + ChangePollingStations,
                "Update" => ChangePollingStations,
                _ => ResultDetail[0].ResultPollingStations
            };
        }
    }

    private List<ResultDetailDTO> ResultDetail { get; set; } = new();

    //public int NewResPollingStations { get; set; }
    private List<GeneralItemDTO> SeatTypeList { get; set; }

    private int? SelectedAssemblyId { get; set; }
    private string SelectedAssemblyStrId { get; set; }
    private int? SelectedNamanigarId { get; set; }
    private int? SelectedSeatTypeId { get; set; }
    private int? SelectedStructureId { get; set; }
    private List<ItemDTO> StrAssembliesList { get; set; }
    private string UserId { get; set; }

    public ResultLogInfoDTO LastLog { get; set; }

    public async ValueTask DisposeAsync()
    {
        await hubConnection.DisposeAsync();
    }


    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        //return base.OnAfterRenderAsync(firstRender);
        if (firstRender)
        {
            var user = (await AuthenticationStateTask).User;
            if (user.Identity is { IsAuthenticated: true })
            {
                //ElectionsList = await Service.GetAll();
                //var dd =  await ProtectedSessionStore.GetAsync<ElectionStateDTO>("election");
                var dd = await localStorage.GetAsync<ElectionStateDTO>(await confreader.GetSettingValue("electionkey"));
                state.SetState(dd.Value);
                var info = await _UserManager.GetUserAsync(user);
                if (info != null) UserId = info.Id;
                await Task.CompletedTask;
                await FillDDLs();
                StateHasChanged();
            }
            else
            {
                try
                {
                    //await  ProtectedSessionStore.DeleteAsync("election");
                    await localStorage.DeleteAsync(await confreader.GetSettingValue("electionkey"));
                    state.SetState(new ElectionStateDTO
                        { ElectionId = 0, ElectionTitle = "", PhaseId = 0, PhaseTitle = "" });
                    StateHasChanged();
                }
                catch (Exception)
                {
                    // ignored
                }
            }
        }
    }

    protected override async Task OnInitializedAsync()
    {
        hubConnection = new HubConnectionBuilder()
            .WithUrl(NavigationManager.ToAbsoluteUri("/resultshub"))
            .Build();
        //hubConnection.On<ResultDTO>("ReceiveResult", (result) =>
        //{
        //	var ok = result;
        //});
        await hubConnection.StartAsync();
        //var user = (await authenticationStateTask).User;
        //var info = await _UserManager.GetUserAsync(user);
        //UserId = info.Id;
        await Task.CompletedTask;
    }

    //protected override async Task OnParametersSetAsync()
    //{
    //    if (SelectedAssemblyId != null && SelectedStructureId != null && SelectedSeatTypeId != null)
    //    {
    //        AssemblyTypesList = await Service.GetUserAssemblyTypes(UserId, state.state.ElectionId, state.state.PhaseId);
    //        //
    //    }
    //}

    private async Task CancelRecord()
    {
        ChangePollingStations = 0;
        EditMode = "";
        ResultDetail = await Service.GetElectionResults((int)SelectedAssemblyId, state.state.PhaseId,
            (int)SelectedStructureId, (int)SelectedSeatTypeId);
        StateHasChanged();
        await Task.CompletedTask;
    }

    private async Task FillDDLs()
    {
        //AssemblyTypesList = await Service.GetUserAssemblyTypes(UserId, state.state.ElectionId, state.state.PhaseId);
        AssembliesList = await Service.GetUserAssemblies(UserId, state.state.ElectionId, state.state.PhaseId);
    }

    private async Task FillDDLs2()
    {
        AssembliesList = await Service.GetUserAssemblies(UserId, state.state.ElectionId, state.state.PhaseId);
        StrAssembliesList = (from aa in AssembliesList
            orderby aa.EnglishTitle
            select new ItemDTO
            {
                Id = $"{aa.Id}",
                Title = aa.EnglishTitle
            }).ToList();
        if (AssembliesList.Count == 1)
        {
            SelectedAssemblyId = AssembliesList[0].Id;
            SelectedAssemblyStrId = SelectedAssemblyId.ToString();
            SeatTypeList = await Service.GetAssemblySeatTypes((int)SelectedAssemblyId);
            ConstituenciesList = await Service.GetUsersConstituencies(UserId, state.state.ElectionId,
                (int)SelectedAssemblyId, state.state.PhaseId);
            if (SeatTypeList.Count == 1) SelectedSeatTypeId = SeatTypeList[0].Id;
        }
        //StateHasChanged();
    }

    private async Task FillResult()
    {
        if (SelectedAssemblyId != null && SelectedStructureId != null && SelectedSeatTypeId != null)
        {
            ResultDetail = await Service.GetElectionResults((int)SelectedAssemblyId, state.state.PhaseId,
                (int)SelectedStructureId, (int)SelectedSeatTypeId);
            LastLog = await Service.GetLastLog((int)SelectedAssemblyId, state.state.PhaseId, (int)SelectedStructureId,
                (int)SelectedSeatTypeId);
            IsPSWiseResultsExist = await Service.IsPSWiseResultsExist(state.state.PhaseId, (int)SelectedStructureId,
                (int)SelectedSeatTypeId);
        }
        else
        {
            ResultDetail = new List<ResultDetailDTO>();
        }
    }

    //public async Task OnAssemblyChange(Syncfusion.Blazor.DropDowns.ChangeEventArgs<int?> args)
    private async Task OnAssemblyChange(ChangeEventArgs<int?, GeneralItemDTO> args)
    {
        SelectedAssemblyId = args.Value;
        SelectedStructureId = null;
        NamanigarList = null;
        StateHasChanged();
        if (SelectedAssemblyId == null)
        {
            ConstituenciesList = null;
            SeatTypeList = null;
            SelectedNamanigarId = null;
            SelectedSeatTypeId = null;
        }
        else
        {
            ConstituenciesList = await Service.GetUsersConstituencies(UserId, state.state.ElectionId,
                (int)SelectedAssemblyId, state.state.PhaseId);
            SeatTypeList = await Service.GetAssemblySeatTypes((int)SelectedAssemblyId);
            if (SeatTypeList.Any())
            {
                if (SeatTypeList.Count == 1)
                {
                    SelectedSeatTypeId = SeatTypeList[0].Id;
                }
                else
                {
                    if (!SeatTypeList.Any(k => k.Id == SelectedSeatTypeId)) SelectedSeatTypeId = null;
                }
            }
            else
            {
                SelectedAssemblyId = null;
            }
        }
    }

    private async Task OnAssemblyTypeChangeChange(ChangeEventArgs<int?, GeneralItemDTO> args)
    {
        if (args.Value != null)
        {
            var typeId = (int)args.Value;

            AssembliesList = await Service.GetUserAssemblies(UserId, state.state.ElectionId, typeId);
            StrAssembliesList = (from aa in AssembliesList
                select new ItemDTO
                {
                    Id = $"{aa.Id}",
                    Title = aa.EnglishTitle
                }).ToList();
        }
        else
        {
            AssembliesList = null;
        }

        ConstituenciesList = null;
        SeatTypeList = null;
        NamanigarList = null;
        //SeatTypeList = await Service.GetAssemblySeatTypes((int)SelectedAssemblyId);
        //ConstituenciesList = await Service.GetUsersConstituencies(UserId, state.state.ElectionId, (int)SelectedAssemblyId);
        //ResultDetail = await Service.GetElectionResults((int)SelectedAssemblyId, state.state.PhaseId, (int)SelectedStructureId, (int)SelectedSeatTypeId);
    }

    private async Task OnConstChange(ChangeEventArgs<int?, GeneralItemDTO> args)
    {
        try
        {
            await ddlNamanigar.ClearAsync();
        }
        catch (Exception)
        {
        }

        SelectedStructureId = args.Value;
        SelectedNamanigarId = null;
        StateHasChanged();
        if (SelectedStructureId != null)
        {
            NamanigarList = await Service.GetConstituencyNamanigars((int)SelectedStructureId, state.state.PhaseId);
            // Most Important!!!! Will be remove after testing...
            if (NamanigarList == null || NamanigarList.Count == 0)
                NamanigarList = new List<NamanigarDTO>
                {
                    new()
                    {
                        Code = "1", Id = 1, Name = "Joker", District = "All", Email = "<EMAIL>", Phone = "123"
                    }
                };
            await FillResult();
        }
        else
        {
            ResultDetail = null;
            NamanigarList = null;
        }
    }

    private async Task OnNamanigarChange(ChangeEventArgs<int?, NamanigarDTO> args)
    {
        SelectedNamanigarId = args.Value;
        StateHasChanged();
    }

    private async Task OnSeatTypChange(ChangeEventArgs<int?, GeneralItemDTO> args)
    {
        SelectedSeatTypeId = args.Value;
        await FillResult();
    }

    private async Task OpenForm(string mode)
    {
        if (mode == "Add")
        {
            if (ChangePollingStations <= 0)
            {
                await JSRuntime.InvokeVoidAsync("alert", "Please enter polling stations");
                EditMode = "";
                mode = "";
                return;
            }

            if (ChangePollingStations + ResultDetail[0].ResultPollingStations > ResultDetail[0].TotalPollingStations)
            {
                await JSRuntime.InvokeVoidAsync("alert",
                    "The result of polling stations count is exceeding against total polling stations.");
                mode = "";
                EditMode = "";
                return;
            }
        }

        if (ChangePollingStations <= 0)
        {
            //await JSRuntime.InvokeVoidAsync("alert", new string[] { "Please enter polling stations" });
            await DialogService.AlertAsync("Please enter polling stations", "Error");
            return;
        }

        EditMode = mode;
        //ChangePollingStations = 0;
        //await dlgForm.ShowAsync();
        //if (result_detail.Count > 0)
        //{
        //	if (mode == "Add")
        //	{
        //		ChangePollingStations = 0;
        //	}
        //	else
        //	{
        //		ChangePollingStations = result_detail[0].ResultPollingStations;
        //	}
        //}
        foreach (var res in ResultDetail)
        {
            res.Mode = mode;
            res.ChangeVotes = mode == "Add" ? 0 : res.Votes;
        }

        StateHasChanged();
        await Task.CompletedTask;
    }

    private async Task SaveRecord()
    {
        //var mode = await Service.GetConstituencyResultMode(SelectedStructureId, state.state.PhaseId);
        //if(mode != ResultMode.Lumsum && mode != ResultMode.Default)
        //{
        //	await DialogService.AlertAsync("Result posting mode has been changed, Please Use Polling Statin wise results entry page", "Info");
        //	return;
        //}


        var user = (await AuthenticationStateTask).User;

        try
        {
            //await Service.SaveResults(result_detail, ChangePollingStations, EditMode, user.Identity.Name, (int)selectedNamanigarId);
            var r = await Service.SaveResults(ResultDetail, (int)SelectedAssemblyId, state.state.PhaseId,
                (int)SelectedStructureId, (int)SelectedSeatTypeId, ChangePollingStations, EditMode, user.Identity.Name,
                (int)SelectedNamanigarId);
            r.PhaseId = state.state.PhaseId;
            r.OnAir = false;
            r.Status = "No";
            r.LastUpdatedOn = DateTime.Now;
            ResultDetail = await Service.GetElectionResults((int)SelectedAssemblyId, state.state.PhaseId,
                (int)SelectedStructureId, (int)SelectedSeatTypeId);
            var ticker = await Service.GetLiveResultDetailLB(state.state.ElectionId, (int)SelectedAssemblyId,
                state.state.PhaseId, (int)SelectedStructureId, (int)SelectedSeatTypeId);
            EditMode = "";
            if (!string.IsNullOrEmpty(ticker))
                r.Ticker = ticker;

            await hubConnection.SendAsync("SendResult", r);
            ChangePollingStations = 0;
            LastLog = await Service.GetLastLog((int)SelectedAssemblyId, state.state.PhaseId, (int)SelectedStructureId,
                (int)SelectedSeatTypeId);
            StateHasChanged();
        }
        catch (Exception ex)
        {
            var mm = new ToastModel
            {
                Title = "Error", Content = ex.Message, CssClass = "e-toast-danger", ShowProgressBar = true,
                Timeout = 5000, ShowCloseButton = true
            };
            await ToastObj.ShowAsync(mm);
        }
        //EditMode = "";
        //await Task.CompletedTask;
    }
}