﻿using ElectionAppServer.DTO;
using Microsoft.AspNetCore.SignalR;
using System.Threading.Tasks;

namespace ElectionAppServer.Hubs;

public class ResultsHub : Hub
{

	public async Task SendOfflineResult(ResultDTO result)
	{
		await Clients.All.SendAsync("ReceiveOfflineResult", result);
	}
	public async Task SendResult(ResultDTO result)
	{
		await Clients.All.SendAsync("ReceiveResult", result);
	}
	public async Task SendPublishNotification(ResultDTO result)
	{
		await Clients.All.SendAsync("ReceiveNotification", result);
	}
}

public class NamanigarResultsHub : Hub
{
	public async Task SendNamanigarResult(int constituencyId)
	{
		await Clients.All.SendAsync("ReceiveNamanigarResult", constituencyId);
	}
}