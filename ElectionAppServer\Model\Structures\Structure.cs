﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using ElectionAppServer.Model.Structures;

namespace ElectionAppServer.Model;

public abstract class Structure
{
    public int Id { get; set; }

    [Required] [StringLength(200)] public string UrduTitle { get; set; }

    public string EnglishTitle { get; set; }

    public virtual List<Nomination> Nominations { get; set; }
    //public virtual List<ElectionAssemblyConstituency> ElectionAssemblyConstituencies { get; set; }

    //public virtual List<NationalAssemblyHalka> NationalAssemblyHalkas { get; set; }
    //public virtual List<ProvincialAssemblyHalka> ProvincialAssemblyHalkas { get; set; }
    public string Code { get; set; }

    public bool IsPostponed { get; set; }
    public ResultStatus ResultStatus { get; set; }

    public int? MaleVoters { get; set; }
    public int? FemaleVoters { get; set; }
    public int? TotalPollingStations { get; set; }

    //public int? CasteId { get; set; }
    //public virtual Caste Caste { get; set; }
    //public int? LanguageId { get; set; }
    //public virtual Language Language { get; set; }
    [StringLength(200)] public string Languages { get; set; }

    [StringLength(200)] public string Castes { get; set; }

    [StringLength(2000)] public string Trivia { get; set; }

    [StringLength(2000)] public string GeneralTrivia { get; set; }

    [StringLength(2000)] public string ImportantPoliticalPersonalities { get; set; }

    [StringLength(2000)] public string Problems { get; set; }

    [StringLength(2000)] public string ImportantAreas { get; set; }

    public float? RuralAreaPer { get; set; }

    public float? UrbanAreaPer { get; set; }

    public bool IsSeat { get; set; } = false;

    public int ElectionId { get; set; }
    public virtual Election Election { get; set; }

    public virtual List<UserConstituency> UserConstituencies { get; set; }

    [NotMapped] public int TotalVoters => MaleVoters ?? 0 + FemaleVoters ?? 0;

    public int? Population { get; set; }

    [StringLength(200)] public string MajorityIncomeSource { get; set; }

    [StringLength(50)] public string LiteracyRate { get; set; }

    [StringLength(50)] public string AverageHouseholdIncome { get; set; }

    [StringLength(50)] public string Area { get; set; }

    public int? AssemblyId { get; set; }
    public virtual ElectionAssembly Assembly { get; set; }
    public virtual List<PollingStation> PollingStations { get; set; }

    //public int? AssemblyId { get; set; }
    //public virtual Assembly Assembly { get; set; }
    [ForeignKey("PrevStructure")] public int? PrevStructureId { get; set; }

    public Structure PrevStructure { get; set; }
    public virtual List<StructureMapping> StructureMappings { get; set; }

    public virtual List<StructureMapping> PreviousStructureMappings { get; set; }

    public virtual List<ResultLog> ResultLogs { get; set; }
    public virtual List<NamanigarConstituency> NamanigarConstituencies { get; set; }
    public bool HasBilaMokablia { get; set; } = false;
    public virtual List<RePoll> RePolls { get; set; }
    public virtual List<RePollSeat> RePollSeats { get; set; }

    public virtual List<PollingScheme> PollingSchemes { get; set; }
    public List<StructureProblem> StructureProblems { get; set; } = new List<StructureProblem>();
    #region Audit Log Fields

    public DateTime CreatedDate { get; set; } = DateTime.Now;
    public DateTime? ModifiedDate { get; set; }

    [StringLength(450)] public string CreatedBy { get; set; }

    [StringLength(450)] public string ModifiedBy { get; set; }

    //public object RePollseats { get; internal set; }

    #endregion Audit Log Fields
}

public enum ResultStatus
{
    Result = 1,
    BilaMokablia = 2,
    Moltavi = 3
}