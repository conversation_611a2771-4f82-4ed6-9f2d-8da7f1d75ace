@import url('open-iconic/font/css/open-iconic-bootstrap.min.css');
@import url('https://fonts.googleapis.com/css2?family=Noto+Nastaliq+Urdu&display=swap');


@media print {
    button {
        display: none;
    }
}

/*@font-face {
    font-family: 'Urdu Nastaleeq';*/
    /*src: url('Jameel-Noori-Nastaleeq.woff') format('woff');*/
    /*src: url('NotoNastaliqUrdu-VariableFont_wght.ttf') format('ttf');*/
    /*src: url('Fajer-Noori-Nastalique.woff') format('woff');*/
    /*src: url('Nafees-Nastaleeq.woff') format('woff');*/
    /*src: url('Nafees-Nastaleeq.woff') format('woff');*/
/*}*/

html, body {
    font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

a, .btn-link {
    color: #0366d6;
}

.btn-primary {
    color: #fff;
    background-color: #1b6ec2;
    border-color: #1861ac;
}

app {
    position: relative;
    display: flex;
    flex-direction: column;
}

.top-row {
    height: 3.5rem;
    display: flex;
    align-items: center;
}

.main {
    flex: 1;
}

    .main .top-row {
        background-color: #f7f7f7;
        border-bottom: 1px solid #d6d5d5;
        justify-content: flex-end;
    }

        .main .top-row > a, .main .top-row .btn-link {
            white-space: nowrap;
            margin-left: 1.5rem;
        }

        .main .top-row a:first-child {
            overflow: hidden;
            text-overflow: ellipsis;
        }

.sidebar {
    /*background-image: linear-gradient(180deg, rgb(5, 39, 103) 0%, #3a0647 70%);*/
}

    .sidebar .top-row {
        background-color: rgba(0,0,0,0.4);
    }

    .sidebar .navbar-brand {
        font-size: 1.1rem;
    }

    .sidebar .oi {
        width: 2rem;
        font-size: 1.1rem;
        vertical-align: text-top;
        top: -2px;
    }

    .sidebar .nav-item {
        font-size: 0.9rem;
        padding-bottom: 0.1rem;
    }

        .sidebar .nav-item:first-of-type {
            padding-top: 0.2rem;
        }

        .sidebar .nav-item:last-of-type {
            padding-bottom: 1rem;
        }

        .sidebar .nav-item a {
            color: #d7d7d7;
            border-radius: 4px;
            height: 2rem;
            display: flex;
            align-items: center;
            line-height: 2.5rem;
        }

            .sidebar .nav-item a.active {
                background-color: rgba(255,255,255,0.25);
                color: white;
            }

            .sidebar .nav-item a:hover {
                background-color: rgba(255,255,255,0.1);
                color: white;
            }

.content {
    padding-top: 1.1rem;
}

.navbar-toggler {
    background-color: rgba(255, 255, 255, 0.1);
}

.valid.modified:not([type=checkbox]) {
    outline: 1px solid #26b050;
}

.invalid {
    outline: 1px solid red;
}

.validation-message {
    color: red;
}

#blazor-error-ui {
    background: lightyellow;
    bottom: 0;
    box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.2);
    display: none;
    left: 0;
    padding: 0.6rem 1.25rem 0.7rem 1.25rem;
    position: fixed;
    width: 100%;
    z-index: 1000;
}

    #blazor-error-ui .dismiss {
        cursor: pointer;
        position: absolute;
        right: 0.75rem;
        top: 0.5rem;
    }

@media (max-width: 767.98px) {
    .main .top-row:not(.auth) {
        display: none;
    }

    .main .top-row.auth {
        justify-content: space-between;
    }

    .main .top-row a, .main .top-row .btn-link {
        margin-left: 0;
    }
}

@media (min-width: 2000px) {
    app {
        flex-direction: row;
    }

    .sidebar {
        width: 250px;
        height: 100vh;
        position: sticky;
        top: 0;
    }

    .main .top-row {
        position: sticky;
        top: 0;
    }

    .main > div {
        padding-left: 2rem !important;
        padding-right: 1.5rem !important;
    }

    .navbar-toggler {
        display: none;
    }

    .sidebar .collapse {
        /* Never collapse the sidebar for wide screens */
        display: block;
    }
}

.sec {
    font-weight: bold;
    font-size: 1rem;
}

.myimg {
    width: 80px;
    object-fit: cover;
    border-radius: 10px;
    padding: 2px;
    margin-right: 5px;
    border: 1px solid #a16eda;
    -webkit-box-shadow: 4px 4px 13px 0px rgba(0,0,0,0.43);
    -moz-box-shadow: 4px 4px 13px 0px rgba(0,0,0,0.43);
    box-shadow: 4px 4px 13px 0px rgba(0,0,0,0.43);
}

.bimg {
    width: 235px;
    height: 280px;
    object-fit: cover;
    border-radius: 10px;
    padding: 2px;
    border: 1px solid #a16eda;
    margin-right: 5px;
    -webkit-box-shadow: 4px 4px 13px 0px rgba(0,0,0,0.43);
    -moz-box-shadow: 4px 4px 13px 0px rgba(0,0,0,0.43);
    box-shadow: 4px 4px 13px 0px rgba(0,0,0,0.43);
}

.hh {
    font-weight: bold;
    padding: 4px;
    background-color: gainsboro;
}

.authflex {
    display: flex;
}

.mainpage {
    flex: 1
}

.candidateinfo {
    display: flex;
}

    .candidateinfo img {
        width: 120px;
        margin-right: 10px;
    }

.fileselector {
    flex: 1;
}

.urdu-column {
    font-family: 'Noto Nastaliq Urdu';
    /*src: url(Jameel-Noori-Nastaleeq.woff) format("woff");*/
    font-size: 18px !important;
    line-height: 1.4;
    text-align: right;
    direction: rtl;
}

.urdu-cap {
    font-family: 'Noto Nastaliq Urdu';
    font-size: 16px;
    line-height: 1.5;
    text-align: right;
    direction: rtl;
}

.urdu-sm {
    font-family: 'Noto Nastaliq Urdu';
    font-size: 14px !important;
    line-height: 1.5;
    text-align: right;
    direction: rtl;
}

.tabheader {
    background-color: #e3165b;
    color: white;
    padding-left: 8px;
    padding-bottom: 4px;
    padding-top: 4px;
}

label {
    margin: 0;
}

.btnSearch {
    margin-left: 5px;
    margin-bottom: 4px;
}

.rptfield {
    display: flex;
    align-items: baseline
}

.rptdata {
    flex: 1;
    margin-left: 4px;
    padding-bottom: 3px;
    background: #fff;
    border-radius: 3px;
    border-bottom: 1px solid lightgrey;
    padding-left: 4px;
    margin-bottom: 3px;
}

.esection {
}

.alog {
    font-size: 11px;
}

.newresult {
    height: 50px;
    background-color: red; /* For browsers that do not support gradients */
    background-image: linear-gradient(pink, white);
}

.pubresult {
    height: 50px;
    background-color: red; /* For browsers that do not support gradients */
    background-image: linear-gradient(lightgreen, white);
}

.viewresult {
    height: 50px;
    background-color: #D9E1F2; /* For browsers that do not support gradients */
    background-image: linear-gradient(#D9E1F2, white);
}

.tiker-last-update {
    font-family: 'Roboto';
    font-size: 11px;
}

.actcell {
    display: flex;
    gap: 5px;
    justify-content: space-between
}

.ticker-block {
    direction: rtl;
    text-align: right;
    background-color: #87cefa;
    padding: 5px;
    line-height: 1.7;
    margin: 5px;
    max-width: 87vw !important;
    margin-right: 60px;
}


.mud-drawer-content, .mud-navmenu {
    background-color: #343a40;
    color: white;
}

.mud-nav-item .active {
    background-color: lightgray !important;
    color: black !important;
}

.mud-appbar {
    background-color: #e6f6fb !important;
}
