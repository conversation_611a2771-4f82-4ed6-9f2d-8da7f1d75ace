﻿@page "/db1"
@*@using DevExpress.Blazor*@
@using ElectionAppServer.DTO.Dashboard
@inherits OwningComponentBase<DashboardDataService>

<h3>StructureDashboard</h3>
<div class="row">


    @foreach (var item in sericeData)
    {
        <div class="col-3">
            @*<DxChart Data="@item.Data">
				<DxChartTitle Text="@item.Title" />
				<DxChartLegend VerticalAlignment="@VerticalEdge.Bottom" Position="@RelativePosition.Outside" />
				<DxChartPieSeries ValueField="@((PieChartDTO ii) => ii.Data)" ArgumentField="@(ii => ii.Label)" AggregationMethod="@Enumerable.Sum">
					<DxChartSeriesLabel Visible="true" />
				</DxChartPieSeries>
			</DxChart>*@
        </div>
    }
</div>

@code {


    List<PieChartSeriesDTO> sericeData;

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();

        sericeData = await Service.ProvinceWiseAssemblies(1);
    }


}