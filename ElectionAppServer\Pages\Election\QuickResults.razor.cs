using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ElectionAppServer.DTO;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Syncfusion.Blazor.DropDowns;
using Syncfusion.Blazor.Notifications;

namespace ElectionAppServer.Pages.Election;

public partial class QuickResults
{
    private int? SelectedAssemblyId;

    private string SelectedAssemblyStrId;

    //--> Not In Use //private int? selectedNamanigarId;
    private VCResultDTO SelectedObject = new();
    private int? SelectedSeatTypeId;
    private SfToast ToastObj;
    private List<GeneralItemDTO> Assmblieslist { get; set; }
    private List<GeneralItemDTO> ConstituencyList { get; set; }
    private List<GeneralItemDTO> SeatTypeList { get; set; }
    private List<GeneralItemDTO> PartiesList { get; set; }
    private List<GeneralItemDTO> SymbolsList { get; set; }
    private int? SelectedStructureId { get; set; }
    private List<NamanigarDTO> namanigarList { get; set; }
    [CascadingParameter] private Task<AuthenticationState> authenticationStateTask { get; set; }
    private string UserId { get; set; }

    private int? SelectedPartyId { get; set; }
    private int? SelectedSymbolId { get; set; }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        //return base.OnAfterRenderAsync(firstRender);
        if (firstRender)
        {
            var user = (await authenticationStateTask).User;
            if (user.Identity is { IsAuthenticated: true })
            {
                // ElectionsList = await Service.GetAll();
                // var dd =  await ProtectedSessionStore.GetAsync<ElectionStateDTO>("election");
                var dd = await localStorage.GetAsync<ElectionStateDTO>(await confreader.GetSettingValue("electionkey"));
                state.SetState(dd.Value);
                var info = await _UserManager.GetUserAsync(user);
                UserId = info.Id;
                await Task.CompletedTask;
                await FillDDLs();
                SelectedObject.PhaseId = dd.Value.PhaseId;
                StateHasChanged();
            }
            else
            {
                try
                {
                    //await  ProtectedSessionStore.DeleteAsync("election");
                    await localStorage.DeleteAsync(await confreader.GetSettingValue("electionkey"));
                    state.SetState(new ElectionStateDTO
                        { ElectionId = 0, ElectionTitle = "", PhaseId = 0, PhaseTitle = "" });
                    StateHasChanged();
                }
                catch (Exception)
                {
                    // ignored
                }
            }
        }
    }

    private async Task FillDDLs()
    {
        Assmblieslist = await Service.GetUserAssemblies(UserId, state.state.ElectionId, state.state.PhaseId);
        PartiesList = await Service.GetParties();
        SymbolsList = await Service.GetSymbols();
        if (Assmblieslist.Count == 1)
        {
            SelectedAssemblyId = Assmblieslist[0].Id;
            SelectedAssemblyStrId = SelectedAssemblyId.ToString();
            SeatTypeList = await Service.GetAssemblySeatTypes((int)SelectedAssemblyId);
            ConstituencyList = await Service.GetUsersConstituencies(UserId, state.state.ElectionId,
                (int)SelectedAssemblyId, state.state.PhaseId);
            if (SeatTypeList.Count == 1) SelectedSeatTypeId = SeatTypeList[0].Id;
        }
        // StateHasChanged();
    }

    private async Task OnSeatTypChange(ChangeEventArgs<int?, GeneralItemDTO> args)
    {
        // SelectedSeatTypeId = args.Value;
        // await FillResult();
        await Task.CompletedTask;
    }

    private async Task OnConstChange(ChangeEventArgs<int?, GeneralItemDTO> args)
    {
        SelectedStructureId = args.Value;
        //selectedNamanigarId = null;
        if (SelectedStructureId != null)
        {
            namanigarList = await Service.GetConstituencyNamanigars((int)SelectedStructureId, state.state.PhaseId);
            // Most Important!!!! Will be remove after testing...
            if (namanigarList == null || namanigarList.Count == 0)
                namanigarList = new List<NamanigarDTO>
                {
                    new()
                    {
                        Code = "1", Id = 1, Name = "Joker", District = "All", Email = "<EMAIL>", Phone = "123"
                    }
                };
            // await FillResult();
        }
        else
        {
            // result_detail = null;
            namanigarList = null;
        }
    }


    private async Task OnAssemblyChange(ChangeEventArgs<int?, GeneralItemDTO> args)
    {
        SelectedAssemblyId = args.Value;
        SelectedStructureId = null;
        namanigarList = null;
        StateHasChanged();
        if (SelectedAssemblyId == null)
        {
            ConstituencyList = null;
            SeatTypeList = null;
            //selectedNamanigarId = null;
            SelectedSeatTypeId = null;
        }
        else
        {
            ConstituencyList = await Service.GetUsersConstituenciesVCNC(UserId, state.state.ElectionId,
                (int)SelectedAssemblyId, state.state.PhaseId);
            SeatTypeList = await Service.GetAssemblySeatTypes((int)SelectedAssemblyId);
            if (SeatTypeList.Any())
            {
                if (SeatTypeList.Count == 1)
                {
                    SelectedSeatTypeId = SeatTypeList[0].Id;
                }
                else
                {
                    if (!SeatTypeList.Any(k => k.Id == SelectedSeatTypeId)) SelectedSeatTypeId = null;
                }
            }
            else
            {
                SelectedAssemblyId = null;
            }
        }
    }

    private async Task OnPartyChange(ChangeEventArgs<int?, GeneralItemDTO> obj)
    {
        if (obj.Value != null)
        {
            SelectedPartyId = obj.Value;
            SelectedSymbolId = await Service.GetPartySymbol((int)SelectedPartyId);
        }
        else
        {
            SelectedPartyId = null;
        }

        StateHasChanged();
    }

    private async Task SaveRecord()
    {
        SelectedObject.PhaseId = state.state.PhaseId;
        var user = (await authenticationStateTask).User;
        var op = await Service.SaveVCNCResults(SelectedObject, state.state.PhaseId, user.Identity.Name);
        if (op.ToUpper() == "OK")
        {
            SelectedObject = new VCResultDTO { PhaseId = state.state.PhaseId };
            var tm = new ToastModel
            {
                Content = "Record has Saved successfully", Title = "Saved", Timeout = 5000, CssClass = "e-success",
                ShowCloseButton = true
            };
            await ToastObj.ShowAsync(tm);
        }
        else
        {
            var tm = new ToastModel
                { Content = op, Title = "Error", Timeout = 5000, CssClass = "e-danger", ShowCloseButton = true };
            await ToastObj.ShowAsync(tm);
        }
    }
}