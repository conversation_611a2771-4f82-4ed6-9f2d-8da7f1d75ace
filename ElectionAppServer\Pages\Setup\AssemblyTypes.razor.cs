using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using ElectionAppServer.Data;
using ElectionAppServer.DTO;
using ElectionAppServer.Model;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.JSInterop;
using Syncfusion.Blazor.Grids;
using Syncfusion.Blazor.Notifications;
using Syncfusion.Blazor.Popups;

namespace ElectionAppServer.Pages.Setup;

public partial class AssemblyTypes
{
    private const string FormTitle = "Create AssemblyType";
    private SfDialog _dlgForm;

    private bool _isDlgVisible;

    private List<AssemblyType> _objList;

    private string _saveButtonText = "Save";

    private AssemblyType _selectedObj = new();
/*
    public string ToastContent = "Unable to delete Record, Related Record(s) Exist";
*/

    private SfToast _toastObj;

    public SfGrid<AssemblyType> Grid { get; set; }

    // AuthenticationState is available as a CascadingParameter
    [CascadingParameter] private Task<AuthenticationState> AuthenticationStateTask { get; set; }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        //return base.OnAfterRenderAsync(firstRender);
        if (firstRender)
        {
            var user = (await AuthenticationStateTask).User;
            if (user.Identity is { IsAuthenticated: true })
            {
                //var dd =  await ProtectedSessionStore.GetAsync<ElectionStateDTO>("election");
                var dd = await localStorage.GetAsync<ElectionStateDTO>(await confreader.GetSettingValue("electionkey"));
                state.SetState(dd.Value);
            }
            else
            {
                try
                {
                    //await  ProtectedSessionStore.DeleteAsync("election");
                    await localStorage.DeleteAsync(await confreader.GetSettingValue("electionkey"));
                    state.SetState(new ElectionStateDTO
                        { ElectionId = 0, ElectionTitle = "", PhaseId = 0, PhaseTitle = "" });
                }
                catch (Exception)
                {
                    // ignored
                }

                NavigationManager.NavigateTo("");
            }

            StateHasChanged();
        }
    }

    protected override async Task OnInitializedAsync()
    {
        //var user = (await AuthenticationStateTask).User;
        _objList = await Service.GetAll();
    }

    private void BeforeOpen(BeforeOpenEventArgs args)
    {
        args.MaxHeight = null;
    }

    private void ClearData()
    {
        _selectedObj.Id = 0;
        _selectedObj.UrduTitle = "";
        _selectedObj.EnglishTitle = "";
    }

    private async Task DeleteRecord(int id)
    {
        var res = await JSRuntime.InvokeAsync<bool>("confirm", "Are you want to delete this record?");
        if (res == false)
            return;
        try
        {
            //var msg2 = 
            await Service.Delete(id);
            _objList = await Service.GetAll();
            await Grid.Refresh();
            ClearData();
        }
        catch (Exception ex)
        {
            var m = new ToastModel { Content = ex.Message, Title = "Error" };
            await _toastObj.ShowAsync(m);
        }
    }

    private async Task OpenCreateForm()
    {
        _selectedObj = new AssemblyType();
        await _dlgForm.ShowAsync();
        _saveButtonText = "Create";
    }

    private async Task OpenEditForm(AssemblyType st)
    {
        _selectedObj.Id = st.Id;
        _selectedObj.UrduTitle = st.UrduTitle;
        _selectedObj.EnglishTitle = st.EnglishTitle;
        await _dlgForm.ShowAsync();
        _saveButtonText = "Update";
    }

    private async Task SaveAssemblyTypeData()
    {
        var user = (await AuthenticationStateTask).User;
        try
        {
            if (user.Identity != null) await Service.Save(_selectedObj, user.Identity.Name);
        }
        catch (Exception ex)
        {
            var m = new ToastModel { Content = ex.Message, Title = "Error" };
            await _toastObj.ShowAsync(m);
        }

        _objList = await Service.GetAll();
        await Grid.Refresh();
        ClearData();
        await _dlgForm.HideAsync();
    }

    private async Task TranslateToUrdu()
    {
        //if (string.IsNullOrEmpty(selectedObj.UrduTitle))
        try
        {
            if (string.IsNullOrEmpty(_selectedObj.UrduTitle))
                _selectedObj.UrduTitle = await Translation.TranslateText(_selectedObj.EnglishTitle);
        }
        catch (Exception)
        {
            // ignored
        }
    }
}