﻿using System;
using System.Collections.Generic;
using ElectionAppServer.Model;

namespace ElectionAppServer.DTO;

public class CandidateDetailDTO
{
    public int Id { get; set; }
    public NominationType NominationType { get; set; }
    public string UrduName { get; set; }
    public string EnglishName { get; set; }
    public string FatherEnglishName { get; set; }
    public string FatherUrduName { get; set; }
    public Gender? Gender { get; set; }
    public DateTime? DateOfBirth { get; set; }
    public string Trivia { get; set; }
    public string StrDistrict { get; set; }
    public string District { get; set; }
    public string Profession { get; set; }
    public string Education { get; set; }
    public string Language { get; set; }
    public string Caste { get; set; }
    public string TotalAssets { get; set; }
    public string ContactNumber { get; set; }
    public bool IsFresh { get; set; } = false;
    public string EducationDegree { get; set; }
    public List<PartyAffDTO> PartyAffiliations { get; set; }
    public List<PoliticalCareerDTO> PoliticalCareers { get; set; }
    public List<NominationDTO> Nominations { get; set; }
    public string CandidateTypeStr { get; set; }
}

public class PartyAffDTO
{
    public string Party { get; set; }
    public string Position { get; set; }
    public DateTime DateFrom { get; set; }
    public DateTime? DateTo { get; set; }
}

//public class PoliticalCareerDTO
//{
//	public string Position { get; set; }
//	public DateTime DateFrom { get; set; }
//	public DateTime? DateTo { get; set; }
//}