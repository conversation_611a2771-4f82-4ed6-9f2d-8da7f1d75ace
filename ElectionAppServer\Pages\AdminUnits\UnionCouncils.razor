﻿@page "/ucs/{townId:int}"
@page "/ucs/{townId:int}/{ucId:int}"
@using FilterType = Syncfusion.Blazor.Grids.FilterType
@inherits OwningComponentBase<ElectionStructureService>
@attribute [Authorize(Roles = "Administrators,Data Viewer")]
<MudText Typo="Typo.h5">Village/Neighborhood/Union Councils</MudText>
@*<PageCaption Title="Village/Neighborhood/Union Councils"></PageCaption>*@
@*<a href="/elections">@ElectionTitle</a> <span>></span>*@
<SfDialog Width="900px" @bind-Visible="@isDlgVisible" @ref="dlgForm" IsModal="true" ShowCloseIcon="true">
    <DialogEvents OnOpen="beforeOpen"></DialogEvents>
    <DialogTemplates>
        <Header>Tier 2</Header>
        <Content>
            <AdminUnitForm SelectedObj="@selectedObj" OnValidDataSubmit="SaveData"></AdminUnitForm>

        </Content>
    </DialogTemplates>
</SfDialog>
<SfToast @ref="ToastObj">
    <ToastPosition X="Right"></ToastPosition>
</SfToast>
<section>
    @foreach (var bc in BredCrum)
    {
        <a href="@bc.UrduTitle">@bc.EnglishTitle</a>
        <span>></span>
    }
    Tier 2
    @if (objList == null)
    {
        <p>
            <em>Loading...</em>
        </p>
    }
    else
    {
        <AuthorizeView Roles="Administrators">
            <div class="row">
                <SfButton IsPrimary="true" CssClass="e-primary mb-2" OnClick="OpenCreateForm">Create Tier 2</SfButton>
            </div>
        </AuthorizeView>
        <div class="row">
            <SearchStructure ElectionId="@electionId"></SearchStructure>
        </div>
        <div class="row">
            <SfGrid DataSource="@objList" AllowFiltering="true" ModelType="@selectedObj" @ref="Grid" AllowSorting="true" AllowPaging="true" AllowTextWrap="true" Width="100%" Height="calc(100vh - 310px)">
                <GridEvents QueryCellInfo="CustomizeCell" TValue="ElcStructureDTO"></GridEvents>
                <GridFilterSettings Type="FilterType.Excel"></GridFilterSettings>
                <GridPageSettings PageSize="10"></GridPageSettings>
                <GridColumns>
                    <GridColumn AutoFit="true" Field="Id" HeaderText="Id"></GridColumn>
                    <GridColumn AutoFit="true" Field="Code" HeaderText="Code"></GridColumn>
                    <GridColumn AutoFit="true" Field="UrduTitle" HeaderText="Urdu Title" TextAlign="TextAlign.Right"></GridColumn>
                    <GridColumn AutoFit="true" Field="EnglishTitle" HeaderText="English Title"></GridColumn>
                    @*<GridColumn AutoFit="true" Field="Assembly" HeaderText="Assembly"></GridColumn>*@
                    @*<GridColumn Field="MaleVoters" HeaderText="Male Voters"></GridColumn>
					<GridColumn Field="FemaleVoters" HeaderText="Female Voters"></GridColumn>
					<GridColumn Field="TotalPollingStations" HeaderText="Polling Stations"></GridColumn>*@
                    <GridColumn AutoFit="true" Field="SubUnits" HeaderText="Sub Tiers"></GridColumn>
                    <GridColumn Width="200px" HeaderText="Audit Info">
                        <Template Context="kk">
                            @{
                                var oo = kk as ElcStructureDTO;
                            <div style="font-size:11px">
                                <span>Created By: @oo.CreateBy</span><span> On @oo.CreatedDate</span>
                                @if (!string.IsNullOrEmpty(oo.ModifiedBy))
                                {
                                    <br/>
                                    <span>Modified By: @oo.ModifiedBy</span>
                                    <span> On @oo.ModifiedDate</span>
                                }
                            </div>}
                        </Template>
                    </GridColumn>
                    <GridColumn Width="190px" HeaderText="Actions" AllowFiltering="false">
                        <Template Context="ss">
                            @{
                                var kk = ss as ElcStructureDTO;
                                <a target="@($"au{kk.Id}")" href="report/adminunit/@kk.Id">
                                    <span class="e-control e-btn e-lib e-success">
                                        <i class="fas fa-clipboard-check"></i>
                                    </span>
                                </a>
                                <AuthorizeView Roles="Administrators">
                                    <SfButton IsPrimary="true" CssClass="e-danger" OnClick="@(() => DeleteRecord(kk.Id))">
                                        <i class="fas fa-trash-alt"></i>
                                    </SfButton>
                                </AuthorizeView>
                                <SfButton IsPrimary="true" CssClass="e-primary" OnClick="@(() => OpenEditForm(kk))">
                                    <i class="fas fa-pencil-alt"></i>
                                </SfButton>
                                <a style="padding-left:3px;" href="/wards/@kk.Id">Tier 3</a>
                            }
                        </Template>
                    </GridColumn>
                </GridColumns>
            </SfGrid>
        </div>
    }
</section>