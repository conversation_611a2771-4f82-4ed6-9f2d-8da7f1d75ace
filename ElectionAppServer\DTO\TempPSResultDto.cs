﻿namespace ElectionAppServer.DTO;

public class TempPSResultDto
{
    public int ConstituencyId { get; set; }
    public string Code { get; set; }
    public string Constituency { get; set; }
    public string Party { get; set; }
    public string Candidate { get; set; }
    public string <PERSON><PERSON>ee { get; set; }
    public int AssemblyId { get; set; }
    public string Assembly { get; set; }
    public int PhaseId { get; set; }
    public string Phase { get; set; }
    public int SeatTypeId { get; set; }
    public string SeatType { get; set; }
    public int Votes { get; set; }
    public int PSCount { get; set; }
    public int TotalPollingStations { get; set; }
    public string RunnerUpParty { get; set; }
    public string RunnerUp { get; set; }
    public string RunnerUpKaKee { get; set; }
    public int RunnerUpVotes { get; set; }
}