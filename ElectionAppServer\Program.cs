using System;
using System.Linq;
using ElectionAppServer.Areas.Identity;
using ElectionAppServer.Data;
using ElectionAppServer.Hubs;
using ElectionAppServer.Model;
using FluentValidation.AspNetCore;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.DataProtection;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.ResponseCompression;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using MudBlazor.Services;
using Syncfusion.Blazor;
using Syncfusion.Blazor.Popups;
using Syncfusion.Licensing;

var builder = WebApplication.CreateBuilder(args);
staticConfig = builder.Configuration;

// Add services to the container.
builder.Services.AddDbContextFactory<ApplicationDbContext>(options =>
    options.UseSqlServer(
        builder.Configuration.GetConnectionString("DefaultConnection")));

builder.Services.AddScoped(p => p.GetRequiredService<IDbContextFactory<ApplicationDbContext>>().CreateDbContext());

builder.Services.AddDefaultIdentity<ApplicationUser>(options => options.SignIn.RequireConfirmedAccount = true)
    .AddRoles<IdentityRole>()
    .AddEntityFrameworkStores<ApplicationDbContext>();

builder.Services.AddRazorPages().AddFluentValidation();

builder.Services.AddServerSideBlazor()
    .AddCircuitOptions(options => { options.DetailedErrors = true; });

builder.Services.AddSignalR();
builder.Services.AddAntiforgery();
builder.Services.AddDataProtection().SetDefaultKeyLifetime(TimeSpan.FromDays(14));

builder.Services.AddMudServices();

builder.Services.AddResponseCompression(opts =>
{
    opts.MimeTypes = ResponseCompressionDefaults.MimeTypes.Concat(
        ["application/octet-stream"]);
});

builder.Services.AddScoped<SfDialogService>();

builder.Services.AddSyncfusionBlazor();
builder.Services
    .AddScoped<AuthenticationStateProvider, RevalidatingIdentityAuthenticationStateProvider<ApplicationUser>>();

builder.Services.AddScoped<SeatTypeService>();
builder.Services.AddScoped<EducationService>();
builder.Services.AddScoped<CasteService>();
builder.Services.AddScoped<LanguageService>();
builder.Services.AddScoped<ProfessionService>();
builder.Services.AddScoped<ProvinceService>();
builder.Services.AddScoped<DivisionService>();
builder.Services.AddScoped<DistrictService>();
builder.Services.AddScoped<AssemblyTypeService>();
builder.Services.AddScoped<ConstituencyService>();
builder.Services.AddScoped<SymbolService>();
builder.Services.AddScoped<PartyService>();
builder.Services.AddScoped<CandidateService>();
builder.Services.AddScoped<TownService>();
builder.Services.AddScoped<UnionCouncilService>();
builder.Services.AddScoped<WardService>();
builder.Services.AddScoped<ReportService>();
builder.Services.AddScoped<ElectionService>();
builder.Services.AddScoped<ElectionAssemblyService>();
builder.Services.AddScoped<NominationDataService>();
builder.Services.AddScoped<ElectionStructureService>();
builder.Services.AddScoped<ConfigurationReader>();
builder.Services.AddScoped<ElectionStateService>();
builder.Services.AddScoped<UserManagementDataService>();
builder.Services.AddScoped<ElectionResultsDataService>();
builder.Services.AddScoped<CoalitionDataService>();
builder.Services.AddScoped<RegionDataService>();
builder.Services.AddScoped<DashboardDataService>();
builder.Services.AddScoped<PollingStationDataService>();
builder.Services.AddScoped<NamanigarDataService>();
builder.Services.AddScoped<PartyPositionDataService>();
builder.Services.AddScoped<PanelDataService>();
builder.Services.AddScoped<ClipboardService>();
builder.Services.AddScoped<DistrictWisePartyService>();
builder.Services.AddScoped<MenuService>();
builder.Services.AddScoped<AdminDivisionDataService>();
builder.Services.AddScoped<ProblemService>();

var app = builder.Build();

// Configure the HTTP request pipeline.
app.UseResponseCompression();

SyncfusionLicenseProvider.RegisterLicense(
    "Mzk4MjE1MUAzMzMwMmUzMDJlMzAzYjMzMzAzYkhZQ1Y3R1gvR3B2MmQwTWdJMzlSZVhmVTRNb3g5UEEreGs2cGppVXMxVmM9");

if (app.Environment.IsDevelopment())
{
    app.UseDeveloperExceptionPage();
}
else
{
    app.UseExceptionHandler("/Error");
    app.UseHsts();
}

app.UseHttpsRedirection();
app.UseStaticFiles();

app.UseAuthentication();
app.UseRouting();
app.UseAuthorization();

app.MapControllers();
app.MapBlazorHub();
app.MapFallbackToPage("/_Host");
app.MapHub<CandidteHub>("/candidatehub");
app.MapHub<PartyHub>("/partyhub");
app.MapHub<SymbolHub>("/symbolhub");
app.MapHub<ResultsHub>("/resultshub");
app.MapHub<ResultsHub>("/offlineresultshub");
app.MapHub<NamanigarResultsHub>("/nnresultshub");
app.MapHub<PSResultsHub>("/psresultshub");

app.Run();