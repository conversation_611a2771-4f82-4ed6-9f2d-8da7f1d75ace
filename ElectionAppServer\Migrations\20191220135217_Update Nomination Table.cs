﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace ElectionAppServer.Migrations
{
	public partial class UpdateNominationTable : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropPrimaryKey(
				 name: "PK_Nominations",
				 table: "Nominations");

			migrationBuilder.AlterColumn<int>(
				 name: "Weight",
				 table: "Nominations",
				 nullable: false,
				 oldClrType: typeof(int),
				 oldType: "int",
				 oldNullable: true);

			migrationBuilder.AddColumn<int>(
				 name: "Id",
				 table: "Nominations",
				 nullable: false,
				 defaultValue: 0)
				 .Annotation("SqlServer:Identity", "1, 1");

			migrationBuilder.AddPrimaryKey(
				 name: "PK_Nominations",
				 table: "Nominations",
				 column: "Id");

			migrationBuilder.CreateIndex(
				 name: "IX_Nominations_ElectionAssemblyId_StructureId_SeatTypeId_CandidteId_ElectionPhaseId",
				 table: "Nominations",
				 columns: new[] { "ElectionAssemblyId", "StructureId", "SeatTypeId", "CandidteId", "ElectionPhaseId" },
				 unique: true);
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropPrimaryKey(
				 name: "PK_Nominations",
				 table: "Nominations");

			migrationBuilder.DropIndex(
				 name: "IX_Nominations_ElectionAssemblyId_StructureId_SeatTypeId_CandidteId_ElectionPhaseId",
				 table: "Nominations");

			migrationBuilder.DropColumn(
				 name: "Id",
				 table: "Nominations");

			migrationBuilder.AlterColumn<int>(
				 name: "Weight",
				 table: "Nominations",
				 type: "int",
				 nullable: true,
				 oldClrType: typeof(int));

			migrationBuilder.AddPrimaryKey(
				 name: "PK_Nominations",
				 table: "Nominations",
				 columns: new[] { "ElectionAssemblyId", "StructureId", "SeatTypeId", "CandidteId", "ElectionPhaseId" });
		}
	}
}
