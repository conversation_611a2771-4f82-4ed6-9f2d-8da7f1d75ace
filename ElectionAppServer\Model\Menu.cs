﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.AspNetCore.Identity;

namespace ElectionAppServer.Model;

public class Menu
{
    public int Id { get; set; }
    public string Name { get; set; }
    public string Link { get; set; }
    public string Module { get; set; }
    public bool IsActive { get; set; }
    public int SortOrder { get; set; }

    public virtual List<RoleMenu> RoleMenus { get; set; }
}

public class RoleMenu
{
    public int MenuId { get; set; }

    [ForeignKey("Role")] public string RoleId { get; set; }

    public virtual Menu Menu { get; set; }
    public virtual IdentityRole Role { get; set; }
    public virtual bool HasAccess { get; set; } = false;
}