﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace ElectionAppServer.Migrations.ApplicationDb
{
    public partial class addbilamokabilafield : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Elections_Elections_ParentElectionId",
                table: "Elections");

            migrationBuilder.AddColumn<bool>(
                name: "IsBilaMokabilaWinner",
                table: "Nominations",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddForeignKey(
                name: "FK_Elections_Elections_ParentElectionId",
                table: "Elections",
                column: "ParentElectionId",
                principalTable: "Elections",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Elections_Elections_ParentElectionId",
                table: "Elections");

            migrationBuilder.DropColumn(
                name: "IsBilaMokabilaWinner",
                table: "Nominations");

            migrationBuilder.AddForeignKey(
                name: "FK_Elections_Elections_ParentElectionId",
                table: "Elections",
                column: "ParentElectionId",
                principalTable: "Elections",
                principalColumn: "Id");
        }
    }
}
