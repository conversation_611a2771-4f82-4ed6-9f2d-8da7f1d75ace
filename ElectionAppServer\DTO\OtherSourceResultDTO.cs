﻿using System;

namespace ElectionAppServer.DTO;

public class OtherSourceResultDTO
{
    public string Source { get; set; }
    public string Assembly { get; set; }
    public string Constituency { get; set; }
    public string District { get; set; }
    public int? ResPS { get; set; }
    public DateTime LastUpdate { get; set; }
    public string Winner { get; set; }
    public string WinnerParty { get; set; }
    public int WinnerVotes { get; set; }
    public string RunnerUp { get; set; }
    public string RunnerUpParty { get; set; }
    public int? RunnerUpVotes { get; set; }
    public int SourceId { get; set; }
    public int AssemblyId { get; set; }
    public int StructureId { get; set; }
    public int DistrictId { get; set; }
}