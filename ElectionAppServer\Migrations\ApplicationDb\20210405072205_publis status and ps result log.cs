﻿using Microsoft.EntityFrameworkCore.Migrations;
using System;

namespace ElectionAppServer.Migrations.ApplicationDb
{
	public partial class publisstatusandpsresultlog : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.AddColumn<bool>(
				 name: "IsPublished",
				 table: "PSResults",
				 type: "bit",
				 nullable: false,
				 defaultValue: false);

			migrationBuilder.AddColumn<string>(
				 name: "PublishedBy",
				 table: "PSResults",
				 type: "nvarchar(max)",
				 nullable: true);

			migrationBuilder.AddColumn<DateTime>(
				 name: "PublishedDate",
				 table: "PSResults",
				 type: "datetime2",
				 nullable: true);

			migrationBuilder.CreateTable(
				 name: "PSResultLogs",
				 columns: table => new
				 {
					 Id = table.Column<long>(type: "bigint", nullable: false)
							.Annotation("SqlServer:Identity", "1, 1"),
					 NominationId = table.Column<int>(type: "int", nullable: false),
					 PollingStationId = table.Column<int>(type: "int", nullable: false),
					 Votes = table.Column<int>(type: "int", nullable: false),
					 IsPublished = table.Column<bool>(type: "bit", nullable: false),
					 PublishedBy = table.Column<string>(type: "nvarchar(max)", nullable: true),
					 PublishedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
					 LogDate = table.Column<DateTime>(type: "datetime2", nullable: false),
					 UserId = table.Column<string>(type: "nvarchar(max)", nullable: true)
				 },
				 constraints: table =>
				 {
					 table.PrimaryKey("PK_PSResultLogs", x => x.Id);
				 });
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropTable(
				 name: "PSResultLogs");

			migrationBuilder.DropColumn(
				 name: "IsPublished",
				 table: "PSResults");

			migrationBuilder.DropColumn(
				 name: "PublishedBy",
				 table: "PSResults");

			migrationBuilder.DropColumn(
				 name: "PublishedDate",
				 table: "PSResults");
		}
	}
}
