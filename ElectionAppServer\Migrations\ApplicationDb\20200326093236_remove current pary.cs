﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace ElectionAppServer.Migrations.ApplicationDb
{
	public partial class removecurrentpary : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropForeignKey(
				 name: "FK_Candidates_Parties_CurrentPartyId",
				 table: "Candidates");

			migrationBuilder.DropIndex(
				 name: "IX_Candidates_CurrentPartyId",
				 table: "Candidates");

			migrationBuilder.DropColumn(
				 name: "CurrentPartyId",
				 table: "Candidates");

			migrationBuilder.AddColumn<int>(
				 name: "PartyId",
				 table: "Candidates",
				 nullable: true);

			migrationBuilder.CreateIndex(
				 name: "IX_Candidates_PartyId",
				 table: "Candidates",
				 column: "PartyId");

			migrationBuilder.AddForeignKey(
				 name: "FK_Candidates_Parties_PartyId",
				 table: "Candidates",
				 column: "PartyId",
				 principalTable: "Parties",
				 principalColumn: "Id",
				 onDelete: ReferentialAction.Restrict);
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropForeignKey(
				 name: "FK_Candidates_Parties_PartyId",
				 table: "Candidates");

			migrationBuilder.DropIndex(
				 name: "IX_Candidates_PartyId",
				 table: "Candidates");

			migrationBuilder.DropColumn(
				 name: "PartyId",
				 table: "Candidates");

			migrationBuilder.AddColumn<int>(
				 name: "CurrentPartyId",
				 table: "Candidates",
				 type: "int",
				 nullable: true);

			migrationBuilder.CreateIndex(
				 name: "IX_Candidates_CurrentPartyId",
				 table: "Candidates",
				 column: "CurrentPartyId");

			migrationBuilder.AddForeignKey(
				 name: "FK_Candidates_Parties_CurrentPartyId",
				 table: "Candidates",
				 column: "CurrentPartyId",
				 principalTable: "Parties",
				 principalColumn: "Id",
				 onDelete: ReferentialAction.Restrict);
		}
	}
}
