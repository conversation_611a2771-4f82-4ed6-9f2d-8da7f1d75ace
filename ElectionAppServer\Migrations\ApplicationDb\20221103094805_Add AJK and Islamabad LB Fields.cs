﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace ElectionAppServer.Migrations.ApplicationDb
{
    public partial class AddAJKandIslamabadLBFields : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "IsL<PERSON>J<PERSON>",
                table: "Parties",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IsLBIslamabad",
                table: "Parties",
                type: "bit",
                nullable: false,
                defaultValue: false);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "IsLBAJK",
                table: "Parties");

            migrationBuilder.DropColumn(
                name: "IsLBIslamabad",
                table: "Parties");
        }
    }
}
