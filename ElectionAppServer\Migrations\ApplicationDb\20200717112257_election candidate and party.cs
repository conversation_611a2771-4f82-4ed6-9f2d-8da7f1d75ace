﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace ElectionAppServer.Migrations.ApplicationDb
{
	public partial class electioncandidateandparty : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.CreateTable(
				 name: "ElectionCandidates",
				 columns: table => new
				 {
					 ElectionId = table.Column<int>(nullable: false),
					 CandidateId = table.Column<int>(nullable: false)
				 },
				 constraints: table =>
				 {
					 table.PrimaryKey("PK_ElectionCandidates", x => new { x.CandidateId, x.ElectionId });
					 table.ForeignKey(
							  name: "FK_ElectionCandidates_Candidates_CandidateId",
							  column: x => x.CandidateId,
							  principalTable: "Candidates",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Restrict);
					 table.ForeignKey(
							  name: "FK_ElectionCandidates_Elections_ElectionId",
							  column: x => x.ElectionId,
							  principalTable: "Elections",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Restrict);
				 });

			migrationBuilder.CreateTable(
				 name: "ElectionParties",
				 columns: table => new
				 {
					 ElectionId = table.Column<int>(nullable: false),
					 PartyId = table.Column<int>(nullable: false)
				 },
				 constraints: table =>
				 {
					 table.PrimaryKey("PK_ElectionParties", x => new { x.PartyId, x.ElectionId });
					 table.ForeignKey(
							  name: "FK_ElectionParties_Elections_ElectionId",
							  column: x => x.ElectionId,
							  principalTable: "Elections",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Restrict);
					 table.ForeignKey(
							  name: "FK_ElectionParties_Parties_PartyId",
							  column: x => x.PartyId,
							  principalTable: "Parties",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Restrict);
				 });

			migrationBuilder.CreateIndex(
				 name: "IX_ElectionCandidates_ElectionId",
				 table: "ElectionCandidates",
				 column: "ElectionId");

			migrationBuilder.CreateIndex(
				 name: "IX_ElectionParties_ElectionId",
				 table: "ElectionParties",
				 column: "ElectionId");
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropTable(
				 name: "ElectionCandidates");

			migrationBuilder.DropTable(
				 name: "ElectionParties");
		}
	}
}
