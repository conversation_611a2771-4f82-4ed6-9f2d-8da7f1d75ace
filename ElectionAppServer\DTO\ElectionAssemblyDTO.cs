﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace ElectionAppServer.DTO;

public class ElectionAssemblyDTO
{
    public ElectionAssemblyDTO()
    {
        //Seats = new List<SeatTypeDTO>();
        ConsSeatTypeList = new List<ConSeatDTO>();
    }

    public int ElectionId { get; set; }
    public int Id { get; internal set; }
    public string AssemblyType { get; internal set; }

    [Required(ErrorMessage = "Required")] public int? AssemblyTypeId { get; set; }

    [Required(ErrorMessage = "Required")]
    [StringLength(200)]
    public string EnglishTitle { get; internal set; }

    [Required(ErrorMessage = "Required")]
    [StringLength(200)]
    public string UrduTitle { get; internal set; }

    //public List<SeatTypeDTO> Seats { get; set; }
    public List<ConSeatDTO> ConsSeatTypeList { get; set; }

    public string SeatsList { get; set; }

    //[Required]
    [Display(Name = "Reserve Seats")]
    [Range(0, 9999)]
    public int? ReserveSeats { get; set; }

    //[Required]
    [Display(Name = "Women Seats")]
    [Range(0, 9999)]
    public int? WomenSeats { get; set; }

    public string CreatedBy { get; set; }
    public string CreatedOn { get; set; }
    public string ModifiedBy { get; set; }
    public string ModifiedOn { get; set; }
    public string Constituencies { get; set; }

    [Required]
    [Display(Name = "Nomination Type")]
    public int? NominationTypeId { get; set; }

    public string NominationType { get; internal set; }
    //[Required]
    //[Display(Name = "Seat Configuration")]
    //  public int? SeatConfigurationId { get; set; }
    //  public string SeatConfiguration{ get; set; }
}

public class SeatTypeDTO
{
    public int SeatTypeId { get; set; }
    public string Title { get; set; }
    public bool IsSelected { get; set; }
}