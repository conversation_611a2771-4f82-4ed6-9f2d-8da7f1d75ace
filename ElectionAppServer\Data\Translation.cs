﻿using System;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using ElectionAppServer.DTO;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using RestSharp;

namespace ElectionAppServer.Data;

public class Translation
{
    public static async Task<string> PostData(ImmResultDTO obj)
    {
        // Temp Close for Testing
        //return Task.FromResult("OK");
        //var client = new RestClient("http://apnegs.geo.tv/election_api.php");
        //RestClient client = new("https://www.geo.tv/election_api.php")
        var options = new RestClientOptions("https://www.geo.tv/election/election_api");
        RestClient client = new(options);
        RestRequest request = new RestRequest("", Method.Post);
        request.AddHeader("Content-Type", "application/x-www-form-urlencoded");

        request.AddParameter("constituency_id", $"{obj.constituency_id}");
        request.AddParameter("constituency_name", $"{obj.constituency_name}");
        request.AddParameter("number_of_polling_station", $"{obj.number_of_polling_station}");

        var op = "";
        foreach (var res in obj.candidate_info)
            op += "{\"candidate_id\":" + res.candidate_id + ",\"votes\":" + res.votes + ",\"party_id\":" +
                  res.party_id +
                  ",\"nomination_id\":" + res.nomination_id + ",\"status\":\"PUBLISH\"},";

        if (op != "") op = "[" + op.Substring(0, op.Length - 1) + "]";

        request.AddParameter("candidate_info", op);

        try
        {
            var response = await client.ExecuteAsync(request);
            return "OK";
        }
        catch (Exception ex)
        {
            return ex.Message;
        }
    }

    public static async Task<string> PostDataPS(ImmResultDTO r)
    {
        // Temp Close for Testing
        return "OK";
        //var client = new RestClient("http://apnegs.geo.tv/election_ps_api.php");
        var options = new RestClientOptions("https://www.geo.tv/election_ps_api.php")
        {
            Proxy = new WebProxy
            {
                Address = new Uri("http://**********:8080"),
                BypassProxyOnLocal = false,
                UseDefaultCredentials = false,

                // *** These creds are given to the proxy server, not the web server ***
                Credentials = new NetworkCredential(
                    @"geo\services.soft",
                    "Softw@re456")
            },
            Timeout = TimeSpan.FromMilliseconds(-1)
        };
        var client = new RestClient(options);
        var request = new RestRequest("", Method.Post);
        request.AddHeader("Content-Type", "application/x-www-form-urlencoded");
        request.AddParameter("constituency_name", r.constituency_name);
        request.AddParameter("constituency_id", $"{r.constituency_id}");
        request.AddParameter("polling_station_name", r.polling_station_name);
        request.AddParameter("polling_station_id", $"{r.polling_station_id}");
        //request.AddParameter("candidate_info", "[{\"candidate_id\": 669,\"votes\": 50,\"party_id\": 52,\"nomination_id\": 876,\"status\": \"PUBLISH\"}, {\"candidate_id\": 670,\"votes\": 70,\"party_id\": 52,\"nomination_id\": 876,\"status\": \"PUBLISH\"}, {\"candidate_id\": 671,\"votes\": 90,\"party_id\": 29,\"nomination_id\": 877,\"status\": \"PUBLISH\"}, {\"candidate_id\": 672,\"votes\": 300,\"party_id\": 29,\"nomination_id\": 878,\"status\": \"PUBLISH\"}, {\"candidate_id\": 673,\"votes\": 35,\"party_id\": 29,\"nomination_id\": 879,\"status\": \"PUBLISH\"}, {\"candidate_id\": 674,\"votes\": 2,\"party_id\": 29,\"nomination_id\": 880,\"status\": \"PUBLISH\"}, {\"candidate_id\": 675,\"votes\": 10,\"party_id\": 29,\"nomination_id\": 881,\"status\": \"PUBLISH\"}, {\"candidate_id\": 676,\"votes\": 55,\"party_id\": 29,\"nomination_id\": 882,\"status\": \"PUBLISH\"}, {\"candidate_id\": 677,\"votes\": 33,\"party_id\": 29,\"nomination_id\": 883,\"status\": \"PUBLISH\"}]");
        var op = "";
        foreach (var res in r.candidate_info)
            op +=
                $"{{\"candidate_id\":{res.candidate_id},\"votes\":{res.votes},\"party_id\":{res.party_id},\"nomination_id\":{res.nomination_id},\"status\":\"PUBLISH\"}},";

        if (op != "") op = "[" + op.Substring(0, op.Length - 1) + "]";

        request.AddParameter("candidate_info", op);
        try
        {
            var response = await client.ExecuteAsync(request);
            return "OK";
        }
        catch (Exception ex)
        {
            return ex.Message;
        }
    }

    public static Task<string> TranslateText(string input)
    {
        if (Program.staticConfig.GetValue<string>("translate") == "false")
            return Task.FromResult("");

        if (string.IsNullOrEmpty(input))
            return Task.FromResult("");

        var url = string.Format(
            "https://translate.googleapis.com/translate_a/single?client=gtx&sl={0}&tl={1}&dt=t&q={2}",
            "en", "ur", Uri.EscapeUriString(input));
        WebProxy proxy = new()
        {
            Address = new Uri("http://**********:8080"),
            BypassProxyOnLocal = false,
            UseDefaultCredentials = false,

            // *** These creds are given to the proxy server, not the web server ***
            Credentials = new NetworkCredential(
                @"geo\services.soft",
                "Softw@re456")
        };

        HttpClientHandler httpClientHandler = new();
        if (Program.staticConfig.GetValue<string>("useproxy") == "true") httpClientHandler.Proxy = proxy;
        httpClientHandler.PreAuthenticate = true;
        httpClientHandler.UseDefaultCredentials = false;

        // *** These creds are given to the web server, not the proxy server ***
        httpClientHandler.Credentials = new NetworkCredential(
            @"geo\services.soft",
            "Softw@re456");

        HttpClient httpClient = new(httpClientHandler, true);

        var result = httpClient.GetStringAsync(url).Result;

        var obj = JsonConvert.DeserializeObject(result);

        var jsonArray = JArray.Parse(result);

        var translation = JArray.Parse(JArray.Parse(jsonArray[0].ToString())[0].ToString())[0].ToString();

        translation = translation.Replace("۔", "");
        //if (!string.IsNullOrEmpty(translation))
        //{
        //	translation = translation.Substring(0, translation.Length - 1);
        //}
        return Task.FromResult(translation);
    }

    public static Task<string> TranslateTextEnglish(string input)
    {
        if (Program.staticConfig.GetValue<string>("translate") == "false")
            return Task.FromResult("");

        if (string.IsNullOrEmpty(input))
            return Task.FromResult("");

        var url = string.Format(
            "https://translate.googleapis.com/translate_a/single?client=gtx&sl={0}&tl={1}&dt=t&q={2}",
            "ur", "en", Uri.UnescapeDataString(input));
        WebProxy proxy = new()
        {
            Address = new Uri("http://**********:8080"),
            BypassProxyOnLocal = false,
            UseDefaultCredentials = false,

            // *** These creds are given to the proxy server, not the web server ***
            Credentials = new NetworkCredential(
                @"geo\services.soft",
                "Softw@re456")
        };

        HttpClientHandler httpClientHandler = new();
        if (Program.staticConfig.GetValue<string>("useproxy") == "true") httpClientHandler.Proxy = proxy;
        httpClientHandler.PreAuthenticate = true;
        httpClientHandler.UseDefaultCredentials = false;

        // *** These creds are given to the web server, not the proxy server ***
        httpClientHandler.Credentials = new NetworkCredential(
            @"geo\services.soft",
            "Softw@re456");

        HttpClient httpClient = new(httpClientHandler, true);

        var result = httpClient.GetStringAsync(url).Result;

        var obj = JsonConvert.DeserializeObject(result);

        var jsonArray = JArray.Parse(result);

        var translation = JArray.Parse(JArray.Parse(jsonArray[0].ToString())[0].ToString())[0].ToString();

        translation = translation.Replace("۔", "");
        //if (!string.IsNullOrEmpty(translation))
        //{
        //	translation = translation.Substring(0, translation.Length - 1);
        //}
        return Task.FromResult(translation);
    }
    //public static Task<string> PostDataToImm(ImmResultDTO obj1)
    //{
    //	if (obj1 == null)
    //		return Task.FromResult("Not found");

    //	try
    //	{
    //		string url = String.Format("https://www.geo.tv/election_api.php");
    //		var proxy = new WebProxy
    //		{
    //			Address = new Uri($"http://**********:8080"),
    //			BypassProxyOnLocal = false,
    //			UseDefaultCredentials = false,

    //			// *** These creds are given to the proxy server, not the web server ***
    //			Credentials = new NetworkCredential(
    //				userName: @"geo\services.soft",
    //				password: "Softw@re456")
    //		};

    //		var httpClientHandler = new HttpClientHandler
    //		{
    //			Proxy = proxy,
    //		};

    //		httpClientHandler.PreAuthenticate = true;
    //		httpClientHandler.UseDefaultCredentials = false;

    //		// *** These creds are given to the web server, not the proxy server ***
    //		httpClientHandler.Credentials = new NetworkCredential(
    //			 userName: @"geo\services.soft",
    //			 password: "Softw@re456");

    //		HttpClient httpClient = new HttpClient(handler: httpClientHandler, disposeHandler: true);
    //		httpClient.DefaultRequestHeaders.Add("Content-Type", "application/x-www-form-urlencoded");
    //		httpClient.DefaultRequestHeaders.Add("Cookie", "__cfduid=d2d72cc44dd40f0edc42e224bd5c191371605264107");

    //		//string result = httpClient.GetStringAsync(url).Result;
    //		//HttpContent requestParams = new FormUrlEncodedContent(obj1);
    //		var k = System.Text.Encoding.UTF8.GetBytes(JsonConvert.SerializeObject(obj1));
    //		var cnt = new ByteArrayContent(k);
    //		var res = httpClient.PostAsync(url, cnt).Result;
    //		//var op = Newtonsoft.Json.JsonConvert.DeserializeObject(res);

    //		//JArray jsonArray = JArray.Parse(result);

    //		//string translation = JArray.Parse(JArray.Parse(jsonArray[0].ToString())[0].ToString())[0].ToString();

    //		return Task.FromResult("OK");
    //	}
    //	catch (Exception)
    //	{
    //		throw;
    //	}
    //}
}