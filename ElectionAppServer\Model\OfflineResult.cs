﻿using System;
using System.ComponentModel.DataAnnotations;

namespace ElectionAppServer.Model;

public class OfflineResult
{
    public int ResultSourceId { get; set; }
    public int NominationId { get; set; }
    public virtual ResultSource ResultSource { get; set; }
    public virtual Nomination Nomination { get; set; }
    public int? ResPS { get; set; } // Result Polling Stations
    public int Votes { get; set; }

    #region Audit Log Fields

    public DateTime CreatedDate { get; set; } = DateTime.Now;
    public DateTime? ModifiedDate { get; set; }
    [StringLength(450)] public string CreatedBy { get; set; }
    [StringLength(450)] public string ModifiedBy { get; set; }

    #endregion Audit Log Fields
}

public class OfflineResultLog
{
    public int Id { get; set; }
    public int NominationId { get; set; }
    public int SourceId { get; set; }
    public int? ResPS { get; set; }
    public int Votes { get; set; }
    public DateTime CreatedDate { get; set; }
    public string CreatedBy { get; set; }
}