@page "/setup/problems"
@using BeforeOpenEventArgs = Syncfusion.Blazor.Popups.BeforeOpenEventArgs
@using ButtonType = MudBlazor.ButtonType
@using FilterType = Syncfusion.Blazor.Grids.FilterType
@using Size = MudBlazor.Size
@inherits OwningComponentBase<ProblemService>

<MudText Typo="Typo.h5">Problems</MudText>

<SfDialog Width="500px" @bind-Visible="@isDlgVisible" @ref="dlgForm" IsModal="true" ShowCloseIcon="true">
    <DialogEvents OnOpen="beforeOpen"></DialogEvents>
    <DialogTemplates>
        <Header>@FormTitle</Header>
        <Content>
            <EditForm Model="@selectedObj" OnValidSubmit="@SaveProblemData">
                <DataAnnotationsValidator/>
                <div class="row">
                    <div class="input-field col s12">
                        <SfTextBox @bind-Value="selectedObj.Title" Placeholder="Title" FloatLabelType="FloatLabelType.Always"></SfTextBox>
                        <ValidationMessage For="@(() => selectedObj.Title)"/>
                    </div>
                </div>
                <div class="row">
                    <div class="col">
                        <MudButton ButtonType="ButtonType.Submit"
                                   Color="Color.Primary" Size="Size.Small"
                                   Variant="Variant.Filled">
                            @SaveButtonText
                        </MudButton>
                    </div>
                </div>
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>

<SfToast @ref="ToastObj" Title="Alert" ShowProgressBar="true" Timeout="3000">
    <ToastPosition X="Right" Y="Bottom"></ToastPosition>
</SfToast>

<AuthorizeView>
    <Authorized>
        <section>
            @if (objList == null)
            {
                <p><em>Loading...</em></p>
            }
            else
            {
                <SfButton IsPrimary="true" CssClass="e-primary mb-2" OnClick="OpenCreateForm">Create Problem</SfButton>
                <SfGrid DataSource="@objList" AllowFiltering="true" @ref="Grid" AllowSorting="true">
                    <GridFilterSettings Type="FilterType.Excel"></GridFilterSettings>
                    <GridColumns>
                        <GridColumn Field="Id" HeaderText="Id" AutoFit="true"></GridColumn>
                        <GridColumn Field="Title" HeaderText="Title"></GridColumn>
                        <GridColumn HeaderText="Actions" AllowFiltering="false">
                            <Template Context="ss">
                                @{
                                    var kk = ss as Problem;
                                    <SfButton IsPrimary="true" CssClass="e-danger" OnClick="@(() => DeleteRecord(kk.Id))">
                                        <i class="fas fa-trash-alt"></i>
                                    </SfButton>
                                    <SfButton IsPrimary="true" CssClass="e-primary" OnClick="@(() => OpenEditForm(kk))">
                                        <i class="fas fa-pencil-alt"></i>
                                    </SfButton>
                                }
                            </Template>
                        </GridColumn>
                    </GridColumns>
                </SfGrid>
            }
        </section>
    </Authorized>
    <NotAuthorized>
        <p>You're not signed in.</p>
    </NotAuthorized>
</AuthorizeView>

@code {
    [CascadingParameter] private Task<AuthenticationState> authenticationStateTask { get; set; }

    private SfDialog dlgForm;
    List<Problem> objList;
    private bool isDlgVisible;
    private string SaveButtonText = "Save";
    private readonly string FormTitle = "Create Problem";
    private Problem selectedObj = new();
    private SfToast ToastObj;
    public string ToastContent = "Unable to delete Record, Related Record(s) Exist";
    public SfGrid<Problem> Grid { get; set; }

    protected override async Task OnInitializedAsync()
    {
        var user = (await authenticationStateTask).User;
        objList = await Service.GetList();
    }

    private void OpenCreateForm()
    {
        ClearData();
        dlgForm.ShowAsync();
        SaveButtonText = "Create";
    }

    private void OpenEditForm(Problem st)
    {
        selectedObj.Id = st.Id;
        selectedObj.Title = st.Title;
        dlgForm.ShowAsync();
        SaveButtonText = "Update";
    }

    private void ClearData()
    {
        selectedObj = new Problem();
        selectedObj.Id = 0;
        selectedObj.Title = "";
    }

    private async Task SaveProblemData()
    {
        var user = (await authenticationStateTask).User;
        
        if (selectedObj.Id == 0)
        {
            var st = new Problem();
            st.Title = selectedObj.Title.Trim();
            st.CreatedDate = DateTime.Now;
            st.ModifiedDate = DateTime.Now;
            st.CreatedBy = user.Identity.Name;
            st.ModifiedBy = user.Identity.Name;
            
            var res = Service.CreateProblem(st);
            objList = await Service.GetList();
            await dlgForm.HideAsync();
        }
        else
        {
            var st = new Problem();
            st.Title = selectedObj.Title.Trim();
            st.CreatedDate = DateTime.Now;
            st.ModifiedDate = DateTime.Now;
            st.CreatedBy = user.Identity.Name;
            st.ModifiedBy = user.Identity.Name;
            st.Id = selectedObj.Id;
            
            var res = Service.UpdateProblem(st);
            objList = await Service.GetList();
            await dlgForm.HideAsync();
        }

        await Grid.Refresh();
    }

    private async Task DeleteRecord(int Id)
    {
        var res = await JSRuntime.InvokeAsync<bool>("confirm", "Are you want to delete this record?");
        if (res == false)
            return;

        try
        {
            var msg2 = await Service.DeleteProblem(Id);
            objList = await Service.GetList();
            ClearData();
            await Grid.Refresh();
        }
        catch (Exception ee)
        {
            var mm = new ToastModel { Title = "Error", Content = ee.Message };
            if (ee.InnerException != null)
                mm.Content += " --- Detail: " + ee.InnerException.Message;
            await ToastObj.ShowAsync(mm);
        }
    }

    private void beforeOpen(BeforeOpenEventArgs args)
    {
        args.MaxHeight = null;
    }


    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        //return base.OnAfterRenderAsync(firstRender);
        if (firstRender)
        {
            var user = (await authenticationStateTask).User;
            if (user.Identity is { IsAuthenticated: true })
            {
                //var dd =  await ProtectedSessionStore.GetAsync<ElectionStateDTO>("election");
                var dd = await localStorage.GetAsync<ElectionStateDTO>(await confreader.GetSettingValue("electionkey"));
                state.SetState(dd.Value);
            }
            else
            {
                try
                {
                    //await  ProtectedSessionStore.DeleteAsync("election");
                    await localStorage.DeleteAsync(await confreader.GetSettingValue("electionkey"));
                    state.SetState(new ElectionStateDTO { ElectionId = 0, ElectionTitle = "", PhaseId = 0, PhaseTitle = "" });
                }
                catch (Exception)
                {
                    // ignored
                }

                NavigationManager.NavigateTo("");
            }

            StateHasChanged();
        }
    }

}