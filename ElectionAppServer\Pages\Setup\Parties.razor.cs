﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using BlazorInputFile;
using ElectionAppServer.Data;
using ElectionAppServer.DTO;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Syncfusion.Blazor.Grids;
using Syncfusion.Blazor.Notifications;
using Syncfusion.Blazor.Popups;

namespace ElectionAppServer.Pages.Setup;

public partial class Parties
{
    private readonly string FormTitle = "Create Party";

    private readonly bool ImportMode = false;

    //HubConnection connection;
    private SfDialog dlgForm;

    private SfDialog dlgSymbolForm;
    private IFileListEntry file;

    //--> No in use //private ElementReference inputElement;
    private bool isDlgVisible;

    private bool isSymbolDialogOpne;
    public long max;
    private List<PartyDTO> objList;
    private string SaveButtonText = "Save";
    private PartyDTO selectedObj = new();

    //--> No in use //private string status;
    private List<SymbolDTO> symbols;

    private List<GeneralItemDTO> teamsList = new();
    public string ToastContent = "Unable to delete Record, Related Record(s) Exist";
    private SfToast ToastObj;
    public long value;
    public string baseurl { get; set; }
    public List<GeneralItemDTO> coalition_list { get; set; }
    public string FileBase64String { get; set; }
    public string FileBase64StringCurrentParyLeader { get; set; }
    public string FileBase64StringPartyLeader { get; set; }
    public string FileBase64StringSymbol { get; set; }
    public byte[] FileByteArray { get; set; }
    public byte[] FileByteArrayCurrentPartyLeader { get; set; }
    public byte[] FileByteArrayPartyLeader { get; set; }
    public byte[] FileByteArraySymbol { get; set; }

    public SfGrid<PartyDTO> Grid { get; set; }

    //public bool IsDisable { get { if (selectedObj.Id != 0) return false; else if (FileBase64String == "") return true; else return false; } }
    public bool IsDisable { get; set; } = false;

    public string mediapath { get; set; }
    public SymbolDTO selectedSymbol { get; set; }

    public SymbolDTO symbolObj { get; set; } = new() { Urdu = "", English = "", Id = 0 };

    // AuthenticationState is available as a CascadingParameter
    [CascadingParameter] private Task<AuthenticationState> authenticationStateTask { get; set; }

    public void CustomizeCell(QueryCellInfoEventArgs<PartyDTO> args)
    {
        if (args.Column.Field == "UrduTitle")
        {
            args.Cell.AddClass(new[] { "urdu-column" });
            args.Cell.AddStyle(new[] { "font-size:18px;line-height: 1.4;text-align: right" });
        }
    }

    public async Task FillSeatTypes()
    {
        //if (selectedObj.SymbolId != null)
        //{
        //   seatTypes_list = await Service.GetSeatType((int)assemblyId);
        //   if (seatTypes_list.Count > 0)
        //   {
        //      seatTypeId = seatTypes_list[0].Id;
        //   }
        //   structures_list = await Service.GetConstituencies((int)assemblyId);
        //}
        //else
        //{
        //   seatTypes_list = new List<GeneralItemDTO>();
        //}
        //await FillNominations();
        await Task.CompletedTask;
    }

    public string ToTitleCase(string txt)
    {
        if (txt.Length == 0) return "";
        if (txt.Length == 1)
        {
            var txt1 = txt.ToUpper();
            return txt1;
        }
        else
        {
            var txt1 = txt.Substring(0, 1).ToUpper();
            var txt2 = txt.Substring(1, txt.Length - 1).ToLower();
            return txt1 + txt2;
        }
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        //return base.OnAfterRenderAsync(firstRender);
        if (firstRender)
        {
            var user = (await authenticationStateTask).User;
            if (user.Identity.IsAuthenticated)
            {
                //var dd =  await ProtectedSessionStore.GetAsync<ElectionStateDTO>("election");
                var dd = await localStorage.GetAsync<ElectionStateDTO>(confreader.GetSettingValue("electionkey")
                    .Result);
                state.SetState(dd.Value);
                objList = await Service.GetAllParties(state.state.ElectionId);
                teamsList = await Service.GetTeamList();
            }
            else
            {
                try
                {
                    //await  ProtectedSessionStore.DeleteAsync("election");
                    await localStorage.DeleteAsync(await confreader.GetSettingValue("electionkey"));
                    state.SetState(new ElectionStateDTO
                        { ElectionId = 0, ElectionTitle = "", PhaseId = 0, PhaseTitle = "" });
                }
                catch (Exception)
                {
                    // ignored
                }

                NavigationManager.NavigateTo("");
            }

            StateHasChanged();
        }
    }

    protected override async Task OnInitializedAsync()
    {
        var user = (await authenticationStateTask).User;
        symbols = await Service.GetSymbolList();
        mediapath = env.WebRootPath + "\\media\\"; //await confreader.GetSettingValue("mediapath");
        baseurl = await confreader.GetSettingValue("BaseURL");
        //connection = new HubConnectionBuilder().WithUrl("http://localhost:59071/partyhub").Build();
        //connection = new HubConnectionBuilder().WithUrl("http://********:6565/partyhub").Build();
        //connection = new HubConnectionBuilder().WithUrl(BaseURL + "/partyhub").Build();
        //connection.On<PartyDTO>("ReceiveInfo", On_ReceiveMessage);
        //connection.On<PartyDTO>("ReceiveDelInfo", On_ReceiveDelMessage);
        //await connection.StartAsync();
    }

    protected async Task OpenCreateSymbolForm()
    {
        symbolObj = new SymbolDTO();
        await dlgSymbolForm.ShowAsync();
        await Task.CompletedTask;
    }

    protected async Task SaveSymbolData()
    {
        var user = (await authenticationStateTask).User;
        if (FileBase64StringSymbol == "") return;
        try
        {
            var res = await Service.CreateSymbol(symbolObj, user.Identity.Name);
            symbols.Add(res);
            symbols = symbols.OrderBy(c => c.English).ToList();
            //objList = await Service.GetList();
            await dlgSymbolForm.HideAsync();
            if (FileBase64StringSymbol != "" && FileByteArraySymbol != null && FileByteArraySymbol.Length > 0)
                File.WriteAllBytes(mediapath + @"\symbols\" + res.Id + ".jpg", FileByteArraySymbol);
            //await connection.InvokeAsync("SendInfo", selectedObj);
            //ClearData();
            selectedObj.SymbolId = res.Id;
            StateHasChanged();
        }
        catch (Exception ex)
        {
            var mm = new ToastModel
            {
                Title = "Error", Content = ex.Message, CssClass = "e-toast-danger", ShowProgressBar = true,
                Timeout = 5000,
                ShowCloseButton = true
            };
            await ToastObj.ShowAsync(mm);
        }
    }

    protected async Task TranslateToUrduSymbol()
    {
        try
        {
            if (symbolObj != null)
            {
                symbolObj.English = symbolObj.English.Trim();
                if (string.IsNullOrEmpty(symbolObj.Urdu))
                    symbolObj.Urdu = await Translation.TranslateText(symbolObj.English);
            }
        }
        catch (Exception)
        {
            // ignored
        }
    }

    //private async Task AddPartyToElection()
    //{
    //	List<int> partyIds = new List<int>();
    //	var user = (await authenticationStateTask).User;
    //	foreach (var cc in objList)
    //	{
    //		if (cc.IsSelected)
    //		{
    //			partyIds.Add(cc.Id);
    //		}
    //	}
    //	try
    //	{
    //		string res = await Service.AddRemoveParties(state.state.ElectionId, partyIds, user.Identity.Name);
    //		ImportMode = false;
    //		objList = await Service.GetAllParties(state.state.ElectionId);
    //		await Task.CompletedTask;
    //	}
    //	catch (Exception ex)
    //	{
    //		var mm = new ToastModel { Title = "Error", Content = ex.Message, TimeOut = 10000 };
    //		await ToastObj.ShowAsync(mm);
    //	}
    //}
    //private async Task BackToSelected()
    //{
    //	ImportMode = false;
    //	objList = await Service.GetAllParties(state.state.ElectionId);
    //}
    private void beforeOpen(BeforeOpenEventArgs args)
    {
        args.MaxHeight = null;
    }

    private void ClearData()
    {
        //selectedObj.Id = 0;
        //selectedObj.UrduTitle = "";
        //selectedObj.EnglishTitle = "";
        //selectedObj.ShortUrduTitle = "";
        //selectedObj.ShortEnglishTitle = "";
        //selectedObj.SymbolId = 0;
        selectedObj = new PartyDTO { UrduTitle = "", EnglishTitle = "", TeamId = 0 };
        FileBase64String = "";
        SaveButtonText = "Create";
        FileBase64String = "";
        FileByteArray = new byte[0];
        FileBase64StringCurrentParyLeader = "";
        FileBase64StringPartyLeader = "";
        FileByteArrayCurrentPartyLeader = new byte[0];
        FileByteArrayPartyLeader = new byte[0];
    }

    private async Task DeleteRecord(int Id)
    {
        var para = new[] { "Are you want to delete this record?" };
        var res = await JSRuntime.InvokeAsync<bool>("confirm", para);
        if (res == false)
            return;
        //string msg = "";
        var hasError = false;
        try
        {
            var msg2 = await Service.DeleteParty(Id, state.state.ElectionId);
            //parties_list = await Service.GetList();
            objList = await Service.GetAllParties(state.state.ElectionId);
            var cc = new PartyDTO { Id = Id };
            //await connection.InvokeAsync<PartyDTO>("DeleteRec", cc);
            //await Grid.Refresh();
            ClearData();
        }
        catch (Exception)
        {
            //msg = ee.Message;
            //if (ee.InnerException != null)
            //   msg += "<br />" + ee.InnerException.Message;
            hasError = true;
            //ToastObj.ShowAsync();
            //StateHasChanged();
        }

        if (hasError)
            //ToastContent = msg;
            await ToastObj.ShowAsync();
    }

    private async Task HandleFileSelected(IFileListEntry[] files)
    {
        file = files.FirstOrDefault();
        var dd = await file.ReadAllAsync();
        FileByteArray = dd.ToArray();
        FileBase64String = Convert.ToBase64String(FileByteArray);
    }

    private async Task HandleFileSelectedCurrentPartyLeader(IFileListEntry[] files)
    {
        var file = files.FirstOrDefault();
        var dd = await file.ReadAllAsync();
        FileByteArrayCurrentPartyLeader = dd.ToArray();
        FileBase64StringCurrentParyLeader = Convert.ToBase64String(FileByteArrayCurrentPartyLeader);
    }

    private async Task HandleFileSelectedPartyLeader(IFileListEntry[] files)
    {
        var file = files.FirstOrDefault();
        var dd = await file.ReadAllAsync();
        FileByteArrayPartyLeader = dd.ToArray();
        FileBase64StringPartyLeader = Convert.ToBase64String(FileByteArrayPartyLeader);
    }

    private async Task HandleFileSelectedSymbol(IFileListEntry[] files)
    {
        var file = files.FirstOrDefault();
        var dd = await file.ReadAllAsync();
        FileByteArraySymbol = dd.ToArray();
        FileBase64StringSymbol = Convert.ToBase64String(FileByteArraySymbol);
    }

    private async Task On_ReceiveDelMessage(PartyDTO cc)
    {
        var k = (from o in objList
            where o.Id == cc.Id
            select o).FirstOrDefault();
        if (k != null) objList.Remove(k);
        //await Grid.Refresh();
        StateHasChanged();
        await Task.CompletedTask;
    }

    private async Task On_ReceiveMessage(PartyDTO party)
    {
        //Messages.Add(new Msg { Text = message, User = user, SendDate = DateTime.Now });
        var k = (from o in objList
            where o.Id == party.Id
            select o).FirstOrDefault();
        if (k != null)
        {
            k.UrduTitle = party.UrduTitle;
            k.EnglishTitle = party.EnglishTitle;
            k.Address = party.Address;
            k.Designation = party.Designation;
            k.FlagURL = party.FlagURL;
            k.Leader = party.Leader;
            k.ShortEnglishTitle = party.ShortEnglishTitle;
            k.ShortUrduTitle = party.ShortUrduTitle;
            k.SymbolId = party.SymbolId;
            k.SymbolURL = party.SymbolURL;
            k.Trivia = party.Trivia;
            k.UrduTitle = party.UrduTitle;
        }
        else
        {
            objList.Add(new PartyDTO
            {
                Id = party.Id,
                UrduTitle = party.UrduTitle,
                EnglishTitle = party.EnglishTitle,
                Address = party.Address,
                Designation = party.Designation,
                FlagURL = party.FlagURL,
                Leader = party.Leader,
                ShortEnglishTitle = party.ShortEnglishTitle,
                ShortUrduTitle = party.ShortUrduTitle,
                Symbol = party.Symbol,
                SymbolId = party.SymbolId,
                SymbolURL = party.SymbolURL,
                Trivia = party.Trivia
            });
            //parties_list = parties_list.OrderBy(c => c.EnglishTitle).ToList();
        }

        //parties_list = parties_list.OrderBy(c => c.EnglishTitle).ToList();

        //await Grid.Refresh();
        StateHasChanged();
        //return Task.CompletedTask;
    }

    private void OpenCreateForm()
    {
        ClearData();
        dlgForm.ShowAsync();
    }

    private async Task OpenEditForm(PartyDTO st)
    {
        selectedObj = await Service.GetPartyDetail(st.Id, state.state.ElectionId);
        //selectedObj = new PartyDTO
        //{
        //	Id = st.Id,
        //	Address = st.Address,
        //	Designation = st.Designation,
        //	EnglishTitle = st.EnglishTitle,
        //	FlagURL = st.FlagURL,
        //	Leader = st.Leader,
        //	ShortEnglishTitle = st.ShortEnglishTitle,
        //	ShortUrduTitle = st.ShortUrduTitle,
        //	SymbolId = st.SymbolId,
        //	SymbolURL = st.SymbolURL,
        //	Trivia = st.Trivia,
        //	UrduTitle = st.UrduTitle,
        //	CurrentLeader = st.CurrentLeader,
        //	CurrentLeaderDesignation = st.CurrentLeaderDesignation,
        //	CurrentLeaderPicId = st.CurrentLeaderPicId,
        //	LeaderPicId = st.LeaderPicId,
        //	TeamId = st.TeamId,
        //	DateOfCreation = st.DateOfCreation
        //	//CoalitionId = st.CoalitionId
        //};
        //selectedObj = new PartyDTO();
        //selectedObj.Id = st.Id;
        //selectedObj.UrduTitle = st.UrduTitle;
        //selectedObj.EnglishTitle = st.EnglishTitle;
        //selectedObj.ShortEnglishTitle = st.ShortEnglishTitle;
        //selectedObj.ShortUrduTitle = st.ShortUrduTitle;
        //selectedObj.SymbolId = st.SymbolId;
        //selectedObj.Trivia = st.Trivia;
        //selectedObj.Leader = st.Leader;
        //selectedObj.Designation = st.Designation;
        //selectedObj.Address = st.Address;
        /*
         * Read File and convert to base 64 string
         */
        try
        {
            var f = mediapath + @"\flags\" + selectedObj.Id + ".jpg";
            var fs = File.OpenRead(f);
            var fileBytes = new byte[fs.Length];
            fs.Read(fileBytes, 0, fileBytes.Length);
            fs.Close();
            FileBase64String = Convert.ToBase64String(fileBytes);
        }
        catch (Exception)
        {
            FileBase64String = "";
            //throw;
        }

        try
        {
            if (!string.IsNullOrEmpty(selectedObj.CurrentLeaderPicId))
            {
                var f = mediapath + @"\Leaders\" + selectedObj.CurrentLeaderPicId;
                var fs = File.OpenRead(f);
                var fileBytes = new byte[fs.Length];
                fs.Read(fileBytes, 0, fileBytes.Length);
                fs.Close();
                FileBase64StringCurrentParyLeader = Convert.ToBase64String(fileBytes);
            }
            else
            {
                FileBase64StringCurrentParyLeader = "";
            }
        }
        catch (Exception)
        {
            FileBase64StringCurrentParyLeader = "";
            //throw;
        }

        try
        {
            if (!string.IsNullOrEmpty(selectedObj.LeaderPicId))
            {
                var f = mediapath + @"\Leaders\" + selectedObj.LeaderPicId;
                var fs = File.OpenRead(f);
                var fileBytes = new byte[fs.Length];
                fs.Read(fileBytes, 0, fileBytes.Length);
                fs.Close();
                FileBase64StringPartyLeader = Convert.ToBase64String(fileBytes);
            }
            else
            {
                FileBase64StringPartyLeader = "";
            }
        }
        catch (Exception)
        {
            FileBase64StringPartyLeader = "";
            //throw;
        }

        await dlgForm.ShowAsync();
        SaveButtonText = "Update";
        //FileBase64String = "";
        //FileByteArray = new byte[0];
    }

    //private async Task OpenImportForm()
    //{
    //	ImportMode = true;
    //	objList = await Service.GetAllParties(state.state.ElectionId, ImportMode);
    //	await Task.CompletedTask;
    //}
    private async Task SavePartyData()
    {
        // Close the Popup
        // ShowPopup = false;
        // Get the current user
        var user = (await authenticationStateTask).User;
        // A new forecast will have the Id set to 0
        var imgPartyLeaderId = "";
        var imgCurrentPartyLeaderId = "";
        if (FileByteArrayPartyLeader is { Length: > 1 })
            imgPartyLeaderId = Guid.NewGuid() + ".jpg";
        if (FileBase64StringCurrentParyLeader is { Length: > 1 })
            imgCurrentPartyLeaderId = Guid.NewGuid() + ".jpg";
        try
        {
            if (selectedObj.Id == 0)
            {
                //if (FileBase64String == "")
                //{
                //   return;
                //}
                // Create forecast
                var st = new PartyDTO
                {
                    UrduTitle = selectedObj.UrduTitle.Trim(),
                    EnglishTitle = selectedObj.EnglishTitle.Trim(),
                    ShortUrduTitle = selectedObj.ShortUrduTitle.Trim(),
                    ShortEnglishTitle = selectedObj.ShortEnglishTitle.Trim(),
                    SymbolId = selectedObj.SymbolId,
                    Address = selectedObj.Address,
                    Leader = selectedObj.Leader,
                    Designation = selectedObj.Designation,
                    CurrentLeader = selectedObj.CurrentLeader,
                    CurrentLeaderDesignation = selectedObj.CurrentLeaderDesignation,
                    Trivia = selectedObj.Trivia,
                    CurrentLeaderPicId = imgCurrentPartyLeaderId,
                    LeaderPicId = imgPartyLeaderId,
                    DateOfCreation = selectedObj.DateOfCreation,
                    TeamId = selectedObj.TeamId,
                    IsGB = selectedObj.IsGB,
                    IsGeneral = selectedObj.IsGeneral,
                    IsLB = selectedObj.IsLB,
                    IsAJK = selectedObj.IsAJK,
                    IsLBBalochistan = selectedObj.IsLBBalochistan,
                    IsLBPunjab = selectedObj.IsLBPunjab,
                    IsLBSindh = selectedObj.IsLBSindh,
                    IsLBAJK = selectedObj.IsLBAJK,
                    IsLBIslamabad = selectedObj.IsLBIslamabad
                };
                var res = Service.CreateParty(st, user.Identity.Name, state.state.ElectionId);
                //parties_list = await Service.GetList();
                await dlgForm.HideAsync();
                try
                {
                    if (FileByteArray.Length > 0 && FileByteArray.Length > 1)
                        File.WriteAllBytes(mediapath + @"\flags\" + res.Result.Id + ".jpg", FileByteArray);
                }
                catch (Exception)
                {
                    // ignored
                }

                try
                {
                    if (FileByteArrayPartyLeader.Length > 0 && FileByteArrayPartyLeader.Length > 1)
                        File.WriteAllBytes($"{mediapath}\\Leaders\\{imgPartyLeaderId}", FileByteArrayPartyLeader);
                }
                catch (Exception)
                {
                    // ignored
                }

                try
                {
                    if (FileByteArrayCurrentPartyLeader.Length > 0 && FileByteArrayCurrentPartyLeader.Length > 1)
                        File.WriteAllBytes($"{mediapath}\\Leaders\\{imgCurrentPartyLeaderId}",
                            FileByteArrayCurrentPartyLeader);
                }
                catch (Exception)
                {
                    // ignored
                }

                selectedObj.Id = res.Id;
                //parties_list = await Service.GetList();
                objList = await Service.GetAllParties(state.state.ElectionId);
                //await connection.InvokeAsync("SendInfo", res.Result);
                ClearData();
                //await Grid.Refresh();
                StateHasChanged();
            }
            else
            {
                var st = new PartyDTO
                {
                    UrduTitle = selectedObj.UrduTitle.Trim(),
                    EnglishTitle = selectedObj.EnglishTitle.Trim(),
                    ShortUrduTitle = selectedObj.ShortUrduTitle.Trim(),
                    ShortEnglishTitle = selectedObj.ShortEnglishTitle.Trim(),
                    SymbolId = selectedObj.SymbolId,
                    Id = selectedObj.Id,
                    Address = selectedObj.Address,
                    Leader = selectedObj.Leader,
                    Designation = selectedObj.Designation,
                    Trivia = selectedObj.Trivia,
                    CurrentLeader = selectedObj.CurrentLeader,
                    CurrentLeaderDesignation = selectedObj.CurrentLeaderDesignation,
                    LeaderPicId = imgPartyLeaderId,
                    CurrentLeaderPicId = imgCurrentPartyLeaderId,
                    DateOfCreation = selectedObj.DateOfCreation,
                    TeamId = selectedObj.TeamId,
                    IsAJK = selectedObj.IsAJK,
                    IsGeneral = selectedObj.IsGeneral,
                    IsLB = selectedObj.IsLB,
                    IsGB = selectedObj.IsGB,
                    IsLBBalochistan = selectedObj.IsLBBalochistan,
                    IsLBPunjab = selectedObj.IsLBPunjab,
                    IsLBSindh = selectedObj.IsLBSindh,
                    IsLBAJK = selectedObj.IsLBAJK,
                    IsLBIslamabad = selectedObj.IsLBIslamabad
                };
                //Service.CreateParty(st);
                var res = Service.UpdateParty(st, user.Identity.Name, state.state.ElectionId);
                //parties_list = await Service.GetList();
                objList = await Service.GetAllParties(state.state.ElectionId);
                if (FileByteArray != null && FileByteArray.Length > 0)
                {
                    File.WriteAllBytes(mediapath + @"\flags\" + st.Id + ".jpg", FileByteArray);
                    StateHasChanged();
                }

                try
                {
                    if (FileByteArray.Length > 0 && FileByteArray.Length > 1)
                        File.WriteAllBytes(mediapath + @"\flags\" + res.Result.Id + ".jpg", FileByteArray);
                }
                catch (Exception)
                {
                    // ignored
                }

                try
                {
                    if (FileByteArrayPartyLeader != null && FileByteArrayPartyLeader.Length > 0 &&
                        FileByteArrayPartyLeader.Length > 1)
                        File.WriteAllBytes($"{mediapath}\\Leaders\\{imgPartyLeaderId}", FileByteArrayPartyLeader);
                }
                catch (Exception)
                {
                    // ignored
                }

                try
                {
                    if (FileByteArrayCurrentPartyLeader != null && FileByteArrayCurrentPartyLeader.Length > 0 &&
                        FileByteArrayCurrentPartyLeader.Length > 1)
                        File.WriteAllBytes($"{mediapath}\\Leaders\\{imgCurrentPartyLeaderId}",
                            FileByteArrayCurrentPartyLeader);
                }
                catch (Exception)
                {
                    // ignored
                }

                await dlgForm.HideAsync();
                //parties_list = await Service.GetList();
                objList = await Service.GetAllParties(state.state.ElectionId);
                //await connection.InvokeAsync("SendInfo", res.Result);
                //await Grid.Refresh();
                ClearData();
                StateHasChanged();
            }
        }
        catch (Exception ex)
        {
            await ToastObj.ShowAsync(new ToastModel
            {
                Title = "Error",
                Content = ex.Message + (ex.InnerException != null ? "Detail: " + ex.InnerException.Message : "")
            });
        }
    }

    private async Task TranslateShort()
    {
        try
        {
            if (string.IsNullOrEmpty(selectedObj.ShortUrduTitle))
                selectedObj.ShortUrduTitle = await Translation.TranslateText(selectedObj.ShortEnglishTitle);
        }
        catch (Exception)
        {
            // ignored
        }
    }

    private async Task TranslateToUrdu()
    {
        var words = selectedObj.EnglishTitle.Trim().Split(' ');
        var abv = "";
        selectedObj.EnglishTitle = "";
        foreach (var word in words)
            if (word.Length > 0)
            {
                var kword = ToTitleCase(word);
                if (kword[0] >= 'A' && kword[0] <= 'Z') abv += kword[0];
                selectedObj.EnglishTitle += kword + " ";
            }

        selectedObj.EnglishTitle = selectedObj.EnglishTitle.Trim();
        if (abv != "" && string.IsNullOrEmpty(selectedObj.ShortEnglishTitle))
            selectedObj.ShortEnglishTitle = abv;
        try
        {
            if (string.IsNullOrEmpty(selectedObj.UrduTitle))
                selectedObj.UrduTitle = await Translation.TranslateText(selectedObj.EnglishTitle);
        }
        catch (Exception)
        {
            // ignored
        }

        try
        {
            if (string.IsNullOrEmpty(selectedObj.ShortUrduTitle))
                selectedObj.ShortUrduTitle = await Translation.TranslateText(selectedObj.ShortEnglishTitle);
        }
        catch (Exception)
        {
            // ignored
        }
    }
}