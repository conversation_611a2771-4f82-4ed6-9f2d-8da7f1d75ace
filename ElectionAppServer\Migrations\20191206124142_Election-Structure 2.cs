﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace ElectionAppServer.Migrations
{
	public partial class ElectionStructure2 : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropColumn(
				 name: "ProvincialAssemblyHalka_Code",
				 table: "Structures");

			migrationBuilder.AddColumn<int>(
				 name: "CasteId",
				 table: "Structures",
				 nullable: true);

			migrationBuilder.AddColumn<int>(
				 name: "FemaleVoters",
				 table: "Structures",
				 nullable: true);

			migrationBuilder.AddColumn<string>(
				 name: "ImportantAreas",
				 table: "Structures",
				 maxLength: 500,
				 nullable: true);

			migrationBuilder.AddColumn<string>(
				 name: "ImportantPoliticalPersonalities",
				 table: "Structures",
				 maxLength: 500,
				 nullable: true);

			migrationBuilder.AddColumn<bool>(
				 name: "IsSeat",
				 table: "Structures",
				 nullable: false,
				 defaultValue: false);

			migrationBuilder.AddColumn<int>(
				 name: "LanguageId",
				 table: "Structures",
				 nullable: true);

			migrationBuilder.AddColumn<int>(
				 name: "MaleVoters",
				 table: "Structures",
				 nullable: true);

			migrationBuilder.AddColumn<string>(
				 name: "Problems",
				 table: "Structures",
				 maxLength: 500,
				 nullable: true);

			migrationBuilder.AddColumn<float>(
				 name: "RuralAreaPer",
				 table: "Structures",
				 nullable: true);

			migrationBuilder.AddColumn<int>(
				 name: "TotalPollingStations",
				 table: "Structures",
				 nullable: true);

			migrationBuilder.AddColumn<string>(
				 name: "Trivia",
				 table: "Structures",
				 maxLength: 2000,
				 nullable: true);

			migrationBuilder.AddColumn<float>(
				 name: "UrbanAreaPer",
				 table: "Structures",
				 nullable: true);

			migrationBuilder.CreateIndex(
				 name: "IX_Structures_CasteId",
				 table: "Structures",
				 column: "CasteId");

			migrationBuilder.CreateIndex(
				 name: "IX_Structures_LanguageId",
				 table: "Structures",
				 column: "LanguageId");

			migrationBuilder.AddForeignKey(
				 name: "FK_Structures_Castes_CasteId",
				 table: "Structures",
				 column: "CasteId",
				 principalTable: "Castes",
				 principalColumn: "Id",
				 onDelete: ReferentialAction.Restrict);

			migrationBuilder.AddForeignKey(
				 name: "FK_Structures_Languages_LanguageId",
				 table: "Structures",
				 column: "LanguageId",
				 principalTable: "Languages",
				 principalColumn: "Id",
				 onDelete: ReferentialAction.Restrict);
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropForeignKey(
				 name: "FK_Structures_Castes_CasteId",
				 table: "Structures");

			migrationBuilder.DropForeignKey(
				 name: "FK_Structures_Languages_LanguageId",
				 table: "Structures");

			migrationBuilder.DropIndex(
				 name: "IX_Structures_CasteId",
				 table: "Structures");

			migrationBuilder.DropIndex(
				 name: "IX_Structures_LanguageId",
				 table: "Structures");

			migrationBuilder.DropColumn(
				 name: "CasteId",
				 table: "Structures");

			migrationBuilder.DropColumn(
				 name: "FemaleVoters",
				 table: "Structures");

			migrationBuilder.DropColumn(
				 name: "ImportantAreas",
				 table: "Structures");

			migrationBuilder.DropColumn(
				 name: "ImportantPoliticalPersonalities",
				 table: "Structures");

			migrationBuilder.DropColumn(
				 name: "IsSeat",
				 table: "Structures");

			migrationBuilder.DropColumn(
				 name: "LanguageId",
				 table: "Structures");

			migrationBuilder.DropColumn(
				 name: "MaleVoters",
				 table: "Structures");

			migrationBuilder.DropColumn(
				 name: "Problems",
				 table: "Structures");

			migrationBuilder.DropColumn(
				 name: "RuralAreaPer",
				 table: "Structures");

			migrationBuilder.DropColumn(
				 name: "TotalPollingStations",
				 table: "Structures");

			migrationBuilder.DropColumn(
				 name: "Trivia",
				 table: "Structures");

			migrationBuilder.DropColumn(
				 name: "UrbanAreaPer",
				 table: "Structures");

			migrationBuilder.AddColumn<string>(
				 name: "ProvincialAssemblyHalka_Code",
				 table: "Structures",
				 type: "nvarchar(max)",
				 nullable: true);
		}
	}
}
