﻿using Microsoft.EntityFrameworkCore.Migrations;
using System;

namespace ElectionAppServer.Migrations
{
	public partial class AddAuditLogtoElectionPhase : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.AddColumn<string>(
				 name: "CreatedBy",
				 table: "ElectionPhases",
				 maxLength: 450,
				 nullable: true);

			migrationBuilder.AddColumn<DateTime>(
				 name: "CreatedDate",
				 table: "ElectionPhases",
				 nullable: false,
				 defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

			migrationBuilder.AddColumn<string>(
				 name: "ModifiedBy",
				 table: "ElectionPhases",
				 maxLength: 450,
				 nullable: true);

			migrationBuilder.AddColumn<DateTime>(
				 name: "ModifiedDate",
				 table: "ElectionPhases",
				 nullable: true);
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropColumn(
				 name: "Created<PERSON><PERSON>",
				 table: "ElectionPhases");

			migrationBuilder.DropColumn(
				 name: "CreatedDate",
				 table: "ElectionPhases");

			migrationBuilder.DropColumn(
				 name: "ModifiedBy",
				 table: "ElectionPhases");

			migrationBuilder.DropColumn(
				 name: "ModifiedDate",
				 table: "ElectionPhases");
		}
	}
}
