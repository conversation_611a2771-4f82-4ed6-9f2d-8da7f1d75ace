﻿//using Foolproof;

using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace ElectionAppServer.Model;

public class Election
{
    public Election()
    {
        ElectionPhases = new List<ElectionPhase>();
        PartyCoalitions = new List<PartyCoalition>();
    }

    public int Id { get; set; }
    public string EnglishTitle { get; set; }
    public string UrduTitle { get; set; }
    public string Description { get; set; }

    [Range(1980, 2200)] public int YearFrom { get; set; }

    [Range(1980, 2200)] public int YearTo { get; set; }

    public virtual List<ElectionPhase> ElectionPhases { get; set; }
    public virtual List<Structure> Structures { get; set; }
    public virtual List<PartyCoalition> PartyCoalitions { get; set; }

    //public virtual List<ElectionParty> ElectionParties { get; set; }
    //public virtual List<ElectionCandidate> ElectionCandidates { get; set; }
    public ElectionType? ElectionType { get; set; }

    public int? ParentElectionId { get; set; }
    public virtual Election ParentElection { get; set; }

    //public virtual Election ChildElection { get; set; }
    public virtual List<CandidateDistrict> CandidateDistricts { get; set; }

    //public virtual List<Namanigar> Namanigars { get; set; }
    public CandidatePoll CandidatePoll { get; set; } = CandidatePoll.GeneralElection;

    #region Audit Log Fields

    public DateTime CreatedDate { get; set; } = DateTime.Now;
    public DateTime? ModifiedDate { get; set; }

    [StringLength(450)] public string CreatedBy { get; set; }

    [StringLength(450)] public string ModifiedBy { get; set; }

    #endregion Audit Log Fields
}

public enum ElectionType
{
    General = 1,
    LocalBody = 2
}