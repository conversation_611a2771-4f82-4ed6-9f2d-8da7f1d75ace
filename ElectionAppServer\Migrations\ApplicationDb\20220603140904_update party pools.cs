﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace ElectionAppServer.Migrations.ApplicationDb
{
    public partial class updatepartypools : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "IsLBBalochistan",
                table: "Parties",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IsLBPunjab",
                table: "Parties",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IsLBSindh",
                table: "Parties",
                type: "bit",
                nullable: false,
                defaultValue: false);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "IsLBBalochistan",
                table: "Parties");

            migrationBuilder.DropColumn(
                name: "IsLBPunjab",
                table: "Parties");

            migrationBuilder.DropColumn(
                name: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
                table: "Parties");
        }
    }
}
