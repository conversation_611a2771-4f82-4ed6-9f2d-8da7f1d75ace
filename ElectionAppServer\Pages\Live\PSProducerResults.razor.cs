using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ElectionAppServer.Data;
using ElectionAppServer.DTO;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.SignalR.Client;
using Syncfusion.Blazor.Popups;

namespace ElectionAppServer.Pages.Live;

public partial class PSProducerResults
{
    private SfDialog dlgForm;
    private HubConnection hubConnection;
    private List<ResultDTO> results = new();

    [CascadingParameter] private Task<AuthenticationState> authenticationStateTask { get; set; }

    private LiveResultDetailDTO detail { get; set; }
    private bool isDlgVisible { get; set; }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            var user = (await authenticationStateTask).User;
            if (user.Identity.IsAuthenticated)
            {
                var dd = await localStorage.GetAsync<ElectionStateDTO>(confreader.GetSettingValue("electionkey")
                    .Result);
                state.SetState(dd.Value);
                var info = await _UserManager.GetUserAsync(user);
                await Task.CompletedTask;
                results = await Service.GetLivePSResults(dd.Value.PhaseId);
                results = (from k in results
                    //where k.ConstituencyId == 158
                    select k).ToList();
                StateHasChanged();
            }
            else
            {
                try
                {
                    await localStorage.DeleteAsync(await confreader.GetSettingValue("electionkey"));
                    state.SetState(
                        new ElectionStateDTO { ElectionId = 0, ElectionTitle = "", PhaseId = 0, PhaseTitle = "" });
                    StateHasChanged();
                }
                catch (Exception)
                {
                    // ignored
                }
            }
        }
    }

    protected override async Task OnInitializedAsync()
    {
        isDlgVisible = false;
        detail = new LiveResultDetailDTO { Nominations = new List<LiveResultCandidateDetailDTO>() };
        hubConnection = new HubConnectionBuilder().WithUrl(NavigationManager.ToAbsoluteUri("/psresultshub")).Build();
        hubConnection.On<ResultDTO>("ReceivePSResult", result =>
        {
            dlgForm.HideAsync();

            var isExist = (
                from r in results
                //where r.ConstituencyId == 158
                where r.PollingStationId == result.PollingStationId
                select r).FirstOrDefault();
            if (isExist != null)
            {
                isExist.WinnerCandidateId = result.WinnerCandidateId;
                isExist.WinnerName = result.WinnerName;
                isExist.WinnerParty = result.WinnerParty;
                isExist.WinnerVotes = result.WinnerVotes;
                isExist.RunnerUpCandidateId = result.RunnerUpCandidateId;
                isExist.RunnerUpName = result.RunnerUpName;
                isExist.RunnerUpParty = result.RunnerUpParty;
                isExist.RunnerUpVotes = result.RunnerUpVotes;
                isExist.OnAir = result.OnAir;
                isExist.Status = result.Status;
                isExist.LastUpdatedOn = result.LastUpdatedOn;
                isExist.PollingStationId = result.PollingStationId;
            }
            else
            {
                if (result.PhaseId == state.state.PhaseId)
                    results.Add(result);
            }

            results = (
                from r in results
                //where r.ConstituencyId == 158
                orderby r.Status descending, r.LastUpdatedOn descending
                select r).ToList();
            StateHasChanged();
        });
        hubConnection.On<ResultDTO>("ReceivePSNotification", result =>
        {
            var isExist = (
                from r in results
                where r.ElectionId == result.ElectionId && r.AssemblyId == result.AssemblyId &&
                      r.ConstituencyId == result.ConstituencyId && r.SeatTypeId == result.SeatTypeId
                select r).FirstOrDefault();
            results = (
                from r in results
                //where r.ConstituencyId == 158
                orderby r.OnAir descending, r.LastUpdatedOn descending
                select r).ToList();
            StateHasChanged();
        });
        await hubConnection.StartAsync();
        //results = await Service.GetLivePSResults(state.state.PhaseId);
        StateHasChanged();
        await Task.CompletedTask;
    }

    private void beforeOpen(BeforeOpenEventArgs args)
    {
        args.MaxHeight = null;
    }

    private async Task DisplayDetail(int pollingStationId, int assemblyId, int constituencyId, int seatTypeId)
    {
        detail = await Service.GetLiveResultDetailPS(pollingStationId, assemblyId, constituencyId, seatTypeId,
            state.state.PhaseId);

        //detail = await Service.GetLiveResultDetail(state.state.ElectionId, assemblyId, state.state.PhaseId,
        //	constituencyId, seatTypeId);
        await dlgForm.ShowAsync();
    }

    //private async Task MarkPublish(int phaseId, int assemblyId, int constituencyId, int seatTypeId, int psId)
    private async Task MarkPublish(ResultDTO r)
    {
        var user = (await authenticationStateTask).User;
        var logDate = DateTime.Now;
        var msg = await Service.MarkPublishPS(r, user.Identity.Name, logDate);
        if (msg == "OK")
        {
            r.Status = "Published";
            r.LastUpdatedOn = logDate;
            await hubConnection.SendAsync("SendPSResult", r);
            var allowToPush = await confreader.GetSettingValue("AllowToPush");
            if (allowToPush == "Yes")
            {
                var imm = await Service.GetImmResultPS(r.ElectionId, r.PhaseId, r.AssemblyId, r.ConstituencyId,
                    r.SeatTypeId, r.PollingStationId);
                await Translation.PostDataPS(imm);
            }
        }
    }
}