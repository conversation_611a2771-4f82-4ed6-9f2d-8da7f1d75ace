﻿using Microsoft.EntityFrameworkCore.Migrations;
using System;

namespace ElectionAppServer.Migrations.ApplicationDb
{
	public partial class PreStructure : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.AddColumn<int>(
				 name: "PrevStructureId",
				 table: "Structures",
				 nullable: true);

			migrationBuilder.AlterColumn<DateTime>(
				 name: "CreatedDate",
				 table: "ElectionParties",
				 nullable: false,
				 defaultValue: new DateTime(2020, 8, 19, 18, 37, 48, 508, DateTimeKind.Local).AddTicks(8735),
				 oldClrType: typeof(DateTime),
				 oldType: "datetime2",
				 oldDefaultValue: new DateTime(2020, 7, 22, 19, 36, 38, 326, DateTimeKind.Local).AddTicks(8578));

			migrationBuilder.AlterColumn<DateTime>(
				 name: "CreatedDate",
				 table: "ElectionCandidates",
				 nullable: false,
				 defaultValue: new DateTime(2020, 8, 19, 18, 37, 48, 513, DateTimeKind.Local).AddTicks(7325),
				 oldClrType: typeof(DateTime),
				 oldType: "datetime2",
				 oldDefaultValue: new DateTime(2020, 7, 22, 19, 36, 38, 332, DateTimeKind.Local).AddTicks(9315));

			migrationBuilder.CreateIndex(
				 name: "IX_Structures_PrevStructureId",
				 table: "Structures",
				 column: "PrevStructureId");

			migrationBuilder.AddForeignKey(
				 name: "FK_Structures_Structures_PrevStructureId",
				 table: "Structures",
				 column: "PrevStructureId",
				 principalTable: "Structures",
				 principalColumn: "Id",
				 onDelete: ReferentialAction.Restrict);
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropForeignKey(
				 name: "FK_Structures_Structures_PrevStructureId",
				 table: "Structures");

			migrationBuilder.DropIndex(
				 name: "IX_Structures_PrevStructureId",
				 table: "Structures");

			migrationBuilder.DropColumn(
				 name: "PrevStructureId",
				 table: "Structures");

			migrationBuilder.AlterColumn<DateTime>(
				 name: "CreatedDate",
				 table: "ElectionParties",
				 type: "datetime2",
				 nullable: false,
				 defaultValue: new DateTime(2020, 7, 22, 19, 36, 38, 326, DateTimeKind.Local).AddTicks(8578),
				 oldClrType: typeof(DateTime),
				 oldDefaultValue: new DateTime(2020, 8, 19, 18, 37, 48, 508, DateTimeKind.Local).AddTicks(8735));

			migrationBuilder.AlterColumn<DateTime>(
				 name: "CreatedDate",
				 table: "ElectionCandidates",
				 type: "datetime2",
				 nullable: false,
				 defaultValue: new DateTime(2020, 7, 22, 19, 36, 38, 332, DateTimeKind.Local).AddTicks(9315),
				 oldClrType: typeof(DateTime),
				 oldDefaultValue: new DateTime(2020, 8, 19, 18, 37, 48, 513, DateTimeKind.Local).AddTicks(7325));
		}
	}
}
