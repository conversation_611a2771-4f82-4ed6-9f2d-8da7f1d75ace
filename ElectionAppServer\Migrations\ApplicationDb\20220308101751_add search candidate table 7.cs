﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace ElectionAppServer.Migrations.ApplicationDb
{
    public partial class addsearchcandidatetable7 : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropPrimaryKey(
                name: "PK_SearchCandidates",
                table: "SearchCandidates");

            migrationBuilder.DropColumn(
                name: "Id",
                table: "SearchCandidates");

            migrationBuilder.AddPrimaryKey(
                name: "PK_SearchCandidates",
                table: "SearchCandidates",
                columns: new[] { "ElectionId", "CandidateId" });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropPrimaryKey(
                name: "PK_SearchCandidates",
                table: "SearchCandidates");

            migrationBuilder.AddColumn<int>(
                name: "Id",
                table: "SearchCandidates",
                type: "int",
                nullable: false,
                defaultValue: 0)
                .Annotation("SqlServer:Identity", "1, 1");

            migrationBuilder.AddPrimaryKey(
                name: "PK_SearchCandidates",
                table: "SearchCandidates",
                column: "Id");
        }
    }
}
