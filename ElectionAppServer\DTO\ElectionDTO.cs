﻿using System;
using System.ComponentModel.DataAnnotations;
using ElectionAppServer.Model;

namespace ElectionAppServer.DTO;

public class ElectionDTO
{
    public int Id { get; set; }
    public int ElectionId { get; set; }
    public string Head { get; set; }

    [Required] [StringLength(200)] public string EnglishTitle { get; set; }

    [Required] [StringLength(200)] public string UrduTitle { get; set; }

    public string PhaseTitle { get; set; }

    [Required] public DateTime? StartDate { get; set; }

    [Required] public int? CandidatePoolId { get; set; }

    public string ElectionDate
    {
        get
        {
            if (StartDate == null)
                return "";
            var dd = new DateTime(StartDate.Value.Year, StartDate.Value.Month, StartDate.Value.Day);
            return dd.ToString("d MMM, yyyy");
        }
    }

    public string Description { get; set; }
    public int YearFrom { get; set; }
    public int YearTo { get; set; }
    public bool CanDelete { get; set; }
    public ElectionType? ElectionType { get; internal set; }
    public bool IsGeneral { get; set; }
    public int? Pool { get; set; }
    public CandidatePoll CandidatePoll { get; internal set; }
}