﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using BlazorInputFile;
using ElectionAppServer.Data;
using ElectionAppServer.DTO;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Syncfusion.Blazor.Grids;
using Syncfusion.Blazor.Notifications;
using Syncfusion.Blazor.Popups;

namespace ElectionAppServer.Pages.Setup;

public partial class Symbols
{
    private readonly string FormTitle = "Create Symbol";

    //private HubConnection connection;
    private SfDialog dlgForm;

    //--> No in use //private ElementReference inputElement;
    private IFileListEntry file;
    private bool isDlgVisible;
    public long max;
    private List<SymbolDTO> objList;
    private string SaveButtonText = "Save";
    private SymbolDTO selectedObj = new();
    public string ToastContent = "Unable to delete Record, Related Record(s) Exist";
    private SfToast ToastObj;

    public long value;

    // AuthenticationState is available as a CascadingParameter
    [CascadingParameter] private Task<AuthenticationState> AuthenticationStateTask { get; set; }

    public SfGrid<SymbolDTO> Grid { get; set; }

    public bool IsDisable
    {
        get
        {
            if (selectedObj.Id != 0) return false;
            if (FileBase64String == "") return true;
            return false;
        }
    }

    //--> No in use //		private string status;
    public string FileBase64String { get; set; }
    public byte[] FileByteArray { get; set; }
    public string mediapath { get; set; }
    public string baseurl { get; set; }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        //return base.OnAfterRenderAsync(firstRender);
        if (firstRender)
        {
            var user = (await AuthenticationStateTask).User;
            if (user.Identity.IsAuthenticated)
            {
                //var dd =  await ProtectedSessionStore.GetAsync<ElectionStateDTO>("election");
                var dd = await localStorage.GetAsync<ElectionStateDTO>(await confreader.GetSettingValue("electionkey"));
                state.SetState(dd.Value);
            }
            else
            {
                try
                {
                    //await  ProtectedSessionStore.DeleteAsync("election");
                    await localStorage.DeleteAsync(await confreader.GetSettingValue("electionkey"));
                    state.SetState(new ElectionStateDTO
                        { ElectionId = 0, ElectionTitle = "", PhaseId = 0, PhaseTitle = "" });
                }
                catch (Exception)
                {
                    // ignored
                }

                NavigationManager.NavigateTo("");
            }

            StateHasChanged();
        }
    }

    private async Task HandleFileSelected(IFileListEntry[] files)
    {
        file = files.FirstOrDefault();
        var dd = await file.ReadAllAsync();
        FileByteArray = dd.ToArray();
        FileBase64String = Convert.ToBase64String(FileByteArray);
    }

    protected override async Task OnInitializedAsync()
    {
        mediapath = env.WebRootPath + "\\media\\"; // await confreader.GetSettingValue("mediapath");
        baseurl = await confreader.GetSettingValue("BaseURL");
        // -- Old Way --- connection = new HubConnectionBuilder().WithUrl(BaseURL + "/symbolhub").Build();
        //connection = new HubConnectionBuilder()
        //.WithUrl(NavigationManager.ToAbsoluteUri("/symbolhub"))
        //.Build();
        //connection.On<SymbolDTO>("ReceiveInfo", On_ReceiveMessage);
        //connection.On<SymbolDTO>("ReceiveDelInfo", On_ReceiveDelMessage);
        //await connection.StartAsync();
        // Get the current user
        //var user = (await authenticationStateTask).User;
        // Get the objList for the current user
        // We access WeatherobjListervice using @Service
        objList = await Service.GetList();
    }

    // private Task On_ReceiveDelMessage(SymbolDTO cc)
    // {
    //     try
    //     {
    //         objList.Remove(cc);
    //         StateHasChanged();
    //     }
    //     catch (Exception)
    //     {
    //     }
    //     await Grid.Refresh();
    //     return Task.CompletedTask;
    // }
    // private Task On_ReceiveMessage(SymbolDTO cc)
    // {
    //     var obj = objList.FirstOrDefault(m => m.Id == cc.Id);
    //     var rr = new Random();
    //     if (obj == null)
    //     {
    //         cc.ChangeKey = rr.Next().ToString();
    //         objList.Add(cc);
    //     }
    //     else
    //     {
    //         obj.Urdu = cc.Urdu;
    //         obj.English = cc.English;
    //         //obj.Code = cc.Code;
    //         obj.ChangeKey = rr.Next().ToString();
    //     }
    //     objList = objList.OrderBy(c => c.English).ToList();
    //     await Grid.Refresh();
    //     StateHasChanged();
    //     return Task.CompletedTask;
    // }
    private void OpenCreateForm()
    {
        selectedObj = new SymbolDTO();
        dlgForm.ShowAsync();
        SaveButtonText = "Create";
        FileBase64String = "";
        FileByteArray = new byte[1];
    }

    private void OpenEditForm(SymbolDTO st)
    {
        FileBase64String = "";
        selectedObj.Id = st.Id;
        selectedObj.Urdu = st.Urdu;
        selectedObj.English = st.English;
        //selectedObj.Code = st.Code;
        dlgForm.ShowAsync();
        SaveButtonText = "Update";
        var f = mediapath + @"\symbols\" + selectedObj.Id + ".jpg";
        try
        {
            var fs = File.OpenRead(f);
            var fileBytes = new byte[fs.Length];
            fs.Read(fileBytes, 0, fileBytes.Length);
            fs.Close();
            FileBase64String = Convert.ToBase64String(fileBytes);
        }
        catch (Exception)
        {
            //FileBase64Strin = "";
        }
    }

    private void ClearData()
    {
        selectedObj.Id = 0;
        selectedObj.Urdu = "";
        selectedObj.English = "";
        //selectedObj.Code = 1;
        SaveButtonText = "Create";
        FileBase64String = "";
        FileByteArray = new byte[1];
    }

    private async Task SaveSymbolData()
    {
        // Close the Popup
        // ShowPopup = false;
        // Get the current user
        var user = (await AuthenticationStateTask).User;
        // A new forecast will have the Id set to 0
        try
        {
            if (selectedObj.Id == 0)
            {
                if (FileBase64String == "") return;
                var res = await Service.CreateSymbol(selectedObj, user.Identity.Name);
                //objList = await Service.GetList();
                await dlgForm.HideAsync();
                if (FileBase64String != "" && FileByteArray != null && FileByteArray.Length > 0)
                    File.WriteAllBytes(mediapath + @"\symbols\" + res.Id + ".jpg", FileByteArray);
                //await connection.InvokeAsync("SendInfo", selectedObj);
                objList = await Service.GetList();
                ClearData();
                StateHasChanged();
            }
            else
            {
                var res = await Service.UpdateSymbol(selectedObj, user.Identity.Name);
                //objList = await Service.GetList();
                if (FileBase64String != "" && FileByteArray != null && FileByteArray.Length > 0 &&
                    FileByteArray.Length != 1)
                    File.WriteAllBytes(mediapath + @"\symbols\" + res.Id + ".jpg", FileByteArray);
                //await connection.InvokeAsync("SendInfo", selectedObj);
                ClearData();
                await dlgForm.HideAsync();
                objList = await Service.GetList();
                StateHasChanged();
            }
        }
        catch (Exception ex)
        {
            var mm = "";
            mm = ex.Message;
            if (ex.InnerException != null)
                mm += "Detail: " + ex.InnerException.Message;
            var m2 = new ToastModel { Title = "Error", Content = mm };
            await ToastObj.ShowAsync(m2);
        }
    }

    private async Task DeleteRecord(int Id)
    {
        var para = new[] { "Are you want to delete this record?" };
        var res = await JSRuntime.InvokeAsync<bool>("confirm", para);
        if (res == false)
            return;
        //string msg = "";
        //bool hasError = false;
        try
        {
            var msg2 = await Service.DeleteSymbol(Id);
            var obj = new SymbolDTO { Id = Id };
            //await connection.InvokeAsync<SymbolDTO>("DeleteRec", obj);
            objList = await Service.GetList();
            ClearData();
            StateHasChanged();
        }
        catch (Exception ee)
        {
            var mm = "";
            mm = ee.Message;
            if (ee.InnerException != null)
                mm += "Detail: " + ee.InnerException.Message;
            var m2 = new ToastModel { Title = "Error", Content = mm };
            await ToastObj.ShowAsync(m2);
        }
    }

    private async Task TranslateToUrdu()
    {
        try
        {
            if (string.IsNullOrEmpty(selectedObj.Urdu))
                selectedObj.Urdu = await Translation.TranslateText(selectedObj.English);
        }
        catch (Exception)
        {
            // ignored
        }
    }

    public void CustomizeCell(QueryCellInfoEventArgs<SymbolDTO> args)
    {
        if (args.Column.Field == "Urdu")
        {
            args.Cell.AddClass(new[] { "urdu-column" });
            args.Cell.AddStyle(new[] { "font-size:18px;line-height: 1.4;text-align: right" });
        }
    }

    private void beforeOpen(BeforeOpenEventArgs args)
    {
        args.MaxHeight = null;
    }
}