﻿using FluentValidation;

namespace ElectionAppServer.DTO;

public class CreateStructureDTO
{
    public int Id { get; set; }
    public int? ConstTypeId { get; set; }
    public string Code { get; set; }
    public int ElectionAssemblyId { get; set; }
    public string UrduTitle { get; set; }
    public string EnglishTitle { get; set; }
    public StructureType? StructureType { get; set; }
    public int? ProvinceId { get; set; }
    public int? DivisionId { get; set; }
    public int? DistrictId { get; set; }
    public int? TownId { get; set; }
    public int? UCId { get; set; }
    public bool IsPostponed { get; set; }
    public bool HasBilaMokablia { get; set; }
    public string Profile { get; set; }

    #region Voters

    public int? MaleVoters { get; set; }
    public int? YouthVoters { get; set; }
    public int? FemaleVoters { get; set; }
    public int? TotalPollingStations { get; set; }

    public int TotalVoters => (MaleVoters ?? 0) + (FemaleVoters ?? 0);

    public int? Population { get; set; }

    #endregion Voters

    #region Demographics

    //public int? CasteId { get; set; }
    //public int? LanguageId { get; set; }
    public string Languages { get; set; }

    public string Castes { get; set; }
    public string Trivia { get; set; }
    public string GeneralTrivia { get; set; }
    public string ImportantPoliticalPersonalities { get; set; }
    public string Problems { get; set; }
    public string ImportantAreas { get; set; }
    public float? RuralAreaPer { get; set; }
    public float? UrbanAreaPer { get; set; }

    public int? ParentId { get; set; }

    public int ElectionId { get; set; }
    public int TotalWards { get; set; } = 0;
    public string MajorityIncomeSource { get; set; }
    public string LiteracyRate { get; set; }
    public string AverageHouseholdIncome { get; set; }
    public string Area { get; set; }

    #endregion Demographics
}

public enum StructureType
{
    Province = 1,
    Division = 2,
    District = 3,
    Town = 4,
    UnionCouncil = 5,
    Ward = 6,
    NA = 7,
    PA = 8
}

public class CreateStructureDTOValidator : AbstractValidator<CreateStructureDTO>
{
    public CreateStructureDTOValidator()
    {
        RuleFor(c => c.ConstTypeId).NotNull().WithMessage("Constituency Type is required");
        RuleFor(c => c.EnglishTitle).NotEmpty().WithMessage("Title (English) is required");
        RuleFor(c => c.UrduTitle).NotEmpty().WithMessage("Title (Urdu) is required");
        RuleFor(c => c.StructureType).IsInEnum().WithMessage("Please select valid Structure Type");
        RuleFor(c => c.Code).NotEmpty().WithMessage("Code is required");
        //RuleFor(c => c.MaleVoters).NotEmpty().WithMessage("Male voters is required");
        //RuleFor(c => c.FemaleVoters).NotEmpty().WithMessage("Female voters is required");
        //RuleFor(c => c.TotalPollingStations).NotEmpty().WithMessage("Total Polling Stations is required");

        When(c => c.ConstTypeId == (int)StructureType.UnionCouncil, () =>
        {
            RuleFor(c => c.ProvinceId).NotNull().WithMessage("Province is required");
            //RuleFor(c => c.DivisionId).NotNull().WithMessage("Division is required");
            RuleFor(c => c.DistrictId).NotNull().WithMessage("District is required");
            RuleFor(c => c.TownId).NotNull().WithMessage("Town is required");
        });

        When(c => c.ConstTypeId == (int)StructureType.Ward, () =>
        {
            RuleFor(c => c.ProvinceId).NotNull().WithMessage("Province is required");
            //RuleFor(c => c.DivisionId).NotNull().WithMessage("Division is required");
            RuleFor(c => c.DistrictId).NotNull().WithMessage("District is required");
            RuleFor(c => c.TownId).NotNull().WithMessage("Town is required");
            RuleFor(c => c.UCId).NotNull().WithMessage("Union Council is required");
        });

        When(c => c.ConstTypeId == (int)StructureType.NA, () =>
        {
            RuleFor(c => c.ProvinceId).NotNull().WithMessage("Province is required");
            //RuleFor(c => c.DivisionId).NotNull().WithMessage("Division is required");
            RuleFor(c => c.DistrictId).NotNull().WithMessage("District is required");
        });

        When(c => c.ConstTypeId == (int)StructureType.PA, () =>
        {
            RuleFor(c => c.ProvinceId).NotNull().WithMessage("Province is required");
            //RuleFor(c => c.DivisionId).NotNull().WithMessage("Division is required");
            RuleFor(c => c.DistrictId).NotNull().WithMessage("District is required");
        });

        //When(c => c.StructureType == StructureType.UnionCouncil ||
        //			 c.StructureType == StructureType.Ward ||
        //			 c.StructureType == StructureType.NA ||
        //			 c.StructureType == StructureType.PA, () =>
        //	  {
        //		  RuleFor(c => c.ProvinceId).NotNull().WithMessage("Province is required");
        //	  });

        //When(c => c.StructureType == StructureType.UnionCouncil ||
        //			 c.StructureType == StructureType.Ward ||
        //			 c.StructureType == StructureType.NA ||
        //			 c.StructureType == StructureType.PA, () =>
        //			 {
        //				 RuleFor(c => c.DivisionId).NotNull().WithMessage("Division is required");
        //			 });

        //When(c => c.StructureType == StructureType.UnionCouncil ||
        //			 c.StructureType == StructureType.Ward ||
        //			 c.StructureType == StructureType.NA ||
        //			 c.StructureType == StructureType.PA, () =>
        //			 {
        //				 RuleFor(c => c.ProvinceId).NotNull().WithMessage("District is required");
        //			 });

        //When(c => c.StructureType == StructureType.UnionCouncil ||
        //			 c.StructureType == StructureType.Ward, () =>
        //			 {
        //				 RuleFor(c => c.TownId).NotNull().WithMessage("Town is required");
        //			 });

        //When(c => c.StructureType == StructureType.UnionCouncil, () =>
        //			 {
        //				 RuleFor(c => c.UCId).NotNull().WithMessage("Union Council is required");
        //			 });

        //When(c => c.StructureType == StructureType.UnionCouncil || c.StructureType == StructureType.Ward, () =>
        // {
        //	 RuleFor(c => c.DivisionId).NotNull().WithMessage("Division is required");
        // });

        //When(c => c.StructureType == StructureType.UnionCouncil || c.StructureType == StructureType.Ward, () =>
        //{
        //	RuleFor(c => c.DistrictId).NotNull().WithMessage("District is required");
        //});

        //When(c => c.StructureType == StructureType.UnionCouncil || c.StructureType == StructureType.Ward, () =>
        //{
        //	RuleFor(c => c.TownId).NotNull().WithMessage("Town is required");
        //});

        //When(c => c.StructureType == StructureType.Ward, () =>
        //{
        //	RuleFor(c => c.UCId).NotNull().WithMessage("Union Council is required");
        //});

        //When(c => c.StructureType == StructureType.UnionCouncil || c.StructureType == StructureType.Ward || c.StructureType == StructureType.NA || c.StructureType == StructureType.PA, () =>
        //{
        //   RuleFor(c => c.Code).NotNull().WithMessage("Code is required");
        //   RuleFor(c => c.Code).MaximumLength(10).WithMessage("Maximum 10 characters are allowed");
        //});

        //RuleFor(c => c.Trivia).MaximumLength(2000).WithMessage("Maximum 2000 Characters are allowed");
        RuleFor(c => c.ImportantPoliticalPersonalities).MaximumLength(500)
            .WithMessage("Maximum 500 Characters are allowed");
        RuleFor(c => c.Problems).MaximumLength(4000).WithMessage("Maximum 500 Characters are allowed");
    }
}