﻿@page "/assemblies/{electionId:int}"
@attribute [Authorize(Roles = "Administrators,Data Viewer")]
@using FilterType = Syncfusion.Blazor.DropDowns.FilterType
@using Size = MudBlazor.Size
@using ButtonType = MudBlazor.ButtonType
@inherits OwningComponentBase<ElectionAssemblyService>
<SfDialog Width="600px" @bind-Visible="@isDlgVisible" @ref="dlgForm" IsModal="true" ShowCloseIcon="true">
    <DialogEvents OnOpen="beforeOpen"></DialogEvents>
    <DialogTemplates>
        <Header>@FormTitle</Header>
        <Content>
            <EditForm Model="@selectedObj" OnValidSubmit="@SaveData" Context="ef">
                <DataAnnotationsValidator/>
                <div class="row">
                    <div class="col-md">
                        <SfTextBox @bind-Value="selectedObj.EnglishTitle" Placeholder="Name (English)" OnBlur="TranslateToUrdu" FloatLabelType="FloatLabelType.Always"></SfTextBox>
                        <ValidationMessage For="@(() => selectedObj.EnglishTitle)"/>
                    </div>
                    <div class="col-md">
                        <SfTextBox @bind-Value="selectedObj.UrduTitle" Placeholder="نام (اردو)" FloatLabelType="FloatLabelType.Always" EnableRtl="true"></SfTextBox>
                        <ValidationMessage For="@(() => selectedObj.UrduTitle)"/>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md">
                        <SfDropDownList FilterType="FilterType.Contains" TItem="GeneralItemDTO" TValue="int?" @bind-Value="selectedObj.AssemblyTypeId" Placeholder="Select Assembly Type" DataSource="@assemblyTypes" FloatLabelType="FloatLabelType.Always" AllowFiltering="true">
                            <DropDownListFieldSettings Value="Id" Text="EnglishTitle"></DropDownListFieldSettings>
                        </SfDropDownList>
                        <ValidationMessage For="@(() => selectedObj.AssemblyTypeId)"/>
                    </div>
                    <div class="col-md">
                        <SfDropDownList FilterType="FilterType.Contains" TItem="GeneralItemDTO" TValue="int?" @bind-Value="selectedObj.NominationTypeId" Placeholder="Election Based On" DataSource="nominationTypes" FloatLabelType="FloatLabelType.Always" AllowFiltering="true">
                            <DropDownListFieldSettings Value="Id" Text="EnglishTitle"></DropDownListFieldSettings>
                        </SfDropDownList>
                        <ValidationMessage For="@(() => selectedObj.NominationTypeId)"/>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md">
                        <SfNumericTextBox ShowSpinButton="false" Format="####" @bind-Value="selectedObj.ReserveSeats" Placeholder="Reserve Seats" FloatLabelType="FloatLabelType.Always" Min="0" Max="99999"></SfNumericTextBox>
                        <ValidationMessage For="@(() => selectedObj.ReserveSeats)"/>
                    </div>
                    <div class="col-md">
                        <SfNumericTextBox ShowSpinButton="false" Format="####" @bind-Value="selectedObj.WomenSeats" Placeholder="Women Seats" FloatLabelType="FloatLabelType.Always" Min="0" Max="99999"></SfNumericTextBox>
                        <ValidationMessage For="@(() => selectedObj.WomenSeats)"/>
                    </div>
                </div>
                @if (electionType == ElectionType.LocalBody)
                {
                    <div class="row">
                        <div class="col">
                            <div class="tabheader mb-2">Seats Configuration</div>
                            <SfGrid @ref="ConsSeatsGrid" DataSource="selectedObj.ConsSeatTypeList" TValue="ConSeatDTO" Toolbar="@(new List<string> { "Add", "Edit", "Delete", "Cancel", "Update" })">
                                <GridEditSettings NewRowPosition="NewRowPosition.Bottom" AllowAdding="true" AllowEditing="true" AllowDeleting="true" ShowConfirmDialog="true"></GridEditSettings>
                                <GridEvents OnActionBegin="ActionBeginHandler" TValue="ConSeatDTO">
                                </GridEvents>
                                <GridColumns>
                                    <GridColumn Visible="false" Width="150px" IsPrimaryKey="true" Field=@nameof(ConSeatDTO.Id)></GridColumn>
                                    <GridForeignColumn HeaderText="Constituency Type"
                                                       Field=@nameof(ConSeatDTO.ConstituencyTypeId)
                                                       ForeignKeyField="Id"
                                                       ForeignKeyValue="Title"
                                                       ForeignDataSource="ConstituencyTypeList"/>
                                    <GridForeignColumn HeaderText="Seat Type"
                                                       Field=@nameof(ConSeatDTO.SeatTypeId)
                                                       ForeignKeyField="Id"
                                                       ForeignKeyValue="EnglishTitle"
                                                       ForeignDataSource="seatTypes"/>
                                    @*<GridColumn Width="150px" Field=@nameof(ConSeatDTO.ConstituencyTypeId) EditType="EditType.DropDownEdit" ForeignKeyField="Id" ForeignKeyValue="Title" DataSource="@ConstituencyTypeList" HeaderText="Constituency Type"></GridColumn>*@
                                    @*<GridForeignColumn Field=@nameof(ConSeatDTO.ConstituencyTypeId) HeaderText="Constituency Type" ForeignKeyValue="EnglishTitle" ForeignDataSource="@ConstituencyTypeList" Width="150"></GridForeignColumn>*@
                                    @*<GridColumn Width="150px" Field=@nameof(ConSeatDTO.SeatTypeId) EditType="EditType.DropDownEdit" ForeignKeyField="Id" ForeignKeyValue="EnglishTitle" DataSource="@seatTypes" HeaderText="Seat Type"></GridColumn>*@
                                    @*<GridForeignColumn Field=@nameof(ConSeatDTO.SeatTypeId) HeaderText="Seat Type" ForeignKeyValue="EnglishTitle" ForeignDataSource="@seatTypes" Width="150"></GridForeignColumn>*@
                                    <GridColumn Width="150px" Field=@nameof(ConSeatDTO.SeatCount) EditType="EditType.NumericEdit" Format="####" HeaderText="Total Seats"></GridColumn>
                                </GridColumns>
                            </SfGrid>
                        </div>
                    </div>
                }
                <AuthorizeView Roles="Administrators">
                    <div class="row">
                        <div class="col-md">
                            <MudButton ButtonType="ButtonType.Submit" Disabled="DisableButton" Size="Size.Small" Variant="Variant.Filled" Color="Color.Primary" StartIcon="@Icons.Material.Filled.Save">@SaveButtonText</MudButton>
                        </div>
                    </div>
                </AuthorizeView>
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>
<SfToast @ref="ToastObj" Title="Adaptive Tiles Meeting" Icon="e-meeting">
    <ToastPosition X="Right"></ToastPosition>
</SfToast>
<!-- AuthorizeView allows us to only show sections of the page -->
<!-- based on the security on the current user -->
@if (objList == null)
{
    <p>
        <em>Loading...</em>
    </p>
}
else
{
    <div>
        <MudText Class="mb-2" Typo="Typo.h5"><MudIcon Size="Size.Large" StartIcon="@Icons.Material.Filled.Apartment"/>Assemblies</MudText>

        <AuthorizeView Roles="Administrators">
            <div class="row">
                <MudButton Color="Color.Primary" Size="Size.Small" Variant="Variant.Filled" StartIcon="@Icons.Material.Filled.Add" Class="mb-2" OnClick="OpenCreateForm">Create Assembly</MudButton>
            </div>
        </AuthorizeView>
        <div class="row">
            <SfGrid DataSource="@objList" AllowFiltering="true" @ref="Grid" AllowSorting="true" AllowTextWrap="true" Width="100%" Height="calc(100vh - 210px)" FrozenColumns="3">
                <GridEvents QueryCellInfo="CustomizeCell" TValue="ElectionAssemblyDTO"></GridEvents>
                <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Excel"></GridFilterSettings>
                <GridColumns>
                    <GridColumn AutoFit="true" Field="Id" HeaderText="Id"></GridColumn>
                    <GridColumn Width="180px" Field="UrduTitle" HeaderText="Urdu Title" TextAlign="TextAlign.Right"></GridColumn>
                    <GridColumn Width="180px" Field="EnglishTitle" HeaderText="English Title"></GridColumn>
                    <GridColumn Width="130px" Field="AssemblyType" HeaderText="Type"></GridColumn>
                    @*<GridColumn AutoFit="true" Field="@nameof(ElectionAssemblyDTO.SeatConfiguration)" HeaderText="Seat Configuration"></GridColumn>*@
                    @*<GridColumn AutoFit="true" Field="ReserveSeats" HeaderText="Reserve Seats"></GridColumn>*@
                    @*<GridColumn AutoFit="true" Field="WomenSeats" HeaderText="Women Seats"></GridColumn>*@
                    @*<GridColumn Field="Constituencies" HeaderText="Constituencies" DisableHtmlEncode="false"></GridColumn>*@
                    @*<GridColumn AutoFit="true" Field="SeatsList" HeaderText="Seats" DisableHtmlEncode="false"></GridColumn>*@
                    <GridColumn HeaderText="Audit Info" Width="310px">
                        <Template Context="kk">
                            @{
                                if (kk is ElectionAssemblyDTO oo)
                                {
                                    <div style="font-size:11px">
                                        <span>Created By: @oo.CreatedBy</span><span> On @oo.CreatedOn</span>
                                        @if (!string.IsNullOrEmpty(oo.ModifiedBy))
                                        {
                                            <br/>
                                            <span>Modified By: @oo.ModifiedBy</span>
                                            <span> On @oo.ModifiedOn</span>
                                        }
                                    </div>
                                }
                            }
                        </Template>
                    </GridColumn>
                    <GridColumn HeaderText="Actions" AllowFiltering="false" AutoFit="true">
                        <Template Context="ss">
                            @{
                                var kk = ss as ElectionAssemblyDTO;
                                <a target="@($"am{kk.Id}")" href="report/assembly/@kk.Id">
                                    <MudIcon Icon="@Icons.Material.Filled.TableChart"></MudIcon>
                                </a>
                                <AuthorizeView Roles="Administrators">
                                    <MudFab StartIcon="@Icons.Material.Filled.Delete" Size="Size.Small"
                                            Color="Color.Error" OnClick="@(() => DeleteRecord(kk.Id))">
                                    </MudFab>
                                    <MudFab Color="Color.Primary" Size="Size.Small" StartIcon="@Icons.Material.Filled.Edit" OnClick="@(() => OpenEditForm(kk))"></MudFab>
                                </AuthorizeView>
                                <a href="/constituencies/@kk.Id">Constituencies</a>
                            }
                        </Template>
                    </GridColumn>
                </GridColumns>
            </SfGrid>
        </div>
    </div>
    @*<PageCaption Title="Assemblies"></PageCaption>*@
}