﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace ElectionAppServer.Migrations
{
	public partial class JavaScriptInterOp : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropForeignKey(
				 name: "FK_ElectionAssemblies_AssemblyType_AssemblyTypeId",
				 table: "ElectionAssemblies");

			migrationBuilder.AddColumn<string>(
				 name: "Code",
				 table: "ElectionAssemblyConstituencies",
				 nullable: true);

			migrationBuilder.AddColumn<bool>(
				 name: "IsSeat",
				 table: "ElectionAssemblyConstituencies",
				 nullable: false,
				 defaultValue: false);

			migrationBuilder.AddForeignKey(
				 name: "FK_ElectionAssemblies_AssemblyType_AssemblyTypeId",
				 table: "ElectionAssemblies",
				 column: "AssemblyTypeId",
				 principalTable: "AssemblyType",
				 principalColumn: "Id",
				 onDelete: ReferentialAction.Restrict);
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropFore<PERSON><PERSON>ey(
				 name: "FK_ElectionAssemblies_AssemblyType_AssemblyTypeId",
				 table: "ElectionAssemblies");

			migrationBuilder.DropColumn(
				 name: "Code",
				 table: "ElectionAssemblyConstituencies");

			migrationBuilder.DropColumn(
				 name: "IsSeat",
				 table: "ElectionAssemblyConstituencies");

			migrationBuilder.AddForeignKey(
				 name: "FK_ElectionAssemblies_AssemblyType_AssemblyTypeId",
				 table: "ElectionAssemblies",
				 column: "AssemblyTypeId",
				 principalTable: "AssemblyType",
				 principalColumn: "Id",
				 onDelete: ReferentialAction.Cascade);
		}
	}
}
