﻿function PrintElem(elem) {
   var mywindow = window.open('', 'PRINT', 'height=600,width=800');

   mywindow.document.write('<html><head><title>' + document.title + '</title>');
   mywindow.document.write('	<meta name="viewport" content="width=device-width, initial-scale=1.0" />');
   mywindow.document.write('<link rel="stylesheet" href="css/bootstrap/bootstrap.min.css" />');
   mywindow.document.write('<link href="css/site.css" rel="stylesheet" />');
   mywindow.document.write('<link href="_content/Syncfusion.Blazor/styles/material.css" rel="stylesheet" />');
      
   mywindow.document.write('</head>'+'<body>');
   mywindow.document.write('<h1>' + document.title + '</h1>');
   mywindow.document.write(document.getElementById(elem).innerHTML);
   mywindow.document.write('</body></html>');

   mywindow.document.close(); // necessary for IE >= 10
   mywindow.focus(); // necessary for IE >= 10*/

   mywindow.print();
   //mywindow.close();

   return true;
}