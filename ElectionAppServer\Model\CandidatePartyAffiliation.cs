﻿using System;
using System.ComponentModel.DataAnnotations;

namespace ElectionAppServer.Model;

public class CandidatePartyAffiliation
{
    public int Id { get; set; }
    public int CandidateId { get; set; }
    public virtual Candidate Candidate { get; set; }
    public int PartyId { get; set; }
    public virtual Party Party { get; set; }
    [StringLength(200)] [Required] public string Position { get; set; }
    public DateTime DateFrom { get; set; }
    public DateTime? DateTo { get; set; }
    public bool TillToday { get; set; }

    #region Audit Log Fields

    public DateTime CreatedDate { get; set; } = DateTime.Now;
    public DateTime? ModifiedDate { get; set; }

    [StringLength(450)] public string CreatedBy { get; set; }

    [StringLength(450)] public string ModifiedBy { get; set; }

    #endregion Audit Log Fields
}