﻿@using ButtonType = MudBlazor.ButtonType
@using FilterType = Syncfusion.Blazor.DropDowns.FilterType
@using Size = MudBlazor.Size
@inherits OwningComponentBase<ElectionStructureService>

@attribute [Authorize(Roles = "Administrators,Data Viewer")]

<EditForm Model="SelectedObj" OnValidSubmit="RaiseSaveData" Context="ef">
@*<FluentValidator TValidator="ElcStructureDTOValidator"></FluentValidator>*@
<FluentValidationValidator/>
<ValidationSummary/>
@if (SelectedObj.StructureType == StructureType.District)
{
    <div class="row mb-2">
        <div class="col mb-1">
            @if (_regionsList != null && _regionsList.Any())
            {
                <SfComboBox TItem="RegionDTO" TValue="int?" @bind-Value="SelectedObj.RegionId" Placeholder="Region"
                            DataSource="@_regionsList" FloatLabelType="FloatLabelType.Always">
                    <ComboBoxFieldSettings Value="Id" Text="EnglishTitle"></ComboBoxFieldSettings>
                </SfComboBox>
                <ValidationMessage For="@(() => SelectedObj.RegionId)"/>
            }
        </div>
        <div class="col mb-1" style="display:none">
            @if (_divisionsList != null && _divisionsList.Any())
            {
                <SfComboBox TItem="AdminDivisionDTO" TValue="int?" @bind-Value="SelectedObj.DivisionId" Placeholder="Division"
                            DataSource="@_divisionsList" FloatLabelType="FloatLabelType.Always">
                    <ComboBoxFieldSettings Value="Id" Text="EnglishTitle"></ComboBoxFieldSettings>
                </SfComboBox>
                <ValidationMessage For="@(() => SelectedObj.DivisionId)"/>
            }
        </div>

    </div>
}

<div class="row">
    @if (SelectedObj.StructureType == StructureType.UnionCouncil ||
         SelectedObj.StructureType == StructureType.Ward ||
         SelectedObj.StructureType == StructureType.NA ||
         SelectedObj.StructureType == StructureType.PA ||
         SelectedObj.StructureType == StructureType.Town ||
         SelectedObj.StructureType == StructureType.District)
    {
        <div class="input-field col-2 mb-1">
            <SfTextBox Placeholder="Code" @bind-Value="@SelectedObj.Code" FloatLabelType="FloatLabelType.Always">
            </SfTextBox>
            <ValidationMessage For="@(() => SelectedObj.Code)"/>
        </div>
    }

    <div class="input-field col mb-1">
        <SfTextBox Placeholder="Title (English)" @bind-Value="@SelectedObj.EnglishTitle" OnBlur="TranslateToUrdu"
                   FloatLabelType="FloatLabelType.Always">
        </SfTextBox>
        <ValidationMessage For="@(() => SelectedObj.EnglishTitle)"/>
    </div>

    <div class="input-field col mb-1">
        <SfTextBox Placeholder="Title (Urdu)" @bind-Value="@SelectedObj.UrduTitle"
                   FloatLabelType="FloatLabelType.Always">
        </SfTextBox>
        <ValidationMessage For="@(() => SelectedObj.UrduTitle)"/>
    </div>
</div>
@if (SelectedObj.StructureType == StructureType.NA ||
     SelectedObj.StructureType == StructureType.PA ||
     SelectedObj.StructureType == StructureType.Town ||
     SelectedObj.StructureType == StructureType.UnionCouncil ||
     SelectedObj.StructureType == StructureType.Ward ||
     SelectedObj.StructureType == StructureType.District)
{
    @if (SelectedObj.StructureType != StructureType.District)
    {
        <div class="tabheader mb-2">Assembly Detail</div>
        <div class="row">
            <div class="col mb-1">
                <SfDropDownList AllowFiltering="true" FilterType="FilterType.Contains"
                                TItem="GeneralItemDTO" TValue="int?" @bind-Value="SelectedObj.AssemblyId" Placeholder="Assembly"
                                DataSource="@_assembliesList" FloatLabelType="FloatLabelType.Always">
                    <DropDownListFieldSettings Value="Id" Text="EnglishTitle"></DropDownListFieldSettings>
                </SfDropDownList>
                <ValidationMessage For="@(() => SelectedObj.AssemblyId)"/>
            </div>
        </div>
    }

    <div class="tabheader mb-2">Voters/Polling Stations</div>

    <div class="row">

        <div class="col-md mb-1">
            <SfNumericTextBox ShowSpinButton="false" Decimals="0" Format="########"
                              @bind-Value="@SelectedObj.MaleVoters" Min="1" FloatLabelType="FloatLabelType.Always"
                              Placeholder="Male Voters">
            </SfNumericTextBox>
        </div>
        <div class="col-md mb-1">
            <SfNumericTextBox ShowSpinButton="false" Decimals="0" Format="########"
                              @bind-Value="@SelectedObj.FemaleVoters" Min="1" FloatLabelType="FloatLabelType.Always"
                              Placeholder="Female Voters">
            </SfNumericTextBox>
        </div>
        <div class="col-md mb-1">
            <SfNumericTextBox ShowSpinButton="false" Decimals="0" Format="########"
                              @bind-Value="@SelectedObj.YouthVoters" Min="1" FloatLabelType="FloatLabelType.Always"
                              Placeholder="Youth Voters">
            </SfNumericTextBox>
        </div>
        <div class="col-md mb-1">
            Total Voters<br/>
            <b>@SelectedObj.TotalVoters</b>
        </div>
    </div>
    <div class="row">
        <div class="col mb-1">
            <SfNumericTextBox ShowSpinButton="false" Decimals="0" Format="########"
                              @bind-Value="@SelectedObj.Population" Min="1" FloatLabelType="FloatLabelType.Always"
                              Placeholder="Poulation">
            </SfNumericTextBox>
        </div>
        <div class="col mb-1">
            <SfNumericTextBox ShowSpinButton="false" Decimals="0" Format="########"
                              @bind-Value="@SelectedObj.TotalPollingStations" Min="1" FloatLabelType="FloatLabelType.Always"
                              Placeholder="Total Polling Stations">
            </SfNumericTextBox>
        </div>
    </div>

    <div class="tabheader mb-2">Demographics</div>
    <div class="row">
        <div class="col mb-1">
            <SfTextBox Multiline="true" @bind-Value="SelectedObj.Trivia" FloatLabelType="FloatLabelType.Always"
                       Placeholder="Trivia (Economy):">
            </SfTextBox>
            <ValidationMessage For="@(() => SelectedObj.Trivia)"/>
        </div>
    </div>
    <div class="row">
        <div class="col mb-1">
            <SfTextBox Multiline="true" @bind-Value="SelectedObj.GeneralTrivia" FloatLabelType="FloatLabelType.Always"
                       Placeholder="Trivia (General)">
            </SfTextBox>
            <ValidationMessage For="@(() => SelectedObj.GeneralTrivia)"/>
        </div>
    </div>
    <div class="row">
        <div class="col mb-1">
            <SfTextBox @bind-Value="SelectedObj.Languages" Placeholder="Language Spoken"
                       FloatLabelType="FloatLabelType.Always">
            </SfTextBox>
            <ValidationMessage For="@(() => SelectedObj.Languages)"></ValidationMessage>
        </div>
        <div class="col mb-1">

            <SfTextBox @bind-Value="SelectedObj.Castes" Placeholder="Castes" FloatLabelType="FloatLabelType.Always">
            </SfTextBox>
            <ValidationMessage For="@(() => SelectedObj.Castes)"></ValidationMessage>
        </div>
    </div>

    <div class="row">
        <div class="col mb-1">
            <SfTextBox @bind-Value="SelectedObj.ImportantPoliticalPersonalities" FloatLabelType="FloatLabelType.Always"
                       Placeholder="Important Political Personalities">
            </SfTextBox>
            <ValidationMessage For="@(() => SelectedObj.ImportantPoliticalPersonalities)"/>
        </div>
    </div>
    <div class="row">
        <div class="col mb-1">
            <SfTextBox Multiline="true" @bind-Value="SelectedObj.Problems" FloatLabelType="FloatLabelType.Always"
                       Placeholder="Problems">
            </SfTextBox>
            <ValidationMessage For="@(() => SelectedObj.Problems)"/>
        </div>
        <div class="col mb-1">
            <SfTextBox Multiline="true" @bind-Value="SelectedObj.ImportantAreas" FloatLabelType="FloatLabelType.Always"
                       Placeholder="Important Cities/Areas">
            </SfTextBox>
        </div>
    </div>
    <div class="row">
        <div class="col mb-1">
            <SfMultiSelect FilterType="FilterType.Contains">

            </SfMultiSelect>  
        </div>
    </div>
    <div class="row">
        <div class="col mb-1">
            <SfNumericTextBox ShowSpinButton="false" @bind-Value="@SelectedObj.UrbanAreaPer"
                              FloatLabelType="FloatLabelType.Always" Placeholder="Urban Area (%)" Min="0" Max="100">
            </SfNumericTextBox>
        </div>
        <div class="col mb-1">
            <SfNumericTextBox ShowSpinButton="false" @bind-Value="@SelectedObj.RuralAreaPer"
                              FloatLabelType="FloatLabelType.Always" Placeholder="Rural Area (%)" Min="0" Max="100">
            </SfNumericTextBox>
        </div>
        <div class="col mb-1">
            <SfTextBox @bind-Value="@SelectedObj.Area" FloatLabelType="FloatLabelType.Always" Placeholder="Total Area">
            </SfTextBox>
            <ValidationMessage For="@(() => SelectedObj.Problems)"/>
        </div>
    </div>
    <div class="row">
        <div class="col mb-1">
            <SfTextBox @bind-Value="@SelectedObj.MajorityIncomeSource" FloatLabelType="FloatLabelType.Always"
                       Placeholder="Majority Income Source">
            </SfTextBox>
            <ValidationMessage For="@(() => SelectedObj.MajorityIncomeSource)"/>
        </div>
        <div class="col mb-1">
            <SfTextBox @bind-Value="@SelectedObj.LiteracyRate" FloatLabelType="FloatLabelType.Always"
                       Placeholder="Literacy Rate">
            </SfTextBox>
            <ValidationMessage For="@(() => SelectedObj.LiteracyRate)"/>
        </div>
        <div class="col mb-1">
            <SfTextBox @bind-Value="@SelectedObj.AverageHouseholdIncome" FloatLabelType="FloatLabelType.Always"
                       Placeholder="Average Household Income">
            </SfTextBox>
            <ValidationMessage For="@(() => SelectedObj.AverageHouseholdIncome)"/>
        </div>
    </div>
    <div class="tabheader mb-2">Previous Constituency/Administrative Unit</div>
    <div class="row">
        <div class="col mb-1">
            <SfGrid DataSource="@SelectedObj.PrevConstituencies" AllowFiltering="true" AllowSorting="true"
                    AllowTextWrap="true">
                <GridEvents QueryCellInfo="CustomizeCell" TValue="PrevConstituencyDTO"></GridEvents>
                <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Excel"></GridFilterSettings>
                <GridColumns>
                    <GridColumn AutoFit="true" Field="@nameof(PrevConstituencyDTO.ParentStructureId)" HeaderText="Id">
                    </GridColumn>
                    <GridColumn AutoFit="true" Field="@nameof(PrevConstituencyDTO.Election)" HeaderText="Election">
                    </GridColumn>
                    <GridColumn AutoFit="true" Field="@nameof(PrevConstituencyDTO.UrduTitle)" HeaderText="Urdu Title"
                                TextAlign="TextAlign.Right">
                    </GridColumn>
                    <GridColumn AutoFit="true" Field="@nameof(PrevConstituencyDTO.EnglishTitle)"
                                HeaderText="Region (English)">
                    </GridColumn>
                    <GridColumn AutoFit="true" Field="@nameof(PrevConstituencyDTO.Description)"
                                HeaderText="Description">
                    </GridColumn>
                </GridColumns>
            </SfGrid>
        </div>
    </div>
}

@if (SelectedObj.StructureType == StructureType.NA ||
     SelectedObj.StructureType == StructureType.PA ||
     SelectedObj.StructureType == StructureType.Town ||
     SelectedObj.StructureType == StructureType.UnionCouncil ||
     SelectedObj.StructureType == StructureType.Ward ||
     SelectedObj.StructureType == StructureType.District)
{
    <div class="row mb-2">
        <div class="col-md mb-1">
            <MudTextField Style="text-align:right;direction:rtl;" Lines="8" @bind-Value="@SelectedObj.Profile" Placeholder="Profile"></MudTextField>
        </div>
    </div>
}

<AuthorizeView Roles="Administrators">

    <div class="row">
        <div class="col mb-1">
            <MudButton ButtonType="ButtonType.Submit" Color="Color.Primary" Size="Size.Small"
                       Variant="Variant.Filled" StartIcon="@Icons.Material.Filled.Save">
                Save
            </MudButton>
        </div>
    </div>
</AuthorizeView>
</EditForm>