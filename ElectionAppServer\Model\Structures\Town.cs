﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;

namespace ElectionAppServer.Model;

public class Town : Structure
{
    public Town()
    {
        UnionCouncils = new List<UnionCouncil>();
    }

    [Column("DistrictId")] public int DistrictId { get; set; }

    public virtual District District { get; set; }
    public virtual List<UnionCouncil> UnionCouncils { get; set; }
}