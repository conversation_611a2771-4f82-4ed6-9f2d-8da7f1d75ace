﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace ElectionAppServer.Migrations.ApplicationDb
{
	public partial class AddPollingStationsToNominationTable : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.AddColumn<int>(
				 name: "ResultPollingStations",
				 table: "Nominations",
				 nullable: true);

			migrationBuilder.AddColumn<int>(
				 name: "TotalPollingStations",
				 table: "Nominations",
				 nullable: true);
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropColumn(
				 name: "ResultPollingStations",
				 table: "Nominations");

			migrationBuilder.DropColumn(
				 name: "TotalPollingStations",
				 table: "Nominations");
		}
	}
}
