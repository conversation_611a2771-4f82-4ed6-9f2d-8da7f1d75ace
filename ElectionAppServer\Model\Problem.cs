
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace ElectionAppServer.Model;

public class Problem
{
    public int Id { get; set; }
    [Required]
    [StringLength(500)]
    public string Title { get; set; }

    public virtual ICollection<StructureProblem> StructureProblems { get; set; }

    #region Audit Log Fields

    public DateTime CreatedDate { get; set; } = DateTime.Now;
    public DateTime? ModifiedDate { get; set; }

    [StringLength(450)]
    public string CreatedBy { get; set; }

    [StringLength(450)]
    public string ModifiedBy { get; set; }

    #endregion
}
