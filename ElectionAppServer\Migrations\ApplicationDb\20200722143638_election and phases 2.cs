﻿using Microsoft.EntityFrameworkCore.Migrations;
using System;

namespace ElectionAppServer.Migrations.ApplicationDb
{
	public partial class electionandphases2 : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.AlterColumn<DateTime>(
				 name: "CreatedDate",
				 table: "ElectionParties",
				 nullable: false,
				 defaultValue: new DateTime(2020, 7, 22, 19, 36, 38, 326, DateTimeKind.Local).AddTicks(8578),
				 oldClrType: typeof(DateTime),
				 oldType: "datetime2",
				 oldDefaultValue: new DateTime(2020, 7, 18, 12, 26, 18, 978, DateTimeKind.Local).AddTicks(455));

			migrationBuilder.AlterColumn<DateTime>(
				 name: "CreatedDate",
				 table: "ElectionCandidates",
				 nullable: false,
				 defaultValue: new DateTime(2020, 7, 22, 19, 36, 38, 332, DateTimeKind.Local).AddTicks(9315),
				 oldClrType: typeof(DateTime),
				 oldType: "datetime2",
				 oldDefaultValue: new DateTime(2020, 7, 18, 12, 26, 18, 984, DateTimeKind.Local).AddTicks(3659));
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.AlterColumn<DateTime>(
				 name: "CreatedDate",
				 table: "ElectionParties",
				 type: "datetime2",
				 nullable: false,
				 defaultValue: new DateTime(2020, 7, 18, 12, 26, 18, 978, DateTimeKind.Local).AddTicks(455),
				 oldClrType: typeof(DateTime),
				 oldDefaultValue: new DateTime(2020, 7, 22, 19, 36, 38, 326, DateTimeKind.Local).AddTicks(8578));

			migrationBuilder.AlterColumn<DateTime>(
				 name: "CreatedDate",
				 table: "ElectionCandidates",
				 type: "datetime2",
				 nullable: false,
				 defaultValue: new DateTime(2020, 7, 18, 12, 26, 18, 984, DateTimeKind.Local).AddTicks(3659),
				 oldClrType: typeof(DateTime),
				 oldDefaultValue: new DateTime(2020, 7, 22, 19, 36, 38, 332, DateTimeKind.Local).AddTicks(9315));
		}
	}
}
