﻿using Microsoft.EntityFrameworkCore.Migrations;
using System;

namespace ElectionAppServer.Migrations.ApplicationDb
{
	public partial class Region : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.CreateTable(
				 name: "Regions",
				 columns: table => new
				 {
					 Id = table.Column<int>(nullable: false)
							.Annotation("SqlServer:Identity", "1, 1"),
					 EnglishTitle = table.Column<string>(unicode: false, maxLength: 200, nullable: false),
					 UrduTitle = table.Column<string>(maxLength: 200, nullable: false),
					 ProvinceId = table.Column<int>(nullable: false),
					 CreatedDate = table.Column<DateTime>(nullable: false),
					 ModifiedDate = table.Column<DateTime>(nullable: true),
					 CreatedBy = table.Column<string>(maxLength: 450, nullable: true),
					 ModifiedBy = table.Column<string>(maxLength: 450, nullable: true)
				 },
				 constraints: table =>
				 {
					 table.PrimaryKey("PK_Regions", x => x.Id);
					 table.ForeignKey(
							  name: "FK_Regions_Structures_ProvinceId",
							  column: x => x.ProvinceId,
							  principalTable: "Structures",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Restrict);
				 });

			migrationBuilder.CreateIndex(
				 name: "IX_Regions_ProvinceId_EnglishTitle",
				 table: "Regions",
				 columns: new[] { "ProvinceId", "EnglishTitle" });

			migrationBuilder.CreateIndex(
				 name: "IX_Regions_ProvinceId_UrduTitle",
				 table: "Regions",
				 columns: new[] { "ProvinceId", "UrduTitle" });
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropTable(
				 name: "Regions");
		}
	}
}
