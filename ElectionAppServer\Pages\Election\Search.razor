﻿@page "/search"
@using FilterType = Syncfusion.Blazor.DropDowns.FilterType
@using ButtonType = MudBlazor.ButtonType
@using Size = MudBlazor.Size
@inherits OwningComponentBase<ConstituencyService>
@attribute [Authorize(Roles = "Administrators,Data Viewer")]

<MudText Class="mb-2" Typo="Typo.h5"><MudIcon Icon="@Icons.Material.Filled.LocationOn" Size="Size.Large"/>Constituencies</MudText>

@*<PageCaption Title="Constituencies"></PageCaption>*@

<SfToast @ref="ToastObj" Title="Alert" ShowProgressBar="true" Timeout="3000">
    <ToastPosition X="Right"></ToastPosition>
</SfToast>

<SfDialog Width="600px" @bind-Visible="@IsPreviousConstDialogOpen" @ref="dlgPreviousConst" IsModal="true" ShowCloseIcon="true">
    <DialogTemplates>
        <Header>Previous Constituency Detail</Header>
        <Content>
            <EditForm Model="@previousConst" OnValidSubmit="@SavePreContData" Context="pcf">
                <FluentValidationValidator/>
                <ValidationSummary></ValidationSummary>
                <div class="row mb-2">
                    <div class="col-md">
                        <SfTextBox Placeholder="Previous Election" @bind-Value="previousConst.Election" FloatLabelType="FloatLabelType.Always" Readonly="true"></SfTextBox>
                    </div>
                </div>
                <div class="row mb-2">
                    <div class="col-md">
                        <SfDropDownList DataSource="allPrevConstuencies" TItem="GeneralItemDTO" TValue="int?" Placeholder="Previous Constituency" FloatLabelType="FloatLabelType.Always" @bind-Value="previousConst.PreviousConstituencyId"
                                        AllowFiltering="true" FilterType="FilterType.Contains">
                            <DropDownListFieldSettings Value="Id" Text="EnglishTitle"></DropDownListFieldSettings>
                        </SfDropDownList>
                        <ValidationMessage For="@(() => previousConst.PreviousConstituencyId)"></ValidationMessage>
                    </div>
                </div>
                <div class="row mb-2">
                    <div class="col-md">
                        <SfTextBox Multiline="true" @bind-Value="previousConst.Description" FloatLabelType="FloatLabelType.Always" Placeholder="Description">
                        </SfTextBox>
                        <ValidationMessage For="@(() => previousConst.Description)"></ValidationMessage>
                    </div>
                </div>
                <div class="row mb-2">
                    <div class="col-md">
                        <MudButton ButtonType="ButtonType.Submit" Color="Color.Primary" Variant="Variant.Filled" Size="Size.Small" StartIcon="@Icons.Material.Filled.Save">Save</MudButton>
                    </div>
                </div>
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>


<SfDialog Width="90%" @bind-Visible="@IsDialogOpen" @ref="dlgForm" IsModal="true" ShowCloseIcon="true" MinHeight="600px">
<DialogTemplates>
<Header>Constituency Detail - @selectedObj.Code - @selectedObj.EnglishTitle </Header>
<Content>
<EditForm Model="@selectedObj" OnValidSubmit="@SaveData" Context="ef">
<FluentValidationValidator/>
<ValidationSummary></ValidationSummary>
<div class="row">

    <div class="input-field col-md-2">
        <SfTextBox Placeholder="Code" @bind-Value="@selectedObj.Code" FloatLabelType="FloatLabelType.Always">
        </SfTextBox>
        <ValidationMessage For="@(() => selectedObj.Code)"/>
    </div>
    <div class="input-field col-md-5">
        <SfTextBox Placeholder="Title (English)" @bind-Value="@selectedObj.EnglishTitle" OnBlur="TranslateToUrdu" FloatLabelType="FloatLabelType.Always"></SfTextBox>
        <ValidationMessage For="@(() => selectedObj.EnglishTitle)"/>
    </div>
    <div class="input-field col-md-5">
        <SfTextBox Placeholder="Title (Urdu)" @bind-Value="@selectedObj.UrduTitle" FloatLabelType="FloatLabelType.Always">
        </SfTextBox>
        <ValidationMessage For="@(() => selectedObj.UrduTitle)"/>
    </div>
</div>
@if (selectedObj.Id != 0)
{
    <div class="row mt-2">
        <div class="col">
            <SfSwitch @bind-Checked="selectedObj.IsPostponed"></SfSwitch>
            <label> Status: @(selectedObj.IsPostponed ? "Postponed" : "Active")</label>
        </div>
        <div class="col">
            <SfSwitch @bind-Checked="selectedObj.HasBilaMokablia"></SfSwitch>
            <label>Bila Muqabala: @(selectedObj.HasBilaMokablia ? "Yes" : "No")</label>
        </div>
    </div>
}


<div class="tabheader mb-2">Voters/Polling Stations </div>

<div class="row mb-2">

    <div class="col-md-3">
        <SfNumericTextBox ShowSpinButton="false" Decimals="0" Format="########" @bind-Value="@selectedObj.MaleVoters" Min="1" FloatLabelType="FloatLabelType.Always" Placeholder="Male Voters"></SfNumericTextBox>
        <ValidationMessage For="@(() => selectedObj.MaleVoters)"/>
    </div>
    <div class="col-md-3">
        <SfNumericTextBox ShowSpinButton="false" Decimals="0" Format="########" @bind-Value="@selectedObj.FemaleVoters" Min="1" FloatLabelType="FloatLabelType.Always" Placeholder="Female Voters"></SfNumericTextBox>
        <ValidationMessage For="@(() => selectedObj.FemaleVoters)"/>
    </div>
    <div class="col-md-3">
        <SfNumericTextBox ShowSpinButton="false" Decimals="0" Format="########" @bind-Value="@selectedObj.YouthVoters" Min="1" FloatLabelType="FloatLabelType.Always" Placeholder="Youth Voters"></SfNumericTextBox>
        <ValidationMessage For="@(() => selectedObj.YouthVoters)"/>
    </div>
    <div class="col-md-3">
        Total Voters<br/>
        <b>@selectedObj.TotalVoters</b>
    </div>
</div>
<div class="row mb-2">
    <div class="col-md-4">
        <SfNumericTextBox ShowSpinButton="false" Decimals="0" Format="########" @bind-Value="@selectedObj.Population" Min="1" FloatLabelType="FloatLabelType.Always" Placeholder="Poulation"></SfNumericTextBox>
        <ValidationMessage For="@(() => selectedObj.Population)"/>
    </div>
    <div class="col-md-4">
        <SfNumericTextBox ShowSpinButton="false" Decimals="0" Format="########" @bind-Value="@selectedObj.TotalPollingStations" Min="1" FloatLabelType="FloatLabelType.Always" Placeholder="Total Polling Stations"></SfNumericTextBox>
        <ValidationMessage For="@(() => selectedObj.TotalPollingStations)"/>
        <AuthorizeView Roles="Administrators">


            <MudButton ButtonType="ButtonType.Button"
                       Color="Color.Info" Variant="Variant.Filled" Size="Size.Small"
                       StartIcon="@Icons.Material.Filled.GeneratingTokens"
                       @onclick="GeneratePSs">
                Generate Polling Stations
            </MudButton>
        </AuthorizeView>
    </div>
    @if (selectedObj.Id == 0)
    {
        <div class="col-md-4">
            <SfNumericTextBox ShowSpinButton="false" Min="0" Max="200" Format="###" Decimals="0" Placeholder="Total Wards" @bind-Value="@selectedObj.TotalWards" FloatLabelType="FloatLabelType.Always">
            </SfNumericTextBox>
        </div>
    }
</div>

<div class="tabheader mb-2">Voters/Polling Stations (PS)</div>
<div class="row mb-2">
    <div class="col-md">Male Voters: <b>@selectedObj.PSMaleVoters</b></div>
    <div class="col-md">Female Voters: <b>@selectedObj.PSFemaleVoters</b></div>
    <div class="col-md">Total Voters: <b>@(selectedObj.PSMaleVoters + selectedObj.PSFemaleVoters)</b></div>
    <div class="col-md">Total Polling Stations: <b>@selectedObj.PSCount</b></div>
</div>

<div class="tabheader mb-2">Demographics</div>
<div class="row">
    <div class="col">
        <SfTextBox Multiline="true" @bind-Value="selectedObj.Trivia" FloatLabelType="FloatLabelType.Always" Placeholder="Trivia (Economy)"></SfTextBox>
        <ValidationMessage For="@(() => selectedObj.Trivia)"/>
    </div>
</div>
<div class="row">
    <div class="col">
        <SfTextBox Multiline="true" @bind-Value="selectedObj.GeneralTrivia" FloatLabelType="FloatLabelType.Always" Placeholder="Trivia (General)"></SfTextBox>
        <ValidationMessage For="@(() => selectedObj.GeneralTrivia)"/>
    </div>
</div>
<div class="row">
    <div class="col-md">
        <SfTextBox Multiline="true" @bind-Value="selectedObj.Profile" FloatLabelType="FloatLabelType.Always" Placeholder="Profile"></SfTextBox>
    </div>
</div>
<div class="row">
    <div class="col-md-6">
        <SfTextBox @bind-Value="selectedObj.Languages" Placeholder="Languages Spoken" FloatLabelType="FloatLabelType.Always"></SfTextBox>
        <ValidationMessage For="@(() => selectedObj.Languages)"></ValidationMessage>
    </div>
    <div class="col-md-6">
        <SfTextBox @bind-Value="selectedObj.Castes" Placeholder="Castes" FloatLabelType="FloatLabelType.Always"></SfTextBox>
        <ValidationMessage For="@(() => selectedObj.Castes)"></ValidationMessage>
    </div>
</div>

<div class="row">
    <div class="col">
        <SfTextBox @bind-Value="selectedObj.ImportantPoliticalPersonalities" FloatLabelType="FloatLabelType.Always" Placeholder="Important Political Personalities"></SfTextBox>
        <ValidationMessage For="@(() => selectedObj.ImportantPoliticalPersonalities)"/>
    </div>
</div>
<div class="row">
    <div class="col-md-6">
        <SfTextBox Multiline="true" @bind-Value="selectedObj.Problems" FloatLabelType="FloatLabelType.Always" Placeholder="Problems"></SfTextBox>
        <ValidationMessage For="@(() => selectedObj.Problems)"/>
    </div>
    <div class="col-md-6">
        <SfTextBox Multiline="true" @bind-Value="selectedObj.ImportantAreas" FloatLabelType="FloatLabelType.Always" Placeholder="Important Cities/Areas"></SfTextBox>
    </div>
</div>

<div class="row">
    <div class="col-md-4">
        @*<SfTextBox @bind-Value="selectedObj.UrbanAreaPer" FloatLabelType="FloatLabelType.Always" Placeholder="Urban Area (%)"></SfTextBox>*@
        <SfNumericTextBox ShowSpinButton="false" @bind-Value="@selectedObj.UrbanAreaPer" FloatLabelType="FloatLabelType.Always" Placeholder="Urban Area (%)" Min="0" Max="100"></SfNumericTextBox>
    </div>
    <div class="col-md-4">
        @*<SfTextBox @bind-Value="selectedObj.RuralAreaPer" FloatLabelType="FloatLabelType.Always" Placeholder="Rural Area (%)"></SfTextBox>*@
        <SfNumericTextBox ShowSpinButton="false" @bind-Value="@selectedObj.RuralAreaPer" FloatLabelType="FloatLabelType.Always" Placeholder="Rural Area (%)" Min="0" Max="100"></SfNumericTextBox>
    </div>
    <div class="col-md-4">
        <SfTextBox @bind-Value="@selectedObj.Area" FloatLabelType="FloatLabelType.Always" Placeholder="Total Area"></SfTextBox>
        <ValidationMessage For="@(() => selectedObj.Problems)"/>
    </div>
</div>
<div class="row">
    <div class="col-md-4">
        <SfTextBox @bind-Value="@selectedObj.MajorityIncomeSource" FloatLabelType="FloatLabelType.Always" Placeholder="Majority Income Source"></SfTextBox>
        <ValidationMessage For="@(() => selectedObj.MajorityIncomeSource)"/>
    </div>
    <div class="col-md-4">
        <SfTextBox @bind-Value="@selectedObj.LiteracyRate" FloatLabelType="FloatLabelType.Always" Placeholder="Literacy Rate"></SfTextBox>
        <ValidationMessage For="@(() => selectedObj.LiteracyRate)"/>
    </div>
    <div class="col-md-4">
        <SfTextBox @bind-Value="@selectedObj.AverageHouseholdIncome" FloatLabelType="FloatLabelType.Always" Placeholder="Average Household Income"></SfTextBox>
        <ValidationMessage For="@(() => selectedObj.AverageHouseholdIncome)"/>
    </div>
</div>

<AuthorizeView Roles="Administrators">
    <div class="row">
        <div class="col">
            <MudButton ButtonType="ButtonType.Submit" Size="Size.Small" Variant="Variant.Filled" Color="Color.Primary" StartIcon="@Icons.Material.Filled.Save" Class="mb-2">Update</MudButton>
        </div>
    </div>
</AuthorizeView>
@if (allPrevConstuencies != null && allPrevConstuencies.Any())
{
    <div class="tabheader mb-2">Previous Constituency/Administrative Unit</div>
    <div class="row">
        <div class="col">
            <AuthorizeView Roles="Administrators">
                <MudButton ButtonType="ButtonType.Button" Class="mb-2"
                           Size="Size.Small" Variant="Variant.Filled" Color="Color.Primary"
                           StartIcon="@Icons.Material.Filled.Add"
                           OnClick="OpenAddPreviousConstituencyForm">
                    Add New Mapping
                </MudButton>
            </AuthorizeView>
            <SfGrid @ref="dlgPrevConGrid" DataSource="@selectedObj.PrevConstituencies" AllowFiltering="true" AllowSorting="true" AllowTextWrap="true" Width="100%">
                <GridEvents QueryCellInfo="CustomizeCell" TValue="PrevConstituencyDTO"></GridEvents>
                <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Excel"></GridFilterSettings>
                <GridColumns>
                    <GridColumn AutoFit="true" Field="@nameof(PrevConstituencyDTO.Election)" HeaderText="Election"></GridColumn>
                    <GridColumn AutoFit="true" Field="@nameof(PrevConstituencyDTO.ParentStructureId)" HeaderText="Previous Id"></GridColumn>
                    <GridColumn AutoFit="true" Field="@nameof(PrevConstituencyDTO.UrduTitle)" HeaderText="Urdu Title" TextAlign="TextAlign.Right"></GridColumn>
                    <GridColumn AutoFit="true" Field="@nameof(PrevConstituencyDTO.EnglishTitle)" HeaderText="Region (English)"></GridColumn>
                    <GridColumn AutoFit="true" Field="@nameof(PrevConstituencyDTO.Description)" HeaderText="Description"></GridColumn>
                    <GridColumn Width="150px">
                        <Template Context="kk">
                            @{
                                var obj = kk as PrevConstituencyDTO;
                                <AuthorizeView Roles="Administrators">

                                    <MudFab Size="Size.Small" Color="Color.Primary" ButtonType="ButtonType.Button"
                                            StartIcon="@Icons.Material.Filled.Edit"
                                            OnClick="() => OpenEditPreviousConstituencyForm((int)obj.PreviousConstituencyId, obj.Description)">
                                    </MudFab>
                                    <MudFab Size="Size.Small" ButtonType="ButtonType.Button"
                                            Color="Color.Error" StartIcon="@Icons.Material.Filled.Delete"
                                            OnClick="() => DeletePreviousConstituency((int)obj.PreviousConstituencyId)">
                                    </MudFab>
                                </AuthorizeView>
                            }
                        </Template>
                    </GridColumn>
                </GridColumns>
            </SfGrid>
        </div>
    </div>
}


<br/>
<div class="tabheader mb-2">Namanigars</div>
<div class="row">
    <div class="col">
        <AuthorizeView Roles="Administrators">
            <MudButton ButtonType="ButtonType.Button" Class="mb-1"
                       Size="Size.Small" Color="Color.Primary"
                       StartIcon="@Icons.Material.Filled.PersonAdd"
                       OnClick="OpenCreateNamaniagrForm">
                Add Namanigar
            </MudButton>
        </AuthorizeView>
        <SfGrid DataSource="selectedObj.Namanigars" AllowFiltering="true" AllowSorting="true" AllowTextWrap="true" Width="100%">
            <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Excel"></GridFilterSettings>
            <GridColumns>
                <GridColumn AutoFit="true" Field="@nameof(NamanigarDTO.Code)" HeaderText="Code"></GridColumn>
                <GridColumn AutoFit="true" Field="@nameof(NamanigarDTO.Name)" HeaderText="Name"></GridColumn>
                <GridColumn AutoFit="true" Field="@nameof(NamanigarDTO.District)" HeaderText="District"></GridColumn>
                <GridColumn AutoFit="true" Field="@nameof(NamanigarDTO.Phone)" HeaderText="Phone"></GridColumn>
                <GridColumn AutoFit="true" Field="@nameof(NamanigarDTO.Email)" HeaderText="Email"></GridColumn>
                <GridColumn AutoFit="true" Field="@nameof(NamanigarDTO.PSDetail)" HeaderText="Polling Stations"></GridColumn>
                <GridColumn HeaderText="Actions" Width="110px">
                    <Template Context="dd">
                        @{
                            var k = dd as NamanigarDTO;
                            <AuthorizeView Roles="Administrators">
                                <MudFab ButtonType="ButtonType.Button"
                                        Color="Color.Primary" StartIcon="@Icons.Material.Filled.Edit"
                                        Size="Size.Small"
                                        OnClick="@(() => OpenEditNamaniagrForm(k))">
                                </MudFab>
                                <MudFab ButtonType="ButtonType.Button"
                                        Size="Size.Small" Color="Color.Error"
                                        StartIcon="@Icons.Material.Filled.Delete"
                                        Class="e-danger" OnClick="@(() => DeleteNamanigarForm(k))">
                                </MudFab>
                            </AuthorizeView>
                        }
                    </Template>
                </GridColumn>
            </GridColumns>
        </SfGrid>
    </div>
</div>

</EditForm>
</Content>
</DialogTemplates>
</SfDialog>


<SfDialog Width="80%" @bind-Visible="@isDlgNamaniagrVisible" @ref="dlgNamanigarForm" IsModal="true" ShowCloseIcon="true">
    <DialogTemplates>
        <Header>Namanigar Detail</Header>
        <Content>
            <EditForm Model="selectedNamanigar" OnValidSubmit="AddNamanigarToConstituency" Context="ef">
                <DataAnnotationsValidator></DataAnnotationsValidator>
                <ValidationSummary/>
                <div class="row">
                    <div class="col">
                        <SfDropDownList DataSource="allNamanigars" TItem="NamanigarDTO" TValue="int?" Placeholder="Namaigar"
                                        FloatLabelType="FloatLabelType.Always" @bind-Value="selectedNamanigar.Id"
                                        AllowFiltering="true"
                                        FilterType="FilterType.Contains">
                            <DropDownListFieldSettings Value="Id" Text="Name"></DropDownListFieldSettings>
                            <DropDownListEvents TValue="int?" TItem="NamanigarDTO" ValueChange="GetNamanigarInfo"></DropDownListEvents>
                        </SfDropDownList>
                        <ValidationMessage For="@(() => selectedNamanigar.Id)"/>
                    </div>
                </div>
                <div class="row">
                    <div class="col">
                        <SfTextBox Readonly="true" @bind-Value="selectedNamanigar.Phone" Placeholder="Phone" FloatLabelType="FloatLabelType.Always"></SfTextBox>
                        <ValidationMessage For="@(() => selectedNamanigar.Phone)"/>
                    </div>
                    <div class="col">
                        <SfTextBox Readonly="true" @bind-Value="selectedNamanigar.Email" Placeholder="Email" FloatLabelType="FloatLabelType.Always"></SfTextBox>
                        <ValidationMessage For="@(() => selectedNamanigar.Email)"/>
                    </div>
                </div>
                <div class="row">
                    <div class="col">
                        <SfTextBox Readonly="true" @bind-Value="selectedNamanigar.District" Placeholder="District" FloatLabelType="FloatLabelType.Always"></SfTextBox>
                    </div>
                </div>
                <div class="row">
                    <div class="col">
                        <SfTextBox @bind-Value="selectedNamanigar.PSDetail" Placeholder="Polling Stations Detail" FloatLabelType="FloatLabelType.Always"></SfTextBox>
                        <ValidationMessage For="@(() => selectedNamanigar.PSDetail)"/>
                    </div>
                </div>
                <AuthorizeView Roles="Administrators">
                    <div class="row">
                        <div class="col">
                            <MudButton ButtonType="ButtonType.Submit" Color="Color.Primary" Size="Size.Small" Variant="Variant.Filled"
                                       StartIcon="@Icons.Material.Filled.Save">
                                Save
                            </MudButton>
                        </div>
                    </div>
                </AuthorizeView>
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>


<section>

    <div class="row">

        <div class="col-md-4">
            @if (assemblies_list == null)
            {
                <span>wait...</span>
            }
            else if (!assemblies_list.Any())
            {
                <span>No assembly found...</span>
            }
            else
            {
                <SfDropDownList TValue="int?" TItem="GeneralItemDTO" @bind-Value="electionAssemblyId" Placeholder="Select Assembly"
                                DataSource="@assemblies_list" FloatLabelType="FloatLabelType.Always"
                                AllowFiltering="true" FilterType="FilterType.Contains" ShowClearButton="true">
                    <DropDownListFieldSettings Value="Id" Text="EnglishTitle"></DropDownListFieldSettings>

                </SfDropDownList>
            }
        </div>
        <div class="col-md-4">
            @if (provinces_list == null)
            {
                <span>wait...</span>
            }
            else if (!provinces_list.Any())
            {
                <span>No province found...</span>
            }
            else
            {
                <SfDropDownList TValue="int?" TItem="GeneralItemDTO" @bind-Value="provinceId" Placeholder="Select Province" DataSource="@provinces_list" FloatLabelType="FloatLabelType.Always"
                                AllowFiltering="true" FilterType="FilterType.Contains" ShowClearButton="true">
                    <DropDownListFieldSettings Value="Id" Text="EnglishTitle"></DropDownListFieldSettings>
                    <DropDownListEvents TItem="GeneralItemDTO" TValue="int?" ValueChange="@FilterByProvince"></DropDownListEvents>
                </SfDropDownList>
            }

        </div>


        <div class="col-md-4">
            <SfDropDownList TValue="int?" TItem="GeneralItemDTO" @bind-Value="districtId" Placeholder="Select District" DataSource="@districts_list" FloatLabelType="FloatLabelType.Always"
                            AllowFiltering="true" FilterType="FilterType.Contains" ShowClearButton="true">
                <DropDownListFieldSettings Value="Id" Text="EnglishTitle"></DropDownListFieldSettings>
                <DropDownListEvents TItem="GeneralItemDTO" TValue="int?" ValueChange="@FilterByDistrict"></DropDownListEvents>
            </SfDropDownList>
        </div>
    </div>
    @*<div class="row">

            @if (electionType == ElectionType.LocalBody)
            {
                <div class="col-md-6">
                    <SfDropDownList TValue="int?" TItem="GeneralItemDTO" @bind-Value="townId" Placeholder="Select Town/Tehsil/LG Tier 1" DataSource="@towns_list" FloatLabelType="FloatLabelType.Always"
                                    AllowFiltering="true" FilterType="FilterType.Contains" ShowClearButton="true">
                        <DropDownListFieldSettings Value="Id" Text="EnglishTitle"></DropDownListFieldSettings>
                        <DropDownListEvents TItem="GeneralItemDTO" TValue="int?" ValueChange="@FilterByTown"></DropDownListEvents>
                    </SfDropDownList>
                </div>
                <div class="col-md-6">
                    <SfDropDownList TValue="int?" TItem="GeneralItemDTO" @bind-Value="unionCouncilId" Placeholder="Select LG Tier 2" DataSource="@unionCouncils_list" FloatLabelType="FloatLabelType.Always"
                                    AllowFiltering="true" FilterType="FilterType.Contains" ShowClearButton="true">
                        <DropDownListFieldSettings Value="Id" Text="EnglishTitle"></DropDownListFieldSettings>
                        <DropDownListEvents TItem="GeneralItemDTO" TValue="int?" ValueChange="@FilterByUC"></DropDownListEvents>
                    </SfDropDownList>

                </div>
            }

        </div>*@
    <div class="row">
        <div class="col">
            <div style="display:flex;align-items:baseline; grid-gap:4px;">
                <MudButton Size="Size.Small" Variant="Variant.Filled" Color="Color.Info"
                           StartIcon="@Icons.Material.Filled.PersonSearch"
                           OnClick="SearchData">
                    Search
                </MudButton>
                @if (search_result != null && search_result.Any())
                {
                    <p>Total Constituencies: <b>@search_result.Count</b></p>
                }
            </div>
        </div>
    </div>


    @if (search_result != null && search_result.Any())
    {
        <div class="row">
            <SfGrid AllowFiltering="true" DataSource="search_result" @ref="Grid" AllowTextWrap="true"
                    EnableVirtualization="true"
                    AllowSorting="true" AllowExcelExport="true" AllowPdfExport="true" Width="100%" Height="calc(100vh - 275px)">
                <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Excel"></GridFilterSettings>
                @*<GridEvents QueryCellInfo="CustomizeCell" TValue="ConstituencyDTO"></GridEvents>*@
                <GridColumns>
                    <GridColumn AutoFit="true" Field="@nameof(ConstituencyDTO.Assembly)" HeaderText="Assembly" DisableHtmlEncode="false"></GridColumn>
                    <GridColumn AutoFit="true" Field="@nameof(ConstituencyDTO.Code)" HeaderText="Code" AllowFiltering="true" AllowSorting="true"></GridColumn>
                    <GridColumn AutoFit="true" Field="@nameof(ConstituencyDTO.EnglishTitle)" HeaderText="Constituency" DisableHtmlEncode="false"></GridColumn>
                    <GridColumn AutoFit="true" Field="Type" HeaderText="Type"></GridColumn>
                    <GridColumn AutoFit="true" Field="@nameof(ConstituencyDTO.Province)" HeaderText="Province" DisableHtmlEncode="false"></GridColumn>
                    <GridColumn AutoFit="true" Visible="state.state.ElectionType == ElectionType.LocalBody" Field="@nameof(ConstituencyDTO.Division)" HeaderText="Division" DisableHtmlEncode="false"></GridColumn>
                    <GridColumn AutoFit="true" Field="@nameof(ConstituencyDTO.District)" HeaderText="District" DisableHtmlEncode="false"></GridColumn>
                    <GridColumn AutoFit="true" Field="@nameof(ConstituencyDTO.Region)" HeaderText="Region" DisableHtmlEncode="false"></GridColumn>
                    @*<GridColumn AutoFit="true" Visible="electionType == ElectionType.LocalBody" Field="@nameof(ConstituencyDTO.Town)" HeaderText="Town" DisableHtmlEncode="false"></GridColumn>*@
                    @*<GridColumn AutoFit="true" Visible="electionType == ElectionType.LocalBody" Field="@nameof(ConstituencyDTO.UC)" HeaderText="UC" DisableHtmlEncode="false"></GridColumn>*@
                    <GridColumn HeaderText="Action" AutoFit="true">
                        <Template Context="ss">
                            @{
                                var k = ss as ConstituencyDTO;
                            <div class="">
                                <a target="@($"cc{k.Id}")" href="report/constituency/@k.Id">
                                    <span class="e-control e-btn e-lib e-success mb-1">
                                        <i class="far fa-clipboard"></i>
                                    </span>
                                </a>
                                <AuthorizeView Roles="Administrators">

                                    <MudFab Color="Color.Info" Size="Size.Small" StartIcon="@Icons.Material.Filled.Edit" OnClick="@(() => OpenEditForm(k.Id, k.AssemblyId))"></MudFab>
                                </AuthorizeView>
                                @*<MudFab Color="Color.Success" Size="MudBlazor.Size.Small" Icon="@Icons.Material.Filled.People" Title="Nominations" OnClick="@(() => OpenNominationForm(k.Id, k.AssemblyId))"></MudFab>*@
                                @if (electionType == ElectionType.General)
                                {
                                    <a style="padding-left:2px;" href="/pollingstations/@k.Id">
                                        <span class="e-info e-btn e-lib e-info mb-1">PS</span>
                                    </a>
                                }
                            </div>}
                        </Template>
                    </GridColumn>
                </GridColumns>
            </SfGrid>
        </div>
    }
</section>