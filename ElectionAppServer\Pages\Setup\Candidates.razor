﻿@page "/candidates"

@using ButtonType = MudBlazor.ButtonType
@using FilterType = Syncfusion.Blazor.DropDowns.FilterType
@using Size = MudBlazor.Size
@inherits OwningComponentBase<CandidateService>
@attribute [Authorize(Roles = "Administrators,Data Viewer")]

<SfToast @ref="_toastObj" Title="Alert" ShowProgressBar="true" Timeout="3000">
    <ToastPosition X="Right" Y="Bottom"></ToastPosition>
</SfToast>
<SfDialog Width="950px" @bind-Visible="@_isDlgVisible" @ref="_dlgForm" IsModal="true" ShowCloseIcon="true">
    <DialogEvents OnOpen="beforeOpen"></DialogEvents>
    <DialogTemplates>
        <Header>@(_selectedObj.CandidateType == "1" ? "Candidate" : _selectedObj.CandidateType == "2" ? "Panel" : "") Detail</Header>
        <Content>
            <EditForm Model="@_selectedObj" OnValidSubmit="@SaveCandidateData" Context="ef">
                <DataAnnotationsValidator/>
                @if (ElectionType == ElectionType.LocalBody)
                {
                    <div class="row mb-2">
                        <div class="col-6">
                            <SfDropDownList Enabled="@(_selectedObj.Id == 0)" TItem="GeneralItemDTO" TValue="string"
                                            @bind-Value="_selectedObj.CandidateType" Placeholder="Type"
                                            DataSource="@CandidateTypesList" FloatLabelType="FloatLabelType.Always"
                                            FilterType="FilterType.Contains">
                                <DropDownListFieldSettings Value="Id" Text="EnglishTitle"></DropDownListFieldSettings>
                            </SfDropDownList>
                            <ValidationMessage For="@(() => _selectedObj.CandidateType)"/>
                        </div>
                    </div>
                }
                <div class="candidateinfo">
                    <img alt="" src="data:image/jpg;base64,@FileBase64String"/>
                    <div class="fileselector">
                        <AuthorizeView Roles="Administrators">
                            <BlazorInputFile.InputFile OnChange="HandleFileSelected" accept="image/jpeg,image/jpg"/>
                        </AuthorizeView>
                        @if (_selectedObj.Id != 0)
                        {
                            <AuthorizeView Roles="Administrators">
                                <MudButton ButtonType="ButtonType.Button"
                                           Size="Size.Small" Variant="Variant.Filled" Color="Color.Warning"
                                           StartIcon="@Icons.Material.Filled.RemoveCircleOutline"
                                           OnClick="RemovePic">
                                    Remove Pic
                                </MudButton>
                            </AuthorizeView>
                        }
                    </div>
                </div>
                <div class="row mb-2">
                    <div class="col-md" style="display:flex;gap:5px;align-items:center">

                        <div style="flex:1">
                            <SfTextBox @bind-Value="_selectedObj.EnglishName" Placeholder="Name" FloatLabelType="FloatLabelType.Always" OnBlur="TranslateToUrdu"></SfTextBox>
                            <ValidationMessage For="@(() => _selectedObj.EnglishName)"/>
                        </div>
                        <MudButton OnClick="TranslateToUrdu2" ButtonType="ButtonType.Button" Color="Color.Success" Size="Size.Small" Variant="Variant.Filled">ار</MudButton>
                    </div>
                    <div class="col-md" style="display:flex;gap:5px;align-items:center">
                        <div style="flex:1">
                            <SfTextBox @bind-Value="_selectedObj.UrduName" Placeholder="Name (Urdu)" FloatLabelType="FloatLabelType.Always"></SfTextBox>
                            <ValidationMessage For="@(() => _selectedObj.UrduName)"/>
                        </div>
                        <MudButton OnClick="TranslateToEnglish2" ButtonType="ButtonType.Button" Color="Color.Success" Size="Size.Small" Variant="Variant.Filled">En</MudButton>
                    </div>
                    <div class="col-md-2">
                        <label>Big Name:</label><br/>
                        <SfSwitch @bind-Checked="_selectedObj.IsBigName"></SfSwitch> <span>@_selectedObj.BigName</span>
                    </div>
                </div>
                @if (_selectedObj.CandidateType == "1")
                {
                    <div class="row mb-2">
                        <div class="col">
                            <SfTextBox @bind-Value="_selectedObj.EnglishFatherName" Placeholder="Father Name" FloatLabelType="FloatLabelType.Always" OnBlur="TranslateToUrdu_father"></SfTextBox>
                            <ValidationMessage For="@(() => _selectedObj.EnglishFatherName)"/>
                        </div>
                        <div class="col">
                            <SfTextBox @bind-Value="_selectedObj.UrduFatherName" Placeholder="Father Name (Urdu)" FloatLabelType="FloatLabelType.Always"></SfTextBox>
                            <ValidationMessage For="@(() => _selectedObj.UrduFatherName)"/>
                        </div>
                    </div>
                    <div class="row mb-2">
                        <div class="col-2">
                            <SfDropDownList TItem="GeneralItemDTO" TValue="int?" @bind-Value="_selectedObj.IsFresh"
                                            Placeholder="Experience" DataSource="@fresh_exp_list"
                                            FloatLabelType="FloatLabelType.Always">
                                <DropDownListFieldSettings Value="Id" Text="EnglishTitle"></DropDownListFieldSettings>
                            </SfDropDownList>
                        </div>
                        <div class="col-2">
                            <SfDropDownList TItem="GenderCls" TValue="int?" @bind-Value="_selectedObj.Gender" Placeholder="Select Gender" DataSource="@gender_list" FloatLabelType="FloatLabelType.Always">
                                <DropDownListFieldSettings Value="ID" Text="Text"></DropDownListFieldSettings>
                            </SfDropDownList>
                        </div>
                        <div class="col-2">
                            <label>Real DOB</label>
                            <SfSwitch @bind-Checked="_selectedObj.HasRealDOB"></SfSwitch>
                        </div>

                        <div class="col" style="display:flex; grid-gap:8px;align-items:center">
                            @*<SfDatePicker Format="d MMM, yyyy" ShowClearButton="true" @bind-Value="selectedObj.DateOfBirth" Placeholder="Date Of Birth" FloatLabelType="FloatLabelType.Always"></SfDatePicker>*@
                            @if (_selectedObj.HasRealDOB)
                            {
                                <SfNumericTextBox Format="#0" Decimals="0" ShowClearButton="true" @bind-Value="_selectedObj.Day" Placeholder="DOB - Day" FloatLabelType="FloatLabelType.Always" Min="0" Max="31" ShowSpinButton="false"/>
                                <SfNumericTextBox Format="#0" Decimals="0" ShowClearButton="true" @bind-Value="_selectedObj.Month" Placeholder="Month" FloatLabelType="FloatLabelType.Always" Min="0" Max="12" ShowSpinButton="false"/>
                            }
                            <SfNumericTextBox Format="0000" Decimals="0" ShowClearButton="true" @bind-Value="_selectedObj.Year" Placeholder="Year" FloatLabelType="FloatLabelType.Always" Min="1930" Max="2300" ShowSpinButton="false"/>
                        </div>
                        @if (_selectedObj.HasRealDOB == false)
                        {
                            <div class="col" style="display:flex; grid-gap:8px; align-items:center">
                                <SfNumericTextBox Format="##0" Decimals="0" ShowClearButton="true" ShowSpinButton="false" @bind-Value="_selectedObj.Age" Placeholder="Age" FloatLabelType="FloatLabelType.Always" Min="18" Max="120"/>
                                <MudButton ButtonType="ButtonType.Button" OnClick="@(() => CalculateDOB())" Style="width:170px" Size="Size.Small" Color="Color.Info" Variant="Variant.Filled" StartIcon="@Icons.Material.Filled.Calculate">Calculate</MudButton>
                            </div>
                        }
                    </div>
                    <div class="row mb-2">
                        <div class="col">
                            @*<SfTextBox @bind-Value="selectedObj.District" Placeholder="District" FloatLabelType="FloatLabelType.Always"></SfTextBox>
                                <ValidationMessage For="@(() => selectedObj.District)"></ValidationMessage>*@
                            <SfComboBox DataSource="_districtsList" Placeholder="District" FloatLabelType="FloatLabelType.Always" @bind-Value="_selectedObj.DistrictId">
                                <ComboBoxFieldSettings Value="Id" Text="EnglishTitle"></ComboBoxFieldSettings>
                            </SfComboBox>
                        </div>
                    </div>
                }
                <div class="row mb-2">
                    <div class="col">
                        <SfTextBox @bind-Value="_selectedObj.TotalAssets" Placeholder="Total Assets" FloatLabelType="FloatLabelType.Always"></SfTextBox>
                        <ValidationMessage For="@(() => _selectedObj.TotalAssets)"/>
                    </div>
                    <div class="col">
                        <SfTextBox @bind-Value="_selectedObj.ContactNo" Placeholder="Contact Number" FloatLabelType="FloatLabelType.Always"></SfTextBox>
                        <ValidationMessage For="@(() => _selectedObj.ContactNo)"/>
                    </div>
                </div>
                <div class="row mb-2">
                    <div class="col">
                        <SfTextBox Multiline="true" Width="100%" @bind-Value="_selectedObj.Trivia" Placeholder="Trivia" FloatLabelType="FloatLabelType.Always"></SfTextBox>
                    </div>
                </div>
                @if (_selectedObj.Id != 0)
                {
                    <div class="tabheader mb-2">Election Wise Districts</div>
                    <SfGrid DataSource="_selectedObj.ElectionWiseDistricts" AllowTextWrap="true" AllowSorting="true" Width="100%">
                        <GridColumns>
                            <GridColumn AutoFit="true" HeaderText="Election" Field="@nameof(CandidateElectionWiseDistrictDTO.Election)"></GridColumn>
                            <GridColumn AutoFit="true" HeaderText="Province" Field="@nameof(CandidateElectionWiseDistrictDTO.Province)"></GridColumn>
                            <GridColumn AutoFit="true" HeaderText="District" Field="@nameof(CandidateElectionWiseDistrictDTO.District)"></GridColumn>
                        </GridColumns>
                    </SfGrid>
                    <div class="tabheader mb-2">Election History</div>
                    <SfGrid @ref="GridHistory" DataSource="_selectedObj.CandidateElections" AllowTextWrap="true" TValue="CandidateElection" Width="100%">
                        <GridTextWrapSettings WrapMode="WrapMode.Content"></GridTextWrapSettings>
                        <GridColumns>
                            <GridColumn AutoFit="true" HeaderText="Election" Field="Election"></GridColumn>
                            <GridColumn AutoFit="true" HeaderText="Constituency" Field="Constituency"></GridColumn>
                            <GridColumn AutoFit="true" HeaderText="Party" Field="Party"></GridColumn>
                            <GridColumn AutoFit="true" HeaderText="Symbol" Field="Symbol"></GridColumn>
                            <GridColumn AutoFit="true" HeaderText="Seat Type" Field="SeatType"></GridColumn>
                            <GridColumn AutoFit="true" HeaderText="Votes" Field="Votes"></GridColumn>
                            <GridColumn AutoFit="true" HeaderText="Status" Field="Status"></GridColumn>
                        </GridColumns>
                    </SfGrid>
                }
                @*<div class="tabheader mb-2">Party Affiliations</div>
                    <div class="mb-2">
                        <SfGrid @ref="GridPA" DataSource="@selectedObj.PartyAffiliations" TValue="PartyAffiliationDTO" Toolbar="@(new List<string>() { "Add", "Edit", "Delete", "Cancel", "Update" })" AllowTextWrap="true" Width="100%">
                            <GridEvents OnActionComplete="ActionCompletedHandler" TValue="PartyAffiliationDTO"></GridEvents>
                            <GridEditSettings NewRowPosition="Syncfusion.Blazor.Grids.NewRowPosition.Bottom" AllowAdding="true" AllowEditing="true" AllowDeleting="true" ShowConfirmDialog="true"></GridEditSettings>
                            <GridColumns>
                                <GridColumn AutoFit="true" MinWidth="200px" Field=@nameof(PartyAffiliationDTO.UI_Id) IsPrimaryKey="true" HeaderText="Id" Visible="false"></GridColumn>
                                <GridColumn AutoFit="true" MinWidth="200px" Field=@nameof(PartyAffiliationDTO.PartyId) EditType="EditType.DropDownEdit" ForeignKeyField="Id" ForeignKeyValue="ShortEnglishTitle" DataSource="@parties_list" HeaderText="Party"></GridColumn>
                                <GridColumn AutoFit="true" MinWidth="200px" Field=@nameof(PartyAffiliationDTO.Position) HeaderText="Position"></GridColumn>
                                <GridColumn AutoFit="true" MinWidth="200px" Field=@nameof(PartyAffiliationDTO.DateFrom) Format="d MMM, yyyy" EditType="EditType.DatePickerEdit" HeaderText="Date From" CustomFormat="@( new { type = "date", format = "d MMM, yyyy" })"></GridColumn>
                                <GridColumn AutoFit="true" MinWidth="200px" Field=@nameof(PartyAffiliationDTO.DateTo) Format="d MMM, yyyy" EditType="EditType.DatePickerEdit" HeaderText="DateTo" CustomFormat="@( new { type = "date", format = "d MMM, yyyy" })"></GridColumn>
                            </GridColumns>
                        </SfGrid>
                    </div>*@
                @if (_selectedObj.CandidateType == "1")
                {
                    @*<div class="tabheader">Political Careers</div>
                        <div class="mb-2">
                            <SfGrid @ref="GridPC" DataSource="@selectedObj.PoliticalCareers" TValue="PoliticalCareerDTO" Toolbar="@(new List<string>() { "Add", "Edit", "Delete", "Cancel", "Update" })" AllowTextWrap="true" Width="100%">
                                <GridEditSettings NewRowPosition="Syncfusion.Blazor.Grids.NewRowPosition.Bottom" AllowAdding="true" AllowEditing="true" AllowDeleting="true" ShowConfirmDialog="true"></GridEditSettings>
                                <GridEvents OnActionComplete="ActionCompletedHandlerPC" TValue="PoliticalCareerDTO"></GridEvents>
                                <GridColumns>
                                    <GridColumn AutoFit="true" MinWidth="200px" Field=@nameof(PoliticalCareerDTO.UI_Id) IsPrimaryKey="true" HeaderText="Id" Visible="false"></GridColumn>
                                    <GridColumn AutoFit="true" MinWidth="200px" Field=@nameof(PoliticalCareerDTO.Position) HeaderText="Position"></GridColumn>
                                    <GridColumn AutoFit="true" MinWidth="200px" Field=@nameof(PoliticalCareerDTO.DateFrom) Format="d MMM, yyyy" EditType="EditType.DatePickerEdit" HeaderText="Date From" CustomFormat="@( new { type = "date", format = "d MMM, yyyy" })"></GridColumn>
                                    <GridColumn AutoFit="true" MinWidth="200px" Field=@nameof(PoliticalCareerDTO.DateTo) Format="d MMM, yyyy" EditType="EditType.DatePickerEdit" HeaderText="DateTo" CustomFormat="@( new { type = "date", format = "d MMM, yyyy" })"></GridColumn>
                                </GridColumns>
                            </SfGrid>
                        </div>*@
                    <div class="tabheader">Personal Information</div>
                    <div class="row mb-2">
                        <div class="col">
                            <SfComboBox TItem="Profession" TValue="int?" @bind-Value="@_selectedObj.ProfessionId" Placeholder="Select Profession" DataSource="@_professionsList" FloatLabelType="FloatLabelType.Always">
                                <ComboBoxFieldSettings Value="Id" Text="EnglishTitle"></ComboBoxFieldSettings>
                            </SfComboBox>
                        </div>
                        <div class="col">
                            <SfComboBox TItem="GeneralItemDTO" TValue="int?" Placeholder="Select Education" @bind-Value="@_selectedObj.EducationId" DataSource="@_educationsList" FloatLabelType="FloatLabelType.Always">
                                <ComboBoxFieldSettings Value="Id" Text="EnglishTitle"></ComboBoxFieldSettings>
                            </SfComboBox>
                        </div>
                        <div class="col">
                            <SfTextBox @bind-Value="_selectedObj.EducationDegree" Placeholder="Education Degree" FloatLabelType="FloatLabelType.Always"></SfTextBox>
                            <ValidationMessage For="@(() => _selectedObj.EducationDegree)"/>
                        </div>
                    </div>
                    <div class="row mb-2">
                        <div class="col">
                            <SfComboBox TItem="Language" TValue="int?" Placeholder="Select Language" @bind-Value="@_selectedObj.LanguageId" DataSource="@_languagesList" FloatLabelType="FloatLabelType.Always">
                                <ComboBoxFieldSettings Value="Id" Text="EnglishTitle"></ComboBoxFieldSettings>
                            </SfComboBox>
                        </div>
                        <div class="col">
                            <SfComboBox TItem="Caste" TValue="int?" Placeholder="Select Cast" @bind-Value="@_selectedObj.CasteId" DataSource="@_castesList" FloatLabelType="FloatLabelType.Always">
                                <ComboBoxFieldSettings Value="Id" Text="EnglishTitle"></ComboBoxFieldSettings>
                            </SfComboBox>
                        </div>
                    </div>
                }
                <AuthorizeView Roles="Administrators">
                    <MudButton ButtonType="ButtonType.Submit" Disabled="DisableButton" Class="mt-1" Color="Color.Primary" Size="Size.Small" Variant="Variant.Filled" StartIcon="@Icons.Material.Filled.Save">Save Profile</MudButton>
                </AuthorizeView>
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>
<section>
    @if (_objFilteredList == null)
    {
        <p>
            <em>Loading...</em>
        </p>
    }
    else
    {
        @if (!_importMode)
        {
            <div style="display:flex;justify-items:center;gap:10px;align-items:center" class="mb-2">
                <MudText Class="" Typo="Typo.h5">
                    <MudIcon Icon="@Icons.Material.Filled.PersonPinCircle" Size="Size.Large"/> @(state.state.ElectionType == ElectionType.General ? "Candidates" : "Candidates/Panels")
                </MudText>
                <AuthorizeView Roles="Administrators">
                    <MudButton OnClick="OpenCreateForm" Size="Size.Small"
                               Variant="Variant.Filled" Color="Color.Primary">
                        Create
                    </MudButton>
                    @*<MudButton Color="Color.Info" Variant="MudBlazor.Variant.Filled" Size="MudBlazor.Size.Small" StartIcon="@Icons.Material.Filled.Refresh" OnClick="RefreshCandidatesList">Refresh</MudButton>*@
                </AuthorizeView>
            </div>
            <div style="display:flex; grid-gap: 10px; margin-bottom: 8px">
                <SfTextBox Width="150px" Placeholder="Search By Code" @bind-Value="@FilterCode"></SfTextBox>
                <SfTextBox Placeholder="Search By Name" @bind-Value="@FilterText"></SfTextBox>
                <MudButton Size="Size.Small" Variant="Variant.Filled" Color="Color.Primary" StartIcon="@Icons.Material.Filled.Search" Style="width:150px" OnClick="@(() => DoSearch())">Search</MudButton>
            </div>
            @*<SfButton IsPrimary="false" CssClass="e-primary mb-2" OnClick="OpenImportForm">Import <span class="oi oi-cloud-download" aria-hidden="true"></span></SfButton>*@
            @*<div class="table-responsive">
                    <table class="table table-sm">
                        <thead class="thead-dark">
                            <tr>
                                <th style="width:55px">Code</th>
                                <th>Name</th>
                                <th>Date of Birth</th>
                                <th>Status</th>
                                <th>Current Party</th>
                                <th>District</th>
                                <th>Nominations</th>
                                <th>Audit Info</th>
                                <th style="width: 55px; text-align:center">Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            <Virtualize Context="n" Items="objFilteredList">
                                <tr>
                                    <td>@n.Id</td>
                                    <td><img src="@n.PictureURL" class="myimg" style="width:75px; height:100px" align="left" />  @n.EnglishName<br /><span class="urdu-sm">@n.UrduName</span></td>
                                    <td>@(n.DateOfBirth == null ? "" : n.DateOfBirth.Value.ToString("d MMM, yyyy"))</td>
                                    <td>@n.FreshStatus</td>
                                    <td>@n.CurrentParty</td>
                                    <td>@n.District</td>
                                    <td>@((MarkupString)n.Constituencies)</td>
                                    <td>
                                        <span style="font-size:10px">
                                            Created By: @n.CreatedBy on @n.CreatedOn<br />
                                            @if (!string.IsNullOrEmpty(n.ModifiedBy))
                                            {
                                                <span>Modified By: @n.ModifiedBy on @n.ModifiedOn</span>
                                            }
                                        </span>
                                    </td>
                                    <td>
                                        <a target="@($"cd{n.Id}")" href="report/candidate/@n.Id"><MudIcon Icon="@Icons.Material.Filled.TableChart"></MudIcon></a>
                                        <AuthorizeView Roles="Administrators">
                                            <SfButton Disabled="@(n.DisableDelete)" IsPrimary="true" CssClass="e-warning" OnClick="@(() => DeleteRecord(n.Id))"><i class="fas fa-trash-alt"></i></SfButton>
                                        </AuthorizeView>
                                        <SfButton IsPrimary="true" CssClass="e-info" OnClick="@(() => OpenEditForm(n))"><i class="fas fa-pencil-alt"></i></SfButton>
                                    </td>
                                </tr>
                            </Virtualize>
                        </tbody>
                    </table>
                </div>*@
            //OnRecordDoubleClick="OpenEditForm"
            <SfGrid @ref="Grid" FrozenColumns="3" AllowSorting="true" AllowFiltering="true"
                    AllowPaging="true"
                    DataSource="_objFilteredList" AllowTextWrap="true" Width="100%" Height="calc(100vh - 260px)">
                <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Excel"></GridFilterSettings>
                <GridPageSettings PageSize="10"/>
                <GridEvents QueryCellInfo="CustomizeCell" TValue="CandidateDTO"></GridEvents>
                <GridTextWrapSettings WrapMode="WrapMode.Content"></GridTextWrapSettings>
                <GridColumns>
                    <GridColumn Width="130px" HeaderText="Code" Field="@nameof(CandidateDTO.Id)"></GridColumn>
                    <GridColumn AutoFit="true" Visible="state.state.ElectionType == ElectionType.LocalBody" HeaderText="Type" Field="@nameof(CandidateDTO.CandidateTypeStr)"></GridColumn>
                    <GridColumn HeaderText="Picture" Width="120px">
                        <Template Context="cc">
                            @{
                                if (cc is CandidateDTO p)
                                {
                                    var img = $"media/candidates/{p.Id}.jpg";
                                    <img src="@p.PictureURL" class="myimg" style="width:75px; height:100px" alt=""/>
                                }
                            }
                        </Template>
                    </GridColumn>
                    <GridColumn Width="220px" HeaderText="Name" Field="@nameof(CandidateDTO.FullName)" DisableHtmlEncode="false"></GridColumn>
                    @*<GridColumn AutoFit="true" HeaderText="Date of Birth" Field="@nameof(CandidateDTO.DateOfBirthStr)"></GridColumn>*@
                    <GridColumn AutoFit="true" HeaderText="Big Name" Field="@nameof(CandidateDTO.BigName)"></GridColumn>
                    <GridColumn Width="120px" HeaderText="Status" Field="@nameof(CandidateDTO.IsFreshStr)"></GridColumn>
                    @*<GridColumn AutoFit="true" HeaderText="Current Party" Field="@nameof(CandidateDTO.CurrentParty)"></GridColumn>*@
                    <GridColumn Width="150px" HeaderText="District" Field="@nameof(CandidateDTO.District)"></GridColumn>
                    <GridColumn Width="300px" HeaderText="Nominations" Field="@nameof(CandidateDTO.District)">
                        <Template Context="nn">
                            @{
                                if (nn is CandidateDTO mm)
                                {
                                    <div style="max-height:80px;overflow-y:auto">
                                        @((MarkupString)mm.Constituencies)
                                    </div>
                                }
                            }
                        </Template>
                    </GridColumn>

                    <GridColumn HeaderText="Audit Info" AutoFit="true">
                        <Template Context="kk">
                            @{
                                if (kk is CandidateDTO oo)
                                {
                                    <div style="font-size:11px">
                                        <span>Created By: @oo.CreatedBy</span><span> On @oo.CreatedOn</span>
                                        @if (!string.IsNullOrEmpty(oo.ModifiedBy))
                                        {
                                            <br/>
                                            <span>Modified By: @oo.ModifiedBy</span>
                                            <span> On @oo.ModifiedOn</span>
                                        }
                                    </div>
                                }
                            }
                        </Template>
                    </GridColumn>
                    <GridColumn HeaderText="" AutoFit="true">
                        <Template Context="ec">
                            @{
                                var obj = ec as CandidateDTO;
                                var lnk = $"/setup/mergecandidate/{obj.Id}";
                                var trg = $"fd{obj.Id}";
                                <a target="@($"cd{obj.Id}")" href="report/candidate/@obj.Id">
                                    <MudIcon Icon="@Icons.Material.Filled.TableChart"></MudIcon>
                                </a>
                                <AuthorizeView Roles="Administrators">
                                    <MudFab Color="Color.Error" Size="Size.Small" StartIcon="@Icons.Material.Filled.Delete" Disabled="@(obj.DisableDelete)" OnClick="@(() => DeleteRecord(obj.Id))"></MudFab>
                                    <MudFab Size="Size.Small" Color="Color.Primary" StartIcon="@Icons.Material.Filled.Edit" OnClick="@(() => OpenEditForm(obj))"></MudFab>
                                    <MudLink Target="@trg" Href="@lnk">Find Duplicate</MudLink>
                                </AuthorizeView>
                            }
                        </Template>
                    </GridColumn>
                </GridColumns>
            </SfGrid>
        }
        @*else
            {
                <SfButton IsPrimary="false" CssClass="e-primary mb-2" OnClick="AddCandidateToElection">Add/Remove Candidates <i class="fas fa-plus-circle"></i></SfButton>
                <SfButton IsPrimary="false" CssClass="e-secondary mb-2" OnClick="BackToSelected">Cancel <span class="oi oi-action-undo" aria-hidden="true"></span></SfButton>
                <SfGrid @ref="Grid" AllowSorting="true" AllowFiltering="true" AllowPaging="true" DataSource="objList" AllowTextWrap="true">
                    <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Excel"></GridFilterSettings>
                    <GridEvents QueryCellInfo="CustomizeCell" TValue="CandidateDTO"></GridEvents>
                    <GridPageSettings PageSize="10"></GridPageSettings>
                    <GridTextWrapSettings WrapMode="WrapMode.Content"></GridTextWrapSettings>
                    <GridColumns>
                        <GridColumn HeaderText="">
                            <Template Context="ec">
                                @{
                                    var obj = ec as CandidateDTO;
                                    if (obj.NominationExistOnThisElection)
                                    {
                                        <span>Nomination Exist</span>
                                    }
                                    else
                                    {
                                        <SfCheckBox @bind-Checked="obj.IsSelected"></SfCheckBox>
                                    }
                                }
                            </Template>
                        </GridColumn>
                        <GridColumn AutoFit="true" HeaderText="Code" Field="Id"></GridColumn>
                        <GridColumn AutoFit="true" HeaderText="Type" Field="CandidateTypeStr"></GridColumn>
                        <GridColumn HeaderText="Picture" Width="120px">
                            <Template Context="cc">
                                @{
                                    var p = cc as CandidateDTO;
                                    var img = $"media/candidates/{p.Id}.jpg";
                                    <img src="@p.PictureURL" class="myimg" style="width:75px; height:100px" />
                                }
                            </Template>
                        </GridColumn>
                        <GridColumn AutoFit="true" HeaderText="Name" Field="FullName" DisableHtmlEncode="false"></GridColumn>
                        <GridColumn AutoFit="true" Format="d MMM, yyyy" HeaderText="Date of Birth" Field="DateOfBirth"></GridColumn>
                        <GridColumn AutoFit="true"  HeaderText="Status" Field="FreshStatus"></GridColumn>
                        <GridColumn AutoFit="true"  HeaderText="Current Party" Field="CurrentParty"></GridColumn>
                        <GridColumn AutoFit="true"  HeaderText="District" Field="District"></GridColumn>
                        <GridColumn AutoFit="true"  HeaderText="Constituencies" Field="Constituencies" DisableHtmlEncode="false"></GridColumn>
                        <GridColumn HeaderText="Audit Info" Width="150px">
                            <Template Context="kk">
                                @{
                                    var oo = kk as CandidateDTO;
                                    <div style="font-size:11px">
                                        <span>Created By: @oo.CreatedBy</span><span> On @oo.CreatedOn</span>
                                        @if (!string.IsNullOrEmpty(oo.ModifiedBy))
                                        {
                                            <br /><span>Modified By: @oo.ModifiedBy</span><span> On @oo.ModifiedOn</span>
                                        }
                                    </div>
                                }
                            </Template>
                        </GridColumn>
                        <GridColumn HeaderText="" Width="67px">
                            <Template Context="ec">
                                @{
                                    var obj = ec as CandidateDTO;
                                    <a target="@($"cd{obj.Id}")" href="report/candidate/@obj.Id"><MudIcon Icon="@Icons.Material.Filled.TableChart"></MudIcon></a>
                                }
                            </Template>
                        </GridColumn>
                    </GridColumns>
                </SfGrid>
            }*@
    }
</section>