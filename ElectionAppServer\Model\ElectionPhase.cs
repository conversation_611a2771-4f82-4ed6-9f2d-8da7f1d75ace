﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using ElectionAppServer.Model.Structures;

namespace ElectionAppServer.Model;

public class ElectionPhase
{
    public int Id { get; set; }
    public string Title { get; set; }
    public DateTime StartDate { get; set; }
    public int ElectionId { get; set; }
    public virtual Election Election { get; set; }
    public virtual List<PSTotalVoteCaste> PSTotalVoteCastes { get; set; }

    public virtual List<Nomination> Nominations { get; set; }
    public virtual List<ResultLog> ResultLogs { get; set; }
    public virtual List<PanelNomination> PannelNominations { get; set; }

    public bool IsLocked { get; set; } = false;
    public virtual List<RePoll> RePolls { get; set; }
    public virtual List<RePollSeat> RePollSeats { get; set; }
    public virtual List<PollingStationCombinedResult> PollingStationCombinedResults { get; set; }
    public virtual List<NamanigarPerformance> NamanigarPerformances { get; set; }
    public virtual List<PollingScheme> PollingSchemes { get; set; }
    public virtual List<PhaseWiseNamanigar> PhaseWiseNamanigars { get; set; }
    #region Audit Log Fields

    public DateTime CreatedDate { get; set; } = DateTime.Now;
    public DateTime? ModifiedDate { get; set; }

    [StringLength(450)] public string CreatedBy { get; set; }

    [StringLength(450)] public string ModifiedBy { get; set; }

    public bool IsActive { get; set; } = false;

    #endregion Audit Log Fields
}

public enum ElectionStatus
{
    NotConfigured = 1,
    Current = 2,
    Completed = 3
}