﻿using System.Collections.Generic;
using ElectionAppServer.Model;
using ElectionAppServer.Model.Structures;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using ApplicationUser = ElectionAppServer.Model.ApplicationUser;
using AssemblyType = ElectionAppServer.Model.AssemblyType;
using Candidate = ElectionAppServer.Model.Candidate;
using CandidateDistrict = ElectionAppServer.Model.CandidateDistrict;
using CandidatePartyAffiliation = ElectionAppServer.Model.CandidatePartyAffiliation;
using CandidtePoliticalCareer = ElectionAppServer.Model.CandidtePoliticalCareer;
using Caste = ElectionAppServer.Model.Caste;
using Coalition = ElectionAppServer.Model.Coalition;
using District = ElectionAppServer.Model.District;
using Division = ElectionAppServer.Model.Division;
using Education = ElectionAppServer.Model.Education;
using Election = ElectionAppServer.Model.Election;
using ElectionAssembly = ElectionAppServer.Model.ElectionAssembly;
using ElectionAssemblySeat = ElectionAppServer.Model.ElectionAssemblySeat;
using ElectionPhase = ElectionAppServer.Model.ElectionPhase;
using Language = ElectionAppServer.Model.Language;
using LiveResult = ElectionAppServer.Model.LiveResult;
using Menu = ElectionAppServer.Model.Menu;
using Namanigar = ElectionAppServer.Model.Namanigar;
using NamanigarConstituency = ElectionAppServer.Model.NamanigarConstituency;
using NamanigarPerformance = ElectionAppServer.Model.NamanigarPerformance;
using NamanigarResult = ElectionAppServer.Model.NamanigarResult;
using NamanigarResultLog = ElectionAppServer.Model.NamanigarResultLog;
using Nomination = ElectionAppServer.Model.Nomination;
using NominationPanelist = ElectionAppServer.Model.NominationPanelist;
using OfflineResult = ElectionAppServer.Model.OfflineResult;
using PanelNomination = ElectionAppServer.Model.PanelNomination;
using Party = ElectionAppServer.Model.Party;
using PartyCoalition = ElectionAppServer.Model.PartyCoalition;
using PollingScheme = ElectionAppServer.Model.PollingScheme;
using Profession = ElectionAppServer.Model.Profession;
using Province = ElectionAppServer.Model.Province;
using PSResult = ElectionAppServer.Model.PSResult;
using PSResultLog = ElectionAppServer.Model.PSResultLog;
using PSTotalVoteCaste = ElectionAppServer.Model.PSTotalVoteCaste;
using RePoll = ElectionAppServer.Model.RePoll;
using RePollSeat = ElectionAppServer.Model.RePollSeat;
using Reporter = ElectionAppServer.Model.Reporter;
using ResultLog = ElectionAppServer.Model.ResultLog;
using ResultLogDetail = ElectionAppServer.Model.ResultLogDetail;
using ResultSource = ElectionAppServer.Model.ResultSource;
using ResultStatus = ElectionAppServer.Model.ResultStatus;
using RoleMenu = ElectionAppServer.Model.RoleMenu;
using SearchCandidate = ElectionAppServer.Model.SearchCandidate;
using SeatType = ElectionAppServer.Model.SeatType;
using Structure = ElectionAppServer.Model.Structure;
using Symbol = ElectionAppServer.Model.Symbol;
using Town = ElectionAppServer.Model.Town;
using UnionCouncil = ElectionAppServer.Model.UnionCouncil;
using UserConstituency = ElectionAppServer.Model.UserConstituency;

namespace ElectionAppServer.Data;

public class ApplicationDbContext : IdentityDbContext<ApplicationUser>
{
    public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
        : base(options)
    {
    }

    public virtual DbSet<AdminDivision> AdminDivisions { get; set; }
    public virtual DbSet<OfflineResult> OfflineResult { get; set; }
    public virtual DbSet<ResultSource> ResultSource { get; set; }
    public virtual DbSet<Caste> Castes { get; set; }
    public virtual DbSet<Candidate> Candidates { get; set; }
    public virtual DbSet<Model.Structure> Structures { get; set; }
    public virtual DbSet<StructureMapping> StructureMappings { get; set; }
    public virtual DbSet<Education> Educations { get; set; }
    public virtual DbSet<Language> Languages { get; set; }
    public virtual DbSet<AssemblyType> LocalBodyStructures { get; set; }
    public virtual DbSet<Profession> Professions { get; set; }
    public virtual DbSet<SeatType> SeatTypes { get; set; }
    public virtual DbSet<Model.Symbol> Symbols { get; set; }
    public virtual DbSet<Model.Party> Parties { get; set; }
    public virtual DbSet<CandidatePartyAffiliation> PartyAffiliations { get; set; }
    public virtual DbSet<CandidtePoliticalCareer> PoliticalCareers { get; set; }
    public virtual DbSet<AssemblyType> AssemblyTypes { get; set; }
    public virtual DbSet<Model.Election> Elections { get; set; }
    public virtual DbSet<Region> Regions { get; set; }
    public virtual DbSet<ElectionPhase> ElectionPhases { get; set; }
    public virtual DbSet<Model.ElectionAssembly> ElectionAssemblies { get; set; }
    public virtual DbSet<ElectionAssemblySeat> ElectionAssemblySeats { get; set; }
    public virtual DbSet<PollingStation> PollingStations { get; set; }
    public virtual List<PollingStationCombinedResult> PollingStationCombinedResults { get; set; }
    public virtual DbSet<Reporter> Reporters { get; set; }
    public virtual DbSet<Nomination> Nominations { get; set; }
    public virtual DbSet<NamanigarResult> NamanigarResults { get; set; }
    public virtual DbSet<Coalition> Coalitions { get; set; }
    public virtual DbSet<PartyCoalition> PartyCoalitions { get; set; }
    public virtual DbSet<UserConstituency> UserConstituencies { get; set; }
    public virtual DbSet<ResultLog> ResultLogs { get; set; }
    public virtual DbSet<ResultLogDetail> ResultLogDetails { get; set; }
    public virtual DbSet<Namanigar> Namanigars { get; set; }
    public virtual DbSet<NamanigarPerformance> NamanigarPerformances { get; set; }
    public virtual DbSet<NamanigarConstituency> NamanigarConstituencies { get; set; }
    public virtual DbSet<CandidateDistrict> CandidateDistricts { get; set; }
    public virtual DbSet<LiveResult> LiveResults { get; set; }
    public virtual DbSet<PanelNomination> PanelNominations { get; set; }
    public virtual DbSet<PSResult> PSResults { get; set; }
    public virtual DbSet<PSResultLog> PSResultLogs { get; set; }
    public virtual DbSet<PSTotalVoteCaste> PSTotalVoteCastes { get; set; }

    public virtual DbSet<NamanigarResultLog> NamanigarResultLogs { get; set; }
    public virtual DbSet<SearchCandidate> SearchCandidates { get; set; }

    public virtual DbSet<NominationPanelist> NominationPanelists { get; set; }

    //public virtual DbSet<ElectionCandidate> ElectionCandidates { get; set; }
    //public virtual DbSet<ElectionParty> ElectionParties { get; set; }
    public DbSet<RePoll> RePolls { get; set; }
    public DbSet<RePollSeat> RePollSeats { get; set; }
    public DbSet<Menu> Menus { get; set; }
    public DbSet<RoleMenu> RoleMenus { get; set; }
    public virtual DbSet<PollingScheme> PollingSchemes { get; set; }
    public virtual DbSet<PhaseWiseNamanigar> PhaseWiseNamanigars { get; set; }
    public virtual DbSet<Problem> Problems { get; set; }
    public virtual DbSet<StructureProblem> StructureProblems { get; set; }

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
        if (!optionsBuilder.IsConfigured)
        {
        }
    }

    protected override void OnModelCreating(ModelBuilder mb)
    {
        base.OnModelCreating(mb);
        mb.HasAnnotation("ProductVersion", "2.2.0-rtm-35687");
        //mb.Entity<ElectionParty>(cc =>
        //{
        //	cc.HasKey(c => new { c.PartyId, c.ElectionId });
        //	cc.Property(c => c.CreatedBy).HasMaxLength(430).IsUnicode(false).IsRequired(true);
        //	cc.Property(c => c.CreatedDate).HasDefaultValue(DateTime.Now);
        //});
        //mb.Entity<ElectionCandidate>(cc =>
        //{
        //	cc.HasKey(c => new { c.CandidateId, c.ElectionId });
        //	cc.Property(c => c.CreatedBy).HasMaxLength(430).IsUnicode(false).IsRequired(true);
        //	cc.Property(c => c.CreatedDate).HasDefaultValue(DateTime.Now);
        //});

        mb.Entity<AdminDivision>(e =>
        {
            e.Property(p => p.EnglishTitle).IsRequired().HasMaxLength(200);
            e.Property(p => p.UrduTitle).IsRequired().HasMaxLength(200);
            e.HasIndex(p => new { p.ProvinceId, p.UrduTitle }).IsUnique();
            e.HasIndex(p => new { p.ProvinceId, p.EnglishTitle }).IsUnique();
            e.HasMany(p => p.Districts).WithOne(p => p.AdminDivision).HasForeignKey(p => p.AdminDivisionId)
                .OnDelete(DeleteBehavior.Restrict);
        });

        mb.Entity<Menu>(e =>
        {
            e.Property(p => p.Link).HasMaxLength(200).IsRequired().IsUnicode(false);
            e.Property(p => p.Name).HasMaxLength(200).IsRequired().IsUnicode(false);
            e.HasMany(p => p.RoleMenus).WithOne(p => p.Menu).HasForeignKey(p => p.MenuId)
                .OnDelete(DeleteBehavior.Cascade);
        });

        mb.Entity<ResultSource>(e =>
        {
            e.Property(p => p.Title).IsRequired().HasMaxLength(200).IsUnicode(false);
            e.HasIndex(p => p.Title).IsUnique();
            e.HasMany(p => p.OfflineResults).WithOne(p => p.ResultSource).HasForeignKey(p => p.ResultSourceId)
                .OnDelete(DeleteBehavior.Restrict);
        });

        mb.Entity<RoleMenu>(e => { e.HasKey(p => new { p.RoleId, p.MenuId }); });

        mb.Entity<OfflineResult>(e => { e.HasKey(p => new { p.NominationId, p.ResultSourceId }); });

        mb.Entity<RePoll>(e =>
        {
            e.HasKey(k => new { k.PhaseId, k.ConstituencyId });
            e.HasOne(k => k.Phase).WithMany(k => k.RePolls).HasForeignKey(k => k.PhaseId)
                .OnDelete(DeleteBehavior.Restrict);
            e.HasOne(k => k.Constituency).WithMany(k => k.RePolls).HasForeignKey(k => k.ConstituencyId)
                .OnDelete(DeleteBehavior.Restrict);
            e.Property(k => k.CreatedBy).HasMaxLength(450).IsRequired();
        });

        mb.Entity<PSTotalVoteCaste>(e =>
        {
            e.HasKey(m => new { m.PhaseId, m.PollingStationId });
            e.HasOne(m => m.PollingStation).WithMany(m => m.PSTotalVoteCastes).HasForeignKey(m => m.PollingStationId)
                .OnDelete(DeleteBehavior.Cascade);
            e.HasOne(m => m.Phase).WithMany(m => m.PSTotalVoteCastes).HasForeignKey(m => m.PhaseId)
                .OnDelete(DeleteBehavior.Cascade);
        });

        mb.Entity<RePollSeat>(e =>
        {
            e.HasKey(k => new { k.PhaseId, k.ConstituencyId, k.SeatTypeId });
            e.HasOne(k => k.Phase).WithMany(k => k.RePollSeats).HasForeignKey(k => k.PhaseId)
                .OnDelete(DeleteBehavior.Restrict);
            e.HasOne(k => k.Constituency).WithMany(k => k.RePollSeats).HasForeignKey(k => k.ConstituencyId)
                .OnDelete(DeleteBehavior.Restrict);
            e.HasOne(k => k.SeatType).WithMany(k => k.RePollSeats).HasForeignKey(k => k.SeatTypeId)
                .OnDelete(DeleteBehavior.Restrict);
            e.Property(k => k.CreatedBy).HasMaxLength(450).IsRequired();
        });


        mb.Entity<NominationPanelist>(e =>
        {
            e.Property(p => p.NameEnglish).IsRequired().IsUnicode(false).HasMaxLength(200);
            e.Property(p => p.NameUrdu).IsRequired().IsUnicode().HasMaxLength(200);
            e.Property(p => p.Gender).IsRequired();
            e.Property(p => p.SeatTypeId).IsRequired();
            e.Property(p => p.CreatedBy).HasMaxLength(200).IsUnicode(false).IsRequired();
            e.Property(p => p.ModifiedBy).HasMaxLength(200).IsUnicode(false).IsRequired(false);
        });

        mb.Entity<SearchCandidate>(cc =>
        {
            cc.HasKey(p => new { p.ElectionId, p.CandidateId });
            cc.Property(p => p.EnglishName).HasMaxLength(200).IsUnicode(false).IsRequired();
            cc.Property(p => p.UrduName).HasMaxLength(200).IsRequired();
            cc.Property(p => p.District).HasMaxLength(200).IsUnicode(false);
            cc.Property(p => p.FullName).HasMaxLength(500);
            cc.Property(p => p.CreatedBy).HasMaxLength(200).IsUnicode(false);
            cc.Property(p => p.ModifiedBy).HasMaxLength(200).IsUnicode(false);
            cc.Property(p => p.UrduFatherName).HasMaxLength(200);
            cc.Property(p => p.EnglishFatherName).HasMaxLength(200).IsUnicode(false);
            cc.Property(p => p.CandidateType).HasMaxLength(20).IsUnicode(false);
            cc.Property(p => p.Status).HasMaxLength(20);
            cc.Property(p => p.CurrentParty).HasMaxLength(200).IsUnicode(false);
            cc.Property(p => p.Constituencies).IsUnicode(false);
            cc.HasIndex(p => p.ElectionId);
            cc.HasIndex(p => p.CandidateId);
            cc.HasIndex(p => p.EnglishName);
        });
        mb.Entity<PanelNomination>(cc =>
        {
            cc.Property(k => k.CandidateEnglishName).HasMaxLength(200).IsRequired().IsUnicode(false);
            cc.Property(k => k.CandidateUrduName).HasMaxLength(200).IsRequired().IsUnicode();
            cc.HasOne(k => k.Assembly).WithMany(k => k.PannelNominations).HasForeignKey(k => k.AssemblyId)
                .OnDelete(DeleteBehavior.Restrict);
            cc.HasOne(k => k.Phase).WithMany(k => k.PannelNominations).HasForeignKey(k => k.PhaseId)
                .OnDelete(DeleteBehavior.Restrict);
            //cc.HasOne(k => k.Panel).WithMany(k => k.PanelNominations).HasForeignKey(k => k.PanelId).OnDelete(DeleteBehavior.Restrict);
            cc.Property(c => c.CreatedBy).HasMaxLength(450).IsRequired().IsUnicode(false);
            cc.Property(c => c.CreatedDate).HasDefaultValueSql("getdate()");
            cc.Property(c => c.ModifiedBy).HasMaxLength(450).IsRequired(false).IsUnicode(false);
        });
        mb.Entity<District>(cc =>
        {
            cc.HasIndex(e => e.DivisionId);
            cc.HasOne(d => d.Division).WithMany(p => p.Districts).HasForeignKey(d => d.DivisionId)
                .OnDelete(DeleteBehavior.ClientSetNull).HasConstraintName("FK_Districts_Divisions");
            cc.HasMany(c => c.Towns).WithOne(c => c.District).HasForeignKey(c => c.DistrictId)
                .OnDelete(DeleteBehavior.Restrict);
            cc.HasMany(c => c.NationalAssemblyHalkas).WithOne(c => c.District).HasForeignKey(c => c.DistrictId)
                .OnDelete(DeleteBehavior.Restrict);
            cc.HasMany(c => c.ProvincialAssemblyHalkas).WithOne(c => c.District).HasForeignKey(c => c.DistrictId)
                .OnDelete(DeleteBehavior.Restrict);
        });
        mb.Entity<Division>(cc =>
        {
            cc.HasIndex(e => e.ProvinceId);
            cc.HasOne(d => d.Province).WithMany(p => p.Divisions).HasForeignKey(d => d.ProvinceId)
                .OnDelete(DeleteBehavior.ClientSetNull).HasConstraintName("FK_Divisions_Provinces");
            cc.HasMany(c => c.Districts).WithOne(c => c.Division).HasForeignKey(c => c.DivisionId)
                .OnDelete(DeleteBehavior.Restrict);
        });
        mb.Entity<Province>(cc =>
        {
            cc.HasMany(c => c.Regions).WithOne(c => c.Province).HasForeignKey(c => c.ProvinceId)
                .OnDelete(DeleteBehavior.Restrict);
            cc.HasMany(c => c.Divisions).WithOne(c => c.Province).HasForeignKey(c => c.ProvinceId)
                .OnDelete(DeleteBehavior.Restrict);
            cc.HasMany(p => p.AdminDivisions).WithOne(p => p.Province).HasForeignKey(p => p.ProvinceId)
                .OnDelete(DeleteBehavior.Restrict);
        });
        mb.Entity<Region>(cc =>
        {
            cc.HasIndex(c => new { c.ProvinceId, c.UrduTitle });
            cc.HasIndex(c => new { c.ProvinceId, c.EnglishTitle });
            cc.Property(c => c.EnglishTitle).IsRequired().IsUnicode(false).HasMaxLength(200);
            cc.Property(c => c.UrduTitle).IsRequired().IsUnicode().HasMaxLength(200);
            cc.HasMany(c => c.Districts).WithOne(c => c.Region).HasForeignKey(c => c.RegionId)
                .OnDelete(DeleteBehavior.Restrict);
        });
        mb.Entity<PollingStation>(cc =>
        {
            cc.Property(c => c.EnglishTitle).HasMaxLength(200).IsRequired().IsUnicode(false);
            cc.Property(c => c.UrduTitle).HasMaxLength(200).IsRequired();
            cc.Property(c => c.Number).HasMaxLength(200).IsRequired().IsUnicode(false);
            cc.HasIndex(c => new { c.ConstituencyId, c.Number }).IsUnique();
            cc.HasIndex(c => new { c.ConstituencyId, c.EnglishTitle }).IsUnique(false);
            cc.HasIndex(c => new { c.ConstituencyId, c.UrduTitle }).IsUnique(false);
        });


        mb.Entity<PollingStationCombinedResult>(cc =>
        {
            cc.HasKey(m => new { m.ElectionPhaseId, m.PollingStationId });
            cc.HasOne(m => m.ElectionPhase).WithMany(m => m.PollingStationCombinedResults)
                .HasForeignKey(m => m.ElectionPhaseId).OnDelete(DeleteBehavior.Cascade);
            cc.HasOne(m => m.PollingStation).WithMany(m => m.PollingStationCombinedResults)
                .HasForeignKey(m => m.PollingStationId).OnDelete(DeleteBehavior.Cascade);
        });

        mb.Entity<AssemblyType>(cc =>
        {
            cc.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            cc.Property(e => e.EnglishTitle).IsUnicode(false);
            cc.HasMany(c => c.ElectionAssemblies).WithOne(c => c.AssemblyType).HasForeignKey(c => c.AssemblyTypeId)
                .OnDelete(DeleteBehavior.Restrict);
            cc.Property(c => c.EnglishTitle).IsUnicode(false).IsRequired().HasMaxLength(200);
            cc.Property(c => c.UrduTitle).IsUnicode().IsRequired().HasMaxLength(200);
        });
        mb.Entity<Town>(cc =>
        {
            cc.HasMany(c => c.UnionCouncils).WithOne(c => c.Town).HasForeignKey(c => c.TownId)
                .OnDelete(DeleteBehavior.Restrict);
        });
        mb.Entity<UnionCouncil>(cc =>
        {
            cc.HasMany(c => c.Wards).WithOne(c => c.UnionCouncil).HasForeignKey(c => c.UnionCouncilId)
                .OnDelete(DeleteBehavior.Restrict);
            cc.HasIndex(c => new { c.ElectionId, c.Code, c.TownId }).IsUnique();
            cc.HasIndex(c => new { c.ElectionId, c.EnglishTitle, c.TownId }).IsUnique();
            cc.HasIndex(c => new { c.ElectionId, c.UrduTitle, c.TownId }).IsUnique();
        });
        mb.Entity<SeatType>(cc =>
        {
            cc.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            //cc.Property(e => e.EnglishTitle).IsUnicode(false);
            cc.HasMany(c => c.Nominations).WithOne(c => c.SeatType).HasForeignKey(c => c.SeatTypeId)
                .OnDelete(DeleteBehavior.Restrict);
            cc.Property(c => c.EnglishTitle).HasMaxLength(200).IsRequired().IsUnicode(false);
            cc.Property(c => c.UrduTitle).HasMaxLength(200).IsRequired().IsUnicode();
            cc.HasIndex(c => c.UrduTitle).IsUnique();
            cc.HasIndex(c => c.UrduTitle).IsUnique();
        });
        mb.Entity<ElectionAssemblySeat>(cc =>
        {
            cc.HasKey(c => new { c.ElectionAssemblyId, c.SeatTypeId, c.ConstituencyType });
        });

        mb.Entity<NamanigarResult>(cc =>
        {
            cc.HasKey(k => k.NominationId);
            cc.Property(c => c.CreatedBy).HasMaxLength(450).IsRequired().IsUnicode(false);
            cc.Property(c => c.CreatedOn).HasDefaultValueSql("getdate()");
            cc.Property(c => c.ModifiedBy).HasMaxLength(450).IsRequired(false).IsUnicode(false);
            cc.Property(c => c.LastActionMode).HasMaxLength(10).IsRequired().IsUnicode(false);
            cc.HasIndex(c => c.BatchId);
        });

        mb.Entity<NamanigarResultLog>(cc =>
        {
            cc.Property(c => c.LastActionMode).HasMaxLength(10).IsRequired().IsUnicode(false);
            cc.Property(c => c.NamanigarUserId).HasMaxLength(450).IsRequired().IsUnicode(false);
            cc.Property(c => c.ApprovedBy).HasMaxLength(450).IsRequired(false).IsUnicode(false);
            cc.HasIndex(c => c.BatchId);
        });

        mb.Entity<Nomination>(cc =>
        {
            cc.HasMany(p => p.OfflineResults)
                .WithOne(p => p.Nomination)
                .HasForeignKey(p => p.NominationId)
                .OnDelete(DeleteBehavior.Restrict);

            cc.HasOne(a => a.NamanigarResult).WithOne(c => c.Nomination)
                .HasForeignKey<NamanigarResult>(c => c.NominationId);
            cc.HasIndex(c => new
            {
                c.ElectionAssemblyId,
                c.StructureId,
                c.SeatTypeId,
                c.CandidteId,
                c.ElectionPhaseId
            }).IsUnique(false);
            cc.HasKey(c => c.Id);
            cc.Property(p => p.ResultUpdateBy).HasMaxLength(200).IsUnicode(false);
        });
        mb.Entity<Candidate>(cc =>
        {
            cc.HasMany(a => a.Nominations).WithOne(a => a.Candidate).HasForeignKey(a => a.CandidteId)
                .OnDelete(DeleteBehavior.Restrict);
            //cc.HasMany(a => a.CandidateElections).WithOne(a => a.Candidate).HasForeignKey(a => a.CandidateId).OnDelete(DeleteBehavior.Restrict);
            cc.Property(p => p.EducationDegree).HasMaxLength(200).IsUnicode().IsRequired(false);
            cc.Property(c => c.UrduName).IsRequired().HasMaxLength(200).IsUnicode();
            cc.Property(c => c.EnglishName).IsRequired().HasMaxLength(200).IsUnicode();
            cc.Property(c => c.FatherEnglishName).IsUnicode(false).HasMaxLength(100).IsRequired(false);
            cc.Property(c => c.FatherUrduName).IsUnicode().HasMaxLength(100).IsRequired(false);
            cc.Property(c => c.Trivia).HasColumnType("ntext");
            cc.Property(c => c.StrDistrict).HasMaxLength(200);
            cc.Property(c => c.TotalAssets).HasMaxLength(1000);
            cc.Property(c => c.ContactNumber).HasMaxLength(50).IsUnicode(false);
            cc.Property(c => c.EducationDegree).HasMaxLength(200);
            cc.Property(c => c.Source).HasMaxLength(100).IsUnicode(false);
            //cc.HasMany(c => c.PanelCandidates).WithOne(c => c.Panel).HasForeignKey(c => c.PanelId).OnDelete(DeleteBehavior.Restrict);
            //cc.HasOne(c => c.Panel).WithMany(c => c.PanelCandidates).HasForeignKey(c => c.PanelId).OnDelete(DeleteBehavior.Restrict);
        });
        mb.Entity<ElectionAssembly>(cc =>
        {
            cc.HasMany(c => c.Nominations).WithOne(c => c.ElectionAssembly).HasForeignKey(c => c.ElectionAssemblyId)
                .OnDelete(DeleteBehavior.Restrict);
            cc.HasMany(c => c.Constituencies).WithOne(c => c.Assembly).HasForeignKey(c => c.AssemblyId)
                .OnDelete(DeleteBehavior.Restrict);
            cc.HasMany(c => c.NamnigarConstituencies).WithOne(c => c.ElectionAssembly)
                .HasForeignKey(c => c.ElectionAssemblyId).OnDelete(DeleteBehavior.Restrict);
            cc.Property(c => c.EnglishTitle).IsRequired().HasMaxLength(200).IsUnicode(false);
            cc.Property(c => c.UrduTitle).IsRequired().HasMaxLength(200).IsUnicode();
        });
        mb.Entity<Structure>(cc =>
        {
            cc.HasMany(c => c.Nominations).WithOne(c => c.Structure).HasForeignKey(c => c.StructureId)
                .OnDelete(DeleteBehavior.Restrict);
            cc.Property(c => c.Code).HasMaxLength(20).IsUnicode();
            cc.HasMany(c => c.UserConstituencies).WithOne(c => c.Structure).HasForeignKey(c => c.StructureId)
                .OnDelete(DeleteBehavior.Restrict);
            cc.Property(c => c.EnglishTitle).IsUnicode(false).IsRequired().HasMaxLength(200);
            cc.HasMany(c => c.PollingStations).WithOne(c => c.Constituency).HasForeignKey(c => c.ConstituencyId)
                .OnDelete(DeleteBehavior.Restrict);
            cc.HasMany(c => c.StructureMappings).WithOne(c => c.Structure).HasForeignKey(c => c.StructureId)
                .OnDelete(DeleteBehavior.Cascade);
            cc.HasMany(c => c.PreviousStructureMappings).WithOne(c => c.PreviousStructure)
                .HasForeignKey(c => c.PreviousStructureId).OnDelete(DeleteBehavior.Restrict);
            cc.Property(c => c.ResultStatus).HasDefaultValue(ResultStatus.Result);
            cc.Property(c => c.HasBilaMokablia).HasDefaultValue(false);
            cc.HasMany(c=>c.StructureProblems)
                .WithOne(c => c.Structure)
                .HasForeignKey(c => c.StructureId)
                .OnDelete(DeleteBehavior.Restrict);
        });
        mb.Entity<StructureMapping>(cc =>
        {
            cc.HasKey(c => new { c.StructureId, c.PreviousStructureId });
            cc.Property(c => c.Description).HasMaxLength(200).IsRequired(false);
        });
        mb.Entity<Election>(cc =>
        {
            //cc.HasMany(a => a.ElectionCandidates).WithOne(a => a.Election).HasForeignKey(a => a.ElectionId).OnDelete(DeleteBehavior.Restrict);
            //cc.HasMany(c => c.ElectionParties).WithOne(c => c.Election).HasForeignKey(c => c.ElectionId).OnDelete(DeleteBehavior.Restrict);
            cc.HasMany(c => c.Structures).WithOne(c => c.Election).HasForeignKey(c => c.ElectionId)
                .OnDelete(DeleteBehavior.Restrict);
            cc.HasMany(c => c.PartyCoalitions).WithOne(c => c.Election).HasForeignKey(c => c.ElectionId)
                .OnDelete(DeleteBehavior.Restrict);
            cc.Property(c => c.UrduTitle).IsUnicode().HasMaxLength(200).IsRequired();
            cc.Property(c => c.EnglishTitle).IsUnicode(false).HasMaxLength(200).IsRequired();
            cc.Property(c => c.Description).HasMaxLength(2000);
            //cc.HasOne(c => c.ParentElection).WithOne(c => c.ChildElection).HasForeignKey<Election>(c => c.ParentElectionId);
            cc.HasOne(c => c.ParentElection)
                .WithOne()
                .HasForeignKey<Election>(k => k.ParentElectionId)
                .OnDelete(DeleteBehavior.Restrict);
        });
        mb.Entity<Party>(cc =>
        {
            //cc.HasMany(c => c.PartyElections).WithOne(c => c.Party).HasForeignKey(c => c.PartyId).OnDelete(DeleteBehavior.Restrict);
            cc.Property(c => c.EnglishTitle).IsRequired().HasMaxLength(200).IsUnicode(false);
            cc.Property(c => c.UrduTitle).IsRequired().HasMaxLength(200);
            cc.Property(c => c.ShortEnglishTitle).HasMaxLength(50).IsUnicode(false);
            cc.Property(c => c.ShortUrduTitle).HasMaxLength(50).IsUnicode();
            cc.HasMany(c => c.Nominations).WithOne(c => c.Party).HasForeignKey(c => c.PartyId)
                .OnDelete(DeleteBehavior.Restrict);
            cc.HasMany(c => c.PartyCoalitions).WithOne(c => c.Party).HasForeignKey(c => c.PartyId)
                .OnDelete(DeleteBehavior.Restrict);
            cc.HasIndex(c => c.EnglishTitle).IsUnique();
            cc.HasIndex(c => c.UrduTitle).IsUnique();
        });
        mb.Entity<Symbol>(cc =>
        {
            cc.HasMany(c => c.Nominations).WithOne(c => c.Symbol).HasForeignKey(c => c.SymbolId)
                .OnDelete(DeleteBehavior.Restrict);
            cc.Property(c => c.EnglishTitle).IsRequired().HasMaxLength(200).IsUnicode(false);
            cc.Property(c => c.UrduTitle).IsRequired().HasMaxLength(200);
            cc.HasMany(c => c.Parties).WithOne(c => c.Symbol).HasForeignKey(c => c.SymbolId)
                .OnDelete(DeleteBehavior.Restrict);
        });
        mb.Entity<UserConstituency>(cc => { cc.HasKey(c => new { c.ElectionAssemblyId, c.UserId, c.StructureId }); });
        mb.Entity<ApplicationUser>(cc =>
        {
            cc.HasMany(c => c.UserConstituencies).WithOne(c => c.User).HasForeignKey(c => c.UserId)
                .OnDelete(DeleteBehavior.Restrict);
        });
        mb.Entity<PartyCoalition>(cc => { cc.HasKey(c => new { c.PartyId, c.CoalitionId, c.ElectionId }); });
        mb.Entity<Coalition>(cc =>
        {
            cc.HasMany(c => c.PartyCoalitions).WithOne(c => c.Coalition).HasForeignKey(c => c.CoalitionId)
                .OnDelete(DeleteBehavior.Restrict);
            cc.Property(c => c.EnglishTitle).IsRequired().HasMaxLength(200).IsUnicode(false);
            cc.Property(c => c.UrduTitle).IsRequired().HasMaxLength(200);
            cc.HasIndex(c => c.UrduTitle).IsUnique();
            cc.HasIndex(c => c.EnglishTitle).IsUnique();
        });
        mb.Entity<Caste>(cc =>
        {
            cc.Property(c => c.EnglishTitle).HasMaxLength(200).IsRequired().IsUnicode(false);
            cc.Property(c => c.UrduTitle).HasMaxLength(200).IsRequired().IsUnicode();
            cc.HasIndex(c => c.UrduTitle).IsUnique();
            cc.HasIndex(c => c.UrduTitle).IsUnique();
        });
        mb.Entity<Education>(cc =>
        {
            cc.Property(c => c.EnglishTitle).HasMaxLength(200).IsRequired().IsUnicode(false);
            cc.Property(c => c.UrduTitle).HasMaxLength(200).IsRequired().IsUnicode();
            cc.HasIndex(c => c.UrduTitle).IsUnique();
            cc.HasIndex(c => c.UrduTitle).IsUnique();
        });
        mb.Entity<Language>(cc =>
        {
            cc.Property(c => c.EnglishTitle).HasMaxLength(200).IsRequired().IsUnicode(false);
            cc.Property(c => c.UrduTitle).HasMaxLength(200).IsRequired().IsUnicode();
            cc.HasIndex(c => c.UrduTitle).IsUnique();
            cc.HasIndex(c => c.UrduTitle).IsUnique();
        });

        mb.Entity<Problem>(e =>
        {
            e.Property(p => p.Title).IsRequired().HasMaxLength(500);
            e.HasMany(c => c.StructureProblems)
                .WithOne(c => c.Problem)
                .HasForeignKey(c => c.ProblemId)
                .OnDelete(DeleteBehavior.Restrict);
        });

        mb.Entity<StructureProblem>(e =>
        {
            e.HasKey(c => new { c.ProblemId, c.StructureId });
        });

        mb.Entity<Profession>(cc =>
        {
            cc.Property(c => c.EnglishTitle).HasMaxLength(200).IsRequired().IsUnicode(false);
            cc.Property(c => c.UrduTitle).HasMaxLength(200).IsRequired().IsUnicode();
            cc.HasIndex(c => c.UrduTitle).IsUnique();
            cc.HasIndex(c => c.UrduTitle).IsUnique();
        });
        mb.Entity<ElectionPhase>(cc =>
        {
            cc.Property(c => c.Title).HasMaxLength(200).IsRequired();
            cc.HasIndex(c => new { c.ElectionId, c.Title }).IsUnique();
            cc.HasMany(c => c.Nominations).WithOne(c => c.ElectionPhase).HasForeignKey(c => c.ElectionPhaseId)
                .OnDelete(DeleteBehavior.Restrict);
        });
        mb.Entity<ResultLog>(cc =>
        {
            cc.HasMany(c => c.ResultLogDetails).WithOne(c => c.ResultLog).HasForeignKey(c => c.ResultLogId)
                .OnDelete(DeleteBehavior.Restrict);
            cc.HasOne(c => c.Namanigar).WithMany(c => c.AsNamanigarResultLogs).HasForeignKey(c => c.NamanigarId)
                .OnDelete(DeleteBehavior.Restrict);
            cc.Property(c => c.CreatedByUserId).HasMaxLength(450).IsUnicode(false).IsRequired();
            cc.HasOne(c => c.Assembly).WithMany(c => c.ResultLogs).HasForeignKey(c => c.AssemblyId)
                .OnDelete(DeleteBehavior.Restrict);
            cc.HasOne(c => c.Phase)
                .WithMany(c => c.ResultLogs)
                .HasForeignKey(c => c.PhaseId)
                .OnDelete(DeleteBehavior.Restrict);
            cc.HasOne(c => c.Constituency)
                .WithMany(c => c.ResultLogs)
                .HasForeignKey(c => c.ConstituencyId)
                .OnDelete(DeleteBehavior.Restrict);
            cc.HasOne(c => c.SeatType)
                .WithMany(c => c.ResultLogs)
                .HasForeignKey(c => c.SeatTypeId)
                .OnDelete(DeleteBehavior.Restrict);
        });
        mb.Entity<ResultLogDetail>(cc =>
        {
            cc.HasOne(c => c.Candidate).WithMany(c => c.ResultLogDetails).HasForeignKey(c => c.CandidateId)
                .OnDelete(DeleteBehavior.Cascade);
        });
        mb.Entity<Namanigar>(cc =>
        {
            cc.Property(c => c.Code).HasMaxLength(20).IsUnicode(false).IsRequired();
            cc.Property(c => c.Name).HasMaxLength(200).IsUnicode(false).IsRequired();
            cc.Property(c => c.Phone).HasMaxLength(150).IsRequired().IsUnicode(false);
            cc.Property(c => c.Phone2).HasMaxLength(150).IsUnicode(false);
            cc.Property(c => c.Phone3).HasMaxLength(150).IsUnicode(false);
            cc.Property(c => c.Phone4).HasMaxLength(150).IsUnicode(false);
            cc.Property(c => c.Email).HasMaxLength(300).IsRequired(false).IsUnicode(false);
            cc.Property(c => c.CreatedBy).HasMaxLength(450).IsRequired().IsUnicode(false);
            cc.Property(c => c.CreatedDate).HasDefaultValueSql("getdate()");
            cc.Property(c => c.ModifiedBy).HasMaxLength(450).IsRequired(false).IsUnicode(false);
            cc.Property(c => c.District).HasMaxLength(200).IsRequired().IsUnicode();
            cc.Property(c => c.Address).HasMaxLength(200).IsRequired(false);
            cc.HasMany(c => c.NamanigarPerformances)
                .WithOne(c => c.Namanigar)
                .HasForeignKey(c => c.NamanigarId)
                .OnDelete(DeleteBehavior.Restrict);
        });

        mb.Entity<NamanigarPerformance>(e =>
        {
            e.HasKey(c => new { c.NamanigarId, c.ElectionPhaseId });
            e.Property(c => c.BehavioralIssueDescription).HasMaxLength(2000);
            e.Property(c => c.Trivia).HasMaxLength(2000);
            e.Property(c => c.ModifiedBy).HasMaxLength(450).IsRequired(false).IsUnicode(false);
            e.Property(c => c.CreatedBy).HasMaxLength(450).IsRequired().IsUnicode(false);
        });

        mb.Entity<NamanigarConstituency>(cc =>
        {
            cc.HasKey(c => new { c.NamanigarId, c.ConstituencyId });
            cc.Property(c => c.PSDetail).HasMaxLength(500).IsUnicode(false).IsRequired(false);
            cc.Property(c => c.CreatedBy).IsRequired().IsUnicode(false).HasMaxLength(450);
            cc.Property(c => c.ModifiedBy).IsRequired(false).IsUnicode(false).HasMaxLength(450);
            cc.Property(c => c.CreatedDate).HasDefaultValueSql("getdate()");
            cc.HasOne(c => c.Namanigar).WithMany(c => c.NamanigarConstituencies).HasForeignKey(c => c.NamanigarId)
                .OnDelete(DeleteBehavior.Restrict);
            cc.HasOne(c => c.Constituency).WithMany(c => c.NamanigarConstituencies).HasForeignKey(c => c.ConstituencyId)
                .OnDelete(DeleteBehavior.Restrict);
        });
        mb.Entity<CandidateDistrict>(cc =>
        {
            cc.HasKey(c => new { c.CandidateId, c.DistrictId, c.ElectionId });
            cc.HasOne(c => c.District).WithMany(c => c.CandidateDistricts).HasForeignKey(c => c.DistrictId)
                .OnDelete(DeleteBehavior.Restrict);
            cc.HasOne(c => c.Election).WithMany(c => c.CandidateDistricts).HasForeignKey(c => c.ElectionId)
                .OnDelete(DeleteBehavior.Cascade);
            cc.HasOne(c => c.Candidate).WithMany(c => c.CandidateDistricts).HasForeignKey(c => c.CandidateId)
                .OnDelete(DeleteBehavior.Cascade);
            cc.Property(c => c.CreatedBy).IsRequired().IsUnicode(false).HasMaxLength(450);
            cc.Property(c => c.ModifiedBy).IsRequired(false).IsUnicode(false).HasMaxLength(450);
            cc.Property(c => c.CreatedDate).HasDefaultValueSql("getdate()");
            cc.HasIndex(c => new { c.ElectionId, c.CandidateId }).IsUnique();
        });
        mb.Entity<LiveResult>(cc =>
        {
            cc.Property(c => c.Assembly).HasMaxLength(400).IsRequired();
            cc.Property(c => c.Election).HasMaxLength(400).IsRequired();
            cc.Property(c => c.Phase).HasMaxLength(400).IsRequired();
            cc.Property(c => c.SeatType).HasMaxLength(400).IsRequired();
            cc.Property(c => c.Constituency).HasMaxLength(400).IsRequired();
            cc.Property(c => c.Winner).HasMaxLength(400).IsRequired();
            cc.Property(c => c.WinnerParty).HasMaxLength(400).IsRequired();
            cc.Property(c => c.RunnerUp).HasMaxLength(400).IsRequired();
            cc.Property(c => c.RunnerUpParty).HasMaxLength(400).IsRequired();
            cc.Property(c => c.PublishedByUser).HasMaxLength(450).IsUnicode(false).IsRequired(false);
            cc.HasKey(c => new
            {
                c.ElectionId,
                c.PhaseId,
                c.AssemblyId,
                c.ConstituencyId,
                c.SeatTypeId
            });
            cc.Property(c => c.CreatedBy).IsRequired().IsUnicode(false).HasMaxLength(450);
            cc.Property(c => c.ModifiedBy).IsRequired(false).IsUnicode(false).HasMaxLength(450);
            cc.Property(c => c.CreatedDate).HasDefaultValueSql("getdate()");
            cc.Property(c => c.District).HasMaxLength(200).IsRequired(false);
        });

        mb.Entity<PSResult>(cc =>
        {
            cc.HasKey(c => new { c.PollingStationId, c.NominationId });
            cc.HasOne(c => c.Nomination).WithMany(c => c.PSResults).HasForeignKey(c => c.NominationId)
                .OnDelete(DeleteBehavior.Restrict);
            cc.HasOne(c => c.PollingStation).WithMany(c => c.PSResults).HasForeignKey(c => c.PollingStationId)
                .OnDelete(DeleteBehavior.Restrict);
            cc.Property(c => c.CreatedBy).IsRequired().IsUnicode(false).HasMaxLength(450);
            cc.Property(c => c.ModifiedBy).IsRequired(false).IsUnicode(false).HasMaxLength(450);
            cc.Property(c => c.CreatedDate).HasDefaultValueSql("getdate()");
        });

        mb.Entity<PollingScheme>(e =>
        {
            e.HasKey(p => new { p.StructureId, p.PhaseId });
            e.HasOne(p => p.Phase).WithMany(p => p.PollingSchemes).HasForeignKey(p => p.PhaseId)
                .OnDelete(DeleteBehavior.Restrict);
            e.HasOne(p => p.Structure).WithMany(p => p.PollingSchemes).HasForeignKey(p => p.StructureId)
                .OnDelete(DeleteBehavior.Restrict);
            e.Property(p => p.Languages).HasMaxLength(200);
            e.Property(p => p.Castes).HasMaxLength(200);
            e.Property(p => p.Trivia).HasColumnType("ntext");
            e.Property(p => p.GeneralTrivia).HasColumnType("ntext");
            e.Property(p => p.ImportantPoliticalPersonalities).HasMaxLength(4000);
            e.Property(p => p.Problems).HasMaxLength(4000);
            e.Property(p => p.ImportantAreas).HasMaxLength(4000);
            e.Property(p => p.MajorityIncomeSource).HasMaxLength(2000);
            e.Property(p => p.LiteracyRate).HasMaxLength(2000);
            e.Property(p => p.AverageHouseholdIncome).HasMaxLength(2000);
            e.Property(p => p.Area).HasMaxLength(2000);
            e.Property(p => p.Profile).HasColumnType("ntext");
        });

        mb.Entity<PhaseWiseNamanigar>(e =>
        {
            e.HasKey(c => new { c.NamanigarId, c.PhaseId });
            e.HasOne(c => c.Namanigar).WithMany(c => c.PhaseWiseNamanigars)
                .HasForeignKey(c => c.NamanigarId)
                .OnDelete(DeleteBehavior.Restrict);
            e.HasOne(c => c.Phase).WithMany(c => c.PhaseWiseNamanigars)
                .HasForeignKey(c => c.PhaseId)
                .OnDelete(DeleteBehavior.Restrict);
            
        });
    }

    //partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
}