﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ElectionAppServer.DTO;
using ElectionAppServer.Model;
using Microsoft.EntityFrameworkCore;

namespace ElectionAppServer.Data;

public class UnionCouncilService
{
    private readonly IDbContextFactory<ApplicationDbContext>
        _contextFactory; // private  readonly  ApplicationDbContext dc;

    public UnionCouncilService(
        IDbContextFactory<ApplicationDbContext> contextFactory) // (ApplicationDbContext  context)
    {
        _contextFactory = contextFactory; // dc  =  context;
    }

    public Task<List<GeneralItemDTO>> GetList(int townId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.Structures.OfType<UnionCouncil>()
            orderby aa.EnglishTitle
            where aa.TownId == townId
            select new GeneralItemDTO
                { Id = aa.Id, EnglishTitle = aa.EnglishTitle, UrduTitle = aa.UrduTitle }).ToList();
        return Task.FromResult(q);
    }

    public Task<UnionCouncil> Create(UnionCouncil obj)
    {
        using var dc = _contextFactory.CreateDbContext();
        dc.Structures.Add(obj);
        dc.SaveChanges();
        return Task.FromResult(obj);
    }

    public Task<UnionCouncil> Update(UnionCouncil obj)
    {
        using var dc = _contextFactory.CreateDbContext();
        var st = (from aa in dc.Structures.OfType<UnionCouncil>() where aa.Id == obj.Id select aa).FirstOrDefault();
        st.UrduTitle = obj.UrduTitle.Trim();
        st.EnglishTitle = obj.EnglishTitle.Trim();
        st.ModifiedBy = obj.ModifiedBy;
        st.ModifiedDate = DateTime.Now;
        st.TownId = obj.TownId;
        dc.SaveChanges();
        return Task.FromResult(obj);
    }

    public async Task<UnionCouncil> Delete(int Id)
    {
        using var dc = _contextFactory.CreateDbContext();
        var st = await (from aa in dc.Structures.OfType<UnionCouncil>() where aa.Id == Id select aa)
            .FirstOrDefaultAsync();
        dc.Structures.Remove(st);
        await dc.SaveChangesAsync();
        return await Task.FromResult(st);
    }

    public Task<List<GeneralItemDTO>> GetStructure(int townId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var op = new List<GeneralItemDTO>();
        var q = (from aa in dc.Structures.OfType<Town>()
            where aa.Id == townId
            select new
            {
                TownId = aa.Id,
                Town = aa.EnglishTitle,
                aa.DistrictId,
                District = aa.District.EnglishTitle,
                aa.District.DivisionId,
                Division = aa.District.Division.EnglishTitle,
                aa.District.Division.ProvinceId,
                Province = aa.District.Division.Province.EnglishTitle
            }).FirstOrDefault();
        op.Add(new GeneralItemDTO { Id = q.ProvinceId, EnglishTitle = q.Province, UrduTitle = "Province" });
        op.Add(new GeneralItemDTO { Id = q.DivisionId, EnglishTitle = q.Division, UrduTitle = "Division" });
        op.Add(new GeneralItemDTO { Id = q.DistrictId, EnglishTitle = q.District, UrduTitle = "District" });
        op.Add(new GeneralItemDTO { Id = q.TownId, EnglishTitle = q.Town, UrduTitle = "Town" });

        return Task.FromResult(op);
    }
}