﻿using System;

namespace ElectionAppServer.Model;

public class RePoll
{
    public int PhaseId { get; set; }
    public int ConstituencyId { get; set; }
    public DateTime CreatedDate { get; set; }
    public string CreatedBy { get; set; }
    public virtual ElectionPhase Phase { get; set; }
    public virtual Structure Constituency { get; set; }
}

public class RePollSeat
{
    public int PhaseId { get; set; }
    public int ConstituencyId { get; set; }
    public int SeatTypeId { get; set; }
    public DateTime CreatedDate { get; set; }
    public string CreatedBy { get; set; }
    public virtual ElectionPhase Phase { get; set; }
    public virtual Structure Constituency { get; set; }
    public virtual SeatType SeatType { get; set; }
}