﻿using ElectionAppServer.DTO;
using Microsoft.AspNetCore.SignalR;
using System.Threading.Tasks;

namespace ElectionAppServer.Hubs;

public class CandidteHub : Hub
{
	public async Task SendCandidateInfo(CandidateDTO candidate)
	{
		await Clients.All.SendAsync("ReceiveCandidate", candidate);
	}

	public async Task RemoveCandidate(int id)
	{
		await Clients.All.SendAsync("OnRemoveCandidate", id);
	}

	public async Task UpdateNomination(CandidateDTO cand)
	{
		await Clients.All.SendAsync("OnUpdateNomination", cand);
	}
}