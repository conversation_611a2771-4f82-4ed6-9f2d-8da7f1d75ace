﻿using System.ComponentModel.DataAnnotations;

namespace ElectionAppServer.DTO;

public class ApplicationUserDTO
{
    //public string UserName { get; set; }
    public string Id { get; set; }

    [StringLength(10)] public string Code { get; set; }

    [StringLength(10)] public string EmpCode { get; set; }

    [Required]
    [StringLength(300)]
    [DataType(DataType.EmailAddress)]
    public string UserNameId { get; set; }

    [Required] [StringLength(30)] public string PhoneNumber { get; set; }

    public string Address { get; set; }

    [Required] [StringLength(100)] public string FullName { get; set; }

    [Required] public int? Gender { get; set; }

    public string PasswordHash { get; set; }
    public bool IsNamanigar { get; set; } = false;

    [StringLength(200)] public string District { get; set; }

    public string Roles { get; set; }

    public string Status => IsActive ? "Active" : "In-active";

    public bool IsActive { get; set; }

    public string GenderText
    {
        get
        {
            if (Gender == 1) return "Male";
            if (Gender == 2) return "Female";
            if (Gender == 3) return "Trans-gender";
            return "";
        }
    }
}