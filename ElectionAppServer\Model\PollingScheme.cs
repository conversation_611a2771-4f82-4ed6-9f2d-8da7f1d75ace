﻿using System;
using System.ComponentModel.DataAnnotations;

namespace ElectionAppServer.Model;

public class PollingScheme
{
    public int PhaseId { get; set; }
    public int StructureId { get; set; }
    public virtual ElectionPhase Phase { get; set; }
    public virtual Structure Structure { get; set; }

    public ResultMode ResultMode { get; set; } = ResultMode.Default;
    public string ResultModeChangedBy { get; set; }
    public DateTime? ResultModeChangedDate { get; set; }
    public int? RejectedVotes { get; set; }

    #region Demographics

    public int? MaleVoters { get; set; }
    public int? FemaleVoters { get; set; }
    public int? TotalPollingStations { get; set; }
    public string Languages { get; set; }
    public string Castes { get; set; }
    public string Trivia { get; set; }
    public string GeneralTrivia { get; set; }
    public string ImportantPoliticalPersonalities { get; set; }
    public string Problems { get; set; }
    public string ImportantAreas { get; set; }
    public float? RuralAreaPer { get; set; }
    public float? UrbanAreaPer { get; set; }
    public int TotalVoters => MaleVoters ?? 0 + FemaleVoters ?? 0;
    public int? Population { get; set; }
    public string MajorityIncomeSource { get; set; }
    public string LiteracyRate { get; set; }
    public string AverageHouseholdIncome { get; set; }
    public string Area { get; set; }
    public int? YouthVoters { get; set; }
    public string Profile { get; set; }
    public bool BigContest { get; set; } = false;

    #endregion

    #region Audit Log Fields

    public DateTime CreatedDate { get; set; } = DateTime.Now;
    public DateTime? ModifiedDate { get; set; }

    [StringLength(450)] public string CreatedBy { get; set; }

    [StringLength(450)] public string ModifiedBy { get; set; }

    //public object RePollseats { get; internal set; }

    #endregion Audit Log Fields
}

public enum ResultMode
{
    Default = 0,
    Lumsum = 1,
    PollingStationWise = 2
}