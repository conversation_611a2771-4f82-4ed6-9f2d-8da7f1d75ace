﻿@page "/imm"

@inject NavigationManager NavigationManager
@inject UserManager<ApplicationUser> _UserManager
@inject RoleManager<IdentityRole> _RoleManager
@inject AuthenticationStateProvider AuthenticationStateProvider

@inherits OwningComponentBase<ElectionResultsDataService>

@*<PageCaption Title="Live Results"></PageCaption>*@
<MudText Typo="Typo.h5">Live Results</MudText>

<section style="padding: 15px 1rem; margin-top: -3px;">
    <table class="table table-sm">
        <thead>
        <tr>
            <th>Assembly</th>
            <th>Constituency</th>
            <th>Winner</th>
            <th>Winner Party</th>
            <th>Winner Votes</th>
            <th>Runner Up</th>
            <th>Runner Party</th>
            <th>Runner Votes</th>
            <th>PS %</th>
            <th>Total PS</th>
            <th>PS Result</th>
            <th>Published</th>
            <th>Updated Time</th>
        </tr>
        </thead>
        <tbody>
        @foreach (var r in results)
        {
            var winner = "./media/candidates/Blank.jpg";
            var runnerup = "./media/candidates/Blank.jpg";
            ;
            if (r.WinnerCandidateId != null && r.WinnerCandidateId != 0)
                winner = $"./media/candidtes/{r.WinnerCandidateId}.jpg";

            if (r.RunnerUpCandidateId != null && r.RunnerUpCandidateId != 0)
                runnerup = $"./media/candidtes/{r.RunnerUpCandidateId}.jpg";
            <tr>
                <td>@r.Assembly</td>
                <td>@r.Constituency</td>
                <td>
                    <img src="@winner" style="width: 80px" align="left" alt=""/> @r.WinnerName
                </td>
                <td>@r.WinnerParty</td>
                <td>@r.WinnerVotes</td>
                <td>
                    <img src="@runnerup" style="width: 80px" align="left" alt=""/>@r.RunnerUpName
                </td>
                <td>@r.RunnerUpParty</td>
                <td>@r.RunnerUpVotes</td>
                <td>@Math.Round(r.PercentageCompletedPollingStations, 3)</td>
                <td>@r.TotalPollingStations</td>
                <td>@r.ResultOfPollingStations</td>
                <td>@r.Status</td>
                <td>@r.LastUpdatedOn.ToString("d MMM, yyyy h:mm:ss")</td>
            </tr>
        }
        </tbody>
    </table>
</section>

@code {

    private HubConnection hubConnection;
    private List<ResultDTO> results = new();

    [CascadingParameter] private Task<AuthenticationState> authenticationStateTask { get; set; }

    protected override async Task OnInitializedAsync()
    {
        hubConnection = new HubConnectionBuilder()
            .WithUrl(NavigationManager.ToAbsoluteUri("/resultshub"))
            .Build();


        hubConnection.On<ResultDTO>("ReceiveResult", result =>
        {
            //var ok = result;
            //results.Add(result);
            var isExist = (from r in results
                where r.ElectionId == result.ElectionId &&
                      r.AssemblyId == result.AssemblyId &&
                      r.ConstituencyId == result.ConstituencyId &&
                      r.SeatTypeId == result.SeatTypeId
                select r).FirstOrDefault();
            if (isExist != null)
            {
                isExist.WinnerCandidateId = result.WinnerCandidateId;
                isExist.WinnerName = result.WinnerName;
                isExist.WinnerParty = result.WinnerParty;
                isExist.WinnerVotes = result.WinnerVotes;

                isExist.RunnerUpCandidateId = result.RunnerUpCandidateId;
                isExist.RunnerUpName = result.RunnerUpName;
                isExist.RunnerUpParty = result.RunnerUpParty;
                isExist.RunnerUpVotes = result.RunnerUpVotes;
                isExist.OnAir = result.OnAir;
                isExist.Status = result.Status;
                isExist.LastUpdatedOn = result.LastUpdatedOn;
            }
            else
            {
                if (result.PhaseId == state.state.PhaseId)
                    results.Add(result);
            }

            results = (from r in results
                orderby r.Status, r.LastUpdatedOn descending
                select r).ToList();


            StateHasChanged();
        });

        hubConnection.On<ResultDTO>("ReceiveNotification", result =>
        {
            var isExist = (from r in results
                where r.ElectionId == result.ElectionId &&
                      r.AssemblyId == result.AssemblyId &&
                      r.ConstituencyId == result.ConstituencyId &&
                      r.SeatTypeId == result.SeatTypeId
                select r).FirstOrDefault();


            results = (from r in results
                orderby r.OnAir descending, r.LastUpdatedOn descending
                select r).ToList();


            StateHasChanged();
        });

        await hubConnection.StartAsync();
        results = await Service.GetLiveResults(state.state.PhaseId);
        StateHasChanged();
        await Task.CompletedTask;
    }

    //private async Task MarkPublish(int electionId, int phaseId, int assemblyId, int constituencyId, int seatTypeId)
    //{
    //	var user = (await authenticationStateTask).User;
    //	var msg = await Service.MarkPublish(electionId, phaseId, assemblyId, constituencyId, seatTypeId, user.Identity.Name);
    //	if (msg == "OK")
    //	{
    //		var res = (from r in results
    //					  where r.ConstituencyId == constituencyId &&
    //							  r.ElectionId == electionId &&
    //							  r.AssemblyId == assemblyId &&
    //							  r.PhaseId == phaseId
    //					  select r).FirstOrDefault();
    //		//res.Status = "Yes";
    //		res.OnAir = true;
    //		res.Status = "Yes";

    //		results = (from aa in results
    //					  orderby aa.Status, aa.LastUpdatedOn descending
    //					  select aa).ToList();

    //		//ImmResultDTO immInfo = await Service.GetImmResult(electionId, phaseId, assemblyId, constituencyId, seatTypeId);
    //		//await Translation.PostDataToImm(immInfo);
    //	}
    //	else
    //	{

    //	}

    //	StateHasChanged();
    //}

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            var user = (await authenticationStateTask).User;
            if (user.Identity.IsAuthenticated)
            {
                var dd = await localStorage.GetAsync<ElectionStateDTO>(await confreader.GetSettingValue("electionkey"));
                state.SetState(dd.Value);
                var info = await _UserManager.GetUserAsync(user);
                results = await Service.GetLiveResults(state.state.PhaseId);
                await Task.CompletedTask;

                StateHasChanged();
            }
            else
            {
                try
                {
                    await localStorage.DeleteAsync(await confreader.GetSettingValue("electionkey"));
                    state.SetState(new ElectionStateDTO { ElectionId = 0, ElectionTitle = "", PhaseId = 0, PhaseTitle = "" });
                    StateHasChanged();
                }
                catch (Exception)
                {
                    // ignored
                }
            }
        }
    }

}