﻿@page "/parties"
@attribute [Authorize(Roles = "Administrators,Data Viewer")]
@using ButtonType = MudBlazor.ButtonType
@using FilterType = Syncfusion.Blazor.DropDowns.FilterType
@using Size = MudBlazor.Size
@inherits OwningComponentBase<PartyService>
@*<PageCaption Title="Parties"></PageCaption>*@
<MudText Class="mb-2" Typo="Typo.h5"><MudIcon Icon="@Icons.Material.Filled.Flag" Size="Size.Large"/> Parties</MudText>

<SfDialog Width="600" @bind-Visible="@isSymbolDialogOpne" @ref="dlgSymbolForm" IsModal="true" ShowCloseIcon="true">
    <DialogEvents OnOpen="beforeOpen"></DialogEvents>
    <DialogTemplates>
        <Header>Create Symbol</Header>
        <Content>
            <EditForm Model="@symbolObj" OnValidSubmit="SaveSymbolData">
                <DataAnnotationsValidator/>
                <ValidationSummary/>
                <div class="row">
                    <div class="input-field col ">
                        <SfTextBox @bind-Value="symbolObj.English" Placeholder="Title (English)" OnBlur="TranslateToUrduSymbol" FloatLabelType="FloatLabelType.Always"></SfTextBox>
                        <ValidationMessage For="@(() => symbolObj.English)"/>
                    </div>
                    <div class="input-field col ">
                        <SfTextBox @bind-Value="symbolObj.Urdu" Placeholder="Title (Urdu)" FloatLabelType="FloatLabelType.Always"></SfTextBox>
                        <ValidationMessage For="@(() => symbolObj.Urdu)"/>
                    </div>
                </div>
                <div class="row">
                    <div class="col">
                        <label>Symbol Picture</label>
                        <BlazorInputFile.InputFile OnChange="HandleFileSelectedSymbol"/>
                        <img style="max-height:150px;max-width:150px;" alt="" src="data:image/jpg;base64,@FileBase64StringSymbol"/>
                    </div>
                </div>
                <AuthorizeView Roles="Administrators" Context="kk">
                    <div class="row mb-2">
                        <div class="col">
                            <MudButton ButtonType="ButtonType.Submit" Color="Color.Primary" Size="Size.Small" Variant="Variant.Filled" StartIcon="@Icons.Material.Filled.Add">Create</MudButton>
                        </div>
                    </div>
                </AuthorizeView>
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>
<SfDialog Width="700px" @bind-Visible="@isDlgVisible" @ref="dlgForm" IsModal="true" ShowCloseIcon="true" MinHeight="95vh">
    <DialogEvents OnOpen="beforeOpen"></DialogEvents>
    <DialogTemplates>
        <Header>@FormTitle</Header>
        <Content>
            <EditForm Model="@selectedObj" OnValidSubmit="@SavePartyData">
                <DataAnnotationsValidator/>
                <ValidationSummary></ValidationSummary>
                <div class="row">
                    <div class="input-field col ">
                        <SfTextBox @bind-Value="selectedObj.EnglishTitle" Placeholder="Title (English)" OnBlur="TranslateToUrdu" FloatLabelType="FloatLabelType.Always"></SfTextBox>
                        <ValidationMessage For="@(() => selectedObj.EnglishTitle)"/>
                    </div>
                    <div class="input-field col ">
                        <SfTextBox @bind-Value="selectedObj.UrduTitle" Placeholder="Title (Urdu)" FloatLabelType="FloatLabelType.Always"></SfTextBox>
                        <ValidationMessage For="@(() => selectedObj.UrduTitle)"/>
                    </div>
                </div>
                <div class="row">
                    <div class="col">
                        <h5>Election Pool</h5>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-4 mb-2">
                        <SfCheckBox @bind-Checked="selectedObj.IsGeneral" Label="General Election"></SfCheckBox>
                    </div>
                    <div class="col-md-4 mb-2">
                        <SfCheckBox @bind-Checked="selectedObj.IsGB" Label="Gilgit/Baltistan Election"></SfCheckBox>
                    </div>
                    <div class="col-md-4 mb-2">
                        <SfCheckBox @bind-Checked="selectedObj.IsAJK" Label="AJK Election"></SfCheckBox>
                    </div>
                    <div class="col-md-4 mb-2">
                        <SfCheckBox @bind-Checked="selectedObj.IsLB" Label="Local-body KPK"></SfCheckBox>
                    </div>
                    <div class="col-md-4 mb-2">
                        <SfCheckBox @bind-Checked="selectedObj.IsLBBalochistan" Label="Local-body Balochistan"></SfCheckBox>
                    </div>
                    <div class="col-md-4 mb-2">
                        <SfCheckBox @bind-Checked="selectedObj.IsLBSindh" Label="Local-body Sindh"></SfCheckBox>
                    </div>
                    <div class="col-md-4 mb-2">
                        <SfCheckBox @bind-Checked="selectedObj.IsLBPunjab" Label="Local-body Punjab"></SfCheckBox>
                    </div>
                    <div class="col-md-4 mb-2">
                        <SfCheckBox @bind-Checked="selectedObj.IsLBAJK" Label="Local-body AJK"></SfCheckBox>
                    </div>
                    <div class="col-md-4 mb-2">
                        <SfCheckBox @bind-Checked="selectedObj.IsLBIslamabad" Label="Local-body Islamabad"></SfCheckBox>
                    </div>
                </div>
                <div class="row">
                    <div class="input-field col ">
                        <SfTextBox @bind-Value="selectedObj.ShortEnglishTitle" Placeholder="Short Title (English)" OnBlur="TranslateShort" FloatLabelType="FloatLabelType.Always"></SfTextBox>
                        <ValidationMessage For="@(() => selectedObj.ShortEnglishTitle)"/>
                    </div>
                    <div class="input-field col ">
                        <SfTextBox @bind-Value="selectedObj.ShortUrduTitle" Placeholder="Short Title (Urdu)" FloatLabelType="FloatLabelType.Always"></SfTextBox>
                        <ValidationMessage For="@(() => selectedObj.ShortUrduTitle)"/>
                    </div>
                    <div class="col">
                        <SfDatePicker @bind-Value="selectedObj.DateOfCreation" Placeholder="Date Of Creation" FloatLabelType="FloatLabelType.Always"></SfDatePicker>
                        <ValidationMessage For="@(() => selectedObj.DateOfCreation)"/>
                    </div>
                </div>
                <div class="row">
                    <div class="col">
                        <SfComboBox TItem="GeneralItemDTO" TValue="int" @bind-Value="selectedObj.TeamId" Placeholder="Team" DataSource="@teamsList" FloatLabelType="FloatLabelType.Always">
                            <ComboBoxFieldSettings Value="Id" Text="EnglishTitle"></ComboBoxFieldSettings>
                        </SfComboBox>
                    </div>
                </div>
                @*<div class="row">
                    <div class="col">
                    <SfDropDownList TItem="GeneralItemDTO" TValue="int?" Placeholder="Team" @bind-Value="@selectedObj.CoalitionId" DataSource="@coalition_list" FloatLabelType="FloatLabelType.Always">
                    <DropDownListFieldSettings Value="Id" Text="EnglishTitle"></DropDownListFieldSettings>
                    </SfDropDownList>
                    <ValidationMessage For="@(() => selectedObj.CoalitionId)" />
                    </div>
                    </div>*@
                <div class="row">
                    <div class="col">
                        <SfTextBox @bind-Value="selectedObj.Leader" Placeholder="Party Leader" FloatLabelType="FloatLabelType.Always"></SfTextBox>
                        <ValidationMessage For="@(() => selectedObj.Leader)"/>
                    </div>
                    <div class="col">
                        <SfTextBox @bind-Value="selectedObj.Designation" Placeholder="Designation" FloatLabelType="FloatLabelType.Always"></SfTextBox>
                        <ValidationMessage For="@(() => selectedObj.Designation)"/>
                    </div>
                </div>
                <div class="row">
                    <div class="col">
                        <SfTextBox @bind-Value="selectedObj.CurrentLeader" Placeholder="Current Party Leader" FloatLabelType="FloatLabelType.Always"></SfTextBox>
                        <ValidationMessage For="@(() => selectedObj.CurrentLeader)"/>
                    </div>
                    <div class="col">
                        <SfTextBox @bind-Value="selectedObj.CurrentLeaderDesignation" Placeholder="Designation" FloatLabelType="FloatLabelType.Always"></SfTextBox>
                        <ValidationMessage For="@(() => selectedObj.CurrentLeaderDesignation)"/>
                    </div>
                </div>
                <div class="row">
                    <div class="col">
                        <label>Party Leader Picture</label>
                        <BlazorInputFile.InputFile OnChange="HandleFileSelectedPartyLeader"/>
                        <img style="width:160px;height:160px;object-fit:cover" alt="" src="data:image/jpg;base64,@FileBase64StringPartyLeader"/>
                    </div>
                    <div class="col">
                        <label>Current Party Leader Picture</label>
                        <BlazorInputFile.InputFile OnChange="HandleFileSelectedCurrentPartyLeader"/>
                        <img style="width:160px;height:160px;object-fit:cover" alt="" src="data:image/jpg;base64,@FileBase64StringCurrentParyLeader"/>
                    </div>
                </div>
                <div class="row">
                    <div class="col">
                        <SfTextBox Multiline="true" @bind-Value="selectedObj.Address" Placeholder="Address" FloatLabelType="FloatLabelType.Always"></SfTextBox>
                        <ValidationMessage For="@(() => selectedObj.Address)"/>
                    </div>
                </div>
                <div class="row">
                    <div class="col">
                        <SfTextBox Multiline="true" @bind-Value="selectedObj.Trivia" Placeholder="Trival" FloatLabelType="FloatLabelType.Always"></SfTextBox>
                        <ValidationMessage For="@(() => selectedObj.Address)"/>
                    </div>
                </div>
                <div class="row">
                    <div class="input-field col  ">
                        <label>Party Flag</label>
                        <BlazorInputFile.InputFile OnChange="HandleFileSelected"/>
                        <img style="max-width:160px;max-height:160px;" alt="" src="data:image/jpg;base64,@FileBase64String"/>
                    </div>
                    <div class="input-field col  ">
                        <div style="display:flex;">
                            <SfDropDownList @bind-Value="selectedObj.SymbolId" Placeholder="Symbol" DataSource="@symbols"
                                            FloatLabelType="FloatLabelType.Always" AllowFiltering="true" FilterType="FilterType.Contains">
                                <DropDownListFieldSettings Value="Id" Text="English"></DropDownListFieldSettings>
                            </SfDropDownList>
                            <button type="button" class="btn btn-primary btn-sm" @onclick="OpenCreateSymbolForm" style="margin-left:5px; height:30px;margin-top:auto; margin-bottom:3px">
                                <i class="fas fa-plus-circle"></i>
                            </button>
                        </div>
                        <ValidationMessage For="@(() => selectedObj.SymbolId)"/>
                        @if (selectedObj.SymbolId != null)
                        {
                            var img = $"/media/symbols/{selectedObj.SymbolId}.jpg";
                            <img onerror="this.onerror=null; this.src='/media/symbols/missing-symbol.png'" style="max-width:160px;max-height:160px" alt="" src="@img"/>
                        }
                    </div>
                </div>
                <AuthorizeView Roles="Administrators" Context="kk">
                    <div class="row">
                        <div class="col ">
                            <MudButton ButtonType="ButtonType.Submit" Disabled="@IsDisable" Color="Color.Primary" Size="Size.Small" Variant="Variant.Filled" StartIcon="@Icons.Material.Filled.Save">@SaveButtonText</MudButton>
                            @*<p>@status</p>*@
                        </div>
                    </div>
                </AuthorizeView>
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>
<SfToast @ref="ToastObj" Title="Alert" ShowProgressBar="true" Timeout="3000">
    <ToastPosition X="Right" Y="Bottom"></ToastPosition>
</SfToast>
<section>
    @if (objList == null)
    {
        <p>
            <em>Loading...</em>
        </p>
    }
    else
    {
        if (!ImportMode)
        {
            <AuthorizeView Roles="Administrators" Context="kk">
                <MudButton Color="Color.Primary" Size="Size.Small" Variant="Variant.Filled"
                           StartIcon="@Icons.Material.Filled.Add"
                           Class="mb-2"

                           OnClick="OpenCreateForm">
                    Create
                </MudButton>
            </AuthorizeView>
            @*<SfButton IsPrimary="false" CssClass="e-primary mb-2" OnClick="OpenImportForm">Import <span class="oi oi-cloud-download" aria-hidden="true"></span></SfButton>*@
            <SfGrid Width="100%" Height="calc(100vh - 260px)" DataSource="@objList" AllowFiltering="true"
                    AllowSorting="true" @ref="Grid" AllowTextWrap="true"
                    AllowPaging="true">
                <GridPageSettings PageSize="6"></GridPageSettings>
                <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Excel"></GridFilterSettings>
                <GridEvents QueryCellInfo="CustomizeCell" TValue="PartyDTO"></GridEvents>
                <GridTextWrapSettings WrapMode="WrapMode.Content"></GridTextWrapSettings>
                <GridColumns>
                    <GridColumn AutoFit="true" Field="Id" HeaderText="Id"></GridColumn>
                    <GridColumn Width="170px" HeaderText="Title (English)" Field="EnglishTitle"></GridColumn>
                    <GridColumn Width="170px" HeaderText="Title (Urdu)" TextAlign="TextAlign.Right" Field="UrduTitle"></GridColumn>
                    <GridColumn AutoFit="true" HeaderText="Title (Short)" Field="ShortEnglishTitle"></GridColumn>
                    @*<GridColumn AutoFit="true" HeaderText="Creation Date" Field="DateOfCreation" Format="d MMM, yyyy" Type="ColumnType.Date"></GridColumn>*@
                    @*<GridColumn AutoFit="true" HeaderText="Team" Field="Coalition"></GridColumn>*@
                    <GridColumn AutoFit="true" HeaderText="Pool" Field=@nameof(PartyDTO.ElectionPool)></GridColumn>
                    <GridColumn HeaderText="Flag" Width="110px">
                        <Template Context="cc">
                            @{
                                var oo = cc as PartyDTO;
                                <img src="@oo.FlagURL" class="myimg" style="width:70px" alt="@oo.EnglishTitle"/>
                            }
                        </Template>
                    </GridColumn>
                    <GridColumn HeaderText="Symbol" Width="110px">
                        <Template Context="cc">
                            @{
                                var oo = cc as PartyDTO;
                                <img title="@oo.Symbol" src="@oo.SymbolURL" class="myimg" style="width:70px;" alt="@oo.EnglishTitle" align="left"/>
                            }
                        </Template>
                    </GridColumn>
                    @*<GridColumn AutoFit="true" HeaderText="Leader" Field="Leader"></GridColumn>*@
                    @*<GridColumn HeaderText="Designation" Field="Designation"></GridColumn>*@
                    <GridColumn HeaderText="" Width="150px">
                        <Template Context="ec">
                            @{
                                var obj = ec as PartyDTO;
                                <a target="@($"pp{obj.Id}")" href="report/party/@obj.Id">
                                    <span class="e-control e-btn e-lib e-success m1">
                                        <i class="fas fa-clipboard-check"></i>
                                    </span>
                                </a>
                                <AuthorizeView Roles="Administrators" Context="kk">
                                    <MudFab Title="Delete" Size="Size.Small" Color="Color.Error" StartIcon="@Icons.Material.Filled.Delete" Disabled="@obj.DisabbledDelete" OnClick="@(() => DeleteRecord(obj.Id))">

                                    </MudFab>
                                    <MudFab Title="Edit" Color="Color.Primary" Size="Size.Small" StartIcon="@Icons.Material.Filled.Edit" OnClick="@(() => OpenEditForm(obj))">
                                    </MudFab>
                                </AuthorizeView>
                            }
                        </Template>
                    </GridColumn>
                    <GridColumn AutoFit="true" HeaderText="Audit Info" Field="AuditInfo" DisableHtmlEncode="false"></GridColumn>
                </GridColumns>
            </SfGrid>
        }
    }
</section>