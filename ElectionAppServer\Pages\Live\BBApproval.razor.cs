using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ElectionAppServer.DTO;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Syncfusion.Blazor.Buttons;
using Syncfusion.Blazor.Grids;
using Syncfusion.Blazor.Popups;

namespace ElectionAppServer.Pages.Live;

public partial class BBApproval
{
    private SfDialog dlgForm;

    private bool isPendingOnly = true;

    //private HubConnection hubConnection { get; set; }
    private List<ResultDTO> results = new();

    private bool isDlgVisible { get; set; }

    private LiveResultDetailDTO detail { get; set; }

    private SfGrid<ResultDTO> pendingResultsGrid { get; set; }
    private SfGrid<ResultDTO> approvedResultsGrid { get; set; }

    [CascadingParameter] private Task<AuthenticationState> authenticationStateTask { get; set; }

    protected override async Task OnInitializedAsync()
    {
        isDlgVisible = false;
        detail = new LiveResultDetailDTO { Nominations = new List<LiveResultCandidateDetailDTO>() };
        //hubConnection = new HubConnectionBuilder()
        //    .WithUrl(NavigationManager.ToAbsoluteUri("/resultshub"))
        //    .Build();
        // not working
        //hubConnection.On<ResultDTO>("ReceiveResult", (result) =>
        //{
        //    results = Service.GetLiveResults(state.state.PhaseId).Result;
        //    try
        //    {
        //        resultsGrid.Refresh();
        //    }
        //    catch (Exception)
        //    {
        //    }
        //});
        //hubConnection.On<ResultDTO>("ReceiveResult", (result) =>
        //{
        //    dlgForm.HideAsync();
        //    //var ok = result;
        //    //results.Add(result);
        //    var isExist = (from r in results
        //                   where r.ElectionId == result.ElectionId &&
        //                     r.AssemblyId == result.AssemblyId &&
        //                     r.ConstituencyId == result.ConstituencyId &&
        //                     r.SeatTypeId == result.SeatTypeId
        //                   select r).FirstOrDefault();
        //    if (isExist != null)
        //    {
        //        isExist.WinnerCandidateId = result.WinnerCandidateId;
        //        isExist.WinnerName = result.WinnerName;
        //        isExist.WinnerParty = result.WinnerParty;
        //        isExist.WinnerVotes = result.WinnerVotes;
        //        isExist.RunnerUpCandidateId = result.RunnerUpCandidateId;
        //        isExist.RunnerUpName = result.RunnerUpName;
        //        isExist.RunnerUpParty = result.RunnerUpParty;
        //        isExist.RunnerUpVotes = result.RunnerUpVotes;
        //        isExist.OnAir = result.OnAir;
        //        isExist.Status = result.Status;
        //        isExist.LastUpdatedOn = result.LastUpdatedOn;
        //    }
        //    else
        //    {
        //        if (result.PhaseId == state.state.PhaseId)
        //            results.Add(result);
        //    }
        //    results = (from r in results
        //               orderby r.Status, r.LastUpdatedOn descending
        //               select r).ToList();
        //    StateHasChanged();
        //});
        //hubConnection.On<ResultDTO>("ReceiveNotification", (result) =>
        //{
        //    var isExist = (from r in results
        //                   where r.ElectionId == result.ElectionId &&
        //                     r.AssemblyId == result.AssemblyId &&
        //                     r.ConstituencyId == result.ConstituencyId &&
        //                     r.SeatTypeId == result.SeatTypeId
        //                   select r).FirstOrDefault();
        //    results = (from r in results
        //               orderby r.OnAir descending, r.LastUpdatedOn descending
        //               select r).ToList();
        //    StateHasChanged();
        //});
        //await hubConnection.StartAsync();
        try
        {
        }
        catch (Exception)
        {
            // ignored
        }

        StateHasChanged();
        await Task.CompletedTask;
    }

    private async Task DisplayDetail(int assemblyId, int constituencyId, int seatTypeId)
    {
        detail = await Service.GetLiveResultDetail(state.state.ElectionId, assemblyId, state.state.PhaseId,
            constituencyId, seatTypeId);
        await dlgForm.ShowAsync();
    }

    //private async Task MarkPublish(int electionId, int phaseId, int assemblyId, int constituencyId, int seatTypeId)
    //{
    //    var user = (await authenticationStateTask).User;
    //    var msg = await Service.MarkPublish(electionId, phaseId, assemblyId, constituencyId, seatTypeId, user.Identity.Name);
    //    if (msg == "OK")
    //    {
    //        var res = (from r in results
    //                   where r.ConstituencyId == constituencyId &&
    //                         r.ElectionId == electionId &&
    //                         r.AssemblyId == assemblyId &&
    //                         r.PhaseId == phaseId
    //                   select r).FirstOrDefault();
    //        //res.Status = "Yes";
    //        if (res != null)
    //        {
    //            res.OnAir = true;
    //            res.Status = "Yes";
    //        }
    //        results = (from aa in results
    //                   orderby aa.Status, aa.LastUpdatedOn descending
    //                   select aa).ToList();
    //        var allowToPush = await confreader.GetSettingValue("AllowToPush");
    //        if (allowToPush == "Yes")
    //        {
    //            ImmResultDTO immInfo = await Service.GetImmResult(electionId, phaseId, assemblyId, constituencyId, seatTypeId);
    //            await Translation.PostData(immInfo);
    //        }
    //    }
    //    StateHasChanged();
    //}
    private void beforeOpen(BeforeOpenEventArgs args)
    {
        args.MaxHeight = null;
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            var user = (await authenticationStateTask).User;
            if (user.Identity is { IsAuthenticated: true })
            {
                var dd = await localStorage.GetAsync<ElectionStateDTO>(await confreader.GetSettingValue("electionkey"));
                state.SetState(dd.Value);
                var info = await _UserManager.GetUserAsync(user);
                //results = await Service.GetLiveResults(state.state.PhaseId);
                results = await Service.GetPendingBBApprovals(state.state.PhaseId);
                if (isPendingOnly)
                    results = results.Where(nn => nn.BBStatus == "Pending").ToList();
                //await Task.CompletedTask;
                StateHasChanged();
            }
            else
            {
                try
                {
                    await localStorage.DeleteAsync(await confreader.GetSettingValue("electionkey"));
                    state.SetState(new ElectionStateDTO
                        { ElectionId = 0, ElectionTitle = "", PhaseId = 0, PhaseTitle = "" });
                    StateHasChanged();
                }
                catch (Exception)
                {
                    // ignored
                }
            }
        }
    }

    private async Task ReloadResult()
    {
        //results = await Service.GetLiveResults(state.state.PhaseId);
        results = await Service.GetPendingBBApprovals(state.state.PhaseId);
        if (isPendingOnly)
            results = results.Where(nn => nn.BBStatus == "Pending").ToList();
    }

    private async Task SetBlockToBB(int constituencyId, int seatTypeId)
    {
        var user = (await authenticationStateTask).User;
        var op = await Service.BlockConstituencyForBB(constituencyId, seatTypeId, user.Identity.Name);
        results = await Service.GetPendingBBApprovals(state.state.PhaseId);
        if (isPendingOnly)
            results = results.Where(nn => nn.BBStatus == "Pending").ToList();
    }

    private async Task SetAllowToBB(int constituencyId, int seatTypeId)
    {
        var user = (await authenticationStateTask).User;
        var op = await Service.ApproveConstituencyForBB(constituencyId, seatTypeId, user.Identity.Name);
        results = await Service.GetPendingBBApprovals(state.state.PhaseId);
        if (isPendingOnly)
            results = results.Where(nn => nn.BBStatus == "Pending").ToList();
    }


    private async Task OnIsPendingOnlyChangeAsync(ChangeEventArgs<bool?> args)
    {
        // Your code here.
        await ReloadResult();
    }
}