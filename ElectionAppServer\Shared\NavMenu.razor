﻿@inject UserManager<ApplicationUser> _UserManager
@inject RoleManager<IdentityRole> _RoleManager
@inject AuthenticationStateProvider AuthenticationStateProvider

<AuthorizeView>
    <Authorized>
        <div class="top-row pl-4 navbar navbar-dark">
            <button class="navbar-toggler" @onclick="ToggleNavMenu">
                <span class="navbar-toggler-icon"></span>
            </button>
            @if (!string.IsNullOrEmpty(state.state.ElectionTitle))
            {
                <div style="display: flex; grid-gap: 5px; align-content: center; align-items: start;">
                    <SfButton CssClass="e-primary" OnClick="NavigateToHome">Change</SfButton>
                    <h4 style="color:white">
                        @state.state.ElectionTitle <span>-</span> @state.state.PhaseTitle
                    </h4>
                </div>
            }
            else
            {
                <h4 style="color:white">No election is selected</h4>
            }
            <span class="navbar-brand">
                @context.User.Identity.Name
            </span>
        </div>
        <div class="@NavMenuCssClass" @onclick="ToggleNavMenu">
            <ul class="nav flex-column">
                @if (!string.IsNullOrEmpty(state.state.ElectionTitle))
                {
                    var link1 = $"administrativeunits/{state.state.ElectionId}";
                    var link2 = $"assemblies/{state.state.ElectionId}";
                    @*<li class="nav-item px-3">
                     <NavLink class="nav-link" href="administrativeunits" Match="NavLinkMatch.All">
                        <span class="oi oi-map-marker" aria-hidden="true"></span> Structures
                     </NavLink>
                  </li>*@
                    @*<li class="nav-item px-3">
                     <NavLink class="nav-link" href="elections" Match="NavLinkMatch.All">
                        <span class="oi oi-map-marker" aria-hidden="true"></span> Elections
                     </NavLink>
                  </li>*@

                    <li class="nav-item px-3">
                        <NavLink class="nav-link" href="results" Match="NavLinkMatch.All">
                            <span class="oi oi-excerpt" aria-hidden="true"></span> Results
                        </NavLink>
                    </li>
                    @if (context.User.IsInRole(ADMINISTRATION_ROLE))
                    {
                        <li class="nav-item px-3">
                            <NavLink class="nav-link" href="@link1" Match="NavLinkMatch.All">
                                <span class="oi oi-map" aria-hidden="true"></span> Administrative Units
                            </NavLink>
                        </li>
                        <li class="nav-item px-3">
                            <NavLink class="nav-link" href="regions" Match="NavLinkMatch.All">
                                <span class="oi oi-loop-square" aria-hidden="true"></span> Regions
                            </NavLink>
                        </li>
                        <li class="nav-item px-3">
                            <NavLink class="nav-]link" href="@link2" Match="NavLinkMatch.All">
                                <span class="oi oi-grid-three-up" aria-hidden="true"></span> Assemblies
                            </NavLink>
                        </li>
                        <li class="nav-item px-3">
                            <NavLink class="nav-link" href="search" Match="NavLinkMatch.All">
                                <span class="oi oi-map-marker" aria-hidden="true"></span> Constituencies
                            </NavLink>
                        </li>
                        <li class="nav-item px-3">
                            <NavLink class="nav-link" href="nominations2" Match="NavLinkMatch.All">
                                <span class="oi oi-tags" aria-hidden="true"></span> Nominations
                            </NavLink>
                        </li>
                        <li class="nav-item px-3">
                            <NavLink class="nav-link" href="parties" Match="NavLinkMatch.All">
                                <span class="oi oi-flag" aria-hidden="true"></span> Parties
                            </NavLink>
                        </li>
                        <li class="nav-item px-3">
                            <NavLink class="nav-link" href="teams" Match="NavLinkMatch.All">
                                <span class="oi oi-shield" aria-hidden="true"></span> Teams
                            </NavLink>
                        </li>
                        <li class="nav-item px-3">
                            <NavLink class="nav-link" href="candidates" Match="NavLinkMatch.All">
                                <span class="oi oi-people" aria-hidden="true"></span> Candidates
                            </NavLink>
                        </li>
                        @*<li class="nav-item px-3">
                        <NavLink class="nav-link" href="localbodytypes" Match="NavLinkMatch.All">
                           <span class="oi oi-puzzle-piece" aria-hidden="true"></span> Assembly Types
                        </NavLink>
                     </li>*@

                        <li class="nav-item px-3">
                            <NavLink class="nav-link" href="seattypes" Match="NavLinkMatch.All">
                                <span class="oi oi-badge" aria-hidden="true"></span> Seat Types
                            </NavLink>
                        </li>

                        <li class="nav-item px-3">
                            <NavLink class="nav-link" href="assemblytypes" Match="NavLinkMatch.All">
                                <span class="oi oi-badge" aria-hidden="true"></span> Assembly Types
                            </NavLink>
                        </li>

                        <li class="nav-item px-3">
                            <NavLink class="nav-link" href="symbols" Match="NavLinkMatch.All">
                                <span class="oi oi-question-mark" aria-hidden="true"></span> Symbols
                            </NavLink>
                        </li>
                        <li class="nav-item px-3">
                            <NavLink class="nav-link" href="castes" Match="NavLinkMatch.All">
                                <span class="oi oi-location" aria-hidden="true"></span> Castes
                            </NavLink>
                        </li>
                        <li class="nav-item px-3">
                            <NavLink class="nav-link" href="languages" Match="NavLinkMatch.All">
                                <span class="oi oi-globe" aria-hidden="true"></span> Languages
                            </NavLink>
                        </li>
                        <li class="nav-item px-3">
                            <NavLink class="nav-link" href="educations" Match="NavLinkMatch.All">
                                <span class="oi oi-book" aria-hidden="true"></span> Educations
                            </NavLink>
                        </li>
                        <li class="nav-item px-3">
                            <NavLink class="nav-link" href="professions" Match="NavLinkMatch.All">
                                <span class="oi oi-briefcase" aria-hidden="true"></span> Professions
                            </NavLink>
                        </li>
                        <li class="nav-item px-3">
                            <NavLink class="nav-link" href="users" Match="NavLinkMatch.All">
                                <span class="oi oi-person" aria-hidden="true"></span> Users
                            </NavLink>
                        </li>
                    }
                }
                <li class="nav-item px-3">

                    <form method="post" action="Identity/Account/LogOut">
                        <button type="submit" class="nav-link"><span class="oi oi-account-logout" aria-hidden="true"></span> Log out</button>
                    </form>
                </li>
            </ul>
        </div>
    </Authorized>
    <NotAuthorized>
        @*<a href="Identity/Account/Register">Register</a>*@
        <div class="top-row pl-4 navbar navbar-dark">
            <button class="navbar-toggler" @onclick="ToggleNavMenu">
                <span class="navbar-toggler-icon"></span>
            </button>

            <a class="navbar-brand" href="">
                <span class="oi oi-box" aria-hidden="true"></span> Election Portal
            </a>
        </div>
        <div class="@NavMenuCssClass" @onclick="ToggleNavMenu">
            <ul class="nav flex-column">
                <li class="nav-item px-3">
                    <NavLink class="nav-link" href="Identity/Account/Login" Match="NavLinkMatch.All">
                        <span class="oi oi-account-login" aria-hidden="true"></span> Login
                    </NavLink>
                </li>
            </ul>
        </div>
    </NotAuthorized>
</AuthorizeView>

@code {
    private bool collapseNavMenu = true;
    readonly string ADMINISTRATION_ROLE = "Administrators";
    [CascadingParameter] private Task<AuthenticationState> authenticationStateTask { get; set; }

    private string NavMenuCssClass => collapseNavMenu ? "collapse" : null;

    private void ToggleNavMenu()
    {
        collapseNavMenu = !collapseNavMenu;
    }

    protected override void OnInitialized()
    {
        state.OnChange += StateHasChanged;
    }

    private void NavigateToHome()
    {
        collapseNavMenu = true;

        NavigationManager.NavigateTo("/");
        StateHasChanged();
    }

}