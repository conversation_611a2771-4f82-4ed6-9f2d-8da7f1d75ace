﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ElectionAppServer.Data;
using ElectionAppServer.DTO;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.SignalR.Client;
using Syncfusion.Blazor.Popups;

namespace ElectionAppServer.Pages.Live;

public partial class FilterTickers
{
    private List<ResultDTO> allTickers = new();
    private List<GeneralItemDTO> AssembliesList = new();
    private int? AssemblyId;

    //private List<GeneralItemDTO> assemblyTypes = new List<GeneralItemDTO>
    //{
    //	new GeneralItemDTO { Id = 1, EnglishTitle = "National Assembly" },
    //	new GeneralItemDTO { Id = 2, EnglishTitle = "Provincial Assembly" }
    //};

    private List<AssemblyAndSeatTypeDTO> assemblyTypes = new();
    private int? DistrictId;
    private List<GeneralItemDTO> DistrictsList = new();
    private List<ResultDTO> filterTickers = new();
    private HubConnection hubConnection;
    private int? ProvinceId;
    private List<GeneralItemDTO> ProvincesList = new();

    [CascadingParameter] private Task<AuthenticationState> authenticationStateTask { get; set; }

    private LiveResultDetailDTO detail { get; set; }
    private bool isDlgVisible { get; set; }
    private List<ResultDTO> tickers { get; set; }

    private async Task FilterDistrics()
    {
        if (ProvinceId == null)
            DistrictsList = new List<GeneralItemDTO>();
        else
            DistrictsList = await Service.GetDistricts(ProvinceId, state.state.ElectionId);
        ApplyTickerFilter();
    }

    public async Task RefreshTickers()
    {
        allTickers = await Service.GetLiveResultsLB(state.state.PhaseId);

        ApplyTickerFilter();
    }

    private async Task PublishTicker(int constituencyId, int phaseId)
    {
        var c = await DialogService.ConfirmAsync("Are you sure want to mark ticker as Publish?", "Confirm");
        if (c)
        {
            var res = await Service.MarkTickerAsPublish(constituencyId, phaseId);
            if (res == "OK")
            {
                var obj = allTickers.FirstOrDefault(m => m.ConstituencyId == constituencyId && m.PhaseId == phaseId);
                if (obj != null)
                {
                    allTickers.Remove(obj);
                    ApplyTickerFilter();
                }
            }
        }
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            var user = (await authenticationStateTask).User;
            if (user.Identity.IsAuthenticated)
            {
                var dd = await localStorage.GetAsync<ElectionStateDTO>(await confreader.GetSettingValue("electionkey"));
                state.SetState(dd.Value);
                ProvincesList = await Service.GetAllProvincs(state.state.PhaseId);
                AssembliesList = await Service.GetAllAssemblies(state.state.PhaseId);
                var info = await _UserManager.GetUserAsync(user);
                //try
                //{

                allTickers = await Service.GetLiveResultsLB(state.state.PhaseId);

                ApplyTickerFilter();
                // Get assemblyTypes from service based on phaseId
                //assemblyTypes = (await Service.GetAssemblyTypes(state.state.PhaseId)).OrderBy(l => l.EnglishTitle).ToList();
                assemblyTypes = await Service.GetAssemblyAndSeatTypes(state.state.PhaseId);

                //}
                //catch (Exception ex)
                //{
                //}

                await Task.CompletedTask;
                StateHasChanged();
            }
            else
            {
                try
                {
                    await localStorage.DeleteAsync(await confreader.GetSettingValue("electionkey"));
                    state.SetState(new ElectionStateDTO
                        { ElectionId = 0, ElectionTitle = "", PhaseId = 0, PhaseTitle = "" });
                    StateHasChanged();
                }
                catch (Exception)
                {
                    // ignored
                }
            }
        }
    }

    private async void ApplyTickerFilter()
    {
        filterTickers = (from r in allTickers
            orderby r.LastUpdatedOn descending
            where (ProvinceId == null || r.ProvinceId == ProvinceId) &&
                  (DistrictId == null || r.DistrictId == DistrictId) &&
                  (AssemblyId == null || r.AssemblyId == AssemblyId)
            select r).ToList();
        await InvokeAsync(() => { StateHasChanged(); });
    }

    protected override async Task OnInitializedAsync()
    {
        isDlgVisible = false;
        detail = new LiveResultDetailDTO { Nominations = new List<LiveResultCandidateDetailDTO>() };
        hubConnection = new HubConnectionBuilder().WithUrl(NavigationManager.ToAbsoluteUri("/resultshub")).Build();


        hubConnection.On<ResultDTO>("ReceiveResult", async result =>
        {
            //var ok = result;
            //results.Add(result);
            var isExist = (
                from r in allTickers
                where r.ConstituencyId == result.ConstituencyId && r.SeatTypeId == result.SeatTypeId &&
                      r.PhaseId == result.PhaseId
                select r).FirstOrDefault();
            //var op = Service.GetLiveResultDetail(result.ElectionId, result.AssemblyId, result.PhaseId, result.ConstituencyId, result.SeatTypeId).Result;
            if (isExist != null)
            {
                //result.Ticker = op.Ticker;
                isExist.Ticker = result.Ticker;
                isExist.LastUpdatedOn = result.LastUpdatedOn;
            }
            else
            {
                //if (result.PhaseId == state.state.PhaseId)
                //	results.Add(result);
                //result = new ResultDTO { AssemblyId = result.AssemblyId, SeatTypeId = result.SeatTypeId, ConstituencyId = result.ConstituencyId, ElectionId = result.ElectionId, PhaseId = result.PhaseId, LastUpdatedOn = result.LastUpdatedOn, Ticker=result.Ticker };
                if (allTickers == null || allTickers.Count == 0) allTickers = new List<ResultDTO>();
                allTickers.Add(result);
            }

            allTickers = (
                from r in allTickers
                //where r.ConstituencyId==154
                orderby r.Status, r.LastUpdatedOn descending
                select r).ToList();
            ApplyTickerFilter();
            //StateHasChanged();
            await InvokeAsync(() => { StateHasChanged(); });
        });
        hubConnection.On<ResultDTO>("ReceiveNotification", async result =>
        {
            var isExist = (
                from r in allTickers
                where r.ConstituencyId == result.ConstituencyId && r.SeatTypeId == result.SeatTypeId &&
                      r.PhaseId == result.PhaseId
                select r).FirstOrDefault();
            //allTickers = (
            //         from r in allTickers
            //             //where r.ConstituencyId==154
            //         orderby r.OnAir descending, r.LastUpdatedOn descending
            //         select r).ToList();

            if (isExist != null)
            {
                //result.Ticker = op.Ticker;
                isExist.Ticker = result.Ticker;
                isExist.LastUpdatedOn = result.LastUpdatedOn;
            }
            else
            {
                //if (result.PhaseId == state.state.PhaseId)
                //	results.Add(result);
                //result = new ResultDTO { AssemblyId = result.AssemblyId, SeatTypeId = result.SeatTypeId, ConstituencyId = result.ConstituencyId, ElectionId = result.ElectionId, PhaseId = result.PhaseId, LastUpdatedOn = result.LastUpdatedOn, Ticker=result.Ticker };
                if (allTickers == null || allTickers.Count == 0) allTickers = new List<ResultDTO>();
                allTickers.Add(result);
            }

            ApplyTickerFilter();
            //StateHasChanged();
            await InvokeAsync(() => { StateHasChanged(); });
        });
        await hubConnection.StartAsync();
        try
        {
            //results = await Service.GetLiveResults(state.state.PhaseId);
            //results = (
            //	 from kk in results
            //		 //where kk.ConstituencyId == 154
            //	 select kk).ToList();
            //StateHasChanged();
            await InvokeAsync(() => { StateHasChanged(); });
        }
        catch (Exception)
        {
            // ignored
        }

        await Task.CompletedTask;
    }

    private void beforeOpen(BeforeOpenEventArgs args)
    {
        args.MaxHeight = null;
    }

    private async Task DisplayDetail(int assemblyId, int constituencyId, int seatTypeId)
    {
        detail = await Service.GetLiveResultDetail(state.state.ElectionId, assemblyId, state.state.PhaseId,
            constituencyId, seatTypeId);
    }

    private async Task MarkPublish(int electionId, int phaseId, int assemblyId, int constituencyId, int seatTypeId)
    {
        var user = (await authenticationStateTask).User;
        var msg = await Service.MarkPublish(electionId, phaseId, assemblyId, constituencyId, seatTypeId,
            user.Identity.Name);
        if (msg == "OK")
        {
            var res = (
                from r in allTickers
                where r.ConstituencyId == constituencyId && r.ElectionId == electionId && r.AssemblyId == assemblyId &&
                      r.PhaseId == phaseId
                select r).FirstOrDefault();
            //res.Status = "Yes";
            res.OnAir = true;
            res.Status = "Yes";
            allTickers = (
                from aa in allTickers
                //where aa.ConstituencyId==154
                orderby aa.Status, aa.LastUpdatedOn descending
                select aa).ToList();
            var allowToPush = await confreader.GetSettingValue("AllowToPush");
            if (allowToPush == "Yes")
            {
                var immInfo = await Service.GetImmResult(electionId, phaseId, assemblyId, constituencyId, seatTypeId);
                await Translation.PostData(immInfo);
            }
        }

        //StateHasChanged();
        await InvokeAsync(() => { StateHasChanged(); });
    }

    private async Task CopyToClipboard(string text)
    {
        await ClipboardService.WriteTextAsync(text);
    }
}