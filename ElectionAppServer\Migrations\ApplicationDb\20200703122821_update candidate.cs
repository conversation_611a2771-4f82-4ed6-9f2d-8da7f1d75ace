﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace ElectionAppServer.Migrations.ApplicationDb
{
	public partial class updatecandidate : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.AlterColumn<string>(
				 name: "TotalAssets",
				 table: "Candidates",
				 unicode: false,
				 maxLength: 1000,
				 nullable: true,
				 oldClrType: typeof(string),
				 oldType: "nvarchar(max)",
				 oldNullable: true);

			migrationBuilder.AlterColumn<string>(
				 name: "FatherUrduName",
				 table: "Candidates",
				 maxLength: 100,
				 nullable: true,
				 oldClrType: typeof(string),
				 oldType: "nvarchar(100)",
				 oldMaxLength: 100);

			migrationBuilder.AlterColumn<string>(
				 name: "FatherEnglishName",
				 table: "Candidates",
				 unicode: false,
				 maxLength: 100,
				 nullable: true,
				 oldClrType: typeof(string),
				 oldType: "nvarchar(100)",
				 oldMaxLength: 100);

			migrationBuilder.AlterColumn<string>(
				 name: "EnglishName",
				 table: "Candidates",
				 unicode: false,
				 maxLength: 100,
				 nullable: false,
				 oldClrType: typeof(string),
				 oldType: "nvarchar(100)",
				 oldMaxLength: 100);

			migrationBuilder.AlterColumn<string>(
				 name: "ContactNumber",
				 table: "Candidates",
				 unicode: false,
				 maxLength: 50,
				 nullable: true,
				 oldClrType: typeof(string),
				 oldType: "nvarchar(max)",
				 oldNullable: true);
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.AlterColumn<string>(
				 name: "TotalAssets",
				 table: "Candidates",
				 type: "nvarchar(max)",
				 nullable: true,
				 oldClrType: typeof(string),
				 oldUnicode: false,
				 oldMaxLength: 1000,
				 oldNullable: true);

			migrationBuilder.AlterColumn<string>(
				 name: "FatherUrduName",
				 table: "Candidates",
				 type: "nvarchar(100)",
				 maxLength: 100,
				 nullable: false,
				 oldClrType: typeof(string),
				 oldMaxLength: 100,
				 oldNullable: true);

			migrationBuilder.AlterColumn<string>(
				 name: "FatherEnglishName",
				 table: "Candidates",
				 type: "nvarchar(100)",
				 maxLength: 100,
				 nullable: false,
				 oldClrType: typeof(string),
				 oldUnicode: false,
				 oldMaxLength: 100,
				 oldNullable: true);

			migrationBuilder.AlterColumn<string>(
				 name: "EnglishName",
				 table: "Candidates",
				 type: "nvarchar(100)",
				 maxLength: 100,
				 nullable: false,
				 oldClrType: typeof(string),
				 oldUnicode: false,
				 oldMaxLength: 100);

			migrationBuilder.AlterColumn<string>(
				 name: "ContactNumber",
				 table: "Candidates",
				 type: "nvarchar(max)",
				 nullable: true,
				 oldClrType: typeof(string),
				 oldUnicode: false,
				 oldMaxLength: 50,
				 oldNullable: true);
		}
	}
}
