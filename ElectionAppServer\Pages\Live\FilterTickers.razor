﻿@page "/filterticker"
@attribute [Authorize(Roles = "Ticker Desk")]

@inject NavigationManager NavigationManager
@inject UserManager<ApplicationUser> _UserManager
@inject RoleManager<IdentityRole> _RoleManager
@inject AuthenticationStateProvider AuthenticationStateProvider

@using FilterType = Syncfusion.Blazor.DropDowns.FilterType
@using Size = MudBlazor.Size
@inherits OwningComponentBase<ElectionResultsDataService>
@inject ClipboardService ClipboardService

<section>
    <div style="display: flex; gap:10px;">
        <MudText Typo="Typo.h5">Live Ticker</MudText> <MudButton Size="Size.Small" Variant="Variant.Text" Color="Color.Primary" StartIcon="@Icons.Material.Filled.Refresh" OnClick="RefreshTickers">Refresh</MudButton>
    </div>
    <div class="row">
        <div class="col-md">
            <SfDropDownList TItem="GeneralItemDTO" TValue="int?" Placeholder="Province" @bind-Value="ProvinceId"
                            DataSource="@ProvincesList" FloatLabelType="FloatLabelType.Always" AllowFiltering="true"
                            FilterType="FilterType.Contains" ShowClearButton="true">
                <DropDownListFieldSettings Value="Id" Text="EnglishTitle"></DropDownListFieldSettings>
                <DropDownListEvents TValue="int?" TItem="GeneralItemDTO" ValueChange="@FilterDistrics"></DropDownListEvents>
            </SfDropDownList>
        </div>
        <div class="col-md">
            <SfDropDownList TItem="GeneralItemDTO" TValue="int?" Placeholder="District" @bind-Value="DistrictId"
                            DataSource="@DistrictsList" FloatLabelType="FloatLabelType.Always" AllowFiltering="true"
                            FilterType="FilterType.Contains" ShowClearButton="true">
                <DropDownListFieldSettings Value="Id" Text="EnglishTitle"></DropDownListFieldSettings>
                <DropDownListEvents TValue="int?" TItem="GeneralItemDTO" ValueChange="@ApplyTickerFilter"></DropDownListEvents>
            </SfDropDownList>
        </div>
        <div class="col-md">
            <SfDropDownList TItem="GeneralItemDTO" TValue="int?" Placeholder="Assembly" @bind-Value="AssemblyId"
                            DataSource="@AssembliesList" FloatLabelType="FloatLabelType.Always" AllowFiltering="true"
                            FilterType="FilterType.Contains" ShowClearButton="true">
                <DropDownListFieldSettings Value="Id" Text="EnglishTitle"></DropDownListFieldSettings>
                <DropDownListEvents TValue="int?" TItem="GeneralItemDTO" ValueChange="@ApplyTickerFilter"></DropDownListEvents>
            </SfDropDownList>
        </div>
    </div>
    @if (filterTickers.Any())
    {
        @foreach (var r in filterTickers)
        {
            <div class="col-12 urdu-column" style="direction: rtl; text-align: right; background-color: #87cefa; padding: 5px; line-height: 1.7; margin: 5px;">
                <MudButton Size="Size.Small" Variant="Variant.Filled" Color="Color.Secondary" StartIcon="@Icons.Material.Filled.FactCheck" OnClick="() => PublishTicker(r.ConstituencyId, state.state.PhaseId)">Mark Publish</MudButton>
                <div>
                    @((MarkupString)r.Ticker)
                    <div style="padding:3px;background-color: gold;font-family:'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; font-size:14px;margin-top:5px;">Last updated on: @r.LastUpdatedOn.ToString("d MMM, yyyy h:mm:ss")</div>
                </div>

            </div>
            <hr/>
        }
    }

</section>