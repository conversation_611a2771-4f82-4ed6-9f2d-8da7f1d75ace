﻿@page "/castes"

@using ButtonType = MudBlazor.ButtonType
@using FilterType = Syncfusion.Blazor.Grids.FilterType
@using Size = MudBlazor.Size
@inherits OwningComponentBase<CasteService>

@*<PageCaption Title="Castes"></PageCaption>*@
<MudText Typo="Typo.h5">Castes</MudText>
<SfToast @ref="ToastObj" Title="Alert" ShowProgressBar="true" Timeout="3000">
    <ToastPosition X="Right" Y="Bottom"></ToastPosition>
</SfToast>

<SfDialog Width="500px" @bind-Visible="@isDlgVisible" @ref="dlgForm" IsModal="true" ShowCloseIcon="true">
    <DialogEvents OnOpen="beforeOpen"></DialogEvents>
    <DialogTemplates>
        <Header>Caste Detail</Header>
        <Content>
            <EditForm Model="@selectedObj" OnValidSubmit="@SaveCasteData">
                <DataAnnotationsValidator/>

                <div class="row">
                    <div class="input-field col s6">
                        <SfTextBox @bind-Value="selectedObj.EnglishTitle" Placeholder="Title (English)" OnBlur="TranslateToUrdu" FloatLabelType="FloatLabelType.Always"></SfTextBox>
                        <ValidationMessage For="@(() => selectedObj.EnglishTitle)"/>
                    </div>
                    <div class="input-field col s6">
                        <SfTextBox @bind-Value="selectedObj.UrduTitle" Placeholder="Title (Urdu)" FloatLabelType="FloatLabelType.Always"></SfTextBox>
                        <ValidationMessage For="@(() => selectedObj.UrduTitle)"/>
                    </div>
                </div>
                <div class="row">
                    <div class="col">
                        <MudButton ButtonType="ButtonType.Submit"
                                   Color="Color.Primary" Size="Size.Small"
                                   Variant="Variant.Filled">
                            @SaveButtonText
                        </MudButton>
                    </div>
                </div>
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>

<!-- AuthorizeView allows us to only show sections of the page -->
<!-- based on the security on the current user -->
<AuthorizeView>
    <!-- Show this section if the user is logged in -->
    <Authorized>
        <section>
            @if (objList == null)
            {
                <p>
                    <em>Loading...</em>
                </p>
            }
            else
            {
                <SfButton IsPrimary="true" CssClass="e-primary mb-2" OnClick="OpenCreateForm">Create Caste</SfButton>
                <SfGrid DataSource="@objList" AllowFiltering="true" AllowSorting="true" ModelType="@selectedObj" @ref="Grid" AllowTextWrap="true">
                    <GridFilterSettings Type="FilterType.Excel"></GridFilterSettings>
                    <GridColumns>
                        <GridColumn AutoFit="true" Field="Id" HeaderText="Id"></GridColumn>
                        <GridColumn AutoFit="true" Field="UrduTitle" HeaderText="Urdu Title" TextAlign="TextAlign.Right"></GridColumn>
                        <GridColumn AutoFit="true" Field="EnglishTitle" HeaderText="English Title"></GridColumn>
                        <GridColumn Width="110px" HeaderText="Actions" AllowFiltering="false">
                            <Template Context="ss">
                                @{
                                    var kk = ss as Caste;
                                    <SfButton IsPrimary="true" CssClass="e-danger" OnClick="@(() => DeleteRecord(kk.Id))">
                                        <i class="fas fa-trash-alt"></i>
                                    </SfButton>
                                    <SfButton IsPrimary="true" CssClass="e-primary" OnClick="@(() => OpenEditForm(kk))">
                                        <i class="fas fa-pencil-alt"></i>
                                    </SfButton>
                                }
                            </Template>
                        </GridColumn>
                    </GridColumns>
                </SfGrid>
            }
        </section>
    </Authorized>
    <!-- Show this section if the user is not logged in -->
    <NotAuthorized>
        <p>You're not signed in.</p>
    </NotAuthorized>
</AuthorizeView>