﻿using Microsoft.EntityFrameworkCore.Migrations;
using System;

namespace ElectionAppServer.Migrations.ApplicationDb
{
	public partial class PreviousStructureMappings3 : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropForeignKey(
				 name: "FK_StructureMappings_Structures_StructureId",
				 table: "StructureMappings");

			migrationBuilder.AlterColumn<DateTime>(
				 name: "CreatedDate",
				 table: "ElectionParties",
				 nullable: false,
				 defaultValue: new DateTime(2020, 9, 3, 14, 54, 27, 379, DateTimeKind.Local).AddTicks(9437),
				 oldClrType: typeof(DateTime),
				 oldType: "datetime2",
				 oldDefaultValue: new DateTime(2020, 9, 3, 14, 44, 1, 172, DateTimeKind.Local).AddTicks(6620));

			migrationBuilder.AlterColumn<DateTime>(
				 name: "CreatedDate",
				 table: "ElectionCandidates",
				 nullable: false,
				 defaultValue: new DateTime(2020, 9, 3, 14, 54, 27, 385, DateTimeKind.Local).AddTicks(2162),
				 oldClrType: typeof(DateTime),
				 oldType: "datetime2",
				 oldDefaultValue: new DateTime(2020, 9, 3, 14, 44, 1, 178, DateTimeKind.Local).AddTicks(6804));

			migrationBuilder.AddForeignKey(
				 name: "FK_StructureMappings_Structures_StructureId",
				 table: "StructureMappings",
				 column: "StructureId",
				 principalTable: "Structures",
				 principalColumn: "Id",
				 onDelete: ReferentialAction.Cascade);
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropForeignKey(
				 name: "FK_StructureMappings_Structures_StructureId",
				 table: "StructureMappings");

			migrationBuilder.AlterColumn<DateTime>(
				 name: "CreatedDate",
				 table: "ElectionParties",
				 type: "datetime2",
				 nullable: false,
				 defaultValue: new DateTime(2020, 9, 3, 14, 44, 1, 172, DateTimeKind.Local).AddTicks(6620),
				 oldClrType: typeof(DateTime),
				 oldDefaultValue: new DateTime(2020, 9, 3, 14, 54, 27, 379, DateTimeKind.Local).AddTicks(9437));

			migrationBuilder.AlterColumn<DateTime>(
				 name: "CreatedDate",
				 table: "ElectionCandidates",
				 type: "datetime2",
				 nullable: false,
				 defaultValue: new DateTime(2020, 9, 3, 14, 44, 1, 178, DateTimeKind.Local).AddTicks(6804),
				 oldClrType: typeof(DateTime),
				 oldDefaultValue: new DateTime(2020, 9, 3, 14, 54, 27, 385, DateTimeKind.Local).AddTicks(2162));

			migrationBuilder.AddForeignKey(
				 name: "FK_StructureMappings_Structures_StructureId",
				 table: "StructureMappings",
				 column: "StructureId",
				 principalTable: "Structures",
				 principalColumn: "Id",
				 onDelete: ReferentialAction.Restrict);
		}
	}
}
