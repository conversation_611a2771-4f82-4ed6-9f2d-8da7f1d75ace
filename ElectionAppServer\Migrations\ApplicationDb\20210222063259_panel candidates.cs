﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace ElectionAppServer.Migrations.ApplicationDb
{
	public partial class panelcandidates : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropColumn(
				 name: "SeatConfigurationType",
				 table: "ElectionAssemblies");

			migrationBuilder.AddColumn<int>(
				 name: "PanelId",
				 table: "Candidates",
				 type: "int",
				 nullable: true);

			migrationBuilder.CreateIndex(
				 name: "IX_Candidates_PanelId",
				 table: "Candidates",
				 column: "PanelId");

			migrationBuilder.AddForeignKey(
				 name: "FK_Candidates_Candidates_PanelId",
				 table: "Candidates",
				 column: "PanelId",
				 principalTable: "Candidates",
				 principalColumn: "Id",
				 onDelete: ReferentialAction.Restrict);
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropForeignKey(
				 name: "FK_Candidates_Candidates_PanelId",
				 table: "Candidates");

			migrationBuilder.DropIndex(
				 name: "IX_Candidates_PanelId",
				 table: "Candidates");

			migrationBuilder.DropColumn(
				 name: "PanelId",
				 table: "Candidates");

			migrationBuilder.AddColumn<int>(
				 name: "SeatConfigurationType",
				 table: "ElectionAssemblies",
				 type: "int",
				 nullable: false,
				 defaultValue: 0);
		}
	}
}
