﻿@page "/towns/{districtId:int}"
@page "/towns/{districtId:int}/{cid:int}"
@using BeforeOpenEventArgs = Syncfusion.Blazor.Popups.BeforeOpenEventArgs
@using FilterType = Syncfusion.Blazor.Grids.FilterType
@inherits OwningComponentBase<ElectionStructureService>
@attribute [Authorize(Roles = "Administrators,Data Viewer")]

<MudText Typo="Typo.h5">Towns/Tehsils/LG Tier 1</MudText>
@*<PageCaption Title="Towns/Tehsil/LG Tier 1"></PageCaption>*@
@*<a href="/elections">@ElectionTitle</a> <span>></span>*@

<SfDialog Width="900px" @bind-Visible="@isDlgVisible" @ref="dlgForm" IsModal="true" ShowCloseIcon="true">
    <DialogEvents OnOpen="beforeOpen"></DialogEvents>
    <DialogTemplates>
        <Header>Town/Tehsil/LG Tier 1 Detail</Header>
        <Content>
            <AdminUnitForm SelectedObj="@selectedObj" OnValidDataSubmit="SaveData"></AdminUnitForm>
        </Content>
    </DialogTemplates>
</SfDialog>

<SfToast @ref="ToastObj">
    <ToastPosition X="Right"></ToastPosition>
</SfToast>

@if (objList == null)
{
    <p>
        <em>Loading...</em>
    </p>
}
else
{
    <section >
        @foreach (var bc in BredCrum)
        {
            <a href="@bc.UrduTitle">@bc.EnglishTitle</a>
            <span>></span>
        }
        <AuthorizeView Roles="Administrators">
            <div class="row">
                <SfButton IsPrimary="true" CssClass="e-primary mb-2" OnClick="OpenCreateForm">Create Town/Tehsil/LG Tier 1</SfButton>
            </div>
        </AuthorizeView>
        <div class="row">
            <SearchStructure ElectionId="@electionId"></SearchStructure>
        </div>
        <div class="row">
            <SfGrid DataSource="@objList" AllowFiltering="true" ModelType="@selectedObj" @ref="Grid" AllowSorting="true" AllowPaging="true" AllowTextWrap="true" Width="100%" Height="calc(100vh - 310px)">
                <GridEvents QueryCellInfo="CustomizeCell" TValue="ElcStructureDTO"></GridEvents>
                <GridFilterSettings Type="FilterType.Excel"></GridFilterSettings>
                <GridPageSettings PageSize="10"></GridPageSettings>
                <GridColumns>
                    <GridColumn Field="Id" HeaderText="Id" AutoFit="true"></GridColumn>
                    <GridColumn AutoFit="true" Field="UrduTitle" HeaderText="Urdu Title" TextAlign="TextAlign.Right"></GridColumn>
                    <GridColumn AutoFit="true" Field="EnglishTitle" HeaderText="English Title"></GridColumn>
                    <GridColumn AutoFit="true" Field="SubUnits" HeaderText="Total UCs"></GridColumn>
                    <GridColumn AutoFit="true" HeaderText="Audit Info" Width="200px">
                        <Template Context="kk">
                            @{
                                var oo = kk as ElcStructureDTO;
                            <div style="font-size:11px">
                                <span>Created By: @oo.CreateBy</span><span> On @oo.CreatedDate</span>
                                @if (!string.IsNullOrEmpty(oo.ModifiedBy))
                                {
                                    <br/>
                                    <span>Modified By: @oo.ModifiedBy</span>
                                    <span> On @oo.ModifiedDate</span>
                                }
                            </div>}
                        </Template>
                    </GridColumn>

                    <GridColumn Width="200px" HeaderText="Actions" AllowFiltering="false">
                        <Template Context="ss">
                            @{
                                var kk = ss as ElcStructureDTO;
                                <a target="@($"au{kk.Id}")" href="report/adminunit/@kk.Id">
                                    <span class="e-control e-btn e-lib e-success">
                                        <i class="fas fa-clipboard-check"></i>
                                    </span>
                                </a>
                                <AuthorizeView Roles="Administrators">
                                    <SfButton IsPrimary="true" CssClass="e-danger" OnClick="@(() => DeleteRecord(kk.Id))">
                                        <i class="fas fa-trash-alt"></i>
                                    </SfButton>
                                </AuthorizeView>
                                <SfButton IsPrimary="true" CssClass="e-primary" OnClick="@(() => OpenEditForm(kk))">
                                    <i class="fas fa-pencil-alt"></i>
                                </SfButton>

                                <a style="padding-left:3px;" href="/ucs/@kk.Id">Tier 2</a>
                            }
                        </Template>
                    </GridColumn>
                </GridColumns>
            </SfGrid>
        </div>
    </section>
}

@code {

    // AuthenticationState is available as a CascadingParameter
    [CascadingParameter] private Task<AuthenticationState> authenticationStateTask { get; set; }
    [Parameter] public int districtId { get; set; }
    private SfDialog dlgForm;
    List<ElcStructureDTO> objList;

    private bool isDlgVisible;

    //private string SaveButtonText = "Save";
    //private string FormTitle = "Create Town";
    private ElcStructureDTO selectedObj;
    private SfToast ToastObj;
    public string ToastContent = "Unable to delete Record, Related Record(s) Exist";
    public List<GeneralItemDTO> BredCrum { get; set; }
    public int electionId { get; set; }
    public List<GeneralItemDTO> languagesList { get; set; }
    public List<GeneralItemDTO> casteList { get; set; }
    public string ElectionTitle { get; set; }
    [Parameter] public int? cid { get; set; }
    public SfGrid<ElcStructureDTO> Grid { get; set; }

    protected override async Task OnInitializedAsync()
    {
        var user = (await authenticationStateTask).User;
        objList = await Service.GetList(StructureType.Town, districtId, state.state.PhaseId, cid);
        BredCrum = await Service.GetBreadCrum(StructureType.Town, districtId);
        electionId = BredCrum[0].Id;
        languagesList = await Service.GetLanguages();
        casteList = await Service.GetCastesList();
        ElectionTitle = await Service.GetElectionTitle(electionId);
        ClearData();
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        //return base.OnAfterRenderAsync(firstRender);
        if (firstRender)
        {
            var user = (await authenticationStateTask).User;
            if (user.Identity.IsAuthenticated)
            {
                //var dd = await ProtectedSessionStore.GetAsync<ElectionStateDTO>("election");
                var dd = await localStorage.GetAsync<ElectionStateDTO>(await confreader.GetSettingValue("electionkey"));
                state.SetState(dd.Value);
            }
            else
            {
                try
                {
                    //await  ProtectedSessionStore.DeleteAsync("election");
                    await localStorage.DeleteAsync(await confreader.GetSettingValue("electionkey"));

                    state.SetState(new ElectionStateDTO { ElectionId = 0, ElectionTitle = "", PhaseId = 0, PhaseTitle = "" });
                }
                catch (Exception)
                {
                    // ignored
                }

                NavigationManager.NavigateTo("");
            }

            StateHasChanged();
        }
    }

    private async Task OpenCreateForm()
    {
        ClearData();
        await dlgForm.ShowAsync();
        //SaveButtonText = "Create";
    }

    private async Task OpenEditForm(ElcStructureDTO st)
    {
        selectedObj = await Service.GetAdminUnitDetail(st.Id, districtId, StructureType.Town, state.state.PhaseId);
        await dlgForm.ShowAsync();
        //SaveButtonText = "Update";
    }
    //private void OpenEditForm2(ElcStructureDTO st)
    //{
    //   selectedObj = new ElcStructureDTO
    //   {
    //      Id = st.Id,
    //      //CasteId = st.CasteId,
    //      Code = st.Code,
    //      EnglishTitle = st.EnglishTitle,
    //      FemaleVoters = st.FemaleVoters,
    //      TotalPollingStations = st.TotalPollingStations,
    //      ImportantAreas = st.ImportantAreas,
    //      ImportantPoliticalPersonalities = st.ImportantPoliticalPersonalities,
    //      //LanguageId = st.LanguageId,
    //      Castes = st.Castes,
    //      Languages = st.Languages,
    //      MaleVoters = st.MaleVoters,
    //      ParentId = districtId,
    //      Problems = st.Problems,
    //      RuralAreaPer = st.RuralAreaPer,
    //      Trivia = st.Trivia,
    //      UrbanAreaPer = st.UrbanAreaPer,
    //      UrduTitle = st.UrduTitle,
    //      StructureType = StructureType.Town,
    //      MajorityIncomeSource = st.MajorityIncomeSource,
    //      LiteracyRate = st.LiteracyRate,
    //      AverageHouseholdIncome = st.AverageHouseholdIncome,
    //      Population = st.Population,
    //      GeneralTrivia = st.GeneralTrivia,
    //      Area = st.Area
    //   };
    //   dlgForm.ShowAsync();
    //   SaveButtonText = "Update";
    //}

    private void ClearData()
    {
        selectedObj = new ElcStructureDTO { ParentId = districtId, StructureType = StructureType.Town, ElectionId = electionId };
    }

    private async Task SaveData()
    {
        var user = (await authenticationStateTask).User;

        try
        {
            await Service.Save(selectedObj, user.Identity.Name, state.state.PhaseId);
            objList = await Service.GetList(StructureType.Town, districtId, state.state.PhaseId, cid);
            await Grid.Refresh();
            ClearData();
            await dlgForm.HideAsync();
        }
        catch (Exception ex)
        {
            var mm = new ToastModel { Title = "Error", Content = ex.Message, CssClass = "e-toast-danger", ShowProgressBar = true, Timeout = 5000, ShowCloseButton = true };
            await ToastObj.ShowAsync(mm);
        }
    }

    private async Task DeleteRecord(int Id)
    {
        var res = await JSRuntime.InvokeAsync<bool>("confirm", "Are you want to delete this record?");
        if (res == false)
            return;

        try
        {
            var msg2 = await Service.Delete(Id);
            objList = await Service.GetList(StructureType.Town, districtId, state.state.PhaseId, cid);
            await Grid.Refresh();
            ClearData();
        }
        catch (Exception ex)
        {
            var mm = new ToastModel { Title = "Error", Content = ex.Message, CssClass = "e-toast-danger", ShowProgressBar = true, Timeout = 5000, ShowCloseButton = true };
            await ToastObj.ShowAsync(mm);
        }
    }

    private async Task TranslateToUrdu()
    {
        try
        {
            if (string.IsNullOrEmpty(selectedObj.UrduTitle))
                selectedObj.UrduTitle = await Translation.TranslateText(selectedObj.EnglishTitle);
        }
        catch (Exception)
        {
            // ignored
        }

        //if (string.IsNullOrEmpty(selectedObj.UrduTitle))
    }

    public void CustomizeCell(QueryCellInfoEventArgs<ElcStructureDTO> args)
    {
        if (args.Column.Field == "UrduTitle")
        {
            args.Cell.AddClass(new[] { "urdu-column" });
            args.Cell.AddStyle(new[] { "font-size:18px;line-height: 1.4;text-align: right" });
        }
    }

    private void beforeOpen(BeforeOpenEventArgs args)
    {
        args.MaxHeight = null;
    }

}