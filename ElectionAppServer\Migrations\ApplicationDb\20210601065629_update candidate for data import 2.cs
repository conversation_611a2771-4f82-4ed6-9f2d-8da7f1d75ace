﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace ElectionAppServer.Migrations.ApplicationDb
{
	public partial class updatecandidatefordataimport2 : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.AlterColumn<int>(
				 name: "Gender",
				 table: "Candidates",
				 type: "int",
				 nullable: true,
				 oldClrType: typeof(int),
				 oldType: "int");

			migrationBuilder.AlterColumn<string>(
				 name: "EnglishName",
				 table: "Candidates",
				 type: "nvarchar(200)",
				 maxLength: 200,
				 nullable: false,
				 oldClrType: typeof(string),
				 oldType: "varchar(200)",
				 oldUnicode: false,
				 oldMaxLength: 200);
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.AlterColumn<int>(
				 name: "Gender",
				 table: "Candidates",
				 type: "int",
				 nullable: false,
				 defaultValue: 0,
				 oldClrType: typeof(int),
				 oldType: "int",
				 oldNullable: true);

			migrationBuilder.AlterColumn<string>(
				 name: "EnglishName",
				 table: "Candidates",
				 type: "varchar(200)",
				 unicode: false,
				 maxLength: 200,
				 nullable: false,
				 oldClrType: typeof(string),
				 oldType: "nvarchar(200)",
				 oldMaxLength: 200);
		}
	}
}
