﻿@page "/teams"
@attribute [Authorize(Roles = "Administrators")]

@using ButtonType = MudBlazor.ButtonType
@using FilterType = Syncfusion.Blazor.Grids.FilterType
@using Size = MudBlazor.Size
@inherits OwningComponentBase<CoalitionDataService>
<MudText Typo="Typo.h5">Teams</MudText>

@*<PageCaption Title="Teams"></PageCaption>*@
<SfDialog Width="700px" @bind-Visible="@isDlgVisible" @ref="dlgForm" IsModal="true" ShowCloseIcon="true">
    <DialogEvents OnOpen="beforeOpen"></DialogEvents>
    <DialogTemplates>
        <Header>@FormTitle</Header>
        <Content>
            <EditForm Model="@selectedObj" OnValidSubmit="@SaveData">
                <DataAnnotationsValidator/>

                <div class="row">
                    <div class="input-field col ">
                        <SfTextBox @bind-Value="selectedObj.EnglishTitle" Placeholder="Title (Urdu)" FloatLabelType="FloatLabelType.Always"></SfTextBox>
                        <ValidationMessage For="@(() => selectedObj.EnglishTitle)"/>
                    </div>
                    <div class="input-field col ">
                        <SfTextBox @bind-Value="selectedObj.UrduTitle" Placeholder="Title (اردو)" FloatLabelType="FloatLabelType.Always"></SfTextBox>
                        <ValidationMessage For="@(() => selectedObj.UrduTitle)"/>
                    </div>
                </div>
                <div class="row">
                    <div class="input-field col">
                        <div class="tabheader mb-2 mt-2">Parties</div>
                        <SfGrid Width="100%" @ref="TeamGrid" DataSource="selectedObj.TeamParties" TValue="GeneralItemDTO" Toolbar="@(new List<string> { "Add", "Edit", "Delete", "Cancel", "Update" })">
                            <GridEditSettings NewRowPosition="NewRowPosition.Bottom" AllowAdding="true" AllowEditing="true" AllowDeleting="true" ShowConfirmDialog="true"></GridEditSettings>
                            <GridEvents OnActionBegin="ActionBeginHandler" TValue="GeneralItemDTO">
                            </GridEvents>
                            <GridColumns>
                                <GridColumn IsPrimaryKey="true" Field="@nameof(GeneralItemDTO.Id)" EditType="EditType.DropDownEdit" ForeignKeyField="Id" ForeignKeyValue="EnglishTitle" DataSource="@parties_list" HeaderText="Party"></GridColumn>
                            </GridColumns>
                        </SfGrid>

                    </div>
                </div>
                <div class="row">
                    <div class="col">
                        <MudButton ButtonType="ButtonType.Submit"
                                   Color="Color.Primary" Size="Size.Small"
                                   Variant="Variant.Filled"
                                   Disabled="DisabledButton" IsPrimary="true">
                            @SaveButtonText
                        </MudButton>
                    </div>
                </div>
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>

<SfToast @ref="ToastObj" Title="Adaptive Tiles Meeting" Icon="e-meeting" Content="@ToastContent">
    <ToastPosition X="Right"></ToastPosition>
</SfToast>

<section>

    @if (objList == null)
    {
        <p>
            <em>Loading...</em>
        </p>
    }
    else
    {
        <div class="row">
            <SfButton IsPrimary="true" CssClass="e-primary mb-2" OnClick="OpenCreateForm">Create New Team</SfButton>
        </div>

        <div class="row">
            <SfGrid DataSource="@objList" AllowFiltering="true" ModelType="@selectedObj" @ref="Grid" Width="100%">
                <GridEvents QueryCellInfo="CustomizeCell" TValue="CoalitionDTO"></GridEvents>
                <GridFilterSettings Type="FilterType.Excel"></GridFilterSettings>
                <GridColumns>
                    <GridColumn Field="Id" HeaderText="Id" AutoFit="true"></GridColumn>
                    <GridColumn Field="UrduTitle" HeaderText="Urdu Title" TextAlign="TextAlign.Right"></GridColumn>
                    <GridColumn Field="EnglishTitle" HeaderText="English Title"></GridColumn>
                    <GridColumn Field="Parties" HeaderText="Type"></GridColumn>
                    <GridColumn HeaderText="Audit Info">
                        <Template Context="kk">
                            @{
                                var oo = kk as CoalitionDTO;
                            <div style="font-size:11px">
                                <span>Created By: @oo.CreatedBy</span><span> On @oo.CreatedOn</span>
                                @if (!string.IsNullOrEmpty(oo.ModifiedBy))
                                {
                                    <br/>
                                    <span>Modified By: @oo.ModifiedBy</span>
                                    <span> On @oo.ModifiedOn</span>
                                }
                            </div>}
                        </Template>
                    </GridColumn>
                    <GridColumn HeaderText="Actions" AllowFiltering="false" Width="180px">
                        <Template Context="ss">
                            @{
                                var kk = ss as CoalitionDTO;
                                <SfButton IsPrimary="true" CssClass="e-danger" OnClick="@(() => DeleteRecord(kk.Id))">
                                    <i class="fas fa-trash-alt"></i>
                                </SfButton>
                                <SfButton IsPrimary="true" CssClass="e-primary" OnClick="@(() => OpenEditForm(kk))">
                                    <i class="fas fa-pencil-alt"></i>
                                </SfButton>
                            }
                        </Template>
                    </GridColumn>
                </GridColumns>
            </SfGrid>
        </div>
    }
</section>