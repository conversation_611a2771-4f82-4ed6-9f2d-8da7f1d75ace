﻿@page "/ticker"
@attribute [Authorize(Roles = "Ticker Desk")]

@inject NavigationManager NavigationManager
@inject UserManager<ApplicationUser> _UserManager
@inject RoleManager<IdentityRole> _RoleManager
@inject AuthenticationStateProvider AuthenticationStateProvider

@inherits OwningComponentBase<ElectionResultsDataService>
@inject ClipboardService ClipboardService



@*<PageCaption Title="Live Ticker"></PageCaption>*@
<MudText Typo="Typo.h5">Live Ticker</MudText>


<section>


    <SfTab>
        <TabItems>
            @foreach (var asmtyp in assemblyTypes)
            {
                var kk = $"{asmtyp.AssemblyType} - {asmtyp.SeatType.Replace("General ", "")}";
                <TabItem>
                    <ChildContent>
                        <TabHeader Text="@kk"></TabHeader>
                    </ChildContent>
                    <ContentTemplate>
                        <div class="row" style="direction: rtl;">
                            @foreach (var r in results)
                            {
                                if (r.AssemblyTypeId == asmtyp.AssemblyTypeId && r.SeatTypeId == asmtyp.SeatTypeId)
                                {
                                    <div class="col-12 urdu-column" style="direction: rtl; text-align: right; background-color: #87cefa; padding: 5px; line-height: 1.7; margin: 5px;">
                                        <img src=images/copy.png align="left" style="cursor:pointer" @onclick=@(() => CopyToClipboard(r.Ticker))/>
                                        @((MarkupString)r.Ticker)

                                    </div>
                                    <hr/>
                                }
                            }
                        </div>
                    </ContentTemplate>
                </TabItem>
            }


        </TabItems>
    </SfTab>

</section>