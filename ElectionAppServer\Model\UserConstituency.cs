﻿using System.ComponentModel.DataAnnotations.Schema;
using ElectionAppServer.DTO;

namespace ElectionAppServer.Model;

public class UserConstituency
{
    public string UserId { get; set; }
    public virtual ApplicationUser User { get; set; }

    [ForeignKey("ElectionAssembly")] public int ElectionAssemblyId { get; set; }

    public virtual ElectionAssembly ElectionAssembly { get; set; }

    [ForeignKey("Structure")] public int StructureId { get; set; }

    public virtual Structure Structure { get; set; }

    [NotMapped]
    public virtual StructureType ConstituencyType
    {
        get
        {
            if (Structure.GetType() == typeof(UnionCouncil)) return StructureType.UnionCouncil;
            if (Structure.GetType() == typeof(Ward)) return StructureType.Ward;
            if (Structure.GetType() == typeof(NationalAssemblyHalka)) return StructureType.NA;
            if (Structure.GetType() == typeof(ProvincialAssemblyHalka)) return StructureType.PA;
            return 0;
        }
    }
}