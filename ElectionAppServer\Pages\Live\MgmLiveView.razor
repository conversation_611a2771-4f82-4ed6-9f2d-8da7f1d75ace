﻿@page "/mgmlive"
@attribute [Authorize(Roles = "Bureau")]

@inject NavigationManager NavigationManager
@inject UserManager<ApplicationUser> _UserManager
@inject RoleManager<IdentityRole> _RoleManager
@inject AuthenticationStateProvider AuthenticationStateProvider

@using FilterType = Syncfusion.Blazor.DropDowns.FilterType
@inherits OwningComponentBase<ElectionResultsDataService>


<SfDialog Width="950px" @bind-Visible="@isDlgVisible" @ref="dlgForm" IsModal="true" ShowCloseIcon="true">
    <DialogEvents OnOpen="beforeOpen"></DialogEvents>
    <DialogTemplates>
        <Header>Detail</Header>
        <Content>
            <div class="row">
                <div class="col urdu-column" style="direction: rtl; text-align: right; background-color: #87cefa; padding: 5px; line-height: 1.7">
                    @((MarkupString)detail.Ticker)
                </div>
            </div>
            @*<div class="row">
                    <div class="col-3">Code: <b>@detail.Code</b></div>
                    <div class="col">Constituency: <b>@detail.EnglishTitle</b></div>
                    <div class="col">حلقہ: <b>@detail.UrduTitle</b></div>
                </div>
                <div class="row">
                    <div class="col">Register Voters: <b>@detail.RegisterVoters</b></div>
                    <div class="col">Total Polling Stations: <b>@detail.TotalPollingStations</b></div>
                    <div class="col">Results Polling Stations: <b>@detail.ResultPollingStations</b></div>
                </div>
                <table class="table table-sm table-striped mt-2">
                    <thead class="thead-dark">
                    <tr>
                        <th>Candidate</th>
                        <th>Party</th>
                        <th>Votes</th>
                    </tr>
                    </thead>
                    <tbody>
                    @foreach (var n in detail.Nominations)
                    {
                        <tr>
                            <td>@n.Candidate</td>
                            <td>@n.Party</td>
                            <td>@n.Votes</td>
                        </tr>
                    }
                    </tbody>
                </table>*@
        </Content>
    </DialogTemplates>
</SfDialog>

@*<PageCaption Title="Live Results"></PageCaption>*@
<MudText Typo="Typo.h5">Live Results</MudText>

<section style="padding: 15px 1rem; margin-top: -35px;font-size:12px">
    <div class="row">
        <div class="col-md">
            @if (ProvinceList != null && ProvinceList.Any())
            {
                <SfDropDownList AllowFiltering="true" TItem="GeneralItemDTO" TValue="int?"
                                Placeholder="Province" @bind-Value="@selectedProvince"
                                DataSource="@ProvinceList" FloatLabelType="FloatLabelType.Always"
                                FilterType="FilterType.Contains" ShowClearButton="true">
                    <DropDownListFieldSettings Value="Id" Text="EnglishTitle"></DropDownListFieldSettings>
                    <DropDownListEvents TItem="GeneralItemDTO" TValue="int?" ValueChange="OnProvinceChange"></DropDownListEvents>
                </SfDropDownList>
            }
            else
            {
                <span></span>
            }
        </div>
        <div class="col-md">
            @if (DistrictList != null && DistrictList.Any())
            {
                <SfDropDownList AllowFiltering="true" TItem="GeneralItemDTO" TValue="int?"
                                Placeholder="District" @bind-Value="@selectedDistrict"
                                DataSource="@DistrictList" FloatLabelType="FloatLabelType.Always"
                                FilterType="FilterType.Contains" ShowClearButton="true">
                    <DropDownListFieldSettings Value="Id" Text="EnglishTitle"></DropDownListFieldSettings>
                    <DropDownListEvents TItem="GeneralItemDTO" TValue="int?" ValueChange="OnDistrictChange"></DropDownListEvents>
                </SfDropDownList>
            }
            else
            {
                <span></span>
            }
        </div>
    </div>

    <SfGrid DataSource="@filterResults" AllowFiltering="true" @ref="grid" AllowSorting="true" Width="100%">
        <GridTextWrapSettings WrapMode="WrapMode.Both"></GridTextWrapSettings>
        <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Excel"></GridFilterSettings>
        <GridColumns>
            <GridColumn Field="@nameof(ResultDTO.Assembly)" HeaderText="Assembly" AutoFit="true"></GridColumn>
            <GridColumn Field="@nameof(ResultDTO.Constituency)" HeaderText="Constituency" AutoFit="true"></GridColumn>
            <GridColumn Field="@nameof(ResultDTO.WinnerName)" HeaderText="Winner" AutoFit="true"></GridColumn>
            <GridColumn Field="@nameof(ResultDTO.WinnerParty)" HeaderText="Winner Party" AutoFit="true"></GridColumn>
            <GridColumn Field="@nameof(ResultDTO.WinnerVotes)" HeaderText="Winner Votes" AutoFit="true"></GridColumn>
            <GridColumn Field="@nameof(ResultDTO.RunnerUpName)" HeaderText="Runner Up" AutoFit="true"></GridColumn>
            <GridColumn Field="@nameof(ResultDTO.RunnerUpParty)" HeaderText="Runner Up Party" AutoFit="true"></GridColumn>
            <GridColumn Field="@nameof(ResultDTO.RunnerUpVotes)" HeaderText="Runner Up Votes" AutoFit="true"></GridColumn>
            <GridColumn Field="@nameof(ResultDTO.TotalPollingStations)" HeaderText="TPS" AutoFit="true"></GridColumn>
            <GridColumn Field="@nameof(ResultDTO.ResultOfPollingStations)" HeaderText="RPS" AutoFit="true"></GridColumn>
            <GridColumn Field="@nameof(ResultDTO.LastUpdatedOn)" HeaderText="Last Update" AutoFit="true"></GridColumn>
        </GridColumns>
    </SfGrid>

    @*<table class="table table-sm">
            <thead class="thead-dark">
                <tr>
                    <th>Assembly</th>
                    <th>Constituency</th>
                    <th>Winner</th>
                    <th>Winner Party</th>
                    <th>Winner Votes</th>
                    <th>Runner Up</th>
                    <th>Runner Party</th>
                    <th>Runner Votes</th>

                    <th>TPS</th>
                    <th>RPS</th>

                    <th>Updated Time</th>
                </tr>
            </thead>
            <tbody>
                @foreach (var r in results)
                {
                    var winImg = "";
                    if(r.WinnerCandidateId!=0)
                        winImg = $"/media/candidates/{r.WinnerCandidateId}.jpg";
                    else
                        winImg = $"/media/candidates/blank.jpg";

                    var runImg = "";
                    if(r.RunnerUpCandidateId!=0)
                        runImg = $"/media/candidates/{r.RunnerUpCandidateId}.jpg";
                    else
                        runImg = $"/media/candidates/blank.jpg";
                    <tr class="@(r.Status=="No" ? "newresult" : "pubresult")">
                        <td>@r.Assembly</td>
                        <td>
                            <span style="cursor:pointer;color:blue" @onclick="@(()=>DisplayDetail(r.AssemblyId, r.ConstituencyId, r.SeatTypeId))">@r.Constituency</span>
                            </td>
                        <td style="display: flex; gap: 5px"><img src=@winImg style="width: 80px" alt="@r.WinnerName" />@r.WinnerName</td>
                        <td>@r.WinnerParty</td>
                        <td>@r.WinnerVotes</td>
                        <td style="display: flex; gap: 5px"><img src=@winImg style="width: 80px" alt="@r.WinnerName" />@r.RunnerUpName</td>
                        <td>@r.RunnerUpParty</td>
                        <td>@r.RunnerUpVotes</td>

                        <td>@r.TotalPollingStations</td>
                        <td>@r.ResultOfPollingStations</td>

                    <td>@r.LastUpdatedOn.ToString("d MMM, yyyy h:mm:ss")</td>
                </tr>
            }
            </tbody>
        </table>*@
</section>