﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using ElectionAppServer.DTO;
using ElectionAppServer.Model;
using Microsoft.EntityFrameworkCore;

namespace ElectionAppServer.Data;

public class PartyService
{
    private readonly IDbContextFactory<ApplicationDbContext>
        _contextFactory; // private  readonly  ApplicationDbContext dc;

    private readonly SymbolService ss;

    //public PartyService(ApplicationDbContext context, SymbolService ss)
    public PartyService(IDbContextFactory<ApplicationDbContext> contextFactory, SymbolService ss)
    {
        _contextFactory = contextFactory; // dc  =  context;
        this.ss = ss;
    }

    public Task<List<PartyPositionStatistic>> GetOverallPartyPosition(int phaseId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.Nominations
            where aa.ElectionPhaseId == phaseId &&
                  aa.IsWinner &&
                  aa.IsWithdraw == false
            select new
            {
                Party = aa.Party.ShortEnglishTitle,
                ConstituencyId = aa.StructureId,
                Candidate = aa.CandidteId
            }).Distinct().ToList();
        var op = new List<PartyPositionStatistic>();
        foreach (var line in q.GroupBy(info => info.Party)
                     .Select(group => new
                     {
                         Metric = group.Key,
                         Count = group.Count()
                     })
                     .OrderBy(x => x.Metric))
            //Console.WriteLine("{0} {1}", line.Metric, line.Count);
            op.Add(new PartyPositionStatistic
                { Party = line.Metric, SeatsCount = line.Count, Text = line.Count.ToString() });
        return Task.FromResult(op);
    }

    public Task<List<PartyDTO>> GetAllParties(int electionId)
    {
        using var dc = _contextFactory.CreateDbContext();

        var q = (from aa in dc.Parties
            orderby aa.EnglishTitle
            select new PartyDTO
            {
                EnglishTitle = aa.EnglishTitle,
                Id = aa.Id,
                UrduTitle = aa.UrduTitle,
                ShortEnglishTitle = aa.ShortEnglishTitle,
                ShortUrduTitle = aa.ShortUrduTitle,
                FlagURL = $"/media/flags/{aa.Id}.jpg",
                Symbol = aa.Symbol.EnglishTitle,
                SymbolId = aa.SymbolId,
                SymbolURL = $"/media/symbols/{aa.SymbolId}.jpg",
                Designation = aa.Designation,
                Trivia = aa.Trivia,
                Leader = aa.Leader,
                Address = aa.Address,
                CurrentLeaderPicId = aa.CurrentLeaderPicId,
                LeaderPicId = aa.LeaderPicId,
                CurrentLeader = aa.CurrentLeader,
                CurrentLeaderDesignation = aa.CurrentLeaderDesignation,
                DateOfCreation = aa.DateOfCreation,
                IsGB = aa.IsGB,
                IsLB = aa.IsLB,
                IsGeneral = aa.IsGeneral,
                IsAJK = aa.IsAJK,
                IsLBBalochistan = aa.IsLBBalochistan,
                IsLBPunjab = aa.IsLBPunjab,
                IsLBSindh = aa.IsLBSindh,
                IsLBIslamabad = aa.IsLBIslamabad,
                IsLBAJK = aa.IsLBAJK,
                AuditInfo = "<span style='font-size:10px'>Created By: " + aa.CreatedBy + " On: " +
                            aa.CreatedDate.ToString("d MMM, yyyy h:mm") +
                            (aa.ModifiedDate != null
                                ? "<br />Modified By: " + aa.ModifiedBy + " On: " +
                                  aa.ModifiedDate.Value.ToString("d MMM, yyyy h:mm")
                                : "") + "</span>",
                DisabbledDelete = (from bb in dc.Nominations where bb.PartyId == aa.Id select bb).Any() ||
                                  (from bb in dc.PartyCoalitions where bb.PartyId == aa.Id select bb).Any()
            }).ToList();
        foreach (var p in q)
        {
            //C:\Users\<USER>\Source\repos\ElectionAppServer\ElectionAppServer\wwwroot\media\flags\5.jpg
            if (!File.Exists(Directory.GetCurrentDirectory() + $"\\wwwroot\\media\\flags\\{p.Id}.jpg"))
                p.FlagURL = "/media/flags/missing-flag.png";
            if (p.SymbolId == null)
                p.SymbolURL = "/media/symbols/missing-symbol.png";
            else if (!File.Exists(Directory.GetCurrentDirectory() + $"\\wwwroot\\media\\symbols\\{p.SymbolId}.jpg"))
                p.SymbolURL = "/media/symbols/missing-symbol.png";
            var pc = (from aa in dc.PartyCoalitions
                where aa.PartyId == p.Id && aa.ElectionId == electionId
                select new { aa.Coalition.EnglishTitle, aa.CoalitionId }).FirstOrDefault();
            if (pc != null)
            {
                p.Coalition = pc.EnglishTitle;
                p.TeamId = pc.CoalitionId;
            }
            else
            {
                p.Coalition = "";
                p.TeamId = 0;
            }

            if (p.DateOfCreation == DateTime.MinValue)
                p.DateOfCreation = null;
        }

        return Task.FromResult(q);
    }

    //public Task<List<PartyDTO>> GetAllParties2(int electionId, bool ImportMode = false)
    //{
    //	if (!ImportMode)
    //	{
    //		//var res = dc.Symbols.OrderBy(c => c.EnglishTitle).ToList();
    //		var q = (from aa in dc.ElectionParties
    //					where aa.ElectionId == electionId
    //					orderby aa.Party.EnglishTitle
    //					select new PartyDTO
    //					{
    //						EnglishTitle = aa.Party.EnglishTitle,
    //						Id = aa.PartyId,
    //						UrduTitle = aa.Party.UrduTitle,
    //						ShortUrduTitle = aa.Party.ShortUrduTitle,
    //						FlagURL = $"/media/flags/{aa.Party.Id}.jpg",
    //						SymbolId = aa.Party.SymbolId,
    //						SymbolURL = $"/media/symbols/{aa.Party.SymbolId}.jpg",
    //						Designation = aa.Party.Designation,
    //						Trivia = aa.Party.Trivia,
    //						Leader = aa.Party.Leader,
    //						Address = aa.Party.Address,
    //						CurrentLeaderPicId = aa.Party.CurrentLeaderPicId,
    //						LeaderPicId = aa.Party.LeaderPicId,
    //						CurrentLeader = aa.Party.CurrentLeader,
    //						CurrentLeaderDesignation = aa.Party.CurrentLeaderDesignation,
    //						DateOfCreation = aa.Party.DateOfCreation,
    //						AuditInfo = "<span style='font-size:10px'>Created By: " + aa.CreatedBy + " On: " + aa.CreatedDate.ToString("d MMM, yyyy h:mm") +
    //										(aa.Party.ModifiedDate != null ? "<br />Modified By: " + aa.Party.ModifiedBy + " On: " + aa.Party.ModifiedDate.Value.ToString("d MMM, yyyy h:mm") : "") + "</span>",
    //						DisabbledDelete = (from bb in dc.Nominations where bb.PartyId == aa.PartyId select bb).Any() || (from bb in dc.PartyCoalitions where bb.PartyId == aa.PartyId select bb).Any()
    //					}).ToList();
    //		foreach (var p in q)
    //			//C:\Users\<USER>\Source\repos\ElectionAppServer\ElectionAppServer\wwwroot\media\flags\5.jpg
    //			if (!File.Exists(Directory.GetCurrentDirectory() + $"\\wwwroot\\media\\flags\\{p.Id}.jpg"))
    //				p.FlagURL = "/media/flags/missing-flag.png";
    //			if (p.SymbolId == null)
    //				p.SymbolURL = "/media/symbols/missing-symbol.png";
    //			else if (!File.Exists(Directory.GetCurrentDirectory() + $"\\wwwroot\\media\\symbols\\{p.SymbolId}.jpg"))
    //			{
    //				p.SymbolURL = "/media/symbols/missing-symbol.png";
    //			}
    //			var pc = (from aa in dc.PartyCoalitions
    //						 where aa.PartyId == p.Id && aa.ElectionId == electionId
    //						 select new { aa.Coalition.EnglishTitle, aa.CoalitionId }).FirstOrDefault();
    //			if (pc != null)
    //			{
    //				p.Coalition = pc.EnglishTitle;
    //				p.TeamId = pc.CoalitionId;
    //			}
    //			else
    //			{
    //				p.Coalition = ""; p.TeamId = 0;
    //			}
    //			if (p.DateOfCreation == DateTime.MinValue)
    //				p.DateOfCreation = null;
    //		}
    //		return Task.FromResult(q);
    //	}
    //	else
    //	{
    //		//var res = dc.Symbols.OrderBy(c => c.EnglishTitle).ToList();
    //		var q = (from aa in dc.Parties
    //					orderby aa.EnglishTitle
    //					select new PartyDTO
    //					{
    //						EnglishTitle = aa.EnglishTitle,
    //						Id = aa.Id,
    //						UrduTitle = aa.UrduTitle,
    //						ShortEnglishTitle = aa.ShortEnglishTitle,
    //						ShortUrduTitle = aa.ShortUrduTitle,
    //						FlagURL = $"/media/flags/{aa.Id}.jpg",
    //						Symbol = aa.Symbol.EnglishTitle,
    //						SymbolId = aa.SymbolId,
    //						SymbolURL = $"/media/symbols/{aa.SymbolId}.jpg",
    //						Designation = aa.Designation,
    //						Trivia = aa.Trivia,
    //						Leader = aa.Leader,
    //						Address = aa.Address,
    //						CurrentLeaderPicId = aa.CurrentLeaderPicId,
    //						LeaderPicId = aa.LeaderPicId,
    //						CurrentLeader = aa.CurrentLeader,
    //						CurrentLeaderDesignation = aa.CurrentLeaderDesignation,
    //						AuditInfo = "<span style='font-size:10px'>Created By: " + aa.CreatedBy + " On: " + aa.CreatedDate.ToString("d MMM, yyyy h:mm") +
    //										(aa.ModifiedDate != null ? "<br />Modified By: " + aa.ModifiedBy + " On: " + aa.ModifiedDate.Value.ToString("d MMM, yyyy h:mm") : "") + "</span>",
    //						IsSelected = (from bb in dc.ElectionParties where bb.PartyId == aa.Id && bb.ElectionId == electionId select bb).Any(),
    //						NominationExistOnThisElection = (from nm in dc.Nominations
    //																	where nm.PartyId == aa.Id &&
    //																			nm.ElectionPhase.ElectionId == electionId
    //																	select nm).Any()
    //																	&&
    //																	(from te in dc.ElectionParties
    //																	 where te.PartyId == aa.Id &&
    //																	 te.ElectionId == electionId
    //																	 select te).Any()
    //						//NominationExistOnThisElection = false
    //					}).ToList();
    //		{
    //			if (!File.Exists(Directory.GetCurrentDirectory() + $"\\wwwroot\\media\\flags\\{p.Id}.jpg"))
    //				p.FlagURL = "/media/flags/missing-flag.png";
    //			if (p.SymbolId == null)
    //				p.SymbolURL = "/media/symbols/missing-symbol.png";
    //			else if (!File.Exists(Directory.GetCurrentDirectory() + $"\\wwwroot\\media\\symbols\\{p.SymbolId}.jpg"))
    //			{
    //				p.SymbolURL = "/media/symbols/missing-symbol.png";
    //			}
    //			var pc = (from aa in dc.PartyCoalitions
    //						 where aa.PartyId == p.Id && aa.ElectionId == electionId
    //						 select new { aa.Coalition.EnglishTitle, aa.CoalitionId }).FirstOrDefault();
    //			if (pc != null)
    //			{
    //				p.Coalition = pc.EnglishTitle;
    //				p.TeamId = pc.CoalitionId;
    //			}
    //			else
    //			{
    //				p.Coalition = ""; p.TeamId = 0;
    //			}
    //			if (p.DateOfCreation == DateTime.MinValue)
    //				p.DateOfCreation = null;
    //		}
    //		return Task.FromResult(q);
    //	}
    //}
    public Task<PartyDTO> CreateParty(PartyDTO obj, string user, int electionId)
    {
        using var dc = _contextFactory.CreateDbContext();
        using (var tran = dc.Database.BeginTransaction())
        {
            try
            {
                obj.EnglishTitle = (obj.EnglishTitle ?? "").Trim();
                obj.UrduTitle = (obj.UrduTitle ?? "").Trim();
                obj.ShortEnglishTitle = (obj.ShortEnglishTitle ?? "").Trim();
                obj.ShortUrduTitle = (obj.ShortUrduTitle ?? "").Trim();
                obj.FlagURL = (obj.FlagURL ?? "").Trim();
                obj.SymbolURL = (obj.SymbolURL ?? "").Trim();
                obj.Symbol = (obj.Symbol ?? "").Trim();
                obj.Leader = (obj.Leader ?? "").Trim();
                obj.Designation = (obj.Designation ?? "").Trim();
                obj.Address = (obj.Address ?? "").Trim();
                obj.Trivia = (obj.Trivia ?? "").Trim();
                obj.CurrentLeader = (obj.CurrentLeader ?? "").Trim();
                obj.CurrentLeaderDesignation = (obj.CurrentLeaderDesignation ?? "").Trim();
                obj.Coalition = (obj.Coalition ?? "").Trim();
                obj.LeaderPicId = (obj.LeaderPicId ?? "").Trim();
                obj.CurrentLeaderPicId = (obj.CurrentLeaderPicId ?? "").Trim();
                obj.IsAJK = obj.IsAJK;
                obj.IsGB = obj.IsGB;
                obj.IsGeneral = obj.IsGeneral;
                obj.IsLB = obj.IsLB;
                obj.IsLBBalochistan = obj.IsLBBalochistan;
                obj.IsLBSindh = obj.IsLBSindh;
                obj.IsLBPunjab = obj.IsLBPunjab;
                obj.IsLBAJK = obj.IsLBAJK;
                obj.IsLBIslamabad = obj.IsLBIslamabad;
                var isExist = (from aa in dc.Parties
                    where aa.EnglishTitle == obj.EnglishTitle
                    select aa).Any();
                if (isExist)
                    throw new Exception($"Party name {obj.EnglishTitle} already exist");
                isExist = (from aa in dc.Parties
                    where aa.UrduTitle == obj.UrduTitle
                    select aa).Any();
                if (isExist)
                    throw new Exception($"Party name {obj.UrduTitle} already exist");
                var p = new Party();
                p.Id = 0;
                p.CreatedBy = user;
                p.CreatedDate = DateTime.Now;
                p.EnglishTitle = obj.EnglishTitle;
                p.ShortEnglishTitle = obj.ShortEnglishTitle;
                p.ShortUrduTitle = obj.ShortUrduTitle;
                p.SymbolId = obj.SymbolId;
                p.UrduTitle = obj.UrduTitle;
                p.Address = obj.Address == null ? "" : obj.Address;
                p.Leader = obj.Leader == null ? "" : obj.Leader;
                p.Trivia = obj.Trivia == null ? "" : obj.Trivia;
                p.Designation = obj.Designation == null ? "" : obj.Designation;
                p.CurrentLeader = obj.CurrentLeader;
                p.CurrentLeaderDesignation = obj.CurrentLeaderDesignation;
                p.CurrentLeaderPicId = obj.CurrentLeaderPicId;
                p.LeaderPicId = obj.LeaderPicId;
                p.DateOfCreation = obj.DateOfCreation;
                p.IsLB = obj.IsLB;
                p.IsAJK = obj.IsAJK;
                p.IsGB = obj.IsGB;
                p.IsGeneral = obj.IsGeneral;
                p.IsLBBalochistan = obj.IsLBBalochistan;
                p.IsLBSindh = obj.IsLBSindh;
                p.IsLBPunjab = obj.IsLBPunjab;
                p.IsLBAJK = obj.IsLBAJK;
                p.IsLBIslamabad = obj.IsLBIslamabad;
                dc.Parties.Add(p);
                dc.SaveChanges();
                obj.FlagURL = $"/media/flags/{p.Id}.jpg";
                try
                {
                    if (p.SymbolId != null)
                        obj.Symbol = dc.Symbols.Find(p.SymbolId).EnglishTitle;
                    else
                        obj.Symbol = "";
                }
                catch (Exception)
                {
                    obj.Symbol = "";
                }

                obj.SymbolId = p.SymbolId;
                obj.SymbolURL = $"/media/symbols/{p.SymbolId}.jpg";
                if (obj.TeamId != 0)
                {
                    var cc = new PartyCoalition { ElectionId = electionId, CoalitionId = obj.TeamId, PartyId = p.Id };
                    dc.PartyCoalitions.Add(cc);
                    dc.SaveChanges();
                }

                obj.Id = p.Id;
                //var ep = new ElectionParty { CreatedBy = user, CreatedDate = DateTime.Now, ElectionId = electionId, PartyId = obj.Id };
                //dc.ElectionParties.Add(ep);
                //dc.SaveChanges();
                tran.Commit();
                return Task.FromResult(obj);
            }
            catch (Exception)
            {
                tran.Rollback();
                throw;
            }
        }
    }

    internal Task<PartyDTO> GetPartyDetail(int id, int electionId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from st in dc.Parties
            where st.Id == id
            select new PartyDTO
            {
                Id = st.Id,
                Address = st.Address,
                Designation = st.Designation,
                EnglishTitle = st.EnglishTitle,
                FlagURL = $"/media/flags/{st.Id}.jpg",
                Leader = st.Leader,
                ShortEnglishTitle = st.ShortEnglishTitle,
                ShortUrduTitle = st.ShortUrduTitle,
                SymbolId = st.SymbolId,
                SymbolURL = $"/media/symbols/{st.SymbolId}.jpg",
                Trivia = st.Trivia,
                UrduTitle = st.UrduTitle,
                CurrentLeader = st.CurrentLeader,
                CurrentLeaderDesignation = st.CurrentLeaderDesignation,
                CurrentLeaderPicId = st.CurrentLeaderPicId,
                LeaderPicId = st.LeaderPicId,

                DateOfCreation = st.DateOfCreation,
                IsAJK = st.IsAJK,
                IsGB = st.IsGB,
                IsGeneral = st.IsGeneral,
                IsLB = st.IsLB,
                IsLBBalochistan = st.IsLBBalochistan,
                IsLBSindh = st.IsLBSindh,
                IsLBPunjab = st.IsLBPunjab,
                IsLBAJK = st.IsLBAJK,
                IsLBIslamabad = st.IsLBIslamabad
                //CoalitionId = st.CoalitionId
            }).FirstOrDefault();
        var team = (from tc in dc.PartyCoalitions
            where tc.PartyId == id &&
                  tc.ElectionId == electionId
            select tc).FirstOrDefault();
        if (team != null) q.TeamId = team.CoalitionId;
        return Task.FromResult(q);
    }

    internal Task<List<GeneralItemDTO>> GetTeamList()
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.Coalitions
            orderby aa.EnglishTitle
            select new GeneralItemDTO { Id = aa.Id, EnglishTitle = aa.EnglishTitle }).ToList();
        q.Insert(0, new GeneralItemDTO { EnglishTitle = "None", Id = 0 });
        return Task.FromResult(q);
    }

    public Task<PartyDTO> UpdateParty(PartyDTO obj, string ModifiedByUser, int electionId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var st = (from aa in dc.Parties
            where aa.Id == obj.Id
            select aa).FirstOrDefault();
        st.UrduTitle = obj.UrduTitle.Trim();
        st.EnglishTitle = obj.EnglishTitle.Trim();
        st.ShortEnglishTitle = obj.ShortEnglishTitle.Trim();
        st.ShortUrduTitle = obj.ShortUrduTitle.Trim();
        st.SymbolId = obj.SymbolId;
        st.ModifiedBy = ModifiedByUser;
        st.ModifiedDate = DateTime.Now;
        st.Address = obj.Address;
        st.Leader = obj.Leader;
        st.Trivia = obj.Trivia;
        st.Designation = obj.Designation;
        st.CurrentLeader = obj.CurrentLeader;
        st.CurrentLeaderDesignation = obj.CurrentLeaderDesignation;
        st.CurrentLeaderPicId = obj.CurrentLeaderPicId;
        st.LeaderPicId = obj.LeaderPicId;
        st.DateOfCreation = obj.DateOfCreation;
        st.Address = obj.Address == null ? "" : obj.Address;
        st.Leader = obj.Leader == null ? "" : obj.Leader;
        st.Trivia = obj.Trivia == null ? "" : obj.Trivia;
        st.Designation = obj.Designation == null ? "" : obj.Designation;
        st.DateOfCreation = obj.DateOfCreation;
        st.IsAJK = obj.IsAJK;
        st.IsGB = obj.IsGB;
        st.IsGeneral = obj.IsGeneral;
        st.IsLB = obj.IsLB;
        st.IsLBBalochistan = obj.IsLBBalochistan;
        st.IsLBPunjab = obj.IsLBPunjab;
        st.IsLBSindh = obj.IsLBSindh;
        st.IsLBAJK = obj.IsLBAJK;
        st.IsLBIslamabad = obj.IsLBIslamabad;
        obj.FlagURL = $"/media/flags/{st.Id}.jpg";
        //obj.Symbol = dc.Symbols.Find(obj.SymbolId).EnglishTitle; ;
        try
        {
            if (st.SymbolId != null)
                obj.Symbol = dc.Symbols.Find(st.SymbolId).EnglishTitle;
            else
                obj.Symbol = "";
        }
        catch (Exception)
        {
            obj.Symbol = "";
        }

        obj.SymbolId = st.SymbolId;
        obj.SymbolURL = $"/media/symbols/{st.SymbolId}.jpg";
        dc.SaveChanges();
        // Remove all previously created coalitions
        var q = (from aa in dc.PartyCoalitions
            where aa.PartyId == obj.Id
                  && aa.ElectionId == electionId
            select aa).ToList();
        dc.PartyCoalitions.RemoveRange(q);
        dc.SaveChanges();
        if (obj.TeamId != 0)
        {
            var cc = new PartyCoalition { PartyId = obj.Id, ElectionId = electionId, CoalitionId = obj.TeamId };
            dc.PartyCoalitions.Add(cc);
            dc.SaveChanges();
        }

        return Task.FromResult(obj);
    }

    public async Task<string> DeleteParty(int Id, int electionId)
    {
        using var dc = _contextFactory.CreateDbContext();
        //var pes = (from aa in dc.ElectionParties
        //			  where aa.PartyId == Id
        //			  select aa).ToList();
        //dc.ElectionParties.RemoveRange(pes);
        //dc.SaveChanges();
        var st = await (from aa in dc.Parties
            where aa.Id == Id
            select aa).FirstOrDefaultAsync();
        dc.Parties.Remove(st);
        await dc.SaveChangesAsync();
        return await Task.FromResult("ok");
    }

    public Task<List<SymbolDTO>> GetSymbolList()
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.Symbols
            orderby aa.EnglishTitle
            select new SymbolDTO
            {
                English = aa.EnglishTitle,
                Urdu = aa.UrduTitle,
                Id = aa.Id,
                URL = $"/media/symbols/{aa.Id}.jpg"
            }).ToList();
        return Task.FromResult(q);
    }

    public Task<SymbolDTO> CreateSymbol(SymbolDTO obj, string userId)
    {
        using var dc = _contextFactory.CreateDbContext();
        obj.English = obj.English.Trim();
        obj.Urdu = obj.Urdu.Trim();

        #region Exist

        //var isExist = dc.Symbols.Any(c => c.Code == obj.Code);
        //if (isExist) throw new Exception("Code Already exist");
        var isExist = dc.Symbols.Any(c => c.EnglishTitle == obj.English);
        if (isExist) throw new Exception("English Title already exist");
        isExist = dc.Symbols.Any(c => c.UrduTitle == obj.Urdu);
        if (isExist) throw new Exception("Urdu Title already exist");

        #endregion Exist

        var sm = new Symbol
        {
            //Code = obj.Code,
            CreatedBy = userId,
            CreatedDate = DateTime.Now,
            EnglishTitle = obj.English.Trim(),
            UrduTitle = obj.Urdu.Trim()
        };
        dc.Symbols.Add(sm);
        dc.SaveChanges();
        obj.Id = sm.Id;
        return Task.FromResult(obj);
    }

    public Task<PartyRptDTO> GetPartyRptInfo(int partyId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.Parties
            where aa.Id == partyId
            select new PartyRptDTO
            {
                Id = aa.Id,
                EnglishTitle = aa.EnglishTitle,
                UrduTitle = aa.UrduTitle,
                SymbolId = aa.SymbolId,
                Symbol = aa.SymbolId == null ? "NA" : $"{aa.Symbol.EnglishTitle} - ({aa.Symbol.UrduTitle})",
                Trivia = aa.Trivia,
                ShortEnglishTitle = aa.ShortEnglishTitle,
                Address = aa.Address,
                CurrentLeader = aa.CurrentLeader,
                CurrentLeaderDesignation = aa.CurrentLeaderDesignation,
                CurrentLeaderPicId = aa.CurrentLeaderPicId,
                DateOfCreation = aa.DateOfCreation,
                Designation = aa.Designation,
                ShortUrduTitle = aa.ShortUrduTitle,
                Leader = aa.Leader,
                LeaderPicId = aa.LeaderPicId,
                Coalition = (from bb in dc.PartyCoalitions
                    where bb.PartyId == partyId
                    select new CoalitionRptDTO
                    {
                        Coalition = $"{bb.Coalition.EnglishTitle}",
                        Election = $"{bb.Election.EnglishTitle}",
                        Parties = (from cc in dc.PartyCoalitions
                            orderby cc.Party.EnglishTitle
                            where cc.CoalitionId == bb.CoalitionId
                                  && cc.PartyId != partyId
                            select new GeneralItemDTO
                            {
                                Id = cc.PartyId,
                                EnglishTitle =
                                    $"{cc.Party.EnglishTitle}<br><span class='urdu-sm'>{cc.Party.UrduTitle}<span>"
                            }).ToList()
                    }).ToList(),
                Nominations = (from bb in dc.Nominations
                        where bb.PartyId == partyId
                        orderby bb.ElectionPhase.StartDate descending, bb.ElectionPhase.Election.EnglishTitle, bb
                            .Structure
                            .EnglishTitle
                        select new NominationRptDTO
                        {
                            Election =
                                $"{bb.ElectionPhase.Election.EnglishTitle}<br><span class='urdu-sm'>{bb.ElectionPhase.Election.UrduTitle}</span><br>",
                            Date = bb.ElectionPhase.StartDate,
                            Phase = $"{bb.ElectionPhase.Title}",
                            Assembly =
                                $"{bb.ElectionAssembly.EnglishTitle}<br><span class='urdu-sm'>{bb.ElectionAssembly.UrduTitle}</span>",
                            ConstituencyCode = bb.Structure.Code,
                            ConstituencyId = bb.StructureId,
                            Constituency =
                                $"{bb.Structure.EnglishTitle}<br><span class='urdu-sm'>{bb.Structure.UrduTitle}</span>",
                            Symbol = $"{bb.Symbol.EnglishTitle}<br><span class='urdu-sm'>{bb.Symbol.UrduTitle}</span>",
                            SeatType =
                                $"{bb.SeatType.EnglishTitle}<br><span class='urdu-sm'>{bb.SeatType.UrduTitle}</span>",
                            Weight = bb.Weight,
                            Votes = bb.Votes,
                            Candidate =
                                $"{bb.Candidate.EnglishName}<br><span class='urdu-sm'>{bb.Candidate.UrduName}</span>",
                            CandidateId = bb.CandidteId
                        }
                    ).ToList()
            }).FirstOrDefault();
        return Task.FromResult(q);
    }

    //public Task<string> AddRemoveParties(int electionId, List<int> partyIds, string user)
    //{
    //	using (var tran = dc.Database.BeginTransaction())
    //	{
    //		try
    //		{
    //			var q = (from aa in dc.ElectionParties
    //						where aa.ElectionId == electionId &&
    //						!partyIds.Contains(aa.PartyId)
    //						select aa).ToList();
    //			foreach (var i in q)
    //			{
    //				var nomExist = (from bb in dc.Nominations
    //									 where bb.ElectionAssembly.ElectionId == electionId &&
    //									 bb.PartyId == i.PartyId
    //									 select bb).Any();
    //				if (nomExist)
    //					throw new Exception("One or more removed Parties nomination exist on this election");
    //			}
    //			if (q.Any())
    //			{
    //				dc.ElectionParties.RemoveRange(q);
    //				dc.SaveChanges();
    //			}
    //			var ecs = new List<ElectionParty>();
    //			foreach (var c in partyIds)
    //			{
    //				var found = (from bb in dc.ElectionParties
    //								 where bb.PartyId == c && bb.ElectionId == electionId
    //								 select bb).Any();
    //				if (!found)
    //				{
    //					ecs.Add(new ElectionParty { PartyId = c, ElectionId = electionId, CreatedBy = user, CreatedDate = DateTime.Now });
    //				}
    //			}
    //			if (ecs.Any())
    //			{
    //				dc.ElectionParties.AddRange(ecs);
    //				dc.SaveChanges();
    //			}
    //			tran.Commit();
    //			return Task.FromResult("OK");
    //		}
    //		catch (Exception)
    //		{
    //			tran.Rollback();
    //			throw;
    //		}
    //	}
    //}
}