﻿using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ElectionAppServer.DTO;
using ElectionAppServer.Model;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;

namespace ElectionAppServer.Data;

public class MenuService
{
    private readonly IDbContextFactory<ApplicationDbContext> _contextFactory; // private  ApplicationDbContext  dc;
    private readonly RoleManager<IdentityRole> roleManager;
    private readonly UserManager<ApplicationUser> userManager;

    //@inject Microsoft.AspNetCore.Identity.RoleManager<IdentityRole> RoleManager

    public MenuService(IDbContextFactory<ApplicationDbContext> contextFactory,
        RoleManager<IdentityRole> RoleManager,
        UserManager<ApplicationUser> UserManager)
    {
        _contextFactory = contextFactory;
        roleManager = RoleManager;
        userManager = UserManager;
    }

    public async Task<List<MenuDTO>> GetAllMenusAsync()
    {
        var dc = _contextFactory.CreateDbContext();
        //List<string> userRoles = new List<string>();

        //var user = await userManager.FindByNameAsync(email);

        //if (user != null)
        //{
        //	userRoles = (List<string>)await userManager.GetRolesAsync(user);
        //}

        var mm = (from a in dc.Menus
            orderby a.SortOrder
            select new MenuDTO
            {
                Id = a.Id,
                Name = a.Name,
                Roles = new List<RoleDTO>()
            }).ToList();

        var roles = roleManager.Roles.Select(r => new RoleDTO { Id = r.Id, Name = r.Name, HasAccess = false })
            .OrderBy(mm => mm.Name).ToList();

        foreach (var m in mm)
        {
            m.Roles = new List<RoleDTO>();
            foreach (var role in roles)
            {
                var hasAccess = GetRoleMenuAccess(dc, m.Id, role.Id);
                m.Roles.Add(new RoleDTO { Id = role.Id, Name = role.Name, HasAccess = hasAccess });
            }
        }

        return await Task.FromResult(mm);
    }

    private bool GetRoleMenuAccess(ApplicationDbContext dc, int MenuId, string RoleId)
    {
        var res = (from a in dc.RoleMenus
            where a.RoleId == RoleId &&
                  a.MenuId == MenuId
            select a).FirstOrDefault();
        if (res == null)
            return false;
        return res.HasAccess;
    }

    public Task<string> SaveRoleMenuAccess(int MenuId, string RoleId, bool hasAccess)
    {
        var dc = _contextFactory.CreateDbContext();
        var obj = (from a in dc.RoleMenus
            where a.RoleId == RoleId
                  && a.MenuId == MenuId
            select a).FirstOrDefault();
        if (obj == null)
        {
            obj = new RoleMenu { MenuId = MenuId, RoleId = RoleId, HasAccess = hasAccess };
            dc.RoleMenus.Add(obj);
            dc.SaveChanges();
            return Task.FromResult("OK");
        }

        obj.HasAccess = hasAccess;
        dc.SaveChanges();
        return Task.FromResult("OK");
    }
}