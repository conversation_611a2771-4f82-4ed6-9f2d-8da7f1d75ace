﻿using System.ComponentModel.DataAnnotations;

namespace ElectionAppServer.DTO;

public class SymbolDTO
{
    public int Id { get; set; }

    //[Range(1, 5000)]
    [Required] [StringLength(200)] public string Urdu { get; set; }

    [Required] [StringLength(200)] public string English { get; set; }

    public string URL { get; set; }

    public string ChangeKey { get; set; }

    public string CreatedBy { get; set; }
    public string CreatedOn { get; set; }
    public string ModifiedBy { get; set; }
    public string ModifiedOn { get; set; }
}