﻿using Microsoft.EntityFrameworkCore.Migrations;
using System;

namespace ElectionAppServer.Migrations.ApplicationDb
{
	public partial class AddCodeandEmpCodefields : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.AlterColumn<DateTime>(
				 name: "CreatedDate",
				 table: "ElectionParties",
				 nullable: false,
				 defaultValue: new DateTime(2020, 9, 17, 18, 37, 13, 552, DateTimeKind.Local).AddTicks(3319),
				 oldClrType: typeof(DateTime),
				 oldType: "datetime2",
				 oldDefaultValue: new DateTime(2020, 9, 3, 14, 54, 27, 379, DateTimeKind.Local).AddTicks(9437));

			migrationBuilder.AlterColumn<DateTime>(
				 name: "CreatedDate",
				 table: "ElectionCandidates",
				 nullable: false,
				 defaultValue: new DateTime(2020, 9, 17, 18, 37, 13, 557, DateTimeKind.Local).AddTicks(1983),
				 oldClrType: typeof(DateTime),
				 oldType: "datetime2",
				 oldDefaultValue: new DateTime(2020, 9, 3, 14, 54, 27, 385, DateTimeKind.Local).AddTicks(2162));

			migrationBuilder.AddColumn<string>(
				 name: "Code",
				 table: "AspNetUsers",
				 maxLength: 10,
				 nullable: true);

			migrationBuilder.AddColumn<string>(
				 name: "EmpCode",
				 table: "AspNetUsers",
				 maxLength: 10,
				 nullable: true);
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropColumn(
				 name: "Code",
				 table: "AspNetUsers");

			migrationBuilder.DropColumn(
				 name: "EmpCode",
				 table: "AspNetUsers");

			migrationBuilder.AlterColumn<DateTime>(
				 name: "CreatedDate",
				 table: "ElectionParties",
				 type: "datetime2",
				 nullable: false,
				 defaultValue: new DateTime(2020, 9, 3, 14, 54, 27, 379, DateTimeKind.Local).AddTicks(9437),
				 oldClrType: typeof(DateTime),
				 oldDefaultValue: new DateTime(2020, 9, 17, 18, 37, 13, 552, DateTimeKind.Local).AddTicks(3319));

			migrationBuilder.AlterColumn<DateTime>(
				 name: "CreatedDate",
				 table: "ElectionCandidates",
				 type: "datetime2",
				 nullable: false,
				 defaultValue: new DateTime(2020, 9, 3, 14, 54, 27, 385, DateTimeKind.Local).AddTicks(2162),
				 oldClrType: typeof(DateTime),
				 oldDefaultValue: new DateTime(2020, 9, 17, 18, 37, 13, 557, DateTimeKind.Local).AddTicks(1983));
		}
	}
}
