﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace ElectionAppServer.Migrations.ApplicationDb
{
	public partial class RemovePollingStationUniqueTitleIndexes : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropIndex(
				 name: "IX_PollingStations_ConstituencyId_EnglishTitle",
				 table: "PollingStations");

			migrationBuilder.DropIndex(
				 name: "IX_PollingStations_ConstituencyId_UrduTitle",
				 table: "PollingStations");

			migrationBuilder.CreateIndex(
				 name: "IX_PollingStations_ConstituencyId_EnglishTitle",
				 table: "PollingStations",
				 columns: new[] { "ConstituencyId", "EnglishTitle" });

			migrationBuilder.CreateIndex(
				 name: "IX_PollingStations_ConstituencyId_UrduTitle",
				 table: "PollingStations",
				 columns: new[] { "ConstituencyId", "UrduTitle" });
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropIndex(
				 name: "IX_PollingStations_ConstituencyId_EnglishTitle",
				 table: "PollingStations");

			migrationBuilder.DropIndex(
				 name: "IX_PollingStations_ConstituencyId_UrduTitle",
				 table: "PollingStations");

			migrationBuilder.CreateIndex(
				 name: "IX_PollingStations_ConstituencyId_EnglishTitle",
				 table: "PollingStations",
				 columns: new[] { "ConstituencyId", "EnglishTitle" },
				 unique: true);

			migrationBuilder.CreateIndex(
				 name: "IX_PollingStations_ConstituencyId_UrduTitle",
				 table: "PollingStations",
				 columns: new[] { "ConstituencyId", "UrduTitle" },
				 unique: true);
		}
	}
}
