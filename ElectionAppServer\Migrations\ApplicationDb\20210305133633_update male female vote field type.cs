﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace ElectionAppServer.Migrations.ApplicationDb
{
	public partial class updatemalefemalevotefieldtype : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.AlterColumn<int>(
				 name: "MaleVoters",
				 table: "PollingStations",
				 type: "int",
				 nullable: true,
				 oldClrType: typeof(long),
				 oldType: "bigint");

			migrationBuilder.AlterColumn<int>(
				 name: "FemaleVoters",
				 table: "PollingStations",
				 type: "int",
				 nullable: true,
				 oldClrType: typeof(long),
				 oldType: "bigint");
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.AlterColumn<long>(
				 name: "MaleVoters",
				 table: "PollingStations",
				 type: "bigint",
				 nullable: false,
				 defaultValue: 0L,
				 oldClrType: typeof(int),
				 oldType: "int",
				 oldNullable: true);

			migrationBuilder.AlterColumn<long>(
				 name: "FemaleV<PERSON>",
				 table: "PollingStations",
				 type: "bigint",
				 nullable: false,
				 defaultValue: 0L,
				 oldClrType: typeof(int),
				 oldType: "int",
				 oldNullable: true);
		}
	}
}
