﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace ElectionAppServer.Migrations.ApplicationDb
{
	public partial class Resultslog3 : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropForeignKey(
				 name: "FK_ResultLogs_Namanigars_NamanigarId",
				 table: "ResultLogs");

			migrationBuilder.AddForeignKey(
				 name: "FK_ResultLogs_Namanigars_NamanigarId",
				 table: "ResultLogs",
				 column: "NamanigarId",
				 principalTable: "Namanigars",
				 principalColumn: "Id",
				 onDelete: ReferentialAction.Restrict);
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropForeignKey(
				 name: "FK_ResultLogs_Namanigars_NamanigarId",
				 table: "ResultLogs");

			migrationBuilder.AddForeignKey(
				 name: "FK_ResultLogs_Namanigars_NamanigarId",
				 table: "ResultLogs",
				 column: "NamanigarId",
				 principalTable: "Namanigars",
				 principalColumn: "Id",
				 onDelete: ReferentialAction.Cascade);
		}
	}
}
