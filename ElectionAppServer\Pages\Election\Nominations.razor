﻿@page "/nominations"
@page "/nominations/{assemblyId:int}/{constituencyId:int}"
@inherits OwningComponentBase<NominationDataService>
@using Syncfusion.Blazor.Buttons
@using Syncfusion.Blazor.Inputs
@using Syncfusion.Blazor.Popups
@using Syncfusion.Blazor.DropDowns
@using Syncfusion.Blazor.Notifications
@attribute [Authorize(Roles ="Administrators")]

<SfToast @ref="ToastObj" Title="Alert" ShowProgressBar="true" Timeout="3000">
	<ToastPosition X="Right"></ToastPosition>
</SfToast>


<SfDialog Width="800px" @bind-Visible="@isDlgVisible" @ref="dlgForm" IsModal="true" ShowCloseIcon="true" MinHeight="500px">
	<DialogTemplates>
		<Header>Create Nomination</Header>
		<Content>

			<EditForm Model="@selectedObj" OnValidSubmit="@SaveData">
				<DataAnnotationsValidator />
				<div>
					<div class="row">
						<div class="col-8">
							<div class="row">
								<div class="col">
									<SfDropDownList AllowFiltering="true" TItem="GeneralItemDTO" TValue="int?" 
														 Placeholder="Select - Candidate" @bind-Value="@selectedObj.CandidateId" 
														 DataSource="@candidates_List" FloatLabelType="FloatLabelType.Always" 
														 FilterType="Syncfusion.Blazor.DropDowns.FilterType.Contains">
										<DropDownListFieldSettings Value="Id" Text="EnglishTitle"></DropDownListFieldSettings>
										<DropDownListEvents TItem="GeneralItemDTO" TValue="int?" ValueChange="OnCandidateSelect"></DropDownListEvents>
									</SfDropDownList>
									<ValidationMessage For="@(() => selectedObj.CandidateId)" />
								</div>
							</div>
							<div class="row">
								<div class="col">
									<SfDropDownList AllowFiltering="true" TItem="GeneralItemDTO" TValue="int?" Placeholder="Select - Party" @bind-Value="@selectedObj.PartyId" DataSource="@parties_list" FloatLabelType="FloatLabelType.Always" FilterType="Syncfusion.Blazor.DropDowns.FilterType.Contains">
										<DropDownListEvents TItem="GeneralItemDTO" TValue="int?" ValueChange="OnPartyChange"></DropDownListEvents>
										<DropDownListFieldSettings Value="Id" Text="EnglishTitle"></DropDownListFieldSettings>
										<!--
										<DropDownListTemplates TItem="GeneralItemDTO">
											 <ItemTemplate Context="ctx">

												  @{

														var obj = ctx as GeneralItemDTO;
														//var img = $"media/flags/{obj.Id}.jpg";
														<div>
															 @*<img src="@img" class="myimg" alt="@obj.EnglishTitle" />*@
															 <span class='name'>@obj.EnglishTitle</span>
														</div>
												  }
											 </ItemTemplate>
										</DropDownListTemplates>-->
									</SfDropDownList>
									<ValidationMessage For="@(() => selectedObj.PartyId)" />
								</div>
							</div>
							<div class="row">
								<div class="col">
									<SfComboBox AllowFiltering="true" TItem="GeneralItemDTO" TValue="int?" Placeholder="Select - Symbol" @bind-Value="@selectedObj.SymbolId" DataSource="@symbols_list" FloatLabelType="FloatLabelType.Always" FilterType="Syncfusion.Blazor.DropDowns.FilterType.Contains">
										<ComboBoxFieldSettings Value="Id" Text="EnglishTitle"></ComboBoxFieldSettings>
										<DropDownListTemplates TItem="GeneralItemDTO">
											<ItemTemplate Context="ctx">

												@{

													var obj = ctx as GeneralItemDTO;
													//var img = $"media/symbols/{obj.Id}.jpg";
													<div>
														@*<img src="@img" class="myimg" alt="@obj.EnglishTitle" />*@
														<span class='name'>@obj.EnglishTitle</span>
													</div>
												}
											</ItemTemplate>
										</DropDownListTemplates>
									</SfComboBox>
									<ValidationMessage For="@(() => selectedObj.SymbolId)" />
								</div>
							</div>
							<div class="row">
								<div class="col">
									<SfNumericTextBox  ShowSpinButton="false"  @bind-Value="selectedObj.Weight" TValue="int" Min="0" Max="200" Format="########" Decimals="0" Placeholder="Weight" FloatLabelType="FloatLabelType.Always"></SfNumericTextBox>
									<ValidationMessage For="@(() => selectedObj.Weight)" />
								</div>
							</div>
							<div class="row">
								<div class="col">
									<div class="hh">Party Flag</div>
									@{
										if (selectedObj.PartyId != null)
										{
											var ms = $"media/flags/{selectedObj.PartyId}.jpg";
											<img src="@ms" alt="" width="120px" />
										}
									}
								</div>
								<div class="col">
									<div class="hh">Symbol</div>
									@{
										if (selectedObj.SymbolId != null)
										{
											var ms = $"media/symbols/{selectedObj.SymbolId}.jpg";
											<img src="@ms" alt="" width="120px" />
										}
									}
								</div>
							</div>
							<div class="row mt-2">
								<div class="col">
									<MudButton Color="Color.Primary" Size="Size.Small" Variant="Variant.Filled" StartIcon="@Icons.Material.Filled.Save">Save</MudButton>
								</div>
							</div>
						</div>
						<div class="col-4">
							<div class="row">
								<div class="col">
									@{
										var bmg = "";
										if (selectedObj.CandidateId != null)
										{
											bmg = $"media/candidates/{selectedObj.CandidateId}" + ".jpg";
											<img src="@bmg" class="bimg" />
										}

									}
								</div>
							</div>
						</div>
					</div>
				</div>
			</EditForm>
		</Content>
	</DialogTemplates>
</SfDialog>

<section >

	<div class="row">
		@if (isElecStrcVisible)
		{

			<div class="col">

				<SfComboBox TValue="int?" Placeholder="Assembly" TItem="GeneralItemDTO" @bind-Value="@assemblyId" DataSource="@assemblies_list" FloatLabelType="FloatLabelType.Always">
					<ComboBoxEvents TValue="int?" TItem="GeneralItemDTO" ValueChange="OnChangeAssembly"></ComboBoxEvents>
					<ComboBoxFieldSettings Text="EnglishTitle" Value="Id"></ComboBoxFieldSettings>
				</SfComboBox>
			</div>
			<div class="col">

				<SfComboBox TValue="int?" Placeholder="Constituency" TItem="GeneralItemDTO" @bind-Value="@constituencyId" DataSource="@constituency_list" FloatLabelType="FloatLabelType.Always">
					<ComboBoxEvents TValue="int?" TItem="GeneralItemDTO" ValueChange="OnChangeConstituency"></ComboBoxEvents>
					<ComboBoxFieldSettings Text="EnglishTitle" Value="Id"></ComboBoxFieldSettings>
				</SfComboBox>
			</div>
		}

		<div class="col">

			<SfComboBox TValue="int?" Placeholder="Seat Type" TItem="GeneralItemDTO" @bind-Value="@seatTypeId" DataSource="@seatTypes_list" FloatLabelType="FloatLabelType.Always">
				<ComboBoxEvents TValue="int?" TItem="GeneralItemDTO" ValueChange="OnChangeSeatType"></ComboBoxEvents>
				<ComboBoxFieldSettings Text="EnglishTitle" Value="Id"></ComboBoxFieldSettings>
			</SfComboBox>
		</div>
	</div>

	@if ((electionId ?? 0) > 0 && (phaseId ?? 0) > 0 && (assemblyId ?? 0) > 0 && (constituencyId ?? 0) > 0 && (seatTypeId ?? 0) > 0)
	{

		<SfButton CssClass="e-primary" IsPrimary="true" OnClick="OpenCreateForm">Create Nomination</SfButton>
		<table class="table table-sm">
			<thead>
				<tr>
					<th>Pic</th>
					<th>Name</th>
					<th>Party</th>
					<th>Symbol</th>
					<th>Weight</th>
					<th>&nbsp;</th>
				</tr>
			</thead>
			<tbody>
				@if (nominations != null)
				{
					@foreach (var n in nominations)
					{
						var img = $"media/candidates/{n.CandidateId}.jpg";
						var imgP = $"media/flags/{n.PartyId}.jpg";
						var imgS = $"media/symbols/{n.SymbolId}.jpg";
						<tr>
							<td>
								<img src="@img" style="width:75px; height:100px" class="myimg" />
							</td>
							<td>
								@n.EnglishName<br />
								<span class="urdu-cap">@n.UrduName</span>
							</td>
							<td>
								<img src="@imgP" class="myimg" align="left" />@n.Party<br>
								<span class="urdu-cap">@n.PartyUrdu</span>
							</td>
							<td>
								<img src="@imgS" class="myimg" align="left" />@n.Symbol<br>
								<span class="urdu-cap">@n.SymbolUrdu</span>
							</td>
							<td>@n.Weight</td>
							<td>
								<SfButton CssClass="e-primary" IsPrimary="true" OnClick="@(() => OpenEditForm(n))">Edit</SfButton>
								<SfButton CssClass="e-danger" IsPrimary="false" OnClick="@(() => DeleteRecord(n.Id))">Delete</SfButton>
								@if (n.Type == "Panel")
								{
									<SfButton CssClass="e-info" IsPrimary="false" OnClick="@(() => DeleteRecord(n.Id))"><i class="fas fa-user-friends"></i> Panelist</SfButton>
								}
							</td>
						</tr>
					}
				}
			</tbody>
		</table>

	}
</section>