﻿@page "/namanigars"
@attribute [Authorize(Roles = "Administrators")]

@using BeforeOpenEventArgs = Syncfusion.Blazor.Popups.BeforeOpenEventArgs
@using ButtonType = MudBlazor.ButtonType
@using FilterType = Syncfusion.Blazor.Grids.FilterType
@using SelectionType = Syncfusion.Blazor.Grids.SelectionType
@using Size = MudBlazor.Size
@inherits OwningComponentBase<NamanigarDataService>
<MudText Typo="Typo.h5">Namanigars</MudText>

@*<PageCaption Title="Namanigars"></PageCaption>*@

<SfToast @ref="ToastObj" Title="Alert" ShowProgressBar="true" Timeout="3000">
    <ToastPosition X="Right" Y="Bottom"></ToastPosition>
</SfToast>

<SfDialog @ref="dlgPerformance" ShowCloseIcon="true" CloseOnEscape="true" IsModal="true" @bind-Visible="IsPerformanceFormOpen" Width="700px">
    <DialogTemplates>
        <Header>@NamanigarName - Performance for this Election</Header>
        <Content>
            <EditForm Model="@nnPer" OnValidSubmit="SaveNamanigarPerformance">
                <DataAnnotationsValidator/>
                <ValidationSummary/>


                <div class="row mb-3">
                    <div class="col-md-4">
                        <SfNumericTextBox TValue="int" Min="1" Max="10"
                                          @bind-Value="nnPer.PreElectionPerformance"
                                          Placeholder="Pre-Election Performance"
                                          FloatLabelType="FloatLabelType.Always"/>
                        <ValidationMessage For="@(() => nnPer.PreElectionPerformance)"/>
                    </div>
                    <div class="col-md-4">
                        <SfNumericTextBox TValue="int" Min="1" Max="10"
                                          @bind-Value="nnPer.Reachability"
                                          Placeholder="Reachability"
                                          FloatLabelType="FloatLabelType.Always"/>
                        <ValidationMessage For="@(() => nnPer.Reachability)"/>
                    </div>
                    <div class="col-md-4">
                        <SfNumericTextBox TValue="int" Min="1" Max="10"
                                          @bind-Value="nnPer.DocumentEfficiency"
                                          Placeholder="Document Efficiency"
                                          FloatLabelType="FloatLabelType.Always"/>
                        <ValidationMessage For="@(() => nnPer.DocumentEfficiency)"/>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-4">
                        <SfNumericTextBox TValue="int" Min="1" Max="10"
                                          @bind-Value="nnPer.PreElectionInfoAccuracy"
                                          Placeholder="Pre-Election Info Accuracy"
                                          FloatLabelType="FloatLabelType.Always"/>
                        <ValidationMessage For="@(() => nnPer.PreElectionInfoAccuracy)"/>
                    </div>
                    <div class="col-md-4">
                        <SfNumericTextBox TValue="int" Min="1" Max="10"
                                          @bind-Value="nnPer.ResultDayPerformance"
                                          Placeholder="Result Day Performance"
                                          FloatLabelType="FloatLabelType.Always"/>
                        <ValidationMessage For="@(() => nnPer.ResultDayPerformance)"/>
                    </div>
                    <div class="col-md-4">
                        <SfNumericTextBox TValue="int" Min="1" Max="10"
                                          @bind-Value="nnPer.ResultAccuracy"
                                          Placeholder="Result Accuracy"
                                          FloatLabelType="FloatLabelType.Always"/>
                        <ValidationMessage For="@(() => nnPer.ResultAccuracy)"/>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-4">
                        @*<MudCheckBox @bind-Checked="nnPer.WasAvailableDuringResult"
                                     Label="Was Available During Result"
                                     Color="Color.Primary" />*@
                        <SfCheckBox @bind-Checked="nnPer.WasAvailableDuringResult"
                                    Label="Was Available During Result">
                        </SfCheckBox>
                    </div>
                    <div class="col-md-4">
                        <SfCheckBox @bind-Checked="nnPer.HasBehavioralIssues"
                                    Label="Has Behavioral Issues"/>
                    </div>
                    <div class="col-md-4">
                        <SfDropDownList TItem="GeneralItemDTO"
                                        TValue="int?" DataSource="reportingForList" @bind-Value="nnPer.ReportingFor"
                                        ShowClearButton="true"
                                        Placeholder="Reporting For" FloatLabelType="FloatLabelType.Always">
                            <DropDownListFieldSettings Text="EnglishTitle" Value="Id"></DropDownListFieldSettings>
                        </SfDropDownList>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-12">
                        <SfTextBox Multiline="true"
                                   @bind-Value="nnPer.BehavioralIssueDescription"
                                   Placeholder="Behavioral Issue Description"
                                   FloatLabelType="FloatLabelType.Always"/>
                        <ValidationMessage For="@(() => nnPer.BehavioralIssueDescription)"/>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-12">
                        <SfTextBox Multiline="true"
                                   @bind-Value="nnPer.Trivia"
                                   Placeholder="Trivia"
                                   FloatLabelType="FloatLabelType.Always"/>
                        <ValidationMessage For="@(() => nnPer.Trivia)"/>
                    </div>
                </div>

                <div class="row">
                    <div class="col">
                        <MudButton ButtonType="ButtonType.Submit"
                                   Color="Color.Primary"
                                   Size="Size.Small"
                                   Variant="Variant.Filled"
                                   StartIcon="@Icons.Material.Filled.Save">
                            Save
                        </MudButton>
                    </div>
                </div>
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>

<SfDialog Width="800px" @bind-Visible="IsAssignContDialogOpen" @ref="_dlgAssignCons" ShowCloseIcon="true" CloseOnEscape="true" IsModal="true">
    <DialogTemplates>
        <Header>Assign/Remove Constituencies</Header>
        <Content>
            <div class="mb-2">


                <MudButton Color="Color.Primary" Size="Size.Small" Variant="Variant.Filled"
                           ButtonType="ButtonType.Button"
                           OnClick="AssignCons"
                           StartIcon="@Icons.Material.Filled.CheckCircle">
                    Assign
                </MudButton>
                <MudButton Color="Color.Error" Size="Size.Small" Variant="Variant.Filled"
                           ButtonType="ButtonType.Button"
                           OnClick="RemoveCons"
                           StartIcon="@Icons.Material.Filled.Remove">
                    Remove
                </MudButton>
            </div>
            @if (_constituencies == null)
            {
                <p>Please wait...</p>
            }
            else if (_constituencies.Any())
            {
                <SfGrid DataSource="_constituencies" @ref="dgAssignCons" AllowFiltering="true" AllowSorting="true" Height="450px"
                        AllowSelection="true">
                    <GridFilterSettings Type="FilterType.Excel"></GridFilterSettings>
                    <GridSelectionSettings Type="SelectionType.Multiple"></GridSelectionSettings>
                    <GridColumns>
                        <GridColumn Visible="false" AllowFiltering="true" AutoFit="true" HeaderText="Id" Field="@nameof(AssignConstNamanigarDTO.Id)"></GridColumn>
                        <GridColumn AllowFiltering="true" AutoFit="true" HeaderText="Assembly" Field="@nameof(AssignConstNamanigarDTO.Assembly)"></GridColumn>
                        @*<GridColumn AllowFiltering="true" AutoFit="true" HeaderText="Code" Field="@nameof(AssignConstNamanigarDTO.Code)"></GridColumn>*@
                        <GridColumn AllowFiltering="true" AutoFit="true" HeaderText="Constituency" Field="@nameof(AssignConstNamanigarDTO.Constituency)"></GridColumn>
                        <GridColumn AllowFiltering="true" AutoFit="true" HeaderText="Status" Field="@nameof(AssignConstNamanigarDTO.Status)"></GridColumn>
                        <GridColumn Visible="false" AllowFiltering="true" AutoFit="true" HeaderText="Province" Field="@nameof(AssignConstNamanigarDTO.Province)"></GridColumn>
                        <GridColumn Visible="false" AllowFiltering="true" AutoFit="true" HeaderText="District" Field="@nameof(AssignConstNamanigarDTO.District)"></GridColumn>
                        <GridColumn Visible="false" AllowFiltering="true" AutoFit="true" HeaderText="Region" Field="@nameof(AssignConstNamanigarDTO.Region)"></GridColumn>
                    </GridColumns>
                </SfGrid>
            }
            else
            {
                <p>No constituency is defined yet</p>
            }
        </Content>
    </DialogTemplates>
</SfDialog>


<SfDialog Width="600px" @bind-Visible="@_isDlgVisible" @ref="dlgForm" IsModal="true" ShowCloseIcon="true">
    <DialogEvents OnOpen="BeforeOpen"></DialogEvents>
    <DialogTemplates>
        <Header>@FormTitle</Header>
        <Content>
            <EditForm Model="@_selectedObj" OnValidSubmit="@SaveNamanigarData">
                <DataAnnotationsValidator/>
                <ValidationSummary></ValidationSummary>

                <div class="row">
                    <div class="input-field col-4">
                        <SfTextBox @bind-Value="_selectedObj.Code" Placeholder="Code" FloatLabelType="FloatLabelType.Always"></SfTextBox>
                        <ValidationMessage For="@(() => _selectedObj.Code)"/>
                    </div>
                    <div class="input-field col-8">
                        <SfTextBox @bind-Value="_selectedObj.Name" Placeholder="Name" FloatLabelType="FloatLabelType.Always"></SfTextBox>
                        <ValidationMessage For="@(() => _selectedObj.Name)"/>
                    </div>
                </div>
                <div class="row">
                    <div class="col">
                        <SfTextBox @bind-Value="_selectedObj.District" Placeholder="District" FloatLabelType="FloatLabelType.Always"></SfTextBox>
                        <ValidationMessage For="@(() => _selectedObj.District)"/>
                    </div>
                </div>

                <div class="row">
                    <div class="input-field col-6">
                        <SfTextBox @bind-Value="_selectedObj.Phone"

                                   Placeholder="Phone" FloatLabelType="FloatLabelType.Always">
                        </SfTextBox>
                        <ValidationMessage For="@(() => _selectedObj.Phone)"/>
                    </div>
                    <div class="input-field col-6">
                        <SfTextBox @bind-Value="_selectedObj.Phone2"

                                   Placeholder="Phone 2" FloatLabelType="FloatLabelType.Always">
                        </SfTextBox>
                        <ValidationMessage For="@(() => _selectedObj.Phone2)"/>
                    </div>
                </div>

                <div class="row">
                    <div class="input-field col-6">
                        <SfTextBox @bind-Value="_selectedObj.Phone3"

                                   Placeholder="Phone 3" FloatLabelType="FloatLabelType.Always">
                        </SfTextBox>
                        <ValidationMessage For="@(() => _selectedObj.Phone3)"/>
                    </div>
                    <div class="input-field col-6">
                        <SfTextBox @bind-Value="_selectedObj.Phone4"

                                   Placeholder="Phone 4" FloatLabelType="FloatLabelType.Always">
                        </SfTextBox>
                        <ValidationMessage For="@(() => _selectedObj.Phone4)"/>
                    </div>
                </div>

                <div class="row">

                    <div class="input-field col-6">
                        <SfTextBox @bind-Value="_selectedObj.Email" Placeholder="Email" FloatLabelType="FloatLabelType.Always"></SfTextBox>
                        <ValidationMessage For="@(() => _selectedObj.Email)"/>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md">
                        <SfTextBox @bind-Value="_selectedObj.Address"
                                   Multiline="true"
                                   Placeholder="Address" FloatLabelType="FloatLabelType.Always">
                        </SfTextBox>
                        <ValidationMessage For="@(() => _selectedObj.Address)"/>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md">
                        <SfSwitch @bind-Checked="_selectedObj.IsActive"></SfSwitch> <br/>
                        @_selectedObj.Status
                    </div>
                </div>
                <div class="row">
                    <MudButton ButtonType="ButtonType.Submit"
                               Color="Color.Primary" Size="Size.Small"
                               StartIcon="@Icons.Material.Filled.Save"
                               Variant="Variant.Filled">
                        @SaveButtonText
                    </MudButton>
                </div>

            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>

<!-- AuthorizeView allows us to only show sections of the page -->
<!-- based on the security on the current user -->
<AuthorizeView>
    <!-- Show this section if the user is logged in -->
    <Authorized>
        <section>
            @if (objList == null)
            {
                <p>
                    <em>Loading...</em>
                </p>
            }
            else
            {
                <MudButton Color="Color.Primary" Size="Size.Small" Variant="Variant.Filled" StartIcon="@Icons.Material.Filled.Add" Class="mb-2" OnClick="OpenCreateForm">Create</MudButton>
                <SfGrid DataSource="@objList" AllowFiltering="true" AllowSorting="true" AllowTextWrap="true" Height="calc(100vh - 200px)">
                    <GridFilterSettings Type="FilterType.Excel"></GridFilterSettings>
                    <GridColumns>
                        @*<GridColumn AutoFit="true" Field="Id" HeaderText="Id"></GridColumn>*@
                        <GridColumn AutoFit="true" Field="@nameof(NamanigarFormDTO.Code)" HeaderText="Code"></GridColumn>
                        <GridColumn AutoFit="true" Field="@nameof(NamanigarFormDTO.Name)" HeaderText="Name"></GridColumn>
                        <GridColumn AutoFit="true" Field="@nameof(NamanigarFormDTO.District)" HeaderText="District"></GridColumn>
                        <GridColumn AutoFit="true" Field="@nameof(NamanigarFormDTO.Phone)" HeaderText="Phone"></GridColumn>
                        <GridColumn AutoFit="true" Field="@nameof(NamanigarFormDTO.Email)" HeaderText="Email"></GridColumn>
                        <GridColumn AutoFit="true" Field="@nameof(NamanigarFormDTO.Status)" HeaderText="Status"></GridColumn>
                        <GridColumn AutoFit="true" Field="@nameof(NamanigarFormDTO.IsInCurrentPhase)" HeaderText="Selected For This Phases"></GridColumn>
                        <GridColumn HeaderText="Actions" AllowFiltering="false" AutoFit="true">
                            <Template Context="ss">
                                @{
                                    if (ss is NamanigarFormDTO kk)
                                    {
                                        <MudFab Color="Color.Error" Size="Size.Small" StartIcon="@Icons.Material.Filled.Delete" OnClick="@(() => DeleteRecord(kk.Id))"></MudFab>
                                        <MudFab Color="Color.Primary" Size="Size.Small" StartIcon="@Icons.Material.Filled.Edit" OnClick="@(() => OpenEditForm(kk))"></MudFab>
                                        <MudButton Color="Color.Info" Size="Size.Small" StartIcon="@Icons.Material.Filled.Checklist" OnClick="@(() => AssignConstituencies(kk))">Constituencies</MudButton>
                                        <MudButton Color="Color.Success" Size="Size.Small" StartIcon="@Icons.Material.Filled.BarChart" OnClick="@(() => OpenPerformanceForm(kk))">Performance</MudButton>
                                        if (kk.IsInCurrentPhase == "")
                                        {
                                            <MudButton Color="Color.Secondary"
                                                       Size="Size.Small"
                                                       StartIcon="@Icons.Material.Filled.CheckCircle"
                                                       OnClick="@(() => SelectForThisElection(kk))">
                                                Select For This Election
                                            </MudButton>
                                        }
                                        else
                                        {
                                            <MudButton Color="Color.Error"
                                                       Size="Size.Small"
                                                       StartIcon="@Icons.Material.Filled.CheckCircle"
                                                       OnClick="@(() => ExcludeFromThisElection(kk))">
                                                Remove from This Election
                                            </MudButton>
                                        }
                                    }
                                }
                            </Template>
                        </GridColumn>
                    </GridColumns>
                </SfGrid>
            }
        </section>
    </Authorized>
    <!-- Show this section if the user is not logged in -->
    <NotAuthorized>
        <p>You're not signed in.</p>
    </NotAuthorized>
</AuthorizeView>

@code {

    // AuthenticationState is available as a CascadingParameter
    [CascadingParameter] private Task<AuthenticationState> authenticationStateTask { get; set; }

    private SfDialog dlgForm;
    List<NamanigarFormDTO> objList;
    private bool _isDlgVisible;
    private string SaveButtonText = "Save";
    private readonly string FormTitle = "Create";
    private NamanigarFormDTO _selectedObj = new();
    private SfToast ToastObj;
    public string ToastContent = "Unable to delete Record, Related Record(s) Exist";

    private readonly List<GeneralItemDTO> reportingForList = new()
    {
        new GeneralItemDTO { Id = 1, EnglishTitle = "GEO" },
        new GeneralItemDTO { Id = 2, EnglishTitle = "Jang" },
        new GeneralItemDTO { Id = 3, EnglishTitle = "GEO & Jang Both" }
    };

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        //return base.OnAfterRenderAsync(firstRender);
        if (firstRender)
        {
            var user = (await authenticationStateTask).User;
            if (user.Identity is { IsAuthenticated: true })
            {
                //var dd =  await ProtectedSessionStore.GetAsync<ElectionStateDTO>("election");
                var dd = await localStorage.GetAsync<ElectionStateDTO>(await confreader.GetSettingValue("electionkey"));
                state.SetState(dd.Value);
            }
            else
            {
                try
                {
                    //await  ProtectedSessionStore.DeleteAsync("election");
                    await localStorage.DeleteAsync(await confreader.GetSettingValue("electionkey"));
                    state.SetState(new ElectionStateDTO { ElectionId = 0, ElectionTitle = "", PhaseId = 0, PhaseTitle = "" });
                }
                catch (Exception)
                {
                    // ignored
                }

                NavigationManager.NavigateTo("");
            }

            StateHasChanged();
        }
    }

    protected override async Task OnInitializedAsync()
    {
        // Get the current user
        var user = (await authenticationStateTask).User;
        // Get the objList for the current user
        // We access WeatherobjListervice using @Service
        objList = await Service.GetAll(state.state.PhaseId);
    }

    private void OpenCreateForm()
    {
        _selectedObj = new NamanigarFormDTO();
        dlgForm.ShowAsync();
        SaveButtonText = "Create";
    }

    private async Task OpenEditForm(NamanigarFormDTO st)
    {
        _selectedObj = await Service.GetById(st.Id);
        await dlgForm.ShowAsync();
        SaveButtonText = "Update";
    }


    private async Task AssignConstituencies(NamanigarFormDTO nn)
    {
        _selectedObj = nn;
        _constituencies = await Service.GetNamanigarConstituencies(state.state.ElectionId, nn.Id);
        await _dlgAssignCons.ShowAsync();
    }

    private void ClearData()
    {
        _selectedObj = new NamanigarFormDTO();
    }

    private async Task SaveNamanigarData()
    {
        var user = (await authenticationStateTask).User;
        try
        {
            if (user.Identity != null) await Service.Save(_selectedObj, user.Identity.Name);
            objList = await Service.GetAll(state.state.PhaseId);
            ClearData();
            await dlgForm.HideAsync();
        }
        catch (Exception ex)
        {
            var mm = new ToastModel { Title = "Error", Content = ex.Message, CssClass = "e-toast-danger", ShowProgressBar = true, Timeout = 5000, ShowCloseButton = true };
            if (ex.InnerException != null)
                mm.Content += " - Detail: " + ex.InnerException.Message;
            await ToastObj.ShowAsync(mm);
        }
    }

    private async Task DeleteRecord(int namanigarId)
    {
        var res = await JSRuntime.InvokeAsync<bool>("confirm", "Are you want to delete this record?");
        if (res == false)
            return;


        try
        {
            var msg2 = await Service.Delete(namanigarId);
            objList = await Service.GetAll(state.state.PhaseId);
            ClearData();
        }
        catch (Exception ee)
        {
            var mm = new ToastModel { Title = "Error", Content = ee.Message, CssClass = "e-toast-danger", ShowProgressBar = true, Timeout = 5000, ShowCloseButton = true };
            if (ee.InnerException != null)
                mm.Content += " Detail: " + ee.InnerException.Message;

            await ToastObj.ShowAsync(mm);
        }
    }

    private void BeforeOpen(BeforeOpenEventArgs args)
    {
        args.MaxHeight = null;
    }


    #region Assign Constituency Feature

    private bool IsAssignContDialogOpen;
    private SfDialog _dlgAssignCons;
    private SfGrid<AssignConstNamanigarDTO> dgAssignCons;
    private List<AssignConstNamanigarDTO> _constituencies = new();

    private async Task AssignCons()
    {
        var user = (await authenticationStateTask).User;

        var msg = await Service.AssignCons(_selectedObj.Id, dgAssignCons.SelectedRecords, user.Identity.Name);
        _constituencies = await Service.GetNamanigarConstituencies(state.state.ElectionId, _selectedObj.Id);
    }

    private async Task RemoveCons()
    {
        var msg = await Service.RemoveCons(_selectedObj.Id, dgAssignCons.SelectedRecords);
        _constituencies = await Service.GetNamanigarConstituencies(state.state.ElectionId, _selectedObj.Id);
    }

    #endregion

    private SfDialog dlgPerformance;
    private bool IsPerformanceFormOpen;
    private string NamanigarName = "";
    private NamanigarPerformanceDTO nnPer = new();

    private async Task SavePerformanceData()
    {
    }

    private async Task OpenPerformanceForm(NamanigarFormDTO kk)
    {
        NamanigarName = $"{kk.Code} - {kk.Name}";
        nnPer = await Service.GetNamanigarPerformance(kk.Id, state.state.PhaseId);
        if (nnPer != null)
        {
            await dlgPerformance.ShowAsync();
        }
        else
        {
            nnPer = new NamanigarPerformanceDTO
            {
                NamanigarId = kk.Id,
                ElectionPhaseId = state.state.PhaseId
            };
            await dlgPerformance.ShowAsync();
        }
    }

    private async Task SaveNamanigarPerformance(EditContext arg)
    {
        var user = (await authenticationStateTask).User;
        if (user.Identity != null)
        {
            var msg = await Service.SaveNamanigarPerformance(nnPer, user.Identity.Name);
            if (msg == "OK")
            {
                await dlgPerformance.HideAsync();
            }
        }
    }

    private async Task SelectForThisElection(NamanigarFormDTO kk)
    {
        var user = (await authenticationStateTask).User;
        if (user.Identity != null)
        {
            var phaseId = state.state.PhaseId;
            var msg = await Service.AddNamanigarToElection(phaseId, kk.Id, user.Identity.Name);
            objList = await Service.GetAll(state.state.PhaseId);
        }
    }

    private async Task ExcludeFromThisElection(NamanigarFormDTO kk)
    {
        var user = (await authenticationStateTask).User;
        if (user.Identity != null)
        {
            var phaseId = state.state.PhaseId;
            var msg = await Service.RemoveNamanigarFromElection(phaseId, kk.Id, user.Identity.Name);
            objList = await Service.GetAll(state.state.PhaseId);
        }
    }

}