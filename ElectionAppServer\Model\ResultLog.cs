﻿using System;
using System.Collections.Generic;

namespace ElectionAppServer.Model;

public class ResultLog
{
    public int Id { get; set; }
    public int ConstituencyId { get; set; }
    public Structure Constituency { get; set; }
    public int AssemblyId { get; set; }
    public ElectionAssembly Assembly { get; set; }
    public int PhaseId { get; set; }
    public virtual ElectionPhase Phase { get; set; }
    public int SeatTypeId { get; set; }
    public virtual SeatType SeatType { get; set; }
    public int NamanigarId { get; set; }
    public virtual Namanigar Namanigar { get; set; }
    public string Action { get; set; }
    public int PreviousPollingStations { get; set; }
    public int NewPollingStations { get; set; }
    public int PollingStations { get; set; }
    public DateTime CreatedDate { get; set; }
    public string CreatedByUserId { get; set; }
    public virtual List<ResultLogDetail> ResultLogDetails { get; set; }
}

public class ResultLogDetail
{
    public int Id { get; set; }
    public int ResultLogId { get; set; }
    public virtual ResultLog ResultLog { get; set; }
    public int CandidateId { get; set; }
    public virtual Candidate Candidate { get; set; }
    public int PreviousVotes { get; set; }
    public int NewVotes { get; set; }
    public int Votes { get; set; }
}