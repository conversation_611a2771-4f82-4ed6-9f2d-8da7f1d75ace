﻿using System;

namespace ElectionAppServer.Model;

public class NamanigarResult
{
    public int NominationId { get; set; }
    public virtual Nomination Nomination { get; set; }
    public int Votes { get; set; }
    public int ChangeVotes { get; set; }
    public int ResultPollingStations { get; set; }
    public int ChangePollingStations { get; set; }
    public ApprovalStatus Status { get; set; }
    public bool IsViewed { get; set; }
    public string LastActionMode { get; set; }
    public int BatchId { get; set; }

    #region Audit Log

    public string CreatedBy { get; set; }
    public string ModifiedBy { get; set; }
    public DateTime CreatedOn { get; set; }
    public DateTime? ModifiedOn { get; set; }

    #endregion Audit Log
}

public enum ApprovalStatus
{
    Pending = 0,
    Approved = 1
}

public class NamanigarResultLog
{
    public int Id { get; set; }
    public int NominationId { get; set; }
    public int Votes { get; set; }
    public int ChangeVotes { get; set; }
    public int ResultPollingStations { get; set; }
    public int ChangePollingStations { get; set; }
    public ApprovalStatus Status { get; set; }
    public string LastActionMode { get; set; }
    public DateTime ActionDateTime { get; set; }
    public string NamanigarUserId { get; set; }
    public string ApprovedBy { get; set; }
    public DateTime? ApprovedDate { get; set; }
    public int BatchId { get; set; }
}