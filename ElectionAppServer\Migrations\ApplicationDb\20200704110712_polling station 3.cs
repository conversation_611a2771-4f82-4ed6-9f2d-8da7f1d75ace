﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace ElectionAppServer.Migrations.ApplicationDb
{
	public partial class pollingstation3 : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropForeignKey(
				 name: "FK_Nominations_ElectionPhases_ElectionPhaseId",
				 table: "Nominations");

			migrationBuilder.DropForeignKey(
				 name: "FK_PollingStation_Structures_ConstituencyId",
				 table: "PollingStation");

			migrationBuilder.DropPrimaryKey(
				 name: "PK_PollingStation",
				 table: "PollingStation");

			migrationBuilder.DropIndex(
				 name: "IX_PollingStation_ConstituencyId_Code",
				 table: "PollingStation");

			migrationBuilder.DropColumn(
				 name: "Code",
				 table: "PollingStation");

			migrationBuilder.RenameTable(
				 name: "PollingStation",
				 newName: "PollingStations");

			migrationBuilder.RenameIndex(
				 name: "IX_PollingStation_ConstituencyId_UrduTitle",
				 table: "PollingStations",
				 newName: "IX_PollingStations_ConstituencyId_UrduTitle");

			migrationBuilder.RenameIndex(
				 name: "IX_PollingStation_ConstituencyId_EnglishTitle",
				 table: "PollingStations",
				 newName: "IX_PollingStations_ConstituencyId_EnglishTitle");

			migrationBuilder.AddColumn<string>(
				 name: "Number",
				 table: "PollingStations",
				 unicode: false,
				 maxLength: 200,
				 nullable: false,
				 defaultValue: "");

			migrationBuilder.AddPrimaryKey(
				 name: "PK_PollingStations",
				 table: "PollingStations",
				 column: "Id");

			migrationBuilder.CreateIndex(
				 name: "IX_PollingStations_ConstituencyId_Number",
				 table: "PollingStations",
				 columns: new[] { "ConstituencyId", "Number" },
				 unique: true);

			migrationBuilder.AddForeignKey(
				 name: "FK_Nominations_ElectionPhases_ElectionPhaseId",
				 table: "Nominations",
				 column: "ElectionPhaseId",
				 principalTable: "ElectionPhases",
				 principalColumn: "Id",
				 onDelete: ReferentialAction.Restrict);

			migrationBuilder.AddForeignKey(
				 name: "FK_PollingStations_Structures_ConstituencyId",
				 table: "PollingStations",
				 column: "ConstituencyId",
				 principalTable: "Structures",
				 principalColumn: "Id",
				 onDelete: ReferentialAction.Restrict);
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropForeignKey(
				 name: "FK_Nominations_ElectionPhases_ElectionPhaseId",
				 table: "Nominations");

			migrationBuilder.DropForeignKey(
				 name: "FK_PollingStations_Structures_ConstituencyId",
				 table: "PollingStations");

			migrationBuilder.DropPrimaryKey(
				 name: "PK_PollingStations",
				 table: "PollingStations");

			migrationBuilder.DropIndex(
				 name: "IX_PollingStations_ConstituencyId_Number",
				 table: "PollingStations");

			migrationBuilder.DropColumn(
				 name: "Number",
				 table: "PollingStations");

			migrationBuilder.RenameTable(
				 name: "PollingStations",
				 newName: "PollingStation");

			migrationBuilder.RenameIndex(
				 name: "IX_PollingStations_ConstituencyId_UrduTitle",
				 table: "PollingStation",
				 newName: "IX_PollingStation_ConstituencyId_UrduTitle");

			migrationBuilder.RenameIndex(
				 name: "IX_PollingStations_ConstituencyId_EnglishTitle",
				 table: "PollingStation",
				 newName: "IX_PollingStation_ConstituencyId_EnglishTitle");

			migrationBuilder.AddColumn<string>(
				 name: "Code",
				 table: "PollingStation",
				 type: "varchar(200)",
				 unicode: false,
				 maxLength: 200,
				 nullable: false,
				 defaultValue: "");

			migrationBuilder.AddPrimaryKey(
				 name: "PK_PollingStation",
				 table: "PollingStation",
				 column: "Id");

			migrationBuilder.CreateIndex(
				 name: "IX_PollingStation_ConstituencyId_Code",
				 table: "PollingStation",
				 columns: new[] { "ConstituencyId", "Code" },
				 unique: true);

			migrationBuilder.AddForeignKey(
				 name: "FK_Nominations_ElectionPhases_ElectionPhaseId",
				 table: "Nominations",
				 column: "ElectionPhaseId",
				 principalTable: "ElectionPhases",
				 principalColumn: "Id",
				 onDelete: ReferentialAction.Cascade);

			migrationBuilder.AddForeignKey(
				 name: "FK_PollingStation_Structures_ConstituencyId",
				 table: "PollingStation",
				 column: "ConstituencyId",
				 principalTable: "Structures",
				 principalColumn: "Id",
				 onDelete: ReferentialAction.Restrict);
		}
	}
}
