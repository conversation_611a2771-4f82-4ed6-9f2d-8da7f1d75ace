﻿@page
@model LoginModel

@{
	ViewData["Title"] = "Election Portal Log in";
	Layout = "";
}

<!DOCTYPE html>
<html lang="en">
<head>
	<title>Election Portal</title>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<!--===============================================================================================-->
	<link rel="icon" type="image/png" href="/images/icons/favicon.ico" />
	<!--===============================================================================================-->
	@*<link rel="stylesheet" type="text/css" href="/vendor/bootstrap/css/bootstrap.min.css">*@
	<link href="~/vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet" />
	<!--===============================================================================================-->
	<link rel="stylesheet" type="text/css" href="~/fonts/font-awesome-4.7.0/css/font-awesome.min.css">
	<!--===============================================================================================-->
	<link rel="stylesheet" type="text/css" href="~/fonts/Linearicons-Free-v1.0.0/icon-font.min.css">
	<!--===============================================================================================-->
	<link rel="stylesheet" type="text/css" href="~/vendor/animate/animate.css">
	<!--===============================================================================================-->
	<link rel="stylesheet" type="text/css" href="~/vendor/css-hamburgers/hamburgers.min.css">
	<!--===============================================================================================-->
	<link rel="stylesheet" type="text/css" href="~/vendor/animsition/css/animsition.min.css">
	<!--===============================================================================================-->
	<link rel="stylesheet" type="text/css" href="~/vendor/select2/select2.min.css">
	<!--===============================================================================================-->
	<link rel="stylesheet" type="text/css" href="~/vendor/daterangepicker/daterangepicker.css">
	<!--===============================================================================================-->
	<link rel="stylesheet" type="text/css" href="~/css/util.css">
	<link rel="stylesheet" type="text/css" href="~/css/main.css">
	<!--===============================================================================================-->
	<script>
		localStorage.clear();
	</script>
</head>
<body>

	<div class="limiter">
		<div class="container-login100">
			<div class="wrap-login100">
				<div class="login100-form-title" style="background-image: url(/images/bg-01.jpg);">
					<span class="login100-form-title-1">
						Election Portal Login
					</span>
				</div>

				<form class="login100-form validate-form" id="account" method="post">
					<div class="wrap-input100 validate-input m-b-26" data-validate="Username is required">
						<span class="label-input100">Username</span>
						<input asp-for="Input.Email" class="input100" type="text" placeholder="Enter email">
						<span class="focus-input100"></span>
					</div>

					<div class="wrap-input100 validate-input m-b-18" data-validate="Password is required">
						<span class="label-input100">Password</span>
						<input asp-for="Input.Password" class="input100" type="password" placeholder="Enter password">
						<span class="focus-input100"></span>
					</div>
					<div style="display:none">
						<div class="flex-sb-m w-full p-b-30">
							<div class="contact100-form-checkbox">
								<input asp-for="Input.RememberMe" class="input-checkbox100" id="ckb1" type="checkbox">
								<label class="label-checkbox100" for="ckb1">
									Remember me
								</label>
							</div>

							<div>
								@*<a href="#" class="txt1">
										Forgot Password?
									</a>*@
							</div>
						</div>
					</div>
					<div class="container-login100-form-btn">
						<button type="submit" class="login100-form-btn">
							Login
						</button>
						
					</div>
					<div>
						@Html.ValidationSummary(true, "", new { @class = "text-danger" })
							</div>
				</form>
			</div>
		</div>
	</div>

	<!--===============================================================================================-->
	<script src="~/vendor/jquery/jquery-3.2.1.min.js"></script>
	<!--===============================================================================================-->
	<script src="~/vendor/animsition/js/animsition.min.js"></script>
	<!--===============================================================================================-->
	<script src="~/vendor/bootstrap/js/popper.js"></script>
	<script src="~/vendor/bootstrap/js/bootstrap.min.js"></script>
	<!--===============================================================================================-->
	<script src="~/vendor/select2/select2.min.js"></script>
	<!--===============================================================================================-->
	<script src="~/vendor/daterangepicker/moment.min.js"></script>
	<script src="~/vendor/daterangepicker/daterangepicker.js"></script>
	<!--===============================================================================================-->
	<script src="~/vendor/countdowntime/countdowntime.js"></script>
	<!--===============================================================================================-->
	<script src="~/js/main.js"></script>
</body>
</html>

@section Scripts {
	<partial name="_ValidationScriptsPartial" />
}