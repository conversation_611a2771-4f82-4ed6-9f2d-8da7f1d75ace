﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ElectionAppServer.DTO;
using ElectionAppServer.Model;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Syncfusion.Blazor.Grids;

namespace ElectionAppServer.Pages.Election;

public partial class DistrictWiseParties
{
    private int? DistrictId;
    private int? SeatTypeId;
    private List<DistrictPartyDto> DistrictParties { get; set; }
    private List<GeneralItemDTO> DistrictsList { get; set; }
    private List<GeneralItemDTO> SeatTypesList { get; set; }

    [CascadingParameter] private Task<AuthenticationState> authenticationStateTask { get; set; }

    public ElectionType electionType { get; set; }


    private async Task MoveUp(int id)
    {
        var currentParty = (from p in DistrictParties
            where p.Id == id
            select p).FirstOrDefault();

        var prevParty = (from p in DistrictParties
            where p.SortOrder < currentParty.SortOrder
            orderby p.SortOrder descending
            select p).FirstOrDefault();
        if (prevParty != null)
        {
            await Service.UpdatePartySortOrder(currentParty.Id, prevParty.SortOrder);
            await Service.UpdatePartySortOrder(prevParty.Id, currentParty.SortOrder);
            // update party seq in database
            // swap currentParty.SortOrder with prevParty.SortOrder
            await GetCurrentPartiesSortOrder();
        }
    }

    private async Task MoveDown(int id)
    {
        var currentParty = (from p in DistrictParties
            where p.Id == id
            select p).FirstOrDefault();

        var nextParty = (from p in DistrictParties
            where p.SortOrder > currentParty.SortOrder
            orderby p.SortOrder
            select p).FirstOrDefault();

        if (nextParty != null)
        {
            await Service.UpdatePartySortOrder(currentParty.Id, nextParty.SortOrder);
            await Service.UpdatePartySortOrder(nextParty.Id, currentParty.SortOrder);
            await GetCurrentPartiesSortOrder();
            // update party seq in database
            // swap currentParty.SortOrder with nextParty.SortOrder
        }
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            var user = (await authenticationStateTask).User;
            if (user.Identity.IsAuthenticated)
            {
                //var dd  = await ProtectedSessionStore.GetAsync<ElectionStateDTO>("election");
                var dd = await localStorage.GetAsync<ElectionStateDTO>(await confreader.GetSettingValue("electionkey"));
                electionType = dd.Value.ElectionType.Value;
                state.SetState(dd.Value);
                DistrictsList = await Service.GetElectionDistricts(state.state.PhaseId);
                SeatTypesList = await Service.GetPhaseSeatTypes(state.state.PhaseId);
                StateHasChanged();
            }
            else
            {
                try
                {
                    //await  ProtectedSessionStore.DeleteAsync("election");
                    await localStorage.DeleteAsync(await confreader.GetSettingValue("electionkey"));
                    state.SetState(new ElectionStateDTO
                        { ElectionId = 0, ElectionTitle = "", PhaseId = 0, PhaseTitle = "" });
                }
                catch (Exception)
                {
                    // ignored
                }

                NavigationManager.NavigateTo("");
            }

            StateHasChanged();
        }
    }


    public async Task GetCurrentPartiesSortOrder()
    {
        if (DistrictId != null && SeatTypeId != null)
            DistrictParties =
                await Service.GetCurrentDistrictWiseParties(state.state.PhaseId, DistrictId.Value, SeatTypeId.Value);
        else
            DistrictParties = new List<DistrictPartyDto>();
        StateHasChanged();
    }

    public void RowDropHandler(RowDragEventArgs<DistrictPartyDto> args)
    {
        // Here, you can customize your code.
    }
}