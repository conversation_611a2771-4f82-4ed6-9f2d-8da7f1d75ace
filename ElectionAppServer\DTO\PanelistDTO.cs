﻿using System.ComponentModel.DataAnnotations;

namespace ElectionAppServer.DTO;

public class PanelistDTO
{
    public int NominationId { get; set; }
    public int Id { get; set; }

    [Required] [StringLength(200)] public string NameEnglish { get; set; }

    [Required] [StringLength(200)] public string NameUrdu { get; set; }

    [Required] public int? Gender { get; set; }

    public string GenderText => Gender == 1 ? "Male" : Gender == 2 ? "Female" : Gender == 3 ? "Trans-Gender" : "";

    [Required] public int? SeatTypeId { get; set; }

    public string SeatType { get; set; }
}