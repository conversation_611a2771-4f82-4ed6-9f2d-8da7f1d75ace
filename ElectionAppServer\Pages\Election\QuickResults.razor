﻿@page "/quickresults"

@inject NavigationManager NavigationManager
@inject UserManager<ApplicationUser> _UserManager
@inject RoleManager<IdentityRole> _RoleManager
@inject AuthenticationStateProvider AuthenticationStateProvider

@using ButtonType = MudBlazor.ButtonType
@using FilterType = Syncfusion.Blazor.DropDowns.FilterType
@using Size = MudBlazor.Size
@inherits OwningComponentBase<ElectionResultsDataService>


<SfToast @ref="ToastObj" Title="Alert" ShowProgressBar="true" Timeout="3000">
    <ToastPosition X="Right" Y="Bottom"></ToastPosition>
</SfToast>


<section style="padding: 15px 1rem; margin-top: -3px;">
    <EditForm Model="SelectedObject" OnValidSubmit="SaveRecord">
        <DataAnnotationsValidator/>
        <ValidationSummary/>
        <div class="card border border-primary bg-primary mb-2 text-white">
            <div class="card-header p-2 pl-3">Result Posting </div>
            <div class="card-body text-black bg-light" style="color:black; padding: 8px;">
                <div class="row">
                    <div class="col-md-4">
                        @if (Assmblieslist != null)
                        {
                            <SfDropDownList TItem="GeneralItemDTO" TValue="int?" Placeholder="Select Assembly" @bind-Value="SelectedObject.AssemblyId"
                                            DataSource="@Assmblieslist" FloatLabelType="FloatLabelType.Always" AllowFiltering="true"
                                            FilterType="FilterType.Contains" ShowClearButton="true">
                                <DropDownListFieldSettings Value="Id" Text="EnglishTitle"></DropDownListFieldSettings>
                                <DropDownListEvents TValue="int?" TItem="GeneralItemDTO" ValueChange="@OnAssemblyChange"></DropDownListEvents>
                            </SfDropDownList>
                            <ValidationMessage For="@(() => SelectedObject.AssemblyId)"/>
                        }
                        else
                        {
                            <p></p>
                        }
                    </div>
                    <div class="col-md-4">
                        @if (ConstituencyList != null)
                        {
                            @if (ConstituencyList.Any())
                            {
                                <SfDropDownList AllowFiltering="true" TItem="GeneralItemDTO" TValue="int?" Placeholder="Constituency" @bind-Value="SelectedObject.ConstituencyId"
                                                DataSource="@ConstituencyList" FloatLabelType="FloatLabelType.Always" ShowClearButton="true">
                                    <DropDownListFieldSettings Value="Id" Text="EnglishTitle"></DropDownListFieldSettings>
                                    <DropDownListEvents TValue="int?" TItem="GeneralItemDTO" ValueChange="@OnConstChange"></DropDownListEvents>
                                </SfDropDownList>
                                <ValidationMessage For="@(() => SelectedObject.ConstituencyId)"/>
                            }
                            else
                            {
                                <p>No constituency is defined on this assembly</p>
                            }
                        }

                        else
                        {
                            <p></p>
                        }
                    </div>
                    <div class="col-md-4">
                        @if (SeatTypeList != null)
                        {
                            @if (SeatTypeList.Any())
                            {
                                <SfDropDownList TValue="int?" TItem="GeneralItemDTO" Placeholder="Seat" DataSource="@SeatTypeList" @bind-Value="SelectedObject.SeatTypeId"
                                                FloatLabelType="FloatLabelType.Always" ShowClearButton="true"
                                                AllowFiltering="true" FilterType="FilterType.Contains">
                                    <DropDownListFieldSettings Value="Id" Text="EnglishTitle"></DropDownListFieldSettings>
                                    <DropDownListEvents TValue="int?" TItem="GeneralItemDTO" ValueChange="@OnSeatTypChange"></DropDownListEvents>
                                </SfDropDownList>
                                <ValidationMessage For="@(() => SelectedObject.SeatTypeId)"/>
                            }
                            else
                            {
                                <p>No seat type is define on this assembly</p>
                            }
                        }
                        else
                        {
                            <p></p>
                        }
                    </div>

                </div>
                @*<div class="row">
				<div class="col-md-3">
					<SfNumericTextBox Decimals="0" Format="########0" FloatLabelType="FloatLabelType.Always" Placeholder="Male Voters" @bind-Value="SelectedObject.MaleVoters"></SfNumericTextBox>
					<ValidationMessage For="@(() => SelectedObject.MaleVoters)" />
				</div>
				<div class="col-md-3">
					<SfNumericTextBox Decimals="0" Format="########0" FloatLabelType="FloatLabelType.Always" Placeholder="Female Voters" @bind-Value="SelectedObject.FemaleVoters"></SfNumericTextBox>
				<ValidationMessage For="@(() => SelectedObject.FemaleVoters)" />
				</div>
				<div class="col-md-3">
					<SfNumericTextBox Decimals="0" Format="########0" FloatLabelType="FloatLabelType.Always" Placeholder="Total Polling Stations" @bind-Value="SelectedObject.TotalPollingStations"></SfNumericTextBox>
					<ValidationMessage For="@(() => SelectedObject.TotalPollingStations)" />
				</div>
				<div class="col-md-3">
					<SfNumericTextBox Decimals="0" Format="########0" FloatLabelType="FloatLabelType.Always" Placeholder="Population" @bind-Value="SelectedObject.TotalPopulation"></SfNumericTextBox>
					<ValidationMessage For="@(() => SelectedObject.TotalPopulation)" />
				</div>
			</div>*@
            </div>
        </div>
        <table class="table table-sm">
            <thead>
            <tr>
                <th>Candidate</th>
                <th>Party</th>
                <th>Symbol</th>
                <th>Votes</th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td>
                    <SfTextBox @bind-Value="SelectedObject.CandidateName" Placeholder="Candidate Name"></SfTextBox>
                    <ValidationMessage For="@(() => SelectedObject.CandidateName)"/>
                </td>
                <td>
                    @if (PartiesList != null)
                    {
                        if (PartiesList.Any())
                        {
                            <SfDropDownList TItem="GeneralItemDTO" TValue="int?" Placeholder="Select Party" Width="300px"
                                            DataSource="@PartiesList" AllowFiltering="true"
                                            @bind-Value="SelectedObject.PartyId"
                                            FilterType="FilterType.Contains" ShowClearButton="true">
                                <DropDownListFieldSettings Value="Id" Text="EnglishTitle"></DropDownListFieldSettings>
                                <DropDownListEvents TValue="int?" TItem="GeneralItemDTO" ValueChange="@OnPartyChange"></DropDownListEvents>

                            </SfDropDownList>
                            <ValidationMessage For="@(() => SelectedObject.PartyId)"/>
                        }
                        else
                        {
                            <p>No Party found</p>
                        }
                    }
                    else
                    {
                        <p>Wait...</p>
                    }

                </td>
                <td>
                    @if (SymbolsList != null)
                    {
                        if (SymbolsList.Any())
                        {
                            <SfDropDownList TItem="GeneralItemDTO" TValue="int?" Placeholder="Select Symbol" Width="300px"
                                            DataSource="@SymbolsList" AllowFiltering="true"
                                            @bind-Value="SelectedObject.SymbolId"
                                            FilterType="FilterType.Contains" ShowClearButton="true">
                                <DropDownListFieldSettings Value="Id" Text="EnglishTitle"></DropDownListFieldSettings>
                            </SfDropDownList>
                            <ValidationMessage For="@(() => SelectedObject.SymbolId)"/>
                        }
                        else
                        {
                            <p>No Symbol found</p>
                        }
                    }
                    else
                    {
                        <p>Wait...</p>
                    }

                </td>
                <td>
                    <SfNumericTextBox @bind-Value="SelectedObject.Votes" Format="########0" Decimals="0" Placeholder="Votes"></SfNumericTextBox>
                    <ValidationMessage For="@(() => SelectedObject.Votes)"/>
                </td>
            </tr>
            </tbody>
        </table>
        <div class="row">
            <div class="col">
                <MudButton ButtonType="ButtonType.Submit" Size="Size.Small" Color="Color.Primary" Variant="Variant.Filled">Save</MudButton>
            </div>
        </div>
    </EditForm>
</section>

@*Remarks

Steps to Enter Reaults
1. Select Election Assembly (like Lakkty Marwat Village Councils)
2. Select Constituency
3. Select Seat Type (Chairman only)
4. Enter Candidate Name (This will create candidate in candidate profile)
5. Select Party
6. Select Symbol
7. Enter Votes
8. Save Results

Note:
if results already posted then results only update on normal results posting screen
Only one result is allow to enter
after successfull results posting, constituency will automaticall removed from pending list.

clearty required:
will it push results on ticker/live page?

*@