﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ElectionAppServer.DTO;
using ElectionAppServer.Model;
using Microsoft.EntityFrameworkCore;

//using System.Web.Script.Serialization;
//using Nancy.Json;

namespace ElectionAppServer.Data;

public class ElectionStructureService
{
    // public readonly ApplicationDbContext dc;
    private readonly IDbContextFactory<ApplicationDbContext> _contextFactory;

    public ElectionStructureService(
        IDbContextFactory<ApplicationDbContext> contextFactory) // (ApplicationDbContext  dc)
    {
        //this.dc = dc;
        _contextFactory = contextFactory;
    }

    public Task<string> Delete(int Id)
    {
        using var dc = _contextFactory.CreateDbContext();
        using (var tran = dc.Database.BeginTransaction())
        {
            var q = (from aa in dc.Structures
                where aa.Id == Id
                select aa).FirstOrDefault();
            try
            {
                dc.Remove(q);
                dc.SaveChanges();
                tran.Commit();
                return Task.FromResult("OK");
            }
            catch (Exception)
            {
                tran.Rollback();
                throw new Exception("Unable to delete record, Related data exist");
            }
        }
    }

    public Task<List<GeneralItemDTO>> GetAssemblies(int electionId)
    {
        using var dc = _contextFactory.CreateDbContext();

        var q = (from aa in dc.ElectionAssemblies
            orderby aa.EnglishTitle
            where aa.ElectionId == electionId
            select new GeneralItemDTO
            {
                Id = aa.Id,
                EnglishTitle = aa.EnglishTitle,
                UrduTitle = aa.UrduTitle
            }).ToList();

        return Task.FromResult(q);
        //throw new NotImplementedException();
    }

    public Task<List<GeneralItemDTO>> GetBreadCrum(StructureType st, int Id)
    {
        using var dc = _contextFactory.CreateDbContext();
        var obj = new List<GeneralItemDTO>();

        if (st == StructureType.Division)
        {
            var q = (from aa in dc.Structures.OfType<Province>()
                where aa.Id == Id
                select new
                {
                    aa.ElectionId,
                    Province = aa.EnglishTitle
                }).FirstOrDefault();

            obj.Add(new GeneralItemDTO
                { EnglishTitle = q.Province, Id = q.ElectionId, UrduTitle = $"/administrativeunits/{q.ElectionId}" });
        }
        else if (st == StructureType.District)
        {
            // a[electionId]:Division's Province >
            var q = (from aa in dc.Structures.OfType<Province>()
                where aa.Id == Id
                select new
                {
                    aa.ElectionId,
                    Province = aa.EnglishTitle,
                    ProvinceId = aa.Id
                }).FirstOrDefault();
            obj.Add(new GeneralItemDTO
                { EnglishTitle = q.Province, Id = q.ElectionId, UrduTitle = $"/administrativeunits/{q.ElectionId}" });
        }
        else if (st == StructureType.Town)
        {
            var q = (from aa in dc.Structures.OfType<District>()
                where aa.Id == Id
                select new
                {
                    aa.Division.Province.ElectionId,
                    Province = aa.Division.Province.EnglishTitle,
                    aa.Division.ProvinceId,
                    District = aa.EnglishTitle + " District"
                }).FirstOrDefault();
            obj.Add(new GeneralItemDTO
                { EnglishTitle = q.Province, Id = q.ElectionId, UrduTitle = $"/administrativeunits/{q.ElectionId}" });
            obj.Add(new GeneralItemDTO
                { EnglishTitle = q.District, Id = q.ProvinceId, UrduTitle = $"/districts/{q.ProvinceId}" });
        }
        else if (st == StructureType.UnionCouncil)
        {
            var q = (from aa in dc.Structures.OfType<Town>()
                where aa.Id == Id
                select new
                {
                    aa.District.Division.Province.ElectionId,
                    Province = aa.District.Division.Province.EnglishTitle,
                    aa.District.Division.ProvinceId,
                    District = aa.District.EnglishTitle + " District",
                    Town = aa.EnglishTitle + " Town",
                    aa.DistrictId
                }).FirstOrDefault();
            obj.Add(new GeneralItemDTO
                { EnglishTitle = q.Province, Id = q.ElectionId, UrduTitle = $"/administrativeunits/{q.ElectionId}" });
            obj.Add(new GeneralItemDTO
                { EnglishTitle = q.District, Id = q.ProvinceId, UrduTitle = $"/districts/{q.ProvinceId}" });
            obj.Add(new GeneralItemDTO
                { EnglishTitle = q.Town, Id = q.DistrictId, UrduTitle = $"/towns/{q.DistrictId}" });
        }
        else if (st == StructureType.Ward)
        {
            var q = (from aa in dc.Structures.OfType<UnionCouncil>()
                where aa.Id == Id
                select new
                {
                    aa.Town.District.Division.Province.ElectionId,
                    Province = aa.Town.District.Division.Province.EnglishTitle,
                    aa.Town.District.Division.ProvinceId,
                    District = aa.Town.District.EnglishTitle + " District",
                    Town = aa.Town.EnglishTitle + " Town",
                    aa.Town.DistrictId,
                    UnionCouncil = aa.EnglishTitle + " Union Council",
                    aa.TownId
                }).FirstOrDefault();
            obj.Add(new GeneralItemDTO
                { EnglishTitle = q.Province, Id = q.ElectionId, UrduTitle = $"/administrativeunits/{q.ElectionId}" });
            obj.Add(new GeneralItemDTO
                { EnglishTitle = q.District, Id = q.ProvinceId, UrduTitle = $"/districts/{q.ProvinceId}" });
            obj.Add(new GeneralItemDTO
                { EnglishTitle = q.Town, Id = q.DistrictId, UrduTitle = $"/towns/{q.DistrictId}" });
            obj.Add(new GeneralItemDTO
                { EnglishTitle = q.UnionCouncil, Id = q.TownId, UrduTitle = $"/ucs/{q.TownId}" });
        }

        return Task.FromResult(obj);
    }

    public Task<List<GeneralItemDTO>> GetCastesList()
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.Castes
            orderby aa.EnglishTitle
            select new GeneralItemDTO
            {
                Id = aa.Id,
                EnglishTitle = aa.EnglishTitle,
                UrduTitle = aa.UrduTitle
            }).ToList();
        return Task.FromResult(q);
    }

    public Task<string> GetElectionTitle(int electionId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = dc.Elections.Find(electionId);
        if (q != null)
            return Task.FromResult(q.EnglishTitle);
        return Task.FromResult("");
    }

    public Task<List<GeneralItemDTO>> GetLanguages()
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.Languages
            orderby aa.EnglishTitle
            select new GeneralItemDTO
            {
                Id = aa.Id,
                EnglishTitle = aa.EnglishTitle,
                UrduTitle = aa.UrduTitle
            }).ToList();

        return Task.FromResult(q);
    }

    public Task<List<ElcStructureDTO>> GetList(StructureType st, int parentId, int phaseId, int? id = null)
    {
        using var dc = _contextFactory.CreateDbContext();
        if (st == StructureType.Province)
        {
            var q = (from aa in dc.Structures.OfType<Province>()
                where aa.ElectionId == parentId
                      && (id == null || aa.Id == id)
                orderby aa.EnglishTitle
                select new ElcStructureDTO
                {
                    Id = aa.Id,
                    Code = aa.Code,
                    EnglishTitle = aa.EnglishTitle,
                    ParentId = parentId,
                    UrduTitle = aa.UrduTitle,
                    SubUnits = aa.Divisions.Count,
                    CreateBy = aa.CreatedBy,
                    CreatedDate = aa.CreatedDate.ToString("d MMM, yyyy h:mm"),
                    ModifiedBy = aa.ModifiedBy,
                    ModifiedDate = aa.ModifiedDate == null ? "" : aa.ModifiedDate.Value.ToString("d MMM, yyyy h:mm")
                }).ToList();
            q = GetPollingSchmed(dc, q, phaseId);
            return Task.FromResult(q);
        }

        if (st == StructureType.Division)
        {
            var q = (from aa in dc.Structures.OfType<Division>()
                orderby aa.EnglishTitle
                where aa.ProvinceId == parentId
                      && (id == null || aa.Id == id)
                select new ElcStructureDTO
                {
                    Id = aa.Id,
                    Code = aa.Code,
                    EnglishTitle = aa.EnglishTitle,
                    FemaleVoters = aa.FemaleVoters,
                    ParentId = parentId,
                    UrduTitle = aa.UrduTitle,
                    SubUnits = aa.Districts.Count,
                    CreateBy = aa.CreatedBy,
                    CreatedDate = aa.CreatedDate.ToString("d MMM, yyyy h:mm"),
                    ModifiedBy = aa.ModifiedBy,
                    ModifiedDate = aa.ModifiedDate == null ? "" : aa.ModifiedDate.Value.ToString("d MMM, yyyy h:mm")
                }).ToList();
            q = GetPollingSchmed(dc, q, phaseId);
            return Task.FromResult(q);
        }

        if (st == StructureType.District)
        {
            var q = (from aa in dc.Structures.OfType<District>()
                orderby aa.EnglishTitle
                where aa.Division.ProvinceId == parentId
                      && (id == null || aa.Id == id)
                select new ElcStructureDTO
                {
                    Id = aa.Id,
                    RegionEnglish = aa.Region == null ? "" : aa.Region.EnglishTitle,
                    RegionUrdu = aa.Region == null ? "" : aa.Region.UrduTitle,
                    RegionId = aa.RegionId,
                    Code = aa.Code,
                    EnglishTitle = aa.EnglishTitle,
                    FemaleVoters = aa.FemaleVoters,
                    ParentId = parentId,
                    UrduTitle = aa.UrduTitle,
                    SubUnits = aa.Towns.Count,
                    CreateBy = aa.CreatedBy,
                    CreatedDate = aa.CreatedDate.ToString("d MMM, yyyy h:mm"),
                    ModifiedBy = aa.ModifiedBy,
                    ModifiedDate = aa.ModifiedDate == null ? "" : aa.ModifiedDate.Value.ToString("d MMM, yyyy h:mm"),
                    DivisionEnglish = aa.AdminDivision.EnglishTitle
                }).ToList();
            q = GetPollingSchmed(dc, q, phaseId);
            return Task.FromResult(q);
        }

        if (st == StructureType.Town)
        {
            var q = (from aa in dc.Structures.OfType<Town>()
                orderby aa.EnglishTitle
                where aa.DistrictId == parentId
                      && (id == null || aa.Id == id)
                select new ElcStructureDTO
                {
                    Id = aa.Id,
                    Code = aa.Code,
                    EnglishTitle = aa.EnglishTitle,
                    ParentId = parentId,
                    Problems = aa.Problems,
                    UrduTitle = aa.UrduTitle,
                    SubUnits = aa.UnionCouncils.Count,
                    CreateBy = aa.CreatedBy,
                    CreatedDate = aa.CreatedDate.ToString("d MMM, yyyy h:mm"),
                    ModifiedBy = aa.ModifiedBy,
                    ModifiedDate = aa.ModifiedDate == null ? "" : aa.ModifiedDate.Value.ToString("d MMM, yyyy h:mm")
                }).ToList();
            q = GetPollingSchmed(dc, q, phaseId);
            return Task.FromResult(q);
        }

        if (st == StructureType.UnionCouncil)
        {
            var q = (from aa in dc.Structures.OfType<UnionCouncil>()
                where aa.TownId == parentId
                      && (id == null || aa.Id == id)
                orderby aa.EnglishTitle
                select new ElcStructureDTO
                {
                    Id = aa.Id,
                    Code = aa.Code,
                    EnglishTitle = aa.EnglishTitle,
                    ParentId = parentId,
                    UrduTitle = aa.UrduTitle,
                    SubUnits = aa.Wards.Count,
                    CreateBy = aa.CreatedBy,
                    CreatedDate = aa.CreatedDate.ToString("d MMM, yyyy h:mm"),
                    ModifiedBy = aa.ModifiedBy,
                    ModifiedDate = aa.ModifiedDate == null ? "" : aa.ModifiedDate.Value.ToString("d MMM, yyyy h:mm"),
                    Assembly = aa.Assembly != null ? aa.Assembly.UrduTitle : ""
                }).ToList();
            q = GetPollingSchmed(dc, q, phaseId);
            return Task.FromResult(q);
        }

        if (st == StructureType.Ward)
        {
            var q = (from aa in dc.Structures.OfType<Ward>()
                orderby aa.EnglishTitle
                where aa.UnionCouncilId == parentId
                      && (id == null || aa.Id == id)
                select new ElcStructureDTO
                {
                    Id = aa.Id,
                    Code = aa.Code,
                    EnglishTitle = aa.EnglishTitle,
                    ParentId = parentId,
                    Trivia = aa.Trivia,
                    UrduTitle = aa.UrduTitle,
                    SubUnits = 0,
                    CreateBy = aa.CreatedBy,
                    CreatedDate = aa.CreatedDate.ToString("d MMM, yyyy h:mm"),
                    ModifiedBy = aa.ModifiedBy,
                    ModifiedDate = aa.ModifiedDate == null ? "" : aa.ModifiedDate.Value.ToString("d MMM, yyyy h:mm"),
                    Assembly = aa.Assembly != null ? aa.Assembly.EnglishTitle : ""
                }).ToList();
            q = GetPollingSchmed(dc, q, phaseId);
            return Task.FromResult(q);
        }

        throw new Exception("Missing Structure Type");
    }

    private List<ElcStructureDTO> GetPollingSchmed(ApplicationDbContext dc, List<ElcStructureDTO> q, int phaseid)
    {
        foreach (var c in q)
        {
            var ps = (from a in dc.PollingSchemes
                where a.StructureId == c.Id && a.PhaseId == phaseid
                select a).FirstOrDefault();
            if (ps != null)
            {
                c.MaleVoters = ps.MaleVoters;
                c.FemaleVoters = ps.FemaleVoters;
                c.YouthVoters = ps.YouthVoters;
                c.TotalPollingStations = ps.TotalPollingStations;
                c.Languages = ps.Languages;
                c.Castes = ps.Castes;
                c.Trivia = ps.Trivia;
                c.GeneralTrivia = ps.GeneralTrivia;
                c.ImportantPoliticalPersonalities = ps.ImportantPoliticalPersonalities;
                c.Problems = ps.Problems;
                c.ImportantAreas = ps.ImportantAreas;
                c.RuralAreaPer = ps.RuralAreaPer;
                c.UrbanAreaPer = ps.UrbanAreaPer;
                c.Population = ps.Population;
                c.MajorityIncomeSource = ps.MajorityIncomeSource;
                c.LiteracyRate = ps.LiteracyRate;
                c.AverageHouseholdIncome = ps.AverageHouseholdIncome;
                c.Area = ps.Area;
            }
        }

        return q;
    }

    public Task<string> GetNewUCCode(int townId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.Structures.OfType<UnionCouncil>()
            where aa.TownId == townId
            select aa).ToList();

        if (!q.Any()) return Task.FromResult("UC-1");

        var max = 1;
        foreach (var item in q)
            try
            {
                var c = int.Parse(item.Code.Replace("UC-", ""));
                if (c > max)
                    max = c;
            }
            catch (Exception)
            {
                // ignored
            }

        return Task.FromResult($"UC-{max + 1}");
    }


    public Task<List<AdminDivisionDTO>> GetDivisions(int provinceId)
    {
        using var dc = _contextFactory.CreateDbContext();
        //var bb = (from aa in dc.Structures.OfType<Division>()
        //			 where aa.ProvinceId == provinceId
        //			 select aa.ProvinceId).FirstOrDefault();

        var regions = (from aa in dc.AdminDivisions
            where aa.ProvinceId == provinceId
            orderby aa.EnglishTitle
            select new AdminDivisionDTO
            {
                Id = aa.Id,
                ProvinceId = aa.ProvinceId,
                Province = aa.Province.EnglishTitle,
                EnglishTitle = aa.EnglishTitle,
                UrduTitle = aa.UrduTitle
            }).ToList();

        return Task.FromResult(regions);
    }

    public Task<List<RegionDTO>> GetRegions(int provinceId)
    {
        using var dc = _contextFactory.CreateDbContext();
        //var bb = (from aa in dc.Structures.OfType<Division>()
        //			 where aa.ProvinceId == provinceId
        //			 select aa.ProvinceId).FirstOrDefault();

        var regions = (from aa in dc.Regions
            where aa.ProvinceId == provinceId
            orderby aa.EnglishTitle
            select new RegionDTO
            {
                Id = aa.Id,
                ProvinceId = aa.ProvinceId,
                Province = aa.Province.EnglishTitle,
                EnglishTitle = aa.EnglishTitle,
                UrduTitle = aa.UrduTitle
            }).ToList();

        return Task.FromResult(regions);
    }

    public Task<ElcStructureDTO> Save(ElcStructureDTO obj, string user, int PhaseId)
    {
        using var dc = _contextFactory.CreateDbContext();
        obj.Area = (obj.Area ?? "").Trim();
        obj.AverageHouseholdIncome = (obj.AverageHouseholdIncome ?? "").Trim();
        obj.EnglishTitle = (obj.EnglishTitle ?? "").Trim();
        obj.Castes = (obj.Castes ?? "").Trim();
        obj.Code = (obj.Code ?? "").Trim();
        obj.UrduTitle = (obj.UrduTitle ?? "").Trim();
        
        #region Remove and move code for phase wise polling scheme

        //obj.ImportantAreas = (obj.ImportantAreas ?? "").Trim();
        //obj.ImportantPoliticalPersonalities = (obj.ImportantPoliticalPersonalities ?? "").Trim();
        //obj.Languages = (obj.Languages ?? "").Trim();
        //obj.LiteracyRate = (obj.LiteracyRate ?? "").Trim();
        //obj.MajorityIncomeSource = (obj.MajorityIncomeSource ?? "").Trim();
        //obj.Problems = (obj.Problems ?? "").Trim();
        //obj.Trivia = (obj.Trivia ?? "").Trim();
        //obj.GeneralTrivia = (obj.GeneralTrivia ?? "").Trim(); 

        #endregion

        //obj.Code = string.IsNullOrEmpty(obj.Code) ? "" : obj.Code.Trim();
        obj.Code = (obj.Code ?? "").Trim();

        ValidateData(obj.StructureType, obj);
        if (obj.Id == 0)
        {
            Structure dd = null;

            if (obj.StructureType == StructureType.Province)
            {
                dd = new Province { ElectionId = obj.ElectionId };
            }
            else if (obj.StructureType == StructureType.Division)
            {
                dd = new Division { ProvinceId = (int)obj.ParentId, ElectionId = obj.ElectionId };
            }
            else if (obj.StructureType == StructureType.District)
            {
                var divObj = (from aa in dc.Structures.OfType<Division>()
                    where aa.ProvinceId == obj.ProvinceId
                    select aa).FirstOrDefault();
                if (divObj == null)
                    throw new Exception($"Division not found having province id {obj.ParentId}");
                dd = new District { DivisionId = divObj.Id, ElectionId = obj.ElectionId, RegionId = obj.RegionId };
            }
            else if (obj.StructureType == StructureType.Town)
            {
                dd = new Town { DistrictId = (int)obj.ParentId, ElectionId = obj.ElectionId };
            }
            else if (obj.StructureType == StructureType.UnionCouncil)
            {
                dd = new UnionCouncil { TownId = (int)obj.ParentId, ElectionId = obj.ElectionId };
            }
            else if (obj.StructureType == StructureType.Ward)
            {
                dd = new Ward { UnionCouncilId = (int)obj.ParentId, ElectionId = obj.ElectionId };
            }

            if (dd != null)
            {
                //dd.CasteId = obj.CasteId;
                dd.Castes = (obj.Castes ?? "").Trim();
                dd.Code = (obj.Code ?? "").Trim();
                dd.CreatedBy = user;
                dd.CreatedDate = DateTime.Now;
                dd.EnglishTitle = (obj.EnglishTitle ?? "").Trim();
                dd.UrduTitle = (obj.UrduTitle ?? "").Trim();

                #region Move code for phase wise polling schemes

                //dd.FemaleVoters = obj.FemaleVoters;
                //dd.ImportantAreas = obj.ImportantAreas;
                //dd.Languages = (obj.Languages.Trim() ?? "").Trim();
                //dd.MaleVoters = obj.MaleVoters;
                //dd.Problems = obj.Problems;
                //dd.TotalPollingStations = obj.TotalPollingStations;
                //dd.UrbanAreaPer = obj.UrbanAreaPer;
                //dd.UrduTitle = obj.UrduTitle;
                //dd.RuralAreaPer = obj.RuralAreaPer;
                //dd.Trivia = obj.Trivia;
                //dd.ImportantPoliticalPersonalities = obj.ImportantPoliticalPersonalities;
                //dd.Population = obj.Population;
                //dd.MajorityIncomeSource = obj.MajorityIncomeSource;

                ////dd.LanguageId = obj.LanguageId;
                //dd.AverageHouseholdIncome = obj.AverageHouseholdIncome;
                //dd.LiteracyRate = obj.LiteracyRate;
                //dd.GeneralTrivia = obj.GeneralTrivia;
                //dd.Area = obj.Area;

                #endregion

                dd.IsSeat = false;
                dd.AssemblyId = obj.AssemblyId;
                dc.Add(dd);
                dc.SaveChanges();
                obj.Id = dd.Id;
                #region Remove and update Problems

                var j = dc.StructureProblems
                    .Where(c => c.StructureId == obj.Id)
                    .ExecuteDelete();

                foreach (var p in obj.ProblemsList)
                {
                    dc.StructureProblems.Add(new StructureProblem() { StructureId = obj.Id, ProblemId = p });
                    dc.SaveChanges();
                }

                #endregion
                #region Save Phase wise polling schemes

                var ps = new PollingScheme
                {
                    StructureId = dd.Id,
                    PhaseId = PhaseId,
                    Area = obj.Area,
                    AverageHouseholdIncome = obj.AverageHouseholdIncome,
                    Castes = obj.Castes,
                    CreatedBy = user,
                    CreatedDate = DateTime.Now,
                    GeneralTrivia = obj.GeneralTrivia,
                    FemaleVoters = obj.MaleVoters,
                    Languages = obj.Languages,
                    ImportantPoliticalPersonalities = obj.ImportantPoliticalPersonalities,
                    ImportantAreas = obj.ImportantAreas,
                    LiteracyRate = obj.LiteracyRate,
                    MajorityIncomeSource = obj.MajorityIncomeSource,
                    MaleVoters = obj.MaleVoters,
                    YouthVoters = obj.YouthVoters,
                    Population = obj.Population,
                    Problems = obj.Problems,
                    RuralAreaPer = obj.RuralAreaPer,
                    TotalPollingStations = obj.TotalPollingStations,
                    Trivia = obj.Trivia,
                    UrbanAreaPer = obj.UrbanAreaPer,
                    Profile = obj.Profile
                };
                dc.PollingSchemes.Add(ps);
                dc.SaveChanges();

                #endregion

                if (obj.StructureType == StructureType.UnionCouncil && obj.TotalWards > 0)
                {
                    for (var i = 0; i < obj.TotalWards; i++)
                    {
                        var w = new Ward
                        {
                            EnglishTitle = $"Ward {i + 1}",
                            UrduTitle = $"وارڈ - {i + 1}",
                            Code = $"{obj.Code} - W{i + 1}",
                            UnionCouncilId = obj.Id,
                            ElectionId = obj.ElectionId,
                            CreatedDate = DateTime.Now,
                            CreatedBy = user
                        };
                        dc.Structures.Add(w);
                        dc.SaveChanges();
                    }
                }
                else if (obj.StructureType == StructureType.Province)
                {
                    var nDiv = new Division
                    {
                        EnglishTitle = dd.EnglishTitle + " Division",
                        UrduTitle = dd.UrduTitle + " ڈیویژن",
                        ProvinceId = dd.Id,
                        ElectionId = dd.ElectionId,
                        CreatedBy = user,
                        CreatedDate = DateTime.Now
                    };
                    dc.Structures.Add(nDiv);
                    dc.SaveChanges();
                }

                return Task.FromResult(obj);
            }

            throw new Exception("Structure Type is not provided");
        }
        else
        {
            var dd = (from aa in dc.Structures
                where aa.Id == obj.Id
                select aa).FirstOrDefault();

            //dd.CasteId = obj.CasteId;
            dd.Castes = obj.Castes;
            dd.Code = obj.Code;
            dd.ModifiedBy = user;
            dd.ModifiedDate = DateTime.Now;
            dd.EnglishTitle = obj.EnglishTitle;
            dd.IsSeat = false;
            dd.UrduTitle = obj.UrduTitle;
            
            #region Removed Code for phase wise polling schemes

            //dd.FemaleVoters = obj.FemaleVoters;
            //dd.ImportantAreas = obj.ImportantAreas;

            ////dd.LanguageId = obj.LanguageId;
            //dd.Languages = obj.Languages;
            //dd.MaleVoters = obj.MaleVoters;
            //dd.Problems = obj.Problems;
            //dd.TotalPollingStations = obj.TotalPollingStations;
            //dd.UrbanAreaPer = obj.UrbanAreaPer;
            //dd.RuralAreaPer = obj.RuralAreaPer;
            //dd.Trivia = obj.Trivia;
            //dd.ImportantPoliticalPersonalities = obj.ImportantPoliticalPersonalities;
            //dd.Population = obj.Population;
            //dd.LiteracyRate = obj.LiteracyRate;
            //dd.MajorityIncomeSource = obj.MajorityIncomeSource;
            //dd.AverageHouseholdIncome = obj.AverageHouseholdIncome;
            //dd.Area = obj.Area;
            //dd.GeneralTrivia = obj.GeneralTrivia;

            #endregion

            dd.AssemblyId = obj.AssemblyId;
            dc.SaveChanges();
            #region Remove and update Problems

            var j = dc.StructureProblems
                .Where(c => c.StructureId == obj.Id)
                .ExecuteDelete();

            foreach (var p in obj.ProblemsList)
            {
                dc.StructureProblems.Add(new StructureProblem() { StructureId = obj.Id, ProblemId = p });
                dc.SaveChanges();
            }

            #endregion
            #region Save Phase wise Polling Scheme

            var ps = (from a in dc.PollingSchemes
                where a.StructureId == obj.Id && a.PhaseId == PhaseId
                select a).FirstOrDefault();

            if (ps == null)
            {
                ps = new PollingScheme
                {
                    StructureId = obj.Id,
                    PhaseId = PhaseId,
                    CreatedBy = user,
                    CreatedDate = DateTime.Now,
                    Area = obj.Area,
                    ImportantAreas = obj.ImportantAreas,
                    GeneralTrivia = obj.GeneralTrivia,
                    FemaleVoters = obj.FemaleVoters,
                    YouthVoters = obj.YouthVoters,
                    Castes = obj.Castes,
                    AverageHouseholdIncome = obj.AverageHouseholdIncome,
                    UrbanAreaPer = obj.UrbanAreaPer,
                    RuralAreaPer = obj.RuralAreaPer,
                    ImportantPoliticalPersonalities = obj.ImportantPoliticalPersonalities,
                    Languages = obj.Languages,
                    LiteracyRate = obj.LiteracyRate,
                    MajorityIncomeSource = obj.MajorityIncomeSource,
                    MaleVoters = obj.MaleVoters,
                    ModifiedBy = user,
                    ModifiedDate = DateTime.Now,
                    Population = obj.Population,
                    Problems = obj.Problems,
                    TotalPollingStations = obj.TotalPollingStations,
                    Trivia = obj.Trivia,
                    Profile = obj.Profile
                };
                dc.PollingSchemes.Add(ps);
                dc.SaveChanges();
            }
            else
            {
                ps.Area = obj.Area;
                ps.ImportantAreas = obj.ImportantAreas;
                ps.GeneralTrivia = obj.GeneralTrivia;
                ps.FemaleVoters = obj.FemaleVoters;
                ps.Castes = obj.Castes;
                ps.AverageHouseholdIncome = obj.AverageHouseholdIncome;
                ps.UrbanAreaPer = obj.UrbanAreaPer;
                ps.RuralAreaPer = obj.RuralAreaPer;
                ps.ImportantPoliticalPersonalities = obj.ImportantPoliticalPersonalities;
                ps.Languages = obj.Languages;
                ps.LiteracyRate = obj.LiteracyRate;
                ps.MajorityIncomeSource = obj.MajorityIncomeSource;
                ps.MaleVoters = obj.MaleVoters;
                ps.ModifiedBy = user;
                ps.ModifiedDate = DateTime.Now;
                ps.Population = obj.Population;
                ps.Problems = obj.Problems;
                ps.TotalPollingStations = obj.TotalPollingStations;
                ps.Trivia = obj.Trivia;
                ps.Profile = obj.Profile;
                ps.YouthVoters = obj.YouthVoters;
                dc.SaveChanges();
            }

            #endregion

            #region if district

            var q = (from aa in dc.Structures.OfType<District>()
                where aa.Id == obj.Id
                select aa).FirstOrDefault();

            if (q != null)
            {
                q.RegionId = obj.RegionId;
                q.AdminDivisionId = obj.DivisionId;
            }

            dc.SaveChanges();

            #endregion if district

            obj.Id = dd.Id;
            return Task.FromResult(obj);
        }
    }

    private void ValidateData(StructureType st, ElcStructureDTO obj)
    {
        using var dc = _contextFactory.CreateDbContext();
        if (st == StructureType.Province)
        {
            var isExist = (from aa in dc.Structures.OfType<Province>()
                where aa.EnglishTitle == obj.EnglishTitle
                      && aa.Id != obj.Id
                      && aa.ElectionId == obj.ParentId
                select aa).Any();
            if (isExist)
                throw new Exception($"{obj.EnglishTitle} already exist");

            isExist = (from aa in dc.Structures.OfType<Province>()
                where aa.UrduTitle == obj.UrduTitle
                      && aa.Id != obj.Id
                      && aa.ElectionId == obj.ParentId
                select aa).Any();

            if (isExist)
                throw new Exception($"{obj.UrduTitle} already exist");
        }
        else if (st == StructureType.Division)
        {
            var isExist = (from aa in dc.Structures.OfType<Division>()
                where aa.EnglishTitle == obj.EnglishTitle
                      && aa.Id != obj.Id
                      && aa.ProvinceId == obj.ParentId
                select aa).Any();
            if (isExist)
                throw new Exception($"{obj.EnglishTitle} already exist");

            isExist = (from aa in dc.Structures.OfType<Division>()
                where aa.UrduTitle == obj.UrduTitle
                      && aa.Id != obj.Id
                      && aa.ProvinceId == obj.ParentId
                select aa).Any();

            if (isExist)
                throw new Exception($"{obj.UrduTitle} already exist");
        }
        else if (st == StructureType.District)
        {
            var isExist = (from aa in dc.Structures.OfType<District>()
                where aa.EnglishTitle == obj.EnglishTitle
                      && aa.Id != obj.Id
                      && aa.DivisionId == obj.ParentId
                select aa).Any();
            if (isExist)
                throw new Exception($"{obj.EnglishTitle} already exist");

            isExist = (from aa in dc.Structures.OfType<District>()
                where aa.UrduTitle == obj.UrduTitle
                      && aa.Id != obj.Id
                      && aa.DivisionId == obj.ParentId
                select aa).Any();

            if (isExist)
                throw new Exception($"{obj.UrduTitle} already exist");
        }
        else if (st == StructureType.Town)
        {
            var isExist = (from aa in dc.Structures.OfType<Town>()
                where aa.EnglishTitle == obj.EnglishTitle
                      && aa.Id != obj.Id
                      && aa.DistrictId == obj.ParentId
                select aa).Any();
            if (isExist)
                throw new Exception($"{obj.EnglishTitle} already exist");

            isExist = (from aa in dc.Structures.OfType<Town>()
                where aa.UrduTitle == obj.UrduTitle
                      && aa.Id != obj.Id
                      && aa.DistrictId == obj.ParentId
                select aa).Any();

            if (isExist)
                throw new Exception($"{obj.UrduTitle} already exist");
        }
        else if (st == StructureType.UnionCouncil)
        {
            if (obj.Code == "")
                throw new Exception("Union Council Code is required");

            var isExist = (from aa in dc.Structures.OfType<UnionCouncil>()
                where aa.EnglishTitle == obj.EnglishTitle
                      && aa.Id != obj.Id
                      && aa.TownId == obj.ParentId
                select aa).Any();
            if (isExist)
                throw new Exception($"{obj.EnglishTitle} already exist");

            isExist = (from aa in dc.Structures.OfType<UnionCouncil>()
                where aa.UrduTitle == obj.UrduTitle
                      && aa.Id != obj.Id
                      && aa.TownId == obj.ParentId
                select aa).Any();

            if (isExist)
                throw new Exception($"{obj.UrduTitle} already exist");

            isExist = (from aa in dc.Structures.OfType<UnionCouncil>()
                where aa.Code == obj.Code
                      && aa.Id != obj.Id
                      && aa.TownId == obj.ParentId
                select aa).Any();

            if (isExist)
                throw new Exception($"UC Code: {obj.Code} already assigned");
        }
        else if (st == StructureType.Ward)
        {
            if (obj.Code == "")
                throw new Exception("Ward Number is required");

            var isExist = (from aa in dc.Structures.OfType<Ward>()
                where aa.EnglishTitle == obj.EnglishTitle
                      && aa.Id != obj.Id
                      && aa.UnionCouncilId == obj.ParentId
                select aa).Any();
            if (isExist)
                throw new Exception($"{obj.EnglishTitle} already exist");

            isExist = (from aa in dc.Structures.OfType<Ward>()
                where aa.UrduTitle == obj.UrduTitle
                      && aa.Id != obj.Id
                      && aa.UnionCouncilId == obj.ParentId
                select aa).Any();

            if (isExist)
                throw new Exception($"{obj.UrduTitle} already exist");

            isExist = (from aa in dc.Structures.OfType<Ward>()
                where aa.Code == obj.Code
                      && aa.Id != obj.Id
                      && aa.UnionCouncilId == obj.ParentId
                select aa).Any();

            if (isExist)
                throw new Exception($"Ward #: {obj.Code} already exist");
        }
    }

    public Task<AdminUnitRptDTO> GetAdminRptInfo(int structureId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var au = new AdminUnitRptDTO();
        var st = dc.Structures.Find(structureId);
        if (st is Province)
            au = (from aa in dc.Structures.OfType<Province>()
                    where aa.Id == structureId
                    select new AdminUnitRptDTO
                    {
                        Id = aa.Id,
                        Type = "Province",

                        Code = aa.Code,
                        DivisionsCount = aa.Divisions.Count,
                        EnglishTitle = aa.EnglishTitle,
                        RegionCount = aa.Regions.Count,
                        UrduTitle = aa.UrduTitle,

                        //Area = aa.Area,
                        //AverageHouseholdIncome = aa.AverageHouseholdIncome,
                        //Castes = aa.Castes,
                        //FemaleVoters = aa.FemaleVoters,
                        //GeneralTrivia = aa.GeneralTrivia,
                        //ImportantAreas = aa.ImportantAreas,
                        //ImportantPoliticalPersonalities = aa.ImportantPoliticalPersonalities,
                        //Languages = aa.Languages,
                        //LiteracyRate = aa.LiteracyRate,
                        //MajorityIncomeSource = aa.MajorityIncomeSource,
                        //MaleVoters = aa.MaleVoters,
                        //Population = aa.Population,
                        //Problems = aa.Problems,
                        //RuralAreaPer = aa.RuralAreaPer,
                        //TotalPollingStations = aa.TotalPollingStations,
                        ////TotalVoters = aa.TotalVoters,
                        //Trivia = aa.Trivia,
                        //UrbanAreaPer = aa.UrbanAreaPer,
                        SubAdminUnits = (from sd in dc.Structures.OfType<District>()
                                orderby sd.EnglishTitle
                                where sd.Division.ProvinceId == structureId
                                select new GeneralItemDTO
                                {
                                    Id = sd.Id, EnglishTitle = sd.EnglishTitle, UrduTitle = sd.UrduTitle,
                                    Province = "District"
                                }
                            ).ToList(),

                        Regions = (from rg in dc.Regions
                                where rg.ProvinceId == structureId
                                select new GeneralItemDTO
                                    { Id = rg.Id, EnglishTitle = rg.EnglishTitle, UrduTitle = rg.UrduTitle })
                            .ToList(),
                        Nominations = GetNominations(structureId, "Province")
                    }
                ).FirstOrDefault();
        else if (st is Division)
            au = (from aa in dc.Structures.OfType<Division>()
                where aa.Id == structureId
                select new AdminUnitRptDTO
                {
                    Id = aa.Id,
                    Type = "Division",
                    ProvinceEnglish = aa.Province.EnglishTitle,
                    ProvinceUrdu = aa.Province.UrduTitle,
                    Code = aa.Code,
                    DistrictCount = aa.Districts.Count,
                    EnglishTitle = aa.EnglishTitle,
                    UrduTitle = aa.UrduTitle,

                    //Area = aa.Area,
                    //AverageHouseholdIncome = aa.AverageHouseholdIncome,
                    //Castes = aa.Castes,
                    //FemaleVoters = aa.FemaleVoters,
                    //GeneralTrivia = aa.GeneralTrivia,
                    //ImportantAreas = aa.ImportantAreas,
                    //ImportantPoliticalPersonalities = aa.ImportantPoliticalPersonalities,
                    //Languages = aa.Languages,
                    //LiteracyRate = aa.LiteracyRate,
                    //MajorityIncomeSource = aa.MajorityIncomeSource,
                    //MaleVoters = aa.MaleVoters,
                    //Population = aa.Population,
                    //Problems = aa.Problems,
                    //RuralAreaPer = aa.RuralAreaPer,
                    //TotalPollingStations = aa.TotalPollingStations,
                    ////TotalVoters = aa.TotalVoters,
                    //Trivia = aa.Trivia,
                    //UrbanAreaPer = aa.UrbanAreaPer,
                    SubAdminUnits = (from sd in dc.Structures.OfType<District>()
                            orderby sd.EnglishTitle
                            where sd.DivisionId == structureId
                            select new GeneralItemDTO
                            {
                                Id = sd.Id, EnglishTitle = sd.EnglishTitle, UrduTitle = sd.UrduTitle,
                                Province = "District"
                            }
                        ).ToList(),
                    Regions = (from rg in dc.Structures.OfType<District>()
                            where rg.DivisionId == structureId &&
                                  rg.RegionId != null
                            select new GeneralItemDTO
                            {
                                Id = rg.RegionId ?? 0, EnglishTitle = rg.Region.EnglishTitle,
                                UrduTitle = rg.Region.UrduTitle
                            })
                        .Distinct().ToList(),
                    RegionCount = 0,
                    Nominations = GetNominations(structureId, "Division")
                }).FirstOrDefault();
        else if (st is District)
            au = (from aa in dc.Structures.OfType<District>()
                where aa.Id == structureId
                select new AdminUnitRptDTO
                {
                    Id = aa.Id,
                    Type = "District",
                    EnglishTitle = aa.EnglishTitle,
                    UrduTitle = aa.UrduTitle,
                    RegionUrdu = aa.RegionId == null ? "NA" : aa.Region.UrduTitle,
                    ProvinceEnglish = aa.Division.Province.EnglishTitle,
                    DivisionEnglish = aa.Division.EnglishTitle,
                    DivisionUrdu = aa.Division.UrduTitle,
                    ProvinceUrdu = aa.Division.Province.UrduTitle,
                    Code = aa.Code,
                    RegionEnglish = aa.RegionId == null ? "NA" : aa.Region.EnglishTitle,
                    RegionCount = 0,
                    TownCount = aa.Towns.Count,

                    //Area = aa.Area,
                    //AverageHouseholdIncome = aa.AverageHouseholdIncome,
                    //Castes = aa.Castes,
                    //FemaleVoters = aa.FemaleVoters,
                    //GeneralTrivia = aa.GeneralTrivia,
                    //ImportantAreas = aa.ImportantAreas,
                    //ImportantPoliticalPersonalities = aa.ImportantPoliticalPersonalities,
                    //Languages = aa.Languages,
                    //LiteracyRate = aa.LiteracyRate,
                    //MajorityIncomeSource = aa.MajorityIncomeSource,
                    //MaleVoters = aa.MaleVoters,
                    //Population = aa.Population,
                    //Problems = aa.Problems,
                    //RuralAreaPer = aa.RuralAreaPer,
                    //TotalPollingStations = aa.TotalPollingStations,
                    //Trivia = aa.Trivia,
                    //UrbanAreaPer = aa.UrbanAreaPer,
                    SubAdminUnits = (from sd in dc.Structures.OfType<Town>()
                            orderby sd.EnglishTitle
                            where sd.DistrictId == structureId
                            select new GeneralItemDTO
                            {
                                Id = sd.Id, EnglishTitle = sd.EnglishTitle, UrduTitle = sd.UrduTitle, Province = "Towns"
                            }
                        ).ToList(),
                    Nominations = GetNominations(structureId, "District")
                }).FirstOrDefault();
        else if (st is Town)
            au = (from aa in dc.Structures.OfType<Town>()
                where aa.Id == structureId
                select new AdminUnitRptDTO
                {
                    Id = aa.Id,
                    Type = "Town",
                    DivisionEnglish = aa.District.Division.EnglishTitle,
                    DivisionUrdu = aa.District.Division.UrduTitle,
                    ProvinceEnglish = aa.District.Division.Province.EnglishTitle,
                    ProvinceUrdu = aa.District.Division.Province.UrduTitle,
                    DistrictUrdu = aa.District.UrduTitle,
                    DistrictEnglish = aa.District.EnglishTitle,
                    Code = aa.Code,
                    UCCount = aa.UnionCouncils.Count,
                    EnglishTitle = aa.EnglishTitle,
                    UrduTitle = aa.UrduTitle,

                    //Area = aa.Area,
                    //AverageHouseholdIncome = aa.AverageHouseholdIncome,
                    //Castes = aa.Castes,
                    //FemaleVoters = aa.FemaleVoters,
                    //GeneralTrivia = aa.GeneralTrivia,
                    //ImportantAreas = aa.ImportantAreas,
                    //ImportantPoliticalPersonalities = aa.ImportantPoliticalPersonalities,
                    //Languages = aa.Languages,
                    //LiteracyRate = aa.LiteracyRate,
                    //MajorityIncomeSource = aa.MajorityIncomeSource,
                    //MaleVoters = aa.MaleVoters,
                    //Population = aa.Population,
                    //Problems = aa.Problems,
                    //RuralAreaPer = aa.RuralAreaPer,
                    //TotalPollingStations = aa.TotalPollingStations,
                    ////TotalVoters = aa.TotalVoters,
                    //Trivia = aa.Trivia,
                    //UrbanAreaPer = aa.UrbanAreaPer,
                    SubAdminUnits = (from sd in dc.Structures.OfType<UnionCouncil>()
                            orderby sd.EnglishTitle
                            where sd.TownId == structureId
                            select new GeneralItemDTO
                            {
                                Id = sd.Id, EnglishTitle = sd.EnglishTitle, UrduTitle = sd.UrduTitle, Province = "UC"
                            }
                        ).ToList(),
                    RegionUrdu = aa.District.RegionId == null ? "NA" : aa.District.Region.UrduTitle,
                    RegionEnglish = aa.District.RegionId == null ? "NA" : aa.District.Region.EnglishTitle,
                    RegionCount = 0,
                    Nominations = GetNominations(structureId, "Town")
                }).FirstOrDefault();
        else if (st is UnionCouncil)
            au = (from aa in dc.Structures.OfType<UnionCouncil>()
                where aa.Id == structureId
                select new AdminUnitRptDTO
                {
                    Id = aa.Id,
                    Type = "Union Council",
                    DivisionEnglish = aa.Town.District.Division.EnglishTitle,
                    DivisionUrdu = aa.Town.District.Division.UrduTitle,
                    ProvinceEnglish = aa.Town.District.Division.Province.EnglishTitle,
                    ProvinceUrdu = aa.Town.District.Division.Province.UrduTitle,
                    DistrictUrdu = aa.Town.District.UrduTitle,
                    DistrictEnglish = aa.Town.District.EnglishTitle,
                    Code = aa.Code,
                    WardCount = aa.Wards.Count,
                    EnglishTitle = aa.EnglishTitle,

                    //Area = aa.Area,
                    //AverageHouseholdIncome = aa.AverageHouseholdIncome,
                    //Castes = aa.Castes,
                    //FemaleVoters = aa.FemaleVoters,
                    //GeneralTrivia = aa.GeneralTrivia,
                    //ImportantAreas = aa.ImportantAreas,
                    //ImportantPoliticalPersonalities = aa.ImportantPoliticalPersonalities,
                    //Languages = aa.Languages,
                    //LiteracyRate = aa.LiteracyRate,
                    //MajorityIncomeSource = aa.MajorityIncomeSource,
                    //MaleVoters = aa.MaleVoters,
                    //Population = aa.Population,
                    //Problems = aa.Problems,
                    //RuralAreaPer = aa.RuralAreaPer,
                    //TotalPollingStations = aa.TotalPollingStations,
                    ////TotalVoters = aa.TotalVoters,
                    //Trivia = aa.Trivia,
                    //UrbanAreaPer = aa.UrbanAreaPer,
                    UrduTitle = aa.UrduTitle,
                    SubAdminUnits = (from sd in dc.Structures.OfType<Ward>()
                            orderby sd.EnglishTitle
                            where sd.UnionCouncilId == structureId
                            select new GeneralItemDTO
                            {
                                Id = sd.Id, EnglishTitle = sd.EnglishTitle, UrduTitle = sd.UrduTitle, Province = "Wards"
                            }
                        ).ToList(),
                    RegionUrdu = aa.Town.District.RegionId == null ? "NA" : aa.Town.District.Region.UrduTitle,
                    RegionEnglish = aa.Town.District.RegionId == null ? "NA" : aa.Town.District.Region.EnglishTitle,
                    RegionCount = 0,
                    Nominations = GetNominations(structureId, "UC")
                }).FirstOrDefault();
        else if (st is Ward)
            au = (from aa in dc.Structures.OfType<Ward>()
                where aa.Id == structureId
                select new AdminUnitRptDTO
                {
                    Id = aa.Id,
                    Type = "Ward",
                    DivisionEnglish = aa.UnionCouncil.Town.District.Division.EnglishTitle,
                    DivisionUrdu = aa.UnionCouncil.Town.District.Division.UrduTitle,
                    ProvinceEnglish = aa.UnionCouncil.Town.District.Division.Province.EnglishTitle,
                    ProvinceUrdu = aa.UnionCouncil.Town.District.Division.Province.UrduTitle,
                    DistrictUrdu = aa.UnionCouncil.Town.District.UrduTitle,
                    DistrictEnglish = aa.UnionCouncil.Town.District.EnglishTitle,
                    TownUrdu = aa.UnionCouncil.Town.UrduTitle,
                    TownEnglish = aa.UnionCouncil.Town.EnglishTitle,
                    UCUrdu = aa.UnionCouncil.UrduTitle,
                    UCEnglish = aa.UnionCouncil.UrduTitle,
                    Code = aa.Code,
                    EnglishTitle = aa.EnglishTitle,
                    UrduTitle = aa.UrduTitle,

                    //Area = aa.Area,
                    //AverageHouseholdIncome = aa.AverageHouseholdIncome,
                    //Castes = aa.Castes,
                    //FemaleVoters = aa.FemaleVoters,
                    //GeneralTrivia = aa.GeneralTrivia,
                    //ImportantAreas = aa.ImportantAreas,
                    //ImportantPoliticalPersonalities = aa.ImportantPoliticalPersonalities,
                    //Languages = aa.Languages,
                    //LiteracyRate = aa.LiteracyRate,
                    //MajorityIncomeSource = aa.MajorityIncomeSource,
                    //MaleVoters = aa.MaleVoters,
                    //Population = aa.Population,
                    //Problems = aa.Problems,
                    //RuralAreaPer = aa.RuralAreaPer,
                    //TotalPollingStations = aa.TotalPollingStations,
                    ////TotalVoters = aa.TotalVoters,
                    //Trivia = aa.Trivia,
                    //UrbanAreaPer = aa.UrbanAreaPer,
                    SubAdminUnits = new List<GeneralItemDTO>(),
                    RegionUrdu = aa.UnionCouncil.Town.District.RegionId == null
                        ? "NA"
                        : aa.UnionCouncil.Town.District.Region.UrduTitle,
                    RegionEnglish = aa.UnionCouncil.Town.District.RegionId == null
                        ? "NA"
                        : aa.UnionCouncil.Town.District.Region.EnglishTitle,
                    RegionCount = 0,
                    Nominations = GetNominations(structureId, "Ward")
                }).FirstOrDefault();
        else if (st is NationalAssemblyHalka)
            au = (from aa in dc.Structures.OfType<NationalAssemblyHalka>()
                where aa.Id == structureId
                select new AdminUnitRptDTO
                {
                    Id = aa.Id,
                    Type = "NA",
                    DivisionEnglish = aa.District.Division.EnglishTitle,
                    DivisionUrdu = aa.District.Division.UrduTitle,
                    ProvinceEnglish = aa.District.Division.Province.EnglishTitle,
                    ProvinceUrdu = aa.District.Division.Province.UrduTitle,
                    DistrictUrdu = aa.District.UrduTitle,
                    DistrictEnglish = aa.District.EnglishTitle,
                    TownUrdu = "",
                    TownEnglish = "",
                    UCUrdu = "",
                    UCEnglish = "",
                    Code = aa.Code,
                    EnglishTitle = aa.EnglishTitle,
                    UrduTitle = aa.UrduTitle,

                    //Area = aa.Area,
                    //AverageHouseholdIncome = aa.AverageHouseholdIncome,
                    //Castes = aa.Castes,
                    //FemaleVoters = aa.FemaleVoters,
                    //GeneralTrivia = aa.GeneralTrivia,
                    //ImportantAreas = aa.ImportantAreas,
                    //ImportantPoliticalPersonalities = aa.ImportantPoliticalPersonalities,
                    //Languages = aa.Languages,
                    //LiteracyRate = aa.LiteracyRate,
                    //MajorityIncomeSource = aa.MajorityIncomeSource,
                    //MaleVoters = aa.MaleVoters,
                    //Population = aa.Population,
                    //Problems = aa.Problems,
                    //RuralAreaPer = aa.RuralAreaPer,
                    //TotalPollingStations = aa.TotalPollingStations,
                    ////TotalVoters = aa.TotalVoters,
                    //Trivia = aa.Trivia,
                    //UrbanAreaPer = aa.UrbanAreaPer,
                    //SubAdminUnits = new List<GeneralItemDTO>(),
                    RegionUrdu = aa.District.RegionId == null ? "NA" : aa.District.Region.UrduTitle,
                    RegionEnglish = aa.District.RegionId == null ? "NA" : aa.District.Region.EnglishTitle,
                    RegionCount = 0,
                    Nominations = GetNominations(structureId, "NA")
                }).FirstOrDefault();
        else if (st is ProvincialAssemblyHalka)
            au = (from aa in dc.Structures.OfType<ProvincialAssemblyHalka>()
                where aa.Id == structureId
                select new AdminUnitRptDTO
                {
                    Id = aa.Id,
                    Type = "PA",
                    DivisionEnglish = aa.District.Division.EnglishTitle,
                    DivisionUrdu = aa.District.Division.UrduTitle,
                    ProvinceEnglish = aa.District.Division.Province.EnglishTitle,
                    ProvinceUrdu = aa.District.Division.Province.UrduTitle,
                    DistrictUrdu = aa.District.UrduTitle,
                    DistrictEnglish = aa.District.EnglishTitle,
                    TownUrdu = "",
                    TownEnglish = "",
                    UCUrdu = "",
                    UCEnglish = "",
                    Code = aa.Code,
                    EnglishTitle = aa.EnglishTitle,

                    //Area = aa.Area,
                    //AverageHouseholdIncome = aa.AverageHouseholdIncome,
                    //Castes = aa.Castes,
                    //FemaleVoters = aa.FemaleVoters,
                    //GeneralTrivia = aa.GeneralTrivia,
                    //ImportantAreas = aa.ImportantAreas,
                    //ImportantPoliticalPersonalities = aa.ImportantPoliticalPersonalities,
                    //Languages = aa.Languages,
                    //LiteracyRate = aa.LiteracyRate,
                    //MajorityIncomeSource = aa.MajorityIncomeSource,
                    //MaleVoters = aa.MaleVoters,
                    //Population = aa.Population,
                    //Problems = aa.Problems,
                    //RuralAreaPer = aa.RuralAreaPer,
                    //TotalPollingStations = aa.TotalPollingStations,
                    ////TotalVoters = aa.TotalVoters,
                    //Trivia = aa.Trivia,
                    //UrbanAreaPer = aa.UrbanAreaPer,
                    UrduTitle = aa.UrduTitle,
                    SubAdminUnits = new List<GeneralItemDTO>(),
                    RegionUrdu = aa.District.RegionId == null ? "NA" : aa.District.Region.UrduTitle,
                    RegionEnglish = aa.District.RegionId == null ? "NA" : aa.District.Region.EnglishTitle,
                    RegionCount = 0,
                    Nominations = GetNominations(structureId, "NA")
                }).FirstOrDefault();

        au.PollingSchemes = (from a in dc.PollingSchemes
            where a.StructureId == structureId
            orderby a.Phase.StartDate
            select new AdminUnitDemographicDto
            {
                Phase = a.Phase.Title, Area = a.Area ?? "",
                AverageHouseholdIncome = a.AverageHouseholdIncome ?? "",
                Castes = a.Castes ?? "",
                FemaleVoters = a.FemaleVoters,
                GeneralTrivia = a.GeneralTrivia ?? "",
                ImportantAreas = a.ImportantAreas ?? "",
                Languages = a.Languages ?? "",
                ImportantPoliticalPersonalities = a.ImportantPoliticalPersonalities ?? "",
                LiteracyRate = a.LiteracyRate ?? "",
                MajorityIncomeSource = a.MajorityIncomeSource ?? "",
                MaleVoters = a.MaleVoters,
                Population = a.Population,
                Problems = a.Problems ?? "",
                RuralAreaPer = a.RuralAreaPer,
                TotalPollingStations = a.TotalPollingStations,
                Type = "",
                Code = "",
                YouthVoters = a.YouthVoters,
                Profile = a.Profile ?? ""
            }).ToList();
        foreach (var ps in au.PollingSchemes) ps.Profile = ps.Profile.Replace("\n", "<br />");
        return Task.FromResult(au);
        //else
        //	throw new Exception("Unknown structure definition");
    }

    //private List<NominationRptDTO> GetNominations2(int structureId, string type)
    //{
    //	if (type == "Province")
    //	{
    //		var uc = (from s in dc.Structures.OfType<UnionCouncil>()
    //					 join n in dc.Nominations on s.Id equals n.StructureId
    //					 where s.Town.District.Division.ProvinceId==structureId )
    //	}
    //}

    private List<NominationRptDTO> GetNominations(int structureId, string type)
    {
        using var dc = _contextFactory.CreateDbContext();
        if (type == "Province")
        {
            var uc = (from s in dc.Structures.OfType<UnionCouncil>()
                join n in dc.Nominations on s.Id equals n.StructureId
                where s.Town.District.Division.ProvinceId == structureId
                select new NominationRptDTO
                {
                    Assembly =
                        $"{n.ElectionAssembly.EnglishTitle}<br><span class='urdu-sm'>{n.ElectionAssembly.UrduTitle}</span>",
                    Election =
                        $"{n.ElectionPhase.Election.EnglishTitle}<br><span class='urdu-sm'>{n.ElectionPhase.Election.UrduTitle}</span>",
                    Phase = n.ElectionPhase.Title,
                    Date = n.ElectionPhase.StartDate,
                    Candidate = $"{n.Candidate.EnglishName}<br><span class='urdu-sm'>{n.Candidate.UrduName}</span>",
                    CandidateId = n.CandidteId,
                    Constituency =
                        $"{n.Structure.EnglishTitle}<br><span class='urdu-sm'>{n.Structure.UrduTitle}</span>",
                    ConstituencyCode = n.Structure.Code,
                    ConstituencyId = n.Structure.Id,
                    SeatType = $"{n.SeatType.EnglishTitle}<br><span class='urdu-sm'>{n.SeatType.UrduTitle}</span>",
                    Symbol = $"{n.Symbol.EnglishTitle}<br><span class='urdu-sm'>{n.Symbol.UrduTitle}</span>",
                    Votes = n.Votes,
                    Weight = n.Weight,
                    Type = "UC",
                    Town = $"{s.Town.EnglishTitle}<br><span class='urdu-sm'>{s.Town.UrduTitle}</span>",
                    District =
                        $"{s.Town.District.EnglishTitle}<br><span class='urdu-sm'>{s.Town.District.UrduTitle}</span>",
                    Division =
                        $"{s.Town.District.Division.EnglishTitle}<br><span class='urdu-sm'>{s.Town.District.Division.UrduTitle}</span>",
                    Region = s.Town.District.RegionId == null
                        ? "NA"
                        : $"{s.Town.District.Region.EnglishTitle}<br><span class='urdu-sm'>{s.Town.District.Region.UrduTitle}</span>",
                    Province =
                        $"{s.Town.District.Division.Province.EnglishTitle}<br><span class='urdu-sm'>{s.Town.District.Division.Province.UrduTitle}</span>",
                    Party = $"{n.Party.EnglishTitle}<br><span class='urdu-sm'>{n.Party.UrduTitle}</span>"
                }).ToList();
            var na = (from s in dc.Structures.OfType<NationalAssemblyHalka>()
                join n in dc.Nominations on s.Id equals n.StructureId
                where s.District.Division.ProvinceId == structureId
                select new NominationRptDTO
                {
                    Assembly =
                        $"{n.ElectionAssembly.EnglishTitle}<br><span class='urdu-sm'>{n.ElectionAssembly.UrduTitle}</span>",
                    Election =
                        $"{n.ElectionPhase.Election.EnglishTitle}<br><span class='urdu-sm'>{n.ElectionPhase.Election.UrduTitle}</span>",
                    Phase = n.ElectionPhase.Title,
                    Date = n.ElectionPhase.StartDate,
                    Candidate = $"{n.Candidate.EnglishName}<br><span class='urdu-sm'>{n.Candidate.UrduName}</span>",
                    CandidateId = n.CandidteId,
                    Constituency =
                        $"{n.Structure.EnglishTitle}<br><span class='urdu-sm'>{n.Structure.UrduTitle}</span>",
                    ConstituencyCode = n.Structure.Code,
                    ConstituencyId = n.Structure.Id,
                    SeatType = $"{n.SeatType.EnglishTitle}<br><span class='urdu-sm'>{n.SeatType.UrduTitle}</span>",
                    Symbol = $"{n.Symbol.EnglishTitle}<br><span class='urdu-sm'>{n.Symbol.UrduTitle}</span>",
                    Votes = n.Votes,
                    Weight = n.Weight,
                    Type = "NA",
                    Town = "",
                    District = $"{s.District.EnglishTitle}<br><span class='urdu-sm'>{s.District.UrduTitle}</span>",
                    Division =
                        $"{s.District.Division.EnglishTitle}<br><span class='urdu-sm'>{s.District.Division.UrduTitle}</span>",
                    Region = s.District.RegionId == null
                        ? "NA"
                        : $"{s.District.Region.EnglishTitle}<br><span class='urdu-sm'>{s.District.Region.UrduTitle}</span>",
                    Province =
                        $"{s.District.Division.Province.EnglishTitle}<br><span class='urdu-sm'>{s.District.Division.Province.UrduTitle}</span>",
                    Party = $"{n.Party.EnglishTitle}<br><span class='urdu-sm'>{n.Party.UrduTitle}</span>"
                }).ToList();

            var pa = (from s in dc.Structures.OfType<ProvincialAssemblyHalka>()
                join n in dc.Nominations on s.Id equals n.StructureId
                where s.District.Division.ProvinceId == structureId
                select new NominationRptDTO
                {
                    Assembly =
                        $"{n.ElectionAssembly.EnglishTitle}<br><span class='urdu-sm'>{n.ElectionAssembly.UrduTitle}</span>",
                    Election =
                        $"{n.ElectionPhase.Election.EnglishTitle}<br><span class='urdu-sm'>{n.ElectionPhase.Election.UrduTitle}</span>",
                    Phase = n.ElectionPhase.Title,
                    Date = n.ElectionPhase.StartDate,
                    Candidate = $"{n.Candidate.EnglishName}<br><span class='urdu-sm'>{n.Candidate.UrduName}</span>",
                    CandidateId = n.CandidteId,
                    Constituency =
                        $"{n.Structure.EnglishTitle}<br><span class='urdu-sm'>{n.Structure.UrduTitle}</span>",
                    ConstituencyCode = n.Structure.Code,
                    ConstituencyId = n.Structure.Id,
                    SeatType = $"{n.SeatType.EnglishTitle}<br><span class='urdu-sm'>{n.SeatType.UrduTitle}</span>",
                    Symbol = $"{n.Symbol.EnglishTitle}<br><span class='urdu-sm'>{n.Symbol.UrduTitle}</span>",
                    Votes = n.Votes,
                    Weight = n.Weight,
                    Type = "PA",
                    Town = "",
                    District = $"{s.District.EnglishTitle}<br><span class='urdu-sm'>{s.District.UrduTitle}</span>",
                    Division =
                        $"{s.District.Division.EnglishTitle}<br><span class='urdu-sm'>{s.District.Division.UrduTitle}</span>",
                    Region = s.District.RegionId == null
                        ? "NA"
                        : $"{s.District.Region.EnglishTitle}<br><span class='urdu-sm'>{s.District.Region.UrduTitle}</span>",
                    Province =
                        $"{s.District.Division.Province.EnglishTitle}<br><span class='urdu-sm'>{s.District.Division.Province.UrduTitle}</span>"
                }).ToList();

            var q = uc.Union(na).Union(pa).ToList();
            return q;
        }

        if (type == "Division")
        {
            var uc = (from s in dc.Structures.OfType<UnionCouncil>()
                join n in dc.Nominations on s.Id equals n.StructureId
                where s.Town.District.DivisionId == structureId
                select new NominationRptDTO
                {
                    Assembly =
                        $"{n.ElectionAssembly.EnglishTitle}<br><span class='urdu-sm'>{n.ElectionAssembly.UrduTitle}</span>",
                    Election =
                        $"{n.ElectionPhase.Election.EnglishTitle}<br><span class='urdu-sm'>{n.ElectionPhase.Election.UrduTitle}</span>",
                    Phase = n.ElectionPhase.Title,
                    Date = n.ElectionPhase.StartDate,
                    Candidate = $"{n.Candidate.EnglishName}<br><span class='urdu-sm'>{n.Candidate.UrduName}</span>",
                    CandidateId = n.CandidteId,
                    Constituency =
                        $"{n.Structure.EnglishTitle}<br><span class='urdu-sm'>{n.Structure.UrduTitle}</span>",
                    ConstituencyCode = n.Structure.Code,
                    ConstituencyId = n.Structure.Id,
                    SeatType = $"{n.SeatType.EnglishTitle}<br><span class='urdu-sm'>{n.SeatType.UrduTitle}</span>",
                    Symbol = $"{n.Symbol.EnglishTitle}<br><span class='urdu-sm'>{n.Symbol.UrduTitle}</span>",
                    Votes = n.Votes,
                    Weight = n.Weight,
                    Type = "UC",
                    Town = $"{s.Town.EnglishTitle}<br><span class='urdu-sm'>{s.Town.UrduTitle}</span>",
                    District =
                        $"{s.Town.District.EnglishTitle}<br><span class='urdu-sm'>{s.Town.District.UrduTitle}</span>",
                    Division =
                        $"{s.Town.District.Division.EnglishTitle}<br><span class='urdu-sm'>{s.Town.District.Division.UrduTitle}</span>",
                    Region = s.Town.District.RegionId == null
                        ? "NA"
                        : $"{s.Town.District.Region.EnglishTitle}<br><span class='urdu-sm'>{s.Town.District.Region.UrduTitle}</span>",
                    Province =
                        $"{s.Town.District.Division.Province.EnglishTitle}<br><span class='urdu-sm'>{s.Town.District.Division.Province.UrduTitle}</span>",
                    Party = $"{n.Party.EnglishTitle}<br><span class='urdu-sm'>{n.Party.UrduTitle}</span>"
                }).ToList();
            var na = (from s in dc.Structures.OfType<NationalAssemblyHalka>()
                join n in dc.Nominations on s.Id equals n.StructureId
                where s.District.DivisionId == structureId
                select new NominationRptDTO
                {
                    Assembly =
                        $"{n.ElectionAssembly.EnglishTitle}<br><span class='urdu-sm'>{n.ElectionAssembly.UrduTitle}</span>",
                    Election =
                        $"{n.ElectionPhase.Election.EnglishTitle}<br><span class='urdu-sm'>{n.ElectionPhase.Election.UrduTitle}</span>",
                    Phase = n.ElectionPhase.Title,
                    Date = n.ElectionPhase.StartDate,
                    Candidate = $"{n.Candidate.EnglishName}<br><span class='urdu-sm'>{n.Candidate.UrduName}</span>",
                    CandidateId = n.CandidteId,
                    Constituency =
                        $"{n.Structure.EnglishTitle}<br><span class='urdu-sm'>{n.Structure.UrduTitle}</span>",
                    ConstituencyCode = n.Structure.Code,
                    ConstituencyId = n.Structure.Id,
                    SeatType = $"{n.SeatType.EnglishTitle}<br><span class='urdu-sm'>{n.SeatType.UrduTitle}</span>",
                    Symbol = $"{n.Symbol.EnglishTitle}<br><span class='urdu-sm'>{n.Symbol.UrduTitle}</span>",
                    Votes = n.Votes,
                    Weight = n.Weight,
                    Type = "NA",
                    Town = "",
                    District = $"{s.District.EnglishTitle}<br><span class='urdu-sm'>{s.District.UrduTitle}</span>",
                    Division =
                        $"{s.District.Division.EnglishTitle}<br><span class='urdu-sm'>{s.District.Division.UrduTitle}</span>",
                    Region = s.District.RegionId == null
                        ? "NA"
                        : $"{s.District.Region.EnglishTitle}<br><span class='urdu-sm'>{s.District.Region.UrduTitle}</span>",
                    Province =
                        $"{s.District.Division.Province.EnglishTitle}<br><span class='urdu-sm'>{s.District.Division.Province.UrduTitle}</span>",
                    Party = $"{n.Party.EnglishTitle}<br><span class='urdu-sm'>{n.Party.UrduTitle}</span>"
                }).ToList();

            var pa = (from s in dc.Structures.OfType<ProvincialAssemblyHalka>()
                join n in dc.Nominations on s.Id equals n.StructureId
                where s.District.DivisionId == structureId
                select new NominationRptDTO
                {
                    Assembly =
                        $"{n.ElectionAssembly.EnglishTitle}<br><span class='urdu-sm'>{n.ElectionAssembly.UrduTitle}</span>",
                    Election =
                        $"{n.ElectionPhase.Election.EnglishTitle}<br><span class='urdu-sm'>{n.ElectionPhase.Election.UrduTitle}</span>",
                    Phase = n.ElectionPhase.Title,
                    Date = n.ElectionPhase.StartDate,
                    Candidate = $"{n.Candidate.EnglishName}<br><span class='urdu-sm'>{n.Candidate.UrduName}</span>",
                    CandidateId = n.CandidteId,
                    Constituency =
                        $"{n.Structure.EnglishTitle}<br><span class='urdu-sm'>{n.Structure.UrduTitle}</span>",
                    ConstituencyCode = n.Structure.Code,
                    ConstituencyId = n.Structure.Id,
                    SeatType = $"{n.SeatType.EnglishTitle}<br><span class='urdu-sm'>{n.SeatType.UrduTitle}</span>",
                    Symbol = $"{n.Symbol.EnglishTitle}<br><span class='urdu-sm'>{n.Symbol.UrduTitle}</span>",
                    Votes = n.Votes,
                    Weight = n.Weight,
                    Type = "PA",
                    Town = "",
                    District = $"{s.District.EnglishTitle}<br><span class='urdu-sm'>{s.District.UrduTitle}</span>",
                    Division =
                        $"{s.District.Division.EnglishTitle}<br><span class='urdu-sm'>{s.District.Division.UrduTitle}</span>",
                    Region = s.District.RegionId == null
                        ? "NA"
                        : $"{s.District.Region.EnglishTitle}<br><span class='urdu-sm'>{s.District.Region.UrduTitle}</span>",
                    Province =
                        $"{s.District.Division.Province.EnglishTitle}<br><span class='urdu-sm'>{s.District.Division.Province.UrduTitle}</span>",
                    Party = $"{n.Party.EnglishTitle}<br><span class='urdu-sm'>{n.Party.UrduTitle}</span>"
                }).ToList();

            var q = uc.Union(na).Union(pa).ToList();
            return q;
        }

        if (type == "District")
        {
            var uc = (from s in dc.Structures.OfType<UnionCouncil>()
                join n in dc.Nominations on s.Id equals n.StructureId
                where s.Town.DistrictId == structureId
                select new NominationRptDTO
                {
                    Assembly =
                        $"{n.ElectionAssembly.EnglishTitle}<br><span class='urdu-sm'>{n.ElectionAssembly.UrduTitle}</span>",
                    Election =
                        $"{n.ElectionPhase.Election.EnglishTitle}<br><span class='urdu-sm'>{n.ElectionPhase.Election.UrduTitle}</span>",
                    Phase = n.ElectionPhase.Title,
                    Date = n.ElectionPhase.StartDate,
                    Candidate = $"{n.Candidate.EnglishName}<br><span class='urdu-sm'>{n.Candidate.UrduName}</span>",
                    CandidateId = n.CandidteId,
                    Constituency =
                        $"{n.Structure.EnglishTitle}<br><span class='urdu-sm'>{n.Structure.UrduTitle}</span>",
                    ConstituencyCode = n.Structure.Code,
                    ConstituencyId = n.Structure.Id,
                    SeatType = $"{n.SeatType.EnglishTitle}<br><span class='urdu-sm'>{n.SeatType.UrduTitle}</span>",
                    Symbol = $"{n.Symbol.EnglishTitle}<br><span class='urdu-sm'>{n.Symbol.UrduTitle}</span>",
                    Votes = n.Votes,
                    Weight = n.Weight,
                    Type = "UC",
                    Town = $"{s.Town.EnglishTitle}<br><span class='urdu-sm'>{s.Town.UrduTitle}</span>",
                    District =
                        $"{s.Town.District.EnglishTitle}<br><span class='urdu-sm'>{s.Town.District.UrduTitle}</span>",
                    Division =
                        $"{s.Town.District.Division.EnglishTitle}<br><span class='urdu-sm'>{s.Town.District.Division.UrduTitle}</span>",
                    Region = s.Town.District.RegionId == null
                        ? "NA"
                        : $"{s.Town.District.Region.EnglishTitle}<br><span class='urdu-sm'>{s.Town.District.Region.UrduTitle}</span>",
                    Province =
                        $"{s.Town.District.Division.Province.EnglishTitle}<br><span class='urdu-sm'>{s.Town.District.Division.Province.UrduTitle}</span>",
                    Party = $"{n.Party.EnglishTitle}<br><span class='urdu-sm'>{n.Party.UrduTitle}</span>"
                }).ToList();
            var na = (from s in dc.Structures.OfType<NationalAssemblyHalka>()
                join n in dc.Nominations on s.Id equals n.StructureId
                where s.DistrictId == structureId
                select new NominationRptDTO
                {
                    Assembly =
                        $"{n.ElectionAssembly.EnglishTitle}<br><span class='urdu-sm'>{n.ElectionAssembly.UrduTitle}</span>",
                    Election =
                        $"{n.ElectionPhase.Election.EnglishTitle}<br><span class='urdu-sm'>{n.ElectionPhase.Election.UrduTitle}</span>",
                    Phase = n.ElectionPhase.Title,
                    Date = n.ElectionPhase.StartDate,
                    Candidate = $"{n.Candidate.EnglishName}<br><span class='urdu-sm'>{n.Candidate.UrduName}</span>",
                    CandidateId = n.CandidteId,
                    Constituency =
                        $"{n.Structure.EnglishTitle}<br><span class='urdu-sm'>{n.Structure.UrduTitle}</span>",
                    ConstituencyCode = n.Structure.Code,
                    ConstituencyId = n.Structure.Id,
                    SeatType = $"{n.SeatType.EnglishTitle}<br><span class='urdu-sm'>{n.SeatType.UrduTitle}</span>",
                    Symbol = $"{n.Symbol.EnglishTitle}<br><span class='urdu-sm'>{n.Symbol.UrduTitle}</span>",
                    Votes = n.Votes,
                    Weight = n.Weight,
                    Type = "NA",
                    Town = "",
                    District = $"{s.District.EnglishTitle}<br><span class='urdu-sm'>{s.District.UrduTitle}</span>",
                    Division =
                        $"{s.District.Division.EnglishTitle}<br><span class='urdu-sm'>{s.District.Division.UrduTitle}</span>",
                    Region = s.District.RegionId == null
                        ? "NA"
                        : $"{s.District.Region.EnglishTitle}<br><span class='urdu-sm'>{s.District.Region.UrduTitle}</span>",
                    Province =
                        $"{s.District.Division.Province.EnglishTitle}<br><span class='urdu-sm'>{s.District.Division.Province.UrduTitle}</span>",
                    Party = $"{n.Party.EnglishTitle}<br><span class='urdu-sm'>{n.Party.UrduTitle}</span>"
                }).ToList();

            var pa = (from s in dc.Structures.OfType<ProvincialAssemblyHalka>()
                join n in dc.Nominations on s.Id equals n.StructureId
                where s.DistrictId == structureId
                select new NominationRptDTO
                {
                    Assembly =
                        $"{n.ElectionAssembly.EnglishTitle}<br><span class='urdu-sm'>{n.ElectionAssembly.UrduTitle}</span>",
                    Election =
                        $"{n.ElectionPhase.Election.EnglishTitle}<br><span class='urdu-sm'>{n.ElectionPhase.Election.UrduTitle}</span>",
                    Phase = n.ElectionPhase.Title,
                    Date = n.ElectionPhase.StartDate,
                    Candidate = $"{n.Candidate.EnglishName}<br><span class='urdu-sm'>{n.Candidate.UrduName}</span>",
                    CandidateId = n.CandidteId,
                    Constituency =
                        $"{n.Structure.EnglishTitle}<br><span class='urdu-sm'>{n.Structure.UrduTitle}</span>",
                    ConstituencyCode = n.Structure.Code,
                    ConstituencyId = n.Structure.Id,
                    SeatType = $"{n.SeatType.EnglishTitle}<br><span class='urdu-sm'>{n.SeatType.UrduTitle}</span>",
                    Symbol = $"{n.Symbol.EnglishTitle}<br><span class='urdu-sm'>{n.Symbol.UrduTitle}</span>",
                    Votes = n.Votes,
                    Weight = n.Weight,
                    Type = "PA",
                    Town = "",
                    District = $"{s.District.EnglishTitle}<br><span class='urdu-sm'>{s.District.UrduTitle}</span>",
                    Division =
                        $"{s.District.Division.EnglishTitle}<br><span class='urdu-sm'>{s.District.Division.UrduTitle}</span>",
                    Region = s.District.RegionId == null
                        ? "NA"
                        : $"{s.District.Region.EnglishTitle}<br><span class='urdu-sm'>{s.District.Region.UrduTitle}</span>",
                    Province =
                        $"{s.District.Division.Province.EnglishTitle}<br><span class='urdu-sm'>{s.District.Division.Province.UrduTitle}</span>"
                }).ToList();

            var q = uc.Union(na).Union(pa).ToList();
            return q;
        }

        if (type == "Town")
        {
            var uc = (from s in dc.Structures.OfType<UnionCouncil>()
                join n in dc.Nominations on s.Id equals n.StructureId
                where s.TownId == structureId
                select new NominationRptDTO
                {
                    Assembly =
                        $"{n.ElectionAssembly.EnglishTitle}<br><span class='urdu-sm'>{n.ElectionAssembly.UrduTitle}</span>",
                    Election =
                        $"{n.ElectionPhase.Election.EnglishTitle}<br><span class='urdu-sm'>{n.ElectionPhase.Election.UrduTitle}</span>",
                    Phase = n.ElectionPhase.Title,
                    Date = n.ElectionPhase.StartDate,
                    Candidate = $"{n.Candidate.EnglishName}<br><span class='urdu-sm'>{n.Candidate.UrduName}</span>",
                    CandidateId = n.CandidteId,
                    Constituency =
                        $"{n.Structure.EnglishTitle}<br><span class='urdu-sm'>{n.Structure.UrduTitle}</span>",
                    ConstituencyCode = n.Structure.Code,
                    ConstituencyId = n.Structure.Id,
                    SeatType = $"{n.SeatType.EnglishTitle}<br><span class='urdu-sm'>{n.SeatType.UrduTitle}</span>",
                    Symbol = $"{n.Symbol.EnglishTitle}<br><span class='urdu-sm'>{n.Symbol.UrduTitle}</span>",
                    Votes = n.Votes,
                    Weight = n.Weight,

                    Town = $"{s.Town.EnglishTitle}<br><span class='urdu-sm'>{s.Town.UrduTitle}</span>",
                    District =
                        $"{s.Town.District.EnglishTitle}<br><span class='urdu-sm'>{s.Town.District.UrduTitle}</span>",
                    Division =
                        $"{s.Town.District.Division.EnglishTitle}<br><span class='urdu-sm'>{s.Town.District.Division.UrduTitle}</span>",
                    Region = s.Town.District.RegionId == null
                        ? "NA"
                        : $"{s.Town.District.Region.EnglishTitle}<br><span class='urdu-sm'>{s.Town.District.Region.UrduTitle}</span>",
                    Province =
                        $"{s.Town.District.Division.Province.EnglishTitle}<br><span class='urdu-sm'>{s.Town.District.Division.Province.UrduTitle}</span>",
                    Party = $"{n.Party.EnglishTitle}<br><span class='urdu-sm'>{n.Party.UrduTitle}</span>"
                }).ToList();

            return uc;
        }

        if (type == "UC")
        {
            var wrd = (from s in dc.Structures.OfType<UnionCouncil>()
                join n in dc.Nominations on s.Id equals n.StructureId
                where s.Id == structureId
                select new NominationRptDTO
                {
                    Assembly =
                        $"{n.ElectionAssembly.EnglishTitle}<br><span class='urdu-sm'>{n.ElectionAssembly.UrduTitle}</span>",
                    Election =
                        $"{n.ElectionPhase.Election.EnglishTitle}<br><span class='urdu-sm'>{n.ElectionPhase.Election.UrduTitle}</span>",
                    Phase = n.ElectionPhase.Title,
                    Date = n.ElectionPhase.StartDate,
                    Candidate = $"{n.Candidate.EnglishName}<br><span class='urdu-sm'>{n.Candidate.UrduName}</span>",
                    CandidateId = n.CandidteId,
                    Constituency =
                        $"{n.Structure.EnglishTitle}<br><span class='urdu-sm'>{n.Structure.UrduTitle}</span>",
                    ConstituencyCode = n.Structure.Code,
                    ConstituencyId = n.Structure.Id,
                    SeatType = $"{n.SeatType.EnglishTitle}<br><span class='urdu-sm'>{n.SeatType.UrduTitle}</span>",
                    Symbol = $"{n.Symbol.EnglishTitle}<br><span class='urdu-sm'>{n.Symbol.UrduTitle}</span>",
                    Votes = n.Votes,
                    Weight = n.Weight,

                    Town = $"{s.Town.EnglishTitle}<br><span class='urdu-sm'>{s.Town.UrduTitle}</span>",
                    District =
                        $"{s.Town.District.EnglishTitle}<br><span class='urdu-sm'>{s.Town.District.UrduTitle}</span>",
                    Division =
                        $"{s.Town.District.Division.EnglishTitle}<br><span class='urdu-sm'>{s.Town.District.Division.UrduTitle}</span>",
                    Region = s.Town.District.RegionId == null
                        ? "NA"
                        : $"{s.Town.District.Region.EnglishTitle}<br><span class='urdu-sm'>{s.Town.District.Region.UrduTitle}</span>",
                    Province =
                        $"{s.Town.District.Division.Province.EnglishTitle}<br><span class='urdu-sm'>{s.Town.District.Division.Province.UrduTitle}</span>",
                    Party = $"{n.Party.EnglishTitle}<br><span class='urdu-sm'>{n.Party.UrduTitle}</span>"
                }).ToList();
            return wrd;
        }

        if (type == "Ward")
        {
            var wrd = (from s in dc.Structures.OfType<Ward>()
                join n in dc.Nominations on s.Id equals n.StructureId
                where s.Id == structureId
                select new NominationRptDTO
                {
                    Assembly =
                        $"{n.ElectionAssembly.EnglishTitle}<br><span class='urdu-sm'>{n.ElectionAssembly.UrduTitle}</span>",
                    Election =
                        $"{n.ElectionPhase.Election.EnglishTitle}<br><span class='urdu-sm'>{n.ElectionPhase.Election.UrduTitle}</span>",
                    Phase = n.ElectionPhase.Title,
                    Date = n.ElectionPhase.StartDate,
                    Candidate = $"{n.Candidate.EnglishName}<br><span class='urdu-sm'>{n.Candidate.UrduName}</span>",
                    CandidateId = n.CandidteId,
                    Constituency =
                        $"{n.Structure.EnglishTitle}<br><span class='urdu-sm'>{n.Structure.UrduTitle}</span>",
                    ConstituencyCode = n.Structure.Code,
                    ConstituencyId = n.Structure.Id,
                    SeatType = $"{n.SeatType.EnglishTitle}<br><span class='urdu-sm'>{n.SeatType.UrduTitle}</span>",
                    Symbol = $"{n.Symbol.EnglishTitle}<br><span class='urdu-sm'>{n.Symbol.UrduTitle}</span>",
                    Votes = n.Votes,
                    Weight = n.Weight,
                    Type = "Ward",
                    Town =
                        $"{s.UnionCouncil.Town.EnglishTitle}<br><span class='urdu-sm'>{s.UnionCouncil.Town.UrduTitle}</span>",
                    District =
                        $"{s.UnionCouncil.Town.District.EnglishTitle}<br><span class='urdu-sm'>{s.UnionCouncil.Town.District.UrduTitle}</span>",
                    Division =
                        $"{s.UnionCouncil.Town.District.Division.EnglishTitle}<br><span class='urdu-sm'>{s.UnionCouncil.Town.District.Division.UrduTitle}</span>",
                    Region = s.UnionCouncil.Town.District.RegionId == null
                        ? "NA"
                        : $"{s.UnionCouncil.Town.District.Region.EnglishTitle}<br><span class='urdu-sm'>{s.UnionCouncil.Town.District.Region.UrduTitle}</span>",
                    Province =
                        $"{s.UnionCouncil.Town.District.Division.Province.EnglishTitle}<br><span class='urdu-sm'>{s.UnionCouncil.Town.District.Division.Province.UrduTitle}</span>",
                    Party = $"{n.Party.EnglishTitle}<br><span class='urdu-sm'>{n.Party.UrduTitle}</span>"
                }).ToList();
            return wrd;
        }

        if (type == "PA")
            return null;
        if (type == "NA")
            return null;
        return null;
    }

    public Task<ElcStructureDTO> GetAdminUnitDetail(int Id, int? ParentId, StructureType objStructureType, int phaseId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from st in dc.Structures
            where st.Id == Id
            select new ElcStructureDTO
            {
                Id = st.Id,
                EnglishTitle = st.EnglishTitle,
                UrduTitle = st.UrduTitle,
                ProblemsList = dc.StructureProblems.
                    Where(c=>c.StructureId==Id).
                    Select(c=>c.ProblemId)
                    .ToList(),
                //MaleVoters = st.MaleVoters,
                //FemaleVoters = st.FemaleVoters,
                //Population = st.Population,
                //TotalPollingStations = st.TotalPollingStations,

                //Trivia = st.Trivia,
                //GeneralTrivia = st.GeneralTrivia,
                //Languages = st.Languages,
                //Castes = st.Castes,
                //ImportantPoliticalPersonalities = st.ImportantPoliticalPersonalities,
                //Problems = st.Problems,
                //ImportantAreas = st.ImportantAreas,

                //UrbanAreaPer = st.UrbanAreaPer,
                //RuralAreaPer = st.RuralAreaPer,
                //Area = st.Area,
                ////CasteId = st.CasteId,
                //MajorityIncomeSource = st.MajorityIncomeSource,
                //LiteracyRate = st.LiteracyRate,
                //AverageHouseholdIncome = st.AverageHouseholdIncome,

                Code = st.Code,

                //LanguageId = st.LanguageId,

                ParentId = objStructureType != StructureType.District
                    ? ParentId
                    : dc.Structures.OfType<Division>().FirstOrDefault(k => k.ProvinceId == ParentId).Id,
                StructureType = objStructureType,
                PrevConstituencies = (from bb in dc.StructureMappings
                    where bb.StructureId == Id
                    select new PrevConstituencyDTO
                    {
                        ParentStructureId = bb.PreviousStructureId,
                        Description = bb.Description,
                        EnglishTitle = bb.PreviousStructure.Code + " " + bb.PreviousStructure.EnglishTitle,
                        UrduTitle = bb.PreviousStructure.Code + " " + bb.PreviousStructure.UrduTitle,
                        Election = bb.PreviousStructure.Election.EnglishTitle
                    }).ToList(),
                AssemblyId = st.AssemblyId,
                Assembly = st.Assembly.EnglishTitle
            }).FirstOrDefault();
        if (objStructureType == StructureType.District)
        {
            var dis = (from bb in dc.Structures.OfType<District>()
                where bb.Id == Id
                select new { bb.RegionId, bb.Division.ProvinceId, bb.AdminDivisionId }).FirstOrDefault();
            q.RegionId = dis.RegionId;
            q.ProvinceId = dis.ProvinceId;
            q.DivisionId = dis.AdminDivisionId;
        }

        var psDetail = (from a in dc.PollingSchemes
            where a.StructureId == Id && a.PhaseId == phaseId
            select a).FirstOrDefault();
        if (psDetail != null)
        {
            q.MaleVoters = psDetail.MaleVoters;
            q.FemaleVoters = psDetail.FemaleVoters;
            q.YouthVoters = psDetail.YouthVoters;
            q.TotalPollingStations = psDetail.TotalPollingStations;
            q.Languages = psDetail.Languages;
            q.Castes = psDetail.Castes;
            q.Trivia = psDetail.Trivia;
            q.GeneralTrivia = psDetail.GeneralTrivia;
            q.ImportantPoliticalPersonalities = psDetail.ImportantPoliticalPersonalities;
            q.Problems = psDetail.Problems;
            q.ImportantAreas = psDetail.ImportantAreas;
            q.RuralAreaPer = psDetail.RuralAreaPer;
            q.UrbanAreaPer = psDetail.UrbanAreaPer;
            q.Population = psDetail.Population;
            q.MajorityIncomeSource = psDetail.MajorityIncomeSource;
            q.LiteracyRate = psDetail.LiteracyRate;
            q.AverageHouseholdIncome = psDetail.AverageHouseholdIncome;
            q.Area = psDetail.Area;
            q.Profile = psDetail.Profile;
        }

        return Task.FromResult(q);
    }

    public Task<List<ProblemDto>> GetAllProblems()
    {
        var dc = _contextFactory.CreateDbContext();
        var q = (from a in dc.Problems
            orderby a.Title
            select new ProblemDto()
            {
                Id = a.Id, Title = a.Title
            }).ToList();
        return Task.FromResult(q);
    }
}