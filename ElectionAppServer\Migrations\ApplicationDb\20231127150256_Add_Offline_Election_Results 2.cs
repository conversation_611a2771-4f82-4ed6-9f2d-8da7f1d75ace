﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace ElectionAppServer.Migrations.ApplicationDb
{
    /// <inheritdoc />
    public partial class Add_Offline_Election_Results2 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropPrimaryKey(
                name: "PK_OfflineResult",
                table: "OfflineResult");

            migrationBuilder.AddPrimaryKey(
                name: "PK_OfflineResult",
                table: "OfflineResult",
                columns: new[] { "NominationId", "ResultSourceId" });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropPrimaryKey(
                name: "PK_OfflineResult",
                table: "OfflineResult");

            migrationBuilder.AddPrimaryKey(
                name: "PK_OfflineResult",
                table: "OfflineResult",
                column: "NominationId");
        }
    }
}
