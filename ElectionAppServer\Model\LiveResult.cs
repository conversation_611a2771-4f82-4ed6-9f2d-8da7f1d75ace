﻿using System;

namespace ElectionAppServer.Model;

public class LiveResult
{
    public int ElectionId { get; set; }
    public int PhaseId { get; set; }
    public int AssemblyId { get; set; }
    public int ConstituencyId { get; set; }
    public int SeatTypeId { get; set; }

    public string Election { get; set; }
    public string Phase { get; set; }
    public string Assembly { get; set; }
    public string Constituency { get; set; }
    public string SeatType { get; set; }

    public int TotalPollingStations { get; set; }
    public int TotalVoters { get; set; }
    public int PollingStationResult { get; set; }

    public string Winner { get; set; }
    public string WinnerParty { get; set; }
    public int WinnerVotes { get; set; }
    public int? WinnerCandidateId { get; set; }
    public string RunnerUp { get; set; }
    public string RunnerUpParty { get; set; }
    public int? RunnerUpVotes { get; set; }
    public int? RunnerUpCandidateId { get; set; }
    public bool IsPublished { get; set; }

    public string PublishedByUser { get; set; }
    public DateTime? PublishedDate { get; set; }
    public string District { get; set; }
    public bool IsTickerPublish { get; set; } = false;

    #region Audit Log

    public DateTime CreatedDate { get; set; }
    public string CreatedBy { get; set; }
    public DateTime ModifiedDate { get; set; }
    public string ModifiedBy { get; set; }

    #endregion Audit Log
}