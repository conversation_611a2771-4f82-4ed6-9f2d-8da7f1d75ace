﻿@page "/divisions/{provinceId:int}"
@page "/divisions/{provinceId:int}/{cid:int}"
@inherits OwningComponentBase<ElectionStructureService>
@using Syncfusion.Blazor.Popups
@using Syncfusion.Blazor.Grids
@using Syncfusion.Blazor.Notifications
@using Syncfusion.Blazor.Buttons
@attribute [Authorize]

<PageTitle Title="Divisions"></PageTitle>
@*<a href="/elections">@ElectionTitle</a> <span>></span>*@
@foreach (var bc in BredCrum)
{
    <a href="@bc.UrduTitle">@bc.EnglishTitle</a> <span>></span>
} Divisions

<SfDialog Width="900px" @bind-Visible="@isDlgVisible" @ref="dlgForm" IsModal="true" ShowCloseIcon="true">
    <DialogEvents OnOpen="beforeOpen"></DialogEvents>
    <DialogTemplates>
        <Header>Division Detail</Header>
        <Content>
            <AdminUnitForm selectedObj="@selectedObj" OnValidDataSubmit="SaveData"></AdminUnitForm>
        </Content>
    </DialogTemplates>
</SfDialog>

<SfToast @ref="ToastObj" TimeOut=5000>
    <ToastPosition X="Right"></ToastPosition>
</SfToast>

<section class="content" style="padding: 15px 1rem;">

    @if (objList == null)
    {
        <p><em>Loading...</em></p>
    }
    else
    {

        <div class="row"><SfButton IsPrimary="true" CssClass="e-primary mb-2" OnClick="OpenCreateForm">Create Division</SfButton></div>
        <div class="row"><SearchStructure ElectionId="@electionId"></SearchStructure></div>
        <div class="row">
            <SfGrid DataSource="@objList" AllowFiltering="true" ModelType="@selectedObj" AllowSorting="true" AllowPaging="true" AllowTextWrap="true" @ref="Grid">
                <GridEvents QueryCellInfo="CustomizeCell" TValue="ElcStructureDTO"></GridEvents>
                <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Excel"></GridFilterSettings>
                <GridColumns>
                    <GridColumn Field="Id" HeaderText="Id" AutoFit="true"></GridColumn>
                    <GridColumn Field="UrduTitle" HeaderText="Urdu Title" TextAlign="TextAlign.Right"></GridColumn>
                    <GridColumn Field="EnglishTitle" HeaderText="English Title"></GridColumn>
                    <GridColumn Field="SubUnits" HeaderText="Total Districts"></GridColumn>
                    <GridColumn HeaderText="Audit Info">
                        <Template Context="kk">
                            @{
                                var oo = kk as ElcStructureDTO;
                                <div style="font-size:11px">
                                    <span>Created By: @oo.CreateBy</span><span> On @oo.CreatedDate</span>
                                    @if (!string.IsNullOrEmpty(oo.ModifiedBy))
                                    {
                                        <br /><span>Modified By: @oo.ModifiedBy</span><span> On @oo.ModifiedDate</span>
                                    }
                                </div>
                            }
                        </Template>
                    </GridColumn>
                    <GridColumn Width="230px" HeaderText="Actions" AllowFiltering="false">
                        <Template Context="ss">
                            @{
                                var kk = (ss as ElcStructureDTO);
                                <a target="@($"au{kk.Id}")" href="report/adminunit/@kk.Id"><span class="e-control e-btn e-lib e-success"><span class="oi oi-clipboard" aria-hidden="true"></span></span></a>
                                <SfButton IsPrimary="true" CssClass="e-danger" OnClick="@(()=>DeleteRecord(kk.Id))"><span class="oi oi-trash" aria-hidden="true"></span></SfButton>
                                <SfButton IsPrimary="true" CssClass="e-primary" OnClick="@(()=>OpenEditForm(kk))"><span class="oi oi-pencil" aria-hidden="true"></span></SfButton>

                                <a style="padding-left:3px;" href="/districts/@kk.Id">Districts</a>
                            }
                        </Template>
                    </GridColumn>
                </GridColumns>
            </SfGrid>
        </div>

    }
</section>
@code {
    // AuthenticationState is available as a CascadingParameter
    [CascadingParameter] private Task<AuthenticationState> authenticationStateTask { get; set; }
    [Parameter] public int provinceId { get; set; }
    private SfDialog dlgForm;
    List<ElcStructureDTO> objList;
    private bool isDlgVisible = false;
    private string SaveButtonText = "Save";
    private string FormTitle = "Create Division";
    private ElcStructureDTO selectedObj;
    private SfToast ToastObj;
    public string ToastContent = "Unable to delete Record, Related Record(s) Exist";
    public List<GeneralItemDTO> BredCrum { get; set; }
    public int electionId { get; set; }
    public List<GeneralItemDTO> languagesList { get; set; }
    public List<GeneralItemDTO> casteList { get; set; }
    public string ElectionTitle { get; set; }
    [Parameter] public int? cid { get; set; }
    public SfGrid<ElcStructureDTO> Grid { get; set; }

    protected override async Task OnInitializedAsync()
    {
        var user = (await authenticationStateTask).User;
        objList = await Service.GetList(StructureType.Division, provinceId, cid);
        BredCrum = await Service.GetBreadCrum(StructureType.Division, provinceId);
        languagesList = await Service.GetLanguages();
        casteList = await Service.GetCastesList();
        electionId = BredCrum[0].Id;
        ElectionTitle = await Service.GetElectionTitle(electionId);
        ClearData();

    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        //return base.OnAfterRenderAsync(firstRender);
        if (firstRender)
        {
            var user = (await authenticationStateTask).User;
            if (user.Identity.IsAuthenticated)
            {
                //var dd =  await ProtectedSessionStore.GetAsync<ElectionStateDTO>("election");
                var dd = await localStorage.GetItemAsync<ElectionStateDTO>("election");
                state.SetState(dd);
            }
            else
            {
                try
                {
                    //await  ProtectedSessionStore.DeleteAsync("election");
                    await localStorage.RemoveItemAsync("election");
                    state.SetState(new ElectionStateDTO { ElectionId = 0, ElectionTitle = "", PhaseId = 0, PhaseTitle = "" });

                }
                catch (Exception)
                {

                }
                NavigationManager.NavigateTo("");
            }
            StateHasChanged();
        }
    }

    private void OpenCreateForm()
    {
        ClearData();
        await dlgForm.ShowAsync();
        SaveButtonText = "Create";
    }

    private async Task OpenEditForm(ElcStructureDTO st)
    {
        selectedObj = await Service.GetAdminUnitDetail(st.Id, provinceId, StructureType.Division);
        await dlgForm.Show();
        SaveButtonText = "Update";
    }

    //private void OpenEditForm2(ElcStructureDTO st)
    //{
    //   selectedObj = new ElcStructureDTO
    //   {
    //      Id = st.Id,
    //      //CasteId = st.CasteId,
    //      Castes = st.Castes,
    //      Languages = st.Languages,
    //      Code = st.Code,
    //      EnglishTitle = st.EnglishTitle,
    //      FemaleVoters = st.FemaleVoters,
    //      TotalPollingStations = st.TotalPollingStations,
    //      ImportantAreas = st.ImportantAreas,
    //      ImportantPoliticalPersonalities = st.ImportantPoliticalPersonalities,
    //      //LanguageId = st.LanguageId,
    //      MaleVoters = st.MaleVoters,
    //      ParentId = provinceId,
    //      Problems = st.Problems,
    //      RuralAreaPer = st.RuralAreaPer,
    //      Trivia = st.Trivia,
    //      UrbanAreaPer = st.UrbanAreaPer,
    //      UrduTitle = st.UrduTitle,
    //      StructureType = StructureType.Division,
    //      MajorityIncomeSource = st.MajorityIncomeSource,
    //      LiteracyRate = st.LiteracyRate,
    //      AverageHouseholdIncome = st.AverageHouseholdIncome,
    //      Population = st.Population,
    //      GeneralTrivia = st.GeneralTrivia,
    //      Area = st.Area
    //   };
    //   dlgForm.Show();
    //   SaveButtonText = "Update";
    //}

    private void ClearData()
    {
        selectedObj = new ElcStructureDTO { ParentId = provinceId, StructureType = StructureType.Division, ElectionId = electionId };
    }

    private async Task SaveData()
    {
        var user = (await authenticationStateTask).User;

        try
        {
            await Service.Save(selectedObj, user.Identity.Name);
            objList = await Service.GetList(StructureType.Division, provinceId, cid);
            Grid.Refresh();
            ClearData();
            await dlgForm.HideAsync();
        }
        catch (Exception ex)
        {
            ToastModel mm = new ToastModel { Title = "Error", Content = ex.Message };
            await this.ToastObj.Show(mm);
        }
    }

    private async Task DeleteRecord(int Id)
    {
        var res = await JSRuntime.InvokeAsync<bool>("confirm", "Are you want to delete this record?");
        if (res == false)
            return;

        try
        {
            var msg2 = await Service.Delete(Id);
            objList = await Service.GetList(StructureType.Division, provinceId, cid);
            Grid.Refresh();
            ClearData();
        }
        catch (Exception ex)
        {
            ToastModel mm = new ToastModel { Title = "Error", Content = ex.Message };
            await this.ToastObj.Show(mm);
        }

    }

    private async Task TranslateToUrdu()
    {
        try
        {
            selectedObj.UrduTitle = await Translation.TranslateText(selectedObj.EnglishTitle);
        }
        catch (Exception)
        {

        }

        //if (string.IsNullOrEmpty(selectedObj.UrduTitle))

    }

    public void CustomizeCell(QueryCellInfoEventArgs<ElcStructureDTO> args)
    {
        if (args.Column.Field == "UrduTitle")
        {
            args.Cell.AddClass(new string[] { "urdu-column" });
            args.Cell.AddStyle(new string[] { "font-size:18px;line-height: 1.4;text-align: right" });

        }
    }

    private void beforeOpen(Syncfusion.Blazor.Popups.BeforeOpenEventArgs args)
    {
        args.MaxHeight = null;
    }
}