﻿@page "/resultsps"
@inject NavigationManager NavigationManager
@inject UserManager<ApplicationUser> _UserManager
@inject RoleManager<IdentityRole> _RoleManager
@inject AuthenticationStateProvider AuthenticationStateProvider
@using FilterType = Syncfusion.Blazor.DropDowns.FilterType
@using Size = MudBlazor.Size
@inherits OwningComponentBase<ElectionResultsDataService>
@*<a href="/myconstituencies">Back to Constituency Selection</a>*@
<SfToast @ref="ToastObj" Title="Alert" ShowProgressBar="true" Timeout="3000">
    <ToastPosition X="Right" Y="Bottom"></ToastPosition>
</SfToast>
<div class="pt-1">
    <div class="card border border-primary bg-primary mb-2 text-white">
        @* <div class="card-header p-2 pl-3">Result Posting @(IsConnected ? "" : " - (Not Connected)") - <a href="/pr">Live Results</a></div> *@
        <div class="card-header p-2 pl-3">Polling Station Wise Result Posting </div>

        <div class="card-body text-black bg-light" style="color:black; padding: 8px;">
            <div class="row">
                <div class="col-md">
                    @if (assmblieslist != null)
                    {
                        <SfDropDownList TItem="GeneralItemDTO" TValue="int?" Placeholder="Select Assembly"
                                        DataSource="@assmblieslist" FloatLabelType="FloatLabelType.Always" AllowFiltering="true"
                                        FilterType="FilterType.Contains" ShowClearButton="true">
                            <DropDownListFieldSettings Value="Id" Text="EnglishTitle"></DropDownListFieldSettings>
                            <DropDownListEvents TValue="int?" TItem="GeneralItemDTO" ValueChange="@OnAssemblyChange"></DropDownListEvents>
                        </SfDropDownList>
                    }
                    else
                    {
                        <p></p>
                    }
                </div>
                <div class="col-md">
                    @if (constituencylist != null)
                    {
                        @if (constituencylist.Any())
                        {
                            <SfDropDownList AllowFiltering="true" TItem="GeneralItemDTO" TValue="int?"
                                            Placeholder="Constituency" DataSource="@constituencylist"
                                            FloatLabelType="FloatLabelType.Always" ShowClearButton="true">
                                <DropDownListFieldSettings Value="Id" Text="EnglishTitle"></DropDownListFieldSettings>
                                <DropDownListEvents TValue="int?" TItem="GeneralItemDTO" ValueChange="@OnConstChange"></DropDownListEvents>
                            </SfDropDownList>
                        }
                        else
                        {
                            <p>No constituency is defined on this assembly</p>
                        }
                    }
                    else
                    {
                        <p></p>
                    }
                </div>
                @if (state.state.ElectionType == ElectionType.LocalBody)
                {
                    <div class="col-md-2">
                        @if (seatypelist != null)
                        {
                            @if (seatypelist.Any())
                            {
                                <SfDropDownList TValue="int?" TItem="GeneralItemDTO" Placeholder="Seat" DataSource="@seatypelist" FloatLabelType="FloatLabelType.Always" ShowClearButton="true"
                                                AllowFiltering="true" FilterType="FilterType.Contains">
                                    <DropDownListFieldSettings Value="Id" Text="EnglishTitle"></DropDownListFieldSettings>
                                    <DropDownListEvents TValue="int?" TItem="GeneralItemDTO" ValueChange="@OnSeatTypChange"></DropDownListEvents>
                                </SfDropDownList>
                            }
                            else
                            {
                                <p>No seat type is define on this assembly</p>
                            }
                        }
                        else
                        {
                            <p></p>
                        }
                    </div>
                }
            </div>
        </div>
    </div>
    @if (SelectedAssemblyId != null && SelectedStructureId != null && SelectedSeatTypeId != null)
    {
        @if (result_detail == null || result_detail.Count == 0)
        {
            <h3>No nomination found on this constituency</h3>
        }
        else if (result_detail[0].TotalPollingStations == 0)
        {
            <h3>Total Polling Stations is not define on this constituency</h3>
        }
        else if (result_detail[0].VotersCount == 0)
        {
            <h3>Total Voters are not defined on this constituency</h3>
        }
        else
        {
            <div class="row">
                <div class="col-md-4">
                    <div class="card border border-success bg-success text-white mb-2">
                        <div class="card-header p-2 pl-3">
                            Polling Station
                            @if (pollingStationList == null || pollingStationList.Count == 0)
                            {
                                <AuthorizeView Roles="Administrators">
                                    <MudButton Size="Size.Small" Variant="Variant.Filled" Color="Color.Primary"
                                               OnClick="GeneratePS"
                                               StartIcon="@Icons.Material.Filled.Create">
                                        Generate PS
                                    </MudButton>
                                </AuthorizeView>
                            }

                        </div>
                        <div class="card-body text-black bg-light" style="padding: 8px">
                            @if (result_detail.Any(k => k.IsBilaMokabilaWinner))
                            {
                                <p>امیدوار پہلے سے ہی بلا مقابلہ جیت چکا ہے</p>
                                <p>Candidate has already won unopposed</p>
                            }
                            else
                            {
                                @if (pollingStationList != null && pollingStationList.Any())
                                {
                                    <table class="table table-sm">
                                        <tr>
                                            <td>
                                                <SfDropDownList TItem="GeneralItemDTO" TValue="int?" Placeholder="Polling Station"
                                                                DataSource="@pollingStationList" FloatLabelType="FloatLabelType.Always" AllowFiltering="true"
                                                                FilterType="FilterType.Contains" ShowClearButton="true">
                                                    <DropDownListFieldSettings Value="Id" Text="EnglishTitle"></DropDownListFieldSettings>
                                                    <DropDownListEvents TValue="int?" TItem="GeneralItemDTO" ValueChange="@OnPollingStationChange"></DropDownListEvents>
                                                </SfDropDownList>

                                                @if (PollingStationId != null)
                                                {
                                                    @if (_ResultMode == ResultMode.Default || _ResultMode == ResultMode.PollingStationWise)
                                                    {
                                                        <MudButton Color="Color.Primary" Size="Size.Small" Variant="Variant.Filled"
                                                                   StartIcon="@Icons.Material.Filled.Save" OnClick="SaveRecord">
                                                            Save
                                                        </MudButton>
                                                        <span> </span>
                                                        <MudButton Color="Color.Error" Size="Size.Small" Variant="Variant.Filled"
                                                                   StartIcon="@Icons.Material.Filled.Lock" OnClick="DisablePSResultPosting">
                                                            Disable PS Result Posting
                                                        </MudButton>
                                                    }
                                                    else
                                                    {
                                                        <MudButton Color="Color.Error" Size="Size.Small" Variant="Variant.Filled"
                                                                   StartIcon="@Icons.Material.Filled.LockOpen" OnClick="EnablePSResultPosting">
                                                            Enable PS Result Posting
                                                        </MudButton>
                                                    }
                                                }
                                            </td>
                                        </tr>
                                    </table>
                                }
                                else
                                {
                                    <p>No Polling Station is defined in this constituency</p>
                                }

                                @*<table class="table table-sm">
                                        <tr>
                                            <td>
                                                @if (pollingStationList != null)
                                                {
                                                    if (pollingStationList.Any())
                                                    {
                                                        <SfDropDownList TItem="GeneralItemDTO" TValue="int?" Placeholder="Polling Station"
                                                                        DataSource="@pollingStationList" FloatLabelType="FloatLabelType.Always" AllowFiltering="true"
                                                                        FilterType="FilterType.Contains" ShowClearButton="true">
                                                            <DropDownListFieldSettings Value="Id" Text="EnglishTitle"></DropDownListFieldSettings>
                                                            <DropDownListEvents TValue="int?" TItem="GeneralItemDTO" ValueChange="@OnPollingStationChange"></DropDownListEvents>
                                                        </SfDropDownList>


                                                        @if (PollingStationId != null)
                                                        {
                                                            <MudButton Color="Color.Primary" Size="MudBlazor.Size.Small" Variant="Variant.Filled"
                                                                       StartIcon="@Icons.Material.Filled.Save"
                                                                       OnClick="SaveRecord">
                                                                Save
                                                            </MudButton>
                                                        }
                                                    }
                                                }
                                                else
                                                {
                                                    <p>No Polling Station is define on this constituency</p>
                                                }

                                            </td>
                                        </tr>
                                    </table>*@
                            }

                        </div>
                    </div>
                </div>
                <div class="col-md-8">
                    <div class="card border border-secondary bg-secondary mb-2">
                        <div class="card-header p-2 pl-3">Constituency Info</div>
                        <div class="card-body bg-light" style="padding: 8px;">
                            <table class="table table">
                                <tr>
                                    <td>Total Polling Stations: <b>@TotalPollingStations</b></td>
                                    <td>Total Voters: <b>@TotalVoters</b></td>
                                    <td>
                                        Male Voters:
                                        @if (PollingStationId != null)
                                        {
                                            if (MaleVoters != null)
                                            {
                                                <b>@MaleVoters</b>
                                            }
                                            else
                                            {
                                                <b>Not defined</b>
                                            }
                                        }
                                        else
                                        {
                                            <b>@MaleVoters</b>
                                        }
                                    </td>
                                    <td>
                                        Female Voters:
                                        @if (PollingStationId != null)
                                        {
                                            if (FemaleVoters != null)
                                            {
                                                <b>@FemaleVoters</b>
                                            }
                                            else
                                            {
                                                <b>Not defined</b>
                                            }
                                        }
                                        else
                                        {
                                            <b>@FemaleVoters</b>
                                        }
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        @if (false)
                                        {
                                            <b>Votes Caste:</b>
                                            <br/>
                                            <SfNumericTextBox Placeholder="" FloatLabelType="FloatLabelType.Never"
                                                              Decimals="0" Min="0" Format="###,###,##0" ShowSpinButton="false" ShowClearButton="true"
                                                              @bind-Value="VoteCaste"/>
                                        }
                                    </td>
                                    <td>
                                    </td>
                                    <td>Voter Turnout (%): <b>@TurnOutPer</b></td>
                                    <td>&nbsp;</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card border-info border bg-info text-white mb-2 ">
                <div class="card-header p-2 pl-3">Candidates</div>
                <div class="card-body bg-light text-black" style="color:black; padding:8px">
                    <table class="table table-sm table-striped">
                        <thead>
                        <tr class="bg-info text-white">
                            <th>&nbsp;</th>
                            <th>Name</th>
                            <th colspan="2">Party</th>
                            <th colspan="2">Symbol</th>
                            <th>Weight</th>
                            <th>Votes</th>
                            <th>Votes (PS)</th>
                            @if (PollingStationId != null)
                            {
                                <th style="width: 100px">Votes (PS)</th>
                            }
                        </tr>
                        </thead>
                        <tbody>
                        @foreach (var res in result_detail)
                        {
                            //var cImg = $"media/candidates/{ res.CandidateId }.jpg";
                            //var pImg = $"media/flags/{res.PartyId}.jpg";
                            //var sImg = $"media/symbols/{res.SymbolId}.jpg";
                            var stl = res.IsWithdraw ? "background-color:#dc35457a" : "";
                            <tr style="@stl">
                                <td style="width:65px; text-align:center">
                                    <img src="@res.candidateImg" style="width:60px" align="left" alt="1"/>
                                </td>
                                <td>
                                    @res.EnglishName<br/>
                                    <span class="urdu-column">@res.UrduName</span>
                                    @if (res.IsWithdraw)
                                    {
                                        <span style='color:red; font-weight:bold'> (Withdraw)</span>
                                    }
                                </td>
                                <td style="width:65px; text-align:center">
                                    <img src="@res.flagImg" style="width:60px" align="left" alt="PTI"/>
                                </td>
                                <td>
                                    @res.Party<br/>
                                    <span class="urdu-column">@res.PartyUrdu</span>
                                </td>
                                <td style="width:65px; text-align:center">
                                    <img src="@res.symbolImg" style="width:60px" align="left" alt="BAT"/>
                                </td>
                                <td>
                                    @res.Symbol<br/>
                                    <span class="urdu-column">@res.SymbolUrdu</span>
                                </td>
                                <td>@res.Weight</td>
                                <td>@res.TotalVotes</td>
                                <td>@res.TotalVotesPS</td>
                                @if (PollingStationId != null)
                                {
                                    <td>
                                        <SfNumericTextBox Format="########" @bind-Value="res.PSVotes" Decimals="0" Min="0" Placeholder="Votes" ShowSpinButton="false"></SfNumericTextBox>
                                    </td>
                                }

                                /*
                                <td>
                                    <SfButton CssClass="e-info">Edit</SfButton>
                                </td>*/
                            </tr>
                        }
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="card border border-secondary bg-secondary">
                <div class="card-header p-2 pl-3">Namanigars</div>
                <div class="card-body bg-light text-black" style="color:black; padding:8px">
                    <SfGrid DataSource="namanigarList" AllowSorting="true" AllowTextWrap="true" Width="100%">
                        <GridColumns>
                            <GridColumn Field="@nameof(NamanigarDTO.Code)" HeaderText="Code" AutoFit="true"></GridColumn>
                            <GridColumn Field="@nameof(NamanigarDTO.Name)" HeaderText="Code" AutoFit="true"></GridColumn>
                            <GridColumn Field="@nameof(NamanigarDTO.Phone)" HeaderText="Phone" AutoFit="true"></GridColumn>
                            <GridColumn Field="@nameof(NamanigarDTO.Email)" HeaderText="Email" AutoFit="true"></GridColumn>
                            <GridColumn Field="@nameof(NamanigarDTO.PSDetail)" HeaderText="PS Detail" AutoFit="true"></GridColumn>
                        </GridColumns>
                    </SfGrid>
                </div>
            </div>
        }
    }

</div>