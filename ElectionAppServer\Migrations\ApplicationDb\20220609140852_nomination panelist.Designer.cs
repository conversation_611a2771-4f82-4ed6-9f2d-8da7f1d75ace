﻿// <auto-generated />
using System;
using ElectionAppServer.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

namespace ElectionAppServer.Migrations.ApplicationDb
{
    [DbContext(typeof(ApplicationDbContext))]
    [Migration("20220609140852_nomination panelist")]
    partial class nominationpanelist
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("Relational:MaxIdentifierLength", 128)
                .HasAnnotation("ProductVersion", "2.2.0-rtm-35687")
                .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

            modelBuilder.Entity("ElectionAppServer.Model.ApplicationUser", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<int>("AccessFailedCount")
                        .HasColumnType("int");

                    b.Property<string>("Address")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("Code")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("District")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("Email")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<bool>("EmailConfirmed")
                        .HasColumnType("bit");

                    b.Property<string>("EmpCode")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<string>("FullName")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<int?>("Gender")
                        .HasColumnType("int");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<bool>("LockoutEnabled")
                        .HasColumnType("bit");

                    b.Property<DateTimeOffset?>("LockoutEnd")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("NormalizedEmail")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("NormalizedUserName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("PasswordHash")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("PhoneNumberConfirmed")
                        .HasColumnType("bit");

                    b.Property<string>("SecurityStamp")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("TwoFactorEnabled")
                        .HasColumnType("bit");

                    b.Property<string>("UserName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedEmail")
                        .HasDatabaseName("EmailIndex");

                    b.HasIndex("NormalizedUserName")
                        .IsUnique()
                        .HasDatabaseName("UserNameIndex")
                        .HasFilter("[NormalizedUserName] IS NOT NULL");

                    b.ToTable("AspNetUsers");
                });

            modelBuilder.Entity("ElectionAppServer.Model.AssemblyType", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("(getdate())");

                    b.Property<string>("EnglishTitle")
                        .IsRequired()
                        .HasMaxLength(200)
                        .IsUnicode(false)
                        .HasColumnType("varchar(200)");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("UrduTitle")
                        .IsRequired()
                        .HasMaxLength(200)
                        .IsUnicode(true)
                        .HasColumnType("nvarchar(200)");

                    b.HasKey("Id");

                    b.ToTable("AssemblyType");
                });

            modelBuilder.Entity("ElectionAppServer.Model.Candidate", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<int>("CandidatePoll")
                        .HasColumnType("int");

                    b.Property<int>("CandidateType")
                        .HasColumnType("int");

                    b.Property<int?>("CasteId")
                        .HasColumnType("int");

                    b.Property<string>("ContactNumber")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateOfBirth")
                        .HasColumnType("datetime2");

                    b.Property<int?>("Day")
                        .HasColumnType("int");

                    b.Property<int?>("DistrictId")
                        .HasColumnType("int");

                    b.Property<string>("EducationDegree")
                        .HasMaxLength(200)
                        .IsUnicode(true)
                        .HasColumnType("nvarchar(200)");

                    b.Property<int?>("EducationId")
                        .HasColumnType("int");

                    b.Property<string>("EnglishName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .IsUnicode(true)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("FatherEnglishName")
                        .HasMaxLength(100)
                        .IsUnicode(false)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("FatherUrduName")
                        .HasMaxLength(100)
                        .IsUnicode(true)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int?>("Gender")
                        .HasColumnType("int");

                    b.Property<bool>("IsFresh")
                        .HasColumnType("bit");

                    b.Property<int?>("LanguageId")
                        .HasColumnType("int");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<int?>("Month")
                        .HasColumnType("int");

                    b.Property<int?>("ProfessionId")
                        .HasColumnType("int");

                    b.Property<string>("Source")
                        .HasMaxLength(100)
                        .IsUnicode(false)
                        .HasColumnType("varchar(100)");

                    b.Property<int?>("SourceId")
                        .HasColumnType("int");

                    b.Property<string>("StrDistrict")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("TotalAssets")
                        .HasMaxLength(1000)
                        .IsUnicode(false)
                        .HasColumnType("varchar(1000)");

                    b.Property<string>("Trivia")
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)");

                    b.Property<string>("UrduName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .IsUnicode(true)
                        .HasColumnType("nvarchar(200)");

                    b.Property<int?>("Year")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("CasteId");

                    b.HasIndex("DistrictId");

                    b.HasIndex("EducationId");

                    b.HasIndex("LanguageId");

                    b.HasIndex("ProfessionId");

                    b.ToTable("Candidates");
                });

            modelBuilder.Entity("ElectionAppServer.Model.CandidateDistrict", b =>
                {
                    b.Property<int>("CandidateId")
                        .HasColumnType("int");

                    b.Property<int>("DistrictId")
                        .HasColumnType("int");

                    b.Property<int>("ElectionId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(450)
                        .IsUnicode(false)
                        .HasColumnType("varchar(450)");

                    b.Property<DateTime>("CreatedDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getdate()");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(450)
                        .IsUnicode(false)
                        .HasColumnType("varchar(450)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("CandidateId", "DistrictId", "ElectionId");

                    b.HasIndex("DistrictId");

                    b.HasIndex("ElectionId", "CandidateId")
                        .IsUnique();

                    b.ToTable("CandidateDistricts");
                });

            modelBuilder.Entity("ElectionAppServer.Model.CandidatePartyAffiliation", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<int>("CandidateId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateFrom")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateTo")
                        .HasColumnType("datetime2");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("PartyId")
                        .HasColumnType("int");

                    b.Property<string>("Position")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<bool>("TillToday")
                        .HasColumnType("bit");

                    b.HasKey("Id");

                    b.HasIndex("CandidateId");

                    b.HasIndex("PartyId");

                    b.ToTable("PartyAffiliations");
                });

            modelBuilder.Entity("ElectionAppServer.Model.CandidtePoliticalCareer", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<int>("CandidateId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateFrom")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateTo")
                        .HasColumnType("datetime2");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Position")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("TillToday")
                        .HasColumnType("bit");

                    b.HasKey("Id");

                    b.HasIndex("CandidateId");

                    b.ToTable("PoliticalCareers");
                });

            modelBuilder.Entity("ElectionAppServer.Model.Caste", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("EnglishTitle")
                        .IsRequired()
                        .HasMaxLength(200)
                        .IsUnicode(false)
                        .HasColumnType("varchar(200)");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("UrduTitle")
                        .IsRequired()
                        .HasMaxLength(200)
                        .IsUnicode(true)
                        .HasColumnType("nvarchar(200)");

                    b.HasKey("Id");

                    b.HasIndex("UrduTitle")
                        .IsUnique();

                    b.ToTable("Castes");
                });

            modelBuilder.Entity("ElectionAppServer.Model.Coalition", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("EnglishTitle")
                        .IsRequired()
                        .HasMaxLength(200)
                        .IsUnicode(false)
                        .HasColumnType("varchar(200)");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("UrduTitle")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.HasKey("Id");

                    b.HasIndex("EnglishTitle")
                        .IsUnique();

                    b.HasIndex("UrduTitle")
                        .IsUnique();

                    b.ToTable("Coalitions");
                });

            modelBuilder.Entity("ElectionAppServer.Model.Education", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("EnglishTitle")
                        .IsRequired()
                        .HasMaxLength(200)
                        .IsUnicode(false)
                        .HasColumnType("varchar(200)");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("UrduTitle")
                        .IsRequired()
                        .HasMaxLength(200)
                        .IsUnicode(true)
                        .HasColumnType("nvarchar(200)");

                    b.HasKey("Id");

                    b.HasIndex("UrduTitle")
                        .IsUnique();

                    b.ToTable("Educations");
                });

            modelBuilder.Entity("ElectionAppServer.Model.Election", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<int>("CandidatePoll")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)");

                    b.Property<int?>("ElectionType")
                        .HasColumnType("int");

                    b.Property<string>("EnglishTitle")
                        .IsRequired()
                        .HasMaxLength(200)
                        .IsUnicode(false)
                        .HasColumnType("varchar(200)");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<int?>("ParentElectionId")
                        .HasColumnType("int");

                    b.Property<string>("UrduTitle")
                        .IsRequired()
                        .HasMaxLength(200)
                        .IsUnicode(true)
                        .HasColumnType("nvarchar(200)");

                    b.Property<int>("YearFrom")
                        .HasColumnType("int");

                    b.Property<int>("YearTo")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("ParentElectionId")
                        .IsUnique()
                        .HasFilter("[ParentElectionId] IS NOT NULL");

                    b.ToTable("Elections");
                });

            modelBuilder.Entity("ElectionAppServer.Model.ElectionAssembly", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<int>("AssemblyTypeId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<int?>("ElectionAssemblyType")
                        .HasColumnType("int");

                    b.Property<int>("ElectionId")
                        .HasColumnType("int");

                    b.Property<string>("EnglishTitle")
                        .IsRequired()
                        .HasMaxLength(200)
                        .IsUnicode(false)
                        .HasColumnType("varchar(200)");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("NominationType")
                        .HasColumnType("int");

                    b.Property<int?>("ReserveSeats")
                        .HasColumnType("int");

                    b.Property<string>("UrduTitle")
                        .IsRequired()
                        .HasMaxLength(200)
                        .IsUnicode(true)
                        .HasColumnType("nvarchar(200)");

                    b.Property<int?>("WomenSeats")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("AssemblyTypeId");

                    b.HasIndex("ElectionId");

                    b.ToTable("ElectionAssemblies");
                });

            modelBuilder.Entity("ElectionAppServer.Model.ElectionAssemblySeat", b =>
                {
                    b.Property<int>("ElectionAssemblyId")
                        .HasColumnType("int");

                    b.Property<int>("SeatTypeId")
                        .HasColumnType("int");

                    b.Property<int>("ConstituencyType")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<int?>("SeatCount")
                        .HasColumnType("int");

                    b.HasKey("ElectionAssemblyId", "SeatTypeId", "ConstituencyType");

                    b.HasIndex("SeatTypeId");

                    b.ToTable("ElectionAssemblySeats");
                });

            modelBuilder.Entity("ElectionAppServer.Model.ElectionPhase", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("ElectionId")
                        .HasColumnType("int");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<bool>("IsLocked")
                        .HasColumnType("bit");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.HasKey("Id");

                    b.HasIndex("ElectionId", "Title")
                        .IsUnique();

                    b.ToTable("ElectionPhases");
                });

            modelBuilder.Entity("ElectionAppServer.Model.Language", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("EnglishTitle")
                        .IsRequired()
                        .HasMaxLength(200)
                        .IsUnicode(false)
                        .HasColumnType("varchar(200)");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("UrduTitle")
                        .IsRequired()
                        .HasMaxLength(200)
                        .IsUnicode(true)
                        .HasColumnType("nvarchar(200)");

                    b.HasKey("Id");

                    b.HasIndex("UrduTitle")
                        .IsUnique();

                    b.ToTable("Languages");
                });

            modelBuilder.Entity("ElectionAppServer.Model.LiveResult", b =>
                {
                    b.Property<int>("ElectionId")
                        .HasColumnType("int");

                    b.Property<int>("PhaseId")
                        .HasColumnType("int");

                    b.Property<int>("AssemblyId")
                        .HasColumnType("int");

                    b.Property<int>("ConstituencyId")
                        .HasColumnType("int");

                    b.Property<int>("SeatTypeId")
                        .HasColumnType("int");

                    b.Property<string>("Assembly")
                        .IsRequired()
                        .HasMaxLength(400)
                        .HasColumnType("nvarchar(400)");

                    b.Property<string>("Constituency")
                        .IsRequired()
                        .HasMaxLength(400)
                        .HasColumnType("nvarchar(400)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(450)
                        .IsUnicode(false)
                        .HasColumnType("varchar(450)");

                    b.Property<DateTime>("CreatedDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getdate()");

                    b.Property<string>("Election")
                        .IsRequired()
                        .HasMaxLength(400)
                        .HasColumnType("nvarchar(400)");

                    b.Property<bool>("IsPublished")
                        .HasColumnType("bit");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(450)
                        .IsUnicode(false)
                        .HasColumnType("varchar(450)");

                    b.Property<DateTime>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Phase")
                        .IsRequired()
                        .HasMaxLength(400)
                        .HasColumnType("nvarchar(400)");

                    b.Property<int>("PollingStationResult")
                        .HasColumnType("int");

                    b.Property<string>("PublishedByUser")
                        .HasMaxLength(450)
                        .IsUnicode(false)
                        .HasColumnType("varchar(450)");

                    b.Property<DateTime?>("PublishedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("RunnerUp")
                        .IsRequired()
                        .HasMaxLength(400)
                        .HasColumnType("nvarchar(400)");

                    b.Property<int?>("RunnerUpCandidateId")
                        .HasColumnType("int");

                    b.Property<string>("RunnerUpParty")
                        .IsRequired()
                        .HasMaxLength(400)
                        .HasColumnType("nvarchar(400)");

                    b.Property<int?>("RunnerUpVotes")
                        .HasColumnType("int");

                    b.Property<string>("SeatType")
                        .IsRequired()
                        .HasMaxLength(400)
                        .HasColumnType("nvarchar(400)");

                    b.Property<int>("TotalPollingStations")
                        .HasColumnType("int");

                    b.Property<int>("TotalVoters")
                        .HasColumnType("int");

                    b.Property<string>("Winner")
                        .IsRequired()
                        .HasMaxLength(400)
                        .HasColumnType("nvarchar(400)");

                    b.Property<int?>("WinnerCandidateId")
                        .HasColumnType("int");

                    b.Property<string>("WinnerParty")
                        .IsRequired()
                        .HasMaxLength(400)
                        .HasColumnType("nvarchar(400)");

                    b.Property<int>("WinnerVotes")
                        .HasColumnType("int");

                    b.HasKey("ElectionId", "PhaseId", "AssemblyId", "ConstituencyId", "SeatTypeId");

                    b.ToTable("LiveResults");
                });

            modelBuilder.Entity("ElectionAppServer.Model.Namanigar", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(450)
                        .IsUnicode(false)
                        .HasColumnType("varchar(450)");

                    b.Property<DateTime>("CreatedDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getdate()");

                    b.Property<string>("District")
                        .IsRequired()
                        .HasMaxLength(200)
                        .IsUnicode(true)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("Email")
                        .HasMaxLength(300)
                        .IsUnicode(false)
                        .HasColumnType("varchar(300)");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(450)
                        .IsUnicode(false)
                        .HasColumnType("varchar(450)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .IsUnicode(false)
                        .HasColumnType("varchar(200)");

                    b.Property<string>("Phone")
                        .IsRequired()
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.HasKey("Id");

                    b.ToTable("Namanigars");
                });

            modelBuilder.Entity("ElectionAppServer.Model.NamanigarConstituency", b =>
                {
                    b.Property<int>("NamanigarId")
                        .HasColumnType("int");

                    b.Property<int>("ConstituencyId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(450)
                        .IsUnicode(false)
                        .HasColumnType("varchar(450)");

                    b.Property<DateTime>("CreatedDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getdate()");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(450)
                        .IsUnicode(false)
                        .HasColumnType("varchar(450)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("PSDetail")
                        .HasMaxLength(500)
                        .IsUnicode(false)
                        .HasColumnType("varchar(500)");

                    b.HasKey("NamanigarId", "ConstituencyId");

                    b.HasIndex("ConstituencyId");

                    b.ToTable("NamanigarConstituencies");
                });

            modelBuilder.Entity("ElectionAppServer.Model.NamanigarResult", b =>
                {
                    b.Property<int>("NominationId")
                        .HasColumnType("int");

                    b.Property<int>("BatchId")
                        .HasColumnType("int");

                    b.Property<int>("ChangePollingStations")
                        .HasColumnType("int");

                    b.Property<int>("ChangeVotes")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(450)
                        .IsUnicode(false)
                        .HasColumnType("varchar(450)");

                    b.Property<DateTime>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getdate()");

                    b.Property<bool>("IsViewed")
                        .HasColumnType("bit");

                    b.Property<string>("LastActionMode")
                        .IsRequired()
                        .HasMaxLength(10)
                        .IsUnicode(false)
                        .HasColumnType("varchar(10)");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(450)
                        .IsUnicode(false)
                        .HasColumnType("varchar(450)");

                    b.Property<DateTime?>("ModifiedOn")
                        .HasColumnType("datetime2");

                    b.Property<int?>("NamanigarId")
                        .HasColumnType("int");

                    b.Property<int>("ResultPollingStations")
                        .HasColumnType("int");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<int>("Votes")
                        .HasColumnType("int");

                    b.HasKey("NominationId");

                    b.HasIndex("BatchId");

                    b.HasIndex("NamanigarId");

                    b.ToTable("NamanigarResults");
                });

            modelBuilder.Entity("ElectionAppServer.Model.NamanigarResultLog", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<DateTime>("ActionDateTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("ApprovedBy")
                        .HasMaxLength(450)
                        .IsUnicode(false)
                        .HasColumnType("varchar(450)");

                    b.Property<DateTime?>("ApprovedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("BatchId")
                        .HasColumnType("int");

                    b.Property<int>("ChangePollingStations")
                        .HasColumnType("int");

                    b.Property<int>("ChangeVotes")
                        .HasColumnType("int");

                    b.Property<string>("LastActionMode")
                        .IsRequired()
                        .HasMaxLength(10)
                        .IsUnicode(false)
                        .HasColumnType("varchar(10)");

                    b.Property<string>("NamanigarUserId")
                        .IsRequired()
                        .HasMaxLength(450)
                        .IsUnicode(false)
                        .HasColumnType("varchar(450)");

                    b.Property<int>("NominationId")
                        .HasColumnType("int");

                    b.Property<int>("ResultPollingStations")
                        .HasColumnType("int");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<int>("Votes")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("BatchId");

                    b.ToTable("NamanigarResultLogs");
                });

            modelBuilder.Entity("ElectionAppServer.Model.Nomination", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<int>("CandidteId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("ElectionAssemblyId")
                        .HasColumnType("int");

                    b.Property<int>("ElectionPhaseId")
                        .HasColumnType("int");

                    b.Property<bool>("IsBilaMokabilaWinner")
                        .HasColumnType("bit");

                    b.Property<bool>("IsWinner")
                        .HasColumnType("bit");

                    b.Property<bool>("IsWithdraw")
                        .HasColumnType("bit");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("PartyId")
                        .HasColumnType("int");

                    b.Property<int?>("ResultPollingStations")
                        .HasColumnType("int");

                    b.Property<int>("SeatTypeId")
                        .HasColumnType("int");

                    b.Property<int>("StructureId")
                        .HasColumnType("int");

                    b.Property<int?>("SymbolId")
                        .HasColumnType("int");

                    b.Property<int?>("TotalPollingStations")
                        .HasColumnType("int");

                    b.Property<int?>("Votes")
                        .HasColumnType("int");

                    b.Property<int>("Weight")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("CandidteId");

                    b.HasIndex("ElectionPhaseId");

                    b.HasIndex("PartyId");

                    b.HasIndex("SeatTypeId");

                    b.HasIndex("StructureId");

                    b.HasIndex("SymbolId");

                    b.HasIndex("ElectionAssemblyId", "StructureId", "SeatTypeId", "CandidteId", "ElectionPhaseId");

                    b.ToTable("Nominations");
                });

            modelBuilder.Entity("ElectionAppServer.Model.NominationPanelist", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(200)
                        .IsUnicode(false)
                        .HasColumnType("varchar(200)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("Gender")
                        .HasColumnType("int");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(200)
                        .IsUnicode(false)
                        .HasColumnType("varchar(200)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("NameEnglish")
                        .IsRequired()
                        .HasMaxLength(200)
                        .IsUnicode(false)
                        .HasColumnType("varchar(200)");

                    b.Property<string>("NameUrdu")
                        .IsRequired()
                        .HasMaxLength(200)
                        .IsUnicode(true)
                        .HasColumnType("nvarchar(200)");

                    b.Property<int>("NominationId")
                        .HasColumnType("int");

                    b.Property<int>("SeatTypeId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("NominationId");

                    b.HasIndex("SeatTypeId");

                    b.ToTable("NominationPanelists");
                });

            modelBuilder.Entity("ElectionAppServer.Model.PSResult", b =>
                {
                    b.Property<int>("PollingStationId")
                        .HasColumnType("int");

                    b.Property<int>("NominationId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(450)
                        .IsUnicode(false)
                        .HasColumnType("varchar(450)");

                    b.Property<DateTime>("CreatedDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getdate()");

                    b.Property<bool>("IsPublished")
                        .HasColumnType("bit");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(450)
                        .IsUnicode(false)
                        .HasColumnType("varchar(450)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("PublishedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("PublishedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("Votes")
                        .HasColumnType("int");

                    b.HasKey("PollingStationId", "NominationId");

                    b.HasIndex("NominationId");

                    b.ToTable("PSResults");
                });

            modelBuilder.Entity("ElectionAppServer.Model.PSResultLog", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<bool>("IsPublished")
                        .HasColumnType("bit");

                    b.Property<DateTime>("LogDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("NominationId")
                        .HasColumnType("int");

                    b.Property<int>("PollingStationId")
                        .HasColumnType("int");

                    b.Property<string>("PublishedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("PublishedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("UserId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Votes")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.ToTable("PSResultLogs");
                });

            modelBuilder.Entity("ElectionAppServer.Model.PanelNomination", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<int>("AssemblyId")
                        .HasColumnType("int");

                    b.Property<string>("CandidateEnglishName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .IsUnicode(false)
                        .HasColumnType("varchar(200)");

                    b.Property<string>("CandidateUrduName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .IsUnicode(true)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(450)
                        .IsUnicode(false)
                        .HasColumnType("varchar(450)");

                    b.Property<DateTime>("CreatedDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getdate()");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(450)
                        .IsUnicode(false)
                        .HasColumnType("varchar(450)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("OrderNumber")
                        .HasColumnType("int");

                    b.Property<int>("PanelId")
                        .HasColumnType("int");

                    b.Property<int>("PhaseId")
                        .HasColumnType("int");

                    b.Property<int>("SeatTypeId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("AssemblyId");

                    b.HasIndex("PanelId");

                    b.HasIndex("PhaseId");

                    b.HasIndex("SeatTypeId");

                    b.ToTable("PanelNominations");
                });

            modelBuilder.Entity("ElectionAppServer.Model.Party", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("Address")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("CurrentLeader")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("CurrentLeaderDesignation")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("CurrentLeaderPicId")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTime?>("DateOfCreation")
                        .HasColumnType("datetime2");

                    b.Property<string>("Designation")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("EnglishTitle")
                        .IsRequired()
                        .HasMaxLength(200)
                        .IsUnicode(false)
                        .HasColumnType("varchar(200)");

                    b.Property<bool>("IsAJK")
                        .HasColumnType("bit");

                    b.Property<bool>("IsGB")
                        .HasColumnType("bit");

                    b.Property<bool>("IsGeneral")
                        .HasColumnType("bit");

                    b.Property<bool>("IsLB")
                        .HasColumnType("bit");

                    b.Property<bool>("IsLBBalochistan")
                        .HasColumnType("bit");

                    b.Property<bool>("IsLBPunjab")
                        .HasColumnType("bit");

                    b.Property<bool>("IsLBSindh")
                        .HasColumnType("bit");

                    b.Property<string>("Leader")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("LeaderPicId")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("ShortEnglishTitle")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ShortUrduTitle")
                        .HasMaxLength(50)
                        .IsUnicode(true)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int?>("SymbolId")
                        .HasColumnType("int");

                    b.Property<string>("Trivia")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<string>("UrduTitle")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.HasKey("Id");

                    b.HasIndex("EnglishTitle")
                        .IsUnique();

                    b.HasIndex("SymbolId");

                    b.HasIndex("UrduTitle")
                        .IsUnique();

                    b.ToTable("Parties");
                });

            modelBuilder.Entity("ElectionAppServer.Model.PartyCoalition", b =>
                {
                    b.Property<int>("PartyId")
                        .HasColumnType("int");

                    b.Property<int>("CoalitionId")
                        .HasColumnType("int");

                    b.Property<int>("ElectionId")
                        .HasColumnType("int");

                    b.HasKey("PartyId", "CoalitionId", "ElectionId");

                    b.HasIndex("CoalitionId");

                    b.HasIndex("ElectionId");

                    b.ToTable("PartyCoalitions");
                });

            modelBuilder.Entity("ElectionAppServer.Model.Profession", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("EnglishTitle")
                        .IsRequired()
                        .HasMaxLength(200)
                        .IsUnicode(false)
                        .HasColumnType("varchar(200)");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("UrduTitle")
                        .IsRequired()
                        .HasMaxLength(200)
                        .IsUnicode(true)
                        .HasColumnType("nvarchar(200)");

                    b.HasKey("Id");

                    b.HasIndex("UrduTitle")
                        .IsUnique();

                    b.ToTable("Professions");
                });

            modelBuilder.Entity("ElectionAppServer.Model.Reporter", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Email")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Phone")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("Reporters");
                });

            modelBuilder.Entity("ElectionAppServer.Model.ResultLog", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("Action")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("AssemblyId")
                        .HasColumnType("int");

                    b.Property<int>("ConstituencyId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedByUserId")
                        .IsRequired()
                        .HasMaxLength(450)
                        .IsUnicode(false)
                        .HasColumnType("varchar(450)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("NamanigarId")
                        .HasColumnType("int");

                    b.Property<int>("NewPollingStations")
                        .HasColumnType("int");

                    b.Property<int>("PhaseId")
                        .HasColumnType("int");

                    b.Property<int>("PollingStations")
                        .HasColumnType("int");

                    b.Property<int>("PreviousPollingStations")
                        .HasColumnType("int");

                    b.Property<int>("SeatTypeId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("AssemblyId");

                    b.HasIndex("ConstituencyId");

                    b.HasIndex("NamanigarId");

                    b.HasIndex("PhaseId");

                    b.HasIndex("SeatTypeId");

                    b.ToTable("ResultLogs");
                });

            modelBuilder.Entity("ElectionAppServer.Model.ResultLogDetail", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<int>("CandidateId")
                        .HasColumnType("int");

                    b.Property<int>("NewVotes")
                        .HasColumnType("int");

                    b.Property<int>("PreviousVotes")
                        .HasColumnType("int");

                    b.Property<int>("ResultLogId")
                        .HasColumnType("int");

                    b.Property<int>("Votes")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("CandidateId");

                    b.HasIndex("ResultLogId");

                    b.ToTable("ResultLogDetails");
                });

            modelBuilder.Entity("ElectionAppServer.Model.SearchCandidate", b =>
                {
                    b.Property<int>("ElectionId")
                        .HasColumnType("int");

                    b.Property<int>("CandidateId")
                        .HasColumnType("int");

                    b.Property<string>("CandidateType")
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)");

                    b.Property<string>("Constituencies")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(200)
                        .IsUnicode(false)
                        .HasColumnType("varchar(200)");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("datetime2");

                    b.Property<string>("CurrentParty")
                        .HasMaxLength(200)
                        .IsUnicode(false)
                        .HasColumnType("varchar(200)");

                    b.Property<DateTime?>("DateOfBirth")
                        .HasColumnType("datetime2");

                    b.Property<string>("District")
                        .HasMaxLength(200)
                        .IsUnicode(false)
                        .HasColumnType("varchar(200)");

                    b.Property<int?>("DistrictId")
                        .HasColumnType("int");

                    b.Property<string>("EnglishFatherName")
                        .HasMaxLength(200)
                        .IsUnicode(false)
                        .HasColumnType("varchar(200)");

                    b.Property<string>("EnglishName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .IsUnicode(false)
                        .HasColumnType("varchar(200)");

                    b.Property<string>("FullName")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<int?>("Gender")
                        .HasColumnType("int");

                    b.Property<bool>("IsFresh")
                        .HasColumnType("bit");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(200)
                        .IsUnicode(false)
                        .HasColumnType("varchar(200)");

                    b.Property<DateTime?>("ModifiedOn")
                        .HasColumnType("datetime2");

                    b.Property<int>("NominationCount")
                        .HasColumnType("int");

                    b.Property<string>("Status")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("UrduFatherName")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("UrduName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.HasKey("ElectionId", "CandidateId");

                    b.HasIndex("CandidateId");

                    b.HasIndex("ElectionId");

                    b.HasIndex("EnglishName");

                    b.ToTable("SearchCandidates");
                });

            modelBuilder.Entity("ElectionAppServer.Model.SeatType", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("(getdate())");

                    b.Property<string>("EnglishTitle")
                        .IsRequired()
                        .HasMaxLength(200)
                        .IsUnicode(false)
                        .HasColumnType("varchar(200)");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("UrduTitle")
                        .IsRequired()
                        .HasMaxLength(200)
                        .IsUnicode(true)
                        .HasColumnType("nvarchar(200)");

                    b.HasKey("Id");

                    b.HasIndex("UrduTitle")
                        .IsUnique();

                    b.ToTable("SeatTypes");
                });

            modelBuilder.Entity("ElectionAppServer.Model.Structure", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("Area")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int?>("AssemblyId")
                        .HasColumnType("int");

                    b.Property<string>("AverageHouseholdIncome")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Castes")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("Code")
                        .HasMaxLength(20)
                        .IsUnicode(true)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Discriminator")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("ElectionId")
                        .HasColumnType("int");

                    b.Property<string>("EnglishTitle")
                        .IsRequired()
                        .HasMaxLength(200)
                        .IsUnicode(false)
                        .HasColumnType("varchar(200)");

                    b.Property<int?>("FemaleVoters")
                        .HasColumnType("int");

                    b.Property<string>("GeneralTrivia")
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)");

                    b.Property<bool>("HasBilaMokablia")
                        .HasColumnType("bit");

                    b.Property<string>("ImportantAreas")
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)");

                    b.Property<string>("ImportantPoliticalPersonalities")
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)");

                    b.Property<bool>("IsPostponed")
                        .HasColumnType("bit");

                    b.Property<bool>("IsSeat")
                        .HasColumnType("bit");

                    b.Property<string>("Languages")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("LiteracyRate")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("MajorityIncomeSource")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<int?>("MaleVoters")
                        .HasColumnType("int");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<int?>("Population")
                        .HasColumnType("int");

                    b.Property<int?>("PrevStructureId")
                        .HasColumnType("int");

                    b.Property<string>("Problems")
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)");

                    b.Property<int>("ResultStatus")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(1);

                    b.Property<float?>("RuralAreaPer")
                        .HasColumnType("real");

                    b.Property<int?>("TotalPollingStations")
                        .HasColumnType("int");

                    b.Property<string>("Trivia")
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)");

                    b.Property<float?>("UrbanAreaPer")
                        .HasColumnType("real");

                    b.Property<string>("UrduTitle")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.HasKey("Id");

                    b.HasIndex("AssemblyId");

                    b.HasIndex("ElectionId");

                    b.HasIndex("PrevStructureId");

                    b.ToTable("Structures");

                    b.HasDiscriminator<string>("Discriminator").HasValue("Structure");
                });

            modelBuilder.Entity("ElectionAppServer.Model.Structures.PollingStation", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<int>("ConstituencyId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("EnglishTitle")
                        .IsRequired()
                        .HasMaxLength(200)
                        .IsUnicode(false)
                        .HasColumnType("varchar(200)");

                    b.Property<int?>("FemaleVoters")
                        .HasColumnType("int");

                    b.Property<int?>("MaleVoters")
                        .HasColumnType("int");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Number")
                        .IsRequired()
                        .HasMaxLength(200)
                        .IsUnicode(false)
                        .HasColumnType("varchar(200)");

                    b.Property<string>("UrduTitle")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.HasKey("Id");

                    b.HasIndex("ConstituencyId", "EnglishTitle");

                    b.HasIndex("ConstituencyId", "Number")
                        .IsUnique();

                    b.HasIndex("ConstituencyId", "UrduTitle");

                    b.ToTable("PollingStations");
                });

            modelBuilder.Entity("ElectionAppServer.Model.Structures.Region", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("EnglishTitle")
                        .IsRequired()
                        .HasMaxLength(200)
                        .IsUnicode(false)
                        .HasColumnType("varchar(200)");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("ProvinceId")
                        .HasColumnType("int");

                    b.Property<string>("UrduTitle")
                        .IsRequired()
                        .HasMaxLength(200)
                        .IsUnicode(true)
                        .HasColumnType("nvarchar(200)");

                    b.HasKey("Id");

                    b.HasIndex("ProvinceId", "EnglishTitle");

                    b.HasIndex("ProvinceId", "UrduTitle");

                    b.ToTable("Regions");
                });

            modelBuilder.Entity("ElectionAppServer.Model.Structures.StructureMapping", b =>
                {
                    b.Property<int>("StructureId")
                        .HasColumnType("int");

                    b.Property<int>("PreviousStructureId")
                        .HasColumnType("int");

                    b.Property<string>("Description")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.HasKey("StructureId", "PreviousStructureId");

                    b.HasIndex("PreviousStructureId");

                    b.ToTable("StructureMappings");
                });

            modelBuilder.Entity("ElectionAppServer.Model.Symbol", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("EnglishTitle")
                        .IsRequired()
                        .HasMaxLength(200)
                        .IsUnicode(false)
                        .HasColumnType("varchar(200)");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("UrduTitle")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.HasKey("Id");

                    b.ToTable("Symbols");
                });

            modelBuilder.Entity("ElectionAppServer.Model.UserConstituency", b =>
                {
                    b.Property<int>("ElectionAssemblyId")
                        .HasColumnType("int");

                    b.Property<string>("UserId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<int>("StructureId")
                        .HasColumnType("int");

                    b.HasKey("ElectionAssemblyId", "UserId", "StructureId");

                    b.HasIndex("StructureId");

                    b.HasIndex("UserId");

                    b.ToTable("UserConstituencies");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRole", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("NormalizedName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedName")
                        .IsUnique()
                        .HasDatabaseName("RoleNameIndex")
                        .HasFilter("[NormalizedName] IS NOT NULL");

                    b.ToTable("AspNetRoles");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<string>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("ClaimType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RoleId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetRoleClaims");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<string>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("ClaimType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserClaims");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<string>", b =>
                {
                    b.Property<string>("LoginProvider")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<string>("ProviderKey")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<string>("ProviderDisplayName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("LoginProvider", "ProviderKey");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserLogins");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<string>", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("RoleId")
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("UserId", "RoleId");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetUserRoles");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<string>", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("LoginProvider")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<string>("Name")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<string>("Value")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("UserId", "LoginProvider", "Name");

                    b.ToTable("AspNetUserTokens");
                });

            modelBuilder.Entity("ElectionAppServer.Model.District", b =>
                {
                    b.HasBaseType("ElectionAppServer.Model.Structure");

                    b.Property<int>("DivisionId")
                        .HasColumnType("int");

                    b.Property<int?>("RegionId")
                        .HasColumnType("int");

                    b.HasIndex("DivisionId");

                    b.HasIndex("RegionId");

                    b.HasDiscriminator().HasValue("District");
                });

            modelBuilder.Entity("ElectionAppServer.Model.Division", b =>
                {
                    b.HasBaseType("ElectionAppServer.Model.Structure");

                    b.Property<int>("ProvinceId")
                        .HasColumnType("int");

                    b.HasIndex("ProvinceId");

                    b.HasDiscriminator().HasValue("Division");
                });

            modelBuilder.Entity("ElectionAppServer.Model.NationalAssemblyHalka", b =>
                {
                    b.HasBaseType("ElectionAppServer.Model.Structure");

                    b.Property<int>("DistrictId")
                        .ValueGeneratedOnUpdateSometimes()
                        .HasColumnType("int")
                        .HasColumnName("DistrictId");

                    b.HasIndex("DistrictId");

                    b.HasDiscriminator().HasValue("NationalAssemblyHalka");
                });

            modelBuilder.Entity("ElectionAppServer.Model.Province", b =>
                {
                    b.HasBaseType("ElectionAppServer.Model.Structure");

                    b.HasDiscriminator().HasValue("Province");
                });

            modelBuilder.Entity("ElectionAppServer.Model.ProvincialAssemblyHalka", b =>
                {
                    b.HasBaseType("ElectionAppServer.Model.Structure");

                    b.Property<int>("DistrictId")
                        .ValueGeneratedOnUpdateSometimes()
                        .HasColumnType("int")
                        .HasColumnName("DistrictId");

                    b.HasIndex("DistrictId");

                    b.HasDiscriminator().HasValue("ProvincialAssemblyHalka");
                });

            modelBuilder.Entity("ElectionAppServer.Model.Town", b =>
                {
                    b.HasBaseType("ElectionAppServer.Model.Structure");

                    b.Property<int>("DistrictId")
                        .HasColumnType("int")
                        .HasColumnName("DistrictId");

                    b.HasIndex("DistrictId");

                    b.HasDiscriminator().HasValue("Town");
                });

            modelBuilder.Entity("ElectionAppServer.Model.UnionCouncil", b =>
                {
                    b.HasBaseType("ElectionAppServer.Model.Structure");

                    b.Property<int>("TownId")
                        .HasColumnType("int");

                    b.HasIndex("TownId");

                    b.HasIndex("ElectionId", "Code", "TownId")
                        .IsUnique()
                        .HasFilter("[Code] IS NOT NULL AND [TownId] IS NOT NULL");

                    b.HasIndex("ElectionId", "EnglishTitle", "TownId")
                        .IsUnique()
                        .HasFilter("[EnglishTitle] IS NOT NULL AND [TownId] IS NOT NULL");

                    b.HasIndex("ElectionId", "UrduTitle", "TownId")
                        .IsUnique()
                        .HasFilter("[TownId] IS NOT NULL");

                    b.HasDiscriminator().HasValue("UnionCouncil");
                });

            modelBuilder.Entity("ElectionAppServer.Model.Ward", b =>
                {
                    b.HasBaseType("ElectionAppServer.Model.Structure");

                    b.Property<int>("UnionCouncilId")
                        .HasColumnType("int");

                    b.HasIndex("UnionCouncilId");

                    b.HasDiscriminator().HasValue("Ward");
                });

            modelBuilder.Entity("ElectionAppServer.Model.Candidate", b =>
                {
                    b.HasOne("ElectionAppServer.Model.Caste", "Caste")
                        .WithMany("Candidates")
                        .HasForeignKey("CasteId");

                    b.HasOne("ElectionAppServer.Model.District", "District")
                        .WithMany()
                        .HasForeignKey("DistrictId");

                    b.HasOne("ElectionAppServer.Model.Education", "Education")
                        .WithMany("Candidates")
                        .HasForeignKey("EducationId");

                    b.HasOne("ElectionAppServer.Model.Language", "Language")
                        .WithMany("Candidates")
                        .HasForeignKey("LanguageId");

                    b.HasOne("ElectionAppServer.Model.Profession", "Profession")
                        .WithMany("Candidates")
                        .HasForeignKey("ProfessionId");

                    b.Navigation("Caste");

                    b.Navigation("District");

                    b.Navigation("Education");

                    b.Navigation("Language");

                    b.Navigation("Profession");
                });

            modelBuilder.Entity("ElectionAppServer.Model.CandidateDistrict", b =>
                {
                    b.HasOne("ElectionAppServer.Model.Candidate", "Candidate")
                        .WithMany("CandidateDistricts")
                        .HasForeignKey("CandidateId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ElectionAppServer.Model.District", "District")
                        .WithMany("CandidateDistricts")
                        .HasForeignKey("DistrictId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("ElectionAppServer.Model.Election", "Election")
                        .WithMany("CandidateDistricts")
                        .HasForeignKey("ElectionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Candidate");

                    b.Navigation("District");

                    b.Navigation("Election");
                });

            modelBuilder.Entity("ElectionAppServer.Model.CandidatePartyAffiliation", b =>
                {
                    b.HasOne("ElectionAppServer.Model.Candidate", "Candidate")
                        .WithMany("PartyAffiliations")
                        .HasForeignKey("CandidateId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ElectionAppServer.Model.Party", "Party")
                        .WithMany()
                        .HasForeignKey("PartyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Candidate");

                    b.Navigation("Party");
                });

            modelBuilder.Entity("ElectionAppServer.Model.CandidtePoliticalCareer", b =>
                {
                    b.HasOne("ElectionAppServer.Model.Candidate", "Candidate")
                        .WithMany("CandidtePoliticalCareers")
                        .HasForeignKey("CandidateId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Candidate");
                });

            modelBuilder.Entity("ElectionAppServer.Model.Election", b =>
                {
                    b.HasOne("ElectionAppServer.Model.Election", "ParentElection")
                        .WithOne()
                        .HasForeignKey("ElectionAppServer.Model.Election", "ParentElectionId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("ParentElection");
                });

            modelBuilder.Entity("ElectionAppServer.Model.ElectionAssembly", b =>
                {
                    b.HasOne("ElectionAppServer.Model.AssemblyType", "AssemblyType")
                        .WithMany("ElectionAssemblies")
                        .HasForeignKey("AssemblyTypeId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("ElectionAppServer.Model.Election", "Election")
                        .WithMany()
                        .HasForeignKey("ElectionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("AssemblyType");

                    b.Navigation("Election");
                });

            modelBuilder.Entity("ElectionAppServer.Model.ElectionAssemblySeat", b =>
                {
                    b.HasOne("ElectionAppServer.Model.ElectionAssembly", "ElectionAssembly")
                        .WithMany()
                        .HasForeignKey("ElectionAssemblyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ElectionAppServer.Model.SeatType", "SeatType")
                        .WithMany()
                        .HasForeignKey("SeatTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ElectionAssembly");

                    b.Navigation("SeatType");
                });

            modelBuilder.Entity("ElectionAppServer.Model.ElectionPhase", b =>
                {
                    b.HasOne("ElectionAppServer.Model.Election", "Election")
                        .WithMany("ElectionPhases")
                        .HasForeignKey("ElectionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Election");
                });

            modelBuilder.Entity("ElectionAppServer.Model.NamanigarConstituency", b =>
                {
                    b.HasOne("ElectionAppServer.Model.Structure", "Constituency")
                        .WithMany("NamanigarConstituencies")
                        .HasForeignKey("ConstituencyId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("ElectionAppServer.Model.Namanigar", "Namanigar")
                        .WithMany("NamanigarConstituencies")
                        .HasForeignKey("NamanigarId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Constituency");

                    b.Navigation("Namanigar");
                });

            modelBuilder.Entity("ElectionAppServer.Model.NamanigarResult", b =>
                {
                    b.HasOne("ElectionAppServer.Model.Namanigar", null)
                        .WithMany("NamanigarResults")
                        .HasForeignKey("NamanigarId");

                    b.HasOne("ElectionAppServer.Model.Nomination", "Nomination")
                        .WithOne("NamanigarResult")
                        .HasForeignKey("ElectionAppServer.Model.NamanigarResult", "NominationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Nomination");
                });

            modelBuilder.Entity("ElectionAppServer.Model.Nomination", b =>
                {
                    b.HasOne("ElectionAppServer.Model.Candidate", "Candidate")
                        .WithMany("Nominations")
                        .HasForeignKey("CandidteId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("ElectionAppServer.Model.ElectionAssembly", "ElectionAssembly")
                        .WithMany("Nominations")
                        .HasForeignKey("ElectionAssemblyId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("ElectionAppServer.Model.ElectionPhase", "ElectionPhase")
                        .WithMany("Nominations")
                        .HasForeignKey("ElectionPhaseId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("ElectionAppServer.Model.Party", "Party")
                        .WithMany("Nominations")
                        .HasForeignKey("PartyId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("ElectionAppServer.Model.SeatType", "SeatType")
                        .WithMany("Nominations")
                        .HasForeignKey("SeatTypeId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("ElectionAppServer.Model.Structure", "Structure")
                        .WithMany("Nominations")
                        .HasForeignKey("StructureId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("ElectionAppServer.Model.Symbol", "Symbol")
                        .WithMany("Nominations")
                        .HasForeignKey("SymbolId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Candidate");

                    b.Navigation("ElectionAssembly");

                    b.Navigation("ElectionPhase");

                    b.Navigation("Party");

                    b.Navigation("SeatType");

                    b.Navigation("Structure");

                    b.Navigation("Symbol");
                });

            modelBuilder.Entity("ElectionAppServer.Model.NominationPanelist", b =>
                {
                    b.HasOne("ElectionAppServer.Model.Nomination", "Nomination")
                        .WithMany("NominationPanelists")
                        .HasForeignKey("NominationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ElectionAppServer.Model.SeatType", "SeatType")
                        .WithMany("NominationPanelists")
                        .HasForeignKey("SeatTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Nomination");

                    b.Navigation("SeatType");
                });

            modelBuilder.Entity("ElectionAppServer.Model.PSResult", b =>
                {
                    b.HasOne("ElectionAppServer.Model.Nomination", "Nomination")
                        .WithMany("PSResults")
                        .HasForeignKey("NominationId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("ElectionAppServer.Model.Structures.PollingStation", "PollingStation")
                        .WithMany("PSResults")
                        .HasForeignKey("PollingStationId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Nomination");

                    b.Navigation("PollingStation");
                });

            modelBuilder.Entity("ElectionAppServer.Model.PanelNomination", b =>
                {
                    b.HasOne("ElectionAppServer.Model.ElectionAssembly", "Assembly")
                        .WithMany("PannelNominations")
                        .HasForeignKey("AssemblyId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("ElectionAppServer.Model.Candidate", "Panel")
                        .WithMany()
                        .HasForeignKey("PanelId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ElectionAppServer.Model.ElectionPhase", "Phase")
                        .WithMany("PannelNominations")
                        .HasForeignKey("PhaseId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("ElectionAppServer.Model.SeatType", "SeatType")
                        .WithMany("PannelNominations")
                        .HasForeignKey("SeatTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Assembly");

                    b.Navigation("Panel");

                    b.Navigation("Phase");

                    b.Navigation("SeatType");
                });

            modelBuilder.Entity("ElectionAppServer.Model.Party", b =>
                {
                    b.HasOne("ElectionAppServer.Model.Symbol", "Symbol")
                        .WithMany("Parties")
                        .HasForeignKey("SymbolId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Symbol");
                });

            modelBuilder.Entity("ElectionAppServer.Model.PartyCoalition", b =>
                {
                    b.HasOne("ElectionAppServer.Model.Coalition", "Coalition")
                        .WithMany("PartyCoalitions")
                        .HasForeignKey("CoalitionId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("ElectionAppServer.Model.Election", "Election")
                        .WithMany("PartyCoalitions")
                        .HasForeignKey("ElectionId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("ElectionAppServer.Model.Party", "Party")
                        .WithMany("PartyCoalitions")
                        .HasForeignKey("PartyId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Coalition");

                    b.Navigation("Election");

                    b.Navigation("Party");
                });

            modelBuilder.Entity("ElectionAppServer.Model.ResultLog", b =>
                {
                    b.HasOne("ElectionAppServer.Model.ElectionAssembly", "Assembly")
                        .WithMany("ResultLogs")
                        .HasForeignKey("AssemblyId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("ElectionAppServer.Model.Structure", "Constituency")
                        .WithMany("ResultLogs")
                        .HasForeignKey("ConstituencyId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("ElectionAppServer.Model.Namanigar", "Namanigar")
                        .WithMany("AsNamanigarResultLogs")
                        .HasForeignKey("NamanigarId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("ElectionAppServer.Model.ElectionPhase", "Phase")
                        .WithMany("ResultLogs")
                        .HasForeignKey("PhaseId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("ElectionAppServer.Model.SeatType", "SeatType")
                        .WithMany("ResultLogs")
                        .HasForeignKey("SeatTypeId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Assembly");

                    b.Navigation("Constituency");

                    b.Navigation("Namanigar");

                    b.Navigation("Phase");

                    b.Navigation("SeatType");
                });

            modelBuilder.Entity("ElectionAppServer.Model.ResultLogDetail", b =>
                {
                    b.HasOne("ElectionAppServer.Model.Candidate", "Candidate")
                        .WithMany("ResultLogDetails")
                        .HasForeignKey("CandidateId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ElectionAppServer.Model.ResultLog", "ResultLog")
                        .WithMany("ResultLogDetails")
                        .HasForeignKey("ResultLogId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Candidate");

                    b.Navigation("ResultLog");
                });

            modelBuilder.Entity("ElectionAppServer.Model.Structure", b =>
                {
                    b.HasOne("ElectionAppServer.Model.ElectionAssembly", "Assembly")
                        .WithMany("Constituencies")
                        .HasForeignKey("AssemblyId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("ElectionAppServer.Model.Election", "Election")
                        .WithMany("Structures")
                        .HasForeignKey("ElectionId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("ElectionAppServer.Model.Structure", "PrevStructure")
                        .WithMany()
                        .HasForeignKey("PrevStructureId");

                    b.Navigation("Assembly");

                    b.Navigation("Election");

                    b.Navigation("PrevStructure");
                });

            modelBuilder.Entity("ElectionAppServer.Model.Structures.PollingStation", b =>
                {
                    b.HasOne("ElectionAppServer.Model.Structure", "Constituency")
                        .WithMany("PollingStations")
                        .HasForeignKey("ConstituencyId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Constituency");
                });

            modelBuilder.Entity("ElectionAppServer.Model.Structures.Region", b =>
                {
                    b.HasOne("ElectionAppServer.Model.Province", "Province")
                        .WithMany("Regions")
                        .HasForeignKey("ProvinceId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Province");
                });

            modelBuilder.Entity("ElectionAppServer.Model.Structures.StructureMapping", b =>
                {
                    b.HasOne("ElectionAppServer.Model.Structure", "PreviousStructure")
                        .WithMany("PreviousStructureMappings")
                        .HasForeignKey("PreviousStructureId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("ElectionAppServer.Model.Structure", "Structure")
                        .WithMany("StructureMappings")
                        .HasForeignKey("StructureId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("PreviousStructure");

                    b.Navigation("Structure");
                });

            modelBuilder.Entity("ElectionAppServer.Model.UserConstituency", b =>
                {
                    b.HasOne("ElectionAppServer.Model.ElectionAssembly", "ElectionAssembly")
                        .WithMany("NamnigarConstituencies")
                        .HasForeignKey("ElectionAssemblyId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("ElectionAppServer.Model.Structure", "Structure")
                        .WithMany("UserConstituencies")
                        .HasForeignKey("StructureId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("ElectionAppServer.Model.ApplicationUser", "User")
                        .WithMany("UserConstituencies")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("ElectionAssembly");

                    b.Navigation("Structure");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<string>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<string>", b =>
                {
                    b.HasOne("ElectionAppServer.Model.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<string>", b =>
                {
                    b.HasOne("ElectionAppServer.Model.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<string>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ElectionAppServer.Model.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<string>", b =>
                {
                    b.HasOne("ElectionAppServer.Model.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("ElectionAppServer.Model.District", b =>
                {
                    b.HasOne("ElectionAppServer.Model.Division", "Division")
                        .WithMany("Districts")
                        .HasForeignKey("DivisionId")
                        .HasConstraintName("FK_Districts_Divisions")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("ElectionAppServer.Model.Structures.Region", "Region")
                        .WithMany("Districts")
                        .HasForeignKey("RegionId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Division");

                    b.Navigation("Region");
                });

            modelBuilder.Entity("ElectionAppServer.Model.Division", b =>
                {
                    b.HasOne("ElectionAppServer.Model.Province", "Province")
                        .WithMany("Divisions")
                        .HasForeignKey("ProvinceId")
                        .HasConstraintName("FK_Divisions_Provinces")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Province");
                });

            modelBuilder.Entity("ElectionAppServer.Model.NationalAssemblyHalka", b =>
                {
                    b.HasOne("ElectionAppServer.Model.District", "District")
                        .WithMany("NationalAssemblyHalkas")
                        .HasForeignKey("DistrictId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("District");
                });

            modelBuilder.Entity("ElectionAppServer.Model.ProvincialAssemblyHalka", b =>
                {
                    b.HasOne("ElectionAppServer.Model.District", "District")
                        .WithMany("ProvincialAssemblyHalkas")
                        .HasForeignKey("DistrictId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("District");
                });

            modelBuilder.Entity("ElectionAppServer.Model.Town", b =>
                {
                    b.HasOne("ElectionAppServer.Model.District", "District")
                        .WithMany("Towns")
                        .HasForeignKey("DistrictId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("District");
                });

            modelBuilder.Entity("ElectionAppServer.Model.UnionCouncil", b =>
                {
                    b.HasOne("ElectionAppServer.Model.Town", "Town")
                        .WithMany("UnionCouncils")
                        .HasForeignKey("TownId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Town");
                });

            modelBuilder.Entity("ElectionAppServer.Model.Ward", b =>
                {
                    b.HasOne("ElectionAppServer.Model.UnionCouncil", "UnionCouncil")
                        .WithMany("Wards")
                        .HasForeignKey("UnionCouncilId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("UnionCouncil");
                });

            modelBuilder.Entity("ElectionAppServer.Model.ApplicationUser", b =>
                {
                    b.Navigation("UserConstituencies");
                });

            modelBuilder.Entity("ElectionAppServer.Model.AssemblyType", b =>
                {
                    b.Navigation("ElectionAssemblies");
                });

            modelBuilder.Entity("ElectionAppServer.Model.Candidate", b =>
                {
                    b.Navigation("CandidateDistricts");

                    b.Navigation("CandidtePoliticalCareers");

                    b.Navigation("Nominations");

                    b.Navigation("PartyAffiliations");

                    b.Navigation("ResultLogDetails");
                });

            modelBuilder.Entity("ElectionAppServer.Model.Caste", b =>
                {
                    b.Navigation("Candidates");
                });

            modelBuilder.Entity("ElectionAppServer.Model.Coalition", b =>
                {
                    b.Navigation("PartyCoalitions");
                });

            modelBuilder.Entity("ElectionAppServer.Model.Education", b =>
                {
                    b.Navigation("Candidates");
                });

            modelBuilder.Entity("ElectionAppServer.Model.Election", b =>
                {
                    b.Navigation("CandidateDistricts");

                    b.Navigation("ElectionPhases");

                    b.Navigation("PartyCoalitions");

                    b.Navigation("Structures");
                });

            modelBuilder.Entity("ElectionAppServer.Model.ElectionAssembly", b =>
                {
                    b.Navigation("Constituencies");

                    b.Navigation("NamnigarConstituencies");

                    b.Navigation("Nominations");

                    b.Navigation("PannelNominations");

                    b.Navigation("ResultLogs");
                });

            modelBuilder.Entity("ElectionAppServer.Model.ElectionPhase", b =>
                {
                    b.Navigation("Nominations");

                    b.Navigation("PannelNominations");

                    b.Navigation("ResultLogs");
                });

            modelBuilder.Entity("ElectionAppServer.Model.Language", b =>
                {
                    b.Navigation("Candidates");
                });

            modelBuilder.Entity("ElectionAppServer.Model.Namanigar", b =>
                {
                    b.Navigation("AsNamanigarResultLogs");

                    b.Navigation("NamanigarConstituencies");

                    b.Navigation("NamanigarResults");
                });

            modelBuilder.Entity("ElectionAppServer.Model.Nomination", b =>
                {
                    b.Navigation("NamanigarResult");

                    b.Navigation("NominationPanelists");

                    b.Navigation("PSResults");
                });

            modelBuilder.Entity("ElectionAppServer.Model.Party", b =>
                {
                    b.Navigation("Nominations");

                    b.Navigation("PartyCoalitions");
                });

            modelBuilder.Entity("ElectionAppServer.Model.Profession", b =>
                {
                    b.Navigation("Candidates");
                });

            modelBuilder.Entity("ElectionAppServer.Model.ResultLog", b =>
                {
                    b.Navigation("ResultLogDetails");
                });

            modelBuilder.Entity("ElectionAppServer.Model.SeatType", b =>
                {
                    b.Navigation("NominationPanelists");

                    b.Navigation("Nominations");

                    b.Navigation("PannelNominations");

                    b.Navigation("ResultLogs");
                });

            modelBuilder.Entity("ElectionAppServer.Model.Structure", b =>
                {
                    b.Navigation("NamanigarConstituencies");

                    b.Navigation("Nominations");

                    b.Navigation("PollingStations");

                    b.Navigation("PreviousStructureMappings");

                    b.Navigation("ResultLogs");

                    b.Navigation("StructureMappings");

                    b.Navigation("UserConstituencies");
                });

            modelBuilder.Entity("ElectionAppServer.Model.Structures.PollingStation", b =>
                {
                    b.Navigation("PSResults");
                });

            modelBuilder.Entity("ElectionAppServer.Model.Structures.Region", b =>
                {
                    b.Navigation("Districts");
                });

            modelBuilder.Entity("ElectionAppServer.Model.Symbol", b =>
                {
                    b.Navigation("Nominations");

                    b.Navigation("Parties");
                });

            modelBuilder.Entity("ElectionAppServer.Model.District", b =>
                {
                    b.Navigation("CandidateDistricts");

                    b.Navigation("NationalAssemblyHalkas");

                    b.Navigation("ProvincialAssemblyHalkas");

                    b.Navigation("Towns");
                });

            modelBuilder.Entity("ElectionAppServer.Model.Division", b =>
                {
                    b.Navigation("Districts");
                });

            modelBuilder.Entity("ElectionAppServer.Model.Province", b =>
                {
                    b.Navigation("Divisions");

                    b.Navigation("Regions");
                });

            modelBuilder.Entity("ElectionAppServer.Model.Town", b =>
                {
                    b.Navigation("UnionCouncils");
                });

            modelBuilder.Entity("ElectionAppServer.Model.UnionCouncil", b =>
                {
                    b.Navigation("Wards");
                });
#pragma warning restore 612, 618
        }
    }
}
