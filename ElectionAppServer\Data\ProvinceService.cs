﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ElectionAppServer.Model;
using Microsoft.EntityFrameworkCore;

namespace ElectionAppServer.Data;

public class ProvinceService
{
    private readonly IDbContextFactory<ApplicationDbContext>
        _contextFactory; // private  readonly  ApplicationDbContext dc;

    public ProvinceService(IDbContextFactory<ApplicationDbContext> contextFactory) // (ApplicationDbContext  context)
    {
        _contextFactory = contextFactory; // dc  =  context;
    }

    public Task<List<Province>> GetList()
    {
        using var dc = _contextFactory.CreateDbContext();
        var res = dc.Structures.OfType<Province>().OrderBy(d => d.EnglishTitle).ToList();
        return Task.FromResult(res);
    }

    public Task<Province> CreateProvince(Province obj)
    {
        using var dc = _contextFactory.CreateDbContext();
        dc.Structures.Add(obj);
        dc.SaveChanges();
        return Task.FromResult(obj);
    }

    public Task<Province> UpdateProvince(Province obj)
    {
        using var dc = _contextFactory.CreateDbContext();
        var st = (from aa in dc.Structures.OfType<Province>()
            where aa.Id == obj.Id
            select aa).FirstOrDefault();
        st.UrduTitle = obj.UrduTitle.Trim();
        st.EnglishTitle = obj.EnglishTitle.Trim();
        st.ModifiedBy = obj.ModifiedBy;
        st.ModifiedDate = DateTime.Now;
        dc.SaveChanges();
        return Task.FromResult(obj);
    }

    public async Task<Province> DeleteProvince(int Id)
    {
        using var dc = _contextFactory.CreateDbContext();
        var st = await (from aa in dc.Structures.OfType<Province>()
            where aa.Id == Id
            select aa).FirstOrDefaultAsync();
        dc.Structures.Remove(st);
        await dc.SaveChangesAsync();
        return await Task.FromResult(st);
    }
}