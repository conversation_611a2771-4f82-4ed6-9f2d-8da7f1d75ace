﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ElectionAppServer.Model;
using Microsoft.EntityFrameworkCore;

namespace ElectionAppServer.Data;

public class AssemblyTypeService
{
    private readonly IDbContextFactory<ApplicationDbContext>
        _contextFactory; // private  readonly  ApplicationDbContext dc;

    public AssemblyTypeService(IDbContextFactory<ApplicationDbContext> contextFactory) // (ApplicationDbContext  dc)
    {
        _contextFactory = contextFactory;
    }

    public Task<List<AssemblyType>> GetAll()
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.AssemblyTypes
            orderby aa.EnglishTitle
            select aa).ToList();
        return Task.FromResult(q);
    }

    public Task<AssemblyType> Save(AssemblyType obj, string user)
    {
        using var dc = _contextFactory.CreateDbContext();
        obj.EnglishTitle = obj.EnglishTitle.Trim();
        obj.UrduTitle = obj.UrduTitle.Trim();
        var isExist = (from aa in dc.AssemblyTypes
            where aa.EnglishTitle == obj.EnglishTitle
                  && aa.Id != obj.Id
            select aa).Any();
        if (isExist)
            throw new Exception("Title (English) already exist");
        isExist = (from aa in dc.AssemblyTypes
            where aa.UrduTitle == obj.UrduTitle
                  && aa.Id != obj.Id
            select aa).Any();
        if (isExist)
            throw new Exception("Title (Urdu) already exist");
        if (obj.Id == 0)
        {
            var at = new AssemblyType
            {
                CreatedBy = user,
                CreatedDate = DateTime.Now,
                EnglishTitle = obj.EnglishTitle,
                UrduTitle = obj.UrduTitle
            };
            dc.Add(at);
            dc.SaveChanges();
            obj.Id = at.Id;
            return Task.FromResult(obj);
        }
        else
        {
            var at = (from aa in dc.AssemblyTypes
                where aa.Id == obj.Id
                select aa).FirstOrDefault();
            at.EnglishTitle = obj.EnglishTitle;
            at.UrduTitle = obj.UrduTitle;
            at.ModifiedBy = user;
            at.ModifiedDate = DateTime.Now;
            dc.SaveChanges();
            return Task.FromResult(obj);
        }
    }

    public Task<AssemblyType> Create(AssemblyType lgt)
    {
        using var dc = _contextFactory.CreateDbContext();
        dc.AssemblyTypes.Add(lgt);
        dc.SaveChanges();
        return Task.FromResult(lgt);
    }

    public Task<AssemblyType> Update(AssemblyType lgt)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.AssemblyTypes
            where aa.Id == lgt.Id
            select aa).FirstOrDefault();
        q.EnglishTitle = lgt.EnglishTitle.Trim();
        q.ModifiedBy = lgt.ModifiedBy;
        q.ModifiedDate = lgt.ModifiedDate;
        q.UrduTitle = lgt.UrduTitle.Trim();
        dc.SaveChanges();
        return Task.FromResult(q);
    }

    public Task<string> Delete(int Id)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.AssemblyTypes
            where aa.Id == Id
            select aa).FirstOrDefault();
        dc.AssemblyTypes.Remove(q);
        dc.SaveChanges();
        return Task.FromResult("OK");
    }
}