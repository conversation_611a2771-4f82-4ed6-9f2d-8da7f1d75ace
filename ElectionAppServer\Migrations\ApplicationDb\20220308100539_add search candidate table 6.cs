﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace ElectionAppServer.Migrations.ApplicationDb
{
    public partial class addsearchcandidatetable6 : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateIndex(
                name: "IX_SearchCandidates_CandidateId",
                table: "SearchCandidates",
                column: "CandidateId");

            migrationBuilder.CreateIndex(
                name: "IX_SearchCandidates_ElectionId",
                table: "SearchCandidates",
                column: "ElectionId");

            migrationBuilder.CreateIndex(
                name: "IX_SearchCandidates_EnglishName",
                table: "SearchCandidates",
                column: "EnglishName");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_SearchCandidates_CandidateId",
                table: "SearchCandidates");

            migrationBuilder.DropIndex(
                name: "IX_SearchCandidates_ElectionId",
                table: "SearchCandidates");

            migrationBuilder.DropIndex(
                name: "IX_SearchCandidates_EnglishName",
                table: "SearchCandidates");
        }
    }
}
