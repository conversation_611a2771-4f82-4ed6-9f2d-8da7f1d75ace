﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace ElectionAppServer.Model;

public class Party
{
    public int Id { get; set; }
    public string EnglishTitle { get; set; }
    public string UrduTitle { get; set; }
    public string ShortEnglishTitle { get; set; }
    public string ShortUrduTitle { get; set; }

    //public virtual List<ElectionParty> PartyElections { get; set; }
    public int? SymbolId { get; set; }

    public virtual Symbol Symbol { get; set; }
    public DateTime? DateOfCreation { get; set; }

    [StringLength(1000)] public string Trivia { get; set; }

    [StringLength(200)] public string Leader { get; set; }

    [StringLength(200)] public string Designation { get; set; }

    [StringLength(500)] public string Address { get; set; }

    [StringLength(200)] public string LeaderPicId { get; set; }

    [StringLength(200)] public string CurrentLeader { get; set; }

    [StringLength(200)] public string CurrentLeaderDesignation { get; set; }

    [StringLength(200)] public string CurrentLeaderPicId { get; set; }

    public virtual List<Nomination> Nominations { get; set; }

    public virtual List<PartyCoalition> PartyCoalitions { get; set; }

    //public CandidatePoll? CandidatePoll { get; set; }

    //public virtual List<Candidate> CurrentCandidates { get; set; }

    public bool IsGeneral { get; set; }
    public bool IsGB { get; set; }
    public bool IsAJK { get; set; }
    public bool IsLB { get; set; } // Local Body KPK
    public bool IsLBSindh { get; set; } // Local Body Sindh
    public bool IsLBBalochistan { get; set; } // Local Body Balochistan
    public bool IsLBPunjab { get; set; } // Local Body Punjab

    #region Audit Log Fields

    public DateTime CreatedDate { get; set; } = DateTime.Now;
    public DateTime? ModifiedDate { get; set; }

    [StringLength(450)] public string CreatedBy { get; set; }

    [StringLength(450)] public string ModifiedBy { get; set; }

    public bool IsLBAJK { get; set; }
    public bool IsLBIslamabad { get; set; }

    #endregion Audit Log Fields
}