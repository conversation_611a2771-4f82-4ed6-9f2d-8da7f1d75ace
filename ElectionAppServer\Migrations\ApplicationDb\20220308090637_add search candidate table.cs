﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace ElectionAppServer.Migrations.ApplicationDb
{
    public partial class addsearchcandidatetable : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "SearchCandidates",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    EnglishName = table.Column<string>(type: "varchar(200)", unicode: false, maxLength: 200, nullable: false),
                    UrduName = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    IsFresh = table.Column<bool>(type: "bit", nullable: false),
                    CandidateId = table.Column<int>(type: "int", nullable: false),
                    District = table.Column<string>(type: "varchar(200)", unicode: false, maxLength: 200, nullable: true),
                    Party = table.Column<string>(type: "varchar(200)", unicode: false, maxLength: 200, nullable: true),
                    Nominations = table.Column<string>(type: "varchar(max)", unicode: false, nullable: true),
                    AuditInfo = table.Column<string>(type: "nvarchar(600)", maxLength: 600, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SearchCandidates", x => x.Id);
                });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "SearchCandidates");
        }
    }
}
