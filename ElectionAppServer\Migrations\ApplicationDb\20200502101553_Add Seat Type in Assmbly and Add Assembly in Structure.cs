﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace ElectionAppServer.Migrations.ApplicationDb
{
	public partial class AddSeatTypeinAssmblyandAddAssemblyinStructure : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.AddColumn<int>(
				 name: "AssemblyId",
				 table: "Structures",
				 nullable: true);

			migrationBuilder.AddColumn<int>(
				 name: "ReserveSeats",
				 table: "ElectionAssemblies",
				 nullable: true);

			migrationBuilder.AddColumn<int>(
				 name: "WomenSeats",
				 table: "ElectionAssemblies",
				 nullable: true);

			migrationBuilder.CreateIndex(
				 name: "IX_Structures_AssemblyId",
				 table: "Structures",
				 column: "AssemblyId");

			migrationBuilder.AddForeignKey(
				 name: "FK_Structures_ElectionAssemblies_AssemblyId",
				 table: "Structures",
				 column: "AssemblyId",
				 principalTable: "ElectionAssemblies",
				 principalColumn: "Id",
				 onDelete: ReferentialAction.Restrict);
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropForeignKey(
				 name: "FK_Structures_ElectionAssemblies_AssemblyId",
				 table: "Structures");

			migrationBuilder.DropIndex(
				 name: "IX_Structures_AssemblyId",
				 table: "Structures");

			migrationBuilder.DropColumn(
				 name: "AssemblyId",
				 table: "Structures");

			migrationBuilder.DropColumn(
				 name: "ReserveSeats",
				 table: "ElectionAssemblies");

			migrationBuilder.DropColumn(
				 name: "WomenSeats",
				 table: "ElectionAssemblies");
		}
	}
}
