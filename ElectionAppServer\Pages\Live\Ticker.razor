﻿@page "/ticker22"
@attribute [Authorize]


@inject NavigationManager NavigationManager
@inject UserManager<ApplicationUser> _UserManager
@inject RoleManager<IdentityRole> _RoleManager
@inject AuthenticationStateProvider AuthenticationStateProvider

@inherits OwningComponentBase<ElectionResultsDataService>

@*<PageCaption Title="Live Ticker"></PageCaption>*@
<MudText Typo="Typo.h5">Live Results</MudText>

<section>
    <div class="row" style="direction: rtl;">
        @if (results != null)
        {
            <h3>Total Results: @results.Count()</h3>
            @foreach (var r in results)
            {
                <div class="col-12 urdu-column" style="direction: rtl; text-align: right; background-color: #87cefa; padding: 5px; line-height: 1.7; margin: 5px;">
                    @((MarkupString)r.Ticker)
                </div>
                <hr/>
            }
        }
        else
        {
            <p>wait...</p>
        }
    </div>
</section>