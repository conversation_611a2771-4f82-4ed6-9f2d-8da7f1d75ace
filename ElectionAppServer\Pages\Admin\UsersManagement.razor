﻿@page "/users"
@attribute [Authorize(Roles = "Administrators")]
@inject UserManager<ApplicationUser> _UserManager
@inject RoleManager<IdentityRole> _RoleManager
@inject AuthenticationStateProvider AuthenticationStateProvider
@using FilterType = Syncfusion.Blazor.Grids.FilterType
@using InputType = Syncfusion.Blazor.Inputs.InputType
@using SelectionMode = Syncfusion.Blazor.Grids.SelectionMode
@using SelectionType = Syncfusion.Blazor.Grids.SelectionType
@using ButtonType = MudBlazor.ButtonType
@using Size = MudBlazor.Size
@inherits OwningComponentBase<UserManagementDataService>

<SfToast @ref="ToastObj" Title="Error" Icon="e-meeting">
    <ToastPosition X="Right" Y="Bottom"></ToastPosition>
</SfToast>

<SfDialog Width="1250px" @bind-Visible="@IsSelectConsVisible" @ref="dlgSelectCons" IsModal="true" ShowCloseIcon="true">
    <DialogEvents OnOpen="beforeOpen"></DialogEvents>
    <DialogTemplates>
        <Header>Available Constituencies</Header>
        <Content>
            <MudButton Size="Size.Small" Color="Color.Primary" Variant="Variant.Filled"
                       StartIcon="@Icons.Material.Filled.Add" OnClick="AddToUserConsList" Disabled="@(!CanAdd)">
                Add
            </MudButton>
            <SfGrid DataSource="@AvailableConstList" AllowSorting="true"
                    Height="400px"
                    EnableVirtualization="true"
                    AllowSelection="true"
                    AllowFiltering="true" @ref="AvailableConsGrid" AllowTextWrap="true">
                <GridEvents QueryCellInfo="CustomizeCell" TValue="ConstituencyDTO"></GridEvents>
                <GridFilterSettings Type="FilterType.Excel"></GridFilterSettings>
                <GridSelectionSettings Mode="SelectionMode.Row"
                                       Type="SelectionType.Multiple">
                </GridSelectionSettings>
                <GridColumns>
                    <GridColumn HeaderText="" Width="60px" Field="IsSelected">
                        <Template Context="usrcon">
                            @{
                                var cc = usrcon as ConstituencyDTO;
                                <SfCheckBox @bind-Checked="cc.IsSelected"></SfCheckBox>
                            }
                        </Template>
                    </GridColumn>
                    <GridColumn AutoFit="true" HeaderText="type" Field="Type"></GridColumn>
                    <GridColumn AutoFit="true" HeaderText="Assembly" Field="Assembly"></GridColumn>
                    <GridColumn AutoFit="true" HeaderText="Code" Field="Code"></GridColumn>
                    <GridColumn AutoFit="true" HeaderText="UrduTitle" Field="UrduTitle" TextAlign="TextAlign.Right"></GridColumn>
                    <GridColumn AutoFit="true" HeaderText="Province" Field="Province"></GridColumn>
                    @*<GridColumn AutoFit="true" HeaderText="Division" Field="Division"></GridColumn>*@
                    <GridColumn AutoFit="true" HeaderText="District" Field="District"></GridColumn>
                    <GridColumn AutoFit="true" Visible="state.state.ElectionType == ElectionType.LocalBody" HeaderText="Town" Field="Town"></GridColumn>
                    <GridColumn AutoFit="true" Visible="state.state.ElectionType == ElectionType.LocalBody" HeaderText="UC" Field="UC"></GridColumn>
                </GridColumns>
            </SfGrid>
        </Content>
    </DialogTemplates>
</SfDialog>

<SfDialog Width="1300px" @bind-Visible="@IsUserConsVisible" @ref="dlgUserCons" IsModal="true" ShowCloseIcon="true">
    <DialogEvents OnOpen="beforeOpen"></DialogEvents>
    <DialogTemplates>
        <Header>User's Constituencies</Header>
        <Content>
            <div class="mb-2">
                <MudButton Color="Color.Primary" Size="Size.Small" Variant="Variant.Filled"
                           ButtonType="ButtonType.Button"
                           OnClick="AssignCons"
                           StartIcon="@Icons.Material.Filled.CheckCircle">
                    Assign
                </MudButton>
                <MudButton Color="Color.Error" Size="Size.Small" Variant="Variant.Filled"
                           ButtonType="ButtonType.Button"
                           OnClick="RemoveCons"
                           StartIcon="@Icons.Material.Filled.Remove">
                    Remove
                </MudButton>
            </div>
            @*<MudButton Size="MudBlazor.Size.Small" Variant="MudBlazor.Variant.Filled" Color="Color.Primary" OnClick="OpenSelectConsDialog">Select Constituencies</MudButton>*@
            @*<MudButton Size="MudBlazor.Size.Small" Variant="MudBlazor.Variant.Filled" Color="Color.Error" OnClick="RemoveSelectedUserCons" Disabled="@(!CanRemove)">Remove</MudButton>*@
            <SfGrid DataSource="@UserConstList" AllowSorting="true"
                    AllowFiltering="true" @ref="UserConsGrid" AllowTextWrap="true"
                    Height="400px"
                    AllowSelection="true">
                @*<GridEvents QueryCellInfo="CustomizeCell" TValue="ConstituencyDTO"></GridEvents>*@
                <GridFilterSettings Type="FilterType.Excel"></GridFilterSettings>
                <GridSelectionSettings Mode="SelectionMode.Row"
                                       Type="SelectionType.Multiple">
                </GridSelectionSettings>
                <GridPageSettings PageSize="20"></GridPageSettings>
                <GridColumns>

                    <GridColumn AutoFit="true" HeaderText="Assembly" Field="@nameof(ConstituencyDTO.Type)"></GridColumn>
                    @*<GridColumn AutoFit="true" HeaderText="Assembly" Field="Assembly"></GridColumn>*@
                    <GridColumn AutoFit="true" HeaderText="Code" Field="@nameof(ConstituencyDTO.Code)"></GridColumn>
                    <GridColumn AutoFit="true" HeaderText="Constituency" Field="@nameof(ConstituencyDTO.EnglishTitle)"></GridColumn>
                    @*<GridColumn AutoFit="true" HeaderText="UrduTitle" Field="UrduTitle" TextAlign="Syncfusion.Blazor.Grids.TextAlign.Right"></GridColumn>*@
                    <GridColumn AutoFit="true" HeaderText="Status" Field="@nameof(ConstituencyDTO.Status)"></GridColumn>
                    <GridColumn AutoFit="true" HeaderText="Province" Field="@nameof(ConstituencyDTO.Province)"></GridColumn>
                    @*<GridColumn AutoFit="true" HeaderText="Division" Field="Division"></GridColumn>*@
                    <GridColumn AutoFit="true" HeaderText="District" Field="@nameof(ConstituencyDTO.District)"></GridColumn>
                    <GridColumn AutoFit="true" HeaderText="Region" Field="@nameof(ConstituencyDTO.Region)"></GridColumn>
                    @*<GridColumn AutoFit="true" Visible="state.state.ElectionType == ElectionType.LocalBody" HeaderText="Town" Field="Town"></GridColumn>*@
                    @*<GridColumn AutoFit="true" Visible="state.state.ElectionType == ElectionType.LocalBody" HeaderText="UC" Field="UC"></GridColumn>*@
                </GridColumns>
            </SfGrid>
        </Content>
    </DialogTemplates>
</SfDialog>
<SfDialog Width="800px" @bind-Visible="@ShowPopup" @ref="dlgForm" IsModal="true" ShowCloseIcon="true">
    <DialogEvents OnOpen="beforeOpen"></DialogEvents>
    <DialogTemplates>
        <Header>User Information</Header>
        <Content>
            <EditForm Model="@objUser" OnValidSubmit="@SaveUser" Context="usrForm">
                <DataAnnotationsValidator/>
                <ValidationSummary></ValidationSummary>
                <div class="row mb-2">
                    <div class="col-6">
                        <SfTextBox Enabled="@(string.IsNullOrEmpty(objUser.Id))" Placeholder="Email" Autocomplete="AutoComplete.Off" FloatLabelType="FloatLabelType.Always" @bind-Value="objUser.UserNameId"></SfTextBox>
                        <ValidationMessage For="@(() => objUser.UserNameId)"/>
                    </div>
                    <div class="col-6">
                        <SfTextBox Type="InputType.Password" @bind-Value="objUser.PasswordHash" Placeholder="Password" FloatLabelType="FloatLabelType.Always"></SfTextBox>
                        <ValidationMessage For="@(() => objUser.PasswordHash)"/>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-3">
                        <SfTextBox Placeholder="Code" FloatLabelType="FloatLabelType.Always" @bind-Value="objUser.Code"></SfTextBox>
                        <ValidationMessage For="@(() => objUser.Code)"/>
                    </div>
                    <div class="col-3">
                        <SfTextBox Placeholder="Employee Code" FloatLabelType="FloatLabelType.Always" @bind-Value="objUser.EmpCode"></SfTextBox>
                        <ValidationMessage For="@(() => objUser.EmpCode)"/>
                    </div>
                    <div class="col">
                        <SfTextBox Placeholder="Name" FloatLabelType="FloatLabelType.Always" @bind-Value="objUser.FullName"></SfTextBox>
                        <ValidationMessage For="@(() => objUser.FullName)"/>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-6">
                        <SfTextBox Placeholder="Phone" FloatLabelType="FloatLabelType.Always" @bind-Value="objUser.PhoneNumber"></SfTextBox>
                        <ValidationMessage For="@(() => objUser.PhoneNumber)"/>
                    </div>
                    <div class="col-6">
                        <SfComboBox TItem="GenderCls" TValue="int?" @bind-Value="objUser.Gender" Placeholder="Select Gender" DataSource="@gender_list" FloatLabelType="FloatLabelType.Always">
                            <ComboBoxFieldSettings Value="ID" Text="Text"></ComboBoxFieldSettings>
                        </SfComboBox>
                        <ValidationMessage For="@(() => objUser.Gender)"/>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col">
                        <SfTextBox Multiline="true" @bind-Value="objUser.Address" FloatLabelType="FloatLabelType.Always" Placeholder="Address"></SfTextBox>
                        <ValidationMessage For="@(() => objUser.Address)"/>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col">
                        <SfTextBox @bind-Value="objUser.District" FloatLabelType="FloatLabelType.Always" Placeholder="District"></SfTextBox>
                        <ValidationMessage For="@(() => objUser.District)"/>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col" style="display: flex; flex-wrap: wrap; gap: 10px;">
                        @foreach (var r in UserRoles)
                        {
                            @*<SfCheckBox @bind-Checked="r.IsSelected" Label="@r.Text"></SfCheckBox>*@
                            <div>
                                <InputCheckbox @bind-Value="@r.IsSelected"></InputCheckbox> <label style="padding-left: 5px; padding-right: 15px;">@r.Text</label>
                            </div>
                        }
                    </div>
                </div>

                @if (objUser.Id != "")
                {
                    <div class="row mb-3">
                        <div class="col">
                            <span>User Status: <b>@(objUser.IsActive ? "Active" : "In-active")</b></span>
                        </div>
                    </div>
                }
                <div class="row bm-3">
                    <div class="col">
                        <MudButton ButtonType="ButtonType.Submit" Color="Color.Primary" Size="Size.Small" Variant="Variant.Filled" StartIcon="@Icons.Material.Filled.Save">Save</MudButton>
                        @*@if (objUser.Id != "")
                            {
                            <SfButton CssClass="e-danger"  OnClick="DeleteUser">Delete</SfButton>
                            }*@
                        <span style="color:red">@strError</span>
                    </div>
                </div>
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>
<AuthorizeView>
    <Authorized>
        @if (context.User.IsInRole(ADMINISTRATION_ROLE))
        {
            <MudText Typo="Typo.h5">Accounts Management</MudText>
            <section>
                <div class="mb-1">
                    <MudButton Size="Size.Small" Variant="Variant.Filled" Color="Color.Primary" OnClick="AddNewUserAsync">Add User</MudButton>
                </div>
                @if (ColUsers != null && ColUsers.Any())
                {
                    <SfGrid AllowFiltering="true" DataSource="ColUsers"
                            EnableVirtualization="true"
                            AllowSorting="true" @ref="UserGrid" AllowTextWrap="true" Height="calc(100vh - 205px)" Width="100%"
                            FrozenColumns="1">
                        <GridFilterSettings Type="FilterType.Excel"></GridFilterSettings>
                        <GridColumns>
                            <GridColumn AutoFit="true" HeaderText="Email" Field=@nameof(ApplicationUserDTO.UserNameId)/>

                            <GridColumn AutoFit="true" HeaderText="Name" Field=@nameof(ApplicationUserDTO.FullName)></GridColumn>
                            <GridColumn AutoFit="true" HeaderText="Phone" Field=@nameof(ApplicationUserDTO.PhoneNumber)></GridColumn>

                            <GridColumn AutoFit="true" HeaderText="District" Field=@nameof(ApplicationUserDTO.District)></GridColumn>
                            <GridColumn AutoFit="true" HeaderText="Roles" Field=@nameof(ApplicationUserDTO.Roles)></GridColumn>
                            <GridColumn AutoFit="true" HeaderText="Status" Field=@nameof(ApplicationUserDTO.Status)></GridColumn>
                            <GridColumn Width="400px" HeaderText="Actions" TextAlign="TextAlign.Center">
                                <Template Context="ar">
                                    @{
                                        var obj = ar as ApplicationUserDTO;
                                        @if (obj.Status == "Active")
                                        {
                                            <MudButton Size="Size.Small" Variant="Variant.Filled" Color="Color.Primary" Disabled="@(!obj.IsNamanigar)" Class="mb-1" OnClick="@(() => GetUserCons(obj))" StartIcon="@Icons.Material.Filled.Map">Constituencies</MudButton>
                                            <MudFab Size="Size.Small" Color="Color.Primary" StartIcon="@Icons.Material.Filled.Edit" Class="mb-1" OnClick="@(() => EditUser(obj))"></MudFab>
                                            <MudFab Size="Size.Small" Color="Color.Error" StartIcon="@Icons.Material.Filled.Delete" Class="e-danger mb-1" OnClick="@(() => DeleteUser(obj.Id, obj.FullName))"></MudFab>
                                            <MudFab Size="Size.Small" Color="Color.Warning" StartIcon="@Icons.Material.Filled.Block" Class="e-danger mb-1" OnClick="@(() => DisableAccount(obj.Id, obj.FullName))"></MudFab>
                                        }
                                        else
                                        {
                                            <MudFab Size="Size.Small" Color="Color.Success" StartIcon="@Icons.Material.Filled.Undo" Class="mb-1" OnClick="@(() => EnableAccount(obj.Id, obj.FullName))"></MudFab>
                                        }
                                    }
                                </Template>
                            </GridColumn>
                        </GridColumns>
                    </SfGrid>
                }
                else
                {
                    <p>Please wait....</p>
                }

            </section>
        }
        else
        {
            <p>You're not signed in as a user in @ADMINISTRATION_ROLE.</p>
        }
    </Authorized>
    <NotAuthorized>
        <p>You're not logged in.</p>
    </NotAuthorized>
</AuthorizeView>

@code {

}