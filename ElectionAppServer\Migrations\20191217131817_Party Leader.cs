﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace ElectionAppServer.Migrations
{
	public partial class PartyLeader : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.AlterColumn<string>(
				 name: "Trivia",
				 table: "Parties",
				 maxLength: 1000,
				 nullable: true,
				 oldClrType: typeof(string),
				 oldType: "nvarchar(max)",
				 oldNullable: true);

			migrationBuilder.AddColumn<string>(
				 name: "Address",
				 table: "Parties",
				 maxLength: 500,
				 nullable: true);

			migrationBuilder.AddColumn<string>(
				 name: "Designation",
				 table: "Parties",
				 maxLength: 200,
				 nullable: true);

			migrationBuilder.AddColumn<string>(
				 name: "Leader",
				 table: "Parties",
				 maxLength: 200,
				 nullable: true);
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropColumn(
				 name: "Address",
				 table: "Parties");

			migrationBuilder.DropColumn(
				 name: "Designation",
				 table: "Parties");

			migrationBuilder.DropColumn(
				 name: "Leader",
				 table: "Parties");

			migrationBuilder.AlterColumn<string>(
				 name: "Trivia",
				 table: "Parties",
				 type: "nvarchar(max)",
				 nullable: true,
				 oldClrType: typeof(string),
				 oldMaxLength: 1000,
				 oldNullable: true);
		}
	}
}
