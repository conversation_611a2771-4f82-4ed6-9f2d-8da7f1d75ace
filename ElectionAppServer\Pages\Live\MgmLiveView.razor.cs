﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ElectionAppServer.DTO;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.SignalR.Client;
using Syncfusion.Blazor.DropDowns;
using Syncfusion.Blazor.Grids;
using Syncfusion.Blazor.Popups;
using BeforeOpenEventArgs = Syncfusion.Blazor.Popups.BeforeOpenEventArgs;

namespace ElectionAppServer.Pages.Live;

public partial class MgmLiveView
{
    private List<GeneralItemDTO> DistrictList = new();
    private SfDialog dlgForm;
    private List<ResultDTO> filterResults = new();
    private SfGrid<ResultDTO> grid;
    private List<GeneralItemDTO> ProvinceList = new();
    private List<ResultDTO> results = new();
    private int? selectedDistrict;
    private int? selectedProvince;
    private HubConnection hubConnection { get; set; }
    private bool isDlgVisible { get; set; }
    private LiveResultDetailDTO detail { get; set; }


    [CascadingParameter] private Task<AuthenticationState> authenticationStateTask { get; set; }

    protected override async Task OnInitializedAsync()
    {
        isDlgVisible = false;
        detail = new LiveResultDetailDTO { Nominations = new List<LiveResultCandidateDetailDTO>() };

        hubConnection = new HubConnectionBuilder()
            .WithUrl(NavigationManager.ToAbsoluteUri("/resultshub"))
            .Build();

        hubConnection.On<ResultDTO>("ReceiveResult", async result =>
        {
            await dlgForm.HideAsync();
            //var ok = result;
            //results.Add(result);
            var isExist = (from r in results
                where r.ElectionId == result.ElectionId &&
                      r.AssemblyId == result.AssemblyId &&
                      r.ConstituencyId == result.ConstituencyId &&
                      r.SeatTypeId == result.SeatTypeId
                select r).FirstOrDefault();
            if (isExist != null)
            {
                isExist.WinnerCandidateId = result.WinnerCandidateId;
                isExist.WinnerName = result.WinnerName;
                isExist.WinnerParty = result.WinnerParty;
                isExist.WinnerVotes = result.WinnerVotes;

                isExist.RunnerUpCandidateId = result.RunnerUpCandidateId;
                isExist.RunnerUpName = result.RunnerUpName;
                isExist.RunnerUpParty = result.RunnerUpParty;
                isExist.RunnerUpVotes = result.RunnerUpVotes;
                isExist.OnAir = result.OnAir;
                isExist.Status = result.Status;
                isExist.LastUpdatedOn = result.LastUpdatedOn;
            }
            else
            {
                if (result.PhaseId == state.state.PhaseId)
                    results.Add(result);
            }

            results = (from r in results
                orderby r.Status, r.LastUpdatedOn descending
                select r).ToList();

            ApplyFilter();

            try
            {
                await grid.Refresh();
            }
            catch (Exception)
            {
                // ignored
            }

            StateHasChanged();
        });

        hubConnection.On<ResultDTO>("ReceiveNotification", async result =>
        {
            var isExist = (from r in results
                where r.ElectionId == result.ElectionId &&
                      r.AssemblyId == result.AssemblyId &&
                      r.ConstituencyId == result.ConstituencyId &&
                      r.SeatTypeId == result.SeatTypeId
                select r).FirstOrDefault();

            results = (from r in results
                orderby r.OnAir descending, r.LastUpdatedOn descending
                select r).ToList();
            try
            {
                await grid.Refresh();
            }
            catch (Exception)
            {
                // ignored
            }

            StateHasChanged();
        });

        await hubConnection.StartAsync();


        try
        {
            await grid.Refresh();
        }
        catch (Exception)
        {
            // ignored
        }

        StateHasChanged();
        await Task.CompletedTask;
    }

    private async Task DisplayDetail(int assemblyId, int constituencyId, int seatTypeId)
    {
        detail = await Service.GetLiveResultDetail(state.state.ElectionId, assemblyId, state.state.PhaseId,
            constituencyId, seatTypeId);
        await dlgForm.ShowAsync();
    }

    //private async Task MarkPublish(int electionId, int phaseId, int assemblyId, int constituencyId, int seatTypeId)
    //{
    //	var user = (await authenticationStateTask).User;
    //	var msg = await Service.MarkPublish(electionId, phaseId, assemblyId, constituencyId, seatTypeId, user.Identity.Name);
    //	if (msg == "OK")
    //	{
    //		var res = (from r in results
    //					  where r.ConstituencyId == constituencyId &&
    //							  r.ElectionId == electionId &&
    //							  r.AssemblyId == assemblyId &&
    //							  r.PhaseId == phaseId
    //					  select r).FirstOrDefault();
    //		//res.Status = "Yes";
    //		res.OnAir = true;
    //		res.Status = "Yes";

    //		results = (from aa in results
    //					  orderby aa.Status, aa.LastUpdatedOn descending
    //					  select aa).ToList();
    //		var allowToPush = await confreader.GetSettingValue("AllowToPush");
    //		if (allowToPush == "Yes")
    //		{
    //			ImmResultDTO immInfo = await Service.GetImmResult(electionId, phaseId, assemblyId, constituencyId, seatTypeId);
    //			await Translation.PostData(immInfo);
    //		}
    //	}

    //	StateHasChanged();
    //}

    private void beforeOpen(BeforeOpenEventArgs args)
    {
        args.MaxHeight = null;
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            var user = (await authenticationStateTask).User;
            if (user.Identity.IsAuthenticated)
            {
                var dd = await localStorage.GetAsync<ElectionStateDTO>(await confreader.GetSettingValue("electionkey"));
                state.SetState(dd.Value);
                var info = await _UserManager.GetUserAsync(user);
                ProvinceList = await Service.GetProvinces(dd.Value.ElectionId);
                results = await Service.GetLiveResults(state.state.PhaseId);
                ApplyFilter();
                await Task.CompletedTask;

                StateHasChanged();
            }
            else
            {
                try
                {
                    await localStorage.DeleteAsync(await confreader.GetSettingValue("electionkey"));
                    state.SetState(new ElectionStateDTO
                        { ElectionId = 0, ElectionTitle = "", PhaseId = 0, PhaseTitle = "" });
                    StateHasChanged();
                }
                catch (Exception)
                {
                    // ignored
                }
            }
        }
    }

    public async Task OnProvinceChange(ChangeEventArgs<int?, GeneralItemDTO> args)
    {
        selectedProvince = args.Value;
        DistrictList = await Service.GetDistricts(selectedProvince, state.state.ElectionId);
        ApplyFilter();
    }

    private void ApplyFilter()
    {
        filterResults = (from aa in results
            where (selectedProvince == null || aa.ProvinceId == selectedProvince) &&
                  (selectedDistrict == null || aa.DistrictId == selectedDistrict)
            select aa).ToList();
    }

    public async Task OnDistrictChange(ChangeEventArgs<int?, GeneralItemDTO> args)
    {
        selectedDistrict = args.Value;
        ApplyFilter();
        await Task.CompletedTask;
    }
}