﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace ElectionAppServer.Migrations
{
	public partial class ElectionStructure : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.AddColumn<int>(
				 name: "ElectionId",
				 table: "Structures",
				 nullable: true);

			migrationBuilder.CreateIndex(
				 name: "IX_Structures_ElectionId",
				 table: "Structures",
				 column: "ElectionId");

			migrationBuilder.AddForeignKey(
				 name: "FK_Structures_Elections_ElectionId",
				 table: "Structures",
				 column: "ElectionId",
				 principalTable: "Elections",
				 principalColumn: "Id",
				 onDelete: ReferentialAction.Restrict);
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropForeignKey(
				 name: "FK_Structures_Elections_ElectionId",
				 table: "Structures");

			migrationBuilder.DropIndex(
				 name: "IX_Structures_ElectionId",
				 table: "Structures");

			migrationBuilder.DropColumn(
				 name: "ElectionId",
				 table: "Structures");
		}
	}
}
