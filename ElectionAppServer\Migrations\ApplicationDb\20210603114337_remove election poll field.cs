﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace ElectionAppServer.Migrations.ApplicationDb
{
	public partial class removeelectionpollfield : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropColumn(
				 name: "CandidatePoll",
				 table: "Parties");

			migrationBuilder.AddColumn<bool>(
				 name: "IsAJK",
				 table: "Parties",
				 type: "bit",
				 nullable: false,
				 defaultValue: false);

			migrationBuilder.AddColumn<bool>(
				 name: "IsGB",
				 table: "Parties",
				 type: "bit",
				 nullable: false,
				 defaultValue: false);

			migrationBuilder.AddColumn<bool>(
				 name: "IsGeneral",
				 table: "Parties",
				 type: "bit",
				 nullable: false,
				 defaultValue: false);

			migrationBuilder.AddColumn<bool>(
				 name: "IsLB",
				 table: "Parties",
				 type: "bit",
				 nullable: false,
				 defaultValue: false);
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropColumn(
				 name: "IsAJK",
				 table: "Parties");

			migrationBuilder.DropColumn(
				 name: "IsGB",
				 table: "Parties");

			migrationBuilder.DropColumn(
				 name: "IsGeneral",
				 table: "Parties");

			migrationBuilder.DropColumn(
				 name: "IsLB",
				 table: "Parties");

			migrationBuilder.AddColumn<int>(
				 name: "CandidatePoll",
				 table: "Parties",
				 type: "int",
				 nullable: true);
		}
	}
}
