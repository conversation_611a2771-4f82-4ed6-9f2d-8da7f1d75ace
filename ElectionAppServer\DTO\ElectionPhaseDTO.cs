﻿using System;
using System.ComponentModel.DataAnnotations;

namespace ElectionAppServer.DTO;

public class ElectionPhaseDTO
{
    public int ElectionId { get; set; }

    [Required(ErrorMessage = "Title for election phase is required")]
    [StringLength(200)]
    public string Title { get; set; }

    [Required(ErrorMessage = "Election Date is required")]
    public DateTime ElectionDate { get; set; }
}