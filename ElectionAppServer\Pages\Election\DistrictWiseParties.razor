﻿@page "/districtwiseparies"
@attribute [Authorize(Roles = "Administrators")]

@using FilterType = Syncfusion.Blazor.DropDowns.FilterType
@inherits OwningComponentBase<DistrictWisePartyService>

<div style="padding: 10px;">


    <div class="row">
        <div class="col">
            <h3>District wise Parties</h3>
        </div>
    </div>
    <div class="row">
        <div class="col-md-8">
            @if (DistrictsList != null && DistrictsList.Any())
            {
                <SfDropDownList TItem="GeneralItemDTO" TValue="int?" @bind-Value="DistrictId" Placeholder="District" DataSource="@DistrictsList" FloatLabelType="FloatLabelType.Always" AllowFiltering="true" FilterType="FilterType.Contains">
                    <DropDownListFieldSettings Value="Id" Text="EnglishTitle"></DropDownListFieldSettings>
                </SfDropDownList>
            }
        </div>
        <div class="col-md-3">
            @if (SeatTypesList != null && SeatTypesList.Any())
            {
                <SfDropDownList TItem="GeneralItemDTO" TValue="int?" @bind-Value="SeatTypeId" Placeholder="Seat Type" DataSource="@SeatTypesList" FloatLabelType="FloatLabelType.Always" AllowFiltering="true" FilterType="FilterType.Contains">
                    <DropDownListFieldSettings Value="Id" Text="EnglishTitle"></DropDownListFieldSettings>
                </SfDropDownList>
            }
        </div>
        <div class="col-md-1">
            <SfButton CssClass="e-primary" OnClick="GetCurrentPartiesSortOrder">Search</SfButton>
        </div>
    </div>
    <div class="row">
        <div class="col">
            @if (DistrictParties != null && DistrictParties.Any())
            {
                <SfGrid DataSource="DistrictParties" Width="100%" AllowTextWrap="true">
                    <GridColumns>
                        <GridColumn HeaderText="Id" Field=@nameof(DistrictPartyDto.Id) AutoFit="true"></GridColumn>
                        <GridColumn HeaderText="Party" Field=@nameof(DistrictPartyDto.Party)></GridColumn>
                        <GridColumn HeaderText="Sort Order" Field=@nameof(DistrictPartyDto.SortOrder) AutoFit="true"></GridColumn>
                        <GridColumn>
                            <Template Context="dp">
                                @{
                                    var obj = dp as DistrictPartyDto;
                                    <SfButton OnClick="@(() => MoveUp(obj.Id))" CssClass="e-primary">Up</SfButton>
                                    <SfButton OnClick="@(() => MoveDown(obj.Id))" CssClass="e-info">down</SfButton>
                                }
                            </Template>
                        </GridColumn>
                    </GridColumns>
                </SfGrid>
            }
        </div>
    </div>
</div>