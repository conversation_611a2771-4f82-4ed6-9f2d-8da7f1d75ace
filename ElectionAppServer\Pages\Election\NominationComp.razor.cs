﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ElectionAppServer.DTO;
using ElectionAppServer.Model;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Syncfusion.Blazor.DropDowns;
using Syncfusion.Blazor.Notifications;
using Syncfusion.Blazor.Popups;
using BeforeOpenEventArgs = Syncfusion.Blazor.Popups.BeforeOpenEventArgs;

namespace ElectionAppServer.Pages.Election;

public partial class NominationComp
{
    private List<GeneralItemDTO> assembliesList = new();
    private int assemblyId;
    private List<SearchCandidateDTO> candListAll = new();
    private List<SearchCandidateDTO> candListFilter = new();

    private ConstDetailDTO constDetail;

    private List<GeneralItemDTO> constituenciesList =
        new() { new GeneralItemDTO { Id = 0, EnglishTitle = "Select Constituency" } };

    private int constituencyId;
    private SfDialog dlgForm;
    private SfDialog dlgSeachCand;
    private int electionId;
    private bool isDlgSearchCandVisible;
    private bool isDlgVisible;

    private List<NominationDTO> nominations = new();
    //private List<GeneralItemDTO> candidatesList = new List<GeneralItemDTO>();

    private List<GeneralItemDTO> partiesList = new();
    private int phaseId;
    private string searchText = "";

    private List<GeneralItemDTO> seatTypesList = new()
        { new GeneralItemDTO { Id = 0, EnglishTitle = "Select Seat Type" } };

    private NominationDTO selectedObj = new();

    //private GeneralItemDTO selectedAssembly = null;
    //private GeneralItemDTO selectedConstituency = null;
    private GeneralItemDTO selectedSeatType;
    private List<GeneralItemDTO> symbolsList = new();

    private SfToast ToastObj;
    [CascadingParameter] private Task<AuthenticationState> authenticationStateTask { get; set; }
    private NominationType ElectionBasedOn { get; set; }

    public async Task SetParameters(int electionId, int phaseId, int assemblyId, int constituencyId)
    {
        this.electionId = electionId;
        this.phaseId = phaseId;
        this.assemblyId = assemblyId;
        this.constituencyId = constituencyId;
        partiesList = await Service.GetParties_list(electionId);
        symbolsList = await Service.GetSymbols_list();
        ElectionBasedOn = await Service.GetElectionBasedOn(assemblyId);
        candListAll = await Service.SearchCandidate(electionId, ElectionBasedOn);

        constDetail = Service.GetConstInfo(constituencyId, phaseId, selectedObj.SeatTypeId).Result;
        await FillSeatTpe();
        StateHasChanged();
    }

    //protected override async Task OnAfterRenderAsync(bool firstRender)
    //{
    //	if (firstRender)
    //	{
    //		var user = (await authenticationStateTask).User;
    //		if (user.Identity.IsAuthenticated)
    //		{
    //			var dd = await ProtectedSessionStore.GetAsync<ElectionStateDTO>("election");
    //			state.SetState(dd.Value);
    //			await FillAsm_Phase();
    //			//candidatesList = await Service.GetCandidatesList();
    //			partiesList = await Service.GetParties_list();
    //			symbolsList = await Service.GetSymbols_list();
    //			candListAll = await Service.SearchCandidate();
    //			await Task.CompletedTask;
    //			StateHasChanged();
    //		}

    //	}

    //}

    //public async Task FillAsm_Phase()
    //{
    //	assembliesList = await Service.GetAssmblies(state.state.ElectionId);
    //	//assembliesList.Insert(0, new GeneralItemDTO { Id = 0, EnglishTitle = "Select Assembly" });

    //	//constituenciesList = new List<GeneralItemDTO>() { new GeneralItemDTO { Id = 0, EnglishTitle = "Select Constituency" } };
    //	//seatTypesList = new List<GeneralItemDTO>() { new GeneralItemDTO { Id = 0, EnglishTitle = "Select Seat Type" } };
    //	constituenciesList = new List<GeneralItemDTO>();
    //	seatTypesList = new List<GeneralItemDTO>();
    //	selectedConstituency = null;
    //	selectedSeatType = null;

    //}

    private async Task FillSeatTpe()
    {
        selectedSeatType = null;
        seatTypesList = await Service.GetSeatType(assemblyId, constituencyId);
    }

    //void OnChangeAssembly(GeneralItemDTO assembly)
    //{
    //	if (assembly != null && assembly.Id > 0)
    //	{
    //		selectedAssembly = new GeneralItemDTO { Id = assembly.Id, EnglishTitle = assembly.EnglishTitle };
    //		constituenciesList = Service.GetConstituencies(selectedAssembly.Id).Result;
    //		selectedConstituency = null;
    //		seatTypesList = new List<GeneralItemDTO>();
    //		selectedSeatType = null;
    //		InvokeAsync(StateHasChanged);
    //	}
    //	else
    //	{
    //		selectedAssembly = null;
    //		constituenciesList = new List<GeneralItemDTO>();
    //		seatTypesList = new List<GeneralItemDTO>();
    //		selectedConstituency = null;

    //	}

    //	//CurrentCountry = country;
    //	//CurrentCountryCities = Cities.FindAll(city => city.CountryId == CurrentCountry.Id);
    //	//CurrentCity = CurrentCountryCities[0];
    //	//InvokeAsync(StateHasChanged);
    //}

    private void OnChangeSeatType(GeneralItemDTO st)
    {
        if (st != null)
        {
            selectedSeatType = new GeneralItemDTO { Id = st.Id, EnglishTitle = st.EnglishTitle };
            nominations = Service.GetNominations(assemblyId, constituencyId, selectedSeatType.Id, phaseId).Result;
            constDetail = Service.GetConstInfo(constituencyId, phaseId, selectedObj.SeatTypeId).Result;
        }
        else
        {
            selectedSeatType = null;
            nominations = new List<NominationDTO>();
        }

        InvokeAsync(StateHasChanged);
    }

    private async Task OpenEditForm(NominationDTO obj)
    {
        //selectedObj = new NominationDTO
        //{
        //	Id = obj.Id,
        //	CandidateId = obj.CandidateId,
        //	AssemblyId = obj.AssemblyId,
        //	ElectionId = obj.ElectionId,
        //	PartyId = obj.PartyId,
        //	PhaseId = obj.PhaseId,
        //	SeatTypeId = obj.SeatTypeId,
        //	ConstituencyId = obj.ConstituencyId,
        //	SymbolId = obj.SymbolId,
        //	Weight = obj.Weight,
        //	CandidateURL = await Service.GetCandidateUrlById(obj.CandidateId),
        //	PartyFlagURL = await Service.GetFlagUrlById(obj.PartyId),
        //	SymbolURL = await Service.GetSymbolUrlById(obj.SymbolId)
        //};
        selectedObj = await Service.GetNominationById(obj.Id);
        await dlgForm.ShowAsync();
    }

    private async Task DeleteRecord(int Id)
    {
        var para = new[] { "Are you sure want to delete this nomination?" };
        var res = await JSRuntime.InvokeAsync<bool>("confirm", para);
        if (res == false)
            return;

        try
        {
            await Service.DeleteRecord(Id, state.state.ElectionId);
            await FillNominations();
        }
        catch (Exception ex)
        {
            var mm = new ToastModel
            {
                Title = "Error", Content = ex.Message, CssClass = "e-toast-danger", ShowProgressBar = true,
                Timeout = 5000, ShowCloseButton = true
            };
            await ToastObj.ShowAsync(mm);
        }
    }

    private void beforeOpen(BeforeOpenEventArgs args)
    {
        args.MaxHeight = null;
    }

    public async Task OnCandidateSelect(ChangeEventArgs<int?, GeneralItemDTO> args)

    {
        selectedObj.CandidateId = args.Value;
        await FillCandidateParyAndSymbol();
        selectedObj.CandidateURL = await Service.GetCandidateUrlById(selectedObj.CandidateId);
        selectedObj.PartyFlagURL = await Service.GetSymbolUrlById(selectedObj.PartyId);
        selectedObj.SymbolURL = await Service.GetSymbolUrlById(selectedObj.SymbolId);
        StateHasChanged();
    }

    public async Task OnSymbolChange(ChangeEventArgs<int?, GeneralItemDTO> args)
    {
        selectedObj.SymbolId = args.Value;
        selectedObj.SymbolURL = await Service.GetSymbolUrlById(selectedObj.SymbolId);
        StateHasChanged();
    }

    public async Task OnPartyChange(ChangeEventArgs<int?, GeneralItemDTO> args)
    {
        selectedObj.PartyId = args.Value;
        selectedObj.SymbolId = await Service.GetPartySymbolId((int)selectedObj.PartyId);
        selectedObj.PartyFlagURL = await Service.GetSymbolUrlById(selectedObj.PartyId);
        selectedObj.SymbolURL = await Service.GetSymbolUrlById(selectedObj.SymbolId);
        StateHasChanged();
    }

    private async Task FillCandidateParyAndSymbol()
    {
        if (selectedObj.CandidateId != null)
        {
            selectedObj.PartyId = await Service.GetCandidateCurrentParty((int)selectedObj.CandidateId);
            if (selectedObj.PartyId != null)
                selectedObj.SymbolId = await Service.GetPartySymbolId((int)selectedObj.PartyId);
            else
                selectedObj.SymbolId = null;
        }
        else
        {
            selectedObj.PartyId = null;
            selectedObj.SymbolId = null;
        }

        await Task.CompletedTask;
    }

    private async Task SaveData()
    {
        try
        {
            selectedObj.AssemblyId = assemblyId;
            selectedObj.PhaseId = phaseId;
            selectedObj.ConstituencyId = constituencyId;
            selectedObj.SeatTypeId = selectedSeatType.Id;
            selectedObj.ElectionId = electionId;
            var user = (await authenticationStateTask).User;
            await Service.SaveNomination(selectedObj, user.Identity.Name);
            await FillNominations();
            await dlgForm.HideAsync();
        }
        catch (Exception ex)
        {
            var msg = ex.Message;
            if (ex.InnerException != null)
                msg += "<br>" + ex.InnerException.Message;

            var mm = new ToastModel
            {
                Title = "Error", Content = ex.Message, CssClass = "e-toast-danger", ShowProgressBar = true,
                Timeout = 5000, ShowCloseButton = true
            };
            await ToastObj.ShowAsync(mm);
        }
    }

    private async Task OpenCreateForm()
    {
        selectedObj = new NominationDTO
        {
            Id = 0,
            AssemblyId = assemblyId,
            ElectionId = electionId,
            SeatTypeId = selectedSeatType.Id,
            ConstituencyId = constituencyId,
            PhaseId = phaseId,
            Weight = 0
        };
        await dlgForm.ShowAsync();
    }

    public async Task FillNominations()
    {
        //nominations = new List<NominationDTO>();
        try
        {
            nominations = await Service.GetNominations(assemblyId, constituencyId, selectedSeatType.Id, phaseId);
        }
        catch (Exception)
        {
            nominations = new List<NominationDTO>();
        }
    }

    private async Task OpenSearchCandidate()
    {
        searchText = "";
        await dlgSeachCand.ShowAsync();
        StateHasChanged();
    }

    private async Task SearchCandidate()
    {
        var st = searchText.Trim().ToLower();

        var id = 0;
        try
        {
            id = int.Parse(searchText);
        }
        catch (Exception)
        {
            // ignored
        }

        if (id > 0)
            candListFilter = (from aa in candListAll
                where aa.Id == id
                select aa).ToList();
        else
            candListFilter = (from aa in candListAll
                where aa.EnglishName.ToLower().Contains(st) ||
                      aa.UrduName.ToLower().Contains(st) ||
                      aa.Party.ToLower().Contains(st) ||
                      aa.LastConstituency.ToLower().Contains(st)
                orderby aa.EnglishName
                select aa).ToList();

        StateHasChanged();
        await Task.CompletedTask;
    }

    private async Task SelectCandidate(int candidateId, int? partyId, int? symbolId)
    {
        selectedObj.CandidateId = candidateId;
        selectedObj.PartyId = partyId;
        selectedObj.SymbolId = symbolId;
        //await FillCandidateParyAndSymbol();
        StateHasChanged();
        await dlgSeachCand.HideAsync();
        searchText = "";
        candListFilter = new List<SearchCandidateDTO>();
        await Task.CompletedTask;
    }
}