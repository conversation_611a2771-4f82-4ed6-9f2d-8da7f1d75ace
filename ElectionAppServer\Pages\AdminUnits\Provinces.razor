﻿@page "/administrativeunits/{electionId:int}"
@page "/administrativeunits/{electionId:int}/{cid:int}"

@inject AuthenticationStateProvider AuthenticationStateProvider
@using BeforeOpenEventArgs = Syncfusion.Blazor.Popups.BeforeOpenEventArgs
@using FilterType = Syncfusion.Blazor.Grids.FilterType
@using Size = MudBlazor.Size
@inherits OwningComponentBase<ElectionStructureService>
@inject IJSRuntime JSRuntime
@inject ElectionStateService state
@inject NavigationManager NavigationManager
@attribute [Authorize(Roles = "Administrators,Data Viewer")]

<SfDialog Width="900px" @bind-Visible="@isDlgVisible" @ref="dlgForm" IsModal="true" ShowCloseIcon="true">
    <DialogEvents OnOpen="beforeOpen"></DialogEvents>
    <DialogTemplates>
        <Header>Province</Header>
        <Content>

            <AdminUnitForm SelectedObj="@selectedObj" OnValidDataSubmit="SaveProvinceData"></AdminUnitForm>
        </Content>
    </DialogTemplates>
</SfDialog>

<SfToast @ref="ToastObj">
    <ToastPosition X="Right"></ToastPosition>
</SfToast>
<MudText Class="mb-2" Typo="Typo.h5"><MudIcon Size="Size.Large" Icon="@Icons.Material.Filled.ZoomInMap"></MudIcon> Provinces</MudText>
@*<PageCaption Title="Provinces:"></PageCaption>*@
<section>

    @if (objList == null)
    {
        <p>
            <em>Loading...</em>
        </p>
    }
    else
    {
        <div class="row">
            <AuthorizeView Roles="Administrators">
                <MudButton Color="Color.Primary" Size="Size.Small" Variant="Variant.Filled" StartIcon="@Icons.Material.Filled.Add" Class="mb-2" OnClick="OpenCreateForm">Create Province</MudButton>
                <MudButton Color="Color.Info" Size="Size.Small" Variant="Variant.Filled" StartIcon="@Icons.Material.Filled.ImportExport" Class="mb-2 ml-2" OnClick="NavigateImport">Import</MudButton>
            </AuthorizeView>
        </div>
        <div class="row">
            <SearchStructure ElectionId="@electionId"></SearchStructure>
        </div>
        <div class="row">
            <SfGrid DataSource="@objList" AllowFiltering="true" ModelType="@selectedObj" EnableVirtualization="true"
                    @ref="Grid" AllowSorting="true" AllowTextWrap="true" Width="100%" Height="calc(100vh - 242px)">
                <GridEvents QueryCellInfo="CustomizeCell" TValue="ElcStructureDTO"></GridEvents>
                <GridFilterSettings Type="FilterType.Excel"></GridFilterSettings>
                <GridPageSettings PageSize="10"></GridPageSettings>
                <GridColumns>
                    <GridColumn AutoFit="true" Field="Id" HeaderText="Code"></GridColumn>
                    <GridColumn AutoFit="true" Field="UrduTitle" HeaderText="Urdu Title" TextAlign="TextAlign.Right"></GridColumn>
                    <GridColumn AutoFit="true" Field="EnglishTitle" HeaderText="English Title"></GridColumn>
                    @*<GridColumn AutoFit="true" Field="SubUnits" HeaderText="Total Divisions"></GridColumn>*@
                    <GridColumn Width="320px" HeaderText="Audit Info">
                        <Template Context="kk">
                            @{
                                var oo = kk as ElcStructureDTO;
                            <div style="font-size:11px">
                                <span>Created By: @oo.CreateBy</span><span> On @oo.CreatedDate</span>
                                @if (!string.IsNullOrEmpty(oo.ModifiedBy))
                                {
                                    <br/>
                                    <span>Modified By: @oo.ModifiedBy</span>
                                    <span> On @oo.ModifiedDate</span>
                                }
                            </div>}
                        </Template>
                    </GridColumn>
                    <GridColumn Width="220px" HeaderText="Actions" AllowFiltering="false">
                        <Template Context="ss">
                            @{
                                var kk = ss as ElcStructureDTO;

                                <MudLink Target="@($"au{kk.Id}")" Href="@($"report/adminunit/{kk.Id}")">
                                    <MudIcon Icon="@Icons.Material.Filled.TableChart"></MudIcon>
                                </MudLink>
                                <AuthorizeView Roles="Administrators">
                                    <MudFab Color="Color.Error" Size="Size.Small" StartIcon="@Icons.Material.Filled.Delete" OnClick="@(() => DeleteRecord(kk.Id))"></MudFab>
                                    <MudFab Color="Color.Primary" Size="Size.Small" StartIcon="@Icons.Material.Filled.Edit" OnClick="@(() => OpenEditForm(kk))"></MudFab>
                                </AuthorizeView>

                                <a style="padding-left:3px;" href="/districts/@kk.Id">Districts</a>
                            }
                        </Template>
                    </GridColumn>
                </GridColumns>
            </SfGrid>
        </div>
    }
</section>

@code {

    // AuthenticationState is available as a CascadingParameter
    [CascadingParameter] private Task<AuthenticationState> authenticationStateTask { get; set; }

    [Parameter] public int electionId { get; set; }

    private SfDialog dlgForm;
    List<ElcStructureDTO> objList;

    private bool isDlgVisible;

    //private string SaveButtonText = "Save";
    //private string FormTitle = "Create Province";
    private ElcStructureDTO selectedObj;
    private SfToast ToastObj;
    public string ToastContent = "Unable to delete Record, Related Record(s) Exist";
    public List<GeneralItemDTO> languagesList { get; set; }
    public List<GeneralItemDTO> casteList { get; set; }
    public string ElectionTitle { get; set; }

    [Parameter] public int? cid { get; set; }

    public SfGrid<ElcStructureDTO> Grid { get; set; }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        //return base.OnAfterRenderAsync(firstRender);
        if (firstRender)
        {
            var user = (await authenticationStateTask).User;
            if (user.Identity.IsAuthenticated)
            {
                //var dd = await ProtectedSessionStore.GetAsync<ElectionStateDTO>("election");
                var dd = await localStorage.GetAsync<ElectionStateDTO>(await confreader.GetSettingValue("electionkey"));
                state.SetState(dd.Value);
            }
            else
            {
                try
                {
                    //await ProtectedSessionStore.DeleteAsync("election");
                    await localStorage.DeleteAsync(await confreader.GetSettingValue("electionkey"));
                    state.SetState(new ElectionStateDTO { ElectionId = 0, ElectionTitle = "", PhaseId = 0, PhaseTitle = "" });
                }
                catch (Exception)
                {
                    // ignored
                }

                NavigationManager.NavigateTo("");
            }

            StateHasChanged();
        }
    }

    protected override async Task OnInitializedAsync()
    {
        var user = (await authenticationStateTask).User;
        try
        {
            objList = await Service.GetList(StructureType.Province, electionId, state.state.PhaseId, cid);
        }
        catch (Exception)
        {
        }

        languagesList = await Service.GetLanguages();
        casteList = await Service.GetCastesList();
        ElectionTitle = await Service.GetElectionTitle(electionId);
        ClearData();
    }

    private async Task OpenCreateForm()
    {
        ClearData();
        await dlgForm.ShowAsync();
        //SaveButtonText = "Create";
    }

    private async Task OpenEditForm(ElcStructureDTO st)
    {
        selectedObj = await Service.GetAdminUnitDetail(st.Id, st.ParentId, StructureType.Province, state.state.PhaseId);
        await dlgForm.ShowAsync();
        //SaveButtonText = "Update";
    }

    private void ClearData()
    {
        selectedObj = new ElcStructureDTO { ParentId = electionId, StructureType = StructureType.Province, ElectionId = electionId };
    }

    private async Task SaveProvinceData()
    {
        var user = (await authenticationStateTask).User;

        try
        {
            await Service.Save(selectedObj, user.Identity.Name, state.state.PhaseId);
            objList = await Service.GetList(StructureType.Province, electionId, state.state.PhaseId, cid);
            await Grid.Refresh();
            ClearData();
            await dlgForm.HideAsync();
        }
        catch (Exception ex)
        {
            var mm = new ToastModel { Title = "Error", Content = ex.Message, CssClass = "e-toast-danger", ShowProgressBar = true, Timeout = 5000, ShowCloseButton = true };
            await ToastObj.ShowAsync(mm);
        }
    }

    private async Task DeleteRecord(int Id)
    {
        var res = await JSRuntime.InvokeAsync<bool>("confirm", "Are you want to delete this record?");
        if (res == false)
            return;

        try
        {
            var msg2 = await Service.Delete(Id);
            objList = await Service.GetList(StructureType.Province, electionId, state.state.PhaseId, cid);
            await Grid.Refresh();
            ClearData();
        }
        catch (Exception ex)
        {
            var mm = new ToastModel { Title = "Error", Content = ex.Message, CssClass = "e-toast-danger", ShowProgressBar = true, Timeout = 5000, ShowCloseButton = true };
            await ToastObj.ShowAsync(mm);
        }
    }

    private async Task TranslateToUrdu()
    {
        try
        {
            if (string.IsNullOrEmpty(selectedObj.UrduTitle))
                selectedObj.UrduTitle = await Translation.TranslateText(selectedObj.EnglishTitle);
        }
        catch (Exception)
        {
        }

        //if (string.IsNullOrEmpty(selectedObj.UrduTitle))
    }

    public void CustomizeCell(QueryCellInfoEventArgs<ElcStructureDTO> args)
    {
        if (args.Column.Field == "UrduTitle")
        {
            args.Cell.AddClass(new[] { "urdu-column" });
            args.Cell.AddStyle(new[] { "font-size:18px;line-height: 1.4;text-align: right" });
        }
    }

    private void beforeOpen(BeforeOpenEventArgs args)
    {
        args.MaxHeight = null;
    }

    private void NavigateImport()
    {
        NavigationManager.NavigateTo("/import");
    }

}