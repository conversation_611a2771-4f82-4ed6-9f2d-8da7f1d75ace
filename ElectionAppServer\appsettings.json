{
	"ConnectionStrings": {
		//"DefaultConnection": "Data Source=***********;Initial Catalog=electionapp_imported;Integrated Security=False;user id=sa;password=********;TrustServerCertificate=true"
		//"DefaultConnection": "Data Source=***********;Initial Catalog=DummyElection;Integrated Security=False;user id=sa;password=********;TrustServerCertificate=true"
		//"DefaultConnection": "Data Source=(local);Initial Catalog=electionapp_imported;Integrated Security=true;TrustServerCertificate=true"
		"DefaultConnection": "Data Source=***********\\ecp;Initial Catalog=electionapp_imported;Integrated Security=False;user id=sa;password=************;TrustServerCertificate=true"
		//"DefaultConnection": "Data Source=***********\\ecp;Initial Catalog=DummyElection;Integrated Security=False;user id=sa;password=************;TrustServerCertificate=true"
		//"DefaultConnection": "Data Source=***********;Initial Catalog=DummyElection;Integrated Security=False;user id=sa;password=********;TrustServerCertificate=true"
	},
	"Logging": {
		"LogLevel": {
			"Default": "Information",
			"Microsoft": "Warning",
			"Microsoft.Hosting.Lifetime": "Information"
		}
	},
	"AllowedHosts": "*",
	"mediapath": "C:\\Users\\<USER>\\source\\repos\\ElectionAppServer\\ElectionAppServer\\",
	"baseurl": "http://localhost:44366",
	"AllowToPush": "Yes",
	"electionkey": "lbelection",
	"useproxy": "false"
	// end slash is not included
}