﻿@page "/olres"
@using Size = MudBlazor.Size
@attribute [Authorize(Roles = "Administrators,OfflineUser")]
@*Live Offline Results*@
@*@inherits OwningComponentBase<ElectionResultsDataService>*@
@inject ElectionResultsDataService service

<style>
			.rtable {
				border: 1px solid #ddd;
				border-radius: 5px;
				padding: 10px;
				margin: 0 auto;
				width: 100%;
			}

				.rtable th, td {
					padding: 5px 10px;
					border-bottom: 1px solid #ddd;
					text-align: left;
					font-size:12px;
				}

				.rtable th {
					background-color: #eee;
					font-weight: bold;
					font-size: 12px;
				}
	.s1 {
		background-color: #55efc4;
	}
	.s2 {
		background-color: #74b9ff;
	}
	.s3 {
		background-color: #ffeaa7;
	}
	.s4 {
		background-color: darkorange;
	}
			/*tr:nth-child(odd) {
			background-color: #ffccff;
		}

		tr:nth-child(even) {
			background-color: #f0f0f0;
		}*/

	</style>


<div style="display: flex; gap: 10px" class="mb-2">


    <MudText Typo="Typo.h5">Other Source Results</MudText>
    <MudButton Color="Color.Primary" Size="Size.Small" Variant="Variant.Filled"
               OnClick="RefreshData"
               StartIcon="@Icons.Material.Filled.Refresh">
        Refresh
    </MudButton>


</div>
<div class="row mb-1">
    <div class="col-md">
        <SfDropDownList AllowFiltering="true" DataSource="_assemblies"
                        TItem="GeneralItemDTO" TValue="int"
                        Placeholder="Assembly" FloatLabelType="FloatLabelType.Always" @bind-Value="_selectedAssemblyId">
            <DropDownListFieldSettings Text="EnglishTitle" Value="Id"></DropDownListFieldSettings>
            <DropDownListEvents TValue="int" TItem="GeneralItemDTO" ValueChange="RefreshData"></DropDownListEvents>
        </SfDropDownList>
    </div>
    <div class="col-md">
        <SfDropDownList AllowFiltering="true" DataSource="_districts"
                        TItem="GeneralItemDTO" TValue="int"
                        Placeholder="District" FloatLabelType="FloatLabelType.Always" @bind-Value="_selectedDistrictId">
            <DropDownListFieldSettings Text="EnglishTitle" Value="Id"></DropDownListFieldSettings>
            <DropDownListEvents TValue="int" TItem="GeneralItemDTO" ValueChange="RefreshData"></DropDownListEvents>
        </SfDropDownList>
    </div>
    <div class="col-md">
        <SfDropDownList AllowFiltering="true" DataSource="_sources"
                        TItem="GeneralItemDTO" TValue="int"
                        Placeholder="Source" FloatLabelType="FloatLabelType.Always" @bind-Value="_selectedSourceId">
            <DropDownListFieldSettings Text="EnglishTitle" Value="Id"></DropDownListFieldSettings>
            <DropDownListEvents TValue="int" TItem="GeneralItemDTO" ValueChange="RefreshData"></DropDownListEvents>
        </SfDropDownList>
    </div>
</div>
<section>
    @{
        var cos = _results.Select(m => new { m.Constituency, m.Assembly }).Distinct().ToList();
        foreach (var co in cos)
        {
            <MudText Typo="Typo.h6">@co.Constituency - @co.Assembly</MudText>
            <table class="rtable">
                <thead>
                <tr>
                    <th>Source</th>
                    <th>PS #</th>
                    <th>Winner</th>
                    <th>Winner Party</th>
                    <th>Winner Votes</th>
                    <th>Runner Up</th>
                    <th>Runner Up Party</th>
                    <th>Runner Up Votes</th>
                    <th>Last Update</th>
                </tr>
                </thead>
                <tbody>
                @{
                    var mm = _results.Where(r => r.Constituency == co.Constituency &&
                                                 r.Assembly == co.Assembly).ToList();

                    foreach (var m in mm)
                    {
                        var cls = $"s{m.SourceId}";
                        <tr class="@cls">
                            <td>@m.Source</td>
                            <td>@m.ResPS</td>
                            <td>@m.Winner</td>
                            <td>@m.WinnerParty</td>
                            <td>@m.WinnerVotes</td>
                            <td>@m.RunnerUp</td>
                            <td>@m.RunnerUpParty</td>
                            <td>@m.RunnerUpVotes</td>
                            <td>@m.LastUpdate.ToString("d MMM, yyyy h:mm:ss")</td>
                        </tr>
                    }
                }


                </tbody>
            </table>
        }
    }


</section>

@code {

    [CascadingParameter] private Task<AuthenticationState> authenticationStateTask { get; set; }

    private List<OtherSourceResultDTO> _results = new();
    private List<GeneralItemDTO> _assemblies = new();
    private List<GeneralItemDTO> _districts = new();
    private List<GeneralItemDTO> _sources = new();
    private int _selectedAssemblyId;
    private int _selectedDistrictId;
    private int _selectedSourceId;


    private async Task RefreshData()
    {
        _results = await service.GetOtherSourceLiveResults(_selectedAssemblyId, _selectedDistrictId, _selectedSourceId, state.state.PhaseId);
        StateHasChanged();
    }


    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            var user = (await authenticationStateTask).User;
            if (user.Identity!.IsAuthenticated)
            {
                var dd = await localStorage.GetAsync<ElectionStateDTO>(confreader.GetSettingValue("electionkey")
                    .Result);
                state.SetState(dd.Value);

                _results = await service.GetOtherSourceLiveResults(_selectedAssemblyId, _selectedDistrictId, _selectedSourceId, state.state.PhaseId);
                _assemblies = await service.GetOtherSourceAssemblies(state.state.PhaseId);
                _districts = await service.GetOtherSourceDistricts(state.state.PhaseId);
                _sources = await service.GetOtherSources();
            }
            else
            {
                try
                {
                    await localStorage.DeleteAsync(await confreader.GetSettingValue("electionkey"));
                    state.SetState(new ElectionStateDTO
                        { ElectionId = 0, ElectionTitle = "", PhaseId = 0, PhaseTitle = "" });
                }
                catch (Exception)
                {
                    // ignored
                }

                NavigationManager.NavigateTo("");
            }

            StateHasChanged();
        }
    }

}