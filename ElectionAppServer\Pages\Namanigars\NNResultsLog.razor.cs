using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ElectionAppServer.DTO;
using Microsoft.AspNetCore.Components;
using Syncfusion.Blazor.Grids;
using Syncfusion.Blazor.Popups;

namespace ElectionAppServer.Pages.Namanigars;

public partial class NNResultsLog
{
    private bool IsDetailOpen;
    private string LogDate = "";
    private string Namanigar = "";
    private int PS;
    private int PSChange;
    [Parameter] public int ConstituencyId { get; set; }
    public SfGrid<NNResultsLogDTO> Grid { get; set; }
    public List<NNResultsLogDTO> log_list { get; set; } = new();
    public List<NNViewResultDTO> LogResult { get; private set; } = new();
    [Parameter] public int PhaseId { get; set; }
    [Parameter] public int SeatTypeId { get; set; }
    private string Action { get; set; }
    private List<NNResultsLogDTO> logList { get; set; } = new();

    protected override async Task OnParametersSetAsync()
    {
        await base.OnParametersSetAsync();

        logList = await Service.GetNamanigarResultsLog(ConstituencyId, PhaseId, SeatTypeId);
    }

    private void beforeOpen(BeforeOpenEventArgs args)
    {
        args.MaxHeight = null;
    }

    private async Task ViewDetail(int batchId)
    {
        LogResult = await Service.GetBatchResults(batchId);
        if (LogResult.Any())
        {
            LogDate = LogResult[0].ActionDate.ToString("d MMM, yyyy h:mm:dd");
            Namanigar = LogResult[0].Namanigar;
            PS = LogResult[0].PS;
            Action = LogResult[0].Action;
            PSChange = LogResult[0].PSChange;
        }

        IsDetailOpen = true;
        StateHasChanged();
        await Task.CompletedTask;
    }
}