﻿namespace ElectionAppServer.DTO;

public class SearchCandidateDTO
{
    public int Id { get; set; }
    public string EnglishName { get; set; }
    public string UrduName { get; set; }
    public string Party { get; set; }
    public string Disrict { get; set; }
    public string LastConstituency { get; set; }
    public string ImgUrl { get; set; }
    public int? PartyId { get; set; }
    public int? SymbolId { get; set; }
    public string Type { get; set; }

    #region Audit Log

    public string CreatedDate { get; set; }
    public string CreatedBy { get; set; }
    public string ModifiedDate { get; set; }
    public string ModifiedBy { get; set; }
    public int? Gender { get; internal set; }
    public string UrduFatherName { get; internal set; }

    #endregion Audit Log
}