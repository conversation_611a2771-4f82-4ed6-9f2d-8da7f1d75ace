﻿@layout ReportLayout
@page "/report/assembly/{AssemblyId:int}"
@using FilterType = Syncfusion.Blazor.Grids.FilterType
@inherits OwningComponentBase<ElectionAssemblyService>

<style>
    body {
        user-select: none;
    }
</style>

@if (info == null)
{
    <p>Loading</p>
}
else
{
    <div class="card bg-info mb-3">
        <div class="card-header" style="display: flex; justify-content: space-between;align-items: center">
            <div style="flex:1; font-size:30px; font-weight:bold">
                @info.EnglishTitle <br/>
                <span class="urdu-cap" style="font-size:34px; text-align: right;direction: rtl">@info.UrduTitle</span>
            </div>
        </div>
        <div class="card-body bg-light" style="color:black">
            <div class="row">
                <div class="col rptfield">
                    <div>Type (English): </div>
                    <div class="rptdata">
                        <b>@info.TypeEnglish</b>
                    </div>
                </div>
                <div class="col rptfield">
                    <div>Type (Urdu): </div>
                    <div class="rptdata">
                        <b>@info.TypeUrdu</b>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col rptfield">
                    <div>Reserved Seats: </div>
                    <div class="rptdata">
                        <b>@info.ReserveSeats</b>
                    </div>
                </div>
                <div class="col rptfield">
                    <div>Women Seats: </div>
                    <div class="rptdata">
                        <b>@info.WomenSeats</b>
                    </div>
                </div>
                @foreach (var st in info.Seats)
                {
                    <div class="col rptfield">
                        <div>@st.EnglishTitle: </div>
                        <div class="rptdata">
                            <b>@st.Count</b>
                        </div>
                    </div>
                }
            </div>
        </div>
    </div>
    <div class="card bg-secondary mb-3">
        <div class="card-header" style="display: flex; justify-content: space-between;align-items: center">
            <div>Constituencies</div>
            <div class="urdu-cap" style="text-align: right;direction: rtl">حلقے</div>
        </div>
        <div class="card-body bg-light" style="color:black">
            <SfGrid DataSource="info.Constituencies" AllowTextWrap="true" AllowSorting="true" AllowGrouping="true">
                <GridFilterSettings Type="FilterType.Excel"></GridFilterSettings>
                <GridColumns>
                    <GridColumn DisableHtmlEncode="false" Field=@nameof(ConstDetailDTO.Code) HeaderText="Code"></GridColumn>
                    @*<GridColumn DisableHtmlEncode="false" Field=@nameof(ConstDetailDTO.EnglishTitle) HeaderText="Constituency"></GridColumn>*@
                    <GridColumn HeaderText="Constituency">
                        <Template Context="cc">
                            @{
                                var obj = cc as ConstDetailDTO;
                                <a href="/report/constituency/@(obj.Id)">@((MarkupString)obj.EnglishTitle)</a>
                            }
                        </Template>
                    </GridColumn>
                    <GridColumn Field=@nameof(ConstDetailDTO.TotalPollingStations) HeaderText="Total PS"></GridColumn>
                    <GridColumn Field=@nameof(ConstDetailDTO.MaleVoters) HeaderText="Voters (Male)"></GridColumn>
                    <GridColumn Field=@nameof(ConstDetailDTO.FemaleVoters) HeaderText="Voters (Female)"></GridColumn>
                    <GridColumn Field=@nameof(ConstDetailDTO.YouthVoters) HeaderText="Youth Voters"></GridColumn>
                    <GridColumn Field=@nameof(ConstDetailDTO.Population) HeaderText="Population"></GridColumn>
                </GridColumns>
            </SfGrid>
        </div>
    </div>
    <div class="card bg-success mb-3">
        <div class="card-header" style="display: flex; justify-content: space-between;align-items: center">
            <div>Nominations</div>
            <div class="urdu-cap" style="text-align: right;direction: rtl">امیدوار</div>
        </div>
        <div class="card-body bg-light" style="color:black">
            <SfGrid AllowTextWrap="true" AllowSorting="true" AllowFiltering="true" AllowGrouping="true" DataSource="info.Nominations">
                <GridFilterSettings Type="FilterType.Excel"></GridFilterSettings>
                <GridColumns>
                    <GridColumn DisableHtmlEncode="false" Field=@nameof(NominationRptDTO.Phase) HeaderText="Phase"></GridColumn>
                    @*<GridColumn DisableHtmlEncode="false" Field=@nameof(NominationRptDTO.Constituency) HeaderText="Constituency"></GridColumn>*@
                    <GridColumn HeaderText="Constituency">
                        <Template Context="cc">
                            @{
                                var obj = cc as NominationRptDTO;
                                <a target="@($"cc{obj.ConstituencyId}")" href="/report/constituency/@(obj.ConstituencyId)">@((MarkupString)obj.Constituency)</a>
                            }
                        </Template>
                    </GridColumn>
                    <GridColumn DisableHtmlEncode="false" Field=@nameof(NominationRptDTO.Date) HeaderText="Date" Format="d MMM yyyy"></GridColumn>
                    <GridColumn DisableHtmlEncode="false" Field=@nameof(NominationRptDTO.SeatType) HeaderText="Seat"></GridColumn>
                    @*<GridColumn DisableHtmlEncode="false" Field=@nameof(NominationRptDTO.Candidate) HeaderText="Candidate"></GridColumn>*@
                    <GridColumn HeaderText="Candidate">
                        <Template Context="cc">
                            @{
                                var obj = cc as NominationRptDTO;
                                <a target="@($"can{obj.CandidateId}")" href="/report/candidate/@(obj.CandidateId)">@((MarkupString)obj.Candidate)</a>
                            }
                        </Template>
                    </GridColumn>
                    <GridColumn DisableHtmlEncode="false" Field=@nameof(NominationRptDTO.Symbol) HeaderText="Symbol"></GridColumn>
                    <GridColumn DisableHtmlEncode="false" Field=@nameof(NominationRptDTO.Weight) HeaderText="Weight"></GridColumn>
                    <GridColumn DisableHtmlEncode="false" Field=@nameof(NominationRptDTO.Votes) HeaderText="Votes"></GridColumn>
                </GridColumns>
            </SfGrid>
        </div>
    </div>
}

@code {
    [Parameter] public int AssemblyId { get; set; }
    public AssemblyRptDTO info { get; set; }

    protected override async Task OnParametersSetAsync()
    {
        await base.OnParametersSetAsync();
        info = await Service.GetAssemblyRptInfo(AssemblyId);
    }

}