﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace ElectionAppServer.Migrations
{
	public partial class removeextrafields : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropForeignKey(
				 name: "FK_ElectionAssemblyConstituencies_Castes_CasteId",
				 table: "ElectionAssemblyConstituencies");

			migrationBuilder.DropForeignKey(
				 name: "FK_ElectionAssemblyConstituencies_Languages_LanguageId",
				 table: "ElectionAssemblyConstituencies");

			migrationBuilder.DropIndex(
				 name: "IX_ElectionAssemblyConstituencies_CasteId",
				 table: "ElectionAssemblyConstituencies");

			migrationBuilder.DropIndex(
				 name: "IX_ElectionAssemblyConstituencies_LanguageId",
				 table: "ElectionAssemblyConstituencies");

			migrationBuilder.DropColumn(
				 name: "CasteId",
				 table: "ElectionAssemblyConstituencies");

			migrationBuilder.DropColumn(
				 name: "FemaleVoters",
				 table: "ElectionAssemblyConstituencies");

			migrationBuilder.DropColumn(
				 name: "ImportantAreas",
				 table: "ElectionAssemblyConstituencies");

			migrationBuilder.DropColumn(
				 name: "ImportantPoliticalPersonalities",
				 table: "ElectionAssemblyConstituencies");

			migrationBuilder.DropColumn(
				 name: "IsSeat",
				 table: "ElectionAssemblyConstituencies");

			migrationBuilder.DropColumn(
				 name: "LanguageId",
				 table: "ElectionAssemblyConstituencies");

			migrationBuilder.DropColumn(
				 name: "MaleVoters",
				 table: "ElectionAssemblyConstituencies");

			migrationBuilder.DropColumn(
				 name: "Problems",
				 table: "ElectionAssemblyConstituencies");

			migrationBuilder.DropColumn(
				 name: "RuralAreaPer",
				 table: "ElectionAssemblyConstituencies");

			migrationBuilder.DropColumn(
				 name: "TotalPollingStations",
				 table: "ElectionAssemblyConstituencies");

			migrationBuilder.DropColumn(
				 name: "Trivia",
				 table: "ElectionAssemblyConstituencies");

			migrationBuilder.DropColumn(
				 name: "UrbanAreaPer",
				 table: "ElectionAssemblyConstituencies");
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.AddColumn<int>(
				 name: "CasteId",
				 table: "ElectionAssemblyConstituencies",
				 type: "int",
				 nullable: true);

			migrationBuilder.AddColumn<int>(
				 name: "FemaleVoters",
				 table: "ElectionAssemblyConstituencies",
				 type: "int",
				 nullable: true);

			migrationBuilder.AddColumn<string>(
				 name: "ImportantAreas",
				 table: "ElectionAssemblyConstituencies",
				 type: "nvarchar(500)",
				 maxLength: 500,
				 nullable: true);

			migrationBuilder.AddColumn<string>(
				 name: "ImportantPoliticalPersonalities",
				 table: "ElectionAssemblyConstituencies",
				 type: "nvarchar(500)",
				 maxLength: 500,
				 nullable: true);

			migrationBuilder.AddColumn<bool>(
				 name: "IsSeat",
				 table: "ElectionAssemblyConstituencies",
				 type: "bit",
				 nullable: false,
				 defaultValue: false);

			migrationBuilder.AddColumn<int>(
				 name: "LanguageId",
				 table: "ElectionAssemblyConstituencies",
				 type: "int",
				 nullable: true);

			migrationBuilder.AddColumn<int>(
				 name: "MaleVoters",
				 table: "ElectionAssemblyConstituencies",
				 type: "int",
				 nullable: true);

			migrationBuilder.AddColumn<string>(
				 name: "Problems",
				 table: "ElectionAssemblyConstituencies",
				 type: "nvarchar(500)",
				 maxLength: 500,
				 nullable: true);

			migrationBuilder.AddColumn<float>(
				 name: "RuralAreaPer",
				 table: "ElectionAssemblyConstituencies",
				 type: "real",
				 nullable: true);

			migrationBuilder.AddColumn<int>(
				 name: "TotalPollingStations",
				 table: "ElectionAssemblyConstituencies",
				 type: "int",
				 nullable: true);

			migrationBuilder.AddColumn<string>(
				 name: "Trivia",
				 table: "ElectionAssemblyConstituencies",
				 type: "nvarchar(2000)",
				 maxLength: 2000,
				 nullable: true);

			migrationBuilder.AddColumn<float>(
				 name: "UrbanAreaPer",
				 table: "ElectionAssemblyConstituencies",
				 type: "real",
				 nullable: true);

			migrationBuilder.CreateIndex(
				 name: "IX_ElectionAssemblyConstituencies_CasteId",
				 table: "ElectionAssemblyConstituencies",
				 column: "CasteId");

			migrationBuilder.CreateIndex(
				 name: "IX_ElectionAssemblyConstituencies_LanguageId",
				 table: "ElectionAssemblyConstituencies",
				 column: "LanguageId");

			migrationBuilder.AddForeignKey(
				 name: "FK_ElectionAssemblyConstituencies_Castes_CasteId",
				 table: "ElectionAssemblyConstituencies",
				 column: "CasteId",
				 principalTable: "Castes",
				 principalColumn: "Id",
				 onDelete: ReferentialAction.Restrict);

			migrationBuilder.AddForeignKey(
				 name: "FK_ElectionAssemblyConstituencies_Languages_LanguageId",
				 table: "ElectionAssemblyConstituencies",
				 column: "LanguageId",
				 principalTable: "Languages",
				 principalColumn: "Id",
				 onDelete: ReferentialAction.Restrict);
		}
	}
}
