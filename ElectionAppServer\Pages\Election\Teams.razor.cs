﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ElectionAppServer.DTO;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Syncfusion.Blazor.Grids;
using Syncfusion.Blazor.Notifications;
using Syncfusion.Blazor.Popups;
using Action = Syncfusion.Blazor.Grids.Action;
using BeforeOpenEventArgs = Syncfusion.Blazor.Popups.BeforeOpenEventArgs;

namespace ElectionAppServer.Pages.Election;

public partial class Teams
{
    //public int?[] MultiVal = new int?[0];
    //public List<int?> MultiVal { get; set; }

    //private List<GeneralItemDTO> assemblyTypes;

    private SfDialog dlgForm;
    private string FormTitle = "Create Assembly";
    private bool isDlgVisible;
    private List<CoalitionDTO> objList;
    private List<GeneralItemDTO> parties_list;
    private string SaveButtonText = "Save";
    private CoalitionDTO selectedObj = new();
    public string ToastContent = "Unable to delete Record, Related Record(s) Exist";
    private SfToast ToastObj;
    public string ElectionTitle { get; set; }

    // AuthenticationState is available as a CascadingParameter
    [CascadingParameter] private Task<AuthenticationState> authenticationStateTask { get; set; }

    //private string provinceName = "";
    public string ProvinceName { get; set; }

    private SfGrid<CoalitionDTO> Grid { get; set; }
    private SfGrid<GeneralItemDTO> TeamGrid { get; set; }
    private int ElectionId { get; set; }
    private bool DisabledButton { get; set; }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        //return base.OnAfterRenderAsync(firstRender);
        if (firstRender)
        {
            var user = (await authenticationStateTask).User;
            if (user.Identity.IsAuthenticated)
            {
                //var dd =  await ProtectedSessionStore.GetAsync<ElectionStateDTO>("election");
                var dd = await localStorage.GetAsync<ElectionStateDTO>(confreader.GetSettingValue("electionkey")
                    .Result);
                state.SetState(dd.Value);
                ElectionId = dd.Value.ElectionId;
                objList = await Service.GetAllTeams(ElectionId); //Service.GetList(electionId);
                parties_list = await Service.GetAllParties();

                StateHasChanged();
            }
            else
            {
                try
                {
                    //await  ProtectedSessionStore.DeleteAsync("election");
                    await localStorage.DeleteAsync(await confreader.GetSettingValue("electionkey"));
                    state.SetState(new ElectionStateDTO
                        { ElectionId = 0, ElectionTitle = "", PhaseId = 0, PhaseTitle = "" });
                }
                catch (Exception)
                {
                    // ignored
                }

                NavigationManager.NavigateTo("");
            }
            //StateHasChanged();
        }
    }

    // protected async override Task OnInitializedAsync()
    // {
    // 	//objList = await Service.GetAllTeams(electionId); //Service.GetList(electionId);
    // 	//parties_list = await Service.GetAllParties();
    // }

    private async Task OpenCreateForm()
    {
        selectedObj = new CoalitionDTO();
        //MultiVal = new int?[0];
        //MultiVal = new List<int?>();
        await dlgForm.ShowAsync();
        SaveButtonText = "Create";
        FormTitle = "Create New Team";
    }

    private async Task OpenEditForm(CoalitionDTO st)
    {
        selectedObj.Id = st.Id;
        selectedObj.UrduTitle = st.UrduTitle;
        selectedObj.EnglishTitle = st.EnglishTitle;
        selectedObj.TeamParties = new List<GeneralItemDTO>();
        foreach (var p in st.TeamParties)
            selectedObj.TeamParties.Add(new GeneralItemDTO { Id = p.Id, EnglishTitle = p.EnglishTitle });
        //MultiVal = new List<int?>();

        //foreach (var item in st.PartyIds)
        //{
        //   MultiVal.Add(item);
        //}
        StateHasChanged();
        await dlgForm.ShowAsync();
        SaveButtonText = "Update";
        await TeamGrid.Refresh();
    }

    private void ClearData()
    {
        selectedObj = new CoalitionDTO();
    }

    private async Task SaveData()
    {
        var user = (await authenticationStateTask).User;
        try
        {
            if (selectedObj.Id == 0)
            {
                //var st = new CoalitionDTO();
                selectedObj.UrduTitle = selectedObj.UrduTitle.Trim();
                selectedObj.EnglishTitle = selectedObj.EnglishTitle.Trim();
                //foreach (var s in MultiVal)
                //{
                //   st.PartyIds.Add(s.Value);
                //}
                //Service.CreateCoalitionDTO(st);
                var res = Service.Save(selectedObj, ElectionId, user.Identity.Name);
                objList = await Service.GetAllTeams(ElectionId);
                await Grid.Refresh();
                ClearData();
                await dlgForm.HideAsync();
            }
            else
            {
                // Create forecast
                //var st = new CoalitionDTO();
                selectedObj.UrduTitle = selectedObj.UrduTitle.Trim();
                selectedObj.EnglishTitle = selectedObj.EnglishTitle.Trim();
                //st.Id = selectedObj.Id;
                //foreach (var s in MultiVal)
                //{
                //   st.PartyIds.Add(s.Value);
                //}
                //Service.CreateCoalitionDTO(st);
                var res = Service.Save(selectedObj, ElectionId, user.Identity.Name);
                objList = await Service.GetAllTeams(ElectionId);
                await Grid.Refresh();
                //ClearData();
                await dlgForm.HideAsync();
            }
        }
        catch (Exception ex)
        {
            var mm = new ToastModel { Title = "Error", Content = ex.Message };
            if (ex.InnerException != null)
                mm.Content += " -- Detail: " + ex.InnerException.Message;
            await ToastObj.ShowAsync(mm);
        }
    }

    private async Task DeleteRecord(int Id)
    {
        var para = new[] { "Are you want to delete this record?" };
        var res = await JSRuntime.InvokeAsync<bool>("confirm", para);
        if (res == false)
            return;

        //string msg = "";

        try
        {
            var msg2 = await Service.Delete(Id);
            objList = await Service.GetAllTeams(ElectionId);
            await Grid.Refresh();
            ClearData();
        }
        catch (Exception ex)
        {
            var mm = new ToastModel { Title = "Error", Content = ex.Message };
            await ToastObj.ShowAsync(mm);
            //msg = ee.Message;
            //if (ee.InnerException != null)
            //   msg += "<br />" + ee.InnerException.Message;
            //hasError = true;
            //ToastObj.ShowAsync();
            //StateHasChanged();
        }
    }

    public void CustomizeCell(QueryCellInfoEventArgs<CoalitionDTO> args)
    {
        if (args.Column.Field == "UrduTitle" || args.Column.Field == "AssemblyType" ||
            args.Column.Field == "SeatsList")
        {
            args.Cell.AddClass(new[] { "urdu-column" });
            args.Cell.AddStyle(new[] { "font-size:18px;line-height: 1.4;text-align: right" });
        }
    }

    private async Task ActionBeginHandler(ActionEventArgs<GeneralItemDTO> args)
    {
        if (args.RequestType == Action.Cancel ||
            args.RequestType == Action.Refresh ||
            args.RequestType == Action.Reorder ||
            args.RequestType == Action.Delete ||
            args.RequestType == Action.Save)
        {
            if (selectedObj.TeamParties.Count(c => c.Id == args.Data.Id) > 1)
            {
                var mm = new ToastModel { Title = "Error", Content = "Party already exist" };
                args.Cancel = true;
                await ToastObj.ShowAsync(mm);
                DisabledButton = true;
            }
            else
            {
                DisabledButton = false;
            }
        }
        else
        {
            DisabledButton = true;
        }

        await Task.CompletedTask;
    }

    private void beforeOpen(BeforeOpenEventArgs args)
    {
        args.MaxHeight = null;
    }
}