﻿using Microsoft.EntityFrameworkCore.Migrations;
using System;

namespace ElectionAppServer.Migrations
{
	public partial class Iniitial : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.CreateTable(
				 name: "AssemblyType",
				 columns: table => new
				 {
					 Id = table.Column<int>(nullable: false)
							.Annotation("SqlServer:Identity", "1, 1"),
					 UrduTitle = table.Column<string>(maxLength: 200, nullable: false),
					 EnglishTitle = table.Column<string>(unicode: false, maxLength: 200, nullable: false),
					 CreatedDate = table.Column<DateTime>(nullable: false, defaultValueSql: "(getdate())"),
					 ModifiedDate = table.Column<DateTime>(nullable: true),
					 CreatedBy = table.Column<string>(maxLength: 450, nullable: true),
					 ModifiedBy = table.Column<string>(maxLength: 450, nullable: true)
				 },
				 constraints: table =>
				 {
					 table.PrimaryKey("PK_AssemblyType", x => x.Id);
				 });

			migrationBuilder.CreateTable(
				 name: "Cast<PERSON>",
				 columns: table => new
				 {
					 Id = table.Column<int>(nullable: false)
							.Annotation("SqlServer:Identity", "1, 1"),
					 UrduTitle = table.Column<string>(maxLength: 200, nullable: false),
					 EnglishTitle = table.Column<string>(maxLength: 200, nullable: false),
					 CreatedDate = table.Column<DateTime>(nullable: false),
					 ModifiedDate = table.Column<DateTime>(nullable: true),
					 CreatedBy = table.Column<string>(maxLength: 450, nullable: true),
					 ModifiedBy = table.Column<string>(maxLength: 450, nullable: true)
				 },
				 constraints: table =>
				 {
					 table.PrimaryKey("PK_Castes", x => x.Id);
				 });

			migrationBuilder.CreateTable(
				 name: "Educations",
				 columns: table => new
				 {
					 Id = table.Column<int>(nullable: false)
							.Annotation("SqlServer:Identity", "1, 1"),
					 UrduTitle = table.Column<string>(maxLength: 200, nullable: false),
					 EnglishTitle = table.Column<string>(maxLength: 200, nullable: false),
					 CreatedDate = table.Column<DateTime>(nullable: false),
					 ModifiedDate = table.Column<DateTime>(nullable: true),
					 CreatedBy = table.Column<string>(maxLength: 450, nullable: true),
					 ModifiedBy = table.Column<string>(maxLength: 450, nullable: true)
				 },
				 constraints: table =>
				 {
					 table.PrimaryKey("PK_Educations", x => x.Id);
				 });

			migrationBuilder.CreateTable(
				 name: "Elections",
				 columns: table => new
				 {
					 Id = table.Column<int>(nullable: false)
							.Annotation("SqlServer:Identity", "1, 1"),
					 EnglishTitle = table.Column<string>(maxLength: 200, nullable: false),
					 UrduTitle = table.Column<string>(maxLength: 200, nullable: false),
					 Description = table.Column<string>(maxLength: 1000, nullable: true),
					 YearFrom = table.Column<int>(nullable: false),
					 YearTo = table.Column<int>(nullable: false),
					 CreatedDate = table.Column<DateTime>(nullable: false),
					 ModifiedDate = table.Column<DateTime>(nullable: true),
					 CreatedBy = table.Column<string>(maxLength: 450, nullable: true),
					 ModifiedBy = table.Column<string>(maxLength: 450, nullable: true)
				 },
				 constraints: table =>
				 {
					 table.PrimaryKey("PK_Elections", x => x.Id);
				 });

			migrationBuilder.CreateTable(
				 name: "Languages",
				 columns: table => new
				 {
					 Id = table.Column<int>(nullable: false)
							.Annotation("SqlServer:Identity", "1, 1"),
					 UrduTitle = table.Column<string>(maxLength: 200, nullable: false),
					 EnglishTitle = table.Column<string>(maxLength: 200, nullable: false),
					 CreatedDate = table.Column<DateTime>(nullable: false),
					 ModifiedDate = table.Column<DateTime>(nullable: true),
					 CreatedBy = table.Column<string>(maxLength: 450, nullable: true),
					 ModifiedBy = table.Column<string>(maxLength: 450, nullable: true)
				 },
				 constraints: table =>
				 {
					 table.PrimaryKey("PK_Languages", x => x.Id);
				 });

			migrationBuilder.CreateTable(
				 name: "Professions",
				 columns: table => new
				 {
					 Id = table.Column<int>(nullable: false)
							.Annotation("SqlServer:Identity", "1, 1"),
					 UrduTitle = table.Column<string>(maxLength: 200, nullable: false),
					 EnglishTitle = table.Column<string>(maxLength: 200, nullable: false),
					 CreatedDate = table.Column<DateTime>(nullable: false),
					 ModifiedDate = table.Column<DateTime>(nullable: true),
					 CreatedBy = table.Column<string>(maxLength: 450, nullable: true),
					 ModifiedBy = table.Column<string>(maxLength: 450, nullable: true)
				 },
				 constraints: table =>
				 {
					 table.PrimaryKey("PK_Professions", x => x.Id);
				 });

			migrationBuilder.CreateTable(
				 name: "Reporters",
				 columns: table => new
				 {
					 Id = table.Column<int>(nullable: false)
							.Annotation("SqlServer:Identity", "1, 1"),
					 Name = table.Column<string>(nullable: true),
					 Email = table.Column<string>(nullable: true),
					 Phone = table.Column<string>(nullable: true),
					 IsActive = table.Column<bool>(nullable: false),
					 CreatedDate = table.Column<DateTime>(nullable: false),
					 ModifiedDate = table.Column<DateTime>(nullable: true),
					 CreatedBy = table.Column<string>(maxLength: 450, nullable: true),
					 ModifiedBy = table.Column<string>(maxLength: 450, nullable: true)
				 },
				 constraints: table =>
				 {
					 table.PrimaryKey("PK_Reporters", x => x.Id);
				 });

			migrationBuilder.CreateTable(
				 name: "SeatTypes",
				 columns: table => new
				 {
					 Id = table.Column<int>(nullable: false)
							.Annotation("SqlServer:Identity", "1, 1"),
					 UrduTitle = table.Column<string>(maxLength: 200, nullable: false),
					 EnglishTitle = table.Column<string>(unicode: false, maxLength: 200, nullable: false),
					 CreatedDate = table.Column<DateTime>(nullable: false, defaultValueSql: "(getdate())"),
					 ModifiedDate = table.Column<DateTime>(nullable: true),
					 CreatedBy = table.Column<string>(maxLength: 450, nullable: true),
					 ModifiedBy = table.Column<string>(maxLength: 450, nullable: true)
				 },
				 constraints: table =>
				 {
					 table.PrimaryKey("PK_SeatTypes", x => x.Id);
				 });

			migrationBuilder.CreateTable(
				 name: "Structures",
				 columns: table => new
				 {
					 Id = table.Column<int>(nullable: false)
							.Annotation("SqlServer:Identity", "1, 1"),
					 UrduTitle = table.Column<string>(maxLength: 200, nullable: false),
					 EnglishTitle = table.Column<string>(maxLength: 200, nullable: false),
					 CreatedDate = table.Column<DateTime>(nullable: false),
					 ModifiedDate = table.Column<DateTime>(nullable: true),
					 CreatedBy = table.Column<string>(maxLength: 450, nullable: true),
					 ModifiedBy = table.Column<string>(maxLength: 450, nullable: true),
					 Discriminator = table.Column<string>(nullable: false),
					 DivisionId = table.Column<int>(nullable: true),
					 ProvinceId = table.Column<int>(nullable: true),
					 Code = table.Column<string>(nullable: true),
					 DistrictId = table.Column<int>(nullable: true),
					 ProvincialAssemblyHalka_Code = table.Column<string>(nullable: true),
					 ProvincialAssemblyHalka_DistrictId = table.Column<int>(nullable: true),
					 Town_DistrictId = table.Column<int>(nullable: true),
					 TownId = table.Column<int>(nullable: true),
					 UnionCouncilId = table.Column<int>(nullable: true)
				 },
				 constraints: table =>
				 {
					 table.PrimaryKey("PK_Structures", x => x.Id);
					 table.ForeignKey(
							  name: "FK_Districts_Divisions",
							  column: x => x.DivisionId,
							  principalTable: "Structures",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Restrict);
					 table.ForeignKey(
							  name: "FK_Divisions_Provinces",
							  column: x => x.ProvinceId,
							  principalTable: "Structures",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Restrict);
					 table.ForeignKey(
							  name: "FK_Structures_Structures_DistrictId",
							  column: x => x.DistrictId,
							  principalTable: "Structures",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Restrict);
					 table.ForeignKey(
							  name: "FK_Structures_Structures_ProvincialAssemblyHalka_DistrictId",
							  column: x => x.ProvincialAssemblyHalka_DistrictId,
							  principalTable: "Structures",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Restrict);
					 table.ForeignKey(
							  name: "FK_Structures_Structures_Town_DistrictId",
							  column: x => x.Town_DistrictId,
							  principalTable: "Structures",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Restrict);
					 table.ForeignKey(
							  name: "FK_Structures_Structures_TownId",
							  column: x => x.TownId,
							  principalTable: "Structures",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Restrict);
					 table.ForeignKey(
							  name: "FK_Structures_Structures_UnionCouncilId",
							  column: x => x.UnionCouncilId,
							  principalTable: "Structures",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Restrict);
				 });

			migrationBuilder.CreateTable(
				 name: "Symbols",
				 columns: table => new
				 {
					 Id = table.Column<int>(nullable: false)
							.Annotation("SqlServer:Identity", "1, 1"),
					 Code = table.Column<int>(nullable: false),
					 UrduTitle = table.Column<string>(maxLength: 200, nullable: false),
					 EnglishTitle = table.Column<string>(maxLength: 200, nullable: false),
					 CreatedDate = table.Column<DateTime>(nullable: false),
					 ModifiedDate = table.Column<DateTime>(nullable: true),
					 CreatedBy = table.Column<string>(maxLength: 450, nullable: true),
					 ModifiedBy = table.Column<string>(maxLength: 450, nullable: true)
				 },
				 constraints: table =>
				 {
					 table.PrimaryKey("PK_Symbols", x => x.Id);
				 });

			migrationBuilder.CreateTable(
				 name: "ElectionAssemblies",
				 columns: table => new
				 {
					 Id = table.Column<int>(nullable: false)
							.Annotation("SqlServer:Identity", "1, 1"),
					 ElectionId = table.Column<int>(nullable: false),
					 AssemblyTypeId = table.Column<int>(nullable: false),
					 EnglishTitle = table.Column<string>(maxLength: 200, nullable: false),
					 UrduTitle = table.Column<string>(maxLength: 200, nullable: false),
					 CreatedDate = table.Column<DateTime>(nullable: false),
					 ModifiedDate = table.Column<DateTime>(nullable: true),
					 CreatedBy = table.Column<string>(maxLength: 450, nullable: true),
					 ModifiedBy = table.Column<string>(maxLength: 450, nullable: true)
				 },
				 constraints: table =>
				 {
					 table.PrimaryKey("PK_ElectionAssemblies", x => x.Id);
					 table.ForeignKey(
							  name: "FK_ElectionAssemblies_AssemblyType_AssemblyTypeId",
							  column: x => x.AssemblyTypeId,
							  principalTable: "AssemblyType",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Cascade);
					 table.ForeignKey(
							  name: "FK_ElectionAssemblies_Elections_ElectionId",
							  column: x => x.ElectionId,
							  principalTable: "Elections",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Cascade);
				 });

			migrationBuilder.CreateTable(
				 name: "ElectionPhases",
				 columns: table => new
				 {
					 Id = table.Column<int>(nullable: false)
							.Annotation("SqlServer:Identity", "1, 1"),
					 Title = table.Column<string>(nullable: true),
					 StartDate = table.Column<DateTime>(nullable: false),
					 ElectionId = table.Column<int>(nullable: false)
				 },
				 constraints: table =>
				 {
					 table.PrimaryKey("PK_ElectionPhases", x => x.Id);
					 table.ForeignKey(
							  name: "FK_ElectionPhases_Elections_ElectionId",
							  column: x => x.ElectionId,
							  principalTable: "Elections",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Cascade);
				 });

			migrationBuilder.CreateTable(
				 name: "Candidates",
				 columns: table => new
				 {
					 Id = table.Column<int>(nullable: false)
							.Annotation("SqlServer:Identity", "1, 1"),
					 CandidateType = table.Column<int>(nullable: false),
					 UrduName = table.Column<string>(maxLength: 100, nullable: false),
					 EnglishName = table.Column<string>(maxLength: 100, nullable: false),
					 FatherEnglishName = table.Column<string>(maxLength: 100, nullable: false),
					 FatherUrduName = table.Column<string>(maxLength: 100, nullable: false),
					 Gender = table.Column<int>(nullable: false),
					 DateOfBirth = table.Column<DateTime>(nullable: true),
					 Trivia = table.Column<string>(maxLength: 2000, nullable: true),
					 DistrictId = table.Column<int>(nullable: false),
					 ProfessionId = table.Column<int>(nullable: true),
					 EducationId = table.Column<int>(nullable: true),
					 LanguageId = table.Column<int>(nullable: true),
					 CasteId = table.Column<int>(nullable: true),
					 TotalAssets = table.Column<string>(nullable: true),
					 ContactNumber = table.Column<string>(nullable: true),
					 IsFresh = table.Column<bool>(nullable: false),
					 CreatedDate = table.Column<DateTime>(nullable: false),
					 ModifiedDate = table.Column<DateTime>(nullable: true),
					 CreatedBy = table.Column<string>(maxLength: 450, nullable: true),
					 ModifiedBy = table.Column<string>(maxLength: 450, nullable: true)
				 },
				 constraints: table =>
				 {
					 table.PrimaryKey("PK_Candidates", x => x.Id);
					 table.ForeignKey(
							  name: "FK_Candidates_Castes_CasteId",
							  column: x => x.CasteId,
							  principalTable: "Castes",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Restrict);
					 table.ForeignKey(
							  name: "FK_Candidates_Structures_DistrictId",
							  column: x => x.DistrictId,
							  principalTable: "Structures",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Cascade);
					 table.ForeignKey(
							  name: "FK_Candidates_Educations_EducationId",
							  column: x => x.EducationId,
							  principalTable: "Educations",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Restrict);
					 table.ForeignKey(
							  name: "FK_Candidates_Languages_LanguageId",
							  column: x => x.LanguageId,
							  principalTable: "Languages",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Restrict);
					 table.ForeignKey(
							  name: "FK_Candidates_Professions_ProfessionId",
							  column: x => x.ProfessionId,
							  principalTable: "Professions",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Restrict);
				 });

			migrationBuilder.CreateTable(
				 name: "Parties",
				 columns: table => new
				 {
					 Id = table.Column<int>(nullable: false)
							.Annotation("SqlServer:Identity", "1, 1"),
					 EnglishTitle = table.Column<string>(maxLength: 200, nullable: true),
					 UrduTitle = table.Column<string>(maxLength: 200, nullable: true),
					 ShortEnglishTitle = table.Column<string>(maxLength: 50, nullable: true),
					 ShortUrduTitle = table.Column<string>(maxLength: 50, nullable: true),
					 SymbolId = table.Column<int>(nullable: false),
					 DateOfCreation = table.Column<DateTime>(nullable: false),
					 Trivia = table.Column<string>(nullable: true),
					 CreatedDate = table.Column<DateTime>(nullable: false),
					 ModifiedDate = table.Column<DateTime>(nullable: true),
					 CreatedBy = table.Column<string>(maxLength: 450, nullable: true),
					 ModifiedBy = table.Column<string>(maxLength: 450, nullable: true)
				 },
				 constraints: table =>
				 {
					 table.PrimaryKey("PK_Parties", x => x.Id);
					 table.ForeignKey(
							  name: "FK_Parties_Symbols_SymbolId",
							  column: x => x.SymbolId,
							  principalTable: "Symbols",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Cascade);
				 });

			migrationBuilder.CreateTable(
				 name: "ElectionAssemblyConstituencies",
				 columns: table => new
				 {
					 ElectionAssemblyId = table.Column<int>(nullable: false),
					 StructureId = table.Column<int>(nullable: false),
					 MaleVoters = table.Column<int>(nullable: true),
					 FemaleVoters = table.Column<int>(nullable: true),
					 TotalPollingStations = table.Column<int>(nullable: true),
					 CasteId = table.Column<int>(nullable: true),
					 LanguageId = table.Column<int>(nullable: true),
					 Trivia = table.Column<string>(nullable: true),
					 ImportantPoliticalPersonalities = table.Column<string>(nullable: true),
					 Problems = table.Column<string>(nullable: true),
					 ImportantAreas = table.Column<string>(nullable: true),
					 RuralAreaPer = table.Column<string>(nullable: true),
					 UrbanAreaPer = table.Column<string>(nullable: true),
					 CreatedDate = table.Column<DateTime>(nullable: false),
					 ModifiedDate = table.Column<DateTime>(nullable: true),
					 CreatedBy = table.Column<string>(maxLength: 450, nullable: true),
					 ModifiedBy = table.Column<string>(maxLength: 450, nullable: true)
				 },
				 constraints: table =>
				 {
					 table.PrimaryKey("PK_ElectionAssemblyConstituencies", x => new { x.ElectionAssemblyId, x.StructureId });
					 table.ForeignKey(
							  name: "FK_ElectionAssemblyConstituencies_Castes_CasteId",
							  column: x => x.CasteId,
							  principalTable: "Castes",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Restrict);
					 table.ForeignKey(
							  name: "FK_ElectionAssemblyConstituencies_ElectionAssemblies_ElectionAssemblyId",
							  column: x => x.ElectionAssemblyId,
							  principalTable: "ElectionAssemblies",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Cascade);
					 table.ForeignKey(
							  name: "FK_ElectionAssemblyConstituencies_Languages_LanguageId",
							  column: x => x.LanguageId,
							  principalTable: "Languages",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Restrict);
					 table.ForeignKey(
							  name: "FK_ElectionAssemblyConstituencies_Structures_StructureId",
							  column: x => x.StructureId,
							  principalTable: "Structures",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Cascade);
				 });

			migrationBuilder.CreateTable(
				 name: "ElectionAssemblySeats",
				 columns: table => new
				 {
					 ElectionAssemblyId = table.Column<int>(nullable: false),
					 SeatTypeId = table.Column<int>(nullable: false),
					 CreatedDate = table.Column<DateTime>(nullable: false),
					 ModifiedDate = table.Column<DateTime>(nullable: true),
					 CreatedBy = table.Column<string>(maxLength: 450, nullable: true),
					 ModifiedBy = table.Column<string>(maxLength: 450, nullable: true)
				 },
				 constraints: table =>
				 {
					 table.PrimaryKey("PK_ElectionAssemblySeats", x => new { x.ElectionAssemblyId, x.SeatTypeId });
					 table.ForeignKey(
							  name: "FK_ElectionAssemblySeats_ElectionAssemblies_ElectionAssemblyId",
							  column: x => x.ElectionAssemblyId,
							  principalTable: "ElectionAssemblies",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Cascade);
					 table.ForeignKey(
							  name: "FK_ElectionAssemblySeats_SeatTypes_SeatTypeId",
							  column: x => x.SeatTypeId,
							  principalTable: "SeatTypes",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Cascade);
				 });

			migrationBuilder.CreateTable(
				 name: "Nominations",
				 columns: table => new
				 {
					 ElectionAssemblyId = table.Column<int>(nullable: false),
					 StructureId = table.Column<int>(nullable: false),
					 SeatTypeId = table.Column<int>(nullable: false),
					 CandidteId = table.Column<int>(nullable: false),
					 CreatedDate = table.Column<DateTime>(nullable: false),
					 ModifiedDate = table.Column<DateTime>(nullable: true),
					 CreatedBy = table.Column<string>(maxLength: 450, nullable: true),
					 ModifiedBy = table.Column<string>(maxLength: 450, nullable: true)
				 },
				 constraints: table =>
				 {
					 table.PrimaryKey("PK_Nominations", x => new { x.ElectionAssemblyId, x.StructureId, x.SeatTypeId, x.CandidteId });
					 table.ForeignKey(
							  name: "FK_Nominations_Candidates_CandidteId",
							  column: x => x.CandidteId,
							  principalTable: "Candidates",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Restrict);
					 table.ForeignKey(
							  name: "FK_Nominations_ElectionAssemblies_ElectionAssemblyId",
							  column: x => x.ElectionAssemblyId,
							  principalTable: "ElectionAssemblies",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Restrict);
					 table.ForeignKey(
							  name: "FK_Nominations_SeatTypes_SeatTypeId",
							  column: x => x.SeatTypeId,
							  principalTable: "SeatTypes",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Restrict);
					 table.ForeignKey(
							  name: "FK_Nominations_Structures_StructureId",
							  column: x => x.StructureId,
							  principalTable: "Structures",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Restrict);
				 });

			migrationBuilder.CreateTable(
				 name: "PoliticalCareers",
				 columns: table => new
				 {
					 Id = table.Column<int>(nullable: false)
							.Annotation("SqlServer:Identity", "1, 1"),
					 CandidateId = table.Column<int>(nullable: false),
					 Position = table.Column<string>(nullable: true),
					 DateFrom = table.Column<DateTime>(nullable: false),
					 DateTo = table.Column<DateTime>(nullable: true),
					 TillToday = table.Column<bool>(nullable: false),
					 CreatedDate = table.Column<DateTime>(nullable: false),
					 ModifiedDate = table.Column<DateTime>(nullable: true),
					 CreatedBy = table.Column<string>(maxLength: 450, nullable: true),
					 ModifiedBy = table.Column<string>(maxLength: 450, nullable: true)
				 },
				 constraints: table =>
				 {
					 table.PrimaryKey("PK_PoliticalCareers", x => x.Id);
					 table.ForeignKey(
							  name: "FK_PoliticalCareers_Candidates_CandidateId",
							  column: x => x.CandidateId,
							  principalTable: "Candidates",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Cascade);
				 });

			migrationBuilder.CreateTable(
				 name: "PartyAffiliations",
				 columns: table => new
				 {
					 Id = table.Column<int>(nullable: false)
							.Annotation("SqlServer:Identity", "1, 1"),
					 CandidateId = table.Column<int>(nullable: false),
					 PartyId = table.Column<int>(nullable: false),
					 Position = table.Column<string>(maxLength: 200, nullable: false),
					 DateFrom = table.Column<DateTime>(nullable: false),
					 DateTo = table.Column<DateTime>(nullable: true),
					 TillToday = table.Column<bool>(nullable: false),
					 CreatedDate = table.Column<DateTime>(nullable: false),
					 ModifiedDate = table.Column<DateTime>(nullable: true),
					 CreatedBy = table.Column<string>(maxLength: 450, nullable: true),
					 ModifiedBy = table.Column<string>(maxLength: 450, nullable: true)
				 },
				 constraints: table =>
				 {
					 table.PrimaryKey("PK_PartyAffiliations", x => x.Id);
					 table.ForeignKey(
							  name: "FK_PartyAffiliations_Candidates_CandidateId",
							  column: x => x.CandidateId,
							  principalTable: "Candidates",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Cascade);
					 table.ForeignKey(
							  name: "FK_PartyAffiliations_Parties_PartyId",
							  column: x => x.PartyId,
							  principalTable: "Parties",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Cascade);
				 });

			migrationBuilder.CreateIndex(
				 name: "IX_Candidates_CasteId",
				 table: "Candidates",
				 column: "CasteId");

			migrationBuilder.CreateIndex(
				 name: "IX_Candidates_DistrictId",
				 table: "Candidates",
				 column: "DistrictId");

			migrationBuilder.CreateIndex(
				 name: "IX_Candidates_EducationId",
				 table: "Candidates",
				 column: "EducationId");

			migrationBuilder.CreateIndex(
				 name: "IX_Candidates_LanguageId",
				 table: "Candidates",
				 column: "LanguageId");

			migrationBuilder.CreateIndex(
				 name: "IX_Candidates_ProfessionId",
				 table: "Candidates",
				 column: "ProfessionId");

			migrationBuilder.CreateIndex(
				 name: "IX_ElectionAssemblies_AssemblyTypeId",
				 table: "ElectionAssemblies",
				 column: "AssemblyTypeId");

			migrationBuilder.CreateIndex(
				 name: "IX_ElectionAssemblies_ElectionId",
				 table: "ElectionAssemblies",
				 column: "ElectionId");

			migrationBuilder.CreateIndex(
				 name: "IX_ElectionAssemblyConstituencies_CasteId",
				 table: "ElectionAssemblyConstituencies",
				 column: "CasteId");

			migrationBuilder.CreateIndex(
				 name: "IX_ElectionAssemblyConstituencies_LanguageId",
				 table: "ElectionAssemblyConstituencies",
				 column: "LanguageId");

			migrationBuilder.CreateIndex(
				 name: "IX_ElectionAssemblyConstituencies_StructureId",
				 table: "ElectionAssemblyConstituencies",
				 column: "StructureId");

			migrationBuilder.CreateIndex(
				 name: "IX_ElectionAssemblySeats_SeatTypeId",
				 table: "ElectionAssemblySeats",
				 column: "SeatTypeId");

			migrationBuilder.CreateIndex(
				 name: "IX_ElectionPhases_ElectionId",
				 table: "ElectionPhases",
				 column: "ElectionId");

			migrationBuilder.CreateIndex(
				 name: "IX_Nominations_CandidteId",
				 table: "Nominations",
				 column: "CandidteId");

			migrationBuilder.CreateIndex(
				 name: "IX_Nominations_SeatTypeId",
				 table: "Nominations",
				 column: "SeatTypeId");

			migrationBuilder.CreateIndex(
				 name: "IX_Nominations_StructureId",
				 table: "Nominations",
				 column: "StructureId");

			migrationBuilder.CreateIndex(
				 name: "IX_Parties_SymbolId",
				 table: "Parties",
				 column: "SymbolId");

			migrationBuilder.CreateIndex(
				 name: "IX_PartyAffiliations_CandidateId",
				 table: "PartyAffiliations",
				 column: "CandidateId");

			migrationBuilder.CreateIndex(
				 name: "IX_PartyAffiliations_PartyId",
				 table: "PartyAffiliations",
				 column: "PartyId");

			migrationBuilder.CreateIndex(
				 name: "IX_PoliticalCareers_CandidateId",
				 table: "PoliticalCareers",
				 column: "CandidateId");

			migrationBuilder.CreateIndex(
				 name: "IX_Structures_DivisionId",
				 table: "Structures",
				 column: "DivisionId");

			migrationBuilder.CreateIndex(
				 name: "IX_Structures_ProvinceId",
				 table: "Structures",
				 column: "ProvinceId");

			migrationBuilder.CreateIndex(
				 name: "IX_Structures_DistrictId",
				 table: "Structures",
				 column: "DistrictId");

			migrationBuilder.CreateIndex(
				 name: "IX_Structures_ProvincialAssemblyHalka_DistrictId",
				 table: "Structures",
				 column: "ProvincialAssemblyHalka_DistrictId");

			migrationBuilder.CreateIndex(
				 name: "IX_Structures_Town_DistrictId",
				 table: "Structures",
				 column: "Town_DistrictId");

			migrationBuilder.CreateIndex(
				 name: "IX_Structures_TownId",
				 table: "Structures",
				 column: "TownId");

			migrationBuilder.CreateIndex(
				 name: "IX_Structures_UnionCouncilId",
				 table: "Structures",
				 column: "UnionCouncilId");
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropTable(
				 name: "ElectionAssemblyConstituencies");

			migrationBuilder.DropTable(
				 name: "ElectionAssemblySeats");

			migrationBuilder.DropTable(
				 name: "ElectionPhases");

			migrationBuilder.DropTable(
				 name: "Nominations");

			migrationBuilder.DropTable(
				 name: "PartyAffiliations");

			migrationBuilder.DropTable(
				 name: "PoliticalCareers");

			migrationBuilder.DropTable(
				 name: "Reporters");

			migrationBuilder.DropTable(
				 name: "ElectionAssemblies");

			migrationBuilder.DropTable(
				 name: "SeatTypes");

			migrationBuilder.DropTable(
				 name: "Parties");

			migrationBuilder.DropTable(
				 name: "Candidates");

			migrationBuilder.DropTable(
				 name: "AssemblyType");

			migrationBuilder.DropTable(
				 name: "Elections");

			migrationBuilder.DropTable(
				 name: "Symbols");

			migrationBuilder.DropTable(
				 name: "Castes");

			migrationBuilder.DropTable(
				 name: "Structures");

			migrationBuilder.DropTable(
				 name: "Educations");

			migrationBuilder.DropTable(
				 name: "Languages");

			migrationBuilder.DropTable(
				 name: "Professions");
		}
	}
}
