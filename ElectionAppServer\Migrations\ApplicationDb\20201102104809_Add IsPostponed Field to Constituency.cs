﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace ElectionAppServer.Migrations.ApplicationDb
{
	public partial class AddIsPostponedFieldtoConstituency : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.AddColumn<bool>(
				 name: "IsPostponed",
				 table: "Structures",
				 type: "bit",
				 nullable: false,
				 defaultValue: false);

			migrationBuilder.AddColumn<bool>(
				 name: "IsPostponed",
				 table: "Nominations",
				 type: "bit",
				 nullable: false,
				 defaultValue: false);

			migrationBuilder.AddColumn<bool>(
				 name: "IsWithdraw",
				 table: "Nominations",
				 type: "bit",
				 nullable: false,
				 defaultValue: false);
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropColumn(
				 name: "IsPostponed",
				 table: "Structures");

			migrationBuilder.DropColumn(
				 name: "IsPostponed",
				 table: "Nominations");

			migrationBuilder.DropColumn(
				 name: "IsWithdraw",
				 table: "Nominations");
		}
	}
}
