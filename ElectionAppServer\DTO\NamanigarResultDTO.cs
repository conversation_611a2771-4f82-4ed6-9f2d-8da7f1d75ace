﻿using System;
using System.Collections.Generic;

namespace ElectionAppServer.DTO;

public class NamanigarResultDTO
{
    public int BatchId { get; set; }
    public int ConstituencyId { get; set; }
    public string Constituency { get; set; }
    public int AssemblyId { get; set; }
    public string Assembly { get; set; }
    public string SeatType { get; set; }
    public int MaleVoters { get; set; }
    public int FemaleVoters { get; set; }
    public int TotalPollingStations { get; set; }
    public int ResultPollingStations { get; set; }
    public int NamangiarPollingStations { get; set; }
    public string Namanigar { get; set; }
    public string LastAction { get; set; }
    public DateTime LastActionDate { get; set; }

    public virtual List<NamanigarResultDetailDTO> Details { get; set; }
    public int SymbolId { get; set; }
    public int PartyId { get; set; }
    public int SeatTypeId { get; set; }
    public int PhaseId { get; set; }
}

public class NamanigarResultDetailDTO
{
    public int NominationId { get; set; }
    public string Candidate { get; set; }
    public string Party { get; set; }
    public string Symbol { get; set; }
    public int Votes { get; set; }
    public int NamanigarVotes { get; set; }
    public int CandidateId { get; internal set; }
}