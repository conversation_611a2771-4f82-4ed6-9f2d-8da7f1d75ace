﻿using Microsoft.EntityFrameworkCore.Migrations;
using System;

namespace ElectionAppServer.Migrations.ApplicationDb
{
	public partial class PublishedBy : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.AlterColumn<DateTime>(
				 name: "ModifiedDate",
				 table: "LiveResults",
				 type: "datetime2",
				 nullable: false,
				 defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
				 oldClrType: typeof(DateTime),
				 oldType: "datetime2",
				 oldNullable: true);

			migrationBuilder.AddColumn<string>(
				 name: "PublishedByUser",
				 table: "LiveResults",
				 type: "varchar(450)",
				 unicode: false,
				 maxLength: 450,
				 nullable: true);

			migrationBuilder.AddColumn<DateTime>(
				 name: "PublishedDate",
				 table: "LiveResults",
				 type: "datetime2",
				 nullable: true);
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropColumn(
				 name: "PublishedByUser",
				 table: "LiveResults");

			migrationBuilder.DropColumn(
				 name: "PublishedDate",
				 table: "LiveResults");

			migrationBuilder.AlterColumn<DateTime>(
				 name: "ModifiedDate",
				 table: "LiveResults",
				 type: "datetime2",
				 nullable: true,
				 oldClrType: typeof(DateTime),
				 oldType: "datetime2");
		}
	}
}
