﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace ElectionAppServer.Migrations.ApplicationDb
{
    /// <inheritdoc />
    public partial class YouthVotersandAddForm45VerifiedStatus : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "YouthVoters",
                table: "PollingSchemes",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "Form45Verified",
                table: "Nominations",
                type: "bit",
                nullable: false,
                defaultValue: false);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "YouthVoters",
                table: "PollingSchemes");

            migrationBuilder.DropColumn(
                name: "Form45Verified",
                table: "Nominations");
        }
    }
}
