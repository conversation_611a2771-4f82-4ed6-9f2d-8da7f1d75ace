﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace ElectionAppServer.Migrations.ApplicationDb
{
	public partial class CandidateOneDistrictPerElection : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropIndex(
				 name: "IX_CandidateDistricts_ElectionId",
				 table: "CandidateDistricts");

			migrationBuilder.CreateIndex(
				 name: "IX_CandidateDistricts_ElectionId_CandidateId",
				 table: "CandidateDistricts",
				 columns: new[] { "ElectionId", "CandidateId" },
				 unique: true);
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropIndex(
				 name: "IX_CandidateDistricts_ElectionId_CandidateId",
				 table: "CandidateDistricts");

			migrationBuilder.CreateIndex(
				 name: "IX_CandidateDistricts_ElectionId",
				 table: "CandidateDistricts",
				 column: "ElectionId");
		}
	}
}
