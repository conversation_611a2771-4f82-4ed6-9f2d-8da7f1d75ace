﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using ElectionAppServer.DTO;
using ElectionAppServer.Model;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Identity;
using Syncfusion.Blazor.Grids;
using Syncfusion.Blazor.Notifications;
using Syncfusion.Blazor.Popups;

namespace ElectionAppServer.Pages.Admin;

public partial class UsersManagement
{
    private readonly string ADMINISTRATION_ROLE = "Administrators";

    // Options to display in the roles dropdown when editing a user
    //List<string> Options = new List<string>() { "Users", "Administrators", "<PERSON><PERSON>gar", "Producer", "Data Entry Operators" };
    private readonly List<string> AllRolesList = new()
    {
        "Administrators", "Producer", "Data Entry Operators", "Data Viewer", "IMM", "Ticker Desk", "Namanigar",
        "Bureau",
        "Report User", "News User", "Anchors"
    };

    private readonly List<GenderCls> gender_list = new()
    {
        new GenderCls { ID = 1, Text = "Male" },
        new GenderCls { ID = 2, Text = "Female" },
        new GenderCls { ID = 3, Text = "Transgender" }
    };

    // Collection to display the existing users
    private List<ApplicationUserDTO> ColUsers = new();

    private ClaimsPrincipal CurrentUser;
    public SfDialog dlgSelectCons;
    public bool IsSelectConsVisible = false;

    // Property used to add or edit the currently selected user
    private ApplicationUserDTO objUser = new();

    // To enable showing the Popup
    public bool ShowPopup;

    // To hold any possible errors
    private string strError = "";

    private SfToast ToastObj;

    private List<SelectOption> UserRoles = new();
    public bool IsNamaniagardChecked { get; set; } = false;

    public SfGrid<ConstituencyDTO> AvailableConsGrid { get; set; }

    public List<ConstituencyDTO> AvailableConstList { get; set; } = new();

    public bool CanAdd
    {
        get
        {
            if (AvailableConstList == null) return false;
            if (AvailableConstList.Count == 0) return false;
            if (AvailableConstList.Any(c => c.IsSelected)) return true;
            return false;
        }
    }

    public bool CanRemove
    {
        get
        {
            if (UserConstList == null) return false;
            if (UserConstList.Count == 0) return false;
            if (UserConstList.Any(c => c.IsSelected)) return true;
            return false;
        }
    }

    public SfDialog dlgForm { get; set; }

    public SfDialog dlgUserCons { get; set; }

    public bool IsUserConsVisible { get; set; } = false;

    public SfGrid<ConstituencyDTO> UserConsGrid { get; set; }

    public List<ConstituencyDTO> UserConstList { get; set; } = new();

    //public List<string> MultiVal { get; set; } = new List<string>();
    public SfGrid<ApplicationUserDTO> UserGrid { get; set; }

    [CascadingParameter] private Task<AuthenticationState> authenticationStateTask { get; set; }

    // Tracks the selected role for the currently selected user
    private string CurrentUserRole { get; } = "Users";

    //private void onChange(MultiSelectChangeEventArgs<List<string>> args)
    //{
    //   MultiVal = args.Value;
    //   StateHasChanged();

    //}

    public async Task AddToUserConsList()
    {
        var user = await _UserManager.FindByIdAsync(objUser.Id);
        var assemblyIds = new List<int>();
        var structureIds = new List<int>();

        foreach (var ac in AvailableConstList)
            if (ac.IsSelected)
            {
                assemblyIds.Add(ac.AssemblyId);
                structureIds.Add(ac.Id);
            }

        if (assemblyIds.Count > 0)
        {
            await Service.SaveUserConstituencies(user.Id, assemblyIds, structureIds);
            await dlgSelectCons.HideAsync();
            UserConstList = await Service.GetSelectedConstituencies(objUser.Id, state.state.ElectionId);
            await UserConsGrid.Refresh();
        }
        else
        {
            var mm = new ToastModel
            {
                Title = "Error",
                Content = "No Constituency is selected",
                CssClass = "e-toast-danger",
                ShowProgressBar = true,
                Timeout = 5000,
                ShowCloseButton = true
            };
            await ToastObj.ShowAsync(mm);
        }

        await Task.CompletedTask;
    }

    private async Task AssignCons()
    {
        if (UserConsGrid.SelectedRecords.Any())
        {
            var msg = await Service.AssigneConsToUser(objUser.Id, UserConsGrid.SelectedRecords);
            if (msg != "OK")
            {
                var mm = new ToastModel { Content = msg, Title = "Error", ShowProgressBar = true, Timeout = 10000 };
                await ToastObj.ShowAsync(mm);
            }
            else
            {
                UserConstList = await Service.GetSelectedConstituencies(objUser.Id, state.state.ElectionId);
            }
        }

        await Task.CompletedTask;
    }

    private async Task RemoveCons()
    {
        if (UserConsGrid.SelectedRecords.Any())
        {
            var msg = await Service.RemoveConsFromUser(objUser.Id, UserConsGrid.SelectedRecords);
            if (msg != "OK")
            {
                var mm = new ToastModel { Content = msg, Title = "Error", ShowProgressBar = true, Timeout = 10000 };
                await ToastObj.ShowAsync(mm);
            }
            else
            {
                UserConstList = await Service.GetSelectedConstituencies(objUser.Id, state.state.ElectionId);
            }
        }

        await Task.CompletedTask;
    }

    public void CustomizeCell(QueryCellInfoEventArgs<ConstituencyDTO> args)
    {
        if (args.Column.Field is "UrduTitle" or "Province" or "Division" or "District" or "Town" or "UC" or "Assembly")
            args.Cell.AddClass(new[] { "urdu-column" });
        //args.Cell.AddStyle(new string[] { "font-size:18px" });
    }

    public async Task GetUsers()
    {
        // clear any error messages
        strError = "";

        // Collection to hold users
        ColUsers = new List<ApplicationUserDTO>();

        // get users from _UserManager
        var users = _UserManager.Users.Select(x => new ApplicationUserDTO
        {
            Id = x.Id,
            UserNameId = x.UserName ?? "",
            FullName = (x.FullName ?? "").Trim(),
            Address = (x.Address ?? "").Trim(),
            PhoneNumber = (x.PhoneNumber ?? "").Trim(),
            PasswordHash = "*****",
            Gender = x.Gender ?? 1,
            District = (x.District ?? "").Trim(),
            IsActive = x.IsActive, Code = (x.Code ?? "").Trim(), EmpCode = (x.EmpCode ?? "").Trim()
        }).ToList();

        foreach (var item in users)
        {
            if (item.UserNameId != "<EMAIL>")
                ColUsers.Add(item);
            var user1 = await _UserManager.FindByIdAsync(item.Id);
            //item.IsNamanigar = await _UserManager.IsInRoleAsync(user1, "Namanigar") || await _UserManager.IsInRoleAsync(user1, "Data Entry Operators");
            item.IsNamanigar = await _UserManager.IsInRoleAsync(user1, "Data Entry Operators") ||
                               await _UserManager.IsInRoleAsync(user1, "Data Viewer") ||
                               await _UserManager.IsInRoleAsync(user1, "Namanigar");
            item.Roles = "";
            foreach (var op in AllRolesList)
            {
                var ir = await _UserManager.IsInRoleAsync(user1, op);
                if (ir)
                    item.Roles += op + ", ";
            }

            if (item.Roles != "")
                item.Roles.Substring(0, item.Roles.Length - 2);
        }

        try
        {
            await UserGrid.Refresh();
        }
        catch (Exception)
        {
            // ignored
            // if any exception ignore
        }

        //GetUsers();
        await Task.CompletedTask;
    }

    public async Task OpenSelectConsDialog()
    {
        var user = await _UserManager.FindByIdAsync(objUser.Id);
        try
        {
            AvailableConstList = await Service.GetAvailableConstituencies(user.Id, state.state.ElectionId);
            await dlgSelectCons.ShowAsync();
            await AvailableConsGrid.Refresh();
            StateHasChanged();
        }
        catch (Exception ex)
        {
            var mm = new ToastModel
            {
                Title = "Error",
                Content = ex.Message,
                CssClass = "e-toast-danger",
                ShowProgressBar = true,
                Timeout = 5000,
                ShowCloseButton = true
            };
            if (ex.InnerException != null)
                mm.Content += " Detail: " + ex.InnerException.Message;
            await ToastObj.ShowAsync(mm);
        }
    }

    public async Task RemoveSelectedUserCons()
    {
        var paras = new[] { "Are you sure want to remove selected constituencies" };
        var res = await JSRuntime.InvokeAsync<bool>("confirm", paras);
        if (!res)
            return;

        var strs = new List<int>();
        var asms = new List<int>();

        foreach (var c in UserConstList)
            if (c.IsSelected)
            {
                asms.Add(c.AssemblyId);
                strs.Add(c.Id);
            }

        if (asms.Count > 0)
        {
            await Service.RemoveUserConstituencies(objUser.Id, asms, strs);
            UserConstList = await Service.GetSelectedConstituencies(objUser.Id, state.state.ElectionId);
            await UserConsGrid.Refresh();
        }
        else
        {
            var mm = new ToastModel
            {
                Title = "Error",
                Content = "No constituency is selected",
                CssClass = "e-toast-danger",
                ShowProgressBar = true,
                Timeout = 5000,
                ShowCloseButton = true
            };
            await ToastObj.ShowAsync(mm);
        }
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        //return base.OnAfterRenderAsync(firstRender);
        if (firstRender)
        {
            var user = (await authenticationStateTask).User;
            if (user.Identity.IsAuthenticated)
            {
                //ElectionsList = await Service.GetAll();
                //var dd =  await ProtectedSessionStore.GetAsync<ElectionStateDTO>("election");
                //var dd = await localStorage.GetItemAsync<ElectionStateDTO>(confreader.GetSettingValue("electionkey").Result);
                var dd = await localStorage.GetAsync<ElectionStateDTO>(await confreader.GetSettingValue("electionkey"));
                var mm = firstRender;
                state.SetState(dd.Value);
            }
            else
            {
                try
                {
                    //await  ProtectedSessionStore.DeleteAsync("election");
                    await localStorage.DeleteAsync(await confreader.GetSettingValue("electionkey"));
                    state.SetState(
                        new ElectionStateDTO { ElectionId = 0, ElectionTitle = "", PhaseId = 0, PhaseTitle = "" });
                }
                catch (Exception)
                {
                    // ignored
                }
            }

            StateHasChanged();
        }

        await Task.CompletedTask;
    }

    protected override async Task OnInitializedAsync()
    {
        try
        {
            foreach (var r in AllRolesList) await CreateUpdateRole(r);
        }
        catch (Exception)
        {
            // ignored
        }

        UserRoles = new List<SelectOption>();
        foreach (var r in AllRolesList) UserRoles.Add(new SelectOption { ID = r, Text = r });

        // Ensure a <NAME_EMAIL> is an Administrator
        //var user = await _UserManager.FindByNameAsync("<EMAIL>");
        //if (user != null)
        //{
        //   // Is <EMAIL> in administrator role?
        //   var UserResult = await _UserManager.IsInRoleAsync(user, ADMINISTRATION_ROLE);
        //   if (!UserResult)
        //   {
        //      // Put admin in Administrator role
        //      await _UserManager.AddToRoleAsync(user, ADMINISTRATION_ROLE);
        //   }
        //}

        // Get the current logged in user
        CurrentUser = (await authenticationStateTask).User;

        // Get the users
        await GetUsers();
        await Task.CompletedTask;
    }

    private async Task AddNewUserAsync()
    {
        // Make new user
        objUser = new ApplicationUserDTO
        {
            PasswordHash = "*****",
            Id = "",
            Address = "",
            District = "",
            FullName = "",
            PhoneNumber = "",
            UserNameId = ""
        };
        //objUser.PasswordHash = "*****";

        // Set Id to blank so we know it is a new record
        //objUser.Id = "";

        // Open the Popup
        ShowPopup = true;
        await dlgForm.ShowAsync();
    }

    private void beforeOpen(BeforeOpenEventArgs args)
    {
        args.MaxHeight = null;
    }

    //private void ClosePopup()
    //{
    //	// Close the Popup
    //	ShowPopup = false;
    //	dlgForm.HideAsync();
    //	//MultiVal = new List<string>();
    //	StateHasChanged();
    //}

    private async Task CreateUpdateRole(string role)
    {
        // ensure there is a ADMINISTRATION_ROLE
        var roleResult = await _RoleManager.FindByNameAsync(role);
        if (roleResult == null) await _RoleManager.CreateAsync(new IdentityRole(role));
        await Task.CompletedTask;
    }

    private async Task DeleteUser(string id, string username)
    {
        var paras = new[] { $"Are you sure want to delete {username}'s Account?" };
        var res = await JSRuntime.InvokeAsync<bool>("confirm", paras);
        if (!res)
            return;

        // Close the Popup
        ShowPopup = false;

        // Get the user
        var user = await _UserManager.FindByIdAsync(id);
        if (user != null)
        {
            var cons = await Service.HasConstituencies(id);

            // Delete the user
            try
            {
                if (cons) throw new Exception("One or more constituencies are assigned to this user");
                await _UserManager.DeleteAsync(user);
                await GetUsers();
                await dlgForm.HideAsync();
                //MultiVal = new List<string>();
                StateHasChanged();
            }
            catch (Exception ex)
            {
                var mm = new ToastModel
                {
                    Title = "Error",
                    Content = ex.Message,
                    CssClass = "e-toast-danger",
                    ShowProgressBar = true,
                    Timeout = 5000,
                    ShowCloseButton = true
                };
                if (ex.InnerException != null) mm.Content += "Detail: " + ex.InnerException.Message;
                await ToastObj.ShowAsync(mm);
            }
        }

        await Task.CompletedTask;
        // Refresh Users
    }

    private async Task DisableAccount(string id, string username)
    {
        var paras = new[] { $"Are you sure want to DISABLE {username}'s Account?" };
        var res = await JSRuntime.InvokeAsync<bool>("confirm", paras);
        if (!res)
            return;

        var user = await _UserManager.FindByIdAsync(id);
        if (user != null)
            try
            {
                user.IsActive = false;
                await _UserManager.UpdateAsync(user);
                await GetUsers();
                await dlgForm.HideAsync();
                StateHasChanged();
            }
            catch (Exception ex)
            {
                var mm = new ToastModel
                {
                    Title = "Error",
                    Content = ex.Message,
                    CssClass = "e-toast-danger",
                    ShowProgressBar = true,
                    Timeout = 5000,
                    ShowCloseButton = true
                };
                if (ex.InnerException != null) mm.Content += "Detail: " + ex.InnerException.Message;
                await ToastObj.ShowAsync(mm);
            }

        await Task.CompletedTask;
    }

    private async Task EditUser(ApplicationUserDTO _IdentityUser)
    {
        // Set the selected user
        // as the current user
        objUser = _IdentityUser;

        // Get the user
        var user = await _UserManager.FindByIdAsync(objUser.Id);
        //objUser.IsNamanigar = await _UserManager.IsInRoleAsync(user, "Namanigar");
        if (user != null)
            //MultiVal = new List<string>();
            foreach (var r in UserRoles)
            {
                var UserResult =
                    await _UserManager
                        .IsInRoleAsync(user, r.ID);

                r.IsSelected = UserResult;
            }

        //// Is user in administrator role?
        //var UserResult =
        //    await _UserManager
        //    .IsInRoleAsync(user, ADMINISTRATION_ROLE);

        //if (UserResult)
        //{
        //   CurrentUserRole = ADMINISTRATION_ROLE;
        //}
        //else
        //{
        //   CurrentUserRole = "Users";
        //}

        // Open the Popup

        ShowPopup = true;
        StateHasChanged();
        await dlgForm.ShowAsync();
        StateHasChanged();
        await Task.CompletedTask;
    }

    private async Task EnableAccount(string id, string username)
    {
        var paras = new[] { $"Are you sure want to ENABLE {username}'s Account?" };
        var res = await JSRuntime.InvokeAsync<bool>("confirm", paras);
        if (!res)
            return;

        var user = await _UserManager.FindByIdAsync(id);
        if (user != null)
            try
            {
                user.IsActive = true;
                await _UserManager.UpdateAsync(user);
                await GetUsers();
                await dlgForm.HideAsync();
                StateHasChanged();
            }
            catch (Exception ex)
            {
                var mm = new ToastModel
                {
                    Title = "Error",
                    Content = ex.Message,
                    CssClass = "e-toast-danger",
                    ShowProgressBar = true,
                    Timeout = 5000,
                    ShowCloseButton = true
                };
                if (ex.InnerException != null) mm.Content += "Detail: " + ex.InnerException.Message;
                await ToastObj.ShowAsync(mm);
            }

        await Task.CompletedTask;
    }

    private async Task GetUserCons(ApplicationUserDTO user)
    {
        objUser = new ApplicationUserDTO
        {
            Address = user.Address,
            District = user.District,
            UserNameId = user.UserNameId,
            FullName = user.FullName,
            Gender = user.Gender,
            Id = user.Id,
            IsNamanigar = user.IsNamanigar
        };
        await dlgUserCons.ShowAsync();
        //await UserConstList = await service.GetUserConst(userId);
        UserConstList = await Service.GetSelectedConstituencies(user.Id, state.state.ElectionId);
        try
        {
            await UserConsGrid.Refresh();
        }
        catch (Exception)
        {
            // ignored
        }

        StateHasChanged();
        await Task.CompletedTask;
    }

    private async Task SaveUser()
    {
        #region Validation

        strError = "";
        var errs = new List<string>();
        if (objUser.UserNameId == null || objUser.UserNameId.Trim() == "") errs.Add("Email");

        if (objUser.FullName == null || objUser.FullName.Trim() == "") errs.Add("Name");
        if (objUser.Gender == null) errs.Add("Gender");

        if (string.IsNullOrEmpty(CurrentUserRole)) errs.Add("User Role");

        if (errs.Count != 0)
        {
            if (errs.Count == 1)
            {
                strError = "Following field is required: " + errs[0];
                return;
            }

            var msg = "";
            foreach (var er in errs) msg += er + ", ";
            msg = msg.Substring(0, msg.Length - 1);
            strError = "Following fields are required: " + msg;
            return;
        }

        #endregion Validation

        try
        {
            // Is this an existing user?
            if (objUser.Id != "")
            {
                #region Update User Information

                // Get the user
                var user = await _UserManager.FindByIdAsync(objUser.Id);

                // Update Email
                user.Email = objUser.Id.Trim();
                user.PhoneNumber = (objUser.PhoneNumber ?? "").Trim();
                user.Address = (objUser.Address ?? "").Trim();
                user.FullName = objUser.FullName.Trim();
                user.Gender = objUser.Gender;
                user.District = (objUser.District ?? "").Trim();
                user.Code = (objUser.Code ?? "").Trim();
                user.EmpCode = (objUser.EmpCode ?? "").Trim();

                // Update the user
                await _UserManager.UpdateAsync(user);

                #endregion Update User Information

                #region Update Password

                // Only update password if the current value
                // is not the default value
                if (objUser.PasswordHash != "*****")
                {
                    var resetToken =
                        await _UserManager.GeneratePasswordResetTokenAsync(user);

                    var passwordUser =
                        await _UserManager.ResetPasswordAsync(
                            user,
                            resetToken,
                            objUser.PasswordHash);

                    if (!passwordUser.Succeeded)
                    {
                        if (passwordUser.Errors.FirstOrDefault() != null)
                            strError =
                                passwordUser
                                    .Errors
                                    .FirstOrDefault()
                                    .Description;
                        else
                            strError = "Password error";

                        // Keep the popup opened
                        return;
                    }
                }

                #endregion Update Password

                foreach (var r in UserRoles)
                    try
                    {
                        await _UserManager.RemoveFromRoleAsync(user, r.ID);
                    }
                    catch (Exception)
                    {
                        // ignored
                    }

                foreach (var r in UserRoles)
                    try
                    {
                        if (r.IsSelected)
                            await _UserManager.AddToRoleAsync(user, r.ID);
                    }
                    catch (Exception)
                    {
                        // ignored
                    }
            }
            else
            {
                #region Create New User

                // Insert new user

                var newUser =
                    new ApplicationUser
                    {
                        UserName = objUser.UserNameId.ToLower().Trim(),
                        Email = objUser.Id.ToLower().Trim(),
                        EmailConfirmed = true,
                        FullName = objUser.FullName.Trim(),
                        PhoneNumber = (objUser.PhoneNumber ?? "").Trim(),
                        Address = (objUser.Address ?? "").Trim(),
                        Gender = objUser.Gender,
                        District = (objUser.District ?? "").Trim(),
                        Code = (objUser.Code ?? "").Trim(),
                        EmpCode = (objUser.EmpCode ?? "").Trim()
                    };

                var createResult =
                    await _UserManager
                        .CreateAsync(newUser, objUser.PasswordHash);

                #endregion Create New User

                if (!createResult.Succeeded)
                {
                    #region Unable to create user profile

                    if (createResult
                            .Errors
                            .FirstOrDefault() != null)
                        strError =
                            createResult
                                .Errors
                                .FirstOrDefault()
                                .Description;
                    else
                        strError = "Create error";

                    // Keep the popup opened
                    return;

                    #endregion Unable to create user profile
                }

                foreach (var r in UserRoles)
                    try
                    {
                        if (r.IsSelected)
                            await _UserManager.AddToRoleAsync(newUser, r.ID);
                    }
                    catch (Exception)
                    {
                        // ignored
                    }

                //#region Add User Roles
                //// Handle Roles
                //if (CurrentUserRole == ADMINISTRATION_ROLE)
                //{
                //   // Put admin in Administrator role
                //   await _UserManager
                //       .AddToRoleAsync(NewUser, ADMINISTRATION_ROLE);
                //}
                //#endregion
            }

            // Close the Popup
            ShowPopup = false;
            await dlgForm.HideAsync();
            // Refresh Users
            await GetUsers();
            //MultiVal = new List<string>();
            StateHasChanged();
        }
        catch (Exception ex)
        {
            strError = ex.GetBaseException().Message;
        }

        await Task.CompletedTask;
    }

    public async Task OnNamaniar_Cheked(EventArgs e)
    {
        await Task.CompletedTask;
    }

    public class SelectOption
    {
        public string ID { get; set; }
        public bool IsSelected { get; set; }
        public string Text { get; set; }
    }
}