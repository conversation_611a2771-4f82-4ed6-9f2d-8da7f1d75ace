﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using BlazorInputFile;
using ElectionAppServer.Data;
using ElectionAppServer.DTO;
using ElectionAppServer.Model;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.SignalR.Client;
using Microsoft.JSInterop;
using SixLabors.ImageSharp;
using Syncfusion.Blazor.Grids;
using Syncfusion.Blazor.Notifications;
using Syncfusion.Blazor.Popups;
using Action = Syncfusion.Blazor.Grids.Action;

namespace ElectionAppServer.Pages.Setup;

public partial class Candidates
{
    //->Not in use //private string FormTitle = "Create Candidate";
    private readonly List<GenderCls> gender_list = new()
    {
        new GenderCls { ID = 1, Text = "Male" },
        new GenderCls { ID = 2, Text = "Female" },
        new GenderCls { ID = 3, Text = "Transgender" }
    };

    private List<Caste> _castesList = new();
    private List<GeneralItemDTO> _districtsList = new();

    //private HubConnection connection;
    private SfDialog _dlgForm;

    private List<GeneralItemDTO> _educationsList = new();
    private IFileListEntry _file;

    private HubConnection _hubConnection;
    private bool _importMode;

    //--> No in use //private ElementReference inputElement;
    private bool _isDlgVisible;

    private List<Language> _languagesList = new();
    private List<CandidateDTO> _objFilteredList;
    private List<CandidateDTO> _objList;
    private List<Party> _partiesList = new();
    private List<Profession> _professionsList = new();

    //--> No in use //private string SaveButtonText = "Save";
    private CandidateDTO _selectedObj = new() { CandidateType = "1" };

    //--> No in use //private List<SymbolDTO> symbols;
    private SfToast _toastObj;
    public long max;

    public string ToastContent = "Unable to delete Record, Related Record(s) Exist";

    public long value;

    public string BaseURL { get; set; }

    public List<GeneralItemDTO> CandidateTypesList { get; set; } = new()
    {
        new GeneralItemDTO { Id = 1, EnglishTitle = "Candidate" },
        new GeneralItemDTO { Id = 2, EnglishTitle = "Panel" }
    };

    public bool DisableButton { get; set; }

    public bool DisplayTab { get; set; } = false;

    public ElectionType ElectionType { get; set; }

    public string FileBase64String { get; set; }

    public byte[] FileByteArray { get; set; }

    public List<GeneralItemDTO> fresh_exp_list { get; set; } = new()
    {
        new GeneralItemDTO { Id = 1, EnglishTitle = "Fresh" },
        new GeneralItemDTO { Id = 2, EnglishTitle = "Experienced" }
    };

    public SfGrid<CandidateDTO> Grid { get; set; }

    public SfGrid<CandidateElection> GridHistory { get; set; }

    public SfGrid<PartyAffiliationDTO> GridPA { get; set; }

    public SfGrid<PoliticalCareerDTO> GridPC { get; set; }

    public bool IsConnected => _hubConnection.State == HubConnectionState.Connected;

    public string mediapath { get; set; }

    [CascadingParameter] private Task<AuthenticationState> authenticationStateTask { get; set; }

    private string FilterCode { get; set; } = string.Empty;

    private string FilterText { get; set; } = string.Empty;

    public void ActionCompletedHandler(ActionEventArgs<PartyAffiliationDTO> args)
    {
        if (args.RequestType == Action.Cancel ||
            args.RequestType == Action.Refresh ||
            args.RequestType == Action.Reorder ||
            args.RequestType == Action.Delete ||
            args.RequestType == Action.Save)
            DisableButton = false;
        else
            DisableButton = true;
        // Here you can customize your code
        //if (args.Action == "add")
        //{
        //	//var gg = Guid.NewGuid().ToString();
        //	//args.Data.UI_Id = gg.ToString();
        //	StateHasChanged();
        //}
    }

    public void ActionCompletedHandlerPC(ActionEventArgs<PoliticalCareerDTO> args)
    {
        if (args.RequestType == Action.Cancel ||
            args.RequestType == Action.Refresh ||
            args.RequestType == Action.Reorder ||
            args.RequestType == Action.Delete ||
            args.RequestType == Action.Save)
            DisableButton = false;
        else
            DisableButton = true;
        //// Here you can customize your code
        //if (args.Action == "add")
        //{
        //	//var gg = Guid.NewGuid().ToString();
        //	//args.Data.UI_Id = gg.ToString();
        //	StateHasChanged();
        //}
    }

    // List<Games> gender_list = new List<Games> {
    //  new Games() { ID= "1", Text= "American Football" },
    //  new Games() { ID= "Game2", Text= "Badminton" },
    //  new Games() { ID= "Game3", Text= "Basketball" },
    //  new Games() { ID= "Game4", Text= "Cricket" },
    //  new Games() { ID= "Game5", Text= "Football" },
    //  new Games() { ID= "Game6", Text= "Golf" },
    //  new Games() { ID= "Game7", Text= "Hockey" },
    //  new Games() { ID= "Game8", Text= "Rugby"},
    //  new Games() { ID= "Game9", Text= "Snooker" },
    //  new Games() { ID= "Game10", Text= "Tennis"},
    //};
    public void CustomizeCell(QueryCellInfoEventArgs<CandidateDTO> args)
    {
        if (args.Column.Field == "UrduName")
        {
            args.Cell.AddClass(new[] { "urdu-column" });
            args.Cell.AddStyle(new[] { "font-size:18px;line-height: 1.4;text-align: right" });
        }
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        //return base.OnAfterRenderAsync(firstRender);
        if (firstRender)
        {
            var user = (await authenticationStateTask).User;
            if (user.Identity.IsAuthenticated)
            {
                //var dd =  await ProtectedSessionStore.GetAsync<ElectionStateDTO>("election");
                var dd = await localStorage.GetAsync<ElectionStateDTO>(await confreader.GetSettingValue("electionkey"));
                ElectionType = dd.Value.ElectionType.Value;
                state.SetState(dd.Value);
                await LoadData();
            }
            else
            {
                try
                {
                    //await  ProtectedSessionStore.DeleteAsync("election");
                    await localStorage.DeleteAsync(await confreader.GetSettingValue("electionkey"));
                    state.SetState(
                        new ElectionStateDTO
                        {
                            ElectionId = 0,
                            ElectionTitle = string.Empty,
                            PhaseId = 0,
                            PhaseTitle = string.Empty
                        });
                }
                catch (Exception)
                {
                    // ignored
                }

                NavigationManager.NavigateTo(string.Empty);
            }

            StateHasChanged();
        }
    }

    protected override async Task OnInitializedAsync()
    {
        _hubConnection = new HubConnectionBuilder()
            .WithUrl(NavigationManager.ToAbsoluteUri("/candidatehub"))
            .Build();

        _hubConnection.On<CandidateDTO>(
            "OnUpdateNomination",
            cc =>
            {
                //CandidateDTO cc = await Service.GetCandidateNominations(state.state.ElectionId, candidateId);
                if (_objList != null)
                {
                    var kk = _objList.Find(c => c.Id == cc.Id);
                    if (kk != null)
                    {
                        kk.Id = cc.Id;
                        kk.FullName = cc.FullName;
                        kk.CandidateTypeStr = cc.CandidateTypeStr;
                        kk.EnglishName = cc.EnglishName;
                        kk.Gender = cc.Gender;
                        kk.UrduFatherName = cc.UrduFatherName;
                        kk.UrduName = cc.UrduName;
                        kk.CreatedBy = cc.CreatedBy;
                        kk.ModifiedBy = cc.ModifiedBy;
                        kk.CreatedOn = cc.CreatedOn;
                        kk.Constituencies = cc.Constituencies;
                        kk.DisableDelete = cc.DisableDelete;
                        kk.District = cc.District;
                        kk.PictureURL = cc.PictureURL;
                        kk.CreatedOn = cc.CreatedOn;
                        kk.ModifiedOn = cc.ModifiedOn;
                        kk.DateOfBirthStr = cc.DateOfBirthStr;
                        kk.IsFreshStr = cc.IsFreshStr;
                        _objFilteredList = ApplyFilter();
                    }

                    StateHasChanged();
                }
            });

        await _hubConnection.StartAsync();
        //await Task.CompletedTask;
    }

    private List<CandidateDTO> ApplyFilter()
    {
        //var cc = new List<CandidateDTO>();
        var txt = (FilterText ?? string.Empty).Trim().ToLower();
        var cod = 0;
        try
        {
            cod = int.Parse(FilterCode);
        }
        catch (Exception)
        {
            // ignored
        }

        var q = (from aa in _objList
            where ((aa.EnglishName ?? string.Empty).ToLower().Contains(txt) ||
                   (aa.UrduName ?? string.Empty).Contains(txt) ||
                   (aa.CurrentParty ?? string.Empty).ToLower().Contains(txt) ||
                   (aa.District ?? string.Empty).ToLower().Contains(txt) ||
                   (aa.Constituencies ?? string.Empty).ToLower().Contains(txt)) &&
                  (cod == 0 || aa.Id == cod)
            select aa).ToList();

        return q;
    }

    private async Task BackToSelected()
    {
        _importMode = false;
        await RefreshCandidatesList();
    }

    private void beforeOpen(BeforeOpenEventArgs args)
    {
        args.MaxHeight = null;
    }

    private void ClearData()
    {
        FileBase64String = string.Empty;
        //SaveButtonText = "Create";
        FileBase64String = string.Empty;
        FileByteArray = new byte[0];
    }

    private async Task DeleteRecord(int Id)
    {
        var cid = 0;
        int.TryParse(FilterCode, out cid);
        var para = new[] { "Are you want to delete this record?" };
        var res = await JSRuntime.InvokeAsync<bool>("confirm", para);
        if (res == false)
            return;
        try
        {
            await Service.Delete(Id, state.state.ElectionId);
            _objList = await Service.GetAllCandidates(state.state.ElectionId, cid, FilterText);
            _objFilteredList = ApplyFilter();
            var cc = new CandidateDTO { Id = Id };
            //await connection.InvokeAsync<CandidateDTO>("DeleteRec", cc);
            await _hubConnection.SendAsync("RemoveCandidate", Id);
            await Grid.Refresh();
            StateHasChanged();
        }
        catch (Exception ex)
        {
            var m = new ToastModel
            {
                Content = ex.Message,
                Title = "Error",
                CssClass = "e-toast-danger",
                ShowProgressBar = true,
                Timeout = 5000,
                ShowCloseButton = true
            };
            await _toastObj.ShowAsync(m);
        }
    }

    private async Task DoSearch()
    {
        await RefreshCandidatesList();
        //_objFilteredList = ApplyFilter();
        //try
        //{
        //	await Grid.Refresh();
        //}
        //catch (Exception)
        //{
        //	// ignored
        //}
    }

    private async Task HandleFileSelected(IFileListEntry[] files)
    {
        _file = files.FirstOrDefault();
        var dd = await _file.ReadAllAsync();
        FileByteArray = dd.ToArray();
        FileBase64String = Convert.ToBase64String(FileByteArray);
    }

    private async Task LoadData()
    {
        var id = 0;
        int.TryParse(FilterCode, out id);
        _objList = await Service.GetAllCandidates(state.state.ElectionId, id, FilterText); //new List<CandidateDTO>();

        _objFilteredList = ApplyFilter();
        _partiesList = await Service.GetAllParties();
        _professionsList = await Service.GetAllProfessions();
        _languagesList = await Service.GetAllLanguages();
        _castesList = await Service.GetAllCastes();
        //districts_list = await Service.GetAllDistricts();
        _districtsList = await Service.GetElectionWiseDistricts(state.state.ElectionId);
        _educationsList = await Service.GetAllEducations();
        mediapath = env.WebRootPath + "\\media\\"; // await confreader.GetSettingValue("mediapath");
        BaseURL = await confreader.GetSettingValue("BaseURL");
        //try
        //{
        //    await Grid.Refresh();
        //}
        //catch (Exception)
        //{
        //}
        StateHasChanged();
    }

    private void CalculateDOB()
    {
        // calculate date of birth from age
        var r = new Random().Next(0, 180);
        var dateOfBirth = DateTime.Now.AddYears(-(_selectedObj.Age ?? 0)).AddDays(-r);
        _selectedObj.DateOfBirth = dateOfBirth;
        _selectedObj.Day = dateOfBirth.Day;
        _selectedObj.Month = dateOfBirth.Month;
        _selectedObj.Year = dateOfBirth.Year;
    }

    private async Task On_ReceiveDelMessage(CandidateDTO cc)
    {
        var k = (from o in _objList where o.Id == cc.Id select o).FirstOrDefault();
        if (k != null) _objList.Remove(k);
        StateHasChanged();
        try
        {
            await Grid.Refresh();
        }
        catch (Exception)
        {
            // ignored
        }
        //return Task.CompletedTask;
    }

    private async Task On_ReceiveMessage(CandidateDTO cc1)
    {
        //Messages.Add(new Msg { Text = message, User = user, SendDate = DateTime.Now });
        var k = (from o in _objList where o.Id == cc1.Id select o).FirstOrDefault();
        await Task.CompletedTask;
        var cc = await Service.GetCandidateInfo(cc1.Id, state.state.ElectionId);
        await Task.CompletedTask;
        // if record not found in list
        if (k == null)
        {
            CandidateDTO mm = new()
            {
                Id = cc.Id,
                EnglishName = cc.EnglishName,
                CandidateType = cc.CandidateType,
                CasteId = cc.CasteId,
                ContactNo = cc.ContactNo,
                CurrentParty = cc.CurrentParty,
                DateOfBirth = cc.DateOfBirth,
                DistrictId = cc.DistrictId,
                EducationId = cc.EducationId,
                EnglishFatherName = cc.EnglishFatherName,
                Gender = cc.Gender,
                IsFresh = cc.IsFresh,
                LanguageId = cc.LanguageId,
                ProfessionId = cc.ProfessionId,
                CandidateProfession = cc.CandidateProfession,
                TotalAssets = cc.TotalAssets,
                Language = cc.Language,
                Education = cc.Education,
                Trivia = cc.Trivia,
                UrduFatherName = cc.UrduFatherName,
                UrduName = cc.UrduName,
                District = cc.District,
                CreatedBy = cc.CreatedBy,
                ModifiedBy = cc.ModifiedBy,
                CreatedOn = cc.CreatedOn,
                ModifiedOn = cc.ModifiedOn
            };
            //var mm = await Service.GetCandidateInfo(cc.Id);
            _objList.Add(mm);
        }
        else
        {
            k.EnglishName = cc.EnglishName;
            k.CandidateType = cc.CandidateType;
            k.CasteId = cc.CasteId;
            k.ContactNo = cc.ContactNo;
            k.CurrentParty = cc.CurrentParty;
            k.DateOfBirth = cc.DateOfBirth;
            k.DistrictId = cc.DistrictId;
            k.EducationId = cc.EducationId;
            k.EnglishFatherName = cc.EnglishFatherName;
            k.Gender = cc.Gender;
            k.IsFresh = cc.IsFresh;
            k.LanguageId = cc.LanguageId;
            k.ProfessionId = cc.ProfessionId;
            k.CandidateProfession = cc.CandidateProfession;
            k.TotalAssets = cc.TotalAssets;
            k.Language = cc.Language;
            k.Education = cc.Education;
            k.Trivia = cc.Trivia;
            k.UrduFatherName = cc.UrduFatherName;
            k.UrduName = cc.UrduName;
            k.District = cc.District;
            k.CurrentParty = cc.CurrentParty;
            k.CreatedOn = cc.CreatedOn;
            k.CreatedBy = cc.CreatedBy;
            k.ModifiedOn = cc.ModifiedOn;
            k.ModifiedBy = cc.ModifiedBy;
        }

        _objList = _objList.OrderBy(c => c.EnglishName).ToList();
        try
        {
            await Grid.Refresh();
        }
        catch (Exception)
        {
            // ignored
        }

        //try
        //{
        //	GridPA.Refresh();
        //}
        //catch (Exception)
        //{
        //}
        //try
        //{
        //	GridPC.Refresh();
        //}
        //catch (Exception)
        //{
        //}
        StateHasChanged();
        await Task.CompletedTask;
    }

    private void OpenCreateForm()
    {
        _selectedObj = new CandidateDTO
        {
            Gender = 1, //Gender.Male,
            DateOfBirth = DateTime.Today,
            //CandidateType = CandidateType.Politician,
            CandidateType = ((int)NominationType.Candidate).ToString(),
            PartyAffiliations = new List<PartyAffiliationDTO>(),
            PoliticalCareers = new List<PoliticalCareerDTO>(),
            UrduName = string.Empty,
            EnglishFatherName = string.Empty,
            UrduFatherName = string.Empty,
            EnglishName = string.Empty,
            IsFresh = 2
        };
        _dlgForm.ShowAsync();
        ClearData();
    }

    private async Task OpenEditForm(RecordDoubleClickEventArgs<CandidateDTO> args)
    {
        await OpenEditForm(args.RowData);
    }

    private async Task OpenEditForm(CandidateDTO st)
    {
        _selectedObj = await Service.GetCandidateInfo(st.Id, state.state.ElectionId);
        FileBase64String = string.Empty;
        FileByteArray = Array.Empty<byte>();
        try
        {
            var f = mediapath + @"\candidates\" + st.Id + ".jpg";
            var fs = File.OpenRead(f);
            var fileBytes = new byte[fs.Length];
            fs.Read(fileBytes, 0, fileBytes.Length);
            fs.Close();
            FileBase64String = Convert.ToBase64String(fileBytes);
        }
        catch (Exception)
        {
            // ignored
        }

        await _dlgForm.ShowAsync();
        //SaveButtonText = "Update";
    }

    private async Task OpenImportForm()
    {
        var id = 0;
        int.TryParse(FilterCode, out id);
        _importMode = true;
        _objList = await Service.GetAllCandidates(state.state.ElectionId, id, FilterText);
        await Task.CompletedTask;
    }

    private async Task RefreshCandidatesList()
    {
        var id = 0;
        int.TryParse(FilterCode, out id);

        _objList = await Service.GetAllCandidates(state.state.ElectionId, id, FilterText);

        _objFilteredList = ApplyFilter();
        try
        {
            await Grid.Refresh();
        }
        catch (Exception)
        {
            // ignored
        }
    }

    private async Task RemovePic()
    {
        var id = 0;
        int.TryParse(FilterCode, out id);
        if (_selectedObj.Id != 0)
        {
            var confirm = await JSRuntime.InvokeAsync<bool>(
                "confirm",
                new object[] { "Are you sure want to remove this picture?" });
            if (confirm)
                try
                {
                    File.Delete(mediapath + @"\candidates\" + _selectedObj.Id + ".jpg");
                    await _dlgForm.HideAsync();
                    _objList = await Service.GetAllCandidates(state.state.ElectionId, id, FilterText);
                    _objFilteredList = ApplyFilter();
                }
                catch (Exception ex)
                {
                    await JSRuntime.InvokeVoidAsync(
                        "alert", "Unable to delete this picture. Detail: " + ex.Message);
                }
        }
    }

    private void SaveAffiliation()
    {
    }

    private async Task SaveCandidateData()
    {
        if (FileByteArray is { Length: > 0 })
        {
            using var img = Image.Load(FileByteArray);
            if (img.Height == img.Width && img.Height < 512)
            {
                var tm = new ToastModel
                {
                    Title = "Error",
                    Content = "Please select 512x521 or higher pixels image",
                    CssClass = "e-toast-danger",
                    ShowProgressBar = true,
                    Timeout = 5000,
                    ShowCloseButton = true
                };
                await _toastObj.ShowAsync(tm);
                return;
            }
        }

        var user = (await authenticationStateTask).User;
        if (_selectedObj.Day != null && _selectedObj.Month != null && _selectedObj.Year != null)
            try
            {
                var dd = new DateTime(_selectedObj.Year.Value, _selectedObj.Month.Value, _selectedObj.Day.Value);
            }
            catch (Exception)
            {
                // Invalid date of birth
                var mm = new ToastModel
                {
                    Title = "Error",
                    Content = "Invalid date of birth",
                    CssClass = "e-toast-danger",
                    ShowProgressBar = true,
                    Timeout = 5000,
                    ShowCloseButton = true
                };
                await _toastObj.ShowAsync(mm);
                return;
            }

        try
        {
            var id = 0;
            int.TryParse(FilterCode, out id);
            //var kk = selectedObj;
            var res = await Service.SaveCandidate(_selectedObj, user.Identity.Name, state.state.ElectionId);
            if (res.Id != 0)
            {
                if (FileByteArray is { Length: > 0 })
                    File.WriteAllBytes(mediapath + @"\candidates\" + res.Id + ".jpg", FileByteArray);
                _objList = await Service.GetAllCandidates(state.state.ElectionId, id, FilterText);
                _objFilteredList = ApplyFilter();
                //StateHasChanged();
                await _dlgForm.HideAsync();
                try
                {
                    await Grid.Refresh();
                }
                catch (Exception)
                {
                    // ignored
                }

                //await connection.InvokeAsync("SendInfo", res);
                await _hubConnection.SendAsync("SendCandidateInfo", res);
            }
        }
        catch (Exception ex)
        {
            var mm = new ToastModel
            {
                Title = "Error",
                Content = ex.Message,
                CssClass = "e-toast-danger",
                ShowProgressBar = true,
                Timeout = 5000,
                ShowCloseButton = true
            };
            if (ex.InnerException != null)
                mm.Content = mm.Content + " - Detail: " + ex.InnerException.Message;
            await _toastObj.ShowAsync(mm);
        }
    }

    private async Task TranslateToEnglish2()
    {
        var englishName = _selectedObj.EnglishName;
        try
        {
            _selectedObj.EnglishName = await Translation.TranslateTextEnglish(_selectedObj.UrduName);
        }
        catch (Exception)
        {
            // ignored
            //selectedObj.EnglishName = englishName;
        }
    }

    private async Task TranslateToUrdu2()
    {
        var urduName = _selectedObj.UrduName;
        try
        {
            _selectedObj.UrduName = await Translation.TranslateText(_selectedObj.EnglishName);
        }
        catch (Exception)
        {
            // ignored
            //selectedObj.UrduName = urduName;
        }
    }

    private async Task TranslateToUrdu()
    {
        //if (string.IsNullOrEmpty(selectedObj.UrduFatherName))
        try
        {
            if (string.IsNullOrEmpty(_selectedObj.UrduName))
                _selectedObj.UrduName = await Translation.TranslateText(_selectedObj.EnglishName);
        }
        catch (Exception ex)
        {
            Console.WriteLine(ex.Message);
            if (ex.InnerException != null) Console.WriteLine("Detail: " + ex.InnerException.Message);
        }
    }

    private async Task TranslateToUrdu_father()
    {
        try
        {
            if (string.IsNullOrEmpty(_selectedObj.UrduFatherName))
                _selectedObj.UrduFatherName = await Translation.TranslateText(_selectedObj.EnglishFatherName);
        }
        catch (Exception ex)
        {
            Console.WriteLine(ex.Message);
            if (ex.InnerException != null) Console.WriteLine("Detail: " + ex.InnerException.Message);
        }
        //if (string.IsNullOrEmpty(selectedObj.UrduFatherName))
    }

    //private async Task AddCandidateToElection()
    //{
    //	List<int> candidateIds = new List<int>();
    //	var user = (await authenticationStateTask).User;
    //	foreach (var cc in objList)
    //	{
    //		if (cc.IsSelected)
    //		{
    //			candidateIds.Add(cc.Id);
    //		}
    //	}
    //	try
    //	{
    //		string res = await Service.AddRemoveCandidates(state.state.ElectionId, candidateIds, user.Identity.Name);
    //		ImportMode = false;
    //		objList = await Service.GetAllCandidates(state.state.ElectionId, electionType);
    //		await Task.CompletedTask;
    //	}
    //	catch (Exception ex)
    //	{
    //		var mm = new ToastModel { Title = "Error", Content = ex.Message, TimeOut = 10000 };
    //		await ToastObj.ShowAsync(mm);
    //	}
    //}
}