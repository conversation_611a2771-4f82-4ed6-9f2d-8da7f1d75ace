﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace ElectionAppServer.Migrations.ApplicationDb
{
    public partial class namanigarchangevotesfieldinlogentity : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "ChangeVotes",
                table: "NamanigarResults",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "ChangePollingStations",
                table: "NamanigarResultLogs",
                type: "int",
                nullable: false,
                defaultValue: 0);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ChangeVotes",
                table: "NamanigarResults");

            migrationBuilder.DropColumn(
                name: "ChangePollingStations",
                table: "NamanigarResultLogs");
        }
    }
}
