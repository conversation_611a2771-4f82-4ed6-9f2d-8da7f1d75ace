﻿using System;
using System.Collections.Generic;

namespace ElectionAppServer.DTO;

public class ResultLogDTO
{
    public ResultLogDTO()
    {
        PS = new List<PSLogDTO>();
        Votes = new List<VotesLogDTO>();
    }

    public List<PSLogDTO> PS { get; set; }
    public List<VotesLogDTO> Votes { get; set; }
}

public class PSLogDTO
{
    public string Title { get; set; } // Current, 1st Last, 2nd Last, 3rd Last
    public int? PSCount { get; set; }
    public string Action { get; set; }
    public string Namanigar { get; set; }
    public string User { get; set; }
    public DateTime? ActionDateTime { get; set; }
    public int LogId { get; set; }
}

public class VotesLogDTO
{
    public int CandidateId { get; set; }
    public string Candidate { get; set; }
    public int? Current { get; set; }
    public int? FirstLast { get; set; }
    public int? SecondLast { get; set; }
    public int? ThirdLast { get; set; }
}