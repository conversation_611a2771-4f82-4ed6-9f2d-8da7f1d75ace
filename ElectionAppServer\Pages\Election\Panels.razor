﻿@page "/panels"
@using ButtonType = MudBlazor.ButtonType
@using FilterType = Syncfusion.Blazor.Grids.FilterType
@using Size = MudBlazor.Size
@inherits OwningComponentBase<PanelDataService>
@attribute [Authorize(Roles = "Administrators,Data Viewer")]

<PageCaption Title="Assembly Wise Panalist"></PageCaption>

<SfToast @ref="ToastObj" Title="Alert" ShowProgressBar="true" Timeout="3000">
    <ToastPosition X="Right"></ToastPosition>
</SfToast>

<SfDialog IsModal="true" ShowCloseIcon="true" @ref="dlgForm" Visible="IsFormOpen" Width="700px">
    <DialogTemplates>
        <Header>Candidate Detail</Header>
        <Content>
            <EditForm Model="@selectedObj" OnValidSubmit="SavePanelCandidate">
                <DataAnnotationsValidator></DataAnnotationsValidator>
                <ValidationSummary/>
                <div class="row">
                    <div class="col">
                        <SfTextBox @bind-Value="selectedObj.CandidateEnglishName" Placeholder="Candidate Name (English)" FloatLabelType="FloatLabelType.Always" OnBlur="TranslateToUrdu"></SfTextBox>
                        <ValidationMessage For="@(() => selectedObj.CandidateEnglishName)"></ValidationMessage>
                    </div>
                    <div class="col">
                        <SfTextBox @bind-Value="selectedObj.CandidateUrduName" Placeholder="Candidate Name (اردو)" FloatLabelType="FloatLabelType.Always"></SfTextBox>
                        <ValidationMessage For="@(() => selectedObj.CandidateUrduName)"></ValidationMessage>
                    </div>

                    <div class="col">
                        <SfComboBox DataSource="seatType_list" TItem="GeneralItemDTO" TValue="int?" Placeholder="Seat Type" FloatLabelType="FloatLabelType.Always" @bind-Value="selectedObj.SeatTypeId">
                            <ComboBoxFieldSettings Value="Id" Text="EnglishTitle"></ComboBoxFieldSettings>
                        </SfComboBox>
                        <ValidationMessage For="@(() => selectedObj.SeatTypeId)"/>
                        @*<SfDropDownList DataSource="SeatType_list"  TItem="GeneralItemDTO" TValue="int?" Placeholder="Seat Type" FloatLabelType="FloatLabelType.Always" AllowFiltering="true" FilterType="Syncfusion.Blazor.DropDowns.FilterType.Contains" @bind-Value="selectedObj.SeatTypeId">
							<DropDownListFieldSettings Value="Id" Text="EnglishTitle"></DropDownListFieldSettings>
							<DropDownListEvents TItem="GeneralItemDTO" TValue="int?" ValueChange="OnSeatTypeValueChange" ></DropDownListEvents>
						</SfDropDownList>
						<ValidationMessage For="@(()=>selectedObj.SeatTypeId)" />*@
                    </div>
                </div>
                <div class="row">
                    <div class="col">
                        <MudButton ButtonType="ButtonType.Submit" Color="Color.Primary" Size="Size.Small" Variant="Variant.Filled" StartIcon="@Icons.Material.Filled.Save">Save</MudButton>
                    </div>
                </div>
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>

<section>

    <div class="row">

        <div class="col">
            @if (Assemblies_list == null)
            {
                <span>wait...</span>
            }
            else if (Assemblies_list.Any())
            {
                @*<SfDropDownList DataSource="Assemblies_list" @bind-Index="assemblyId" Placeholder="Assembly" FloatLabelType="FloatLabelType.Always" TValue="int?" TItem="GeneralItemDTO" AllowFiltering="true" FilterType="Syncfusion.Blazor.DropDowns.FilterType.Contains">
					<DropDownListFieldSettings Value="Id" Text="EnglishTitle"></DropDownListFieldSettings>
					<DropDownListEvents TValue="int?" TItem="GeneralItemDTO" ValueChange="OnAssemblySelect"></DropDownListEvents>
				</SfDropDownList>*@

                <SfDropDownList TItem="GeneralItemDTO" TValue="int?" Placeholder="Assembly" PopupHeight="auto" DataSource="@Assemblies_list">
                    <DropDownListEvents TItem="GeneralItemDTO" TValue="int?" ValueChange="OnAssemblySelect"></DropDownListEvents>
                    <DropDownListFieldSettings Text="EnglishTitle" Value="Id"></DropDownListFieldSettings>
                </SfDropDownList>
            }
            else
            {
                <span>No assembly is defined...</span>
            }
        </div>
        <div class="col">
            @if (panel_list == null)
            {
                <span>wait...</span>
            }
            else if (panel_list.Any())
            {
                @*<SfDropDownList DataSource="panel_list" @bind-Value="panelId" TValue="int?" TItem="GeneralItemDTO" Placeholder="Panel" FloatLabelType="FloatLabelType.Always" AllowFiltering="true" FilterType="Syncfusion.Blazor.DropDowns.FilterType.Contains">
					<DropDownListEvents ValueChange="OnPanelSelect" TValue="int?" TItem="GeneralItemDTO"></DropDownListEvents>
					<DropDownListFieldSettings Text="EnglishTitle" Value="int"></DropDownListFieldSettings>
				</SfDropDownList>*@

                <SfDropDownList TItem="GeneralItemDTO" TValue="int?" Placeholder="Panel" PopupHeight="auto" DataSource="@panel_list">
                    <DropDownListEvents TItem="GeneralItemDTO" TValue="int?" ValueChange="OnPanelSelect"></DropDownListEvents>
                    <DropDownListFieldSettings Text="EnglishTitle" Value="Id"></DropDownListFieldSettings>
                </SfDropDownList>
            }

            else
            {
                <span>No panel is defined...</span>
            }
        </div>

    </div>

    @if (panelCandidates == null)
    {
        <span></span>
    }
    else if (panelCandidates.Any())
    {
        <SfButton OnClick="OpenCreateForm" CssClass="e-primary mb-2">Add Panel Candidate</SfButton>
        <SfGrid DataSource="panelCandidates" AllowFiltering="true" AllowTextWrap="true" Width="100%">
            <GridFilterSettings Type="FilterType.Excel"></GridFilterSettings>
            <GridColumns>
                <GridColumn AutoFit="true" Field="@nameof(PannelNominationsDTO.Id)" HeaderText="Id"></GridColumn>
                <GridColumn AutoFit="true" Field="@nameof(PannelNominationsDTO.CandidateEnglishName)" HeaderText="Candidate (English)"></GridColumn>
                <GridColumn AutoFit="true" Field="@nameof(PannelNominationsDTO.CandidateUrduName)" HeaderText="Candiate (اردو)"></GridColumn>
                <GridColumn AutoFit="true" Field="@nameof(PannelNominationsDTO.SeatTypeEnglish)" HeaderText="Seat Type (English)"></GridColumn>
                <GridColumn AutoFit="true" Field="@nameof(PannelNominationsDTO.SeatTypeUrdu)" HeaderText="Seat Type (اردو)"></GridColumn>
                <GridColumn AutoFit="true" Format="##0" Field="@nameof(PannelNominationsDTO.Order)" HeaderText="Order Number"></GridColumn>
                <GridColumn HeaderText="Action" Width="200px">
                    <Template Context="mm">
                        @{
                            var obj = mm as PannelNominationsDTO;
                            <SfButton CssClass="e-primary" OnClick="@(() => OpenEditForm(obj.Id))">
                                <i class="material-icons">edit</i>
                            </SfButton>
                            <SfButton CssClass="e-danger" OnClick="@(() => OpenDelteForm(obj.Id, obj.Order))">
                                <i class="material-icons">delete_forever</i>
                            </SfButton>
                            <SfButton CssClass="e-info" OnClick="@(() => MoveUp(obj.Id, obj.Order))">
                                <i class="material-icons">expand_less</i>
                            </SfButton>
                            <SfButton CssClass="e-info" OnClick="@(() => MoveDown(obj.Id, obj.Order))">
                                <i class="material-icons">expand_more</i>
                            </SfButton>
                        }
                    </Template>
                </GridColumn>
            </GridColumns>
        </SfGrid>
    }
    else
    {
        <SfButton OnClick="OpenCreateForm" CssClass="e-primary mb-2">Add Panel Candidate</SfButton>
        <span>No candidate define on this panel</span>
    }

</section>