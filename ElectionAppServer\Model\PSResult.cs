﻿using System;
using ElectionAppServer.Model.Structures;

namespace ElectionAppServer.Model;

public class PSResult
{
    public int NominationId { get; set; }
    public virtual Nomination Nomination { get; set; }
    public int PollingStationId { get; set; }
    public virtual PollingStation PollingStation { get; set; }
    public int Votes { get; set; }
    public bool IsPublished { get; set; } = false;

    public string PublishedBy { get; set; }
    public DateTime? PublishedDate { get; set; }

    #region Audit Log

    public DateTime CreatedDate { get; set; }
    public string CreatedBy { get; set; }
    public DateTime? ModifiedDate { get; set; }
    public string ModifiedBy { get; set; }

    #endregion Audit Log
}

public class PSResultLog
{
    public long Id { get; set; }
    public int NominationId { get; set; }
    public int PollingStationId { get; set; }
    public int Votes { get; set; }
    public bool IsPublished { get; set; } = false;
    public string PublishedBy { get; set; }
    public DateTime? PublishedDate { get; set; }
    public DateTime LogDate { get; set; }
    public string UserId { get; set; }
}

public class PSTotalVoteCaste
{
    public int PollingStationId { get; set; }
    public int PhaseId { get; set; }
    public int Votes { get; set; }

    public virtual PollingStation PollingStation { get; set; }
    public virtual ElectionPhase Phase { get; set; }
}