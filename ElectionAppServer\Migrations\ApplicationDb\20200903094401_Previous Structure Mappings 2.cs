﻿using Microsoft.EntityFrameworkCore.Migrations;
using System;

namespace ElectionAppServer.Migrations.ApplicationDb
{
	public partial class PreviousStructureMappings2 : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropForeignKey(
				 name: "FK_StructureMapping_Structures_PreviousStructureId",
				 table: "StructureMapping");

			migrationBuilder.DropForeignKey(
				 name: "FK_StructureMapping_Structures_StructureId",
				 table: "StructureMapping");

			migrationBuilder.DropPrimaryKey(
				 name: "PK_StructureMapping",
				 table: "StructureMapping");

			migrationBuilder.RenameTable(
				 name: "StructureMapping",
				 newName: "StructureMappings");

			migrationBuilder.RenameIndex(
				 name: "IX_StructureMapping_PreviousStructureId",
				 table: "StructureMappings",
				 newName: "IX_StructureMappings_PreviousStructureId");

			migrationBuilder.AlterColumn<DateTime>(
				 name: "CreatedDate",
				 table: "ElectionParties",
				 nullable: false,
				 defaultValue: new DateTime(2020, 9, 3, 14, 44, 1, 172, DateTimeKind.Local).AddTicks(6620),
				 oldClrType: typeof(DateTime),
				 oldType: "datetime2",
				 oldDefaultValue: new DateTime(2020, 9, 3, 13, 50, 49, 800, DateTimeKind.Local).AddTicks(6005));

			migrationBuilder.AlterColumn<DateTime>(
				 name: "CreatedDate",
				 table: "ElectionCandidates",
				 nullable: false,
				 defaultValue: new DateTime(2020, 9, 3, 14, 44, 1, 178, DateTimeKind.Local).AddTicks(6804),
				 oldClrType: typeof(DateTime),
				 oldType: "datetime2",
				 oldDefaultValue: new DateTime(2020, 9, 3, 13, 50, 49, 806, DateTimeKind.Local).AddTicks(2681));

			migrationBuilder.AddPrimaryKey(
				 name: "PK_StructureMappings",
				 table: "StructureMappings",
				 columns: new[] { "StructureId", "PreviousStructureId" });

			migrationBuilder.AddForeignKey(
				 name: "FK_StructureMappings_Structures_PreviousStructureId",
				 table: "StructureMappings",
				 column: "PreviousStructureId",
				 principalTable: "Structures",
				 principalColumn: "Id",
				 onDelete: ReferentialAction.Restrict);

			migrationBuilder.AddForeignKey(
				 name: "FK_StructureMappings_Structures_StructureId",
				 table: "StructureMappings",
				 column: "StructureId",
				 principalTable: "Structures",
				 principalColumn: "Id",
				 onDelete: ReferentialAction.Restrict);
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropForeignKey(
				 name: "FK_StructureMappings_Structures_PreviousStructureId",
				 table: "StructureMappings");

			migrationBuilder.DropForeignKey(
				 name: "FK_StructureMappings_Structures_StructureId",
				 table: "StructureMappings");

			migrationBuilder.DropPrimaryKey(
				 name: "PK_StructureMappings",
				 table: "StructureMappings");

			migrationBuilder.RenameTable(
				 name: "StructureMappings",
				 newName: "StructureMapping");

			migrationBuilder.RenameIndex(
				 name: "IX_StructureMappings_PreviousStructureId",
				 table: "StructureMapping",
				 newName: "IX_StructureMapping_PreviousStructureId");

			migrationBuilder.AlterColumn<DateTime>(
				 name: "CreatedDate",
				 table: "ElectionParties",
				 type: "datetime2",
				 nullable: false,
				 defaultValue: new DateTime(2020, 9, 3, 13, 50, 49, 800, DateTimeKind.Local).AddTicks(6005),
				 oldClrType: typeof(DateTime),
				 oldDefaultValue: new DateTime(2020, 9, 3, 14, 44, 1, 172, DateTimeKind.Local).AddTicks(6620));

			migrationBuilder.AlterColumn<DateTime>(
				 name: "CreatedDate",
				 table: "ElectionCandidates",
				 type: "datetime2",
				 nullable: false,
				 defaultValue: new DateTime(2020, 9, 3, 13, 50, 49, 806, DateTimeKind.Local).AddTicks(2681),
				 oldClrType: typeof(DateTime),
				 oldDefaultValue: new DateTime(2020, 9, 3, 14, 44, 1, 178, DateTimeKind.Local).AddTicks(6804));

			migrationBuilder.AddPrimaryKey(
				 name: "PK_StructureMapping",
				 table: "StructureMapping",
				 columns: new[] { "StructureId", "PreviousStructureId" });

			migrationBuilder.AddForeignKey(
				 name: "FK_StructureMapping_Structures_PreviousStructureId",
				 table: "StructureMapping",
				 column: "PreviousStructureId",
				 principalTable: "Structures",
				 principalColumn: "Id",
				 onDelete: ReferentialAction.Restrict);

			migrationBuilder.AddForeignKey(
				 name: "FK_StructureMapping_Structures_StructureId",
				 table: "StructureMapping",
				 column: "StructureId",
				 principalTable: "Structures",
				 principalColumn: "Id",
				 onDelete: ReferentialAction.Restrict);
		}
	}
}
