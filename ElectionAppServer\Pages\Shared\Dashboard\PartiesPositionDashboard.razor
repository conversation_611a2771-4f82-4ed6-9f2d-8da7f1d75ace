﻿@using ElectionAppServer.DTO.Dashboard
@inherits OwningComponentBase<PartyPositionDataService>
@page "/dashboard/partyposition"

<PageCaption Title="Giglgit Baltistan Assembly" SubTitle="Gilgit Baltistan Election - Phase 1 - 3 Nov, 2009"></PageCaption>
<div class="content m-2" style="margin-top:-25px; margin-left: 5px; margin-right: 5px;">
    <div class="row">
        <div class="col">
            <SmallBox Color="bg-gradient-info" Indicator="@info.TotalSeats.ToString("###,###")" Description="Total Seats" Icon="ion-grid" Link=""/>
        </div>

        <div class="col">
            <SmallBox Color="bg-green" Description="Register Voters" Indicator="@info.RegisterVoters.ToString("###,###,###,###")" Icon="ion-person-stalker" Link=""></SmallBox>
        </div>
        <div class="col">
            <SmallBox Color="bg-yellow" Indicator="@info.VotePolled.ToString("###,###,###,###")" Description="Vote Polled" Icon="ion-ios-box" Link=""></SmallBox>
        </div>
        <div class="col">
            <SmallBox Color="bg-red" Indicator="@info.TurnOutPer.ToString("##0.00")" Description="Turn Out %" Icon="ion-stats-bars" Link=""></SmallBox>
        </div>
        <div class="col">
            <SmallBox Color="bg-blue" Indicator="@info.ResultAnnounced.ToString("###,###")" Description="Results Announced" Icon="ion-ios-bell" Link=""></SmallBox>
        </div>
        <div class="col">
            <div class="small-box bg-gradient-indigo">
                <div class="inner">
                    <h3>Male: @info.MaleVoterPer.ToString("##0.00") %</h3>

                    <p>Female: @info.FemaleVoterPer.ToString("##0.00") %</p>
                </div>
                <div class="icon">
                    <i class="ion ion-pie-graph"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="row small-box bg-gray text-black">
        <div class="inner">
            <div class="table-responsive-sm">
                <table class="table-sm table-bordered">
                    <thead>
                    <tr>
                        <th>
                            <b>PTI</b>
                        </th>
                        <th>
                            <b>PPP</b>
                        </th>
                        <th>
                            <b>PML-N</b>
                        </th>
                        <th>
                            <b>JUI</b>
                        </th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr>
                        <td>122</td>
                        <td>122</td>
                        <td>122</td>
                        <td>122</td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <div class="row table-responsive">
        <table class="table table-sm table-bordered table-striped">
            <thead>
            <tr class="bg-yellow">
                <th>Constituency</th>
                <th>Register<br/>Voters</th>
                <th>Votes<br/>Polled</th>
                <th>Turn Out %</th>
                <th>TPS</th>
                <th>RPS</th>
                <th class="bg-gradient-cyan">PS %</th>
                <th class="bg-gradient-info">Winner<br/>Party</th>
                <th>Winner</th>
                <th>Winner<br/>Vites</th>
                <th>Winner<br/>%</th>
                <th>Runnerup<br/>Party</th>
                <th>Runnerup</th>
                <th>Runnerup<br/>Votes</th>
                <th>Runnerup<br/>%</th>
                <th>Namanigar</th>
                <th class="bg-gradient-secondary">Votes Diff</th>
                <th>Last<br/>Update On</th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td>NA-011</td>
                <td>123456</td>
                <td>23456</td>
                <td>45 %</td>
                <td>200</td>
                <td>195</td>
                <td class="bg-gradient-cyan">96</td>
                <td class="bg-gradient-info">PTI</td>
                <td>Imran Khan</td>
                <td>11234</td>
                <td>34 %</td>
                <td>JUI-F</td>
                <td>Fazal-ur-Rehman</td>
                <td>5</td>
                <td>0h1 %</td>
                <td>3</td>
                <td class="bg-gradient-secondary">55332</td>
                <td> 4-Jan-2020 8:00 PM</td>
            </tr>

            <tr>
                <td>NA-011</td>
                <td>123456</td>
                <td>23456</td>
                <td>45 %</td>
                <td>200</td>
                <td>195</td>
                <td class="bg-gradient-cyan">96</td>
                <td class="bg-gradient-info">PTI</td>
                <td>Imran Khan</td>
                <td>11234</td>
                <td>34 %</td>
                <td>JUI-F</td>
                <td>Fazal-ur-Rehman</td>
                <td>5</td>
                <td>0h1 %</td>
                <td>3</td>
                <td class="bg-gradient-secondary">55332</td>
                <td> 4-Jan-2020 8:00 PM</td>
            </tr>


            <tr>
                <td>NA-011</td>
                <td>123456</td>
                <td>23456</td>
                <td>45 %</td>
                <td>200</td>
                <td>195</td>
                <td class="bg-gradient-cyan">96</td>
                <td class="bg-gradient-info">PTI</td>
                <td>Imran Khan</td>
                <td>11234</td>
                <td>34 %</td>
                <td>JUI-F</td>
                <td>Fazal-ur-Rehman</td>
                <td>5</td>
                <td>0h1 %</td>
                <td>3</td>
                <td class="bg-gradient-secondary">55332</td>
                <td> 4-Jan-2020 8:00 PM</td>
            </tr>

            </tbody>
        </table>
    </div>
</div>

@code {
    public AssemblyPositionDTO info { get; set; }

    protected override async Task OnInitializedAsync()
    {
        info = await Service.GetAssemblyPartyPostionDetail(2, 2, 2);
    }

}