﻿@using System.Net.Http
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.AspNetCore.Components.Routing
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.JSInterop
@using ElectionAppServer
@using ElectionAppServer.Shared
@using Syncfusion.Blazor
@using Microsoft.AspNetCore.Components.Web.Virtualization
@using Blazored.FluentValidation


@using Syncfusion.Blazor.Buttons
@using Syncfusion.Blazor.Inputs
@using Syncfusion.Blazor.Calendars
@using Syncfusion.Blazor.Grids
@using Syncfusion.Blazor.Navigations
@using Syncfusion.Blazor.Popups
@using Syncfusion.Blazor.Lists
@using Syncfusion.Blazor.DropDowns
@using Syncfusion.Blazor.Notifications
@using Syncfusion.Blazor.Charts

@using MudBlazor

@inject SfDialogService DialogService
@*@using DevExpress.Blazor*@

@using System.IO
@using BlazorInputFile

@using ElectionAppServer.DTO
@using ElectionAppServer.Model
@using ElectionAppServer.Data

@*@using Microsoft.AspNetCore.ProtectedBrowserStorage*@
@using Microsoft.AspNetCore.SignalR.Client

@*@inject ProtectedSessionStorage ProtectedSessionStore*@
@*@inject ProtectedLocalStorage ProtectedSessionStore*@
@inject NavigationManager NavigationManager
@inject IJSRuntime JSRuntime
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject ElectionStateService state
@inject ConfigurationReader confreader
@*@inject Blazored.LocalStorage.ILocalStorageService localStorage*@
@using Microsoft.AspNetCore.Components.Server.ProtectedBrowserStorage
@inject ProtectedLocalStorage localStorage
@inject Microsoft.AspNetCore.Hosting.IWebHostEnvironment env

@using Microsoft.AspNetCore.Identity

