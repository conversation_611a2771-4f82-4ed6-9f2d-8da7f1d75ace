﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace ElectionAppServer.Migrations.ApplicationDb
{
	public partial class AddStructureTypeinElectionAsmbSeat : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropPrimaryKey(
				 name: "PK_ElectionAssemblySeats",
				 table: "ElectionAssemblySeats");

			migrationBuilder.AddColumn<int>(
				 name: "ConstituencyType",
				 table: "ElectionAssemblySeats",
				 nullable: false,
				 defaultValue: 0);

			migrationBuilder.AddPrimaryKey(
				 name: "PK_ElectionAssemblySeats",
				 table: "ElectionAssemblySeats",
				 columns: new[] { "ElectionAssemblyId", "SeatTypeId", "ConstituencyType" });
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropPrimaryKey(
				 name: "PK_ElectionAssemblySeats",
				 table: "ElectionAssemblySeats");

			migrationBuilder.DropColumn(
				 name: "ConstituencyType",
				 table: "ElectionAssemblySeats");

			migrationBuilder.AddPrimaryKey(
				 name: "PK_ElectionAssemblySeats",
				 table: "ElectionAssemblySeats",
				 columns: new[] { "ElectionAssemblyId", "SeatTypeId" });
		}
	}
}
