﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace ElectionAppServer.Migrations
{
	public partial class UpdateElectionStructureRelationship2 : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropForeignKey(
				 name: "FK_Structures_Structures_ProvincialAssemblyHalka_DistrictId",
				 table: "Structures");

			migrationBuilder.DropIndex(
				 name: "IX_Structures_ProvincialAssemblyHalka_DistrictId",
				 table: "Structures");

			migrationBuilder.DropColumn(
				 name: "ProvincialAssemblyHalka_DistrictId",
				 table: "Structures");

			migrationBuilder.AddColumn<int>(
				 name: "StructureId",
				 table: "Structures",
				 nullable: true);

			migrationBuilder.CreateIndex(
				 name: "IX_Structures_StructureId",
				 table: "Structures",
				 column: "StructureId");

			migrationBuilder.AddForeignKey(
				 name: "FK_Structures_Structures_StructureId",
				 table: "Structures",
				 column: "StructureId",
				 principalTable: "Structures",
				 principalColumn: "Id",
				 onDelete: ReferentialAction.Restrict);
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropForeignKey(
				 name: "FK_Structures_Structures_StructureId",
				 table: "Structures");

			migrationBuilder.DropIndex(
				 name: "IX_Structures_StructureId",
				 table: "Structures");

			migrationBuilder.DropColumn(
				 name: "StructureId",
				 table: "Structures");

			migrationBuilder.AddColumn<int>(
				 name: "ProvincialAssemblyHalka_DistrictId",
				 table: "Structures",
				 type: "int",
				 nullable: true);

			migrationBuilder.CreateIndex(
				 name: "IX_Structures_ProvincialAssemblyHalka_DistrictId",
				 table: "Structures",
				 column: "ProvincialAssemblyHalka_DistrictId");

			migrationBuilder.AddForeignKey(
				 name: "FK_Structures_Structures_ProvincialAssemblyHalka_DistrictId",
				 table: "Structures",
				 column: "ProvincialAssemblyHalka_DistrictId",
				 principalTable: "Structures",
				 principalColumn: "Id",
				 onDelete: ReferentialAction.Restrict);
		}
	}
}
