﻿@page "/pr"
@attribute [Authorize(Roles = "Producer,IMM")]

@inject NavigationManager NavigationManager
@inject UserManager<ApplicationUser> _UserManager
@inject RoleManager<IdentityRole> _RoleManager
@inject AuthenticationStateProvider AuthenticationStateProvider
@using Size = MudBlazor.Size
@inherits OwningComponentBase<ElectionResultsDataService>

<SfDialog Width="950px" @bind-Visible="@IsDlgVisible" @ref="_dlgForm" IsModal="true" ShowCloseIcon="true">
    <DialogEvents></DialogEvents>
    <DialogTemplates>
        <Header>Detail</Header>
        <Content>
            <div class="row">
                <div class="col urdu-column" style="direction: rtl; text-align: right; background-color: #87cefa; padding: 5px; line-height: 1.7">
                    @((MarkupString)_tickerText)
                </div>
            </div>
            @*<div class="row">
                <div class="col-3">Code: <b>@detail.Code</b></div>
                <div class="col">Constituency: <b>@detail.EnglishTitle</b></div>
                <div class="col">حلقہ: <b>@detail.UrduTitle</b></div>
                </div>
                <div class="row">
                <div class="col">Register Voters: <b>@detail.RegisterVoters</b></div>
                <div class="col">Total Polling Stations: <b>@detail.TotalPollingStations</b></div>
                <div class="col">Results Polling Stations: <b>@detail.ResultPollingStations</b></div>
                </div>
                <table class="table table-sm table-striped mt-2">
                <thead class="thead-dark">
                <tr>
                <th>Candidate</th>
                <th>Party</th>
                <th>Votes</th>
                </tr>
                </thead>
                <tbody>
                @foreach (var n in detail.Nominations)
                {
                <tr>
                <td>@n.Candidate</td>
                <td>@n.Party</td>
                <td>@n.Votes</td>
                </tr>
                }

                </tbody>
                </table>*@
        </Content>
    </DialogTemplates>
</SfDialog>

@*<PageCaption Title="Live Results"></PageCaption>*@
<div style="display:flex; gap:10px">
    <MudText Typo="Typo.h5">Live Results</MudText> <MudButton Variant="Variant.Text" Size="Size.Small" Color="Color.Primary" StartIcon="@Icons.Material.Filled.Refresh" OnClick="@RefreshResults">Refresh</MudButton>
</div>

<section>
    <table class="table table-sm">
        <thead class="thead-dark">
        <tr>
            <th>Assembly</th>
            <th>Constituency</th>
            <th>Winner</th>
            <th>Winner Party</th>
            <th>Winner Votes</th>
            <th>Runner Up</th>
            <th>Runner Party</th>
            <th>Runner Votes</th>
            <th>PS %</th>
            <th>TPS</th>
            <th>RPS</th>
            <th>Status</th>
            <th>Updated Time</th>
            <th>&nbsp;</th>
        </tr>
        </thead>
        <tbody>
        @foreach (var r in _results)
        {
            @if (true)
            {
                <tr class="@(r.Status == "No" ? "newresult" : "pubresult")">
                    <td>@r.Assembly</td>
                    <td>
                        <span style="cursor:pointer;color:blue" @onclick="@(() => DisplayDetail(r.AssemblyId, r.ConstituencyId, r.SeatTypeId))">@r.Constituency</span>

                    </td>
                    <td>@r.WinnerName</td>
                    <td>@r.WinnerParty</td>
                    <td>@r.WinnerVotes</td>
                    <td>@r.RunnerUpName</td>
                    <td>@r.RunnerUpParty</td>
                    <td>@r.RunnerUpVotes</td>
                    <td>
                        @if (r.PercentageCompletedPollingStations < 100)
                        {
                            <span>@Math.Round(r.PercentageCompletedPollingStations, 2)</span>

                            <br/>
                            <img src="/images/tp.png" alt="Partial" style="height: 18px"/>
                        }
                        else
                        {
                            <img src="/images/tc.gif" alt="Completed" style="height: 22px"/>
                        }
                    </td>
                    <td>@r.TotalPollingStations</td>
                    <td>@r.ResultOfPollingStations</td>
                    <td>
                        @if (r.Status == "No")
                        {
                            <img src="/images/new.gif" alt="new"/>
                        }
                    </td>
                    <td>@r.LastUpdatedOn.ToString("d MMM, yyyy h:mm:ss")</td>
                    <td>
                        <AuthorizeView Roles="Producer">
                            <SfButton IsPrimary="false" CssClass="e-primary" OnClick="@(() => MarkPublish(r.ElectionId, r.PhaseId, r.AssemblyId, r.ConstituencyId, r.SeatTypeId))">Publish</SfButton>
                        </AuthorizeView>

                    </td>
                </tr>
            }
        }
        </tbody>
    </table>
</section>