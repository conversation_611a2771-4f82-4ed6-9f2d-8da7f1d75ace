﻿@page "/districts/{provinceId:int}"
@page "/districts/{provinceId:int}/{cid:int}"
@attribute [Authorize(Roles = "Administrators,Data Viewer")]

@using FilterType = Syncfusion.Blazor.Grids.FilterType
@using Size = MudBlazor.Size
@inherits OwningComponentBase<ElectionStructureService>
@*<MudText Typo="Typo.h5">Districts</MudText>*@
<MudText Class="mb-2" Typo="Typo.h5"><MudIcon Size="Size.Large" Icon="@Icons.Material.Filled.ZoomInMap"></MudIcon> Districts</MudText>

@*<PageCaption Title="Districts"></PageCaption>*@
@*<a href="/elections">@ElectionTitle</a> <span>></span>*@

<SfDialog Width="900px" @bind-Visible="@isDlgVisible" @ref="dlgForm" IsModal="true" ShowCloseIcon="true">
    <DialogEvents OnOpen="BeforeOpen"></DialogEvents>
    <DialogTemplates>
        <Header>District</Header>
        <Content>
            <AdminUnitForm SelectedObj="@selectedObj" OnValidDataSubmit="SaveData"></AdminUnitForm>
        </Content>
    </DialogTemplates>
</SfDialog>

<SfToast @ref="ToastObj">
    <ToastPosition X="Right"></ToastPosition>
</SfToast>

<section >
    @foreach (var bc in BredCrum)
    {
        <a href="@bc.UrduTitle">@bc.EnglishTitle</a>
        <span>></span>
    }
    Districts

    @if (objList == null)
    {
        <p>
            <em>Loading...</em>
        </p>
    }
    else
    {
        <AuthorizeView Roles="Administrators">
            <div class="row">
                <MudButton Class="mb-1" Size="Size.Small" Color="Color.Primary" Variant="Variant.Filled" StartIcon="@Icons.Material.Filled.Add" OnClick="OpenCreateForm">Create District</MudButton>
            </div>
        </AuthorizeView>

        <div class="row">
            <SearchStructure ElectionId="@electionId"></SearchStructure>
        </div>
        <div class="row">
            <SfGrid DataSource="@objList" ModelType="@selectedObj" @ref="Grid" AllowFiltering="true" AllowSorting="true" Width="100%" AllowTextWrap="true" Height="calc(100vh - 262px)">
                <GridEvents QueryCellInfo="CustomizeCell" TValue="ElcStructureDTO"></GridEvents>
                <GridFilterSettings Type="FilterType.Excel"></GridFilterSettings>
                <GridPageSettings PageSize="10"></GridPageSettings>
                <GridColumns>
                    <GridColumn AutoFit="true" Field="Id" HeaderText="Id"></GridColumn>
                    <GridColumn Width="170px" Field="UrduTitle" HeaderText="Urdu Title" TextAlign="TextAlign.Right"></GridColumn>
                    <GridColumn Width="170px" Field="EnglishTitle" HeaderText="English Title"></GridColumn>
                    <GridColumn AutoFit="true" Field="@nameof(ElcStructureDTO.RegionEnglish)" HeaderText="Region (English)"></GridColumn>
                    <GridColumn AutoFit="true" Field="@nameof(ElcStructureDTO.DivisionEnglish)" HeaderText="Division (English)"></GridColumn>
                    <GridColumn Width="310px" HeaderText="Audit Info" AutoFit="true">
                        <Template Context="kk">
                            @{
                                if (kk is ElcStructureDTO oo)
                                {
                                    <div style="font-size:11px">
                                        <span>Created By: @oo.CreateBy</span><span> On @oo.CreatedDate</span>
                                        @if (!string.IsNullOrEmpty(oo.ModifiedBy))
                                        {
                                            <br/>
                                            <span>Modified By: @oo.ModifiedBy</span>
                                            <span> On @oo.ModifiedDate</span>
                                        }

                                    </div>
                                }
                            }
                        </Template>
                    </GridColumn>

                    @*<GridColumn Field="MaleVoters" HeaderText="Male Voters"></GridColumn>
                        <GridColumn Field="FemaleVoters" HeaderText="Female Voters"></GridColumn>
                        <GridColumn Field="TotalPollingStations" HeaderText="Polling Stations"></GridColumn>*@
                    @*<GridColumn Visible="electionType==ElectionType.LocalBody" Field="SubUnits" HeaderText="Sub Units (Tier 1)"></GridColumn>*@
                    <GridColumn Width="200px" HeaderText="Actions" AllowFiltering="false">
                        <Template Context="ss">
                            @{
                                if (ss is ElcStructureDTO kk)
                                {
                                    <MudLink Target="@($"au{kk.Id}")" href="@($"report/adminunit/{kk.Id}")">
                                        <MudIcon Icon="@Icons.Material.Filled.TableChart"></MudIcon>
                                    </MudLink>
                                    <AuthorizeView Roles="Administrators">
                                        <MudFab Disabled="@context.User.IsInRole("Data Viewer")"
                                                Color="Color.Error"
                                                StartIcon="@Icons.Material.Filled.Delete"
                                                Size="Size.Small"
                                                OnClick="@(() => DeleteRecord(kk.Id))">
                                        </MudFab>

                                        <MudFab Color="Color.Primary" Size="Size.Small" StartIcon="@Icons.Material.Filled.Edit" OnClick="@(() => OpenEditForm(kk))" Class="ml-1"></MudFab>
                                    </AuthorizeView>
                                    @if (electionType == ElectionType.LocalBody)
                                    {
                                        <a style="padding-left:3px;" href="/towns/@kk.Id">LG Tier 1</a>
                                    }
                                }
                            }
                        </Template>
                    </GridColumn>
                </GridColumns>
            </SfGrid>
        </div>
    }
</section>