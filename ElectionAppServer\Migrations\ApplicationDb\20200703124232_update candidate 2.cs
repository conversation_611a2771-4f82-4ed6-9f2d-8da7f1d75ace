﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace ElectionAppServer.Migrations.ApplicationDb
{
	public partial class updatecandidate2 : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropIndex(
				 name: "IX_ElectionPhases_ElectionId",
				 table: "ElectionPhases");

			migrationBuilder.AlterColumn<string>(
				 name: "EnglishTitle",
				 table: "Professions",
				 unicode: false,
				 maxLength: 200,
				 nullable: false,
				 oldClrType: typeof(string),
				 oldType: "nvarchar(200)",
				 oldMaxLength: 200);

			migrationBuilder.AlterColumn<string>(
				 name: "EnglishTitle",
				 table: "Languages",
				 unicode: false,
				 maxLength: 200,
				 nullable: false,
				 oldClrType: typeof(string),
				 oldType: "nvarchar(200)",
				 oldMaxLength: 200);

			migrationBuilder.AlterColumn<string>(
				 name: "EnglishTitle",
				 table: "Elections",
				 unicode: false,
				 maxLength: 200,
				 nullable: false,
				 oldClrType: typeof(string),
				 oldType: "nvarchar(200)",
				 oldMaxLength: 200);

			migrationBuilder.AlterColumn<string>(
				 name: "Description",
				 table: "Elections",
				 maxLength: 2000,
				 nullable: true,
				 oldClrType: typeof(string),
				 oldType: "nvarchar(1000)",
				 oldMaxLength: 1000,
				 oldNullable: true);

			migrationBuilder.AlterColumn<string>(
				 name: "Title",
				 table: "ElectionPhases",
				 maxLength: 200,
				 nullable: false,
				 oldClrType: typeof(string),
				 oldType: "nvarchar(max)",
				 oldNullable: true);

			migrationBuilder.AlterColumn<string>(
				 name: "EnglishTitle",
				 table: "ElectionAssemblies",
				 unicode: false,
				 maxLength: 200,
				 nullable: false,
				 oldClrType: typeof(string),
				 oldType: "nvarchar(200)",
				 oldMaxLength: 200);

			migrationBuilder.AlterColumn<string>(
				 name: "EnglishTitle",
				 table: "Educations",
				 unicode: false,
				 maxLength: 200,
				 nullable: false,
				 oldClrType: typeof(string),
				 oldType: "nvarchar(200)",
				 oldMaxLength: 200);

			migrationBuilder.AlterColumn<string>(
				 name: "EnglishTitle",
				 table: "Castes",
				 unicode: false,
				 maxLength: 200,
				 nullable: false,
				 oldClrType: typeof(string),
				 oldType: "nvarchar(200)",
				 oldMaxLength: 200);

			migrationBuilder.CreateIndex(
				 name: "IX_SeatTypes_UrduTitle",
				 table: "SeatTypes",
				 column: "UrduTitle",
				 unique: true);

			migrationBuilder.CreateIndex(
				 name: "IX_Professions_UrduTitle",
				 table: "Professions",
				 column: "UrduTitle",
				 unique: true);

			migrationBuilder.CreateIndex(
				 name: "IX_Parties_EnglishTitle",
				 table: "Parties",
				 column: "EnglishTitle",
				 unique: true);

			migrationBuilder.CreateIndex(
				 name: "IX_Parties_UrduTitle",
				 table: "Parties",
				 column: "UrduTitle",
				 unique: true);

			migrationBuilder.CreateIndex(
				 name: "IX_Languages_UrduTitle",
				 table: "Languages",
				 column: "UrduTitle",
				 unique: true);

			migrationBuilder.CreateIndex(
				 name: "IX_ElectionPhases_ElectionId_Title",
				 table: "ElectionPhases",
				 columns: new[] { "ElectionId", "Title" },
				 unique: true);

			migrationBuilder.CreateIndex(
				 name: "IX_Educations_UrduTitle",
				 table: "Educations",
				 column: "UrduTitle",
				 unique: true);

			migrationBuilder.CreateIndex(
				 name: "IX_Coalitions_EnglishTitle",
				 table: "Coalitions",
				 column: "EnglishTitle",
				 unique: true);

			migrationBuilder.CreateIndex(
				 name: "IX_Coalitions_UrduTitle",
				 table: "Coalitions",
				 column: "UrduTitle",
				 unique: true);

			migrationBuilder.CreateIndex(
				 name: "IX_Castes_UrduTitle",
				 table: "Castes",
				 column: "UrduTitle",
				 unique: true);
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropIndex(
				 name: "IX_SeatTypes_UrduTitle",
				 table: "SeatTypes");

			migrationBuilder.DropIndex(
				 name: "IX_Professions_UrduTitle",
				 table: "Professions");

			migrationBuilder.DropIndex(
				 name: "IX_Parties_EnglishTitle",
				 table: "Parties");

			migrationBuilder.DropIndex(
				 name: "IX_Parties_UrduTitle",
				 table: "Parties");

			migrationBuilder.DropIndex(
				 name: "IX_Languages_UrduTitle",
				 table: "Languages");

			migrationBuilder.DropIndex(
				 name: "IX_ElectionPhases_ElectionId_Title",
				 table: "ElectionPhases");

			migrationBuilder.DropIndex(
				 name: "IX_Educations_UrduTitle",
				 table: "Educations");

			migrationBuilder.DropIndex(
				 name: "IX_Coalitions_EnglishTitle",
				 table: "Coalitions");

			migrationBuilder.DropIndex(
				 name: "IX_Coalitions_UrduTitle",
				 table: "Coalitions");

			migrationBuilder.DropIndex(
				 name: "IX_Castes_UrduTitle",
				 table: "Castes");

			migrationBuilder.AlterColumn<string>(
				 name: "EnglishTitle",
				 table: "Professions",
				 type: "nvarchar(200)",
				 maxLength: 200,
				 nullable: false,
				 oldClrType: typeof(string),
				 oldUnicode: false,
				 oldMaxLength: 200);

			migrationBuilder.AlterColumn<string>(
				 name: "EnglishTitle",
				 table: "Languages",
				 type: "nvarchar(200)",
				 maxLength: 200,
				 nullable: false,
				 oldClrType: typeof(string),
				 oldUnicode: false,
				 oldMaxLength: 200);

			migrationBuilder.AlterColumn<string>(
				 name: "EnglishTitle",
				 table: "Elections",
				 type: "nvarchar(200)",
				 maxLength: 200,
				 nullable: false,
				 oldClrType: typeof(string),
				 oldUnicode: false,
				 oldMaxLength: 200);

			migrationBuilder.AlterColumn<string>(
				 name: "Description",
				 table: "Elections",
				 type: "nvarchar(1000)",
				 maxLength: 1000,
				 nullable: true,
				 oldClrType: typeof(string),
				 oldMaxLength: 2000,
				 oldNullable: true);

			migrationBuilder.AlterColumn<string>(
				 name: "Title",
				 table: "ElectionPhases",
				 type: "nvarchar(max)",
				 nullable: true,
				 oldClrType: typeof(string),
				 oldMaxLength: 200);

			migrationBuilder.AlterColumn<string>(
				 name: "EnglishTitle",
				 table: "ElectionAssemblies",
				 type: "nvarchar(200)",
				 maxLength: 200,
				 nullable: false,
				 oldClrType: typeof(string),
				 oldUnicode: false,
				 oldMaxLength: 200);

			migrationBuilder.AlterColumn<string>(
				 name: "EnglishTitle",
				 table: "Educations",
				 type: "nvarchar(200)",
				 maxLength: 200,
				 nullable: false,
				 oldClrType: typeof(string),
				 oldUnicode: false,
				 oldMaxLength: 200);

			migrationBuilder.AlterColumn<string>(
				 name: "EnglishTitle",
				 table: "Castes",
				 type: "nvarchar(200)",
				 maxLength: 200,
				 nullable: false,
				 oldClrType: typeof(string),
				 oldUnicode: false,
				 oldMaxLength: 200);

			migrationBuilder.CreateIndex(
				 name: "IX_ElectionPhases_ElectionId",
				 table: "ElectionPhases",
				 column: "ElectionId");
		}
	}
}
