﻿@layout ReportLayout
@page "/report/adminunit/{AdminUnitId:int}"
@using FilterType = Syncfusion.Blazor.Grids.FilterType
@inherits OwningComponentBase<ElectionStructureService>

@{
    var Format = new[] { "Election", "Assembly", "Phase", "ConstituencyCode" };
}
<style>
	body{
		user-select: none;
	}
</style>
@if (info == null)
{
    <p>Loading</p>
}
else
{
    <div class="card bg-info mb-3">
        <div class="card-header" style="display: flex; justify-content: space-between;align-items: center">
            <div style="flex:1; font-size:30px; font-weight:bold">
                @if (!string.IsNullOrEmpty(info.Code))
                {
                    <span>@info.Code</span>
                    <span> - </span>
                }
                @info.EnglishTitle (@info.Type)<br/>
                <span class="urdu-cap" style="font-size:34px; text-align: right;direction: rtl">@info.UrduTitle</span>
            </div>
        </div>
        <div class="card-body bg-light" style="color:black">
            <div class="row">
                @if (!string.IsNullOrEmpty(info.ProvinceEnglish))
                {
                    <div class="col rptfield">
                        <div>Province: </div>
                        <div class="rptdata">
                            @info.ProvinceEnglish<br><span class="urdu-sm">@info.ProvinceUrdu</span>
                        </div>
                    </div>
                }

                @if (!string.IsNullOrEmpty(info.DivisionEnglish))
                {
                    <div class="col rptfield">
                        <div>Division: </div>
                        <div class="rptdata">
                            @info.DivisionEnglish<br><span class="urdu-sm">@info.DivisionUrdu</span>
                        </div>
                    </div>
                }

                @if (!string.IsNullOrEmpty(info.RegionEnglish))
                {
                    <div class="col rptfield">
                        <div>Region: </div>
                        <div class="rptdata">
                            @info.RegionEnglish<br><span class="urdu-sm">@info.RegionUrdu</span>
                        </div>
                    </div>
                }
                @if (!string.IsNullOrEmpty(info.DistrictUrdu))
                {
                    <div class="col rptfield">
                        <div>District: </div>
                        <div class="rptdata">
                            @info.DistrictEnglish<br><span class="urdu-sm">@info.DistrictUrdu</span>
                        </div>
                    </div>
                }
                @if (!string.IsNullOrEmpty(info.TownUrdu))
                {
                    <div class="col rptfield">
                        <div>Town: </div>
                        <div class="rptdata">
                            @info.TownEnglish<br><span class="urdu-sm">@info.TownUrdu</span>
                        </div>
                    </div>
                }
                @if (!string.IsNullOrEmpty(info.UCEnglish))
                {
                    <div class="col rptfield">
                        <div>UC: </div>
                        <div class="rptdata">
                            @info.UCEnglish<br><span class="urdu-sm">@info.UCUrdu</span>
                        </div>
                    </div>
                }
            </div>
            @foreach (var ps in info.PollingSchemes)
            {
                <h3>Phase: @ps.Phase</h3>
                <div class="row">
                    <div class="col rptfield">
                        <div>Male Voters: </div>
                        <div class="rptdata">
                            <b>@(ps.MaleVoters ?? 0)</b>
                        </div>
                    </div>
                    <div class="col rptfield">
                        <div>Female Voters: </div>
                        <div class="rptdata">
                            <b>@(ps.FemaleVoters ?? 0)</b>
                        </div>
                    </div>
                    <div class="col rptfield">
                        <div>Youth Voters: </div>
                        <div class="rptdata">
                            <b>@(ps.YouthVoters ?? 0)</b>
                        </div>
                    </div>
                    <div class="col rptfield">
                        <div>Total Voters: </div>
                        <div class="rptdata">
                            <b>@ps.TotalVoters</b>
                        </div>
                    </div>
                    <div class="col rptfield">
                        <div>Total Polling Stations: </div>
                        <div class="rptdata">
                            <b>@(ps.TotalPollingStations ?? 0)</b>
                        </div>
                    </div>
                </div>
                <div class="row">
                    @*<div class="col rptfield"><div>Trivia (Economy): </div><div class="rptdata urdu-sm"><b>@format(ps.Trivia)</b></div></div>*@
                    <div class="col rptfield">
                        <div>Trivia (Economy): </div>
                        <div class="rptdata urdu-sm">
                            <b>@((MarkupString)ps.Trivia)</b>
                        </div>
                    </div>

                </div>
                <div class="row">
                    @*<div class="col rptfield"><div>Trivia (General): </div><div class="rptdata urdu-sm"><b>@format(ps.GeneralTrivia)</b></div></div>*@
                    <div class="col rptfield">
                        <div>Trivia (General): </div>
                        <div class="rptdata urdu-sm">
                            <b>@((MarkupString)ps.GeneralTrivia)</b>
                        </div>
                    </div>

                </div>
                <div class="row">
                    <div class="col rptfield">
                        <div>Spoken Languages: </div>
                        <div class="rptdata urdu-sm">
                            <b>@format(ps.Languages)</b>
                        </div>
                    </div>
                    <div class="col rptfield">
                        <div>Castes: </div>
                        <div class="rptdata urdu-sm">
                            <b>@format(ps.Castes)</b>
                        </div>
                    </div>
                    <div class="col rptfield">
                        <div>Income Source: </div>
                        <div class="rptdata urdu-sm">
                            <b>@format(ps.MajorityIncomeSource)</b>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col rptfield">
                        <div>Important Area/Cities: </div>
                        <div class="rptdata urdu-sm">
                            <b>@format(ps.ImportantAreas)</b>
                        </div>
                    </div>
                    <div class="col rptfield">
                        <div>Political Parties: </div>
                        <div class="rptdata urdu-sm">
                            <b>@format(ps.ImportantPoliticalPersonalities)</b>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col rptfield">
                        <div>Problem: </div>
                        <div class="rptdata urdu-sm">
                            <b>@format(ps.Problems)</b>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col rptfield">
                        <div>Profile: </div>
                        <div class="rptdata urdu-sm">
                            <b>@((MarkupString)ps.Profile)</b>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col rptfield">
                        <div>Urban Area %: </div>
                        <div class="rptdata">
                            <b>@format((ps.UrbanAreaPer ?? 0.0).ToString("###.##"))</b>
                        </div>
                    </div>
                    <div class="col rptfield">
                        <div>Rural Area %: </div>
                        <div class="rptdata">
                            <b>@format((ps.RuralAreaPer ?? 0.0).ToString("###.##"))</b>
                        </div>
                    </div>
                    <div class="col rptfield">
                        <div>Total Area: </div>
                        <div class="rptdata urdu-sm">
                            <b>@format(ps.Area)</b>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col rptfield">
                        <div>Average Income: </div>
                        <div class="rptdata">
                            <b>@format(ps.AverageHouseholdIncome)</b>
                        </div>
                    </div>
                    <div class="col rptfield">
                        <div>Literacy Rate: </div>
                        <div class="rptdata">
                            <b>@format(ps.LiteracyRate)</b>
                        </div>
                    </div>
                </div>
                <hr/>
            }

        </div>
    </div>
    @if (info.SubAdminUnits.Any())
    {
        <div class="card bg-info mb-3">
            <div class="card-header" style="display: flex; justify-content: space-between;align-items: center">
                Sub Units - @info.SubAdminUnits[0].Province
            </div>
            <div class="card-body bg-light" style="color:black">
                <SfGrid DataSource="info.SubAdminUnits" AllowFiltering="true" AllowSorting="true" AllowTextWrap="true">
                    <GridFilterSettings Type="FilterType.Excel"></GridFilterSettings>
                    <GridColumns>
                        <GridColumn HeaderText="Title (English)" Field="@nameof(GeneralItemDTO.EnglishTitle)"></GridColumn>
                        <GridColumn HeaderText="Title (Urdu)" Field="@nameof(GeneralItemDTO.UrduTitle)"></GridColumn>
                    </GridColumns>
                </SfGrid>
            </div>
        </div>
    }

    @if (info.Regions != null && info.Regions.Any())
    {
        <div class="card bg-info mb-3">
            <div class="card-header" style="display: flex; justify-content: space-between;align-items: center">
                Regions
            </div>
            <div class="card-body bg-light" style="color:black">
                <SfGrid DataSource="info.Regions" AllowFiltering="true" AllowSorting="true" AllowTextWrap="true">
                    <GridFilterSettings Type="FilterType.Excel"></GridFilterSettings>
                    <GridColumns>
                        <GridColumn HeaderText="Title (English)" Field="@nameof(GeneralItemDTO.EnglishTitle)"></GridColumn>
                        <GridColumn HeaderText="Title (Urdu)" Field="@nameof(GeneralItemDTO.UrduTitle)"></GridColumn>
                    </GridColumns>
                </SfGrid>
            </div>
        </div>
    }

    <div class="card bg-info mb-3">
        <div class="card-header" style="display: flex; justify-content: space-between;align-items: center">
            Nominations
        </div>
        <div class="card-body bg-light" style="color:black">
            <SfGrid DataSource="info.Nominations" AllowFiltering="true" AllowSorting="true" AllowTextWrap="true" AllowGrouping="true">
                <GridGroupSettings ShowDropArea="true" Columns=@Format></GridGroupSettings>
                <GridFilterSettings Type="FilterType.Excel"></GridFilterSettings>
                <GridColumns>
                    <GridColumn HeaderText="Election" DisableHtmlEncode="false" Field="@nameof(NominationRptDTO.Election)"></GridColumn>
                    <GridColumn HeaderText="Assembly" DisableHtmlEncode="false" Field="@nameof(NominationRptDTO.Assembly)"></GridColumn>
                    <GridColumn HeaderText="Phase" DisableHtmlEncode="false" Field="@nameof(NominationRptDTO.Phase)"></GridColumn>
                    <GridColumn HeaderText="Code" DisableHtmlEncode="false" Field="@nameof(NominationRptDTO.ConstituencyCode)"></GridColumn>
                    <GridColumn HeaderText="Constituency" DisableHtmlEncode="false" Field="@nameof(NominationRptDTO.Constituency)"></GridColumn>
                    <GridColumn HeaderText="Province" DisableHtmlEncode="false" Field="@nameof(NominationRptDTO.Province)"></GridColumn>
                    <GridColumn HeaderText="Division" DisableHtmlEncode="false" Field="@nameof(NominationRptDTO.Division)"></GridColumn>
                    <GridColumn HeaderText="Region" DisableHtmlEncode="false" Field="@nameof(NominationRptDTO.Region)"></GridColumn>
                    <GridColumn HeaderText="District" DisableHtmlEncode="false" Field="@nameof(NominationRptDTO.District)"></GridColumn>
                    <GridColumn HeaderText="Seat Type" DisableHtmlEncode="false" Field="@nameof(NominationRptDTO.SeatType)"></GridColumn>
                    <GridColumn HeaderText="Candidate" DisableHtmlEncode="false" Field="@nameof(NominationRptDTO.Candidate)"></GridColumn>
                    <GridColumn HeaderText="Party" DisableHtmlEncode="false" Field="@nameof(NominationRptDTO.Party)"></GridColumn>
                    <GridColumn HeaderText="Symbol" DisableHtmlEncode="false" Field="@nameof(NominationRptDTO.Symbol)"></GridColumn>
                    <GridColumn HeaderText="Votes" DisableHtmlEncode="false" Field="@nameof(NominationRptDTO.Votes)"></GridColumn>
                </GridColumns>
            </SfGrid>
        </div>
    </div>
}

@code {
    [Parameter] public int AdminUnitId { get; set; }

    public AdminUnitRptDTO info { get; set; }

    protected override async Task OnParametersSetAsync()
    {
        info = await Service.GetAdminRptInfo(AdminUnitId);
    }

    public string format(string ss)
    {
        return string.IsNullOrEmpty(ss) ? "." : ss;
    }

}