﻿using System.ComponentModel.DataAnnotations;

namespace ElectionAppServer.DTO;

public class RegionDTO
{
    public int Id { get; set; }

    [Required(ErrorMessage = "Please Select Province")]
    [Range(1, int.MaxValue, ErrorMessage = "Please Select Province")]
    [Display(Name = "Province")]
    public int? ProvinceId { get; set; }

    public string Province { get; set; }

    [Required]
    [Display(Name = "Region (English)")]
    [MaxLength(200)]
    public string EnglishTitle { get; set; }

    [Display(Name = "Region (Urdu)")]
    [Required]
    [MaxLength(200)]
    public string UrduTitle { get; set; }

    public string CreatedBy { get; set; }
    public string CreatedOn { get; set; }
    public string ModifiedBy { get; set; }
    public string ModifiedOn { get; set; }
}

public class AdminDivisionDTO
{
    public int Id { get; set; }

    [Required(ErrorMessage = "Please Select Province")]
    [Range(1, int.MaxValue, ErrorMessage = "Please Select Province")]
    [Display(Name = "Province")]
    public int? ProvinceId { get; set; }

    public string Province { get; set; }

    [Required]
    [Display(Name = "Division (English)")]
    [MaxLength(200)]
    public string EnglishTitle { get; set; }

    [Display(Name = "Division (Urdu)")]
    [Required]
    [MaxLength(200)]
    public string UrduTitle { get; set; }

    public string CreatedBy { get; set; }
    public string CreatedOn { get; set; }
    public string ModifiedBy { get; set; }
    public string ModifiedOn { get; set; }
}