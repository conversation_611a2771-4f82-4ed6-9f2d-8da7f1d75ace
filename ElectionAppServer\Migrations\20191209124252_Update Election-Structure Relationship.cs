﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace ElectionAppServer.Migrations
{
	public partial class UpdateElectionStructureRelationship : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.AlterColumn<int>(
				 name: "ElectionId",
				 table: "Structures",
				 nullable: false,
				 oldClrType: typeof(int),
				 oldType: "int",
				 oldNullable: true);
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.AlterColumn<int>(
				 name: "ElectionId",
				 table: "Structures",
				 type: "int",
				 nullable: true,
				 oldClrType: typeof(int));
		}
	}
}
