using System.Collections.Generic;
using System.Threading.Tasks;
using ElectionAppServer.DTO;

namespace ElectionAppServer.Pages.Reports;

public partial class PartyPosition
{
    public List<ChartData> MedalDetails = new()
    {
        new ChartData { Country = "KPK", PPP = 50, PTI = 70, PMLN = 45 },
        new ChartData { Country = "Sindh", PPP = 40, PTI = 60, PMLN = 55 },
        new ChartData { Country = "Balochistan", PPP = 70, PTI = 60, PMLN = 50 },
        new ChartData { Country = "Punjab", PPP = 60, PTI = 56, PMLN = 40 }
    };


    public List<PartyPositionStatistic> partyPositionData = new();
    private int phaseId { get; set; }

    public async Task ApplyFilter(int phaseId)
    {
        this.phaseId = phaseId;
        StateHasChanged();

        partyPositionData = await Service.GetOverallPartyPosition(phaseId);
        await Task.CompletedTask;
    }

    //public List<Statistics> StatisticsDetails = new List<Statistics>
    //{
    //  new Statistics { Browser = "Chrome", Users = 37 },
    //  new Statistics { Browser = "UC Browser", Users = 17 },
    //  new Statistics { Browser = "iPhone", Users = 19 },
    //  new Statistics { Browser = "Others", Users = 4  },
    //  new Statistics { Browser = "Opera", Users = 11 },
    //  new Statistics { Browser = "Android", Users = 12 },
    //};
}