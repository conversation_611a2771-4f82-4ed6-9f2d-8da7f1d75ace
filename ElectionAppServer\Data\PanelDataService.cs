﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ElectionAppServer.DTO;
using ElectionAppServer.Model;
using Microsoft.EntityFrameworkCore;

namespace ElectionAppServer.Data;

public class PanelDataService
{
    private readonly IDbContextFactory<ApplicationDbContext> _contextFactory; // private  ApplicationDbContext  dc;

    //public PanelDataService(ApplicationDbContext _dc)
    public PanelDataService(IDbContextFactory<ApplicationDbContext> contextFactory)
    {
        _contextFactory = contextFactory;
    }

    internal Task<List<GeneralItemDTO>> GetAssemblies(int electionId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.ElectionAssemblies
            where aa.ElectionId == electionId &&
                  aa.NominationType == NominationType.Panel
            orderby aa.EnglishTitle
            select new GeneralItemDTO
            {
                Id = aa.Id,
                EnglishTitle = aa.EnglishTitle
            }).ToList();
        return Task.FromResult(q);
    }

    internal Task<List<GeneralItemDTO>> GetPannelList()
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.Candidates
            orderby aa.EnglishName
            where aa.CandidateType == NominationType.Panel
            select new GeneralItemDTO
            {
                Id = aa.Id,
                EnglishTitle = aa.EnglishName
            }).ToList();
        return Task.FromResult(q);
    }

    public Task<List<PannelNominationsDTO>> GetPanelCandidates(int phaseId, int assemblyId, int panelId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.PanelNominations
            where aa.AssemblyId == assemblyId &&
                  aa.PanelId == panelId &&
                  aa.PhaseId == phaseId
            orderby aa.OrderNumber
            select new PannelNominationsDTO
            {
                Id = aa.Id,
                CandidateEnglishName = aa.CandidateEnglishName,
                CandidateUrduName = aa.CandidateUrduName,
                SeatTypeUrdu = aa.SeatType.UrduTitle,
                SeatTypeEnglish = aa.SeatType.EnglishTitle,
                Order = aa.OrderNumber
            }).ToList();
        return Task.FromResult(q);
    }

    public Task<List<GeneralItemDTO>> GetSeatTypes()
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.SeatTypes
            orderby aa.EnglishTitle
            select new GeneralItemDTO
            {
                Id = aa.Id,
                EnglishTitle = aa.EnglishTitle
            }).ToList();
        return Task.FromResult(q);
    }

    public Task<string> SaveCandiadte(PanelFormDTO selectedObj, string user)
    {
        using var dc = _contextFactory.CreateDbContext();
        if (selectedObj.Id == 0)
        {
            var cnt = (from aa in dc.PanelNominations
                where aa.PhaseId == selectedObj.PhaseId &&
                      aa.PanelId == selectedObj.PanelId &&
                      aa.AssemblyId == selectedObj.AssemblyId
                select aa).Count() + 1;
            var q = new PanelNomination
            {
                AssemblyId = (int)selectedObj.AssemblyId,
                CandidateEnglishName = selectedObj.CandidateEnglishName,
                CandidateUrduName = selectedObj.CandidateUrduName,
                OrderNumber = cnt,
                PanelId = (int)selectedObj.PanelId,
                PhaseId = (int)selectedObj.PhaseId,
                SeatTypeId = (int)selectedObj.SeatTypeId,
                CreatedBy = user,
                CreatedDate = DateTime.Now
            };

            dc.PanelNominations.Add(q);
            dc.SaveChanges();
            return Task.FromResult("OK");
        }
        else
        {
            var q = (from aa in dc.PanelNominations
                where aa.Id == selectedObj.Id
                select aa).FirstOrDefault();
            if (q == null)
                return Task.FromResult("Record not found");
            q.ModifiedBy = user;
            q.ModifiedDate = DateTime.Now;
            q.CandidateEnglishName = selectedObj.CandidateEnglishName;
            q.CandidateUrduName = selectedObj.CandidateUrduName;
            q.SeatTypeId = (int)selectedObj.SeatTypeId;
            dc.SaveChanges();
            return Task.FromResult("OK");
        }
    }

    public Task<PanelFormDTO> GetCandidateInfo(int id)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.PanelNominations
            where aa.Id == id
            select new PanelFormDTO
            {
                Id = aa.Id,
                AssemblyId = aa.AssemblyId,
                CandidateEnglishName = aa.CandidateEnglishName,
                CandidateUrduName = aa.CandidateUrduName,
                PanelId = aa.PanelId,
                PhaseId = aa.PhaseId,
                SeatTypeId = aa.SeatTypeId
            }).FirstOrDefault();
        return Task.FromResult(q);
    }

    public Task<string> MoveDown(int id, int order, string user)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q1 = (from aa in dc.PanelNominations
            where aa.Id == id
            select aa).First();

        var q2 = (from aa in dc.PanelNominations
            where aa.PanelId == q1.PanelId &&
                  aa.AssemblyId == q1.AssemblyId &&
                  aa.PhaseId == q1.PhaseId &&
                  aa.OrderNumber == order + 1
            select aa).FirstOrDefault();

        if (q2 == null)
            return Task.FromResult("OK");
        q1.OrderNumber = order + 1;
        q2.OrderNumber = order;
        q1.ModifiedBy = user;
        q2.ModifiedBy = user;
        q1.ModifiedDate = DateTime.Now;
        q2.ModifiedDate = DateTime.Now;

        dc.SaveChanges();
        return Task.FromResult("OK");
    }

    internal Task<string> DeleteMember(int id, int order, string user)
    {
        using var dc = _contextFactory.CreateDbContext();
        using var tran = dc.Database.BeginTransaction();
        var q = (from aa in dc.PanelNominations
            where aa.Id == id &&
                  aa.OrderNumber == order
            select aa).FirstOrDefault();
        dc.PanelNominations.Remove(q);
        dc.SaveChanges();

        var rem = (from aa in dc.PanelNominations
                orderby aa.OrderNumber
                where aa.PanelId == q.PanelId &&
                      aa.AssemblyId == q.AssemblyId &&
                      aa.PhaseId == q.PhaseId &&
                      aa.OrderNumber > order
                select aa
            ).ToList();

        foreach (var pn in rem)
        {
            pn.OrderNumber = pn.OrderNumber - 1;
            pn.ModifiedBy = user;
            pn.ModifiedDate = DateTime.Now;
            dc.SaveChanges();
        }

        tran.Commit();
        return Task.FromResult("OK");
    }

    public Task<string> MoveUp(int id, int order, string user)
    {
        using var dc = _contextFactory.CreateDbContext();
        if (order == 1)
            return Task.FromResult("OK");

        var q1 = (from aa in dc.PanelNominations
            where aa.Id == id
            select aa).FirstOrDefault();

        var q2 = (from aa in dc.PanelNominations
            where aa.AssemblyId == q1.AssemblyId &&
                  aa.PanelId == q1.PanelId &&
                  aa.OrderNumber == order - 1 &&
                  aa.PhaseId == q1.PhaseId
            select aa).FirstOrDefault();

        q1.OrderNumber = order - 1;
        q1.ModifiedBy = user;
        q1.ModifiedDate = DateTime.Now;
        q2.OrderNumber = order;
        q2.ModifiedDate = DateTime.Now;
        q2.ModifiedBy = user;
        dc.SaveChanges();
        return Task.FromResult("OK");
    }
}