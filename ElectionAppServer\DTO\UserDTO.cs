﻿using System.ComponentModel.DataAnnotations;

namespace ElectionAppServer.DTO;

public class UserDTO
{
    public int Id { get; internal set; }

    [Required] [StringLength(200)] public string Name { get; internal set; }

    [Required] public int? DistrictId { get; internal set; }

    public string District { get; internal set; }

    [Required] [StringLength(10)] public string Code { get; internal set; }

    [DataType(DataType.EmailAddress, ErrorMessage = "Please enter valid email address")]
    public string Email { get; internal set; }

    [Required]
    [StringLength(11, MinimumLength = 11, ErrorMessage = "Please enter complete mobile number")]
    public string Mobile { get; internal set; }

    public int ElectionId { get; internal set; }
}