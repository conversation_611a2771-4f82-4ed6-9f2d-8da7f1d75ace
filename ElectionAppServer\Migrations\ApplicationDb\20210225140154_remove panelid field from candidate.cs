﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace ElectionAppServer.Migrations.ApplicationDb
{
	public partial class removepanelidfieldfromcandidate : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropForeignKey(
				 name: "FK_Candidates_Candidates_PanelId",
				 table: "Candidates");

			migrationBuilder.DropForeignKey(
				 name: "FK_PannelNominations_Candidates_PanelId",
				 table: "PannelNominations");

			migrationBuilder.DropIndex(
				 name: "IX_Candidates_PanelId",
				 table: "Candidates");

			migrationBuilder.DropColumn(
				 name: "PanelId",
				 table: "Candidates");

			migrationBuilder.AddForeignKey(
				 name: "FK_PannelNominations_Candidates_PanelId",
				 table: "PannelNominations",
				 column: "PanelId",
				 principalTable: "Candidates",
				 principalColumn: "Id",
				 onDelete: ReferentialAction.Cascade);
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropForeignKey(
				 name: "FK_PannelNominations_Candidates_PanelId",
				 table: "PannelNominations");

			migrationBuilder.AddColumn<int>(
				 name: "PanelId",
				 table: "Candidates",
				 type: "int",
				 nullable: true);

			migrationBuilder.CreateIndex(
				 name: "IX_Candidates_PanelId",
				 table: "Candidates",
				 column: "PanelId");

			migrationBuilder.AddForeignKey(
				 name: "FK_Candidates_Candidates_PanelId",
				 table: "Candidates",
				 column: "PanelId",
				 principalTable: "Candidates",
				 principalColumn: "Id",
				 onDelete: ReferentialAction.Restrict);

			migrationBuilder.AddForeignKey(
				 name: "FK_PannelNominations_Candidates_PanelId",
				 table: "PannelNominations",
				 column: "PanelId",
				 principalTable: "Candidates",
				 principalColumn: "Id",
				 onDelete: ReferentialAction.Restrict);
		}
	}
}
