﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace ElectionAppServer.Migrations.ApplicationDb
{
    /// <inheritdoc />
    public partial class Add_Offline_Election_Results : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "ResultSource",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Title = table.Column<string>(type: "varchar(200)", unicode: false, maxLength: 200, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ResultSource", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "OfflineResult",
                columns: table => new
                {
                    NominationId = table.Column<int>(type: "int", nullable: false),
                    ResultSourceId = table.Column<int>(type: "int", nullable: false),
                    ResPS = table.Column<int>(type: "int", nullable: true),
                    Votes = table.Column<int>(type: "int", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    ModifiedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    CreatedBy = table.Column<string>(type: "nvarchar(450)", maxLength: 450, nullable: true),
                    ModifiedBy = table.Column<string>(type: "nvarchar(450)", maxLength: 450, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_OfflineResult", x => x.NominationId);
                    table.ForeignKey(
                        name: "FK_OfflineResult_Nominations_NominationId",
                        column: x => x.NominationId,
                        principalTable: "Nominations",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_OfflineResult_ResultSource_ResultSourceId",
                        column: x => x.ResultSourceId,
                        principalTable: "ResultSource",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateIndex(
                name: "IX_OfflineResult_ResultSourceId",
                table: "OfflineResult",
                column: "ResultSourceId");

            migrationBuilder.CreateIndex(
                name: "IX_ResultSource_Title",
                table: "ResultSource",
                column: "Title",
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "OfflineResult");

            migrationBuilder.DropTable(
                name: "ResultSource");
        }
    }
}
