﻿@page "/admin/rolemenus"
@attribute [Authorize(Roles = "Administrators")]
@inject MenuService MenuService
@inject RoleManager<IdentityRole> RoleManager

<MudText Typo="Typo.h5">Menu Role Assignment</MudText>

@if (menues != null && roles != null)
{
    <table class="table table-bordered">
        <thead>
        <tr>
            <th>Menu</th>
            @foreach (var role in roles)
            {
                <th>@role.Name</th>
            }
        </tr>
        </thead>
        <tbody>
        @foreach (var menu in menues)
        {
            <tr>
                <td>@menu.Name</td>
                @foreach (var r in menu.Roles)
                {
                    <td>
                        <SfCheckBox @bind-Checked="r.HasAccess" @onchange="@(args => AssignMenuRight(r.Id, menu.Id, r.HasAccess))"/>
                        @*<input type="checkbox" @bind-Checked="r.HasAccess" />*@
                        @*@onclick="@((args) => AssignMenuRight(role.Id, menu.Id))"*@
                    </td>
                }
            </tr>
        }
        </tbody>
    </table>
}


@code {

    //private List<Menu> Menus { get; set; }
    //private List<IdentityRole> Roles { get; set; }
    private List<MenuDTO> menues { get; set; } = new();
    private List<RoleDTO> roles { get; set; }
    [CascadingParameter] private Task<AuthenticationState> authenticationStateTask { get; set; }

    //public object obj2 = new Dictionary<string, Dictionary<int, RoleMenuRightDTO>>();


    protected override async Task OnInitializedAsync()
    {
        var user = (await authenticationStateTask).User;
        await Task.CompletedTask;
        menues = await MenuService.GetAllMenusAsync();
        await Task.CompletedTask;
        roles = menues[0].Roles;
    }

    private bool GetHasMenuRight(string roleId, int menuId)
    {
        // Implement logic to check if the specified role has the specified menu right
        // This can involve querying a database or other data source
        return true;
    }

    private async Task AssignMenuRight(string roleId, int menuId, bool hasAccess)
    {
        // Implement logic to assign or revoke the menu right for the specified role
        // This can involve calling services or updating data sources
        // Update the HasMenuRight property after performing the operation
        var msg = await MenuService.SaveRoleMenuAccess(menuId, roleId, hasAccess);
        await Task.CompletedTask;
    }

}