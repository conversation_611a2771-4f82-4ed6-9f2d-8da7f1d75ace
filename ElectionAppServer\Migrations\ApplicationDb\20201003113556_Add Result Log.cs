﻿using Microsoft.EntityFrameworkCore.Migrations;
using System;

namespace ElectionAppServer.Migrations.ApplicationDb
{
	public partial class AddResultLog : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.CreateTable(
				 name: "ResultLogs",
				 columns: table => new
				 {
					 Id = table.Column<int>(nullable: false)
							.Annotation("SqlServer:Identity", "1, 1"),
					 ConstituencyId = table.Column<int>(nullable: false),
					 AssemblyId = table.Column<int>(nullable: false),
					 PhaseId = table.Column<int>(nullable: false),
					 SeatTypeId = table.Column<int>(nullable: false),
					 NamanigarId = table.Column<string>(nullable: true),
					 Action = table.Column<string>(nullable: true),
					 PreviousPollingStations = table.Column<int>(nullable: false),
					 NewPollingStations = table.Column<int>(nullable: false),
					 CreatedDate = table.Column<DateTime>(nullable: false),
					 CreatedByUserId = table.Column<string>(nullable: true)
				 },
				 constraints: table =>
				 {
					 table.PrimaryKey("PK_ResultLogs", x => x.Id);
					 table.ForeignKey(
							  name: "FK_ResultLogs_ElectionAssemblies_AssemblyId",
							  column: x => x.AssemblyId,
							  principalTable: "ElectionAssemblies",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Restrict);
					 table.ForeignKey(
							  name: "FK_ResultLogs_Structures_ConstituencyId",
							  column: x => x.ConstituencyId,
							  principalTable: "Structures",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Restrict);
					 table.ForeignKey(
							  name: "FK_ResultLogs_AspNetUsers_CreatedByUserId",
							  column: x => x.CreatedByUserId,
							  principalTable: "AspNetUsers",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Restrict);
					 table.ForeignKey(
							  name: "FK_ResultLogs_AspNetUsers_NamanigarId",
							  column: x => x.NamanigarId,
							  principalTable: "AspNetUsers",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Restrict);
					 table.ForeignKey(
							  name: "FK_ResultLogs_ElectionPhases_PhaseId",
							  column: x => x.PhaseId,
							  principalTable: "ElectionPhases",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Restrict);
					 table.ForeignKey(
							  name: "FK_ResultLogs_SeatTypes_SeatTypeId",
							  column: x => x.SeatTypeId,
							  principalTable: "SeatTypes",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Restrict);
				 });

			migrationBuilder.CreateTable(
				 name: "ResultLogDetails",
				 columns: table => new
				 {
					 Id = table.Column<int>(nullable: false)
							.Annotation("SqlServer:Identity", "1, 1"),
					 ResultLogId = table.Column<int>(nullable: false),
					 CandidateId = table.Column<int>(nullable: false),
					 PreviousVotes = table.Column<int>(nullable: false),
					 NewVotes = table.Column<int>(nullable: false)
				 },
				 constraints: table =>
				 {
					 table.PrimaryKey("PK_ResultLogDetails", x => x.Id);
					 table.ForeignKey(
							  name: "FK_ResultLogDetails_Candidates_CandidateId",
							  column: x => x.CandidateId,
							  principalTable: "Candidates",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Restrict);
					 table.ForeignKey(
							  name: "FK_ResultLogDetails_ResultLogs_ResultLogId",
							  column: x => x.ResultLogId,
							  principalTable: "ResultLogs",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Restrict);
				 });

			migrationBuilder.CreateIndex(
				 name: "IX_ResultLogDetails_CandidateId",
				 table: "ResultLogDetails",
				 column: "CandidateId");

			migrationBuilder.CreateIndex(
				 name: "IX_ResultLogDetails_ResultLogId",
				 table: "ResultLogDetails",
				 column: "ResultLogId");

			migrationBuilder.CreateIndex(
				 name: "IX_ResultLogs_AssemblyId",
				 table: "ResultLogs",
				 column: "AssemblyId");

			migrationBuilder.CreateIndex(
				 name: "IX_ResultLogs_ConstituencyId",
				 table: "ResultLogs",
				 column: "ConstituencyId");

			migrationBuilder.CreateIndex(
				 name: "IX_ResultLogs_CreatedByUserId",
				 table: "ResultLogs",
				 column: "CreatedByUserId");

			migrationBuilder.CreateIndex(
				 name: "IX_ResultLogs_NamanigarId",
				 table: "ResultLogs",
				 column: "NamanigarId");

			migrationBuilder.CreateIndex(
				 name: "IX_ResultLogs_PhaseId",
				 table: "ResultLogs",
				 column: "PhaseId");

			migrationBuilder.CreateIndex(
				 name: "IX_ResultLogs_SeatTypeId",
				 table: "ResultLogs",
				 column: "SeatTypeId");
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropTable(
				 name: "ResultLogDetails");

			migrationBuilder.DropTable(
				 name: "ResultLogs");
		}
	}
}
