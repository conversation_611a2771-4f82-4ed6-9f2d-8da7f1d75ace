﻿@page "/nominations2"
@inject IWebHostEnvironment env

@attribute [Authorize(Roles = "Administrators,Data Viewer")]

<MudText Class="mb-2" Typo="Typo.h5"><MudIcon Icon="@Icons.Material.Filled.PersonPin" Size="Size.Large"/> Nominations</MudText>
@using Microsoft.AspNetCore.Hosting
@using ButtonType = MudBlazor.ButtonType
@using FilterType = Syncfusion.Blazor.DropDowns.FilterType
@using Size = MudBlazor.Size
@inherits OwningComponentBase<NominationDataService>

<SfToast @ref="ToastObj" Title="Alert" ShowProgressBar="true" Timeout="3000">
    <ToastPosition X="Right"></ToastPosition>
</SfToast>


<SfDialog Width="820px" @bind-Visible="IsDlgPanelistVisibl" @ref="dlgPanelist" ShowCloseIcon="true" Title="Panelist" IsModal="true">
    <DialogTemplates>
        <Header>Panelist</Header>
        <Content>
            @if (IsPanelistFormOpen)
            {
                <EditForm Model="@panelistObj" OnValidSubmit="@SaveNominationPanelistData" Context="nf">
                    <DataAnnotationsValidator></DataAnnotationsValidator>
                    <div class="row mb-2">
                        <div class="col-md">
                            <SfTextBox FloatLabelType="FloatLabelType.Always" Placeholder="Candidate Name (English)"
                                       @bind-Value="panelistObj.NameEnglish" OnBlur="TranslateToUrdu">
                            </SfTextBox>
                            <ValidationMessage For="@(() => panelistObj.NameEnglish)"/>
                        </div>
                        <div class="col-md">
                            <SfTextBox FloatLabelType="FloatLabelType.Always" dir="rtl" Placeholder="Candidate Name (اردو)"
                                       @bind-Value="panelistObj.NameUrdu">
                            </SfTextBox>
                            <ValidationMessage For="@(() => panelistObj.NameUrdu)"/>
                        </div>
                    </div>
                    <div class="row mb-2">
                        <div class="col-md">
                            <BlazorInputFile.InputFile OnChange="HandleFileSelected" accept="image/jpeg,image/jpg"/>
                            <img alt="" src="data:image/jpg;base64,@FileBase64String" style="max-width: 150px;"/>
                        </div>
                    </div>
                    <div class="row mb-2">
                        <div class="col-md">
                            <SfDropDownList TItem="GeneralItemDTO" TValue="int?" DataSource="gendersList" Placeholder="Gender"
                                            FloatLabelType="FloatLabelType.Always" AllowFiltering="true" @bind-Value="panelistObj.Gender">
                                <DropDownListFieldSettings Value="Id" Text="EnglishTitle"></DropDownListFieldSettings>
                            </SfDropDownList>
                            <ValidationMessage For="@(() => panelistObj.Gender)"/>
                        </div>
                        <div class="col-md">
                            @if (AllSeatTypesList != null)
                            {
                                <SfDropDownList TItem="GeneralItemDTO" TValue="int?" DataSource="AllSeatTypesList" Placeholder="Seat Type"
                                                FloatLabelType="FloatLabelType.Always" AllowFiltering="true" @bind-Value="panelistObj.SeatTypeId"
                                                FilterType="FilterType.Contains">
                                    <DropDownListFieldSettings Value="Id" Text="EnglishTitle"></DropDownListFieldSettings>
                                </SfDropDownList>
                                <ValidationMessage For="@(() => panelistObj.SeatTypeId)"/>
                            }
                        </div>
                    </div>
                    <div class="row mb-2">
                        <div class="col-md">
                            <MudButton ButtonType="ButtonType.Submit" Color="Color.Primary" Size="Size.Small" Variant="Variant.Filled" StartIcon="@Icons.Material.Filled.Save">Save</MudButton>
                            <MudButton ButtonType="ButtonType.Button" Size="Size.Small" Variant="Variant.Filled" Color="Color.Secondary" OnClick="ClosePanelistForm">Cancel</MudButton>
                        </div>
                    </div>

                </EditForm>
            }
            else
            {
                <div class="row mb-2">
                    <div class="col-md">
                        <MudButton Color="Color.Primary" Size="Size.Small" Variant="Variant.Filled" StartIcon="@Icons.Material.Filled.Add" CssClass="e-primary" OnClick="OpenCreatePanelistForm">Create Panelist</MudButton>

                    </div>
                </div>
                <div class="row mb-2">
                    <div class="col-md">
                        @if (panelistList != null)
                        {
                            @if (panelistList.Any())
                            {
                                <SfGrid DataSource="@panelistList" AllowFiltering="true" AllowSorting="true" Width="100%">
                                    <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Excel"></GridFilterSettings>
                                    <GridColumns>
                                        <GridColumn HeaderText="Picture" Width="128px">
                                            <Template Context="jj">
                                                @{
                                                    var obj = jj as PanelistDTO;
                                                    // generate random number between 1 to 100000
                                                    var rnd = new Random().Next(1, 100000);

                                                    var imgPath = "/media/candidates/blank.jpg";
                                                    var fileExist = File.Exists(mediapath + "/panelcandidates/" + obj.Id + ".jpg");
                                                    if (fileExist)
                                                        imgPath = "/media/panelcandidates/" + obj.Id + ".jpg?rnd=" + rnd;

                                                    <img alt="" src="@imgPath" style="width:120px;height:120px;object-fit:cover"/>
                                                }
                                            </Template>
                                        </GridColumn>
                                        <GridColumn HeaderText="ID" Field="Id" AutoFit="true"/>
                                        <GridColumn HeaderText="Name" Field="NameEnglish" AutoFit="true"/>
                                        <GridColumn HeaderText="Name (اردو)" Field="NameUrdu" AutoFit="true"/>
                                        <GridColumn HeaderText="Seat Type" Field="SeatType" AutoFit="true"/>
                                        <GridColumn HeaderText="Gender" Field="GenderText" AutoFit="true"></GridColumn>
                                        <GridColumn HeaderText="Actions" Width="120px">
                                            <Template Context="kk">
                                                @{
                                                    var obj = kk as PanelistDTO;
                                                    <MudFab Size="Size.Small" Color="Color.Primary" StartIcon="@Icons.Material.Filled.Edit" OnClick="@(() => OpenPanelistEditForm(obj.Id))">
                                                    </MudFab>
                                                    <MudFab Color="Color.Error" Size="Size.Small"
                                                            StartIcon="@Icons.Material.Filled.Delete"
                                                            OnClick="@(() => DeletePanelistRecord(obj.Id))">

                                                    </MudFab>
                                                }
                                            </Template>
                                        </GridColumn>
                                    </GridColumns>
                                </SfGrid>
                            }
                            else
                            {
                                <p>No Panelist Found...</p>
                            }
                        }
                        else
                        {
                            <p>Please wait...</p>
                        }

                    </div>
                </div>
            }

        </Content>
    </DialogTemplates>
</SfDialog>

<SfDialog Width="1000px" @bind-Visible="@isDlgSearchCandVisible" @ref="dlgSeachCand" ShowCloseIcon="true" IsModal="true">
    <DialogEvents OnOpen="beforeOpen"></DialogEvents>
    <DialogTemplates>
        <Header>Search Candidate</Header>
        <Content>
            <div class="row" style="display:flex;gap:10px">
                <div class="col" style="display:flex">
                    <SfTextBox Placeholder="Search Candidate"
                               @bind-Value="searchText"
                               FloatLabelType="FloatLabelType.Never">
                    </SfTextBox>
                    <MudButton Size="Size.Small" Variant="Variant.Filled" Color="Color.Primary"
                               StartIcon="@Icons.Material.Filled.PersonSearch"
                               Class="ml-1"
                               OnClick="SearchCandidateAsync">
                        <i class="fas fa-search"></i>
                    </MudButton>
                </div>
            </div>
            <div class="row">
                <div class="col">
                    <SfGrid DataSource="candListFilter" AllowSorting="true" AllowTextWrap="true" AllowFiltering="true" Width="100%" EnableVirtualization="true" Height="400px">
                        <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Excel"></GridFilterSettings>
                        <GridColumns>
                            <GridColumn HeaderText="Code" Field="Id" AutoFit="true"></GridColumn>
                            <GridColumn Visible="state.state.ElectionType == ElectionType.LocalBody" HeaderText="Type" Field="Type" AutoFit="true"></GridColumn>
                            <GridColumn HeaderText="Name" AllowFiltering="true">
                                <Template Context="cc">
                                    @{
                                        var mm = cc as SearchCandidateDTO;
                                        <img src="@mm.ImgUrl" style="max-width:80px;margin:4px;border-radius:10px" align="left"/>
                                        <span>@mm.EnglishName</span>
                                        <br/>
                                        <span class="urdu-cap">@mm.UrduName</span>
                                    }
                                </Template>
                            </GridColumn>
                            <GridColumn HeaderText="Disrict" Field="Disrict"></GridColumn>
                            <GridColumn HeaderText="Last Constituency" Field="LastConstituency">
                                <Template Context="cc">
                                    @{
                                        var mm = cc as SearchCandidateDTO;
                                        // display mm.LastConstituency as html
                                        <div style="max-height:200px;overflow-y:auto">@((MarkupString)mm.LastConstituency)</div>
                                    }
                                </Template>
                            </GridColumn>
                            <GridColumn HeaderText="Audit">
                                <Template Context="cc">
                                    @{
                                        var mm2 = cc as SearchCandidateDTO;
                                        <span class="alog">Created By: <b>@mm2.CreatedBy</b> on @mm2.CreatedDate</span>
                                        if (mm2.ModifiedBy != "")
                                        {
                                            <br>
                                            <span class="alog">Modified By: <b>@mm2.ModifiedBy</b> on @mm2.ModifiedDate</span>
                                        }
                                    }
                                </Template>
                            </GridColumn>
                            <GridColumn HeaderText="Action" Width="110px" TextAlign="TextAlign.Center">
                                <Template Context="cc">
                                    @{
                                        var mm = cc as SearchCandidateDTO;
                                        <MudButton Color="Color.Info" Size="Size.Small" Variant="Variant.Filled"
                                                   StartIcon="@Icons.Material.Filled.CheckBox" OnClick="@(() => SelectCandidate(mm.Id, mm.PartyId, mm.SymbolId, mm.EnglishName))">
                                            Select
                                        </MudButton>
                                    }
                                </Template>
                            </GridColumn>
                        </GridColumns>
                    </SfGrid>
                </div>
            </div>
        </Content>
    </DialogTemplates>
</SfDialog>

<SfDialog Width="800px" @bind-Visible="@isDlgVisible" @ref="dlgForm" IsModal="true" ShowCloseIcon="true">
    <DialogEvents OnOpen="beforeOpen"></DialogEvents>
    <DialogTemplates>
        <Header>Create Nomination</Header>
        <Content>
            <EditForm Model="@selectedObj" OnValidSubmit="@SaveData" Context="ef">
                <FluentValidationValidator/>
                <ValidationSummary/>
                <div class="row">
                    <div class="col-8">
                        <div class="row">
                            <div class="col">
                                <AuthorizeView Roles="Administrators">
                                    <button type="button" @onclick="OpenSearchCandidate" class="btn btn-primary btn-sm">Search</button>

                                    @*<span>Total Candidates: @candListAll.Count</span>*@
                                </AuthorizeView>
                                @if (true /*candListAll != null*/)
                                {
                                    @*<SfDropDownList DataSource="candListAll" TItem="SearchCandidateDTO" TValue="int?" FloatLabelType="FloatLabelType.Always" Placeholder="Select Candidate" AllowFiltering="true" @bind-Value="selectedObj.CandidateId" EnableVirtualization="true" Query="@LocalDataQuery" >
										<DropDownListTemplates TItem="SearchCandidateDTO" Context="kk">
										<ItemTemplate>
										@{
										var c = kk as SearchCandidateDTO;
										var cim = $"media/candidates/{c.Id}" + ".jpg";

										<div style="display:flex">
										<img src="@c.ImgUrl" style="max-height:80px; max-width:70px; margin:2px" alt="" />
										<span>@($"{c.Id} - {c.EnglishName}")</span>
										</div>
										}
										</ItemTemplate>
										</DropDownListTemplates>
										<DropDownListFieldSettings Value="Id" Text="EnglishName"></DropDownListFieldSettings>
										<DropDownListEvents TItem="SearchCandidateDTO" ValueChange="OnCandidateSelect" TValue="int?"></DropDownListEvents>
										</SfDropDownList>*@
                                    <SfTextBox Readonly="true" Value="@CandidateName" Placeholder="Candidate" FloatLabelType="FloatLabelType.Always"></SfTextBox>
                                    <ValidationMessage For="@(() => selectedObj.CandidateId)"/>
                                }
                            </div>
                        </div>
                        <div class="row">
                            <div class="col">
                                @if (partiesList != null)
                                {
                                    <SfDropDownList TItem="GeneralItemDTO" TValue="int?" DataSource="partiesList" Placeholder="Party"
                                                    FilterType="FilterType.Contains"
                                                    FloatLabelType="FloatLabelType.Always" AllowFiltering="true" @bind-Value="selectedObj.PartyId">
                                        <DropDownListFieldSettings Value="Id" Text="EnglishTitle"></DropDownListFieldSettings>
                                        <DropDownListEvents TValue="int?" TItem="GeneralItemDTO" ValueChange="OnPartyChange"></DropDownListEvents>
                                    </SfDropDownList>
                                }
                                else
                                {
                                    <p>No party is defined</p>
                                }
                                <ValidationMessage For="@(() => selectedObj.PartyId)"/>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col">
                                @if (symbolsList != null)
                                {
                                    <SfComboBox TItem="GeneralItemDTO" TValue="int?" DataSource="symbolsList" Placeholder="Party Symbol" FloatLabelType="FloatLabelType.Always" AllowFiltering="true" @bind-Value="selectedObj.SymbolId">
                                        <ComboBoxFieldSettings Value="Id" Text="EnglishTitle"></ComboBoxFieldSettings>
                                        <ComboBoxEvents TItem="GeneralItemDTO" TValue="int?" ValueChange="OnSymbolChange"></ComboBoxEvents>
                                    </SfComboBox>
                                }
                                else
                                {
                                    <p>No symbol is defined</p>
                                }
                                <ValidationMessage For="@(() => selectedObj.SymbolId)"/>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col">
                                <SfNumericTextBox ShowSpinButton="false" @bind-Value="selectedObj.Weight" TValue="int" Min="0" Max="200" Format="d" Decimals="0" Placeholder="Weight" FloatLabelType="FloatLabelType.Always"></SfNumericTextBox>
                                <ValidationMessage For="@(() => selectedObj.Weight)"/>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col">
                                <div class="hh">Party Flag</div>

                                <img src="@selectedObj.PartyFlagURL" alt="" style="width:120px"/>
                            </div>
                            <div class="col">
                                <div class="hh">Symbol</div>
                                @{
                                    <img src="@selectedObj.SymbolURL" alt="" style="width:120px"/>
                                }
                            </div>
                        </div>
                        <AuthorizeView Roles="Administrators">
                            <div class="row mt-2">
                                <div class="col">
                                    <MudButton ButtonType="ButtonType.Submit" Color="Color.Primary" Size="Size.Small" Variant="Variant.Filled" StartIcon="@Icons.Material.Filled.Save">Save</MudButton>
                                    @if (selectedObj.Id != 0 && selectedObj.IsWithdraw == false)
                                    {
                                        <MudButton ButtonType="ButtonType.Button" Color="Color.Warning" Size="Size.Small" Variant="Variant.Filled" OnClick="WithdrawNomination" CssClass="e-Danger">Withdraw</MudButton>
                                    }
                                    else
                                    {
                                        <MudButton ButtonType="ButtonType.Button" Color="Color.Info" Size="Size.Small" OnClick="ReEnterNomination" CssClass="e-Danger">Re-Enter</MudButton>
                                    }
                                </div>
                            </div>
                        </AuthorizeView>
                    </div>
                    <div class="col-4">
                        <div class="row">
                            <div class="col">
                                @{
                                    if (selectedObj.CandidateId != null)
                                    {
                                        <img src="@selectedObj.CandidateURL" class="bimg"/>
                                    }
                                }
                            </div>
                        </div>
                    </div>
                </div>
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>

<section>
<div class="row">
    <div class="col-5">
        <label>Assembly</label>
        @*<DxComboBox TextFieldName="EnglishTitle" NullText="Select Assembly" Data="@assembliesList" SelectedItem="@selectedAssembly" SelectedItemChanged="@OnChangeAssembly"></DxComboBox>*@
        @if (assembliesList != null)
        {
            @if (assembliesList.Any())
            {
                <SfDropDownList Enabled="!EditConstituencyMode" TValue="int?" TItem="GeneralItemDTO" @bind-Value="SelectedAssemblyId"
                                DataSource="@assembliesList"
                                AllowFiltering="true" FilterType="FilterType.Contains" ShowClearButton="true">
                    <DropDownListFieldSettings Value="Id" Text="EnglishTitle"></DropDownListFieldSettings>
                    <DropDownListEvents TItem="GeneralItemDTO" TValue="int?" ValueChange="@FilterByAssembly"></DropDownListEvents>
                </SfDropDownList>
            }
            else
            {
                <p>No assembly found on this election</p>
            }
        }
    </div>
    <div class="col-5">
        <label>Constituency</label>
        @*<DxComboBox TextFieldName="EnglishTitle" NullText="Select Constituency" Data="@constituenciesList" SelectedItem="@selectedConstituency" SelectedItemChanged="@OnChangeConstituency"></DxComboBox>*@
        @if (constituenciesList == null)
        {
            <span></span>
        }
        else if (constituenciesList.Any())
        {
            <SfDropDownList Enabled="!EditConstituencyMode" TValue="int?" TItem="GeneralItemDTO" @bind-Value="SelectedConstituencyId" DataSource="@constituenciesList"
                            AllowFiltering="true" FilterType="FilterType.Contains" ShowClearButton="true">
                <DropDownListFieldSettings Value="Id" Text="EnglishTitle"></DropDownListFieldSettings>
                <DropDownListEvents TItem="GeneralItemDTO" TValue="int?" ValueChange="@FilterByConstituency"></DropDownListEvents>
            </SfDropDownList>
        }
        else
        {
            @if (SelectedAssemblyId != null)
            {
                <p>No Constituency is defined on this assembly</p>
            }
        }
    </div>

    <div class="col-2">
        <label>Seat Type</label>
        @*<DxComboBox TextFieldName="EnglishTitle" NullText="Select Seat Type" Data="@SeatTypesList" SelectedItem="@selectedSeatType" SelectedItemChanged="@OnChangeSeatType"></DxComboBox>*@
        @if (seatTypesList != null)
        {
            @if (seatTypesList.Any())
            {
                <SfDropDownList Enabled="!EditConstituencyMode" TValue="int?" TItem="GeneralItemDTO" @bind-Value="SelectedSeatTypeId" DataSource="@seatTypesList"
                                AllowFiltering="true" FilterType="FilterType.Contains" ShowClearButton="true">
                    <DropDownListFieldSettings Value="Id" Text="EnglishTitle"></DropDownListFieldSettings>
                    <DropDownListEvents TItem="GeneralItemDTO" TValue="int?" ValueChange="@FilterBySeatType"></DropDownListEvents>
                </SfDropDownList>
            }
            else
            {
                <p>&nbsp;</p>
            }

            @*<DxComboBox Data="seatTypesList"
					TextFieldName="EnglishTitle"
					@bind-Value="@selectedSeatType"
					SelectedItemChanged="@OnChangeSeatType"
					SizeMode="SizeMode.Medium">
					</DxComboBox>*@
        }
    </div>
</div>
@if (selectedAssembly != null && selectedConstituency != null && selectedSeatType != null)
{
    <AuthorizeView Roles="Administrators">
        <div class="mt-1 mb-1">

            @if (nominations != null)
            {
                @if (!nominations.Any(k => k.BilaMokabila == "Yes"))
                {
                    <MudButton Color="Color.Primary"
                               Size="Size.Small" Variant="Variant.Filled"
                               StartIcon="@Icons.Material.Filled.CreateNewFolder"
                               OnClick="OpenCreateForm">
                        Create Nomination
                    </MudButton>
                    @if (nominations.Count == 1)
                    {
                        <MudButton Type="MudBlazor.ButtonType.Button"
                                   Color="Color.Info" Size="Size.Small" Variant="Variant.Filled" Class="ml-1"
                                   OnClick="@(() => MarkWinner())">
                            Bila Muqabala Winner
                        </MudButton>
                    }
                }
                else
                {
                    <MudButton Type="MudBlazor.ButtonType.Button" Color="Color.Secondary"
                               Size="Size.Small" Variant="Variant.Filled"
                               Class="e-info mb-2 mt-2" OnClick="@(() => RemoveBilaMoqbila())">
                        Remove Bila Muqabala Winner
                    </MudButton>
                }
            }
            @if (nominations != null && nominations.Count == 1)
            {
            }
        </div>

    </AuthorizeView>
    @if (constDetail != null)
    {
        <div class="card bg-info mb-3 ">
            <div class="card-header" style="display: flex; justify-content: space-between;align-items: center">
                <div style="display: flex;align-items: center; gap:7px">
                    @if (constDetail.BigContest)
                    {
                        <img src="/images/bigcontest.png" alt="Big Contest" style="width:65px"/>
                    }
                    <div>
                        Constituency @constDetail.Code @constDetail.Name - @constDetail.Assembly
                    </div>

                </div>
                <div class="urdu-cap" style="text-align: right;direction: rtl">انتخابی حلقہ @constDetail.Code @constDetail.UrduName - @constDetail.AssemblyUrdu</div>
            </div>
            <div class="card-body bg-light" style="color:black">
                <div class="row mb-2">
                    <div class="col">
                        <b>Election:</b> @constDetail.Election
                    </div>
                    <div class="col">
                        <b>Phase:</b> @constDetail.Phase
                    </div>
                    <div class="col">
                        <b>Assembly:</b> @constDetail.Assembly
                    </div>
                </div>

                <div class="row mb-2">
                    <div class="col">
                        @if (constDetail.TotalVoters == 0)
                        {
                            <b>Total Register Voters: </b>
                            <span style='color:red;font-weight:bold'>Missing</span>
                        }
                        else
                        {
                            <b>Total Register Voters: </b>

                            @constDetail.TotalVoters
                        }
                    </div>
                    <div class="col">
                        <b>Male Voters: </b>@(constDetail.MaleVoters ?? 0)
                    </div>
                    <div class="col">
                        <b>Female Voters: </b>@(constDetail.FemaleVoters ?? 0)
                    </div>
                </div>
                @if (EditConstituencyMode)
                {
                    <div class="row mb-2">
                        <div class="col-md">
                            <SfNumericTextBox ShowSpinButton="false" @bind-Value="BasicConstInfo.MaleVoters" Placeholder="Male Voters" FloatLabelType="FloatLabelType.Always"></SfNumericTextBox>
                        </div>
                        <div class="col-md">
                            <SfNumericTextBox ShowSpinButton="false" @bind-Value="BasicConstInfo.FemaleVoters" Placeholder="Female Voters" FloatLabelType="FloatLabelType.Always"></SfNumericTextBox>
                        </div>

                        <div class="col-md">
                            <SfNumericTextBox ShowSpinButton="false" @bind-Value="BasicConstInfo.TotalPollingStations" Placeholder="Total Polling Stations" FloatLabelType="FloatLabelType.Always"></SfNumericTextBox>
                        </div>
                        <div class="col-md">
                            <SfNumericTextBox ShowSpinButton="false" @bind-Value="BasicConstInfo.Population" Placeholder="Population" FloatLabelType="FloatLabelType.Always"></SfNumericTextBox>
                        </div>
                        <div class="col-md">
                            <SfNumericTextBox ShowSpinButton="false" @bind-Value="BasicConstInfo.YouthVoters" Placeholder="Youth Voters" FloatLabelType="FloatLabelType.Always"></SfNumericTextBox>
                        </div>
                        <div class="col-md">
                            <SfNumericTextBox ShowSpinButton="false" @bind-Value="BasicConstInfo.RejectedVotes" Placeholder="Rejected Voters" FloatLabelType="FloatLabelType.Always"></SfNumericTextBox>
                        </div>

                    </div>
                    <div class="row mb">
                        <div class="col-md">

                            <label>Re-Poll</label>
                            <SfSwitch @bind-Checked="BasicConstInfo.IsRePoll"></SfSwitch> <span>@(BasicConstInfo.IsRePoll ? "Yes" : "No")</span>


                        </div>
                        <div class="col-md">
                            <label>Big Contest</label>
                            <SfSwitch @bind-Checked="BasicConstInfo.IsBigContest"></SfSwitch> <span>@(BasicConstInfo.IsBigContest ? "Yes" : "No")</span>

                        </div>
                        <div class="col">
                            <MudButton Color="Color.Primary"
                                       Size="Size.Small"
                                       Variant="Variant.Filled"
                                       StartIcon="@Icons.Material.Filled.Save"
                                       @onclick="UpdateConstituencyInfo">
                                Save Constituency Info
                            </MudButton>
                            <MudButton Color="Color.Secondary"
                                       Size="Size.Small"
                                       Variant="Variant.Filled"
                                       ButtonType="ButtonType.Button"
                                       @onclick="CloseEditForm">
                                Cancel
                            </MudButton>
                        </div>
                    </div>
                }
                else
                {
                    <div class="row mb-2">
                        <div class="col">
                            @if (constDetail.TotalPollingStations == null)
                            {
                                <b>Total Polling Stations: </b>
                                <span style='color:red;font-weight:bold'>Missing</span>
                            }
                            else
                            {
                                <b>Total Polling Stations: </b>

                                @constDetail.TotalPollingStations
                            }
                        </div>
                        <div class="col">
                            <b>Total Population: </b>@constDetail.Population
                        </div>
                        <div class="col"></div>
                    </div>
                    <div class="row mb-2">
                        <div class="col">
                            <b>Province: </b> @constDetail.Province
                        </div>
                        <div class="col">
                            <b>District: </b> @constDetail.District
                        </div>
                        <div class="col">
                            <b>Region: </b> @constDetail.Region
                        </div>
                    </div>
                    <div class="row mb-2">

                        @if (!string.IsNullOrEmpty(constDetail.Town))
                        {
                            <div class="col">
                                <b>Town: </b> @constDetail.Town
                            </div>
                        }
                        @if (!string.IsNullOrEmpty(constDetail.UnionCouncil))
                        {
                            <div class="col">
                                <b>Union Council: </b> @constDetail.UnionCouncil
                            </div>
                        }
                    </div>
                    <div class="row mb-2">
                        @if (constDetail.IsRePoll)
                        {
                            <div class="col-md">
                                <label>Status</label>
                                <span style="font-weight:bold;color:red"> : RE-POLL</span>
                            </div>
                        }

                    </div>
                    <div class="row mb-2">
                        <div class="col">
                            <AuthorizeView Roles="Administrators">
                                <MudButton ButtonType="ButtonType.Button"
                                           Size="Size.Small" Color="Color.Primary" Variant="Variant.Text"
                                           StartIcon="@Icons.Material.Filled.Edit"
                                           @onclick="OpenConstituencyForm" CssClass="e-link">
                                    Edit Constituency
                                </MudButton>
                            </AuthorizeView>
                        </div>
                    </div>
                }
            </div>
        </div>
    }

    <div class="card bg-primary text-white mb-3">
        <div class="card-header" style="display: flex; justify-content: space-between;align-items: center">
            <div>Nominations for @selectedSeatType.EnglishTitle</div>
            <div class="urdu-cap">امیدوار برائے @selectedSeatType.UrduTitle</div>
        </div>
        <div class="card-body bg-light text-black" style="color:black">
            <table class="table table-sm">
                <thead>
                <tr>
                    <th>Pic</th>
                    <th>Code</th>
                    @if (state.state.ElectionType == ElectionType.LocalBody)
                    {
                        <th>Type</th>
                    }
                    <th>Name</th>
                    <th>Party</th>
                    <th>Symbol</th>
                    <th>Weight</th>
                    <th>Status</th>
                    <th>Audit Info</th>
                    <th>&nbsp;</th>
                </tr>
                </thead>
                <tbody>
                <Virtualize Context="n" Items="nominations">
                    <tr>
                        <td>
                            <img src="@n.CandidateURL" style="width:75px; height:100px" class="myimg"/>
                        </td>
                        <td>@n.CandidateId</td>
                        @if (state.state.ElectionType == ElectionType.LocalBody)
                        {
                            <td>@n.Type</td>
                        }
                        <td>
                            @n.EnglishName<br/>
                            <span class="urdu-cap">@n.UrduName</span>
                        </td>
                        <td>
                            <img src="@n.PartyFlagURL" class="myimg" align="left"/>@n.Party<br>
                            <span class="urdu-cap">@n.PartyUrdu</span>
                        </td>
                        <td>
                            <img src="@n.SymbolURL" class="myimg" align="left"/>@n.Symbol<br>
                            <span class="urdu-cap">@n.SymbolUrdu</span>
                        </td>
                        <td>@n.Weight</td>
                        <td>@n.Status</td>
                        <td>
                            <div style="font-size:11px">
                                <span>Created By: @n.CreatedBy</span><span> On @n.CreatedOn</span>
                                @if (!string.IsNullOrEmpty(n.ModifiedBy))
                                {
                                    <br/>
                                    <span>Modified By: @n.ModifiedBy</span>
                                    <span> On @n.ModifiedOn</span>
                                }
                            </div>
                        </td>
                        <td>

                            <AuthorizeView Roles="Administrators">
                                @if (nominations.Any(k => k.BilaMokabila == "Yes"))
                                {
                                    <p>&nbsp;</p>
                                }
                                else
                                {
                                    <MudFab Color="Color.Primary" Size="Size.Small"
                                            StartIcon="@Icons.Material.Filled.Edit"
                                            OnClick="@(() => OpenEditForm(n))">
                                    </MudFab>
                                    <MudFab Color="Color.Error" Size="Size.Small"
                                            StartIcon="@Icons.Material.Filled.Delete"
                                            OnClick="@(() => DeleteRecord(n.Id))">
                                    </MudFab>

                                    @if (n.Type == "Panel")
                                    {
                                        <MudButton Size="Size.Small" Variant="Variant.Filled"
                                                   Color="Color.Success"
                                                   StartIcon="@Icons.Material.Filled.People"
                                                   ButtonType="ButtonType.Button"
                                                   OnClick="@(() => ShowPanelist(n.Id))">
                                            Panelist
                                        </MudButton>
                                    }
                                }

                            </AuthorizeView>

                        </td>
                    </tr>
                </Virtualize>
                @*@if (nominations != null)
							{
							@foreach (var n in nominations)
							{

							}
							}*@
                </tbody>
            </table>
        </div>
    </div>
}
</section>