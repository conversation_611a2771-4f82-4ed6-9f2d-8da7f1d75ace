﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using Dapper;
using ElectionAppServer.DTO;
using ElectionAppServer.Model;
using ElectionAppServer.Pages.Live;
using Microsoft.EntityFrameworkCore;

namespace ElectionAppServer.Data;

public class NamanigarDataService(IDbContextFactory<ApplicationDbContext> contextFactory)
{
    public Task<string> RemoveCons(int namanigarId, List<AssignConstNamanigarDTO> constituencyNamanigars)
    {
        using var dc = contextFactory.CreateDbContext();
        using var tran = dc.Database.BeginTransaction();
        try
        {
            foreach (var cc in constituencyNamanigars)
            {
                var q = (from aa in dc.NamanigarConstituencies
                    where aa.NamanigarId == namanigarId &&
                          aa.ConstituencyId == cc.Id
                    select aa).FirstOrDefault();

                if (q != null)
                {
                    dc.NamanigarConstituencies.Remove(q);
                    dc.SaveChanges();
                }
            }

            tran.Commit();
            return Task.FromResult("OK");
        }
        catch (Exception ex)
        {
            tran.Rollback();
            return Task.FromResult(ex.Message);
        }
    }

    public Task<string> AssignCons(int namanigarId, List<AssignConstNamanigarDTO> constituencyNamanigars, string userId)
    {
        using var dc = contextFactory.CreateDbContext();
        using var tran = dc.Database.BeginTransaction();
        try
        {
            foreach (var cc in constituencyNamanigars)
            {
                var q = (from aa in dc.NamanigarConstituencies
                    where aa.NamanigarId == namanigarId &&
                          aa.ConstituencyId == cc.Id
                    select aa).FirstOrDefault();

                if (q == null)
                {
                    q = new NamanigarConstituency
                    {
                        ConstituencyId = cc.Id,
                        CreatedBy = userId,
                        CreatedDate = DateTime.Now,
                        NamanigarId = namanigarId,
                        PSDetail = "All PS"
                    };
                    dc.NamanigarConstituencies.Add(q);
                    dc.SaveChanges();
                }
            }

            tran.Commit();
            return Task.FromResult("OK");
        }
        catch (Exception ex)
        {
            tran.Rollback();
            return Task.FromResult(ex.Message);
        }
    }

    public Task<string> Delete(int id)
    {
        using var dc = contextFactory.CreateDbContext();
        var q = dc.Namanigars.Find(id);

        if (q != null)
        {
            var relatedRecord = dc.NamanigarConstituencies.Any(x => x.NamanigarId == id);
            if (relatedRecord) throw new Exception("This namanigar is associated with one or more constituencies");
            dc.Namanigars.Remove(q);
            dc.SaveChanges();
        }

        return Task.FromResult("OK");
    }

    public Task<List<NamanigarFormDTO>> GetAll(int phaseId=0)
    {
        using var dc = contextFactory.CreateDbContext();
        var q = (from aa in dc.Namanigars
            orderby aa.Name
            select new NamanigarFormDTO
            {
                Id = aa.Id,
                Name = aa.Name,
                Phone = aa.Phone,
                Phone2 = aa.Phone2,
                Phone3 = aa.Phone3,
                Phone4 = aa.Phone4,
                Email = aa.Email,
                Code = aa.Code,
                District = aa.District,
                IsActive = aa.IsActive
            }).ToList();

        foreach (var nn in q)
        {
            if(!string.IsNullOrEmpty(nn.Phone2))
                nn.Phone += ", " + nn.Phone2;
            if(!string.IsNullOrEmpty(nn.Phone3))
                nn.Phone += ", " + nn.Phone3;
            if(!string.IsNullOrEmpty(nn.Phone4))
                nn.Phone += ", " + nn.Phone4;
            nn.IsInCurrentPhase = dc.PhaseWiseNamanigars.Any(c => c.NamanigarId == nn.Id && c.PhaseId == phaseId) ? "Yes" : "";
        }

        return Task.FromResult(q);
    }

    public Task<List<AssignConstNamanigarDTO>> GetNamanigarConstituencies(int electionId, int namanigarId)
    {
        using var dc = contextFactory.CreateDbContext();
        var con = dc.Database.GetDbConnection();

        var prams = new { NamanigarId = namanigarId, ElectionId = electionId };

        var res = con
            .Query<AssignConstNamanigarDTO>("GetNamanigarConstituencies", prams,
                commandType: CommandType.StoredProcedure)
            .ToList();

        return Task.FromResult(res);
    }

    public Task<NamanigarFormDTO> GetById(int id)
    {
        using var dc = contextFactory.CreateDbContext();
        var q = (from aa in dc.Namanigars
            where aa.Id == id
            select new NamanigarFormDTO
            {
                Id = aa.Id,
                Name = aa.Name,
                Phone = aa.Phone,
                Phone2 = aa.Phone2,
                Phone3 = aa.Phone3,
                Phone4 = aa.Phone4,
                Email = aa.Email,
                Code = aa.Code,
                District = aa.District,
                IsActive = aa.IsActive
            }).FirstOrDefault();
        return Task.FromResult(q);
    }

    public Task<NamanigarFormDTO> Save(NamanigarFormDTO obj, string user)
    {
        using var dc = contextFactory.CreateDbContext();
        obj.Code = obj.Code.Trim();
        obj.Phone = (obj.Phone ?? "").Trim();
        obj.Phone2 = (obj.Phone2 ?? "").Trim();
        obj.Phone3 = (obj.Phone3 ?? "").Trim();
        obj.Phone4 = (obj.Phone4 ?? "").Trim();
        obj.Email = (obj.Email ?? "").Trim();
        obj.Name = (obj.Name ?? "").Trim();
        obj.District = (obj.District ?? "").Trim();
        var alreadyExist = (from aa in dc.Namanigars
            where aa.Code == obj.Code
                  && aa.Id != obj.Id
            select aa).Any();
        if (alreadyExist)
            throw new Exception("Code already exist");

        if (obj.Id == 0)
        {
            var cc = new Namanigar
            {
                Code = obj.Code.Trim(),
                Name = obj.Name,
                Email = obj.Email,
                Phone = obj.Phone,
                Phone2 = obj.Phone2,
                Phone3 = obj.Phone3,
                Phone4 = obj.Phone4,
                District = obj.District,
                CreatedBy = user,
                CreatedDate = DateTime.Now,
                IsActive = obj.IsActive
            };
            dc.Namanigars.Add(cc);
            dc.SaveChanges();
            obj.Id = cc.Id;
            return Task.FromResult(obj);
        }
        else
        {
            var cc = dc.Namanigars.Find(obj.Id);
            if (cc != null)
            {
                cc.Name = obj.Name.Trim();
                cc.Code = obj.Code;
                cc.Email = obj.Email;
                cc.Phone = obj.Phone;
                cc.Phone2 = obj.Phone2;
                cc.Phone3 = obj.Phone3;
                cc.Phone4 = obj.Phone4;
                cc.District = obj.District;
                cc.ModifiedBy = user;
                cc.ModifiedDate = DateTime.Now;
                cc.IsActive = obj.IsActive;
            }

            dc.SaveChanges();
            return Task.FromResult(obj);
        }
    }

    public Task<NamanigarPerformanceDTO> GetNamanigarPerformance(int namanigarId, int phaseId)
    {
        var dc = contextFactory.CreateDbContext();
        var q = (from a in dc.NamanigarPerformances
            where a.NamanigarId == namanigarId && a.ElectionPhaseId == phaseId
            select new NamanigarPerformanceDTO
            {
                BehavioralIssueDescription = a.BehavioralIssueDescription,
                DocumentEfficiency = a.DocumentEfficiency, ElectionPhaseId = a.ElectionPhaseId,
                HasBehavioralIssues = a.HasBehavioralIssues,
                NamanigarId = a.NamanigarId, Trivia = a.Trivia, PreElectionInfoAccuracy = a.PreElectionInfoAccuracy,
                PreElectionPerformance = a.PreElectionPerformance,
                Reachability = a.Reachability, ResultAccuracy = a.ResultAccuracy,
                ResultDayPerformance = a.ResultDayPerformance, WasAvailableDuringResult = a.WasAvailableDuringResult,
                ReportingFor = a.ReportingFor
            }).FirstOrDefault();
        return Task.FromResult(q);
    }

    public Task<string> SaveNamanigarPerformance(NamanigarPerformanceDTO obj, string userId)
    {
        var dc = contextFactory.CreateDbContext();
        var p = dc.NamanigarPerformances.FirstOrDefault(c =>
            c.NamanigarId == obj.NamanigarId && c.ElectionPhaseId == obj.ElectionPhaseId);
        var isNew = false;

        if (p == null)
        {
            isNew = true;
            p = new NamanigarPerformance();
        }

        p.BehavioralIssueDescription = obj.BehavioralIssueDescription;

        if (isNew)
        {
            p.CreatedBy = userId;
            p.CreatedDate = DateTime.Now;
        }

        p.DocumentEfficiency = obj.DocumentEfficiency;
        p.ElectionPhaseId = obj.ElectionPhaseId!.Value;
        p.HasBehavioralIssues = obj.HasBehavioralIssues;

        p.ModifiedBy = userId;
        p.ModifiedDate = DateTime.Now;

        p.NamanigarId = obj.NamanigarId!.Value;
        p.PreElectionInfoAccuracy = obj.PreElectionInfoAccuracy;
        p.Trivia = obj.Trivia;
        p.PreElectionPerformance = obj.PreElectionPerformance;
        p.Reachability = obj.Reachability;
        p.ResultAccuracy = obj.ResultAccuracy;
        p.ResultDayPerformance = obj.ResultDayPerformance;
        p.WasAvailableDuringResult = obj.WasAvailableDuringResult;
        p.ReportingFor = obj.ReportingFor;
        if (isNew)
            dc.NamanigarPerformances.Add(p);

        dc.SaveChanges();

        return Task.FromResult("OK");
    }

    public Task<string> AddNamanigarToElection(int phaseId, int namanigarId, string userId)
    {
        var dc = contextFactory.CreateDbContext();
        var p = dc.PhaseWiseNamanigars.FirstOrDefault(c =>
            c.NamanigarId == namanigarId && c.PhaseId == phaseId);
        if (p == null)
        {
            p = new PhaseWiseNamanigar()
            {
                PhaseId = phaseId, NamanigarId = namanigarId, CreatedBy = userId, CreatedDate = DateTime.Now
            };
            dc.PhaseWiseNamanigars.Add(p);
            dc.SaveChanges();
            return Task.FromResult("OK");
        }
        else
        {
            return Task.FromResult("Namanigar already added to this phase");
        }

    }

    public Task<string> RemoveNamanigarFromElection(int phaseId, int namanigarId, string userId)
    {
        var dc = contextFactory.CreateDbContext();
        var p = dc.PhaseWiseNamanigars.FirstOrDefault(c =>
            c.NamanigarId == namanigarId && c.PhaseId == phaseId);
        if (p != null)
        {
            dc.PhaseWiseNamanigars.Remove(p);
            dc.SaveChanges();
            return Task.FromResult("OK");
        }
        else
        {
            return Task.FromResult("Namanigar not found in this phase");
        }
    }
}