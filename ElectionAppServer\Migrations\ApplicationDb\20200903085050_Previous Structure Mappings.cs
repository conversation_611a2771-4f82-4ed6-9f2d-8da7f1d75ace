﻿using Microsoft.EntityFrameworkCore.Migrations;
using System;

namespace ElectionAppServer.Migrations.ApplicationDb
{
	public partial class PreviousStructureMappings : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.AlterColumn<DateTime>(
				 name: "CreatedDate",
				 table: "ElectionParties",
				 nullable: false,
				 defaultValue: new DateTime(2020, 9, 3, 13, 50, 49, 800, DateTimeKind.Local).AddTicks(6005),
				 oldClrType: typeof(DateTime),
				 oldType: "datetime2",
				 oldDefaultValue: new DateTime(2020, 9, 2, 19, 44, 2, 312, DateTimeKind.Local).AddTicks(9430));

			migrationBuilder.AlterColumn<DateTime>(
				 name: "CreatedDate",
				 table: "ElectionCandidates",
				 nullable: false,
				 defaultValue: new DateTime(2020, 9, 3, 13, 50, 49, 806, DateTimeKind.Local).AddTicks(2681),
				 oldClrType: typeof(DateTime),
				 oldType: "datetime2",
				 oldDefaultValue: new DateTime(2020, 9, 2, 19, 44, 2, 318, DateTimeKind.Local).AddTicks(2256));

			migrationBuilder.CreateTable(
				 name: "StructureMapping",
				 columns: table => new
				 {
					 StructureId = table.Column<int>(nullable: false),
					 PreviousStructureId = table.Column<int>(nullable: false),
					 Description = table.Column<string>(maxLength: 200, nullable: true)
				 },
				 constraints: table =>
				 {
					 table.PrimaryKey("PK_StructureMapping", x => new { x.StructureId, x.PreviousStructureId });
					 table.ForeignKey(
							  name: "FK_StructureMapping_Structures_PreviousStructureId",
							  column: x => x.PreviousStructureId,
							  principalTable: "Structures",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Restrict);
					 table.ForeignKey(
							  name: "FK_StructureMapping_Structures_StructureId",
							  column: x => x.StructureId,
							  principalTable: "Structures",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Restrict);
				 });

			migrationBuilder.CreateIndex(
				 name: "IX_StructureMapping_PreviousStructureId",
				 table: "StructureMapping",
				 column: "PreviousStructureId");
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropTable(
				 name: "StructureMapping");

			migrationBuilder.AlterColumn<DateTime>(
				 name: "CreatedDate",
				 table: "ElectionParties",
				 type: "datetime2",
				 nullable: false,
				 defaultValue: new DateTime(2020, 9, 2, 19, 44, 2, 312, DateTimeKind.Local).AddTicks(9430),
				 oldClrType: typeof(DateTime),
				 oldDefaultValue: new DateTime(2020, 9, 3, 13, 50, 49, 800, DateTimeKind.Local).AddTicks(6005));

			migrationBuilder.AlterColumn<DateTime>(
				 name: "CreatedDate",
				 table: "ElectionCandidates",
				 type: "datetime2",
				 nullable: false,
				 defaultValue: new DateTime(2020, 9, 2, 19, 44, 2, 318, DateTimeKind.Local).AddTicks(2256),
				 oldClrType: typeof(DateTime),
				 oldDefaultValue: new DateTime(2020, 9, 3, 13, 50, 49, 806, DateTimeKind.Local).AddTicks(2681));
		}
	}
}
