﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using ElectionAppServer.Model.Structures;

namespace ElectionAppServer.Model;

public class Province : Structure
{
    public Province()
    {
        Divisions = new List<Division>();
    }

    [InverseProperty("Province")] public virtual List<Division> Divisions { get; set; }

    public virtual List<Region> Regions { get; set; }
    public virtual List<AdminDivision> AdminDivisions { get; set; }
}