using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using ElectionAppServer.Data;
using ElectionAppServer.DTO;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.JSInterop;
using Syncfusion.Blazor.Grids;
using Syncfusion.Blazor.Notifications;
using Syncfusion.Blazor.Popups;

namespace ElectionAppServer.Pages.AdminUnits;

public partial class UnionCouncils
{
    private SfDialog dlgForm;

    private bool isDlgVisible;

    private List<ElcStructureDTO> objList;

    //private string SaveButtonText = "Save";
    //private string FormTitle = "Create Union Councils";
    private ElcStructureDTO selectedObj = new();
    public string ToastContent = "Unable to delete Record, Related Record(s) Exist";

    private SfToast ToastObj;

    public List<GeneralItemDTO> BredCrum { get; set; }

    public List<GeneralItemDTO> casteList { get; set; }

    public int electionId { get; set; }

    public string ElectionTitle { get; set; }
    public SfGrid<ElcStructureDTO> Grid { get; set; }
    public bool isSaveEnable { get; set; } = true;

    public List<GeneralItemDTO> languagesList { get; set; }

    [Parameter] public int townId { get; set; }

    [Parameter] public int? ucId { get; set; }

    // AuthenticationState is available as a CascadingParameter
    [CascadingParameter] private Task<AuthenticationState> authenticationStateTask { get; set; }

    public void CustomizeCell(QueryCellInfoEventArgs<ElcStructureDTO> args)
    {
        if (args.Column.Field == "UrduTitle")
        {
            args.Cell.AddClass(new[] { "urdu-column" });
            args.Cell.AddStyle(new[] { "font-size:18px;line-height: 1.4;text-align: right" });
        }
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        //return base.OnAfterRenderAsync(firstRender);
        if (firstRender)
        {
            //await JSRuntime.InvokeVoidAsync("document.body.style.zoom = '90%'");
            var user = (await authenticationStateTask).User;
            if (user.Identity.IsAuthenticated)
            {
                //var dd  = await ProtectedSessionStore.GetAsync<ElectionStateDTO>("election");
                var dd = await localStorage.GetAsync<ElectionStateDTO>(await confreader.GetSettingValue("electionkey"));
                state.SetState(dd.Value);
            }
            else
            {
                try
                {
                    //await  ProtectedSessionStore.DeleteAsync("election");
                    await localStorage.DeleteAsync(await confreader.GetSettingValue("electionkey"));
                    state.SetState(new ElectionStateDTO
                        { ElectionId = 0, ElectionTitle = "", PhaseId = 0, PhaseTitle = "" });
                }
                catch (Exception)
                {
                    // ignored
                }

                NavigationManager.NavigateTo("");
            }

            StateHasChanged();
        }
    }

    protected override async Task OnInitializedAsync()
    {
        var user = (await authenticationStateTask).User;
        objList = await Service.GetList(StructureType.UnionCouncil, townId, state.state.PhaseId, ucId);
        BredCrum = await Service.GetBreadCrum(StructureType.UnionCouncil, townId);
        electionId = BredCrum[0].Id;
        languagesList = await Service.GetLanguages();
        casteList = await Service.GetCastesList();
        ElectionTitle = await Service.GetElectionTitle(electionId);
        ClearData();
    }

    protected override async Task OnParametersSetAsync()
    {
        objList = await Service.GetList(StructureType.UnionCouncil, townId, state.state.PhaseId, ucId);
        BredCrum = await Service.GetBreadCrum(StructureType.UnionCouncil, townId);
        electionId = BredCrum[0].Id;
        languagesList = await Service.GetLanguages();
        casteList = await Service.GetCastesList();
        ElectionTitle = await Service.GetElectionTitle(electionId);
        ClearData();
    }

    private void beforeOpen(BeforeOpenEventArgs args)
    {
        args.MaxHeight = null;
    }

    //private void OpenEditForm2(ElcStructureDTO st)
    //{
    //   selectedObj = new ElcStructureDTO
    //   {
    //      Id = st.Id,
    //      //CasteId = st.CasteId,
    //      Code = st.Code,
    //      EnglishTitle = st.EnglishTitle,
    //      FemaleVoters = st.FemaleVoters,
    //      TotalPollingStations = st.TotalPollingStations,
    //      ImportantAreas = st.ImportantAreas,
    //      ImportantPoliticalPersonalities = st.ImportantPoliticalPersonalities,
    //      //LanguageId = st.LanguageId,
    //      Castes = st.Castes,
    //      Languages = st.Languages,
    //      MaleVoters = st.MaleVoters,
    //      ParentId = townId,
    //      Problems = st.Problems,
    //      RuralAreaPer = st.RuralAreaPer,
    //      Trivia = st.Trivia,
    //      UrbanAreaPer = st.UrbanAreaPer,
    //      UrduTitle = st.UrduTitle,
    //      StructureType = StructureType.UnionCouncil,
    //      Population = st.Population,
    //      AverageHouseholdIncome = st.AverageHouseholdIncome,
    //      LiteracyRate = st.LiteracyRate,
    //      MajorityIncomeSource = st.MajorityIncomeSource,
    //      GeneralTrivia = st.GeneralTrivia,
    //      Area = st.Area
    //   };
    //   dlgForm.ShowAsync();
    //   SaveButtonText = "Update";
    //}
    private void ClearData()
    {
        selectedObj = new ElcStructureDTO
        {
            ParentId = townId, StructureType = StructureType.UnionCouncil, ElectionId = electionId, UrduTitle = "",
            EnglishTitle = "", Code = "", TotalWards = 0
        };
    }

    private async Task DeleteRecord(int Id)
    {
        var res = await JSRuntime.InvokeAsync<bool>("confirm", "Are you want to delete this record?");
        if (res == false)
            return;
        try
        {
            var msg2 = await Service.Delete(Id);
            objList = await Service.GetList(StructureType.UnionCouncil, townId, state.state.PhaseId, ucId);
            await Grid.Refresh();
            ClearData();
        }
        catch (Exception ex)
        {
            var mm = new ToastModel
            {
                Title = "Error", Content = ex.Message, CssClass = "e-toast-danger", ShowProgressBar = true,
                Timeout = 5000,
                ShowCloseButton = true
            };
            await ToastObj.ShowAsync(mm);
        }
    }

    private async Task OpenCreateForm()
    {
        ClearData();
        var code = await Service.GetNewUCCode(townId);
        selectedObj.Code = code;
        await dlgForm.ShowAsync();
        //SaveButtonText = "Create";
    }

    private async Task OpenEditForm(ElcStructureDTO st)
    {
        selectedObj = await Service.GetAdminUnitDetail(st.Id, townId, StructureType.UnionCouncil, state.state.PhaseId);
        await dlgForm.ShowAsync();
        //SaveButtonText = "Update";
    }

    private async Task SaveData()
    {
        var user = (await authenticationStateTask).User;
        try
        {
            await Service.Save(selectedObj, user.Identity.Name, state.state.PhaseId);
            objList = await Service.GetList(StructureType.UnionCouncil, townId, state.state.PhaseId, ucId);
            await Grid.Refresh();
            ClearData();
            await dlgForm.HideAsync();
        }
        catch (Exception ex)
        {
            var mm = new ToastModel
            {
                Title = "Error", Content = ex.Message, CssClass = "e-toast-danger", ShowProgressBar = true,
                Timeout = 5000,
                ShowCloseButton = true
            };
            await ToastObj.ShowAsync(mm);
        }
    }

    private async Task TranslateToUrdu()
    {
        try
        {
            if (string.IsNullOrEmpty(selectedObj.UrduTitle))
                selectedObj.UrduTitle = await Translation.TranslateText(selectedObj.EnglishTitle);
        }
        catch (Exception)
        {
            // ignored
        }
        //if (string.IsNullOrEmpty(selectedObj.UrduTitle))
    }
}