﻿@page "/form15"
@attribute [Authorize]

@inject NavigationManager NavigationManager
@inject UserManager<ApplicationUser> _UserManager
@inject RoleManager<IdentityRole> _RoleManager
@inject AuthenticationStateProvider AuthenticationStateProvider
@inherits OwningComponentBase<ElectionResultsDataService>


<PageCaption Title="Form 15 Status"></PageCaption>
<section style="padding: 15px 1rem; margin-top: -35px; font-size:12px">
    <table class="table table-sm">
        <thead class="thead-dark">
        <tr>
            @*<th>Assembly</th>*@
            <th>Constituency</th>
            <th>Polling Station</th>
            <th>Winner</th>
            <th>Winner Party</th>
            <th>Winner Votes</th>
            <th>Runner Up</th>
            <th>Runner Party</th>
            <th>Runner Votes</th>
            @*<th>PS %</th>
				<th>TPS</th>
				<th>RPS</th>*@
            <th>Status</th>
            <th>Updated Time</th>
            <th>&nbsp;</th>
        </tr>
        </thead>
        <tbody>
        @foreach (var r in results)
        {
            <tr class="@(r.Status == "Unpublished" ? "newresult" : "pubresult")">
                @*<td>@r.Assembly</td>*@
                <td>
                    <span style="cursor:pointer;color:blue" @onclick="@(() => DisplayDetail(r.PollingStationId, r.AssemblyId, r.ConstituencyId, r.SeatTypeId))">@r.Constituency</span>
                </td>
                <td>@r.PollingStation</td>
                <td>@r.WinnerName</td>
                <td>@r.WinnerParty</td>
                <td>@r.WinnerVotes</td>
                <td>@r.RunnerUpName</td>
                <td>@r.RunnerUpParty</td>
                <td>@r.RunnerUpVotes</td>
                @*<td>
						@if (r.PercentageCompletedPollingStations < 100)
						{
							<span>@Math.Round(r.PercentageCompletedPollingStations, 2)</span><br />
							<img src="/images/tp.png" alt="Partial" style="height: 18px" />
						}
						else
						{
							<img src="/images/tc.gif" alt="Completed" style="height: 22px" />
						}
					</td>
					<td>@r.TotalPollingStations</td>
					<td>@r.ResultOfPollingStations</td>*@
                <td>
                    @if (r.Status == "Unpublished")
                    {
                        <img src="/images/new.gif" alt="new"/>
                    }
                </td>
                <td>@r.LastUpdatedOn.ToString("d MMM, yyyy h:mm:ss")</td>
                <td>

                    <AuthorizeView Roles="Producer">
                        <SfButton IsPrimary="false" CssClass="e-primary" OnClick="@(() => MarkPublish(r))">Publish</SfButton>
                    </AuthorizeView>
                </td>
            </tr>
        }
        </tbody>
    </table>
</section>