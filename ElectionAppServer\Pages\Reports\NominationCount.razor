﻿@page "/reports/nominationcount"
@using ElectionAppServer.DTO.rpt
@using FilterType = Syncfusion.Blazor.DropDowns.FilterType
@attribute [Authorize(Roles = "Report User")]
@inject ReportService service

<style>
    body {
        user-select: none;
    }
</style>

<h3>Nomination Count</h3>
<div class="row">
    <div class="col-md">
        <SfDropDownList DataSource="@ElectionsList"
                        TValue="int?"
                        TItem="GeneralItemDTO"
                        @bind-Value="SelectedPhaseId"
                        AllowFiltering="true" FilterType="FilterType.Contains"
                        FloatLabelType="FloatLabelType.Always"
                        Placeholder="Election">
            <DropDownListFieldSettings Value="Id" Text="EnglishTitle"></DropDownListFieldSettings>
            <DropDownListEvents TItem="GeneralItemDTO" TValue="int?" ValueChange="OnElectionSelection"></DropDownListEvents>
        </SfDropDownList>
    </div>
</div>
<div class="row">
    <div class="col-md">
        <SfGrid DataSource="ConstituenciesList" AllowFiltering="true" AllowSorting="true" Height="calc(100vh - 220px)">
            <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Excel"></GridFilterSettings>
            <GridColumns>
                <GridColumn AutoFit="true" HeaderText="Assembly" Field="@nameof(RptNominationCount.Assembly)"></GridColumn>
                <GridColumn AutoFit="true" HeaderText="Code" Field="@nameof(RptNominationCount.Code)"></GridColumn>
                <GridColumn AutoFit="true" HeaderText="Constituency" Field="@nameof(RptNominationCount.Constituency)"></GridColumn>
                <GridColumn AutoFit="true" HeaderText="Nomination Counts" Field="@nameof(RptNominationCount.NominationCount)"></GridColumn>
            </GridColumns>
        </SfGrid>
    </div>
</div>

@code {

    private List<GeneralItemDTO> ElectionsList = new();
    private int? SelectedPhaseId;
    private List<RptNominationCount> ConstituenciesList = new();

    protected override async Task OnInitializedAsync()
    {
        ElectionsList = await service.GetAllElections();
    }

    private async Task OnElectionSelection(ChangeEventArgs<int?, GeneralItemDTO> o)
    {
        if (o != null && o.ItemData != null)
        {
            ConstituenciesList = await service.GetConstituenciesList(o.ItemData.Id);
        }
        else
        {
            ConstituenciesList = new List<RptNominationCount>();
        }
    }

}