using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ElectionAppServer.Data;
using ElectionAppServer.DTO;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.SignalR.Client;
using Syncfusion.Blazor.Popups;

namespace ElectionAppServer.Pages.Live;

public partial class Ticker
{
    private HubConnection hubConnection;
    private List<ResultDTO> results = new();

    [CascadingParameter] private Task<AuthenticationState> authenticationStateTask { get; set; }

    private LiveResultDetailDTO detail { get; set; }
    private bool isDlgVisible { get; set; }
    private List<ResultDTO> tickers { get; set; }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            var user = (await authenticationStateTask).User;
            if (user.Identity.IsAuthenticated)
            {
                var dd = await localStorage.GetAsync<ElectionStateDTO>(await confreader.GetSettingValue("electionkey"));
                state.SetState(dd.Value);
                var info = await _UserManager.GetUserAsync(user);
                //try
                //{
                results = await Service.GetLiveResults(state.state.PhaseId);

                //}
                //catch (Exception ex)
                //{
                //}

                await Task.CompletedTask;
                StateHasChanged();
            }
            else
            {
                try
                {
                    await localStorage.DeleteAsync(await confreader.GetSettingValue("electionkey"));
                    state.SetState(new ElectionStateDTO
                        { ElectionId = 0, ElectionTitle = "", PhaseId = 0, PhaseTitle = "" });
                    StateHasChanged();
                }
                catch (Exception)
                {
                    // ignored
                }
            }
        }
    }

    protected override async Task OnInitializedAsync()
    {
        isDlgVisible = false;
        detail = new LiveResultDetailDTO { Nominations = new List<LiveResultCandidateDetailDTO>() };
        hubConnection = new HubConnectionBuilder().WithUrl(NavigationManager.ToAbsoluteUri("/resultshub")).Build();
        hubConnection.On<ResultDTO>("ReceiveResult", result =>
        {
            //var ok = result;
            //results.Add(result);
            var isExist = (
                from r in results
                where r.ElectionId == result.ElectionId && r.AssemblyId == result.AssemblyId &&
                      r.ConstituencyId == result.ConstituencyId && r.SeatTypeId == result.SeatTypeId
                select r).FirstOrDefault();
            //var op = Service.GetLiveResultDetail(result.ElectionId, result.AssemblyId, result.PhaseId, result.ConstituencyId, result.SeatTypeId).Result;
            if (isExist != null)
                //result.Ticker = op.Ticker;
                isExist.Ticker = result.Ticker;
            else
                //if (result.PhaseId == state.state.PhaseId)
                //	results.Add(result);
                //result = new ResultDTO { AssemblyId = result.AssemblyId, SeatTypeId = result.SeatTypeId, ConstituencyId = result.ConstituencyId, ElectionId = result.ElectionId, PhaseId = result.PhaseId, LastUpdatedOn = result.LastUpdatedOn, Ticker = op.Ticker };
                results.Add(result);

            results = (
                from r in results
                //where r.ConstituencyId==154
                orderby r.Status, r.LastUpdatedOn descending
                select r).ToList();
            StateHasChanged();
        });
        hubConnection.On<ResultDTO>("ReceiveNotification", result =>
        {
            var isExist = (
                from r in results
                where r.ElectionId == result.ElectionId && r.AssemblyId == result.AssemblyId &&
                      r.ConstituencyId == result.ConstituencyId && r.SeatTypeId == result.SeatTypeId
                select r).FirstOrDefault();
            results = (
                from r in results
                //where r.ConstituencyId==154
                orderby r.OnAir descending, r.LastUpdatedOn descending
                select r).ToList();
            StateHasChanged();
        });
        await hubConnection.StartAsync();
        try
        {
            //results = await Service.GetLiveResults(state.state.PhaseId);
            //results = (
            //	 from kk in results
            //		 //where kk.ConstituencyId == 154
            //	 select kk).ToList();
            StateHasChanged();
        }
        catch (Exception)
        {
            // ignored
        }

        await Task.CompletedTask;
    }

    private void beforeOpen(BeforeOpenEventArgs args)
    {
        args.MaxHeight = null;
    }

    //private async Task DisplayDetail(int assemblyId, int constituencyId, int seatTypeId)
    //{
    //	//detail = await Service.GetLiveResultDetail(state.state.ElectionId, assemblyId, state.state.PhaseId, constituencyId, seatTypeId);
    //}

    private async Task MarkPublish(int electionId, int phaseId, int assemblyId, int constituencyId, int seatTypeId)
    {
        var user = (await authenticationStateTask).User;
        var msg = await Service.MarkPublish(electionId, phaseId, assemblyId, constituencyId, seatTypeId,
            user.Identity.Name);
        if (msg == "OK")
        {
            var res = (
                from r in results
                where r.ConstituencyId == constituencyId && r.ElectionId == electionId && r.AssemblyId == assemblyId &&
                      r.PhaseId == phaseId
                select r).FirstOrDefault();
            //res.Status = "Yes";
            res.OnAir = true;
            res.Status = "Yes";
            results = (
                from aa in results
                //where aa.ConstituencyId==154
                orderby aa.Status, aa.LastUpdatedOn descending
                select aa).ToList();
            var allowToPush = await confreader.GetSettingValue("AllowToPush");
            if (allowToPush == "Yes")
            {
                var immInfo = await Service.GetImmResult(electionId, phaseId, assemblyId, constituencyId, seatTypeId);
                await Translation.PostData(immInfo);
            }
        }

        StateHasChanged();
    }
}