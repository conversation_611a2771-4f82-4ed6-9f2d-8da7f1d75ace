﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ElectionAppServer.DTO;
using ElectionAppServer.Model;
using Microsoft.EntityFrameworkCore;

namespace ElectionAppServer.Data;

public class DivisionService
{
    private readonly IDbContextFactory<ApplicationDbContext>
        _contextFactory; // private  readonly  ApplicationDbContext dc;

    public DivisionService(IDbContextFactory<ApplicationDbContext> contextFactory) // (ApplicationDbContext  context)
    {
        _contextFactory = contextFactory; // dc  =  context;
    }

    public Task<Division> CreateDivision(Division obj)
    {
        using var dc = _contextFactory.CreateDbContext();
        dc.Structures.Add(obj);
        dc.SaveChanges();
        return Task.FromResult(obj);
    }

    public async Task<Division> DeleteDivision(int Id)
    {
        using var dc = _contextFactory.CreateDbContext();
        var st = await (from aa in dc.Structures.OfType<Division>() where aa.Id == Id select aa).FirstOrDefaultAsync();
        dc.Structures.Remove(st);
        await dc.SaveChangesAsync();
        return await Task.FromResult(st);
    }

    public Task<List<GeneralItemDTO>> GetList(int provinceId)
    {
        using var dc = _contextFactory.CreateDbContext();
        //DevisionResDTO op = new DevisionResDTO();
        //op.Province = dc.Provinces.FirstOrDefault(c=>c.Id==provinceId).EnglishTitle;
        //op.Divisions = dc.Divisions.OrderBy(d => d.EnglishTitle).Where(c=>c.ProvinceId==provinceId).ToList();

        //return Task.FromResult(op);

        //var rs = dc.Divisions.OrderBy(d => d.EnglishTitle).Where(c => c.ProvinceId == provinceId).ToList();
        var q = (from aa in dc.Structures.OfType<Division>()
            orderby aa.EnglishTitle
            where aa.ProvinceId == provinceId
            select new GeneralItemDTO
                { Id = aa.Id, EnglishTitle = aa.EnglishTitle, UrduTitle = aa.UrduTitle }).ToList();
        return Task.FromResult(q);
    }

    /// <summary>
    ///     Get Province Name by Province Id
    /// </summary>
    /// <param name="id">Province Id</param>
    /// <returns>Province</returns>
    public Task<string> GetProvinceName(int id)
    {
        using var dc = _contextFactory.CreateDbContext();
        try
        {
            var res = dc.Structures.OfType<Province>().FirstOrDefault(c => c.Id == id);
            return Task.FromResult(res.EnglishTitle);
        }
        catch (Exception)
        {
            return Task.FromResult(string.Empty);
        }
    }

    public Task<Division> UpdateDivision(Division obj)
    {
        using var dc = _contextFactory.CreateDbContext();
        var st = (from aa in dc.Structures.OfType<Division>() where aa.Id == obj.Id select aa).FirstOrDefault();
        st.UrduTitle = obj.UrduTitle.Trim();
        st.EnglishTitle = obj.EnglishTitle.Trim();
        st.ModifiedBy = obj.ModifiedBy;
        st.ModifiedDate = DateTime.Now;
        st.ProvinceId = obj.ProvinceId;
        dc.SaveChanges();
        return Task.FromResult(obj);
    }
}