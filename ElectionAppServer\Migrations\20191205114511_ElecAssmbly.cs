﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace ElectionAppServer.Migrations
{
	public partial class ElecAssmbly : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropForeignKey(
				 name: "FK_ElectionAssemblyConstituencies_Structures_StructureId",
				 table: "ElectionAssemblyConstituencies");

			migrationBuilder.AlterColumn<float>(
				 name: "UrbanAreaPer",
				 table: "ElectionAssemblyConstituencies",
				 nullable: true,
				 oldClrType: typeof(string),
				 oldType: "nvarchar(max)",
				 oldNullable: true);

			migrationBuilder.AlterColumn<string>(
				 name: "Trivia",
				 table: "ElectionAssemblyConstituencies",
				 maxLength: 2000,
				 nullable: true,
				 oldClrType: typeof(string),
				 oldType: "nvarchar(max)",
				 oldNullable: true);

			migrationBuilder.AlterColumn<float>(
				 name: "RuralAreaPer",
				 table: "ElectionAssemblyConstituencies",
				 nullable: true,
				 oldClrType: typeof(string),
				 oldType: "nvarchar(max)",
				 oldNullable: true);

			migrationBuilder.AlterColumn<string>(
				 name: "Problems",
				 table: "ElectionAssemblyConstituencies",
				 maxLength: 500,
				 nullable: true,
				 oldClrType: typeof(string),
				 oldType: "nvarchar(max)",
				 oldNullable: true);

			migrationBuilder.AlterColumn<string>(
				 name: "ImportantPoliticalPersonalities",
				 table: "ElectionAssemblyConstituencies",
				 maxLength: 500,
				 nullable: true,
				 oldClrType: typeof(string),
				 oldType: "nvarchar(max)",
				 oldNullable: true);

			migrationBuilder.AlterColumn<string>(
				 name: "ImportantAreas",
				 table: "ElectionAssemblyConstituencies",
				 maxLength: 500,
				 nullable: true,
				 oldClrType: typeof(string),
				 oldType: "nvarchar(max)",
				 oldNullable: true);

			migrationBuilder.AddForeignKey(
				 name: "FK_ElectionAssemblyConstituencies_Structures_StructureId",
				 table: "ElectionAssemblyConstituencies",
				 column: "StructureId",
				 principalTable: "Structures",
				 principalColumn: "Id",
				 onDelete: ReferentialAction.Restrict);
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropForeignKey(
				 name: "FK_ElectionAssemblyConstituencies_Structures_StructureId",
				 table: "ElectionAssemblyConstituencies");

			migrationBuilder.AlterColumn<string>(
				 name: "UrbanAreaPer",
				 table: "ElectionAssemblyConstituencies",
				 type: "nvarchar(max)",
				 nullable: true,
				 oldClrType: typeof(float),
				 oldNullable: true);

			migrationBuilder.AlterColumn<string>(
				 name: "Trivia",
				 table: "ElectionAssemblyConstituencies",
				 type: "nvarchar(max)",
				 nullable: true,
				 oldClrType: typeof(string),
				 oldMaxLength: 2000,
				 oldNullable: true);

			migrationBuilder.AlterColumn<string>(
				 name: "RuralAreaPer",
				 table: "ElectionAssemblyConstituencies",
				 type: "nvarchar(max)",
				 nullable: true,
				 oldClrType: typeof(float),
				 oldNullable: true);

			migrationBuilder.AlterColumn<string>(
				 name: "Problems",
				 table: "ElectionAssemblyConstituencies",
				 type: "nvarchar(max)",
				 nullable: true,
				 oldClrType: typeof(string),
				 oldMaxLength: 500,
				 oldNullable: true);

			migrationBuilder.AlterColumn<string>(
				 name: "ImportantPoliticalPersonalities",
				 table: "ElectionAssemblyConstituencies",
				 type: "nvarchar(max)",
				 nullable: true,
				 oldClrType: typeof(string),
				 oldMaxLength: 500,
				 oldNullable: true);

			migrationBuilder.AlterColumn<string>(
				 name: "ImportantAreas",
				 table: "ElectionAssemblyConstituencies",
				 type: "nvarchar(max)",
				 nullable: true,
				 oldClrType: typeof(string),
				 oldMaxLength: 500,
				 oldNullable: true);

			migrationBuilder.AddForeignKey(
				 name: "FK_ElectionAssemblyConstituencies_Structures_StructureId",
				 table: "ElectionAssemblyConstituencies",
				 column: "StructureId",
				 principalTable: "Structures",
				 principalColumn: "Id",
				 onDelete: ReferentialAction.Cascade);
		}
	}
}
