﻿using System;
using System.Linq;
using System.Threading.Tasks;
using ElectionAppServer.DTO.Dashboard;
using Microsoft.EntityFrameworkCore;

namespace ElectionAppServer.Data;

public class PartyPositionDataService
{
    private readonly IDbContextFactory<ApplicationDbContext>
        _contextFactory; // private  readonly  ApplicationDbContext dc;

    public PartyPositionDataService(
        IDbContextFactory<ApplicationDbContext> contextFactory) // (ApplicationDbContext  context)
    {
        _contextFactory = contextFactory; // dc  =  context;
    }

    public Task<AssemblyPositionDTO> GetAssemblyPartyPostionDetail(int assemblyId, int electionId, int phaseId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.ElectionAssemblies
            where aa.Id == assemblyId
            select new AssemblyPositionDTO
            {
                AssemblyId = assemblyId,
                Assembly = aa.EnglishTitle,
                Election = aa.Election.EnglishTitle,
                ElectionId = aa.ElectionId
                //RegisterVoters = (from bb in dc.Structures
                //						where bb.AssemblyId == assemblyId &&
                //								bb.ElectionId == electionId
                //						select (int)bb.TotalVoters).Sum(),
                //MaleVoters = (from bb in dc.Structures
                //				  where bb.AssemblyId == assemblyId &&
                //						  bb.ElectionId == electionId &&
                //						  bb.MaleVoters != null
                //				  select (int)bb.MaleVoters).Sum(),
                //FemaleVoters = (from bb in dc.Structures
                //					 where bb.AssemblyId == assemblyId &&
                //							 bb.ElectionId == electionId &&
                //							 bb.FemaleVoters != null
                //					 select (int)bb.FemaleVoters).Sum(),
                //TotalSeats = (from cc in dc.Nominations
                //				  where cc.ElectionAssemblyId == assemblyId &&
                //				  cc.ElectionPhaseId == phaseId
                //				  select cc).Count(),
                //VotePolled = (from bb in dc.Nominations
                //				  where bb.ElectionAssemblyId == assemblyId &&
                //				  bb.ElectionPhaseId == phaseId &&
                //				  bb.Votes != null &&
                //				  bb.Votes > 0
                //				  select (int)bb.Votes).Sum(),
                //ResultAnnounced = (from bb in dc.Nominations
                //						 where bb.ElectionAssemblyId == assemblyId &&
                //						 bb.ElectionPhaseId == phaseId &&
                //						 bb.Votes != null &&
                //						 bb.Votes > 0
                //						 select bb.StructureId).Distinct().Count()
            }).FirstOrDefault();
        var cc = (from bb in dc.Structures
            where bb.AssemblyId == assemblyId &&
                  bb.ElectionId == electionId &&
                  bb.MaleVoters != null &&
                  bb.FemaleVoters != null
            select new { MV = (int)bb.MaleVoters, FV = (int)bb.FemaleVoters }).ToList();

        var totalSeats = (from bb in dc.Nominations
            where bb.ElectionAssemblyId == assemblyId &&
                  bb.ElectionPhaseId == phaseId
            select new { bb.SeatTypeId, bb.StructureId }).Distinct().Count();

        var resultAnnounced = (from bb in dc.Nominations
            where bb.ElectionAssemblyId == assemblyId &&
                  bb.ElectionPhaseId == phaseId &&
                  bb.Votes > 0
            select new { bb.SeatTypeId, bb.StructureId }).Distinct().Count();

        var votePolled = (from bb in dc.Nominations
            where bb.ElectionAssemblyId == assemblyId &&
                  bb.ElectionPhaseId == phaseId &&
                  bb.Votes > 0
            select (int)bb.Votes).Sum();

        if (cc.Any())
        {
            q.MaleVoters = cc.Sum(k => k.MV);
            q.FemaleVoters = cc.Sum(k => k.FV);
            q.RegisterVoters = q.MaleVoters + q.FemaleVoters;
            //q.ResultAnnounced = cc.Count();
            q.TotalSeats = totalSeats;
            q.ResultAnnounced = resultAnnounced;
            q.VotePolled = votePolled;
            try
            {
                q.TurnOutPer = (float)(q.VotePolled / (float)q.RegisterVoters * 100.0);
            }
            catch (Exception)
            {
                q.TurnOutPer = 0;
            }

            try
            {
                q.MaleVoterPer = (float)(q.MaleVoters / (float)q.RegisterVoters * 100.0);
            }
            catch (Exception)
            {
                q.MaleVoterPer = 0;
            }

            try
            {
                q.FemaleVoterPer = (float)(q.FemaleVoters / (float)q.RegisterVoters * 100.0);
            }
            catch (Exception)
            {
                q.FemaleVoterPer = 0;
            }
        }

        return Task.FromResult(q);
    }
}