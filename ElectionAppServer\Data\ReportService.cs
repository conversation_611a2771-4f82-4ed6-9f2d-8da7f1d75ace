﻿using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using Dapper;
using ElectionAppServer.DTO;
using ElectionAppServer.DTO.rpt;
using ElectionAppServer.Model;
using Microsoft.EntityFrameworkCore;

namespace ElectionAppServer.Data;

public class ReportService
{
    private readonly IDbContextFactory<ApplicationDbContext> _contextFactory;

    public ReportService(IDbContextFactory<ApplicationDbContext> contextFactory)
    {
        _contextFactory = contextFactory;
    }

    public Task<List<RptNominationCount>> GetNominationCount(int phaseId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var pram = new { PhaseId = phaseId };
        var res = dc.Database.GetDbConnection()
            .Query<RptNominationCount>("rpt.NominationCount", pram, commandType: CommandType.StoredProcedure).ToList();
        return Task.FromResult(res);
    }

    public Task<List<GeneralItemDTO>> GetAllElections()
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from ep in dc.ElectionPhases
            orderby ep.Election.YearFrom descending, ep.Title
            where ep.Election.CandidatePoll == CandidatePoll.GeneralElection
            select new GeneralItemDTO
            {
                Id = ep.Id,
                EnglishTitle = ep.Election.EnglishTitle + (ep.Title == "Phase 1" ? "" : " - " + ep.Title)
            }).ToList();
        return Task.FromResult(q);
    }

    public Task<List<RptNominationCount>> GetConstituenciesList(int phaseId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var pram = new { PhaseId = phaseId };
        var con = dc.Database.GetDbConnection();
        var res = con.Query<RptNominationCount>("rpt.NominationCount", pram, commandType: CommandType.StoredProcedure)
            .ToList();
        return Task.FromResult(res);
    }
}