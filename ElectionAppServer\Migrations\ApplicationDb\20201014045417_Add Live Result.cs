﻿using Microsoft.EntityFrameworkCore.Migrations;
using System;

namespace ElectionAppServer.Migrations.ApplicationDb
{
	public partial class AddLiveResult : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.CreateTable(
				 name: "LiveResults",
				 columns: table => new
				 {
					 ElectionId = table.Column<int>(type: "int", nullable: false),
					 PhaseId = table.Column<int>(type: "int", nullable: false),
					 AssemblyId = table.Column<int>(type: "int", nullable: false),
					 ConstituencyId = table.Column<int>(type: "int", nullable: false),
					 SeatTypeId = table.Column<int>(type: "int", nullable: false),
					 Election = table.Column<string>(type: "nvarchar(400)", maxLength: 400, nullable: false),
					 Phase = table.Column<string>(type: "nvarchar(400)", maxLength: 400, nullable: false),
					 Assembly = table.Column<string>(type: "nvarchar(400)", maxLength: 400, nullable: false),
					 Constituency = table.Column<string>(type: "nvarchar(400)", maxLength: 400, nullable: false),
					 SeatType = table.Column<string>(type: "nvarchar(400)", maxLength: 400, nullable: false),
					 TotalPollingStations = table.Column<int>(type: "int", nullable: false),
					 TotalVoters = table.Column<int>(type: "int", nullable: false),
					 PollingStationResult = table.Column<int>(type: "int", nullable: false),
					 Winner = table.Column<string>(type: "nvarchar(400)", maxLength: 400, nullable: false),
					 WinnerParty = table.Column<string>(type: "nvarchar(400)", maxLength: 400, nullable: false),
					 WinnerVotes = table.Column<int>(type: "int", nullable: false),
					 RunnerUp = table.Column<string>(type: "nvarchar(400)", maxLength: 400, nullable: false),
					 RunnerUpParty = table.Column<string>(type: "nvarchar(400)", maxLength: 400, nullable: false),
					 RunnerUpVotes = table.Column<int>(type: "int", nullable: true),
					 CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "getdate()"),
					 CreatedBy = table.Column<string>(type: "varchar(450)", unicode: false, maxLength: 450, nullable: false),
					 ModifiedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
					 ModifiedBy = table.Column<string>(type: "varchar(450)", unicode: false, maxLength: 450, nullable: true)
				 },
				 constraints: table =>
				 {
					 table.PrimaryKey("PK_LiveResults", x => new { x.ElectionId, x.PhaseId, x.AssemblyId, x.ConstituencyId, x.SeatTypeId });
				 });
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropTable(
				 name: "LiveResults");
		}
	}
}
