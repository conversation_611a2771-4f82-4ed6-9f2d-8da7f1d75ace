﻿@page "/setup/mergecandidate/{KeepCandidateId:int}"
@using ElectionAppServer.Pages.Reports
@using FilterType = Syncfusion.Blazor.DropDowns.FilterType
@using Size = MudBlazor.Size

@attribute [Authorize]
@inject CandidateService service

<MudText Typo="Typo.h5">Merge Candidates</MudText>

<div>

    <div class="row">
        <div class="col-md">
            <SfTextBox Placeholder="Keep Candidate" @bind-Value="KeepCandidateName"
                       Enabled="false" FloatLabelType="FloatLabelType.Always"/>
        </div>
        <div class="col-md">
            <SfDropDownList DataSource="RemoveCandidatesList" Placeholder="Target Candidate"
                            FloatLabelType="FloatLabelType.Always" AllowFiltering="true"
                            FilterType="FilterType.Contains"
                            @bind-Value="RemoveCandidateId"
                            TValue="int?" TItem="GeneralItemDTO">
                <DropDownListFieldSettings Value="Id" Text="EnglishTitle"></DropDownListFieldSettings>

            </SfDropDownList>
            <div style="display:flex;align-items:center; gap: 10px">

                <SfTextBox Placeholder="Need to Remove Candidadte" @bind-Value="TargetCandidate"
                           Enabled="true" FloatLabelType="FloatLabelType.Always"/>
                <MudButton Color="Color.Primary" Size="Size.Small" Variant="Variant.Filled"
                           StartIcon="@Icons.Material.Filled.Search"
                           OnClick="FindCandidate">
                    Search
                </MudButton>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-md-6">
            @if (KeepCandidateId != null && KeepCandidateId != 0)
            {
                <CandidateDetail CandidateId="@(KeepCandidateId ?? 0)"/>
            }
        </div>
        <div class="col-md-6">
            @if (RemoveCandidateId != null && RemoveCandidateId != 0)
            {
                <CandidateDetail CandidateId="@(RemoveCandidateId ?? 0)"/>

                <MudButton Color="Color.Primary" Size="Size.Small" Variant="Variant.Filled"
                           OnClick="DoMerge"
                           StartIcon="@Icons.Material.Filled.Merge">
                    Keep @KeepCandidateId and Delete @RemoveCandidateId
                </MudButton>
            }
        </div>
    </div>
</div>