﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using FluentValidation;

namespace ElectionAppServer.DTO;

public class ElcStructureDTO
{
    public int Id { get; set; }
    public string Code { get; set; }
    public int? RegionId { get; set; }
    public int? DivisionId { get; set; }
    public int? ProvinceId { get; set; }
    public string RegionEnglish { get; set; }
    public string DivisionEnglish { get; set; }
    public string RegionUrdu { get; set; }
    public string UrduTitle { get; set; }
    public string EnglishTitle { get; set; }
    public int? MaleVoters { get; set; }
    public int? FemaleVoters { get; set; }
    public int? YouthVoters { get; set; }
    public int? Population { get; set; }
    public string Profile { get; set; }
    public int TotalVoters => (MaleVoters ?? 0) + (FemaleVoters ?? 0);

    public int? TotalPollingStations { get; set; }

    //public int? CasteId { get; set; }
    //public int? LanguageId { get; set; }
    [StringLength(200)] public string Castes { get; set; }
    [StringLength(200)] public string Languages { get; set; }
    public string Trivia { get; set; }
    public string GeneralTrivia { get; set; }
    public string ImportantPoliticalPersonalities { get; set; }
    public string Problems { get; set; }
    public string ImportantAreas { get; set; }
    public float? RuralAreaPer { get; set; }
    public float? UrbanAreaPer { get; set; }
    public int? ParentId { get; set; }
    public StructureType StructureType { get; set; }
    public int ElectionId { get; set; }
    public int TotalWards { get; set; } = 0;
    [StringLength(200)] public string MajorityIncomeSource { get; set; }
    [StringLength(50)] public string LiteracyRate { get; set; }
    [StringLength(50)] public string AverageHouseholdIncome { get; set; }
    public string Area { get; set; }
    public int SubUnits { get; set; }
    public string CreateBy { get; set; }
    public string CreatedDate { get; set; }
    public string ModifiedBy { get; set; }
    public string ModifiedDate { get; set; }
    public string Assembly { get; internal set; }
    public int? AssemblyId { get; set; }
    public List<PrevConstituencyDTO> PrevConstituencies { get; set; }
    public List<int> ProblemsList { get; set; } = new();
    //public bool IsDisable { get { return Code.Trim() == "" || UrduTitle.Trim() == "" || EnglishTitle.Trim() == ""; } }
}

public class ElcStructureDTOValidator : AbstractValidator<ElcStructureDTO>
{
    public ElcStructureDTOValidator()
    {
        RuleFor(c => c.EnglishTitle).NotEmpty().WithMessage("Title (English) is required");
        RuleFor(c => c.UrduTitle).NotEmpty().WithMessage("Title (English) is required");
        RuleFor(c => c.Code).MaximumLength(10).WithMessage("Maximum 10 Characters are allow");
        RuleFor(c => c.UrduTitle).MaximumLength(200).WithMessage("Maximum 200 characters are allow");
        RuleFor(c => c.EnglishTitle).MaximumLength(200).WithMessage("Maximum 200 characters are allow");
        //RuleFor(c => c.Trivia).MaximumLength(4000).WithMessage("Maximum 4000 Characters are allowed");
        RuleFor(c => c.ImportantPoliticalPersonalities).MaximumLength(4000)
            .WithMessage("Maximum 4000 Characters are allowed");
        RuleFor(c => c.Problems).MaximumLength(4000).WithMessage("Maximum 4000 Characters are allowed");
        RuleFor(c => c.Languages).MaximumLength(200).WithMessage("Maximum 200 characters are allowed");
        RuleFor(c => c.ImportantAreas).MaximumLength(4000).WithMessage("Maximum 4000 characters are allowed");
        RuleFor(c => c.Castes).MaximumLength(4000).WithMessage("Maximum 4000 characters are allowed");
        RuleFor(c => c.MajorityIncomeSource).MaximumLength(200).WithMessage("Maximum 200 characters are allowed");
        RuleFor(c => c.LiteracyRate).MaximumLength(50).WithMessage("Maximum 50 characters are allowed");
        RuleFor(c => c.Area).MaximumLength(50).WithMessage("Maximum 50 characters are allowed");
        //RuleFor(c => c.GeneralTrivia).MaximumLength(4000).WithMessage("Maximum 4000 characters are allowed");
        RuleFor(c => c.AverageHouseholdIncome).MaximumLength(50).WithMessage("Maximum 50 characters are allowed");
        When(c => c.StructureType == StructureType.UnionCouncil ||
                  c.StructureType == StructureType.Ward ||
                  c.StructureType == StructureType.NA ||
                  c.StructureType == StructureType.PA,
            () => { RuleFor(c => c.Code).NotEmpty().WithMessage("Code is Required"); });

        When(c => c.StructureType == StructureType.District,
            () => { RuleFor(c => c.RegionId).NotEmpty().WithMessage("Region is required"); });
    }
}