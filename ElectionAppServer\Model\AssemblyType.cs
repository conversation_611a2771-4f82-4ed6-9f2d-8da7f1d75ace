﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace ElectionAppServer.Model;

public class AssemblyType
{
    public int Id { get; set; }
    public string UrduTitle { get; set; }
    public string EnglishTitle { get; set; }

    public virtual List<ElectionAssembly> ElectionAssemblies { get; set; }
    //public virtual List<Assembly> Assemblies { get; set; }

    #region Audit Log Fields

    public DateTime CreatedDate { get; set; } = DateTime.Now;
    public DateTime? ModifiedDate { get; set; }

    [StringLength(450)] public string CreatedBy { get; set; }

    [StringLength(450)] public string ModifiedBy { get; set; }

    #endregion Audit Log Fields
}