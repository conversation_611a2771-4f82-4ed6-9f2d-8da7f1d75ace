﻿using System;

namespace ElectionAppServer.Model;

public class CandidateDistrict
{
    public int ElectionId { get; set; }
    public int DistrictId { get; set; }
    public int CandidateId { get; set; }

    public virtual Election Election { get; set; }
    public virtual District District { get; set; }
    public virtual Candidate Candidate { get; set; }

    #region Audit Log

    public DateTime CreatedDate { get; set; }
    public string CreatedBy { get; set; }
    public DateTime? ModifiedDate { get; set; }
    public string ModifiedBy { get; set; }

    #endregion Audit Log
}