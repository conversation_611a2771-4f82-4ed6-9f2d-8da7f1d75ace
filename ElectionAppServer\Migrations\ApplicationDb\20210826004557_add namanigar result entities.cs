﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace ElectionAppServer.Migrations.ApplicationDb
{
    public partial class addnamanigarresultentities : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "IsActive",
                table: "ElectionPhases",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.CreateTable(
                name: "NamanigarResultLogs",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    NominationId = table.Column<int>(type: "int", nullable: false),
                    Votes = table.Column<int>(type: "int", nullable: false),
                    ChangeVotes = table.Column<int>(type: "int", nullable: false),
                    ResultPollingStations = table.Column<int>(type: "int", nullable: false),
                    Status = table.Column<int>(type: "int", nullable: false),
                    LastActionMode = table.Column<string>(type: "varchar(10)", unicode: false, maxLength: 10, nullable: false),
                    ActionDateTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    NamanigarUserId = table.Column<string>(type: "varchar(450)", unicode: false, maxLength: 450, nullable: false),
                    ApprovedBy = table.Column<string>(type: "varchar(450)", unicode: false, maxLength: 450, nullable: true),
                    ApprovedDate = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_NamanigarResultLogs", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "NamanigarResults",
                columns: table => new
                {
                    NominationId = table.Column<int>(type: "int", nullable: false),
                    Votes = table.Column<int>(type: "int", nullable: false),
                    ResultPollingStations = table.Column<int>(type: "int", nullable: false),
                    Status = table.Column<int>(type: "int", nullable: false),
                    LastActionMode = table.Column<string>(type: "varchar(10)", unicode: false, maxLength: 10, nullable: false),
                    CreatedBy = table.Column<string>(type: "varchar(450)", unicode: false, maxLength: 450, nullable: false),
                    ModifiedBy = table.Column<string>(type: "varchar(450)", unicode: false, maxLength: 450, nullable: true),
                    CreatedOn = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "getdate()"),
                    ModifiedOn = table.Column<DateTime>(type: "datetime2", nullable: true),
                    NamanigarId = table.Column<int>(type: "int", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_NamanigarResults", x => x.NominationId);
                    table.ForeignKey(
                        name: "FK_NamanigarResults_Namanigars_NamanigarId",
                        column: x => x.NamanigarId,
                        principalTable: "Namanigars",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_NamanigarResults_Nominations_NominationId",
                        column: x => x.NominationId,
                        principalTable: "Nominations",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_NamanigarResults_NamanigarId",
                table: "NamanigarResults",
                column: "NamanigarId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "NamanigarResultLogs");

            migrationBuilder.DropTable(
                name: "NamanigarResults");

            migrationBuilder.DropColumn(
                name: "IsActive",
                table: "ElectionPhases");
        }
    }
}
