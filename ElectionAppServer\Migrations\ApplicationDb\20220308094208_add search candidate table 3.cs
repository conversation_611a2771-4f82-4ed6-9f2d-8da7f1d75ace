﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace ElectionAppServer.Migrations.ApplicationDb
{
    public partial class addsearchcandidatetable3 : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "AuditInfo",
                table: "SearchCandidates");

            migrationBuilder.RenameColumn(
                name: "Party",
                table: "SearchCandidates",
                newName: "ModifiedBy");

            migrationBuilder.RenameColumn(
                name: "Nominations",
                table: "SearchCandidates",
                newName: "Constituencies");

            migrationBuilder.RenameColumn(
                name: "ElectionId",
                table: "SearchCandidates",
                newName: "NominationCount");

            migrationBuilder.AddColumn<string>(
                name: "CandidateType",
                table: "SearchCandidates",
                type: "varchar(20)",
                unicode: false,
                maxLength: 20,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "CreatedBy",
                table: "SearchCandidates",
                type: "varchar(200)",
                unicode: false,
                maxLength: 200,
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "CreatedOn",
                table: "SearchCandidates",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<string>(
                name: "CurrentParty",
                table: "SearchCandidates",
                type: "varchar(200)",
                unicode: false,
                maxLength: 200,
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "DateOfBirth",
                table: "SearchCandidates",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "DistrictId",
                table: "SearchCandidates",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "EnglishFatherName",
                table: "SearchCandidates",
                type: "varchar(200)",
                unicode: false,
                maxLength: 200,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "FullName",
                table: "SearchCandidates",
                type: "nvarchar(500)",
                maxLength: 500,
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "Gender",
                table: "SearchCandidates",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "ModifiedOn",
                table: "SearchCandidates",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<string>(
                name: "Status",
                table: "SearchCandidates",
                type: "nvarchar(20)",
                maxLength: 20,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "UrduFatherName",
                table: "SearchCandidates",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CandidateType",
                table: "SearchCandidates");

            migrationBuilder.DropColumn(
                name: "CreatedBy",
                table: "SearchCandidates");

            migrationBuilder.DropColumn(
                name: "CreatedOn",
                table: "SearchCandidates");

            migrationBuilder.DropColumn(
                name: "CurrentParty",
                table: "SearchCandidates");

            migrationBuilder.DropColumn(
                name: "DateOfBirth",
                table: "SearchCandidates");

            migrationBuilder.DropColumn(
                name: "DistrictId",
                table: "SearchCandidates");

            migrationBuilder.DropColumn(
                name: "EnglishFatherName",
                table: "SearchCandidates");

            migrationBuilder.DropColumn(
                name: "FullName",
                table: "SearchCandidates");

            migrationBuilder.DropColumn(
                name: "Gender",
                table: "SearchCandidates");

            migrationBuilder.DropColumn(
                name: "ModifiedOn",
                table: "SearchCandidates");

            migrationBuilder.DropColumn(
                name: "Status",
                table: "SearchCandidates");

            migrationBuilder.DropColumn(
                name: "UrduFatherName",
                table: "SearchCandidates");

            migrationBuilder.RenameColumn(
                name: "NominationCount",
                table: "SearchCandidates",
                newName: "ElectionId");

            migrationBuilder.RenameColumn(
                name: "ModifiedBy",
                table: "SearchCandidates",
                newName: "Party");

            migrationBuilder.RenameColumn(
                name: "Constituencies",
                table: "SearchCandidates",
                newName: "Nominations");

            migrationBuilder.AddColumn<string>(
                name: "AuditInfo",
                table: "SearchCandidates",
                type: "nvarchar(600)",
                maxLength: 600,
                nullable: true);
        }
    }
}
