﻿using System.Collections.Generic;
using System.Threading.Tasks;
using ElectionAppServer.DTO.Dashboard;
using Microsoft.EntityFrameworkCore;

namespace ElectionAppServer.Data;

public class DashboardDataService
{
    private readonly IDbContextFactory<ApplicationDbContext> _contextFactory; // private  ApplicationDbContext  dc;

    public DashboardDataService(IDbContextFactory<ApplicationDbContext> contextFactory) // (ApplicationDbContext  dc)
    {
        //this.dc = dc;
        _contextFactory = contextFactory;
    }

    public Task<List<PieChartSeriesDTO>> ProvinceWiseAssemblies(int electionId)
    {
        var obj = new List<PieChartSeriesDTO>();
        return Task.FromResult(obj);
    }

    //public Task<List<PieChartSeriesDTO>> ProvinceWiseAssemblies(int electionId)
    //{
    //	var qUC = (from aa in dc.ElectionAssemblyConstituencies
    //				  join bb in dc.Structures.OfType<UnionCouncil>() on aa.StructureId equals bb.Id
    //				  where aa.ElectionAssembly.ElectionId == electionId
    //				  select new ProvWiseAssmbDTO
    //				  {
    //					  AssemblyType = aa.ElectionAssembly.AssemblyType.EnglishTitle,
    //					  AssemblyId = aa.ElectionAssemblyId,
    //					  Province = bb.Town.District.Division.Province.EnglishTitle,
    //					  Count = 1
    //				  }).Distinct().ToList();

    //	var qWrd = (from aa in dc.ElectionAssemblyConstituencies
    //				  join bb in dc.Structures.OfType<Ward>() on aa.StructureId equals bb.Id
    //				  where aa.ElectionAssembly.ElectionId == electionId
    //				  select new ProvWiseAssmbDTO
    //				  {
    //					  AssemblyType = aa.ElectionAssembly.AssemblyType.EnglishTitle,
    //					  AssemblyId = aa.ElectionAssemblyId,
    //					  Province = bb.UnionCouncil.Town.District.Division.Province.EnglishTitle,
    //					  Count=1
    //				  }).Distinct().ToList();

    //	var qNa = (from aa in dc.ElectionAssemblyConstituencies
    //					join bb in dc.Structures.OfType<NationalAssemblyHalka>() on aa.StructureId equals bb.Id
    //					where aa.ElectionAssembly.ElectionId == electionId
    //					select new ProvWiseAssmbDTO
    //					{
    //						AssemblyType = aa.ElectionAssembly.AssemblyType.EnglishTitle,
    //						AssemblyId = aa.ElectionAssemblyId,
    //						Province = bb.District.Division.Province.EnglishTitle, Count=1
    //					}).Distinct().ToList();

    //	var qPA = (from aa in dc.ElectionAssemblyConstituencies
    //				  join bb in dc.Structures.OfType<ProvincialAssemblyHalka>() on aa.StructureId equals bb.Id
    //				  where aa.ElectionAssembly.ElectionId == electionId
    //				  select new ProvWiseAssmbDTO
    //				  {
    //					  AssemblyType = aa.ElectionAssembly.AssemblyType.EnglishTitle,
    //					  AssemblyId = aa.ElectionAssemblyId,
    //					  Province = bb.District.Division.Province.EnglishTitle, Count=1
    //				  }).Distinct().ToList();

    //	var m = qWrd.Union(qUC).Union(qNa).Union(qPA).Distinct().ToList();

    //	var pro = (from aa in m
    //				  orderby aa.Province
    //				  select aa.Province).Distinct();

    //	var pcsdList = new List<PieChartSeriesDTO>();

    //	foreach (var p in pro)
    //	{
    //		var pcsd = new PieChartSeriesDTO { Title = p, Data= (from aa in m
    //																			  where aa.Province == p
    //																			  orderby aa.AssemblyType
    //																			  select new PieChartDTO { Data = aa.Count, Label = aa.AssemblyType }).ToList()
    //		};

    //		pcsdList.Add(pcsd);

    //	}

    //	return Task.FromResult(pcsdList);

    //}
}