﻿using Microsoft.EntityFrameworkCore.Migrations;
using System;

namespace ElectionAppServer.Migrations.ApplicationDb
{
	public partial class AddNamanigar3 : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.CreateTable(
				 name: "Namanigars",
				 columns: table => new
				 {
					 Id = table.Column<int>(nullable: false)
							.Annotation("SqlServer:Identity", "1, 1"),
					 Code = table.Column<string>(unicode: false, maxLength: 20, nullable: false),
					 Name = table.Column<string>(unicode: false, maxLength: 200, nullable: false),
					 Phone = table.Column<string>(unicode: false, maxLength: 50, nullable: false),
					 Email = table.Column<string>(unicode: false, maxLength: 300, nullable: true),
					 CreatedDate = table.Column<DateTime>(nullable: false, defaultValueSql: "getdate()"),
					 CreatedBy = table.Column<string>(unicode: false, maxLength: 450, nullable: false),
					 ModifiedDate = table.Column<DateTime>(nullable: true),
					 ModifiedBy = table.Column<string>(unicode: false, maxLength: 450, nullable: true)
				 },
				 constraints: table =>
				 {
					 table.PrimaryKey("PK_Namanigars", x => x.Id);
				 });

			migrationBuilder.CreateTable(
				 name: "NamanigarConstituencies",
				 columns: table => new
				 {
					 NamanigarId = table.Column<int>(nullable: false),
					 ConstituencyId = table.Column<int>(nullable: false),
					 Id = table.Column<int>(nullable: false),
					 PSDetail = table.Column<string>(unicode: false, maxLength: 500, nullable: false),
					 CreatedDate = table.Column<DateTime>(nullable: false, defaultValueSql: "getdate()"),
					 CreatedBy = table.Column<string>(unicode: false, maxLength: 450, nullable: false),
					 ModifiedDate = table.Column<DateTime>(nullable: true),
					 ModifiedBy = table.Column<string>(unicode: false, maxLength: 450, nullable: true)
				 },
				 constraints: table =>
				 {
					 table.PrimaryKey("PK_NamanigarConstituencies", x => new { x.NamanigarId, x.ConstituencyId });
					 table.ForeignKey(
							  name: "FK_NamanigarConstituencies_Structures_ConstituencyId",
							  column: x => x.ConstituencyId,
							  principalTable: "Structures",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Restrict);
					 table.ForeignKey(
							  name: "FK_NamanigarConstituencies_Namanigars_NamanigarId",
							  column: x => x.NamanigarId,
							  principalTable: "Namanigars",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Restrict);
				 });

			migrationBuilder.CreateIndex(
				 name: "IX_NamanigarConstituencies_ConstituencyId",
				 table: "NamanigarConstituencies",
				 column: "ConstituencyId");
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropTable(
				 name: "NamanigarConstituencies");

			migrationBuilder.DropTable(
				 name: "Namanigars");
		}
	}
}
