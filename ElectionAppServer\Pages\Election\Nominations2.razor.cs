﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using BlazorInputFile;
using ElectionAppServer.Data;
using ElectionAppServer.DTO;
using ElectionAppServer.Model;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Syncfusion.Blazor.Data;
using Syncfusion.Blazor.DropDowns;
using Syncfusion.Blazor.Notifications;
using Syncfusion.Blazor.Popups;
using BeforeOpenEventArgs = Syncfusion.Blazor.Popups.BeforeOpenEventArgs;

namespace ElectionAppServer.Pages.Election;

public partial class Nominations2
{
    private List<GeneralItemDTO> assembliesList = new();

    //private List<SearchCandidateDTO> candListAll = new();
    private List<SearchCandidateDTO> candListFilter = new();

    private ConstDetailDTO constDetail;

    private List<GeneralItemDTO> constituenciesList =
        new() { new GeneralItemDTO { Id = 0, EnglishTitle = "Select Constituency" } };

    private SfDialog dlgForm;
    private SfDialog dlgSeachCand;

    //private HubConnection hubConnection;
    private bool isDlgSearchCandVisible;

    private bool isDlgVisible;
    public Query LocalDataQuery = new Query().Take(10);
    private List<NominationDTO> nominations = new();

    //private List<GeneralItemDTO> candidatesList = new List<GeneralItemDTO>();
    private List<GeneralItemDTO> partiesList = new();

    private string searchText = string.Empty;

    private List<GeneralItemDTO> seatTypesList = new()
        { new GeneralItemDTO { Id = 0, EnglishTitle = "Select Seat Type" } };

    private GeneralItemDTO selectedAssembly;
    private GeneralItemDTO selectedConstituency;
    private NominationDTO selectedObj = new();
    private GeneralItemDTO selectedSeatType;
    private List<GeneralItemDTO> symbolsList = new();
    private SfToast ToastObj;

    [CascadingParameter] private Task<AuthenticationState> authenticationStateTask { get; set; }
    private BasicConstituencyDTO BasicConstInfo { get; set; }
    private string CandidateName { get; set; }
    private bool EditConstituencyMode { get; set; }
    private NominationType ElectionBasedOn { get; set; }

    //public bool IsConnected => hubConnection.State == HubConnectionState.Connected;

    public int? SelectedAssemblyId { get; set; }
    public int? SelectedConstituencyId { get; set; }
    public int? SelectedSeatTypeId { get; set; }

    private void beforeOpen(BeforeOpenEventArgs args)
    {
        args.MaxHeight = null;
    }

    private void CloseEditForm()
    {
        constDetail = Service.GetConstInfo(selectedConstituency.Id, state.state.PhaseId, SelectedSeatTypeId.Value)
            .Result;

        EditConstituencyMode = false;
        StateHasChanged();
    }

    private async Task DeleteRecord(int Id)
    {
        var para = new[] { "Are you sure want to delete this nomination?" };
        var cid = nominations.Find(k => k.Id == Id).CandidateId;
        var res = await JSRuntime.InvokeAsync<bool>("confirm", para);
        if (res == false)
            return;
        try
        {
            await Service.DeleteRecord(Id, state.state.ElectionId);
            await FillNominations();
        }
        catch (Exception ex)
        {
            var mm = new ToastModel
            {
                Title = "Error", Content = ex.Message, CssClass = "e-toast-danger", ShowProgressBar = true,
                Timeout = 5000, ShowCloseButton = true
            };
            await ToastObj.ShowAsync(mm);
        }
    }

    private async Task FillCandidateParyAndSymbol()
    {
        if (selectedObj.CandidateId != null)
        {
            selectedObj.PartyId = await Service.GetCandidateCurrentParty((int)selectedObj.CandidateId);
            selectedObj.SymbolId = selectedObj.PartyId != null
                ? await Service.GetPartySymbolId((int)selectedObj.PartyId)
                : null;
        }
        else
        {
            selectedObj.PartyId = null;
            selectedObj.SymbolId = null;
        }

        selectedObj.PartyFlagURL = await Service.GetFlagUrlById(selectedObj.PartyId);
        selectedObj.SymbolURL = await Service.GetSymbolUrlById(selectedObj.SymbolId);
        await Task.CompletedTask;
    }

    private async Task FilterByAssembly(ChangeEventArgs<int?, GeneralItemDTO> arg)
    {
        if (arg != null && arg.ItemData.Id > 0)
        {
            selectedAssembly = new GeneralItemDTO { Id = arg.ItemData.Id, EnglishTitle = arg.ItemData.EnglishTitle };
            constituenciesList = await Service.GetConstituencies(selectedAssembly.Id);
            selectedConstituency = null;
            seatTypesList = new List<GeneralItemDTO>();
            selectedSeatType = null;
            ElectionBasedOn = await Service.GetElectionBasedOn(selectedAssembly.Id);
            //candListAll = new List<SearchCandidateDTO>();
            ElectionBasedOn = Service.GetElectionBasedOn(selectedAssembly.Id).Result;
            //candListAll = await Service.SearchCandidate(state.state.ElectionId, ElectionBasedOn);
        }
        else
        {
            selectedAssembly = null;
            constituenciesList = new List<GeneralItemDTO>();
            seatTypesList = new List<GeneralItemDTO>();
            selectedConstituency = null;
        }

        //CurrentCountry = country;
        //CurrentCountryCities = Cities.FindAll(city => city.CountryId == CurrentCountry.Id);
        //CurrentCity = CurrentCountryCities[0];
        //InvokeAsync(StateHasChanged);
        StateHasChanged();
    }

    //FilterByConstituency
    private async Task FilterByConstituency(ChangeEventArgs<int?, GeneralItemDTO> arg)
    {
        if (arg.Value != null)
        {
            selectedConstituency = new GeneralItemDTO
                { Id = arg.ItemData.Id, EnglishTitle = arg.ItemData.EnglishTitle };
            selectedSeatType = null;
            seatTypesList = await Service.GetSeatType(selectedAssembly.Id, selectedConstituency.Id);
            if (seatTypesList.Count >= 1)
            {
                selectedSeatType = new GeneralItemDTO
                {
                    Id = seatTypesList[0].Id, EnglishTitle = seatTypesList[0].EnglishTitle,
                    UrduTitle = seatTypesList[0].UrduTitle
                };
                nominations = await Service.GetNominations(selectedAssembly.Id, selectedConstituency.Id,
                    selectedSeatType.Id, state.state.PhaseId);
                SelectedSeatTypeId = seatTypesList[0].Id;
            }
            else
            {
                nominations = new List<NominationDTO>();
                seatTypesList = null;
            }

            constDetail = Service.GetConstInfo(selectedConstituency.Id, state.state.PhaseId, SelectedSeatTypeId ?? 0)
                .Result;
            StateHasChanged();
        }
        else
        {
            selectedConstituency = null;
            selectedSeatType = null;
            seatTypesList = new List<GeneralItemDTO>();
            constDetail = null;
            StateHasChanged();
        }
    }

    //FilterBySeatType
    private async Task FilterBySeatType(ChangeEventArgs<int?, GeneralItemDTO> arg)
    {
        if (arg.Value != null)
        {
            selectedSeatType = new GeneralItemDTO
                { Id = arg.ItemData.Id, EnglishTitle = arg.ItemData.EnglishTitle, UrduTitle = arg.ItemData.UrduTitle };
            nominations = await Service.GetNominations(selectedAssembly.Id, selectedConstituency.Id,
                selectedSeatType.Id, state.state.PhaseId);
            constDetail = Service.GetConstInfo(selectedConstituency.Id, state.state.PhaseId, SelectedSeatTypeId ?? 0)
                .Result;
        }
        else
        {
            selectedSeatType = null;
            nominations = new List<NominationDTO>();
        }

        StateHasChanged();
    }

    private async Task MarkWinner()
    {
        var user = (await authenticationStateTask).User;

        if (nominations != null && nominations.Count == 1)
        {
            var op = await Service.MarkBilaMuqabala(nominations[0].Id, user.Identity.Name);
            if (op != "OK")
            {
                var mm = new ToastModel
                {
                    Title = "Error", Content = op, CssClass = "e-toast-danger", ShowProgressBar = true, Timeout = 5000,
                    ShowCloseButton = true
                };
                await ToastObj.ShowAsync(mm);
            }
            else
            {
                nominations = await Service.GetNominations(selectedAssembly.Id, selectedConstituency.Id,
                    selectedSeatType.Id, state.state.PhaseId);
                StateHasChanged();
            }
        }
    }

    private void OnChangeAssembly(GeneralItemDTO assembly)
    {
        if (assembly != null && assembly.Id > 0)
        {
            selectedAssembly = new GeneralItemDTO { Id = assembly.Id, EnglishTitle = assembly.EnglishTitle };
            constituenciesList = Service.GetConstituencies(selectedAssembly.Id).Result;
            selectedConstituency = null;
            seatTypesList = new List<GeneralItemDTO>();
            selectedSeatType = null;
            ElectionBasedOn = Service.GetElectionBasedOn(selectedAssembly.Id).Result;
            //candListAll = Service.SearchCandidate(state.state.ElectionId, ElectionBasedOn).Result;
            //candListAll = new List<SearchCandidateDTO>();
            InvokeAsync(StateHasChanged);
        }
        else
        {
            selectedAssembly = null;
            constituenciesList = new List<GeneralItemDTO>();
            seatTypesList = new List<GeneralItemDTO>();
            selectedConstituency = null;
        }

        //CurrentCountry = country;
        //CurrentCountryCities = Cities.FindAll(city => city.CountryId == CurrentCountry.Id);
        //CurrentCity = CurrentCountryCities[0];
        //InvokeAsync(StateHasChanged);
        StateHasChanged();
    }

    private void OnChangeConstituency(GeneralItemDTO cc)
    {
        if (cc != null)
        {
            selectedConstituency = new GeneralItemDTO { Id = cc.Id, EnglishTitle = cc.EnglishTitle };
            selectedSeatType = null;
            seatTypesList = Service.GetSeatType(selectedAssembly.Id, selectedConstituency.Id).Result;
            if (seatTypesList.Count == 1)
            {
                selectedSeatType = new GeneralItemDTO
                {
                    Id = seatTypesList[0].Id, EnglishTitle = seatTypesList[0].EnglishTitle,
                    UrduTitle = seatTypesList[0].UrduTitle
                };
                nominations = Service.GetNominations(selectedAssembly.Id, selectedConstituency.Id, selectedSeatType.Id,
                    state.state.PhaseId).Result;
            }
            else
            {
                nominations = new List<NominationDTO>();
            }

            constDetail = Service.GetConstInfo(selectedConstituency.Id, state.state.PhaseId, SelectedSeatTypeId ?? 0)
                .Result;
            InvokeAsync(StateHasChanged);
        }
        else
        {
            selectedConstituency = null;
            selectedSeatType = null;
            seatTypesList = new List<GeneralItemDTO>();
            constDetail = null;
            InvokeAsync(StateHasChanged);
        }

        StateHasChanged();
    }

    private void OnChangeSeatType(GeneralItemDTO st)
    {
        if (st != null)
        {
            selectedSeatType = new GeneralItemDTO
                { Id = st.Id, EnglishTitle = st.EnglishTitle, UrduTitle = st.UrduTitle };
            nominations = Service.GetNominations(selectedAssembly.Id, selectedConstituency.Id, selectedSeatType.Id,
                state.state.PhaseId).Result;
        }
        else
        {
            selectedSeatType = null;
            nominations = new List<NominationDTO>();
        }

        InvokeAsync(StateHasChanged);
    }

    private void OpenConstituencyForm()
    {
        if (constDetail != null && SelectedConstituencyId != null)
        {
            constDetail = Service.GetConstInfo(selectedConstituency.Id, state.state.PhaseId, SelectedSeatTypeId ?? 0)
                .Result;
            BasicConstInfo = new BasicConstituencyDTO
            {
                ConstituencyId = (int)SelectedConstituencyId,
                FemaleVoters = constDetail.FemaleVoters,
                MaleVoters = constDetail.MaleVoters,
                TotalPollingStations = constDetail.TotalPollingStations,
                Population = constDetail.Population,
                IsRePoll = constDetail.IsRePoll,
                IsBigContest = constDetail.BigContest,
                RejectedVotes = constDetail.RejectedVotes
            };
            EditConstituencyMode = true;
        }

        StateHasChanged();
    }

    private async Task OpenCreateForm()
    {
        CandidateName = string.Empty;
        //if (candListAll == null || candListAll.Count() == 0)
        //	candListAll = await Service.SearchCandidate(state.state.ElectionId, ElectionBasedOn);
        selectedObj = new NominationDTO
        {
            Id = 0,
            AssemblyId = selectedAssembly.Id,
            ElectionId = state.state.ElectionId,
            SeatTypeId = selectedSeatType.Id,
            ConstituencyId = selectedConstituency.Id,
            PhaseId = state.state.PhaseId,
            Weight = 0
        };
        await dlgForm.ShowAsync();
    }

    private async Task OpenEditForm(NominationDTO obj)
    {
        //selectedObj = new NominationDTO
        //{
        //	Id = obj.Id,
        //	CandidateId = obj.CandidateId,
        //	AssemblyId = obj.AssemblyId,
        //	ElectionId = obj.ElectionId,
        //	PartyId = obj.PartyId,
        //	PhaseId = obj.PhaseId,
        //	SeatTypeId = obj.SeatTypeId,
        //	ConstituencyId = obj.ConstituencyId,
        //	SymbolId = obj.SymbolId,
        //	Weight = obj.Weight
        //};
        selectedObj = await Service.GetNominationById(obj.Id);
        CandidateName = $"{selectedObj.Id} - {selectedObj.CandidateName}";
        await dlgForm.ShowAsync();
    }

    private async Task OpenSearchCandidate()
    {
        searchText = string.Empty;
        await dlgSeachCand.ShowAsync();
        StateHasChanged();
    }

    private async Task ReEnterNomination()
    {
        var res = await JSRuntime.InvokeAsync<bool>("confirm", new[] { "Are you sure want to continue?" });
        if (res)
            if (selectedObj.Id != 0)
            {
                var user = (await authenticationStateTask).User;
                var op = await Service.ReEnterNomination(selectedObj.Id, user.Identity.Name);
                if (op != "OK")
                {
                    ToastModel tt = new() { Title = "Error", Content = op };
                    await ToastObj.ShowAsync(tt);
                }
                else
                {
                    await FillNominations();
                    await dlgForm.HideAsync();
                }
            }
    }

    private async Task RemoveBilaMoqbila()
    {
        var user = (await authenticationStateTask).User;

        if (nominations != null && nominations.Count == 1)
        {
            var res = await Service.RemoveBilaMoqbila(nominations[0].Id, user.Identity.Name);
            if (res != "OK")
            {
                var mm = new ToastModel
                {
                    Title = "Error", Content = res, CssClass = "e-toast-danger", ShowProgressBar = true, Timeout = 5000,
                    ShowCloseButton = true
                };
                await ToastObj.ShowAsync(mm);
            }
            else
            {
                nominations = await Service.GetNominations(selectedAssembly.Id, selectedConstituency.Id,
                    selectedSeatType.Id, state.state.PhaseId);
                StateHasChanged();
            }
        }
    }

    private async Task SaveData()
    {
        try
        {
            selectedObj.AssemblyId = selectedAssembly.Id;
            selectedObj.PhaseId = state.state.PhaseId;
            selectedObj.ConstituencyId = selectedConstituency.Id;
            selectedObj.SeatTypeId = selectedSeatType.Id;
            selectedObj.ElectionId = state.state.ElectionId;

            var user = (await authenticationStateTask).User;
            await Service.SaveNomination(selectedObj, user.Identity.Name);
            await FillNominations();
            await dlgForm.HideAsync();
            var cand = await Service.GetCandidateNominations(state.state.ElectionId, (int)selectedObj.CandidateId);
            //await hubConnection.SendAsync("UpdateNomination", cand);
        }
        catch (Exception ex)
        {
            var msg = ex.Message;
            if (ex.InnerException != null)
                msg += "<br>" + ex.InnerException.Message;
            var mm = new ToastModel
            {
                Title = "Error", Content = ex.Message, CssClass = "e-toast-danger", ShowProgressBar = true,
                Timeout = 5000, ShowCloseButton = true
            };
            await ToastObj.ShowAsync(mm);
        }
    }

    private async Task SearchCandidateAsync()
    {
        var st = searchText.Trim().ToLower();
        var id = 0;
        try
        {
            id = int.Parse(st);
        }
        catch (Exception)
        {
            // ignored
        }

        candListFilter = await Service.SearchCandidate3(st, id, state.state.ElectionId);
        StateHasChanged();
    }

    //private void SearchCandidate2()
    //{
    //	var st = searchText.Trim().ToLower();
    //	int id = 0;
    //	try
    //	{
    //		id = int.Parse(searchText);
    //	}
    //	catch (Exception)
    //	{
    //	}
    //	if (id > 0)
    //	{
    //		candListFilter = (from aa in candListAll
    //								where aa.Id == id
    //								select aa).Take(20).ToList();
    //	}
    //	else
    //	{
    //		candListFilter = (from aa in candListAll
    //								where (aa.EnglishName ?? "").ToLower().Contains(st) ||
    //												 (aa.UrduName ?? "").ToLower().Contains(st) ||
    //												 (aa.Party ?? "").ToLower().Contains(st) ||
    //												 (aa.LastConstituency ?? "").ToLower().Contains(st)
    //								orderby aa.EnglishName
    //								select aa).Take(20).ToList();
    //	}
    //	StateHasChanged();
    //}

    private async Task SelectCandidate(int candidateId, int? partyId, int? symbolId, string name)
    {
        selectedObj.PartyId = partyId;
        selectedObj.SymbolId = symbolId;
        //await FillCandidateParyAndSymbol();
        await dlgSeachCand.HideAsync();
        searchText = string.Empty;
        candListFilter = new List<SearchCandidateDTO>();
        //
        selectedObj.CandidateId = candidateId;
        selectedObj.CandidateURL = await Service.GetCandidateUrlById(selectedObj.CandidateId);
        selectedObj.PartyFlagURL = await Service.GetFlagUrlById(selectedObj.PartyId);
        selectedObj.SymbolURL = await Service.GetSymbolUrlById(selectedObj.SymbolId);
        CandidateName = $"{candidateId} - {name}";
        StateHasChanged();
        await Task.CompletedTask;
    }

    private async Task TranslateToUrdu()
    {
        try
        {
            if (string.IsNullOrEmpty(panelistObj.NameUrdu))
                panelistObj.NameUrdu = await Translation.TranslateText(panelistObj.NameEnglish);
        }
        catch (Exception)
        {
            // ignored
        }
    }

    private async Task UpdateConstituencyInfo()
    {
        var user = (await authenticationStateTask).User;
        if (user != null)
        {
            var op = await Service.UpdateConstituencyInfo(BasicConstInfo.ConstituencyId, BasicConstInfo.MaleVoters,
                BasicConstInfo.FemaleVoters, BasicConstInfo.TotalPollingStations, BasicConstInfo.Population,
                user.Identity.Name, state.state.PhaseId, BasicConstInfo.IsRePoll, SelectedSeatTypeId.Value,
                BasicConstInfo.YouthVoters, BasicConstInfo.IsBigContest, BasicConstInfo.RejectedVotes);
            EditConstituencyMode = false;
            constDetail = Service.GetConstInfo(selectedConstituency.Id, state.state.PhaseId, SelectedSeatTypeId.Value)
                .Result;
            StateHasChanged();
        }
    }

    private async Task WithdrawNomination()
    {
        var res = await JSRuntime.InvokeAsync<bool>("confirm", new[] { "Are you sure want to continue?" });
        if (res)
            if (selectedObj.Id != 0)
            {
                var user = (await authenticationStateTask).User;
                var op = await Service.WithdrawNomination(selectedObj.Id, user.Identity.Name);
                if (op != "OK")
                {
                    var tt = new ToastModel
                    {
                        Title = "Error", Content = op, CssClass = "e-toast-danger", ShowProgressBar = true,
                        Timeout = 5000, ShowCloseButton = true
                    };
                    await ToastObj.ShowAsync(tt);
                }
                else
                {
                    await FillNominations();
                    await dlgForm.HideAsync();
                }
            }
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            var user = (await authenticationStateTask).User;
            if (user.Identity.IsAuthenticated)
            {
                //var dd =  await ProtectedSessionStore.GetAsync<ElectionStateDTO>("election");
                var dd = await localStorage.GetAsync<ElectionStateDTO>(await confreader.GetSettingValue("electionkey"));
                state.SetState(dd.Value);
                await FillAsm_Phase();
                //candidatesList = await Service.GetCandidatesList();
                partiesList = await Service.GetParties_list(state.state.ElectionId);
                symbolsList = await Service.GetSymbols_list();
                StateHasChanged();
            }
        }
    }

    protected override async Task OnInitializedAsync()
    {
        AllSeatTypesList = await Service.GetAllSeatTypes();
        mediapath = env.WebRootPath + "\\media\\"; //await confreader.GetSettingValue("mediapath");
        //mediapath = env.WebRootPath;
        //hubConnection = new HubConnectionBuilder()
        //	 .WithUrl(NavigationManager.ToAbsoluteUri("/candidatehub"))
        //	 .Build();

        //hubConnection.On<int>("OnRemoveCandidate", (id) =>
        //{
        //		//dlgForm.HideAsync();
        //		var isExist = (from r in candListAll
        //						where r.Id == id
        //						select r).FirstOrDefault();

        //	if (isExist != null)
        //	{
        //		candListAll.Remove(isExist);
        //	}
        //	StateHasChanged();
        //});

        //hubConnection.On<CandidateDTO>("OnUpdateNomination", (cc) =>
        //{
        //		//dlgForm.HideAsync();

        //		if (candListAll != null)
        //	{
        //		var kk = candListAll.Find(c => c.Id == cc.Id);
        //		if (kk != null)
        //		{
        //			kk.Id = cc.Id;
        //			kk.EnglishName = cc.EnglishName;
        //			kk.Type = cc.CandidateTypeStr;
        //			kk.UrduName = cc.UrduName;
        //			kk.Gender = cc.Gender;
        //			kk.UrduFatherName = cc.UrduFatherName;
        //			kk.UrduName = cc.UrduName;
        //			kk.CreatedBy = cc.CreatedBy;
        //			kk.ModifiedBy = cc.ModifiedBy;
        //			kk.CreatedDate = cc.CreatedOn;
        //			kk.LastConstituency = cc.Constituencies;
        //			kk.Disrict = cc.District;
        //			kk.ImgUrl = cc.PictureURL;
        //			kk.ModifiedDate = cc.ModifiedOn;
        //		}

        //		StateHasChanged();
        //	}
        //});

        //hubConnection.On<CandidateDTO>("ReceiveCandidate", (can) =>
        //{
        //		//dlgForm.HideAsync();
        //		//var ok = result;
        //		//results.Add(result);
        //		var isExist = (from r in candListAll
        //						where r.Id == can.Id
        //						select r).FirstOrDefault();
        //	if (isExist != null)
        //	{
        //		isExist.CreatedBy = can.CreatedBy;
        //		isExist.CreatedDate = can.CreatedOn;
        //		isExist.Disrict = can.District;
        //		isExist.EnglishName = can.EnglishName;
        //		isExist.Id = can.Id;
        //		isExist.ModifiedBy = can.ModifiedBy;
        //		isExist.ModifiedDate = can.ModifiedOn;
        //		isExist.Party = can.CurrentParty;
        //		isExist.UrduName = can.UrduName;
        //	}
        //	else
        //	{
        //		candListAll.Add(new SearchCandidateDTO
        //		{
        //			CreatedBy = can.CreatedBy,
        //			UrduName = can.UrduName,
        //			Party = can.CurrentParty,
        //			CreatedDate = can.CreatedOn,
        //			Disrict = can.District,
        //			EnglishName = can.EnglishName,
        //			Id = can.Id,
        //			ImgUrl = "",
        //			LastConstituency = "",
        //			ModifiedBy = can.ModifiedBy,
        //			ModifiedDate = can.ModifiedOn,
        //			Type = can.CandidateType
        //		});
        //	}

        //	StateHasChanged();
        //});

        //await hubConnection.StartAsync();
    }

    //public async ValueTask DisposeAsync()
    //{
    //	//await hubConnection.DisposeAsync();
    //}

    public async Task FillAsm_Phase()
    {
        assembliesList = await Service.GetAssmblies(state.state.ElectionId);
        //assembliesList.Insert(0, new GeneralItemDTO { Id = 0, EnglishTitle = "Select Assembly" });
        //constituenciesList = new List<GeneralItemDTO>() { new GeneralItemDTO { Id = 0, EnglishTitle = "Select Constituency" } };
        //seatTypesList = new List<GeneralItemDTO>() { new GeneralItemDTO { Id = 0, EnglishTitle = "Select Seat Type" } };
        constituenciesList = new List<GeneralItemDTO>();
        seatTypesList = new List<GeneralItemDTO>();
        selectedConstituency = null;
        selectedSeatType = null;
        StateHasChanged();
    }

    public async Task FillNominations()
    {
        //nominations = new List<NominationDTO>();
        try
        {
            nominations = await Service.GetNominations(selectedAssembly.Id, selectedConstituency.Id,
                selectedSeatType.Id, state.state.PhaseId);
        }
        catch (Exception)
        {
            nominations = new List<NominationDTO>();
        }
    }

    public async Task OnCandidateSelect(ChangeEventArgs<int?, SearchCandidateDTO> args)
    {
        selectedObj.CandidateId = args.Value;
        selectedObj.CandidateURL = await Service.GetCandidateUrlById(selectedObj.CandidateId);
        await FillCandidateParyAndSymbol();
        StateHasChanged();
    }

    public async Task OnPartyChange(ChangeEventArgs<int?, GeneralItemDTO> args)
    {
        try
        {
            selectedObj.PartyId = args.Value;
            selectedObj.SymbolId = await Service.GetPartySymbolId((int)selectedObj.PartyId);
            selectedObj.PartyFlagURL = await Service.GetFlagUrlById(selectedObj.PartyId);
            selectedObj.SymbolURL = await Service.GetSymbolUrlById(selectedObj.SymbolId);
            StateHasChanged();
        }
        catch (Exception)
        {
            // ignored
        }
    }

    public async Task OnSymbolChange(ChangeEventArgs<int?, GeneralItemDTO> args)
    {
        selectedObj.SymbolId = args.Value;
        selectedObj.SymbolURL = await Service.GetSymbolUrlById(selectedObj.SymbolId);
        StateHasChanged();
    }

    public async Task PostpondConstituency()
    {
        var user = (await authenticationStateTask).User;
        var res = await JSRuntime.InvokeAsync<bool>("confirm",
            new[] { "Are you sure want to postpone election on this constituency?" });
        if (res)
        {
            var op = await Service.PostponeElecOnConst(selectedConstituency.Id, state.state.PhaseId,
                user.Identity.Name);
            if (op == "OK")
            {
                await FillNominations();
                StateHasChanged();
            }
            else
            {
                var tto = new ToastModel
                {
                    Title = "Error", Content = op, CssClass = "e-toast-danger", ShowProgressBar = true, Timeout = 5000,
                    ShowCloseButton = true
                };
                await ToastObj.ShowAsync(tto);
            }
        }
    }

    #region Panelist

    private bool IsDlgPanelistVisibl;
    private SfDialog dlgPanelist;
    private IFileListEntry file;
    private bool IsPanelistFormOpen;
    private PanelistDTO panelistObj = new();

    private readonly List<GeneralItemDTO> gendersList = new()
    {
        new GeneralItemDTO { Id = 1, EnglishTitle = "Male" },
        new GeneralItemDTO { Id = 2, EnglishTitle = "Female" },
        new GeneralItemDTO { Id = 3, EnglishTitle = "Trans-Gender" }
    };

    private async void OpenCreatePanelistForm()
    {
        panelistObj = new PanelistDTO();
        var f = mediapath + @"\candidates\blank.jpg";
        var fs = File.OpenRead(f);
        var fileBytes = new byte[fs.Length];
        fs.Read(fileBytes, 0, fileBytes.Length);
        fs.Close();
        FileByteArray = new byte[0];
        FileBase64String = Convert.ToBase64String(fileBytes);

        panelistList = await Service.GetPanelistsOnNomination(selectedObj.Id);
        IsPanelistFormOpen = true;
    }

    private List<GeneralItemDTO> AllSeatTypesList { get; set; } = new();
    private List<PanelistDTO> panelistList { get; set; } = new();
    public string FileBase64String { get; set; }
    public byte[] FileByteArray { get; set; }

    private async Task HandleFileSelected(IFileListEntry[] files)
    {
        file = files.FirstOrDefault();
        var dd = await file.ReadAllAsync();
        FileByteArray = dd.ToArray();
        FileBase64String = Convert.ToBase64String(FileByteArray);
    }

    public async void DeletePanelistRecord(int id)
    {
        var res = await Service.DeletePanelistNomination(id);
        if (res == "OK") panelistList = await Service.GetPanelistsOnNomination(selectedObj.Id);
    }

    private async void OpenPanelistEditForm(int id)
    {
        IsPanelistFormOpen = true;
        panelistObj = await Service.GetPanelist(id);
        FileBase64String = string.Empty;
        FileByteArray = Array.Empty<byte>();
        try
        {
            var f = mediapath + @"\panelcandidates\" + id + ".jpg";
            var fs = File.OpenRead(f);
            var fileBytes = new byte[fs.Length];
            fs.Read(fileBytes, 0, fileBytes.Length);
            fs.Close();
            FileBase64String = Convert.ToBase64String(fileBytes);
        }
        catch (Exception)
        {
            var f = mediapath + @"\candidates\blank.jpg";
            var fs = File.OpenRead(f);
            var fileBytes = new byte[fs.Length];
            fs.Read(fileBytes, 0, fileBytes.Length);
            fs.Close();
            FileBase64String = Convert.ToBase64String(fileBytes);
        }
    }

    private void ClosePanelistForm()
    {
        IsPanelistFormOpen = false;
        StateHasChanged();
    }

    private async Task ShowPanelist(int nominationId)
    {
        IsPanelistFormOpen = false;
        AllSeatTypesList = await Service.GetAllSeatTypes();
        selectedObj.Id = nominationId;
        panelistList = await Service.GetPanelistsOnNomination(selectedObj.Id);
        await dlgPanelist.ShowAsync();
    }

    public string mediapath { get; set; }

    private async Task SaveNominationPanelistData()
    {
        var user = (await authenticationStateTask).User;
        panelistObj.NominationId = selectedObj.Id;
        var res = await Service.SavePanelistOnNomination(panelistObj, user.Identity.Name);

        if (res != 0)
        {
            if (FileByteArray != null && FileByteArray.Length > 0)
            {
                // create folder if not exist and save file
                var path = Path.Combine(mediapath + @"\panelcandidates\");
                if (!Directory.Exists(path)) Directory.CreateDirectory(path);
                File.WriteAllBytes(mediapath + @"\panelcandidates\" + res + ".jpg", FileByteArray);
            }

            //"Panelist Saved Successfully", "", "Success"
            await ToastObj.ShowAsync(new ToastModel
                { Content = "Panelist Saved Successfully", Timeout = 3000, Title = "Success" });
            panelistList = await Service.GetPanelistsOnNomination(selectedObj.Id);
            IsPanelistFormOpen = false;
        }
    }

    #endregion Panelist
}