﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using Dapper;
using ElectionAppServer.DTO;
using ElectionAppServer.Model;
using Microsoft.EntityFrameworkCore;

namespace ElectionAppServer.Data;

public class UserManagementDataService
{
    private readonly IDbContextFactory<ApplicationDbContext>
        _contextFactory; //public ApplicationDbContext  dc  { get; }

    public UserManagementDataService(
        IDbContextFactory<ApplicationDbContext> contextFactory) // (ApplicationDbContext  dc)
    {
        _contextFactory = contextFactory;
    }

    public Task<List<ConstituencyDTO>> GetSelectedConstituencies(string userId, int electionId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var con = dc.Database.GetDbConnection();
        var prams = new { ElectionId = electionId, userId };
        var res = con
            .Query<ConstituencyDTO>("app.GetUserConstituencies", prams, commandType: CommandType.StoredProcedure)
            .ToList();
        return Task.FromResult(res);
    }


    public Task<List<ConstituencyDTO>> GetSelectedConstituencies2(string userId, int electionId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var qUCs = (from st in dc.Structures.OfType<UnionCouncil>()
            //join st in dc.Structures.OfType<UnionCouncil>() on aa.StructureId equals st.Id
            where st.Assembly.ElectionId == electionId
            select new ConstituencyDTO
            {
                Type = "UC",
                Assembly = st.Assembly.UrduTitle,
                AssemblyId = st.AssemblyId ?? 0,
                Code = st.Code,
                UrduTitle = st.UrduTitle,
                EnglishTitle = st.EnglishTitle,
                Town = st.Town.UrduTitle,
                District = st.Town.District.UrduTitle,
                Division = st.Town.District.Division.UrduTitle,
                Province = st.Town.District.Division.Province.UrduTitle,
                Id = st.Id
            }).ToList();

        var qWards = (from st in dc.Structures.OfType<Ward>()
            //join st in dc.Structures.OfType<Ward>() on aa.StructureId equals st.Id
            where st.Assembly.ElectionId == electionId
            select new ConstituencyDTO
            {
                Type = "WD",
                Assembly = st.Assembly.UrduTitle,
                AssemblyId = st.AssemblyId ?? 0,
                Code = st.Code,
                UrduTitle = st.UrduTitle,
                EnglishTitle = st.EnglishTitle,
                UC = st.UnionCouncil.UrduTitle,
                Town = st.UnionCouncil.Town.UrduTitle,
                District = st.UnionCouncil.Town.District.UrduTitle,
                Division = st.UnionCouncil.Town.District.Division.UrduTitle,
                Province = st.UnionCouncil.Town.District.Division.Province.UrduTitle,
                Id = st.Id
            }).ToList();

        var qNAs = (from st in dc.Structures.OfType<NationalAssemblyHalka>()
            //join st in dc.Structures.OfType<NationalAssemblyHalka>() on aa.StructureId equals st.Id
            where st.Assembly.ElectionId == electionId
            select new ConstituencyDTO
            {
                Type = "NA",
                Assembly = st.Assembly.UrduTitle,
                AssemblyId = st.AssemblyId ?? 0,
                Code = st.Code,
                UrduTitle = st.UrduTitle,
                EnglishTitle = st.EnglishTitle,
                UC = "",
                Town = "",
                Province = st.District.Division.Province.UrduTitle,
                District = st.District.Division.UrduTitle,
                Division = st.District.UrduTitle,
                //UC = st.UnionCouncil.UrduTitle,
                //Town = st.UnionCouncil.Town.UrduTitle,
                //District = st.UnionCouncil.Town.District.UrduTitle,
                //Division = st.UnionCouncil.Town.District.Division.UrduTitle,
                //Province = st.UnionCouncil.Town.District.Division.Province.UrduTitle,
                Id = st.Id
            }).ToList();

        var qPAs = (from st in dc.Structures.OfType<ProvincialAssemblyHalka>()
            //join st in dc.Structures.OfType<ProvincialAssemblyHalka>() on aa.StructureId equals st.Id
            where st.Assembly.ElectionId == electionId
            select new ConstituencyDTO
            {
                Type = "NA",
                Assembly = st.Assembly.UrduTitle,
                AssemblyId = st.AssemblyId ?? 0,
                Code = st.Code,
                UrduTitle = st.UrduTitle,
                EnglishTitle = st.EnglishTitle,
                UC = "",
                Town = "",
                Province = st.District.Division.Province.UrduTitle,
                District = st.District.Division.UrduTitle,
                Division = st.District.UrduTitle,
                //UC = st.UnionCouncil.UrduTitle,
                //Town = st.UnionCouncil.Town.UrduTitle,
                //District = st.UnionCouncil.Town.District.UrduTitle,
                //Division = st.UnionCouncil.Town.District.Division.UrduTitle,
                //Province = st.UnionCouncil.Town.District.Division.Province.UrduTitle,
                Id = st.Id
            }).ToList();

        var AllConst = qUCs.Union(qWards).Union(qNAs).Union(qPAs).ToList();

        var UserConsts = (from aa in dc.UserConstituencies
            where aa.UserId == userId
            select aa).ToList();

        var op = new List<ConstituencyDTO>();
        foreach (var c in AllConst)
        {
            var exist = (from aa in UserConsts
                where aa.ElectionAssemblyId == c.AssemblyId && aa.StructureId == c.Id
                select aa).Any();
            if (exist) op.Add(c);
        }

        return Task.FromResult(op);
    }

    public Task<List<ConstituencyDTO>> GetAvailableConstituencies(string userId, int electionId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var qUCs = (from aa in dc.Structures.OfType<UnionCouncil>()
            //join st in dc.Structures.OfType<UnionCouncil>() on aa.StructureId equals st.Id
            where aa.Assembly.ElectionId == electionId
            select new ConstituencyDTO
            {
                Type = "UC",
                Assembly = aa.Assembly.UrduTitle,
                AssemblyId = aa.AssemblyId ?? 0,
                Code = aa.Code,
                UrduTitle = aa.UrduTitle,
                EnglishTitle = aa.EnglishTitle,
                Town = aa.Town.UrduTitle,
                District = aa.Town.District.UrduTitle,
                Division = aa.Town.District.Division.UrduTitle,
                Province = aa.Town.District.Division.Province.UrduTitle,
                Id = aa.Id
            }).ToList();

        var qWards = (from aa in dc.Structures.OfType<Ward>()
            //join st in dc.Structures.OfType<Ward>() on aa.StructureId equals st.Id
            where aa.Assembly.ElectionId == electionId
            select new ConstituencyDTO
            {
                Type = "WD",
                Assembly = aa.UrduTitle,
                AssemblyId = aa.AssemblyId ?? 0,
                Code = aa.Code,
                UrduTitle = aa.UrduTitle,
                EnglishTitle = aa.EnglishTitle,
                UC = aa.UnionCouncil.UrduTitle,
                Town = aa.UnionCouncil.Town.UrduTitle,
                District = aa.UnionCouncil.Town.District.UrduTitle,
                Division = aa.UnionCouncil.Town.District.Division.UrduTitle,
                Province = aa.UnionCouncil.Town.District.Division.Province.UrduTitle,
                Id = aa.Id
            }).ToList();

        var qNAs = (from aa in dc.Structures.OfType<NationalAssemblyHalka>()
            //join st in dc.Structures.OfType<NationalAssemblyHalka>() on aa.StructureId equals st.Id
            where aa.Assembly.ElectionId == electionId
            select new ConstituencyDTO
            {
                Type = "NA",
                Assembly = aa.UrduTitle,
                AssemblyId = aa.AssemblyId ?? 0,
                Code = aa.Code,
                UrduTitle = aa.UrduTitle,
                EnglishTitle = aa.EnglishTitle,
                UC = "",
                Town = "",
                Province = aa.District.Division.Province.UrduTitle,
                District = aa.District.UrduTitle,
                Division = aa.District.Division.UrduTitle,
                Id = aa.Id
            }).ToList();

        var qPAs = (from aa in dc.Structures.OfType<ProvincialAssemblyHalka>()
            //join st in dc.Structures.OfType<ProvincialAssemblyHalka>() on aa.StructureId equals st.Id
            where aa.Assembly.ElectionId == electionId
            select new ConstituencyDTO
            {
                Type = "PA",
                Assembly = aa.UrduTitle,
                AssemblyId = aa.AssemblyId ?? 0,
                Code = aa.Code,
                UrduTitle = aa.UrduTitle,
                EnglishTitle = aa.EnglishTitle,
                UC = "",
                Town = "",
                District = aa.District.UrduTitle,
                Division = aa.District.Division.UrduTitle,
                Province = aa.District.Division.Province.UrduTitle,
                //UC = st.UnionCouncil.UrduTitle,
                //Town = st.UnionCouncil.Town.UrduTitle,
                //District = st.UnionCouncil.Town.District.UrduTitle,
                //Division = st.UnionCouncil.Town.District.Division.UrduTitle,
                //Province = st.UnionCouncil.Town.District.Division.Province.UrduTitle,
                Id = aa.Id
            }).ToList();

        var AllConst = qUCs.Union(qWards).Union(qNAs).Union(qPAs).ToList();

        var UserConsts = (from aa in dc.UserConstituencies
            where aa.UserId == userId
            select aa).ToList();

        var op = new List<ConstituencyDTO>();
        foreach (var c in AllConst)
        {
            var exist = (from aa in UserConsts
                where aa.ElectionAssemblyId == c.AssemblyId && aa.StructureId == c.Id
                select aa).Any();
            if (!exist) op.Add(c);
        }

        return Task.FromResult(op);
    }

    public Task<string> SaveUserConstituencies(string userId, List<int> assemblies, List<int> structures)
    {
        using var dc = _contextFactory.CreateDbContext();
        using (var tran = dc.Database.BeginTransaction())
        {
            try
            {
                for (var i = 0; i < assemblies.Count; i++)
                {
                    var uc = new UserConstituency
                        { ElectionAssemblyId = assemblies[i], UserId = userId, StructureId = structures[i] };
                    dc.UserConstituencies.Add(uc);
                    dc.SaveChanges();
                }

                tran.Commit();
                return Task.FromResult("OK");
            }
            catch (Exception)
            {
                tran.Rollback();
                throw;
            }
        }
    }

    public Task<string> RemoveUserConstituencies(string userId, List<int> assemblies, List<int> structures)
    {
        using var dc = _contextFactory.CreateDbContext();
        using (var tran = dc.Database.BeginTransaction())
        {
            try
            {
                for (var i = 0; i < assemblies.Count; i++)
                {
                    var q = (from aa in dc.UserConstituencies
                            where aa.UserId == userId &&
                                  aa.ElectionAssemblyId == assemblies[i] &&
                                  aa.StructureId == structures[i]
                            select aa
                        ).FirstOrDefault();
                    if (q != null)
                    {
                        dc.UserConstituencies.Remove(q);
                        dc.SaveChanges();
                    }
                }

                tran.Commit();
                return Task.FromResult("OK");
            }
            catch (Exception)
            {
                tran.Rollback();
                throw;
            }
        }
    }

    public Task<bool> HasConstituencies(string userId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = dc.UserConstituencies.Any(c => c.UserId == userId);
        return Task.FromResult(q);
    }

    public Task<string> AssigneConsToUser(string userId, List<ConstituencyDTO> constituencies)
    {
        using var dc = _contextFactory.CreateDbContext();
        using var tran = dc.Database.BeginTransaction();
        try
        {
            foreach (var co in constituencies)
            {
                var q = (from aa in dc.UserConstituencies
                    where aa.UserId == userId &&
                          aa.StructureId == co.Id &&
                          aa.ElectionAssemblyId == co.AssemblyId
                    select aa).FirstOrDefault();

                if (q == null)
                {
                    q = new UserConstituency
                        { ElectionAssemblyId = co.AssemblyId, StructureId = co.Id, UserId = userId };
                    dc.UserConstituencies.Add(q);
                    dc.SaveChanges();
                }
            }

            tran.Commit();
            return Task.FromResult("OK");
        }
        catch (Exception ex)
        {
            tran.Rollback();
            return Task.FromResult(ex.Message);
        }
    }

    public Task<string> RemoveConsFromUser(string userId, List<ConstituencyDTO> constituencies)
    {
        using var dc = _contextFactory.CreateDbContext();
        using var tran = dc.Database.BeginTransaction();
        try
        {
            foreach (var co in constituencies)
            {
                var q = (from aa in dc.UserConstituencies
                    where aa.UserId == userId &&
                          aa.StructureId == co.Id &&
                          aa.ElectionAssemblyId == co.AssemblyId
                    select aa).FirstOrDefault();

                if (q != null)
                {
                    //q = new UserConstituency { ElectionAssemblyId = co.AssemblyId, StructureId = co.Id, UserId = userId };
                    dc.UserConstituencies.Remove(q);
                    dc.SaveChanges();
                }
            }

            tran.Commit();
            return Task.FromResult("OK");
        }
        catch (Exception ex)
        {
            tran.Rollback();
            return Task.FromResult(ex.Message);
        }
    }
}