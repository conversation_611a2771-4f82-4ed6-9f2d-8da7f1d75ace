﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace ElectionAppServer.DTO;

public class CandidateDTO
{
    public int Id { get; set; }

    public int? Day { get; set; }

    public int? Month { get; set; }

    public int? Year { get; set; }

    public int? Age { get; set; }

    [Required]
    [Display(Name = "Name (Urdu)")]
    [MaxLength(200)]
    public string UrduName { get; set; }

    [Required]
    [Display(Name = "Name (English)")]
    [MaxLength(200)]
    public string EnglishName { get; set; }

    [Display(Name = "Father Name (Urdu)")]
    [MaxLength(200)]
    public string UrduFatherName { get; set; }

    [Display(Name = "Father Name (English)")]
    [MaxLength(200)]
    public string EnglishFatherName { get; set; }

    [Required] public int? Gender { get; set; }

    //public bool IsFresh { get; set; }
    public int? IsFresh { get; set; } // 1=Fresh;2=Experienced

    public string FreshStatus => CandidateType == "1" ? IsFresh == 1 ? "Fresh" : "Experienced" : string.Empty;

    public DateTime? DateOfBirth { get; set; }

    public string DateOfBirthStr { get; set; }

    [StringLength(200)] public string District { get; set; }

    public int? DistrictId { get; set; }

    public string Trivia { get; set; }

    public string Constituencies { get; set; }

    [Required] public string CandidateType { get; set; }

    public List<PartyAffiliationDTO> PartyAffiliations { get; set; } = new();

    public List<PoliticalCareerDTO> PoliticalCareers { get; set; } = new();

    public int? LanguageId { get; set; }

    public string Language { get; set; }

    public int? ProfessionId { get; set; }

    public string CandidateProfession { get; set; }

    public int? CasteId { get; set; }

    public string Caste { get; set; }

    public int? EducationId { get; set; }

    public string Education { get; set; }

    public string GenderTitle
    {
        get
        {
            if (Gender == 1)
                return "Male";
            if (Gender == 2)
                return "Female";
            if (Gender == 3)
                return "Transgender";
            return string.Empty;
        }
    }

    public string TotalAssets { get; set; }

    public string ContactNo { get; set; }

    public List<CandidateElection> CandidateElections { get; set; } = new();

    public string CurrentParty { get; set; }

    [StringLength(200)] public string EducationDegree { get; set; }

    public string CreatedBy { get; set; }

    public string CreatedOn { get; set; }

    public string ModifiedBy { get; set; }

    public string ModifiedOn { get; set; }

    public string FullName { get; set; }

    public string CandidateTypeStr { get; set; }

    public bool IsSelected { get; set; }

    public bool NominationExistOnThisElection { get; set; }

    public bool DisableDelete { get; set; } = true;

    public string PictureURL { get; set; }

    public List<CandidateElectionWiseDistrictDTO> ElectionWiseDistricts { get; set; } = new();

    public string IsFreshStr { get; set; }
    //public int? CurrentPartyId { get; set; }


    public bool IsBigName { get; set; }
    public string BigName => IsBigName ? "Yes" : "No";
    public bool HasRealDOB { get; set; }
}

public class PartyAffiliationDTO
{
    public string UI_Id { get; set; } = Guid.NewGuid().ToString();

    public int Id { get; set; }

    [Required] public int PartyId { get; set; }

    public string Party { get; set; }

    [Required] [MaxLength(200)] public string Position { get; set; }

    [Required] public DateTime DateFrom { get; set; }

    public DateTime? DateTo { get; set; }
}

public class PoliticalCareerDTO
{
    public string UI_Id { get; set; } = Guid.NewGuid().ToString();

    public int Id { get; set; }

    [Required] [MaxLength(200)] public string Position { get; set; }

    [Required] public DateTime DateFrom { get; set; }

    public DateTime? DateTo { get; set; }

    public bool TillToday { get; set; }
}