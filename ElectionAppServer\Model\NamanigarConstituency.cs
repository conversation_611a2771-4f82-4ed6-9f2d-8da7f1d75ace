﻿using System;

namespace ElectionAppServer.Model;

public class NamanigarConstituency
{
    //public int Id { get; set; }
    public int NamanigarId { get; set; }

    public virtual Namanigar Namanigar { get; set; }
    public int ConstituencyId { get; set; }
    public virtual Structure Constituency { get; set; }
    public string PSDetail { get; set; }

    #region Audit Log

    public DateTime CreatedDate { get; set; }
    public string CreatedBy { get; set; }
    public DateTime? ModifiedDate { get; set; }
    public string ModifiedBy { get; set; }

    #endregion Audit Log
}