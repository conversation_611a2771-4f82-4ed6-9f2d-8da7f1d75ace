﻿<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <UserSecretsId>aspnet-ElectionAppServer-BC18A5F1-5282-465C-8086-AAEDA2F82042</UserSecretsId>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <Optimize>false</Optimize>
  </PropertyGroup>
  <ItemGroup>
    <Compile Remove="wwwroot\media\**" />
    <Compile Remove="wwwroot\NewFolder\**" />
    <Content Remove="wwwroot\media\**" />
    <Content Remove="wwwroot\NewFolder\**" />
    <EmbeddedResource Remove="wwwroot\media\**" />
    <EmbeddedResource Remove="wwwroot\NewFolder\**" />
    <None Remove="wwwroot\media\**" />
    <None Remove="wwwroot\NewFolder\**" />
  </ItemGroup>
  <ItemGroup>
    <Compile Remove="Data\AdministrativeUnitDataService.cs" />
    <Compile Remove="Data\AssemblyService.cs" />
    <Compile Remove="Data\LocalGovernmentTypeService.cs" />
    <Compile Remove="Data\NationalAssemblyHalkaService.cs" />
    <Compile Remove="Data\ProvincialAssemblyHalkaService.cs" />
    <Compile Remove="DTO\AdminUnitDTO.cs" />
    <Compile Remove="DTO\AssemblyDTO.cs" />
    <Compile Remove="DTO\ElectionResultDTO.cs" />
    <Compile Remove="DTO\HalkaDTO.cs" />
    <Compile Remove="Migrations\ApplicationDb\20200225151358_remove district from candidate entity.cs" />
    <Compile Remove="Migrations\ApplicationDb\20200225151358_remove district from candidate entity.Designer.cs" />
    <Compile Remove="Migrations\ApplicationDb\20200226084830_replace language and casts id field with text fields.cs" />
    <Compile Remove="Migrations\ApplicationDb\20200226084830_replace language and casts id field with text fields.Designer.cs" />
    <Compile Remove="Migrations\ApplicationDb\20200305152210_Election Asmb Cons.cs" />
    <Compile Remove="Migrations\ApplicationDb\20200305152210_Election Asmb Cons.Designer.cs" />
    <Compile Remove="Migrations\ApplicationDb\20200311130621_rename namanigar to user.cs" />
    <Compile Remove="Migrations\ApplicationDb\20200311130621_rename namanigar to user.Designer.cs" />
    <Compile Remove="Migrations\ApplicationDb\20200704110335_polling station 3.cs" />
    <Compile Remove="Migrations\ApplicationDb\20200704110335_polling station 3.Designer.cs" />
    <Compile Remove="Migrations\ApplicationDb\20200903084627_Previous Structure Mappings.cs" />
    <Compile Remove="Migrations\ApplicationDb\20200903084627_Previous Structure Mappings.Designer.cs" />
    <Compile Remove="Migrations\ApplicationDb\20201006081820_namanigar 2.cs" />
    <Compile Remove="Migrations\ApplicationDb\20201006081820_namanigar 2.Designer.cs" />
    <Compile Remove="Migrations\ApplicationDb\20201006082336_Namanigar 2.cs" />
    <Compile Remove="Migrations\ApplicationDb\20201006082336_Namanigar 2.Designer.cs" />
    <Compile Remove="Model\Assembly.cs" />
    <Compile Remove="Pages\Election\Nominations.razor.cs" />
    <Compile Remove="Pages\Election\SeatWiseNominations.razor.cs" />
    <Compile Remove="Pages\Live\OfflineResults.razor.cs" />
    <Compile Remove="Pages\OldIndex.razor.cs" />
    <Compile Remove="Startup.DevExpress.cs" />
  </ItemGroup>
  <ItemGroup>
    <Content Remove="Pages\AdministrativeUnits2.razor" />
    <Content Remove="Pages\AdminUnits\Divisions.razor" />
    <Content Remove="Pages\AdminUnits\NAs.razor" />
    <Content Remove="Pages\AdminUnits\PAs.razor" />
    <Content Remove="Pages\Election\Namanigars-NotInUse.razor" />
    <Content Remove="Pages\Election\Nominations.razor" />
    <Content Remove="Pages\Election\ProducerResults.razor" />
    <Content Remove="Pages\Election\SearchConstituenies.razor" />
    <Content Remove="Pages\Election\SeatWiseNominations.razor" />
    <Content Remove="Pages\Election\StructureFrom.razor" />
    <Content Remove="Pages\FetchData.razor" />
    <Content Remove="Pages\Live\OfflineResults.razor" />
    <Content Remove="Pages\OldIndex.razor" />
    <Content Remove="Pages\Setup\RefreshGridCmp.razor" />
    <Content Remove="wwwroot\fonts\open-iconic.svg" />
  </ItemGroup>
  <ItemGroup>
    <None Remove="Geoen.mdb" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Blazored.FluentValidation" Version="2.2.0" />
    <PackageReference Include="BlazorInputFile" Version="0.2.0" />
    <PackageReference Include="dapper" Version="2.1.66" />
    <PackageReference Include="EPPlus" Version="8.0.8" />
    <PackageReference Include="FluentValidation.AspNetCore" Version="11.3.1" />
    <PackageReference Include="Microsoft.AspNetCore.Components" Version="9.0.8" />
    <PackageReference Include="Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore" Version="9.0.8" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="9.0.8" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.UI" Version="9.0.8" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR.Client" Version="9.0.8" />
    <PackageReference Include="Microsoft.Data.SqlClient" Version="6.0.2" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.8" />
    <PackageReference Include="microsoft.entityframeworkcore.design" Version="9.0.8">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.8" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.8">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Extensions.Logging.Debug" Version="9.0.8" />
    <PackageReference Include="Microsoft.VisualStudio.Web.CodeGeneration.Design" Version="9.0.0" />
    <PackageReference Include="mudblazor" Version="8.11.0" />
    <PackageReference Include="MudBlazor.ThemeManager" Version="3.0.0" />
    <PackageReference Include="NuGet.Packaging" Version="6.14.0" />
    <PackageReference Include="RestSharp" Version="112.1.0" />
    <PackageReference Include="SixLabors.ImageSharp" Version="3.1.11" />
    <PackageReference Include="Syncfusion.Blazor" Version="30.1.42" />
    <PackageReference Include="Syncfusion.Blazor.Themes" Version="30.1.42" />
    <PackageReference Include="System.Data.SqlClient" Version="4.9.0" />
    <PackageReference Include="System.Formats.Asn1" Version="9.0.8" />
    <PackageReference Include="System.Text.Json" Version="9.0.8" />
  </ItemGroup>
  <ItemGroup>
    <!--<None Include="Pages\Shared\_Layout.cshtml" />-->
    <None Include="Pages\Reports\NominationCount.razor" />
    <None Include="Pages\Shared\_LoginPartial.cshtml" />
    <None Include="Pages\Shared\_ValidationScriptsPartial.cshtml" />
    <None Include="wwwroot\css\bootstrap\bootstrap.min.css.map" />
    <None Include="wwwroot\css\open-iconic\FONT-LICENSE" />
    <None Include="wwwroot\css\open-iconic\font\fonts\open-iconic.svg" />
    <None Include="wwwroot\css\open-iconic\ICON-LICENSE" />
    <None Include="wwwroot\css\open-iconic\README.md" />
    <None Include="wwwroot\fonts\font-awesome-4.7.0\fonts\fontawesome-webfont.svg" />
    <None Include="wwwroot\fonts\font-awesome-4.7.0\fonts\fontawesome-webfont.woff2" />
    <None Include="wwwroot\fonts\font-awesome-4.7.0\less\animated.less" />
    <None Include="wwwroot\fonts\font-awesome-4.7.0\less\bordered-pulled.less" />
    <None Include="wwwroot\fonts\font-awesome-4.7.0\less\core.less" />
    <None Include="wwwroot\fonts\font-awesome-4.7.0\less\fixed-width.less" />
    <None Include="wwwroot\fonts\font-awesome-4.7.0\less\font-awesome.less" />
    <None Include="wwwroot\fonts\font-awesome-4.7.0\less\icons.less" />
    <None Include="wwwroot\fonts\font-awesome-4.7.0\less\larger.less" />
    <None Include="wwwroot\fonts\font-awesome-4.7.0\less\list.less" />
    <None Include="wwwroot\fonts\font-awesome-4.7.0\less\mixins.less" />
    <None Include="wwwroot\fonts\font-awesome-4.7.0\less\path.less" />
    <None Include="wwwroot\fonts\font-awesome-4.7.0\less\rotated-flipped.less" />
    <None Include="wwwroot\fonts\font-awesome-4.7.0\less\screen-reader.less" />
    <None Include="wwwroot\fonts\font-awesome-4.7.0\less\stacked.less" />
    <None Include="wwwroot\fonts\font-awesome-4.7.0\less\variables.less" />
    <None Include="wwwroot\fonts\font-awesome-4.7.0\scss\font-awesome.scss" />
    <None Include="wwwroot\fonts\font-awesome-4.7.0\scss\_animated.scss" />
    <None Include="wwwroot\fonts\font-awesome-4.7.0\scss\_bordered-pulled.scss" />
    <None Include="wwwroot\fonts\font-awesome-4.7.0\scss\_core.scss" />
    <None Include="wwwroot\fonts\font-awesome-4.7.0\scss\_fixed-width.scss" />
    <None Include="wwwroot\fonts\font-awesome-4.7.0\scss\_icons.scss" />
    <None Include="wwwroot\fonts\font-awesome-4.7.0\scss\_larger.scss" />
    <None Include="wwwroot\fonts\font-awesome-4.7.0\scss\_list.scss" />
    <None Include="wwwroot\fonts\font-awesome-4.7.0\scss\_mixins.scss" />
    <None Include="wwwroot\fonts\font-awesome-4.7.0\scss\_path.scss" />
    <None Include="wwwroot\fonts\font-awesome-4.7.0\scss\_rotated-flipped.scss" />
    <None Include="wwwroot\fonts\font-awesome-4.7.0\scss\_screen-reader.scss" />
    <None Include="wwwroot\fonts\font-awesome-4.7.0\scss\_stacked.scss" />
    <None Include="wwwroot\fonts\font-awesome-4.7.0\scss\_variables.scss" />
    <None Include="wwwroot\fonts\Linearicons-Free-v1.0.0\WebFont\Linearicons-Free.svg" />
    <None Include="wwwroot\fonts\Linearicons-Free-v1.0.0\WebFont\Linearicons-Free.woff2" />
    <None Include="wwwroot\js\main.js" />
    <None Include="wwwroot\js\map-custom.js" />
    <None Include="wwwroot\vendor\animsition\js\animsition.js" />
    <None Include="wwwroot\vendor\animsition\js\animsition.min.js" />
    <None Include="wwwroot\vendor\bootstrap\css\bootstrap-grid.css.map" />
    <None Include="wwwroot\vendor\bootstrap\css\bootstrap-grid.min.css.map" />
    <None Include="wwwroot\vendor\bootstrap\css\bootstrap-reboot.css.map" />
    <None Include="wwwroot\vendor\bootstrap\css\bootstrap-reboot.min.css.map" />
    <None Include="wwwroot\vendor\bootstrap\css\bootstrap.css.map" />
    <None Include="wwwroot\vendor\bootstrap\css\bootstrap.min.css.map" />
    <None Include="wwwroot\vendor\bootstrap\js\bootstrap.js" />
    <None Include="wwwroot\vendor\bootstrap\js\bootstrap.min.js" />
    <None Include="wwwroot\vendor\bootstrap\js\popper.js" />
    <None Include="wwwroot\vendor\bootstrap\js\popper.min.js" />
    <None Include="wwwroot\vendor\bootstrap\js\tooltip.js" />
    <None Include="wwwroot\vendor\countdowntime\countdowntime.js" />
    <None Include="wwwroot\vendor\daterangepicker\daterangepicker.js" />
    <None Include="wwwroot\vendor\daterangepicker\moment.js" />
    <None Include="wwwroot\vendor\daterangepicker\moment.min.js" />
    <None Include="wwwroot\vendor\jquery\jquery-3.2.1.min.js" />
    <None Include="wwwroot\vendor\perfect-scrollbar\perfect-scrollbar.min.js" />
    <None Include="wwwroot\vendor\select2\select2.js" />
    <None Include="wwwroot\vendor\select2\select2.min.js" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Model\Namanigar\" />
    <Folder Include="wwwroot\src\" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Remove="Pages\Election\ProducerResults.razor" />
  </ItemGroup>
  <ItemGroup>
    <_ContentIncludedByDefault Remove="Pages\Election\ProducerResults.razor" />
    <_ContentIncludedByDefault Remove="Pages\Reports\NominationCount.razor" />
    <_ContentIncludedByDefault Remove="Pages\Setup\MergeCandidte.razor" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Remove="Pages\Setup\MergeCandidte.razor" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Remove="Pages\Reports\NominationCount.razor" />
  </ItemGroup>
  <!--<ItemGroup>
     <None Include="Pages\Shared\_Layout.cshtml" />
   </ItemGroup>-->
</Project>