﻿// <auto-generated />
using System;
using ElectionAppServer.Model;
using ElectionAppServer.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

namespace ElectionAppServer.Migrations
{
//    [DbContext(typeof(ApplicationDbContext))]
//    partial class ApplicationDbContextModelSnapshot : ModelSnapshot
//    {
//        protected override void BuildModel(ModelBuilder modelBuilder)
//        {
//#pragma warning disable 612, 618
//            modelBuilder
//                .HasAnnotation("ProductVersion", "2.2.0-rtm-35687")
//                .HasAnnotation("Relational:MaxIdentifierLength", 128)
//                .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

//            modelBuilder.Entity("ElectionAppServer.Model.AssemblyType", b =>
//                {
//                    b.Property<int>("Id")
//                        .ValueGeneratedOnAdd()
//                        .HasColumnType("int")
//                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

//                    b.Property<string>("CreatedBy")
//                        .HasColumnType("nvarchar(450)")
//                        .HasMaxLength(450);

//                    b.Property<DateTime>("CreatedDate")
//                        .ValueGeneratedOnAdd()
//                        .HasColumnType("datetime2")
//                        .HasDefaultValueSql("(getdate())");

//                    b.Property<string>("EnglishTitle")
//                        .IsRequired()
//                        .HasColumnType("varchar(200)")
//                        .HasMaxLength(200)
//                        .IsUnicode(false);

//                    b.Property<string>("ModifiedBy")
//                        .HasColumnType("nvarchar(450)")
//                        .HasMaxLength(450);

//                    b.Property<DateTime?>("ModifiedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<string>("UrduTitle")
//                        .IsRequired()
//                        .HasColumnType("nvarchar(200)")
//                        .HasMaxLength(200);

//                    b.HasKey("Id");

//                    b.ToTable("AssemblyType");
//                });

//            modelBuilder.Entity("ElectionAppServer.Model.Candidate", b =>
//                {
//                    b.Property<int>("Id")
//                        .ValueGeneratedOnAdd()
//                        .HasColumnType("int")
//                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

//                    b.Property<int>("CandidateType")
//                        .HasColumnType("int");

//                    b.Property<int?>("CasteId")
//                        .HasColumnType("int");

//                    b.Property<string>("ContactNumber")
//                        .HasColumnType("nvarchar(max)");

//                    b.Property<string>("CreatedBy")
//                        .HasColumnType("nvarchar(450)")
//                        .HasMaxLength(450);

//                    b.Property<DateTime>("CreatedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<DateTime?>("DateOfBirth")
//                        .HasColumnType("datetime2");

//                    b.Property<int?>("DistrictId")
//                        .HasColumnType("int");

//                    b.Property<int?>("EducationId")
//                        .HasColumnType("int");

//                    b.Property<string>("EnglishName")
//                        .IsRequired()
//                        .HasColumnType("nvarchar(100)")
//                        .HasMaxLength(100);

//                    b.Property<string>("FatherEnglishName")
//                        .IsRequired()
//                        .HasColumnType("nvarchar(100)")
//                        .HasMaxLength(100);

//                    b.Property<string>("FatherUrduName")
//                        .IsRequired()
//                        .HasColumnType("nvarchar(100)")
//                        .HasMaxLength(100);

//                    b.Property<int>("Gender")
//                        .HasColumnType("int");

//                    b.Property<bool>("IsFresh")
//                        .HasColumnType("bit");

//                    b.Property<int?>("LanguageId")
//                        .HasColumnType("int");

//                    b.Property<string>("ModifiedBy")
//                        .HasColumnType("nvarchar(450)")
//                        .HasMaxLength(450);

//                    b.Property<DateTime?>("ModifiedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<int?>("ProfessionId")
//                        .HasColumnType("int");

//                    b.Property<string>("TotalAssets")
//                        .HasColumnType("nvarchar(max)");

//                    b.Property<string>("Trivia")
//                        .HasColumnType("nvarchar(2000)")
//                        .HasMaxLength(2000);

//                    b.Property<string>("UrduName")
//                        .IsRequired()
//                        .HasColumnType("nvarchar(100)")
//                        .HasMaxLength(100);

//                    b.HasKey("Id");

//                    b.HasIndex("CasteId");

//                    b.HasIndex("DistrictId");

//                    b.HasIndex("EducationId");

//                    b.HasIndex("LanguageId");

//                    b.HasIndex("ProfessionId");

//                    b.ToTable("Candidates");
//                });

//            modelBuilder.Entity("ElectionAppServer.Model.CandidatePartyAffiliation", b =>
//                {
//                    b.Property<int>("Id")
//                        .ValueGeneratedOnAdd()
//                        .HasColumnType("int")
//                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

//                    b.Property<int>("CandidateId")
//                        .HasColumnType("int");

//                    b.Property<string>("CreatedBy")
//                        .HasColumnType("nvarchar(450)")
//                        .HasMaxLength(450);

//                    b.Property<DateTime>("CreatedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<DateTime>("DateFrom")
//                        .HasColumnType("datetime2");

//                    b.Property<DateTime?>("DateTo")
//                        .HasColumnType("datetime2");

//                    b.Property<string>("ModifiedBy")
//                        .HasColumnType("nvarchar(450)")
//                        .HasMaxLength(450);

//                    b.Property<DateTime?>("ModifiedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<int>("PartyId")
//                        .HasColumnType("int");

//                    b.Property<string>("Position")
//                        .IsRequired()
//                        .HasColumnType("nvarchar(200)")
//                        .HasMaxLength(200);

//                    b.Property<bool>("TillToday")
//                        .HasColumnType("bit");

//                    b.HasKey("Id");

//                    b.HasIndex("CandidateId");

//                    b.HasIndex("PartyId");

//                    b.ToTable("PartyAffiliations");
//                });

//            modelBuilder.Entity("ElectionAppServer.Model.CandidtePoliticalCareer", b =>
//                {
//                    b.Property<int>("Id")
//                        .ValueGeneratedOnAdd()
//                        .HasColumnType("int")
//                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

//                    b.Property<int>("CandidateId")
//                        .HasColumnType("int");

//                    b.Property<string>("CreatedBy")
//                        .HasColumnType("nvarchar(450)")
//                        .HasMaxLength(450);

//                    b.Property<DateTime>("CreatedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<DateTime>("DateFrom")
//                        .HasColumnType("datetime2");

//                    b.Property<DateTime?>("DateTo")
//                        .HasColumnType("datetime2");

//                    b.Property<string>("ModifiedBy")
//                        .HasColumnType("nvarchar(450)")
//                        .HasMaxLength(450);

//                    b.Property<DateTime?>("ModifiedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<string>("Position")
//                        .HasColumnType("nvarchar(max)");

//                    b.Property<bool>("TillToday")
//                        .HasColumnType("bit");

//                    b.HasKey("Id");

//                    b.HasIndex("CandidateId");

//                    b.ToTable("PoliticalCareers");
//                });

//            modelBuilder.Entity("ElectionAppServer.Model.Caste", b =>
//                {
//                    b.Property<int>("Id")
//                        .ValueGeneratedOnAdd()
//                        .HasColumnType("int")
//                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

//                    b.Property<string>("CreatedBy")
//                        .HasColumnType("nvarchar(450)")
//                        .HasMaxLength(450);

//                    b.Property<DateTime>("CreatedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<string>("EnglishTitle")
//                        .IsRequired()
//                        .HasColumnType("nvarchar(200)")
//                        .HasMaxLength(200);

//                    b.Property<string>("ModifiedBy")
//                        .HasColumnType("nvarchar(450)")
//                        .HasMaxLength(450);

//                    b.Property<DateTime?>("ModifiedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<string>("UrduTitle")
//                        .IsRequired()
//                        .HasColumnType("nvarchar(200)")
//                        .HasMaxLength(200);

//                    b.HasKey("Id");

//                    b.ToTable("Castes");
//                });

//            modelBuilder.Entity("ElectionAppServer.Model.Education", b =>
//                {
//                    b.Property<int>("Id")
//                        .ValueGeneratedOnAdd()
//                        .HasColumnType("int")
//                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

//                    b.Property<string>("CreatedBy")
//                        .HasColumnType("nvarchar(450)")
//                        .HasMaxLength(450);

//                    b.Property<DateTime>("CreatedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<string>("EnglishTitle")
//                        .IsRequired()
//                        .HasColumnType("nvarchar(200)")
//                        .HasMaxLength(200);

//                    b.Property<string>("ModifiedBy")
//                        .HasColumnType("nvarchar(450)")
//                        .HasMaxLength(450);

//                    b.Property<DateTime?>("ModifiedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<string>("UrduTitle")
//                        .IsRequired()
//                        .HasColumnType("nvarchar(200)")
//                        .HasMaxLength(200);

//                    b.HasKey("Id");

//                    b.ToTable("Educations");
//                });

//            modelBuilder.Entity("ElectionAppServer.Model.Election", b =>
//                {
//                    b.Property<int>("Id")
//                        .ValueGeneratedOnAdd()
//                        .HasColumnType("int")
//                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

//                    b.Property<string>("CreatedBy")
//                        .HasColumnType("nvarchar(450)")
//                        .HasMaxLength(450);

//                    b.Property<DateTime>("CreatedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<string>("Description")
//                        .HasColumnType("nvarchar(1000)")
//                        .HasMaxLength(1000);

//                    b.Property<string>("EnglishTitle")
//                        .IsRequired()
//                        .HasColumnType("nvarchar(200)")
//                        .HasMaxLength(200);

//                    b.Property<string>("ModifiedBy")
//                        .HasColumnType("nvarchar(450)")
//                        .HasMaxLength(450);

//                    b.Property<DateTime?>("ModifiedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<string>("UrduTitle")
//                        .IsRequired()
//                        .HasColumnType("nvarchar(200)")
//                        .HasMaxLength(200);

//                    b.Property<int>("YearFrom")
//                        .HasColumnType("int");

//                    b.Property<int>("YearTo")
//                        .HasColumnType("int");

//                    b.HasKey("Id");

//                    b.ToTable("Elections");
//                });

//            modelBuilder.Entity("ElectionAppServer.Model.ElectionAssembly", b =>
//                {
//                    b.Property<int>("Id")
//                        .ValueGeneratedOnAdd()
//                        .HasColumnType("int")
//                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

//                    b.Property<int>("AssemblyTypeId")
//                        .HasColumnType("int");

//                    b.Property<string>("CreatedBy")
//                        .HasColumnType("nvarchar(450)")
//                        .HasMaxLength(450);

//                    b.Property<DateTime>("CreatedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<int?>("ElectionAssemblyType")
//                        .HasColumnType("int");

//                    b.Property<int>("ElectionId")
//                        .HasColumnType("int");

//                    b.Property<string>("EnglishTitle")
//                        .IsRequired()
//                        .HasColumnType("nvarchar(200)")
//                        .HasMaxLength(200);

//                    b.Property<string>("ModifiedBy")
//                        .HasColumnType("nvarchar(450)")
//                        .HasMaxLength(450);

//                    b.Property<DateTime?>("ModifiedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<string>("UrduTitle")
//                        .IsRequired()
//                        .HasColumnType("nvarchar(200)")
//                        .HasMaxLength(200);

//                    b.HasKey("Id");

//                    b.HasIndex("AssemblyTypeId");

//                    b.HasIndex("ElectionId");

//                    b.ToTable("ElectionAssemblies");
//                });

//            modelBuilder.Entity("ElectionAppServer.Model.ElectionAssemblyConstituency", b =>
//                {
//                    b.Property<int>("ElectionAssemblyId")
//                        .HasColumnType("int");

//                    b.Property<int>("StructureId")
//                        .HasColumnType("int");

//                    b.Property<string>("Code")
//                        .HasColumnType("nvarchar(max)");

//                    b.Property<string>("CreatedBy")
//                        .HasColumnType("nvarchar(450)")
//                        .HasMaxLength(450);

//                    b.Property<DateTime>("CreatedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<string>("ModifiedBy")
//                        .HasColumnType("nvarchar(450)")
//                        .HasMaxLength(450);

//                    b.Property<DateTime?>("ModifiedDate")
//                        .HasColumnType("datetime2");

//                    b.HasKey("ElectionAssemblyId", "StructureId");

//                    b.HasIndex("StructureId");

//                    b.ToTable("ElectionAssemblyConstituencies");
//                });

//            modelBuilder.Entity("ElectionAppServer.Model.ElectionAssemblySeat", b =>
//                {
//                    b.Property<int>("ElectionAssemblyId")
//                        .HasColumnType("int");

//                    b.Property<int>("SeatTypeId")
//                        .HasColumnType("int");

//                    b.Property<string>("CreatedBy")
//                        .HasColumnType("nvarchar(450)")
//                        .HasMaxLength(450);

//                    b.Property<DateTime>("CreatedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<string>("ModifiedBy")
//                        .HasColumnType("nvarchar(450)")
//                        .HasMaxLength(450);

//                    b.Property<DateTime?>("ModifiedDate")
//                        .HasColumnType("datetime2");

//                    b.HasKey("ElectionAssemblyId", "SeatTypeId");

//                    b.HasIndex("SeatTypeId");

//                    b.ToTable("ElectionAssemblySeats");
//                });

//            modelBuilder.Entity("ElectionAppServer.Model.ElectionPhase", b =>
//                {
//                    b.Property<int>("Id")
//                        .ValueGeneratedOnAdd()
//                        .HasColumnType("int")
//                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

//                    b.Property<string>("CreatedBy")
//                        .HasColumnType("nvarchar(450)")
//                        .HasMaxLength(450);

//                    b.Property<DateTime>("CreatedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<int>("ElectionId")
//                        .HasColumnType("int");

//                    b.Property<string>("ModifiedBy")
//                        .HasColumnType("nvarchar(450)")
//                        .HasMaxLength(450);

//                    b.Property<DateTime?>("ModifiedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<DateTime>("StartDate")
//                        .HasColumnType("datetime2");

//                    b.Property<string>("Title")
//                        .HasColumnType("nvarchar(max)");

//                    b.HasKey("Id");

//                    b.HasIndex("ElectionId");

//                    b.ToTable("ElectionPhases");
//                });

//            modelBuilder.Entity("ElectionAppServer.Model.Language", b =>
//                {
//                    b.Property<int>("Id")
//                        .ValueGeneratedOnAdd()
//                        .HasColumnType("int")
//                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

//                    b.Property<string>("CreatedBy")
//                        .HasColumnType("nvarchar(450)")
//                        .HasMaxLength(450);

//                    b.Property<DateTime>("CreatedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<string>("EnglishTitle")
//                        .IsRequired()
//                        .HasColumnType("nvarchar(200)")
//                        .HasMaxLength(200);

//                    b.Property<string>("ModifiedBy")
//                        .HasColumnType("nvarchar(450)")
//                        .HasMaxLength(450);

//                    b.Property<DateTime?>("ModifiedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<string>("UrduTitle")
//                        .IsRequired()
//                        .HasColumnType("nvarchar(200)")
//                        .HasMaxLength(200);

//                    b.HasKey("Id");

//                    b.ToTable("Languages");
//                });

//            modelBuilder.Entity("ElectionAppServer.Model.Namanigar", b =>
//                {
//                    b.Property<int>("Id")
//                        .ValueGeneratedOnAdd()
//                        .HasColumnType("int")
//                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

//                    b.Property<string>("Code")
//                        .HasColumnType("nvarchar(max)");

//                    b.Property<string>("CreatedBy")
//                        .HasColumnType("nvarchar(450)")
//                        .HasMaxLength(450);

//                    b.Property<DateTime>("CreatedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<int>("DistrictId")
//                        .HasColumnType("int");

//                    b.Property<int>("ElectionId")
//                        .HasColumnType("int");

//                    b.Property<string>("Email")
//                        .HasColumnType("nvarchar(250)")
//                        .HasMaxLength(250);

//                    b.Property<string>("Mobile")
//                        .HasColumnType("nvarchar(50)")
//                        .HasMaxLength(50);

//                    b.Property<string>("ModifiedBy")
//                        .HasColumnType("nvarchar(450)")
//                        .HasMaxLength(450);

//                    b.Property<DateTime?>("ModifiedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<string>("Name")
//                        .HasColumnType("nvarchar(max)");

//                    b.HasKey("Id");

//                    b.HasIndex("DistrictId");

//                    b.HasIndex("ElectionId");

//                    b.ToTable("Namanigars");
//                });

//            modelBuilder.Entity("ElectionAppServer.Model.NamnigarConstituency", b =>
//                {
//                    b.Property<int>("ElectionAssemblyId")
//                        .HasColumnType("int");

//                    b.Property<int>("NamanigarId")
//                        .HasColumnType("int");

//                    b.Property<int>("StructureId")
//                        .HasColumnType("int");

//                    b.HasKey("ElectionAssemblyId", "NamanigarId", "StructureId");

//                    b.HasIndex("NamanigarId");

//                    b.HasIndex("StructureId");

//                    b.ToTable("NamnigarConstituencies");
//                });

//            modelBuilder.Entity("ElectionAppServer.Model.Nomination", b =>
//                {
//                    b.Property<int>("Id")
//                        .ValueGeneratedOnAdd()
//                        .HasColumnType("int")
//                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

//                    b.Property<int>("CandidteId")
//                        .HasColumnType("int");

//                    b.Property<string>("CreatedBy")
//                        .HasColumnType("nvarchar(450)")
//                        .HasMaxLength(450);

//                    b.Property<DateTime>("CreatedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<int>("ElectionAssemblyId")
//                        .HasColumnType("int");

//                    b.Property<int>("ElectionPhaseId")
//                        .HasColumnType("int");

//                    b.Property<string>("ModifiedBy")
//                        .HasColumnType("nvarchar(450)")
//                        .HasMaxLength(450);

//                    b.Property<DateTime?>("ModifiedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<int>("PartyId")
//                        .HasColumnType("int");

//                    b.Property<int>("SeatTypeId")
//                        .HasColumnType("int");

//                    b.Property<int>("StructureId")
//                        .HasColumnType("int");

//                    b.Property<int>("SymbolId")
//                        .HasColumnType("int");

//                    b.Property<int?>("Votes")
//                        .HasColumnType("int");

//                    b.Property<int>("Weight")
//                        .HasColumnType("int");

//                    b.HasKey("Id");

//                    b.HasIndex("CandidteId");

//                    b.HasIndex("ElectionPhaseId");

//                    b.HasIndex("PartyId");

//                    b.HasIndex("SeatTypeId");

//                    b.HasIndex("StructureId");

//                    b.HasIndex("SymbolId");

//                    b.HasIndex("ElectionAssemblyId", "StructureId", "SeatTypeId", "CandidteId", "ElectionPhaseId")
//                        .IsUnique();

//                    b.ToTable("Nominations");
//                });

//            modelBuilder.Entity("ElectionAppServer.Model.Party", b =>
//                {
//                    b.Property<int>("Id")
//                        .ValueGeneratedOnAdd()
//                        .HasColumnType("int")
//                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

//                    b.Property<string>("Address")
//                        .HasColumnType("nvarchar(500)")
//                        .HasMaxLength(500);

//                    b.Property<string>("CreatedBy")
//                        .HasColumnType("nvarchar(450)")
//                        .HasMaxLength(450);

//                    b.Property<DateTime>("CreatedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<DateTime>("DateOfCreation")
//                        .HasColumnType("datetime2");

//                    b.Property<string>("Designation")
//                        .HasColumnType("nvarchar(200)")
//                        .HasMaxLength(200);

//                    b.Property<string>("EnglishTitle")
//                        .HasColumnType("nvarchar(200)")
//                        .HasMaxLength(200);

//                    b.Property<string>("Leader")
//                        .HasColumnType("nvarchar(200)")
//                        .HasMaxLength(200);

//                    b.Property<string>("ModifiedBy")
//                        .HasColumnType("nvarchar(450)")
//                        .HasMaxLength(450);

//                    b.Property<DateTime?>("ModifiedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<string>("ShortEnglishTitle")
//                        .HasColumnType("nvarchar(50)")
//                        .HasMaxLength(50);

//                    b.Property<string>("ShortUrduTitle")
//                        .HasColumnType("nvarchar(50)")
//                        .HasMaxLength(50);

//                    b.Property<int?>("SymbolId")
//                        .HasColumnType("int");

//                    b.Property<string>("Trivia")
//                        .HasColumnType("nvarchar(1000)")
//                        .HasMaxLength(1000);

//                    b.Property<string>("UrduTitle")
//                        .HasColumnType("nvarchar(200)")
//                        .HasMaxLength(200);

//                    b.HasKey("Id");

//                    b.HasIndex("SymbolId");

//                    b.ToTable("Parties");
//                });

//            modelBuilder.Entity("ElectionAppServer.Model.Profession", b =>
//                {
//                    b.Property<int>("Id")
//                        .ValueGeneratedOnAdd()
//                        .HasColumnType("int")
//                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

//                    b.Property<string>("CreatedBy")
//                        .HasColumnType("nvarchar(450)")
//                        .HasMaxLength(450);

//                    b.Property<DateTime>("CreatedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<string>("EnglishTitle")
//                        .IsRequired()
//                        .HasColumnType("nvarchar(200)")
//                        .HasMaxLength(200);

//                    b.Property<string>("ModifiedBy")
//                        .HasColumnType("nvarchar(450)")
//                        .HasMaxLength(450);

//                    b.Property<DateTime?>("ModifiedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<string>("UrduTitle")
//                        .IsRequired()
//                        .HasColumnType("nvarchar(200)")
//                        .HasMaxLength(200);

//                    b.HasKey("Id");

//                    b.ToTable("Professions");
//                });

//            modelBuilder.Entity("ElectionAppServer.Model.Reporter", b =>
//                {
//                    b.Property<int>("Id")
//                        .ValueGeneratedOnAdd()
//                        .HasColumnType("int")
//                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

//                    b.Property<string>("CreatedBy")
//                        .HasColumnType("nvarchar(450)")
//                        .HasMaxLength(450);

//                    b.Property<DateTime>("CreatedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<string>("Email")
//                        .HasColumnType("nvarchar(max)");

//                    b.Property<bool>("IsActive")
//                        .HasColumnType("bit");

//                    b.Property<string>("ModifiedBy")
//                        .HasColumnType("nvarchar(450)")
//                        .HasMaxLength(450);

//                    b.Property<DateTime?>("ModifiedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<string>("Name")
//                        .HasColumnType("nvarchar(max)");

//                    b.Property<string>("Phone")
//                        .HasColumnType("nvarchar(max)");

//                    b.HasKey("Id");

//                    b.ToTable("Reporters");
//                });

//            modelBuilder.Entity("ElectionAppServer.Model.SeatType", b =>
//                {
//                    b.Property<int>("Id")
//                        .ValueGeneratedOnAdd()
//                        .HasColumnType("int")
//                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

//                    b.Property<string>("CreatedBy")
//                        .HasColumnType("nvarchar(450)")
//                        .HasMaxLength(450);

//                    b.Property<DateTime>("CreatedDate")
//                        .ValueGeneratedOnAdd()
//                        .HasColumnType("datetime2")
//                        .HasDefaultValueSql("(getdate())");

//                    b.Property<string>("EnglishTitle")
//                        .IsRequired()
//                        .HasColumnType("varchar(200)")
//                        .HasMaxLength(200)
//                        .IsUnicode(false);

//                    b.Property<string>("ModifiedBy")
//                        .HasColumnType("nvarchar(450)")
//                        .HasMaxLength(450);

//                    b.Property<DateTime?>("ModifiedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<string>("UrduTitle")
//                        .IsRequired()
//                        .HasColumnType("nvarchar(200)")
//                        .HasMaxLength(200);

//                    b.HasKey("Id");

//                    b.ToTable("SeatTypes");
//                });

//            modelBuilder.Entity("ElectionAppServer.Model.Structure", b =>
//                {
//                    b.Property<int>("Id")
//                        .ValueGeneratedOnAdd()
//                        .HasColumnType("int")
//                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

//                    b.Property<int?>("CasteId")
//                        .HasColumnType("int");

//                    b.Property<string>("Code")
//                        .HasColumnType("varchar(20)")
//                        .HasMaxLength(20)
//                        .IsUnicode(false);

//                    b.Property<string>("CreatedBy")
//                        .HasColumnType("nvarchar(450)")
//                        .HasMaxLength(450);

//                    b.Property<DateTime>("CreatedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<string>("Discriminator")
//                        .IsRequired()
//                        .HasColumnType("nvarchar(max)");

//                    b.Property<int>("ElectionId")
//                        .HasColumnType("int");

//                    b.Property<string>("EnglishTitle")
//                        .IsRequired()
//                        .HasColumnType("varchar(200)")
//                        .HasMaxLength(200)
//                        .IsUnicode(false);

//                    b.Property<int?>("FemaleVoters")
//                        .HasColumnType("int");

//                    b.Property<string>("ImportantAreas")
//                        .HasColumnType("nvarchar(500)")
//                        .HasMaxLength(500);

//                    b.Property<string>("ImportantPoliticalPersonalities")
//                        .HasColumnType("nvarchar(500)")
//                        .HasMaxLength(500);

//                    b.Property<bool>("IsSeat")
//                        .HasColumnType("bit");

//                    b.Property<int?>("LanguageId")
//                        .HasColumnType("int");

//                    b.Property<int?>("MaleVoters")
//                        .HasColumnType("int");

//                    b.Property<string>("ModifiedBy")
//                        .HasColumnType("nvarchar(450)")
//                        .HasMaxLength(450);

//                    b.Property<DateTime?>("ModifiedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<string>("Problems")
//                        .HasColumnType("nvarchar(500)")
//                        .HasMaxLength(500);

//                    b.Property<float?>("RuralAreaPer")
//                        .HasColumnType("real");

//                    b.Property<int?>("TotalPollingStations")
//                        .HasColumnType("int");

//                    b.Property<string>("Trivia")
//                        .HasColumnType("nvarchar(2000)")
//                        .HasMaxLength(2000);

//                    b.Property<float?>("UrbanAreaPer")
//                        .HasColumnType("real");

//                    b.Property<string>("UrduTitle")
//                        .IsRequired()
//                        .HasColumnType("nvarchar(200)")
//                        .HasMaxLength(200);

//                    b.HasKey("Id");

//                    b.HasIndex("CasteId");

//                    b.HasIndex("ElectionId");

//                    b.HasIndex("LanguageId");

//                    b.ToTable("Structures");

//                    b.HasDiscriminator<string>("Discriminator").HasValue("Structure");
//                });

//            modelBuilder.Entity("ElectionAppServer.Model.Symbol", b =>
//                {
//                    b.Property<int>("Id")
//                        .ValueGeneratedOnAdd()
//                        .HasColumnType("int")
//                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

//                    b.Property<int>("Code")
//                        .HasColumnType("int");

//                    b.Property<string>("CreatedBy")
//                        .HasColumnType("nvarchar(450)")
//                        .HasMaxLength(450);

//                    b.Property<DateTime>("CreatedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<string>("EnglishTitle")
//                        .IsRequired()
//                        .HasColumnType("nvarchar(200)")
//                        .HasMaxLength(200);

//                    b.Property<string>("ModifiedBy")
//                        .HasColumnType("nvarchar(450)")
//                        .HasMaxLength(450);

//                    b.Property<DateTime?>("ModifiedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<string>("UrduTitle")
//                        .IsRequired()
//                        .HasColumnType("nvarchar(200)")
//                        .HasMaxLength(200);

//                    b.HasKey("Id");

//                    b.ToTable("Symbols");
//                });

//            modelBuilder.Entity("ElectionAppServer.Model.District", b =>
//                {
//                    b.HasBaseType("ElectionAppServer.Model.Structure");

//                    b.Property<int>("DivisionId")
//                        .HasColumnType("int");

//                    b.HasIndex("DivisionId");

//                    b.HasDiscriminator().HasValue("District");
//                });

//            modelBuilder.Entity("ElectionAppServer.Model.Division", b =>
//                {
//                    b.HasBaseType("ElectionAppServer.Model.Structure");

//                    b.Property<int>("ProvinceId")
//                        .HasColumnType("int");

//                    b.HasIndex("ProvinceId");

//                    b.HasDiscriminator().HasValue("Division");
//                });

//            modelBuilder.Entity("ElectionAppServer.Model.NationalAssemblyHalka", b =>
//                {
//                    b.HasBaseType("ElectionAppServer.Model.Structure");

//                    b.Property<int>("StructureId")
//                        .HasColumnType("int");

//                    b.HasIndex("StructureId");

//                    b.HasDiscriminator().HasValue("NationalAssemblyHalka");
//                });

//            modelBuilder.Entity("ElectionAppServer.Model.Province", b =>
//                {
//                    b.HasBaseType("ElectionAppServer.Model.Structure");

//                    b.HasDiscriminator().HasValue("Province");
//                });

//            modelBuilder.Entity("ElectionAppServer.Model.ProvincialAssemblyHalka", b =>
//                {
//                    b.HasBaseType("ElectionAppServer.Model.Structure");

//                    b.Property<int>("StructureId")
//                        .HasColumnName("ProvincialAssemblyHalka_StructureId")
//                        .HasColumnType("int");

//                    b.HasIndex("StructureId");

//                    b.HasDiscriminator().HasValue("ProvincialAssemblyHalka");
//                });

//            modelBuilder.Entity("ElectionAppServer.Model.Town", b =>
//                {
//                    b.HasBaseType("ElectionAppServer.Model.Structure");

//                    b.Property<int>("DistrictId")
//                        .HasColumnType("int");

//                    b.HasIndex("DistrictId");

//                    b.HasDiscriminator().HasValue("Town");
//                });

//            modelBuilder.Entity("ElectionAppServer.Model.UnionCouncil", b =>
//                {
//                    b.HasBaseType("ElectionAppServer.Model.Structure");

//                    b.Property<int>("TownId")
//                        .HasColumnType("int");

//                    b.HasIndex("TownId");

//                    b.HasIndex("Code", "TownId")
//                        .IsUnique()
//                        .HasFilter("[Code] IS NOT NULL AND [TownId] IS NOT NULL");

//                    b.HasIndex("EnglishTitle", "TownId")
//                        .IsUnique()
//                        .HasFilter("[TownId] IS NOT NULL");

//                    b.HasIndex("UrduTitle", "TownId")
//                        .IsUnique()
//                        .HasFilter("[TownId] IS NOT NULL");

//                    b.HasDiscriminator().HasValue("UnionCouncil");
//                });

//            modelBuilder.Entity("ElectionAppServer.Model.Ward", b =>
//                {
//                    b.HasBaseType("ElectionAppServer.Model.Structure");

//                    b.Property<int>("UnionCouncilId")
//                        .HasColumnType("int");

//                    b.HasIndex("UnionCouncilId");

//                    b.HasDiscriminator().HasValue("Ward");
//                });

//            modelBuilder.Entity("ElectionAppServer.Model.Candidate", b =>
//                {
//                    b.HasOne("ElectionAppServer.Model.Caste", "Caste")
//                        .WithMany("Candidates")
//                        .HasForeignKey("CasteId");

//                    b.HasOne("ElectionAppServer.Model.District", "District")
//                        .WithMany()
//                        .HasForeignKey("DistrictId");

//                    b.HasOne("ElectionAppServer.Model.Education", "Education")
//                        .WithMany("Candidates")
//                        .HasForeignKey("EducationId");

//                    b.HasOne("ElectionAppServer.Model.Language", "Language")
//                        .WithMany("Candidates")
//                        .HasForeignKey("LanguageId");

//                    b.HasOne("ElectionAppServer.Model.Profession", "Profession")
//                        .WithMany("Candidates")
//                        .HasForeignKey("ProfessionId");
//                });

//            modelBuilder.Entity("ElectionAppServer.Model.CandidatePartyAffiliation", b =>
//                {
//                    b.HasOne("ElectionAppServer.Model.Candidate", "Candidate")
//                        .WithMany("PartyAffiliations")
//                        .HasForeignKey("CandidateId")
//                        .OnDelete(DeleteBehavior.Cascade)
//                        .IsRequired();

//                    b.HasOne("ElectionAppServer.Model.Party", "Party")
//                        .WithMany()
//                        .HasForeignKey("PartyId")
//                        .OnDelete(DeleteBehavior.Cascade)
//                        .IsRequired();
//                });

//            modelBuilder.Entity("ElectionAppServer.Model.CandidtePoliticalCareer", b =>
//                {
//                    b.HasOne("ElectionAppServer.Model.Candidate", "Candidate")
//                        .WithMany("CandidtePoliticalCareers")
//                        .HasForeignKey("CandidateId")
//                        .OnDelete(DeleteBehavior.Cascade)
//                        .IsRequired();
//                });

//            modelBuilder.Entity("ElectionAppServer.Model.ElectionAssembly", b =>
//                {
//                    b.HasOne("ElectionAppServer.Model.AssemblyType", "AssemblyType")
//                        .WithMany("ElectionAssemblies")
//                        .HasForeignKey("AssemblyTypeId")
//                        .OnDelete(DeleteBehavior.Restrict)
//                        .IsRequired();

//                    b.HasOne("ElectionAppServer.Model.Election", "Election")
//                        .WithMany()
//                        .HasForeignKey("ElectionId")
//                        .OnDelete(DeleteBehavior.Cascade)
//                        .IsRequired();
//                });

//            modelBuilder.Entity("ElectionAppServer.Model.ElectionAssemblyConstituency", b =>
//                {
//                    b.HasOne("ElectionAppServer.Model.ElectionAssembly", "ElectionAssembly")
//                        .WithMany()
//                        .HasForeignKey("ElectionAssemblyId")
//                        .OnDelete(DeleteBehavior.Cascade)
//                        .IsRequired();

//                    b.HasOne("ElectionAppServer.Model.Structure", "Structure")
//                        .WithMany("ElectionAssemblyConstituencies")
//                        .HasForeignKey("StructureId")
//                        .OnDelete(DeleteBehavior.Restrict)
//                        .IsRequired();
//                });

//            modelBuilder.Entity("ElectionAppServer.Model.ElectionAssemblySeat", b =>
//                {
//                    b.HasOne("ElectionAppServer.Model.ElectionAssembly", "ElectionAssembly")
//                        .WithMany()
//                        .HasForeignKey("ElectionAssemblyId")
//                        .OnDelete(DeleteBehavior.Cascade)
//                        .IsRequired();

//                    b.HasOne("ElectionAppServer.Model.SeatType", "SeatType")
//                        .WithMany()
//                        .HasForeignKey("SeatTypeId")
//                        .OnDelete(DeleteBehavior.Cascade)
//                        .IsRequired();
//                });

//            modelBuilder.Entity("ElectionAppServer.Model.ElectionPhase", b =>
//                {
//                    b.HasOne("ElectionAppServer.Model.Election", "Election")
//                        .WithMany("ElectionPhases")
//                        .HasForeignKey("ElectionId")
//                        .OnDelete(DeleteBehavior.Cascade)
//                        .IsRequired();
//                });

//            modelBuilder.Entity("ElectionAppServer.Model.Namanigar", b =>
//                {
//                    b.HasOne("ElectionAppServer.Model.District", "District")
//                        .WithMany("Namanigars")
//                        .HasForeignKey("DistrictId")
//                        .OnDelete(DeleteBehavior.Restrict)
//                        .IsRequired();

//                    b.HasOne("ElectionAppServer.Model.Election", "Election")
//                        .WithMany("Namanigars")
//                        .HasForeignKey("ElectionId")
//                        .OnDelete(DeleteBehavior.Restrict)
//                        .IsRequired();
//                });

//            modelBuilder.Entity("ElectionAppServer.Model.NamnigarConstituency", b =>
//                {
//                    b.HasOne("ElectionAppServer.Model.ElectionAssembly", "ElectionAssembly")
//                        .WithMany("NamnigarConstituencies")
//                        .HasForeignKey("ElectionAssemblyId")
//                        .OnDelete(DeleteBehavior.Restrict)
//                        .IsRequired();

//                    b.HasOne("ElectionAppServer.Model.Namanigar", "Namanigar")
//                        .WithMany("NamnigarConstituencies")
//                        .HasForeignKey("NamanigarId")
//                        .OnDelete(DeleteBehavior.Restrict)
//                        .IsRequired();

//                    b.HasOne("ElectionAppServer.Model.Structure", "Structure")
//                        .WithMany("NamnigarConstituencies")
//                        .HasForeignKey("StructureId")
//                        .OnDelete(DeleteBehavior.Restrict)
//                        .IsRequired();
//                });

//            modelBuilder.Entity("ElectionAppServer.Model.Nomination", b =>
//                {
//                    b.HasOne("ElectionAppServer.Model.Candidate", "Candidate")
//                        .WithMany("Nominations")
//                        .HasForeignKey("CandidteId")
//                        .OnDelete(DeleteBehavior.Restrict)
//                        .IsRequired();

//                    b.HasOne("ElectionAppServer.Model.ElectionAssembly", "ElectionAssembly")
//                        .WithMany("Nominations")
//                        .HasForeignKey("ElectionAssemblyId")
//                        .OnDelete(DeleteBehavior.Restrict)
//                        .IsRequired();

//                    b.HasOne("ElectionAppServer.Model.ElectionPhase", "ElectionPhase")
//                        .WithMany()
//                        .HasForeignKey("ElectionPhaseId")
//                        .OnDelete(DeleteBehavior.Cascade)
//                        .IsRequired();

//                    b.HasOne("ElectionAppServer.Model.Party", "Party")
//                        .WithMany("Nominations")
//                        .HasForeignKey("PartyId")
//                        .OnDelete(DeleteBehavior.Restrict)
//                        .IsRequired();

//                    b.HasOne("ElectionAppServer.Model.SeatType", "SeatType")
//                        .WithMany("Nominations")
//                        .HasForeignKey("SeatTypeId")
//                        .OnDelete(DeleteBehavior.Restrict)
//                        .IsRequired();

//                    b.HasOne("ElectionAppServer.Model.Structure", "Structure")
//                        .WithMany("Nominations")
//                        .HasForeignKey("StructureId")
//                        .OnDelete(DeleteBehavior.Restrict)
//                        .IsRequired();

//                    b.HasOne("ElectionAppServer.Model.Symbol", "Symbol")
//                        .WithMany("Nominations")
//                        .HasForeignKey("SymbolId")
//                        .OnDelete(DeleteBehavior.Restrict)
//                        .IsRequired();
//                });

//            modelBuilder.Entity("ElectionAppServer.Model.Party", b =>
//                {
//                    b.HasOne("ElectionAppServer.Model.Symbol", "Symbol")
//                        .WithMany("Parties")
//                        .HasForeignKey("SymbolId");
//                });

//            modelBuilder.Entity("ElectionAppServer.Model.Structure", b =>
//                {
//                    b.HasOne("ElectionAppServer.Model.Caste", "Caste")
//                        .WithMany()
//                        .HasForeignKey("CasteId");

//                    b.HasOne("ElectionAppServer.Model.Election", "Election")
//                        .WithMany("Structures")
//                        .HasForeignKey("ElectionId")
//                        .OnDelete(DeleteBehavior.Restrict)
//                        .IsRequired();

//                    b.HasOne("ElectionAppServer.Model.Language", "Language")
//                        .WithMany()
//                        .HasForeignKey("LanguageId");
//                });

//            modelBuilder.Entity("ElectionAppServer.Model.District", b =>
//                {
//                    b.HasOne("ElectionAppServer.Model.Division", "Division")
//                        .WithMany("Districts")
//                        .HasForeignKey("DivisionId")
//                        .HasConstraintName("FK_Districts_Divisions")
//                        .OnDelete(DeleteBehavior.Restrict)
//                        .IsRequired();
//                });

//            modelBuilder.Entity("ElectionAppServer.Model.Division", b =>
//                {
//                    b.HasOne("ElectionAppServer.Model.Province", "Province")
//                        .WithMany("Divisions")
//                        .HasForeignKey("ProvinceId")
//                        .HasConstraintName("FK_Divisions_Provinces")
//                        .OnDelete(DeleteBehavior.Restrict)
//                        .IsRequired();
//                });

//            modelBuilder.Entity("ElectionAppServer.Model.NationalAssemblyHalka", b =>
//                {
//                    b.HasOne("ElectionAppServer.Model.Structure", "Structure")
//                        .WithMany("NationalAssemblyHalkas")
//                        .HasForeignKey("StructureId")
//                        .OnDelete(DeleteBehavior.Restrict)
//                        .IsRequired();
//                });

//            modelBuilder.Entity("ElectionAppServer.Model.ProvincialAssemblyHalka", b =>
//                {
//                    b.HasOne("ElectionAppServer.Model.Structure", "Structure")
//                        .WithMany("ProvincialAssemblyHalkas")
//                        .HasForeignKey("StructureId")
//                        .OnDelete(DeleteBehavior.Restrict)
//                        .IsRequired();
//                });

//            modelBuilder.Entity("ElectionAppServer.Model.Town", b =>
//                {
//                    b.HasOne("ElectionAppServer.Model.District", "District")
//                        .WithMany("Towns")
//                        .HasForeignKey("DistrictId")
//                        .OnDelete(DeleteBehavior.Restrict)
//                        .IsRequired();
//                });

//            modelBuilder.Entity("ElectionAppServer.Model.UnionCouncil", b =>
//                {
//                    b.HasOne("ElectionAppServer.Model.Town", "Town")
//                        .WithMany("UnionCouncils")
//                        .HasForeignKey("TownId")
//                        .OnDelete(DeleteBehavior.Restrict)
//                        .IsRequired();
//                });

//            modelBuilder.Entity("ElectionAppServer.Model.Ward", b =>
//                {
//                    b.HasOne("ElectionAppServer.Model.UnionCouncil", "UnionCouncil")
//                        .WithMany("Wards")
//                        .HasForeignKey("UnionCouncilId")
//                        .OnDelete(DeleteBehavior.Restrict)
//                        .IsRequired();
//                });
//#pragma warning restore 612, 618
//        }
//    }
}
