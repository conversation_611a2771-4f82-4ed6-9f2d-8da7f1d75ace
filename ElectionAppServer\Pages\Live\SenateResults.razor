﻿@page "/senateresults"
@inject NavigationManager NavigationManager
@inject UserManager<ApplicationUser> _UserManager
@inject RoleManager<IdentityRole> _RoleManager
@inject AuthenticationStateProvider AuthenticationStateProvider
@inherits OwningComponentBase<ElectionResultsDataService>
@*<PageCaption Title="Live Results"></PageCaption>*@
<MudText Typo="Typo.h5">Live Results</MudText>

<section>
    <table class="table table-sm">
        <thead class="thead-dark">
        <tr>
            @*<th>Assembly</th>*@
            <th>Constituency</th>
            <th>Winner</th>
            <th>Winner Party</th>
            <th>Updated Time</th>
        </tr>
        </thead>
        <tbody>
        @foreach (var r in results)
        {
            <tr>
                <td>@r.Constituency</td>
                <td>@r.WinnerName</td>
                <td>@r.WinnerParty</td>
                <td>@r.LastUpdatedOn.ToString("d MMM, yyyy h:mm:ss")</td>
            </tr>
        }
        </tbody>
    </table>
</section>

@code {
    private HubConnection hubConnection;
    private List<ResultDTO> results = new();
    private bool isDlgVisible { get; set; }

    private LiveResultDetailDTO detail { get; set; }

    //SfDialog dlgForm;
    [CascadingParameter] private Task<AuthenticationState> authenticationStateTask { get; set; }

    protected override async Task OnInitializedAsync()
    {
        isDlgVisible = false;
        detail = new LiveResultDetailDTO { Nominations = new List<LiveResultCandidateDetailDTO>() };
        hubConnection = new HubConnectionBuilder()
            .WithUrl(NavigationManager.ToAbsoluteUri("/resultshub"))
            .Build();
        hubConnection.On<ResultDTO>("ReceiveResult", result =>
        {
            results = Service.GetSenateResults(state.state.PhaseId).Result;
            StateHasChanged();
        });
        hubConnection.On<ResultDTO>("ReceiveNotification", result =>
        {
            results = Service.GetSenateResults(state.state.PhaseId).Result;
            StateHasChanged();
        });
        await hubConnection.StartAsync();
        results = await Service.GetSenateResults(state.state.PhaseId);
        StateHasChanged();
        await Task.CompletedTask;
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            var user = (await authenticationStateTask).User;
            if (user.Identity.IsAuthenticated)
            {
                var dd = await localStorage.GetAsync<ElectionStateDTO>(await confreader.GetSettingValue("electionkey"));
                state.SetState(dd.Value);
                var info = await _UserManager.GetUserAsync(user);
                await Task.CompletedTask;
                StateHasChanged();
            }
            else
            {
                try
                {
                    await localStorage.DeleteAsync(await confreader.GetSettingValue("electionkey"));
                    state.SetState(new ElectionStateDTO { ElectionId = 0, ElectionTitle = "", PhaseId = 0, PhaseTitle = "" });
                    StateHasChanged();
                }
                catch (Exception)
                {
                    // ignored
                }
            }
        }
    }

}