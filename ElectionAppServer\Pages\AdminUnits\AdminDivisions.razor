﻿@page "/divisions"
@*@inherits OwningComponentBase<AdminDivisionsDataService>*@
@using BeforeOpenEventArgs = Syncfusion.Blazor.Popups.BeforeOpenEventArgs
@using ButtonType = MudBlazor.ButtonType
@using FilterType = Syncfusion.Blazor.Grids.FilterType
@using Size = MudBlazor.Size
@inherits OwningComponentBase<AdminDivisionDataService>
@attribute [Authorize(Roles = "Administrators,Data Viewer")]
<SfDialog Width="700px" @bind-Visible="@isDlgVisible" @ref="dlgForm" IsModal="true" ShowCloseIcon="true">
    <DialogEvents OnOpen="beforeOpen"></DialogEvents>
    <DialogTemplates>
        <Header>Division Detail</Header>
        <Content>
            <EditForm Model="@selectedObj" OnValidSubmit="@SaveData" Context="ef">
                <DataAnnotationsValidator/>
                <ValidationSummary/>
                <div class="row mb-2">
                    <div class="col">
                        <SfDropDownList TItem="GeneralItemDTO" TValue="int?" DataSource="ProvincesList" @bind-Value="selectedObj.ProvinceId" Placeholder="Province" FloatLabelType="FloatLabelType.Always">
                            <DropDownListFieldSettings Text="EnglishTitle" Value="Id"></DropDownListFieldSettings>
                        </SfDropDownList>
                        <ValidationMessage For="@(() => selectedObj.ProvinceId)"/>
                    </div>
                </div>
                <div class="row mb-2">
                    <div class="col-md">
                        <SfTextBox OnBlur="TranslateToUrdu" @bind-Value="selectedObj.EnglishTitle" Placeholder="Division" FloatLabelType="FloatLabelType.Always"></SfTextBox>
                        <ValidationMessage For="@(() => selectedObj.EnglishTitle)"/>
                    </div>
                    <div class="col-md">
                        <SfTextBox @bind-Value="selectedObj.UrduTitle" Placeholder="Division (اردو)" FloatLabelType="FloatLabelType.Always"></SfTextBox>
                        <ValidationMessage For="@(() => selectedObj.UrduTitle)"/>
                    </div>
                </div>
                <AuthorizeView Roles="Administrators">
                    <div class="row mb-2">
                        <div class="col">
                            <MudButton ButtonType="ButtonType.Submit" Color="Color.Primary" Variant="Variant.Filled" Size="Size.Small"
                                       StartIcon="@Icons.Material.Filled.Save">
                                Save
                            </MudButton>
                        </div>
                    </div>
                </AuthorizeView>
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>

<SfToast @ref="ToastObj">
    <ToastPosition X="Right"></ToastPosition>
</SfToast>

@if (objList == null)
{
    <p>
        <em>Loading...</em>
    </p>
}
else
{
    <MudText Class="mb-2" Typo="Typo.h5"><MudIcon Icon="@Icons.Material.Filled.Layers" Size="Size.Large"/>Divisions</MudText>
    @*<PageCaption Title="Divisions"></PageCaption>*@
    <section>

        <AuthorizeView Roles="Administrators">
            <div class="row">
                <MudButton Class="mb-2" Color="Color.Primary" Variant="Variant.Filled"
                           Size="Size.Small"
                           StartIcon="@Icons.Material.Filled.Add"
                           OnClick="OpenCreateForm">
                    Create Division
                </MudButton>
            </div>
        </AuthorizeView>

        <div class="row">
            <SfGrid DataSource="@objList" ModelType="@selectedObj" @ref="Grid" AllowFiltering="true"
                    AllowSorting="true" AllowTextWrap="true" Width="100%"
                    Height="calc(100vh - 205px)">
                <GridEvents QueryCellInfo="CustomizeCell" TValue="AdminDivisionDTO"></GridEvents>
                <GridFilterSettings Type="FilterType.Excel"></GridFilterSettings>
                <GridColumns>
                    <GridColumn AutoFit="true" Field="Id" HeaderText="Id"></GridColumn>
                    <GridColumn Width="180px" Field="Province" HeaderText="Province"></GridColumn>
                    <GridColumn Width="180px" Field="UrduTitle" HeaderText="Division (Urdu)" TextAlign="TextAlign.Right"></GridColumn>
                    <GridColumn Width="180px" Field="EnglishTitle" HeaderText="Division (English)"></GridColumn>
                    <GridColumn Width="310px" HeaderText="Audit Info">
                        <Template Context="kk">
                            @{
                                var oo = kk as AdminDivisionDTO;
                            <div style="font-size:11px">
                                <span>Created By: @oo.CreatedBy</span><span> On @oo.CreatedOn</span>
                                @if (!string.IsNullOrEmpty(oo.ModifiedBy))
                                {
                                    <br/>
                                    <span>Modified By: @oo.ModifiedBy</span>
                                    <span> On @oo.ModifiedOn</span>
                                }
                            </div>}
                        </Template>
                    </GridColumn>
                    <GridColumn HeaderText="Actions" AllowFiltering="false" Width="120px">
                        <Template Context="ss">
                            @{
                                var kk = ss as AdminDivisionDTO;
                                <AuthorizeView Roles="Administrators">
                                    <MudFab Color="Color.Error"
                                            Size="Size.Small"
                                            StartIcon="@Icons.Material.Filled.Delete"
                                            OnClick="@(() => DeleteRecord(kk.Id))">
                                    </MudFab>
                                </AuthorizeView>
                                <MudFab Color="Color.Primary"
                                        Size="Size.Small"
                                        StartIcon="@Icons.Material.Filled.Edit"
                                        OnClick="@(() => OpenEditForm(kk))">
                                </MudFab>
                            }
                        </Template>
                    </GridColumn>
                </GridColumns>
            </SfGrid>
        </div>
    </section>
}

@code {

    // AuthenticationState is available as a CascadingParameter
    [CascadingParameter] private Task<AuthenticationState> authenticationStateTask { get; set; }
    [Parameter] public int divisionId { get; set; }
    private SfDialog dlgForm;
    List<AdminDivisionDTO> objList;

    private bool isDlgVisible;

    //private string SaveButtonText = "Save";
    //private string FormTitle = "Create Division";
    private AdminDivisionDTO selectedObj;
    private SfToast ToastObj;
    public string ToastContent = "Unable to delete Record, Related Record(s) Exist";
    public List<GeneralItemDTO> Breadcrumbs { get; set; }
    public int electionId { get; set; }
    public List<GeneralItemDTO> LanguagesList { get; set; }
    public List<GeneralItemDTO> CastesList { get; set; }
    public string ElectionTitle { get; set; }
    private List<GeneralItemDTO> ProvincesList;
    public SfGrid<AdminDivisionDTO> Grid { get; set; }
    private string userId = "";

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        //return base.OnAfterRenderAsync(firstRender);
        if (firstRender)
        {
            var user = (await authenticationStateTask).User;
            if (user.Identity is { IsAuthenticated: true })
            {
                //var dd = await ProtectedSessionStore.GetAsync<ElectionStateDTO>("election");
                var dd = await localStorage.GetAsync<ElectionStateDTO>(await confreader.GetSettingValue("electionkey"));
                state.SetState(dd.Value);
                objList = await Service.GetAll(state.state.ElectionId);
                ProvincesList = await Service.GetProvinceList(state.state.ElectionId);
            }
            else
            {
                try
                {
                    //await ProtectedSessionStore.DeleteAsync("election");
                    await localStorage.DeleteAsync(await confreader.GetSettingValue("electionkey"));
                    state.SetState(new ElectionStateDTO { ElectionId = 0, ElectionTitle = "", PhaseId = 0, PhaseTitle = "" });
                }
                catch (Exception)
                {
                    // ignored
                }

                NavigationManager.NavigateTo("");
            }

            StateHasChanged();
        }
    }

    protected override async Task OnInitializedAsync()
    {
        selectedObj = new AdminDivisionDTO { ProvinceId = null, EnglishTitle = "", UrduTitle = "" };
        userId = (await authenticationStateTask).User.Identity.Name;
        await Task.CompletedTask;
    }

    private void OpenCreateForm()
    {
        ClearData();
        dlgForm.ShowAsync();
        //SaveButtonText = "Create";
    }

    private void OpenEditForm(AdminDivisionDTO st)
    {
        selectedObj = new AdminDivisionDTO { Id = st.Id, UrduTitle = st.UrduTitle, EnglishTitle = st.EnglishTitle, ProvinceId = st.ProvinceId };
        dlgForm.ShowAsync();
        InvokeAsync(StateHasChanged);
    }

    private void ClearData()
    {
        selectedObj = new AdminDivisionDTO { UrduTitle = "", EnglishTitle = "", ProvinceId = null };
    }

    private async Task SaveData()
    {
        try
        {
            var res = await Service.Save(selectedObj, userId);
            objList = await Service.GetAll(state.state.ElectionId);
            await Grid.Refresh();
            await dlgForm.HideAsync();
        }
        catch (Exception ex)
        {
            var mm = new ToastModel { Title = "Error", Content = ex.Message, CssClass = "e-toast-danger", ShowProgressBar = true, Timeout = 5000, ShowCloseButton = true };
            if (ex.InnerException != null)
                mm.Content += "Detail: " + ex.InnerException.Message;
            await ToastObj.ShowAsync(mm);
        }
    }

    private async Task DeleteRecord(int Id)
    {
        var paras = new[] { "Are you sure want to delete this record?" };
        var res = await JSRuntime.InvokeAsync<bool>("confirm", paras);
        if (res)
        {
            try
            {
                await Service.Delete(Id);
                objList = await Service.GetAll(state.state.ElectionId);
                await Grid.Refresh();
            }
            catch (Exception ex)
            {
                var mm = new ToastModel { Title = "Error", Content = ex.Message, CssClass = "e-toast-danger", ShowProgressBar = true, Timeout = 5000, ShowCloseButton = true };
                if (ex.InnerException != null)
                    mm.Content += "Detail: " + ex.InnerException.Message;
                await ToastObj.ShowAsync(mm);
            }
        }
    }

    public void CustomizeCell(QueryCellInfoEventArgs<AdminDivisionDTO> args)
    {
        if (args.Column.Field == "UrduTitle")
        {
            args.Cell.AddClass(new[] { "urdu-column" });
            args.Cell.AddStyle(new[] { "font-size:18px;line-height: 1.4;text-align: right" });
        }
    }

    public async Task CloseForm()
    {
        isDlgVisible = false;
        await dlgForm.HideAsync();
    }

    private void beforeOpen(BeforeOpenEventArgs args)
    {
        args.MaxHeight = null;
    }

    private async Task TranslateToUrdu()
    {
        if (!string.IsNullOrEmpty(selectedObj.EnglishTitle))
        {
            try
            {
                if (string.IsNullOrEmpty(selectedObj.UrduTitle))
                    selectedObj.UrduTitle = await Translation.TranslateText(selectedObj.EnglishTitle);
            }
            catch (Exception)
            {
                // ignored
            }
        }
    }

}