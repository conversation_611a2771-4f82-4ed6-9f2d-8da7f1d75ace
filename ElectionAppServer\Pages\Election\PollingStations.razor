﻿@page "/pollingstations/{ConstituencyId:int}"
@using FilterType = Syncfusion.Blazor.DropDowns.FilterType
@using SortDirection = Syncfusion.Blazor.Grids.SortDirection
@using ButtonType = MudBlazor.ButtonType
@using Size = MudBlazor.Size
@inherits OwningComponentBase<PollingStationDataService>

@attribute [Authorize(Roles = "Administrators")]

<SfToast @ref="ToastObj" Title="Adaptive Tiles Meeting" Icon="e-meeting">
    <ToastPosition X="Right"></ToastPosition>
</SfToast>

<SfDialog Width="700" @bind-Visible="@IsFormOpen" @ref="dlgForm" IsModal="true" ShowCloseIcon="true">
    <DialogTemplates>
        <Header>Polling Station Detail</Header>
        <Content>
            <EditForm Model="@selectedObj" OnValidSubmit="@SavePhaseData">
                <DataAnnotationsValidator/>

                <div class="row">
                    <div class="col-md-2">
                        <SfTextBox @bind-Value="@selectedObj.Number" Placeholder="PS Number" FloatLabelType="FloatLabelType.Always"></SfTextBox>
                        <ValidationMessage For="@(() => selectedObj.Number)"/>
                    </div>
                    <div class="col-md-5">
                        <SfTextBox @bind-Value="@selectedObj.EnglishTitle" Placeholder="Name (English)" FloatLabelType="FloatLabelType.Always" OnBlur="TranslateToUrdu"></SfTextBox>
                        <ValidationMessage For="@(() => selectedObj.EnglishTitle)"/>
                    </div>
                    <div class="col-md-5">
                        <SfTextBox @bind-Value="@selectedObj.UrduTitle" Placeholder="Name (Urdu)" FloatLabelType="FloatLabelType.Always"></SfTextBox>
                        <ValidationMessage For="@(() => selectedObj.UrduTitle)"/>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md">
                        <SfDropDownList ShowClearButton="true" @bind-Value="selectedObj.Type" DataSource="TypeList"
                                        AllowFiltering="true" TItem="GeneralItemDTO" TValue="int?"
                                        Placeholder="Polling Station Type" FloatLabelType="FloatLabelType.Always"
                                        FilterType="FilterType.Contains">
                            <DropDownListFieldSettings Value="Id" Text="EnglishTitle"></DropDownListFieldSettings>
                        </SfDropDownList>


                    </div>

                    <div class="col-md">
                        <SfNumericTextBox ShowSpinButton="false" Format="#######" Decimals="0" @bind-Value="selectedObj.MaleVoters" Placeholder="Male Voters" FloatLabelType="FloatLabelType.Always"></SfNumericTextBox>
                        <ValidationMessage For="@(() => selectedObj.MaleVoters)"/>
                    </div>
                    <div class="col-md">
                        <SfNumericTextBox ShowSpinButton="false" Format="#######" Decimals="0" @bind-Value="selectedObj.FemaleVoters" Placeholder="Female Voters" FloatLabelType="FloatLabelType.Always"></SfNumericTextBox>
                        <ValidationMessage For="@(() => selectedObj.FemaleVoters)"/>
                    </div>
                </div>
                <div class="row mt-2 mb-2">
                    <div class="col-md">
                        <MudButton ButtonType="ButtonType.Submit" Size="Size.Small"
                                   Color="Color.Primary" Variant="Variant.Filled"
                                   StartIcon="@Icons.Material.Filled.Save">
                            @(selectedObj.Id == 0 ? "Save" : "Update")
                        </MudButton>
                    </div>
                </div>
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>

<section style="padding: 15px 1rem;">
    <div class="card bg-info mb-3">
        <div class="card-header" style="display: flex; justify-content: space-between;align-items: center">
            <div style="flex:1; font-size:30px; font-weight:bold">
                @info.Code <span>-</span> @info.EnglishTitle - Polling Stations<br/>
                <span class="urdu-cap" style="font-size:34px; text-align: right;direction: rtl">@info.UrduTitle - پولنگ اسٹیشن </span>
            </div>
        </div>
        <div class="card-body bg-light" style="color:black">
            <div class="row">
                <div class="col-md rptfield">
                    <div>Election: </div>
                    <div class="rptdata">
                        <b>
                            @info.EnglishTitle<br/><span class="urdu-sm">@info.UrduTitle</span>
                        </b>
                    </div>
                </div>
                <div class="col-md rptfield">
                    <div>Assembly: </div>
                    <div class="rptdata">
                        <b>
                            @info.AssemblyEnglish<br/><span class="urdu-sm">@info.AssemblyUrdu</span>
                        </b>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-3 rptfield">
                    <div>Province: </div>
                    <div class="rptdata">
                        <b>
                            @info.ProvinceEnglish<br/><span class="urdu-sm">@info.ProvinceUrdu</span>
                        </b>
                    </div>
                </div>
                @*<div class="col-3 rptfield"><div>Division: </div><div class="rptdata"><b>@info.DivisionEnglish<br /><span class="urdu-sm">@info.DivisionUrdu</span></b></div></div>*@
                <div class="col-md-3 rptfield">
                    <div>Region: </div>
                    <div class="rptdata">
                        <b>
                            @info.RegionEnglish<br/><span class="urdu-sm">@info.RegionUrdu</span>
                        </b>
                    </div>
                </div>
                <div class="col-md-3 rptfield">
                    <div>District: </div>
                    <div class="rptdata">
                        <b>
                            @info.DistrictEnglish<br/><span class="urdu-sm">@info.DistrictUrdu</span>
                        </b>
                    </div>
                </div>
            </div>
            @if (state.state.ElectionType == ElectionType.LocalBody)
            {
                <div class="row">
                    <div class="col-md-3 rptfield">
                        <div>Town: </div>
                        <div class="rptdata">
                            <b>
                                @info.TownEnglish<br/><span class="urdu-sm">@info.TownUrdu</span>
                            </b>
                        </div>
                    </div>
                    <div class="col-md-3 rptfield">
                        <div>UC: </div>
                        <div class="rptdata">
                            <b>
                                @info.UCEnglish<br/><span class="urdu-sm">@info.UCUrdu</span>
                            </b>
                        </div>
                    </div>
                </div>
            }
            <div class="row">
                <div class="col-md">
                    Male Voters: <b>@info.PollingStations.Sum(k => k.MaleVoters)</b>
                </div>
                <div class="col-md">
                    Female Voters: <b>@info.PollingStations.Sum(k => k.FemaleVoters)</b>
                </div>
                <div class="col-md">
                    Total Polling Stations: <b>@info.PollingStations.Count()</b>
                </div>
            </div>
        </div>
    </div>

    <SfButton CssClass="e-primary mb-2" IsPrimary="true" OnClick="OpenCreateForm">Create</SfButton>

    <SfGrid DataSource="@info.PollingStations" AllowSorting="true" AllowPaging="true" AllowFiltering="true" AllowTextWrap="true" Width="100%">
        <GridEvents QueryCellInfo="CustomizeCell" TValue="PollingStationDetailDTO"></GridEvents>
        <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Excel"></GridFilterSettings>
        <GridSortSettings>
            <GridSortColumns>
                <GridSortColumn Field="Number" Direction="SortDirection.Ascending"></GridSortColumn>
            </GridSortColumns>
        </GridSortSettings>
        <GridColumns>
            <GridColumn AutoFit="true" Field="@nameof(PollingStationDetailDTO.Number)" HeaderText="Number"></GridColumn>
            <GridColumn AutoFit="true" Field="@nameof(PollingStationDetailDTO.EnglishTitle)" HeaderText="Name (English)"></GridColumn>
            <GridColumn AutoFit="true" Field="@nameof(PollingStationDetailDTO.UrduTitle)" HeaderText="Name (اردو)"></GridColumn>
            <GridColumn AutoFit="true" Field="@nameof(PollingStationDetailDTO.MaleVoters)" HeaderText="Male Voters"></GridColumn>
            <GridColumn AutoFit="true" Field="@nameof(PollingStationDetailDTO.FemaleVoters)" HeaderText="Female Voters"></GridColumn>
            <GridColumn AutoFit="true" Field="@nameof(PollingStationDetailDTO.TotalVoters)" HeaderText="Total Voters"></GridColumn>
            <GridColumn AutoFit="true" Field="@nameof(PollingStationDetailDTO.PollingStationType)" HeaderText="Type"></GridColumn>
            <GridColumn Width="150px" HeaderText="Actions">
                <Template Context="cc">
                    @{
                        var obj = cc as PollingStationDetailDTO;
                        <SfButton IsPrimary="true" CssClass="e-warning" OnClick="@(() => DeleteRecord(obj.Id))">
                            <i class="fas fa-trash-alt"></i>
                        </SfButton>
                        <SfButton IsPrimary="true" CssClass="e-info" OnClick="@(() => OpenEditFormAsync(obj))">
                            <i class="fas fa-pencil-alt"></i>
                        </SfButton>
                    }
                </Template>
            </GridColumn>
        </GridColumns>
    </SfGrid>
</section>