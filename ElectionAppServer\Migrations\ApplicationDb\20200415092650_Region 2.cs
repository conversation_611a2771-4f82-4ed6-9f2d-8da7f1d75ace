﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace ElectionAppServer.Migrations.ApplicationDb
{
	public partial class Region2 : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.AddColumn<int>(
				 name: "RegionId",
				 table: "Structures",
				 nullable: true);

			migrationBuilder.CreateIndex(
				 name: "IX_Structures_RegionId",
				 table: "Structures",
				 column: "RegionId");

			migrationBuilder.AddForeignKey(
				 name: "FK_Structures_Regions_RegionId",
				 table: "Structures",
				 column: "RegionId",
				 principalTable: "Regions",
				 principalColumn: "Id",
				 onDelete: ReferentialAction.Restrict);
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropForeignKey(
				 name: "FK_Structures_Regions_RegionId",
				 table: "Structures");

			migrationBuilder.DropIndex(
				 name: "IX_Structures_RegionId",
				 table: "Structures");

			migrationBuilder.DropColumn(
				 name: "RegionId",
				 table: "Structures");
		}
	}
}
