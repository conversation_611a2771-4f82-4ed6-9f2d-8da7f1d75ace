﻿using Microsoft.EntityFrameworkCore.Migrations;
using System;

namespace ElectionAppServer.Migrations.ApplicationDb
{
	public partial class removeNamanigar2 : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropTable(
				 name: "ConstituencyNamanigars");
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.CreateTable(
				 name: "ConstituencyNamanigars",
				 columns: table => new
				 {
					 NamanigarId = table.Column<string>(type: "nvarchar(450)", nullable: false),
					 ConstituencyId = table.Column<int>(type: "int", nullable: false),
					 CreatedBy = table.Column<string>(type: "varchar(450)", unicode: false, maxLength: 450, nullable: false),
					 CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "getdate()"),
					 ModifiedBy = table.Column<string>(type: "varchar(450)", unicode: false, maxLength: 450, nullable: true),
					 ModifiedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
					 PSDetail = table.Column<string>(type: "varchar(500)", unicode: false, maxLength: 500, nullable: false)
				 },
				 constraints: table =>
				 {
					 table.PrimaryKey("PK_ConstituencyNamanigars", x => new { x.NamanigarId, x.ConstituencyId });
					 table.ForeignKey(
							  name: "FK_ConstituencyNamanigars_Structures_ConstituencyId",
							  column: x => x.ConstituencyId,
							  principalTable: "Structures",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Cascade);
					 table.ForeignKey(
							  name: "FK_ConstituencyNamanigars_AspNetUsers_NamanigarId",
							  column: x => x.NamanigarId,
							  principalTable: "AspNetUsers",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Cascade);
				 });

			migrationBuilder.CreateIndex(
				 name: "IX_ConstituencyNamanigars_ConstituencyId",
				 table: "ConstituencyNamanigars",
				 column: "ConstituencyId");
		}
	}
}
