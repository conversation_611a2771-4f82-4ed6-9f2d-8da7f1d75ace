﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace ElectionAppServer.Migrations.ApplicationDb
{
    public partial class RePollSeatTyes : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "RePollSeats",
                columns: table => new
                {
                    PhaseId = table.Column<int>(type: "int", nullable: false),
                    ConstituencyId = table.Column<int>(type: "int", nullable: false),
                    SeatTypeId = table.Column<int>(type: "int", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatedBy = table.Column<string>(type: "nvarchar(450)", maxLength: 450, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RePollSeats", x => new { x.PhaseId, x.ConstituencyId, x.SeatTypeId });
                    table.ForeignKey(
                        name: "FK_RePollSeats_ElectionPhases_PhaseId",
                        column: x => x.PhaseId,
                        principalTable: "ElectionPhases",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_RePollSeats_SeatTypes_SeatTypeId",
                        column: x => x.SeatTypeId,
                        principalTable: "SeatTypes",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_RePollSeats_Structures_ConstituencyId",
                        column: x => x.ConstituencyId,
                        principalTable: "Structures",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateIndex(
                name: "IX_RePollSeats_ConstituencyId",
                table: "RePollSeats",
                column: "ConstituencyId");

            migrationBuilder.CreateIndex(
                name: "IX_RePollSeats_SeatTypeId",
                table: "RePollSeats",
                column: "SeatTypeId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "RePollSeats");
        }
    }
}
