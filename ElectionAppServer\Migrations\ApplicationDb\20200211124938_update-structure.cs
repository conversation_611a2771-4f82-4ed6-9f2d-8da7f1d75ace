﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace ElectionAppServer.Migrations.ApplicationDb
{
	public partial class updatestructure : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropForeignKey(
				 name: "FK_Structures_Structures_ProvincialAssemblyHalka_StructureId",
				 table: "Structures");

			migrationBuilder.DropIndex(
				 name: "IX_Structures_ProvincialAssemblyHalka_StructureId",
				 table: "Structures");

			migrationBuilder.DropColumn(
				 name: "ProvincialAssemblyHalka_StructureId",
				 table: "Structures");
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.AddColumn<int>(
				 name: "ProvincialAssemblyHalka_StructureId",
				 table: "Structures",
				 type: "int",
				 nullable: true);

			migrationBuilder.CreateIndex(
				 name: "IX_Structures_ProvincialAssemblyHalka_StructureId",
				 table: "Structures",
				 column: "ProvincialAssemblyHalka_StructureId");

			migrationBuilder.AddForeignKey(
				 name: "FK_Structures_Structures_ProvincialAssemblyHalka_StructureId",
				 table: "Structures",
				 column: "ProvincialAssemblyHalka_StructureId",
				 principalTable: "Structures",
				 principalColumn: "Id",
				 onDelete: ReferentialAction.Restrict);
		}
	}
}
