﻿@page "/"
@inject UserManager<ApplicationUser> _UserManager
@inject RoleManager<IdentityRole> _RoleManager

@using FilterType = Syncfusion.Blazor.Grids.FilterType
@using Size = MudBlazor.Size
@using SortDirection = Syncfusion.Blazor.Grids.SortDirection
@inherits OwningComponentBase<ElectionService>

<SfDialog @ref="dlgPhase" Width="700px" ShowCloseIcon="true" CloseOnEscape="true" Visible="false" IsModal="true">
    <DialogTemplates>
        <Header>Election Date</Header>
        <Content>
            <SfGrid @ref="@dgPhase" AllowSorting="true" AllowFiltering="true" DataSource="@PhasesList">
                <GridFilterSettings Type="FilterType.Excel"></GridFilterSettings>
                <GridColumns>
                    <GridColumn AutoFit="true" HeaderText="Description" Field="@nameof(ElectionDTO.PhaseTitle)"></GridColumn>
                    <GridColumn AutoFit="true" Format="d MMM, yyyy" HeaderText="Date" Field="@nameof(ElectionDTO.StartDate)"></GridColumn>
                    <GridColumn AutoFit="true">
                        <Template Context="mm">
                            @{
                                var ep = mm as ElectionDTO;
                                if (ep != null)
                                {
                                    <MudButton Variant="Variant.Filled"
                                               Size="Size.Small"
                                               Color="Color.Primary"
                                               StartIcon="@Icons.Material.Filled.CalendarViewDay"
                                               OnClick="@(() => SelectPhase(ep.ElectionId, ep.EnglishTitle, ep.Id, ep.PhaseTitle, ep.ElectionType))">
                                        Select
                                    </MudButton>
                                }
                            }
                        </Template>
                    </GridColumn>
                </GridColumns>
            </SfGrid>
        </Content>
    </DialogTemplates>
</SfDialog>

<AuthorizeView>
    <Authorized>
        @if (state.state.ElectionId == 0)
        {
            <PageCaption Title="Welcome to Election Portal" SubTitle="Please select election"></PageCaption>

            <div>
                <SfGrid DataSource="ElectionsList" Height="calc(100vh - 190px)"
                        AllowTextWrap="true"
                        TValue="ElectionDTO"
                        AllowFiltering="true" AllowSorting="true" Width="100%">
                    <GridTextWrapSettings WrapMode="WrapMode.Content"></GridTextWrapSettings>
                    <GridFilterSettings Type="FilterType.Excel"></GridFilterSettings>
                    <GridSortSettings>
                        <GridSortColumns>
                            <GridSortColumn Field="StartDate" Direction="SortDirection.Descending"></GridSortColumn>
                        </GridSortColumns>
                    </GridSortSettings>
                    <GridColumns>
                        <GridColumn AutoFit="true" HeaderText="Head" Field=@nameof(ElectionDTO.Head)></GridColumn>
                        <GridColumn AutoFit="true" HeaderText="Election" Field=@nameof(ElectionDTO.EnglishTitle)></GridColumn>
                        <GridColumn AutoFit="true" HeaderText="Year" Field=@nameof(ElectionDTO.YearFrom) Type="ColumnType.Date"></GridColumn>
                        <GridColumn AllowGrouping="false" Width="100px" AllowSorting="false" AllowFiltering="false">
                            <Template Context="kk">
                                @{
                                    var ep = kk as ElectionDTO;
                                    <MudButton Variant="Variant.Filled" Size="Size.Small" Color="Color.Primary" StartIcon="@Icons.Material.Filled.CheckCircle" OnClick="@(() => SelectElection(ep.ElectionId, ep.EnglishTitle, ep.Id, ep.PhaseTitle, ep.ElectionType))">Select</MudButton>
                                }
                            </Template>
                        </GridColumn>
                    </GridColumns>
                </SfGrid>
            </div>
        }
        else
        {
            <section>
                <div class="callout callout-info" style="background-color: #e0e0e0;margin-top: 7px; display: flex; gap: 10px">
                    <h5 style="margin-bottom: 0px;">@state.state.ElectionTitle</h5>
                    <MudButton Size="Size.Small" Color="Color.Primary" Variant="Variant.Filled" StartIcon="@Icons.Material.Filled.ChangeCircle" OnClick="ClearElection">Change</MudButton>
                </div>
                <div class="row">
                    <div class="col" style="min-width: 150px;">
                        <SmallBox Color="bg-gradient-info" Indicator="@TotalAssemblies.ToString()" Description="@(TotalAssemblies <= 1 ? "Assembly" : "Assemblies")" Icon="ion-grid" Link="@($"/assemblies/{state.state.ElectionId}")"/>
                    </div>
                    <div class="col" style="min-width: 150px;">
                        <SmallBox Color="bg-green" Description="@(TotalConstituencies <= 1 ? "Constituency" : "Constituencies")" Indicator="@TotalConstituencies.ToString()" Icon="ion-map" Link="/search"></SmallBox>
                    </div>
                    @*<div class="col">
							 <SmallBox Color="bg-yellow" Indicator="@TotalCandidates.ToString()" Description="@(TotalCandidates<=1 ? "Candidate" : "Candidates")" Icon="ion-person" Link="/candidates"></SmallBox>
						</div>*@
                    <div class="col" style="min-width: 150px;">
                        <SmallBox Color="bg-red" Indicator="@TotalNominations.ToString()" Description="@(TotalNominations <= 1 ? "Nominated Candidate" : "Nominated Candidate")" Icon="ion-person-stalker" Link="/nominations2"></SmallBox>
                    </div>
                    <div class="col" style="min-width: 150px;">
                        <SmallBox Color="bg-blue" Indicator="@TotalParties.ToString()" Description="@(TotalParties <= 1 ? "Party" : "Parties")" Icon="ion-flag" Link="/parties"></SmallBox>
                    </div>
                    <div class="col" style="min-width: 150px;">
                        <SmallBox Color="bg-gray" Indicator="@TotalNamanigars.ToString()" Description="Namanigars" Icon="ion-ios-people-outline" Link="/namanigars"></SmallBox>
                    </div>
                </div>


                @* <div class="row">
						 <div class="col">
							  <div class="card bg-info border border-info">
									<div class="card-header">
										 Assembly Wise Nominations
									</div>
									<div class="card-body bg-white">
										 <SfAccumulationChart EnableAnimation="true">
											  <AccumulationChartTooltipSettings Enable="true"></AccumulationChartTooltipSettings>
											  <AccumulationChartLegendSettings Visible="false"></AccumulationChartLegendSettings>
											  <AccumulationChartSeriesCollection>
													<AccumulationChartSeries DataSource="@assemblyWiseNominations" XName="xValue" YName="yValue" Name="Browser" Radius="@OuterRadius" StartAngle="@StartAngle" EndAngle="@EndAngle"
																					 InnerRadius="0%" ExplodeIndex="@ExplodeIndex" Explode="true" ExplodeOffset="@ExplodeRadius">
														 <AccumulationDataLabelSettings Visible="true" Name="xValue" Position="AccumulationLabelPosition.Outside"></AccumulationDataLabelSettings>
													</AccumulationChartSeries>
											  </AccumulationChartSeriesCollection>
										 </SfAccumulationChart>
									</div>
							  </div>
						 </div>
						 <div class="col">
							  <div class="card bg-info border border-info">
									<div class="card-header">
										 Party Wise Nominations
									</div>
									<div class="card-body bg-white">
										 <SfAccumulationChart EnableAnimation="true">
											  <AccumulationChartTooltipSettings Enable="true"></AccumulationChartTooltipSettings>
											  <AccumulationChartLegendSettings Visible="false"></AccumulationChartLegendSettings>
											  <AccumulationChartSeriesCollection>
													<AccumulationChartSeries DataSource="@partyWiseNominations" XName="xValue" YName="yValue" Name="Browser" Radius="@OuterRadius" StartAngle="@StartAngle" EndAngle="@EndAngle"
																					 InnerRadius="0%" ExplodeIndex="@ExplodeIndex" Explode="true" ExplodeOffset="@ExplodeRadius">
														 <AccumulationDataLabelSettings Visible="true" Name="xValue" Position="AccumulationLabelPosition.Outside"></AccumulationDataLabelSettings>
													</AccumulationChartSeries>
											  </AccumulationChartSeriesCollection>
										 </SfAccumulationChart>
									</div>
							  </div>
						 </div>
					</div>*@
            </section>
        }
    </Authorized>
    <NotAuthorized>
    </NotAuthorized>
</AuthorizeView>