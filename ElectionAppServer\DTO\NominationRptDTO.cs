﻿using System;

namespace ElectionAppServer.DTO;

public class NominationRptDTO
{
    public string Election { get; set; }
    public string Phase { get; set; }
    public string Assembly { get; set; }
    public string ConstituencyCode { get; set; }
    public int ConstituencyId { get; set; }
    public string Constituency { get; set; }
    public string Symbol { get; set; }
    public string SeatType { get; set; }
    public int Weight { get; set; }
    public int? Votes { get; set; }
    public DateTime Date { get; internal set; }
    public string Candidate { get; internal set; }
    public int CandidateId { get; internal set; }
    public string Type { get; internal set; }
    public string Town { get; internal set; }
    public string District { get; internal set; }
    public string Division { get; internal set; }
    public string Region { get; internal set; }
    public string Province { get; internal set; }
    public string Party { get; internal set; }
}