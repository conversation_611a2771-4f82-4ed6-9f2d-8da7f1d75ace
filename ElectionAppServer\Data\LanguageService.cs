﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ElectionAppServer.Model;
using Microsoft.EntityFrameworkCore;

namespace ElectionAppServer.Data;

public class LanguageService
{
    private readonly IDbContextFactory<ApplicationDbContext>
        _contextFactory; // private  readonly  ApplicationDbContext dc;

    public LanguageService(IDbContextFactory<ApplicationDbContext> contextFactory) // (ApplicationDbContext  context)
    {
        _contextFactory = contextFactory; // dc  =  context;
    }

    public Task<Language> CreateLanguage(Language obj)
    {
        using var dc = _contextFactory.CreateDbContext();
        dc.Languages.Add(obj);
        dc.SaveChanges();
        return Task.FromResult(obj);
    }

    public async Task<Language> DeleteLanguage(int id)
    {
        await using var dc = await _contextFactory.CreateDbContextAsync();
        var st = await (from aa in dc.Languages
            where aa.Id == id
            select aa).FirstOrDefaultAsync();
        dc.Languages.Remove(st);
        await dc.SaveChangesAsync();
        return await Task.FromResult(st);
    }

    public Task<List<Language>> GetList()
    {
        using var dc = _contextFactory.CreateDbContext();
        var res = dc.Languages.OrderBy(c => c.EnglishTitle).ToList();
        return Task.FromResult(res);
    }

    public Task<Language> UpdateLanguage(Language obj)
    {
        using var dc = _contextFactory.CreateDbContext();
        var st = (from aa in dc.Languages
            where aa.Id == obj.Id
            select aa).FirstOrDefault();
        if (st != null)
        {
            st.UrduTitle = obj.UrduTitle.Trim();
            st.EnglishTitle = obj.EnglishTitle.Trim();
            st.ModifiedBy = obj.ModifiedBy;
            st.ModifiedDate = DateTime.Now;
        }

        dc.SaveChanges();
        return Task.FromResult(obj);
    }
}