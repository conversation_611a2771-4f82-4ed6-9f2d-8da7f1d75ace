﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace ElectionAppServer.Migrations.ApplicationDb
{
	public partial class renamenamanigartouser : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropTable(
				 name: "NamanigarConstituencies");

			migrationBuilder.CreateTable(
				 name: "UserConstituencies",
				 columns: table => new
				 {
					 UserId = table.Column<string>(nullable: false),
					 ElectionAssemblyId = table.Column<int>(nullable: false),
					 StructureId = table.Column<int>(nullable: false)
				 },
				 constraints: table =>
				 {
					 table.PrimaryKey("PK_UserConstituencies", x => new { x.ElectionAssemblyId, x.UserId, x.StructureId });
					 table.ForeignKey(
							  name: "FK_UserConstituencies_ElectionAssemblies_ElectionAssemblyId",
							  column: x => x.ElectionAssemblyId,
							  principalTable: "ElectionAssemblies",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Restrict);
					 table.ForeignKey(
							  name: "FK_UserConstituencies_Structures_StructureId",
							  column: x => x.StructureId,
							  principalTable: "Structures",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Restrict);
					 table.ForeignKey(
							  name: "FK_UserConstituencies_AspNetUsers_UserId",
							  column: x => x.UserId,
							  principalTable: "AspNetUsers",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Restrict);
				 });

			migrationBuilder.CreateIndex(
				 name: "IX_UserConstituencies_StructureId",
				 table: "UserConstituencies",
				 column: "StructureId");

			migrationBuilder.CreateIndex(
				 name: "IX_UserConstituencies_UserId",
				 table: "UserConstituencies",
				 column: "UserId");
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropTable(
				 name: "UserConstituencies");

			migrationBuilder.CreateTable(
				 name: "NamanigarConstituencies",
				 columns: table => new
				 {
					 ElectionAssemblyId = table.Column<int>(type: "int", nullable: false),
					 NamanigarId = table.Column<string>(type: "nvarchar(450)", nullable: false),
					 StructureId = table.Column<int>(type: "int", nullable: false)
				 },
				 constraints: table =>
				 {
					 table.PrimaryKey("PK_NamanigarConstituencies", x => new { x.ElectionAssemblyId, x.NamanigarId, x.StructureId });
					 table.ForeignKey(
							  name: "FK_NamanigarConstituencies_ElectionAssemblies_ElectionAssemblyId",
							  column: x => x.ElectionAssemblyId,
							  principalTable: "ElectionAssemblies",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Restrict);
					 table.ForeignKey(
							  name: "FK_NamanigarConstituencies_AspNetUsers_NamanigarId",
							  column: x => x.NamanigarId,
							  principalTable: "AspNetUsers",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Restrict);
					 table.ForeignKey(
							  name: "FK_NamanigarConstituencies_Structures_StructureId",
							  column: x => x.StructureId,
							  principalTable: "Structures",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Restrict);
				 });

			migrationBuilder.CreateIndex(
				 name: "IX_NamanigarConstituencies_NamanigarId",
				 table: "NamanigarConstituencies",
				 column: "NamanigarId");

			migrationBuilder.CreateIndex(
				 name: "IX_NamanigarConstituencies_StructureId",
				 table: "NamanigarConstituencies",
				 column: "StructureId");
		}
	}
}
