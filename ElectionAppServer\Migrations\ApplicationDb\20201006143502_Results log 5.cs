﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace ElectionAppServer.Migrations.ApplicationDb
{
	public partial class Resultslog5 : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropForeignKey(
				 name: "FK_ResultLogs_AspNetUsers_ApplicationUserId",
				 table: "ResultLogs");

			migrationBuilder.DropForeignKey(
				 name: "FK_ResultLogs_AspNetUsers_ApplicationUserId1",
				 table: "ResultLogs");

			migrationBuilder.DropIndex(
				 name: "IX_ResultLogs_ApplicationUserId",
				 table: "ResultLogs");

			migrationBuilder.DropIndex(
				 name: "IX_ResultLogs_ApplicationUserId1",
				 table: "ResultLogs");

			migrationBuilder.DropColumn(
				 name: "ApplicationUserId",
				 table: "ResultLogs");

			migrationBuilder.DropColumn(
				 name: "ApplicationUserId1",
				 table: "ResultLogs");
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.AddColumn<string>(
				 name: "ApplicationUserId",
				 table: "ResultLogs",
				 type: "nvarchar(450)",
				 nullable: true);

			migrationBuilder.AddColumn<string>(
				 name: "ApplicationUserId1",
				 table: "ResultLogs",
				 type: "nvarchar(450)",
				 nullable: true);

			migrationBuilder.CreateIndex(
				 name: "IX_ResultLogs_ApplicationUserId",
				 table: "ResultLogs",
				 column: "ApplicationUserId");

			migrationBuilder.CreateIndex(
				 name: "IX_ResultLogs_ApplicationUserId1",
				 table: "ResultLogs",
				 column: "ApplicationUserId1");

			migrationBuilder.AddForeignKey(
				 name: "FK_ResultLogs_AspNetUsers_ApplicationUserId",
				 table: "ResultLogs",
				 column: "ApplicationUserId",
				 principalTable: "AspNetUsers",
				 principalColumn: "Id",
				 onDelete: ReferentialAction.Restrict);

			migrationBuilder.AddForeignKey(
				 name: "FK_ResultLogs_AspNetUsers_ApplicationUserId1",
				 table: "ResultLogs",
				 column: "ApplicationUserId1",
				 principalTable: "AspNetUsers",
				 principalColumn: "Id",
				 onDelete: ReferentialAction.Restrict);
		}
	}
}
