﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace ElectionAppServer.Migrations.ApplicationDb
{
	public partial class UpdateUserTable : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.AddColumn<string>(
				 name: "Address",
				 table: "AspNetUsers",
				 maxLength: 500,
				 nullable: true);

			migrationBuilder.AddColumn<string>(
				 name: "FullName",
				 table: "AspNetUsers",
				 maxLength: 200,
				 nullable: true);

			migrationBuilder.AddColumn<int>(
				 name: "Gender",
				 table: "AspNetUsers",
				 nullable: true);
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropColumn(
				 name: "Address",
				 table: "AspNetUsers");

			migrationBuilder.DropColumn(
				 name: "FullName",
				 table: "AspNetUsers");

			migrationBuilder.DropColumn(
				 name: "Gender",
				 table: "AspNetUsers");
		}
	}
}
