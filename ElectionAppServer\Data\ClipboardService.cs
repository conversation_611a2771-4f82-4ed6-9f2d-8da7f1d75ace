﻿using System.Threading.Tasks;
using Microsoft.JSInterop;

namespace ElectionAppServer.Data;

public class ClipboardService
{
    private readonly IJSRuntime jsRuntime;

    public ClipboardService(IJSRuntime jsRuntime)
    {
        this.jsRuntime = jsRuntime;
    }

    public ValueTask WriteTextAsync(string ticker)
    {
        //Console.WriteLine(ticker.Trim().Replace("<br />", "\n").Replace("<br/>", "\n").Replace("<span class='tiker-last-update'>", "").Replace("</span>", ""));
        //text = text.Trim().Replace("<br />", "\n\r");
        ticker = ticker.Trim().Replace("<br />", "\n")
            .Replace("<br/>", "\n")
            .Replace("<span class='tiker-last-update'>", "")
            .Replace("</span>", "");

        return jsRuntime.InvokeVoidAsync("navigator.clipboard.writeText", ticker);
    }
}