﻿@page "/languages"

@using BeforeOpenEventArgs = Syncfusion.Blazor.Popups.BeforeOpenEventArgs
@using ButtonType = MudBlazor.ButtonType
@using FilterType = Syncfusion.Blazor.Grids.FilterType
@using Size = MudBlazor.Size
@inherits OwningComponentBase<LanguageService>

<MudText Typo="Typo.h5">Languages</MudText>
@*<PageCaption Title="Languages"></PageCaption>*@
<SfToast @ref="ToastObj" Title="Alert" ShowProgressBar="true" Timeout="3000">
    <ToastPosition X="Right" Y="Bottom"></ToastPosition>
</SfToast>

<SfDialog Width="500px" @bind-Visible="@isDlgVisible" @ref="dlgForm" IsModal="true" ShowCloseIcon="true">
    <DialogEvents OnOpen="beforeOpen"></DialogEvents>
    <DialogTemplates>
        <Header>@FormTitle</Header>
        <Content>
            <EditForm Model="@selectedObj" OnValidSubmit="@SaveLanguageData">
                <DataAnnotationsValidator/>
                <div class="container">
                    <div class="row">
                        <div class="input-field col s6">
                            <SfTextBox @bind-Value="selectedObj.EnglishTitle" Placeholder="Title (English)" OnBlur="TranslateToUrdu" FloatLabelType="FloatLabelType.Always"></SfTextBox>
                            <ValidationMessage For="@(() => selectedObj.EnglishTitle)"/>
                        </div>
                        <div class="input-field col s6">
                            <SfTextBox @bind-Value="selectedObj.UrduTitle" Placeholder="Title (Urdu)" FloatLabelType="FloatLabelType.Always"></SfTextBox>
                            <ValidationMessage For="@(() => selectedObj.UrduTitle)"/>
                        </div>
                    </div>
                    <div class="row">
                        <MudButton ButtonType="ButtonType.Submit"
                                   Color="Color.Primary" Size="Size.Small"
                                   Variant="Variant.Filled">
                            @SaveButtonText
                        </MudButton>
                    </div>
                </div>
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>

<!-- AuthorizeView allows us to only show sections of the page -->
<!-- based on the security on the current user -->
<AuthorizeView>
    <!-- Show this section if the user is logged in -->
    <Authorized>
        <section>
            @if (objList == null)
            {
                <p>
                    <em>Loading...</em>
                </p>
            }
            else
            {
                <SfButton IsPrimary="true" CssClass="e-primary mb-2" OnClick="OpenCreateForm">Create Language</SfButton>
                <SfGrid DataSource="@objList" AllowFiltering="true" ModelType="@selectedObj">
                    <GridFilterSettings Type="FilterType.Excel"></GridFilterSettings>
                    <GridColumns>
                        <GridColumn Field="Id" HeaderText="Id" AutoFit="true"></GridColumn>
                        <GridColumn Field="UrduTitle" HeaderText="Urdu Title" TextAlign="TextAlign.Right"></GridColumn>
                        <GridColumn Field="EnglishTitle" HeaderText="English Title"></GridColumn>
                        <GridColumn HeaderText="Actions" AllowFiltering="false">
                            <Template Context="ss">
                                @{
                                    var kk = ss as Language;
                                    <SfButton IsPrimary="true" CssClass="e-danger" OnClick="@(() => DeleteRecord(kk.Id))">
                                        <i class="fas fa-trash-alt"></i>
                                    </SfButton>
                                    <SfButton IsPrimary="true" CssClass="e-primary" OnClick="@(() => OpenEditForm(kk))">
                                        <i class="fas fa-pencil-alt"></i>
                                    </SfButton>
                                }
                            </Template>
                        </GridColumn>
                    </GridColumns>
                </SfGrid>
            }
        </section>
    </Authorized>
    <!-- Show this section if the user is not logged in -->
    <NotAuthorized>
        <p>You're not signed in.</p>
    </NotAuthorized>
</AuthorizeView>

@code {

    // AuthenticationState is available as a CascadingParameter
    [CascadingParameter] private Task<AuthenticationState> authenticationStateTask { get; set; }

    private SfDialog dlgForm;
    List<Language> objList;
    private bool isDlgVisible;
    private string SaveButtonText = "Save";
    private readonly string FormTitle = "Create Language";
    private Language selectedObj = new();
    private SfToast ToastObj;
    public string ToastContent = "Unable to delete Record, Related Record(s) Exist";

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        //return base.OnAfterRenderAsync(firstRender);
        if (firstRender)
        {
            var user = (await authenticationStateTask).User;
            if (user.Identity.IsAuthenticated)
            {
                //var dd =  await ProtectedSessionStore.GetAsync<ElectionStateDTO>("election");
                var dd = await localStorage.GetAsync<ElectionStateDTO>(await confreader.GetSettingValue("electionkey"));
                state.SetState(dd.Value);
            }
            else
            {
                try
                {
                    //await  ProtectedSessionStore.DeleteAsync("election");
                    await localStorage.DeleteAsync(await confreader.GetSettingValue("electionkey"));
                    state.SetState(new ElectionStateDTO { ElectionId = 0, ElectionTitle = "", PhaseId = 0, PhaseTitle = "" });
                }
                catch (Exception)
                {
                    // ignored
                }

                NavigationManager.NavigateTo("");
            }

            StateHasChanged();
        }
    }

    protected override async Task OnInitializedAsync()
    {
        // Get the current user
        var user = (await authenticationStateTask).User;
        // Get the objList for the current user
        // We access WeatherobjListervice using @Service
        objList = await Service.GetList();
    }

    private async Task OpenCreateForm()
    {
        selectedObj = new Language();
        await dlgForm.ShowAsync();
        SaveButtonText = "Create";
    }

    private async Task OpenEditForm(Language st)
    {
        selectedObj.Id = st.Id;
        selectedObj.UrduTitle = st.UrduTitle;
        selectedObj.EnglishTitle = st.EnglishTitle;
        await dlgForm.ShowAsync();
        SaveButtonText = "Update";
    }

    private void ClearData()
    {
        selectedObj.Id = 0;
        selectedObj.UrduTitle = "";
        selectedObj.EnglishTitle = "";
    }

    private async Task SaveLanguageData()
    {
        // Close the Popup
        // ShowPopup = false;
        // Get the current user
        var user = (await authenticationStateTask).User;
        // A new forecast will have the Id set to 0
        if (selectedObj.Id == 0)
        {
            // Create forecast
            var st = new Language();
            st.UrduTitle = selectedObj.UrduTitle.Trim();
            st.EnglishTitle = selectedObj.EnglishTitle.Trim();
            st.CreatedDate = DateTime.Now;
            st.ModifiedDate = DateTime.Now;

            st.CreatedBy = user.Identity.Name;
            st.ModifiedBy = user.Identity.Name;
            //Service.CreateLanguage(st);
            var res = Service.CreateLanguage(st);
            objList = await Service.GetList();
            ClearData();
            await dlgForm.HideAsync();
        }
        else
        {
            // Create forecast
            var st = new Language();
            st.UrduTitle = selectedObj.UrduTitle.Trim();
            st.EnglishTitle = selectedObj.EnglishTitle.Trim();
            st.CreatedDate = DateTime.Now;
            st.ModifiedDate = DateTime.Now;

            st.CreatedBy = user.Identity.Name;
            st.ModifiedBy = user.Identity.Name;
            st.Id = selectedObj.Id;
            //Service.CreateLanguage(st);
            var res = Service.UpdateLanguage(st);
            objList = await Service.GetList();
            ClearData();
            await dlgForm.HideAsync();
        }
    }

    private async Task DeleteRecord(int Id)
    {
        var res = await JSRuntime.InvokeAsync<bool>("confirm", "Are you want to delete this record?");
        if (res == false)
            return;

        //string msg = "";
        //bool hasError = false;

        try
        {
            var msg2 = await Service.DeleteLanguage(Id);
            objList = await Service.GetList();
            ClearData();
        }
        catch (Exception ee)
        {
            var mm = new ToastModel { Title = "Error", Content = ee.Message };
            if (ee.InnerException != null)
                mm.Content += " Detail: " + ee.InnerException.Message;

            await ToastObj.ShowAsync(mm);
        }
    }

    private async Task TranslateToUrdu()
    {
        try
        {
            if (string.IsNullOrEmpty(selectedObj.UrduTitle))
                selectedObj.UrduTitle = await Translation.TranslateText(selectedObj.EnglishTitle);
        }
        catch (Exception)
        {
            // ignored
        }

        //if (string.IsNullOrEmpty(selectedObj.UrduTitle))
        await Task.CompletedTask;
    }

    private void beforeOpen(BeforeOpenEventArgs args)
    {
        args.MaxHeight = null;
    }

}