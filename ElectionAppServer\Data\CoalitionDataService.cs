﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ElectionAppServer.DTO;
using ElectionAppServer.Model;
using Microsoft.EntityFrameworkCore;

namespace ElectionAppServer.Data;

public class CoalitionDataService
{
    private readonly IDbContextFactory<ApplicationDbContext> _contextFactory;

    public CoalitionDataService(IDbContextFactory<ApplicationDbContext> contextFactory)
    {
        _contextFactory = contextFactory;
    }

    public Task<List<GeneralItemDTO>> GetAllParties()
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.Parties
            orderby aa.EnglishTitle
            select new GeneralItemDTO
                { Id = aa.Id, EnglishTitle = aa.EnglishTitle, UrduTitle = aa.UrduTitle }).ToList();
        return Task.FromResult(q);
    }

    public Task<List<CoalitionDTO>> GetAllTeams(int electionId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.Coalitions
            //where aa.ElectionId == electionId
            orderby aa.EnglishTitle
            select new CoalitionDTO
            {
                Id = aa.Id,
                EnglishTitle = aa.EnglishTitle,
                UrduTitle = aa.UrduTitle,
                CreatedBy = aa.CreatedBy,
                CreatedOn = aa.CreatedDate.ToString("d MMM, yyyy h:mm"),
                ModifiedBy = aa.ModifiedBy ?? "",
                ModifiedOn = aa.ModifiedDate != null ? aa.ModifiedDate.Value.ToString("d MMM, yyyy h:mm") : ""
            }).Distinct().ToList();

        foreach (var item in q)
        {
            var parties = (from aa in dc.PartyCoalitions
                orderby aa.Party.ShortEnglishTitle
                where aa.ElectionId == electionId && aa.CoalitionId == item.Id
                select new { aa.Party.ShortEnglishTitle, aa.PartyId }).ToList();
            //item.PartyIds = new List<int>();
            item.Parties = "";
            item.TeamParties = new List<GeneralItemDTO>();

            foreach (var pp in parties)
            {
                item.TeamParties.Add(new GeneralItemDTO { Id = pp.PartyId, EnglishTitle = pp.ShortEnglishTitle });
                //item.PartyIds.Add(pp.PartyId);
                item.Parties += pp.ShortEnglishTitle + ", ";
            }

            item.Parties = item.Parties.Trim();
            if (item.Parties.Length > 1)
                item.Parties = item.Parties.Substring(0, item.Parties.Length - 1);
            //item.Parties = string.Join(", ", parties.);
        }

        return Task.FromResult(q);
    }

    public Task<CoalitionDTO> Save(CoalitionDTO obj, int electionId, string userId)
    {
        using var dc = _contextFactory.CreateDbContext();
        if (obj.Id == 0)
        {
            using var tran = dc.Database.BeginTransaction();
            try
            {
                var c = new Coalition
                {
                    CreatedBy = userId,
                    CreatedDate = DateTime.Now,
                    EnglishTitle = obj.EnglishTitle.Trim(),
                    UrduTitle = obj.UrduTitle.Trim()
                };
                dc.Coalitions.Add(c);
                dc.SaveChanges();

                foreach (var p in obj.TeamParties)
                {
                    var epc = new PartyCoalition { CoalitionId = c.Id, ElectionId = electionId, PartyId = p.Id };
                    dc.PartyCoalitions.Add(epc);
                    dc.SaveChanges();
                }

                tran.Commit();
                obj.Id = c.Id;
                return Task.FromResult(obj);
            }
            catch (Exception)
            {
                tran.Rollback();
                throw;
            }
        }

        using (var tran = dc.Database.BeginTransaction())
        {
            try
            {
                var c = (from aa in dc.Coalitions
                    where aa.Id == obj.Id
                    select aa).FirstOrDefault();
                if (c != null)
                {
                    c.EnglishTitle = obj.EnglishTitle.Trim();
                    c.ModifiedBy = userId;
                    c.ModifiedDate = DateTime.Now;
                    c.UrduTitle = obj.UrduTitle.Trim();
                    dc.SaveChanges();

                    var epcs = (from aa in dc.PartyCoalitions
                        where aa.ElectionId == electionId &&
                              aa.CoalitionId == obj.Id
                        select aa).ToList();
                    dc.PartyCoalitions.RemoveRange(epcs);
                    dc.SaveChanges();

                    foreach (var p in obj.TeamParties)
                    {
                        var epc = new PartyCoalition { CoalitionId = c.Id, ElectionId = electionId, PartyId = p.Id };
                        dc.PartyCoalitions.Add(epc);
                        dc.SaveChanges();
                    }
                }

                tran.Commit();
                return Task.FromResult(obj);
            }
            catch (Exception)
            {
                tran.Rollback();
                throw;
            }
        }
    }

    public Task<string> Delete(int coalitionId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var isExist = (from aa in dc.PartyCoalitions
            where aa.CoalitionId == coalitionId
            select aa).Any();
        if (isExist) throw new Exception("On or more parties are associated with this Team");
        var obj = dc.Coalitions.Find(coalitionId);
        if (obj != null) dc.Coalitions.Remove(obj);
        dc.SaveChanges();
        return Task.FromResult("OK");
    }
}