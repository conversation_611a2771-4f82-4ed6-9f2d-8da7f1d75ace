﻿using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;

namespace ElectionAppServer.Data;

public class ConfigurationReader
{
    private readonly IConfiguration configuration;

    public ConfigurationReader(IConfiguration config)
    {
        configuration = config;
    }

    public Task<string> GetSettingValue(string key)
    {
        var val = configuration.GetValue<string>(key);
        return Task.FromResult(val);
    }
}