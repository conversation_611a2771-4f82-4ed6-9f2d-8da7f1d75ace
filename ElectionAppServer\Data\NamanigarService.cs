﻿using Microsoft.EntityFrameworkCore;

namespace ElectionAppServer.Data;

public class UserService
{
    private readonly IDbContextFactory<ApplicationDbContext> _contextFactory; // private  ApplicationDbContext  dc;

    //public UserService(ApplicationDbContext _dc)
    public UserService(IDbContextFactory<ApplicationDbContext> contextFactory)
    {
        //this.dc = _dc;
        _contextFactory = contextFactory;
    }

    //public Task<List<UserConstituencyDTO>> GetUserConst()
    //{
    //}
}