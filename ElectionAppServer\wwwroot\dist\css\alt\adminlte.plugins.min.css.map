{"version": 3, "sources": ["..\\..\\..\\build\\scss\\AdminLTE-plugins.scss", "..\\..\\..\\build\\scss\\plugins\\_fullcalendar.scss", "..\\..\\..\\build\\scss\\_bootstrap-variables.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\mixins\\_breakpoints.scss", "..\\..\\..\\build\\scss\\mixins\\_miscellaneous.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\mixins\\_box-shadow.scss", "..\\..\\..\\build\\scss\\_variables.scss", "..\\..\\..\\build\\scss\\plugins\\_select2.scss", "..\\..\\..\\build\\scss\\plugins\\_mixins.scss", "..\\..\\..\\build\\scss\\plugins\\_bootstrap-slider.scss", "..\\..\\..\\build\\scss\\plugins\\_icheck-bootstrap.scss", "..\\..\\..\\build\\scss\\plugins\\_mapael.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\mixins\\_reset-text.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\mixins\\_border-radius.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\vendor\\_rfs.scss", "..\\..\\..\\build\\scss\\plugins\\_jqvmap.scss", "..\\..\\..\\build\\scss\\plugins\\_sweetalert2.scss", "..\\..\\..\\build\\scss\\plugins\\_toastr.scss", "..\\..\\..\\build\\scss\\plugins\\_pace.scss", "..\\..\\..\\build\\scss\\plugins\\_bootstrap-switch.scss", "..\\..\\..\\build\\scss\\plugins\\_miscellaneous.scss"], "names": [], "mappings": "AAAA;;;;;;ACKA,WACE,WCMS,QDLT,iBAAkB,KAClB,oBAAqB,KACrB,aAAc,KACd,MCQS,QDbD,iBAAA,kBAAA,iBAUN,iBAAkB,QAKL,oBACf,MAAO,KACP,UAAW,KACX,YAAa,MACb,YAAa,KAGf,iBACE,cAAe,KAGjB,gBACE,aAAc,KAIhB,kBACE,WAAY,QAGd,SACE,OAAQ,EACR,MAAO,KAIS,iCADD,gCAEf,YAAa,EACb,aAAc,EAIE,gCADD,+BAEf,aAAc,EAGhB,YACW,8BACT,OAAQ,EACR,QAAS,KEcoB,4BFV7B,YACE,eAAgB,OAEhB,qBACE,MAAO,EACP,cAAe,MAGjB,uBACE,MAAO,EACP,cAAe,QAGjB,sBACE,MAAO,GAKb,eACE,UAAW,KACX,YAAa,IACb,cAAe,KAGjB,iBACE,WAAY,KACZ,OAAQ,EACR,QAAS,EAEP,oBACA,MAAO,KACP,UAAW,KACX,YAAa,KACb,aAAc,IAEd,wBAGA,yBADA,yBADA,yBAGA,+BACA,yBACE,WAAY,UAAA,OAAA,IANX,8BAGC,+BADA,+BADA,+BAGM,qCACN,+BGrFN,UAAW,cH+Fb,eACE,WAAY,IAAA,OAAA,IAGd,gBItGM,WCoIQ,EAAE,EAAE,IAAI,gBAAmB,CAAE,EAAE,IAAI,IAAI,eL3BnD,cC4F4B,OD3F5B,OAAQ,KACR,YAAa,IACb,cAAe,IACf,QAAS,IAAA,KAPI,sBItGT,WJgHkB,MAAM,EAAE,EAAE,KAAK,eMjHrC,uDACE,OLkM0B,IKlME,MLDrB,QKGP,QAAS,ULuTiB,OKtT1B,OLqZoC,oBKjZpC,+EACE,aAAc,QAIhB,8CACA,OLqL0B,IKrLE,MLdrB,QKkBP,qDACA,QAAS,IAAA,KACT,YAAa,KACb,oBAAqB,KAGM,oFAC3B,aAAc,EAEd,OAAQ,KACR,WAAY,KAG0B,6FACtC,cAAe,IACf,aAAc,KAGa,iFAC3B,OAAQ,KACR,MAAO,IAG8C,mFACrD,WAAY,EAKZ,qEAAA,2EACE,OLmJwB,IKnJI,MLhDvB,QK+Ce,2EAAA,iFAIlB,QAAS,EACT,OL+IsB,IK/IM,MLwVI,QKnVrB,sEAEb,WAAY,EAFC,sEAMb,cAAe,EAKf,yEACA,MLnEK,QKsEL,yEAGA,iBL5EK,QKyEL,yEAMC,+EACC,MLDQ,QKMd,kEAEE,iBLjEM,QKkEN,ML3FO,KK6FL,iFAIC,uFACC,iBAJM,QAKN,MLnGG,KK0GP,yDACE,OL4FwB,IK5FI,MLvGvB,QKwGL,WLiTkC,oBKnTR,+DAKxB,aLiSgC,QK9RlC,sFACE,QAAS,EAAE,QL0MW,QKzMtB,cAAe,SAEc,2IAC3B,MAAO,KACP,YAAa,QAEb,kKACE,MAAO,eAMT,oJACE,OAAQ,EACR,WAAY,IAKlB,oFACE,iBLhHE,QKiHF,aAAc,QACd,ML3IG,KK4IH,QAAS,EAAA,KACT,WAAY,OAGd,4FACE,MAAO,qBACP,MAAO,MACP,YAAa,IACb,aAAc,KAJkB,kGAO9B,MLvJC,KK8JD,+HAAA,gIACE,WAAY,IAIhB,4FAAA,6FACE,WAAY,MAOhB,kFADA,gFAEE,aLoOgC,QKjOlC,4EACE,OAAQ,EAK4C,uFACxD,cAAe,KAIf,oEACE,0BAA2B,EAC3B,uBAAwB,EAK1B,6EACE,2BAA4B,EAC5B,wBAAyB,EAOF,2EACzB,WAAY,KAMd,mDACE,ULkB0B,QKX1B,gEAAA,8EACE,OLsMkC,sBKpMlC,6FAAA,2GACE,WAAY,OAGd,0FAAA,wGACE,IAAK,QAIT,kEAAA,gFACE,WL0LkC,sBKxLlC,+FAAA,6GACE,QAAS,EAAE,OL4FW,OK3FtB,WAAc,OAEe,oJAAA,kKAC3B,YAAa,OAIb,6JAAA,2KACE,WAAY,IASR,kCACd,QAAS,KC7PH,gGACE,aAAc,QAIS,iGACzB,aAAc,QASQ,4FAAA,kGAAA,2FAAA,4FAAA,kGAAA,2FAElB,ONuLkB,IMvLU,MAAM,QAKxC,mFAAA,mFACE,iBNGE,QMFF,MNvBG,KMyBD,kGAEC,wGAFD,kGAEC,wGACC,iBAAkB,QAClB,MN7BD,KMoCyB,gFAAA,gFAExB,aAAc,QAGhB,qGAAA,qGACE,iBNjBF,QMkBE,aAAc,QACd,MN5CD,KM+CD,6GAAA,6GACE,MNhDD,qBM+CiC,mHAAA,mHAI9B,MNnDH,KMwDwB,mGAAA,mGACzB,aAAc,QA1DhB,kGACE,aAAc,QAIS,mGACzB,aAAc,QASQ,8FAAA,oGAAA,6FAAA,8FAAA,oGAAA,6FAElB,ONuLkB,IMvLU,MAAM,QAKxC,qFAAA,qFACE,iBNhBG,QMiBH,MNvBG,KMyBD,oGAEC,0GAFD,oGAEC,0GACC,iBAAkB,QAClB,MN7BD,KMoCyB,kFAAA,kFAExB,aAAc,QAGhB,uGAAA,uGACE,iBNpCD,QMqCC,aAAc,QACd,MN5CD,KM+CD,+GAAA,+GACE,MNhDD,qBM+CiC,qHAAA,qHAI9B,MNnDH,KMwDwB,qGAAA,qGACzB,aAAc,QA1DhB,gGACE,aAAc,QAIS,iGACzB,aAAc,QASQ,4FAAA,kGAAA,2FAAA,4FAAA,kGAAA,2FAElB,ONuLkB,IMvLU,MAAM,QAKxC,mFAAA,mFACE,iBNUE,QMTF,MNvBG,KMyBD,kGAEC,wGAFD,kGAEC,wGACC,iBAAkB,QAClB,MN7BD,KMoCyB,gFAAA,gFAExB,aAAc,QAGhB,qGAAA,qGACE,iBNVF,QMWE,aAAc,QACd,MN5CD,KM+CD,6GAAA,6GACE,MNhDD,qBM+CiC,mHAAA,mHAI9B,MNnDH,KMwDwB,mGAAA,mGACzB,aAAc,QA1DhB,6FACE,aAAc,QAIS,8FACzB,aAAc,QASQ,yFAAA,+FAAA,wFAAA,yFAAA,+FAAA,wFAElB,ONuLkB,IMvLU,MAAM,QAKxC,gFAAA,gFACE,iBNYE,QMXF,MNvBG,KMyBD,+FAEC,qGAFD,+FAEC,qGACC,iBAAkB,QAClB,MN7BD,KMoCyB,6EAAA,6EAExB,aAAc,QAGhB,kGAAA,kGACE,iBNRF,QMSE,aAAc,QACd,MN5CD,KM+CD,0GAAA,0GACE,MNhDD,qBM+CiC,gHAAA,gHAI9B,MNnDH,KMwDwB,gGAAA,gGACzB,aAAc,QA1DhB,gGACE,aAAc,QAIS,iGACzB,aAAc,QASQ,4FAAA,kGAAA,2FAAA,4FAAA,kGAAA,2FAElB,ONuLkB,IMvLU,MAAM,QAKxC,mFAAA,mFACE,iBNSE,QMRF,MN2DQ,QMzDN,kGAEC,wGAFD,kGAEC,wGACC,iBAAkB,QAClB,MNqDI,QM9CoB,gFAAA,gFAExB,aAAc,QAGhB,qGAAA,qGACE,iBNXF,QMYE,aAAc,QACd,MNsCI,QMnCN,6GAAA,6GACE,MNkCI,kBMnC4B,mHAAA,mHAI9B,MN+BE,QM1BmB,mGAAA,mGACzB,aAAc,QA1DhB,+FACE,aAAc,QAIS,gGACzB,aAAc,QASQ,2FAAA,iGAAA,0FAAA,2FAAA,iGAAA,0FAElB,ONuLkB,IMvLU,MAAM,QAKxC,kFAAA,kFACE,iBNOE,QMNF,MNvBG,KMyBD,iGAEC,uGAFD,iGAEC,uGACC,iBAAkB,QAClB,MN7BD,KMoCyB,+EAAA,+EAExB,aAAc,QAGhB,oGAAA,oGACE,iBNbF,QMcE,aAAc,QACd,MN5CD,KM+CD,4GAAA,4GACE,MNhDD,qBM+CiC,kHAAA,kHAI9B,MNnDH,KMwDwB,kGAAA,kGACzB,aAAc,QA1DhB,8FACE,aAAc,KAIS,+FACzB,aAAc,KASQ,0FAAA,gGAAA,yFAAA,0FAAA,gGAAA,yFAElB,ONuLkB,IMvLU,MAAM,KAKxC,iFAAA,iFACE,iBNrBG,QMsBH,MN2DQ,QMzDN,gGAEC,sGAFD,gGAEC,sGACC,iBAAkB,QAClB,MNqDI,QM9CoB,8EAAA,8EAExB,aAAc,KAGhB,mGAAA,mGACE,iBNzCD,QM0CC,aAAc,QACd,MNsCI,QMnCN,2GAAA,2GACE,MNkCI,kBMnC4B,iHAAA,iHAI9B,MN+BE,QM1BmB,iGAAA,iGACzB,aAAc,KA1DhB,6FACE,aAAc,QAIS,8FACzB,aAAc,QASQ,yFAAA,+FAAA,wFAAA,yFAAA,+FAAA,wFAElB,ONuLkB,IMvLU,MAAM,QAKxC,gFAAA,gFACE,iBNdG,QMeH,MNvBG,KMyBD,+FAEC,qGAFD,+FAEC,qGACC,iBAAkB,QAClB,MN7BD,KMoCyB,6EAAA,6EAExB,aAAc,QAGhB,kGAAA,kGACE,iBNlCD,QMmCC,aAAc,QACd,MN5CD,KM+CD,0GAAA,0GACE,MNhDD,qBM+CiC,gHAAA,gHAI9B,MNnDH,KMwDwB,gGAAA,gGACzB,aAAc,QA1DhB,kGACE,aAAc,QAIS,mGACzB,aAAc,QASQ,8FAAA,oGAAA,6FAAA,8FAAA,oGAAA,6FAElB,ONuLkB,IMvLU,MAAM,QAKxC,qFAAA,qFACE,iBF1BI,QE2BJ,MNvBG,KMyBD,oGAEC,0GAFD,oGAEC,0GACC,iBAAkB,QAClB,MN7BD,KMoCyB,kFAAA,kFAExB,aAAc,QAGhB,uGAAA,uGACE,iBF9CA,QE+CA,aAAc,QACd,MN5CD,KM+CD,+GAAA,+GACE,MNhDD,qBM+CiC,qHAAA,qHAI9B,MNnDH,KMwDwB,qGAAA,qGACzB,aAAc,QA1DhB,6FACE,aAAc,QAIS,8FACzB,aAAc,QASQ,yFAAA,+FAAA,wFAAA,yFAAA,+FAAA,wFAElB,ONuLkB,IMvLU,MAAM,QAKxC,gFAAA,gFACE,iBFzBD,QE0BC,MNvBG,KMyBD,+FAEC,qGAFD,+FAEC,qGACC,iBAAkB,QAClB,MN7BD,KMoCyB,6EAAA,6EAExB,aAAc,QAGhB,kGAAA,kGACE,iBF7CL,QE8CK,aAAc,QACd,MN5CD,KM+CD,0GAAA,0GACE,MNhDD,qBM+CiC,gHAAA,gHAI9B,MNnDH,KMwDwB,gGAAA,gGACzB,aAAc,QA1DhB,8FACE,aAAc,QAIS,+FACzB,aAAc,QASQ,0FAAA,gGAAA,yFAAA,0FAAA,gGAAA,yFAElB,ONuLkB,IMvLU,MAAM,QAKxC,iFAAA,iFACE,iBFvBA,QEwBA,MNvBG,KMyBD,gGAEC,sGAFD,gGAEC,sGACC,iBAAkB,QAClB,MN7BD,KMoCyB,8EAAA,8EAExB,aAAc,QAGhB,mGAAA,mGACE,iBF3CJ,QE4CI,aAAc,QACd,MN5CD,KM+CD,2GAAA,2GACE,MNhDD,qBM+CiC,iHAAA,iHAI9B,MNnDH,KMwDwB,iGAAA,iGACzB,aAAc,QA1DhB,6FACE,aAAc,QAIS,8FACzB,aAAc,QASQ,yFAAA,+FAAA,wFAAA,yFAAA,+FAAA,wFAElB,ONuLkB,IMvLU,MAAM,QAKxC,gFAAA,gFACE,iBFtBD,QEuBC,MN2DQ,QMzDN,+FAEC,qGAFD,+FAEC,qGACC,iBAAkB,QAClB,MNqDI,QM9CoB,6EAAA,6EAExB,aAAc,QAGhB,kGAAA,kGACE,iBF1CL,QE2CK,aAAc,QACd,MNsCI,QMnCN,0GAAA,0GACE,MNkCI,kBMnC4B,gHAAA,gHAI9B,MN+BE,QM1BmB,gGAAA,gGACzB,aAAc,QA1DhB,gGACE,aAAc,QAIS,iGACzB,aAAc,QASQ,4FAAA,kGAAA,2FAAA,4FAAA,kGAAA,2FAElB,ONuLkB,IMvLU,MAAM,QAKxC,mFAAA,mFACE,iBFpBE,QEqBF,MNvBG,KMyBD,kGAEC,wGAFD,kGAEC,wGACC,iBAAkB,QAClB,MN7BD,KMoCyB,gFAAA,gFAExB,aAAc,QAGhB,qGAAA,qGACE,iBFxCF,QEyCE,aAAc,QACd,MN5CD,KM+CD,6GAAA,6GACE,MNhDD,qBM+CiC,mHAAA,mHAI9B,MNnDH,KMwDwB,mGAAA,mGACzB,aAAc,QA1DhB,+FACE,aAAc,QAIS,gGACzB,aAAc,QASQ,2FAAA,iGAAA,0FAAA,2FAAA,iGAAA,0FAElB,ONuLkB,IMvLU,MAAM,QAKxC,kFAAA,kFACE,iBFlBC,QEmBD,MNvBG,KMyBD,iGAEC,uGAFD,iGAEC,uGACC,iBAAkB,QAClB,MN7BD,KMoCyB,+EAAA,+EAExB,aAAc,QAGhB,oGAAA,oGACE,iBFtCH,QEuCG,aAAc,QACd,MN5CD,KM+CD,4GAAA,4GACE,MNhDD,qBM+CiC,kHAAA,kHAI9B,MNnDH,KMwDwB,kGAAA,kGACzB,aAAc,QA1DhB,6FACE,aAAc,QAIS,8FACzB,aAAc,QASQ,yFAAA,+FAAA,wFAAA,yFAAA,+FAAA,wFAElB,ONuLkB,IMvLU,MAAM,QAKxC,gFAAA,gFACE,iBNGE,QMFF,MNvBG,KMyBD,+FAEC,qGAFD,+FAEC,qGACC,iBAAkB,QAClB,MN7BD,KMoCyB,6EAAA,6EAExB,aAAc,QAGhB,kGAAA,kGACE,iBNjBF,QMkBE,aAAc,QACd,MN5CD,KM+CD,0GAAA,0GACE,MNhDD,qBM+CiC,gHAAA,gHAI9B,MNnDH,KMwDwB,gGAAA,gGACzB,aAAc,QA1DhB,+FACE,aAAc,QAIS,gGACzB,aAAc,QASQ,2FAAA,iGAAA,0FAAA,2FAAA,iGAAA,0FAElB,ONuLkB,IMvLU,MAAM,QAKxC,kFAAA,kFACE,iBNIE,QMHF,MNvBG,KMyBD,iGAEC,uGAFD,iGAEC,uGACC,iBAAkB,QAClB,MN7BD,KMoCyB,+EAAA,+EAExB,aAAc,QAGhB,oGAAA,oGACE,iBNhBF,QMiBE,aAAc,QACd,MN5CD,KM+CD,4GAAA,4GACE,MNhDD,qBM+CiC,kHAAA,kHAI9B,MNnDH,KMwDwB,kGAAA,kGACzB,aAAc,QA1DhB,+FACE,aAAc,QAIS,gGACzB,aAAc,QASQ,2FAAA,iGAAA,0FAAA,2FAAA,iGAAA,0FAElB,ONuLkB,IMvLU,MAAM,QAKxC,kFAAA,kFACE,iBNKE,QMJF,MNvBG,KMyBD,iGAEC,uGAFD,iGAEC,uGACC,iBAAkB,QAClB,MN7BD,KMoCyB,+EAAA,+EAExB,aAAc,QAGhB,oGAAA,oGACE,iBNfF,QMgBE,aAAc,QACd,MN5CD,KM+CD,4GAAA,4GACE,MNhDD,qBM+CiC,kHAAA,kHAI9B,MNnDH,KMwDwB,kGAAA,kGACzB,aAAc,QA1DhB,6FACE,aAAc,QAIS,8FACzB,aAAc,QASQ,yFAAA,+FAAA,wFAAA,yFAAA,+FAAA,wFAElB,ONuLkB,IMvLU,MAAM,QAKxC,gFAAA,gFACE,iBNME,QMLF,MNvBG,KMyBD,+FAEC,qGAFD,+FAEC,qGACC,iBAAkB,QAClB,MN7BD,KMoCyB,6EAAA,6EAExB,aAAc,QAGhB,kGAAA,kGACE,iBNdF,QMeE,aAAc,QACd,MN5CD,KM+CD,0GAAA,0GACE,MNhDD,qBM+CiC,gHAAA,gHAI9B,MNnDH,KMwDwB,gGAAA,gGACzB,aAAc,QA1DhB,4FACE,aAAc,QAIS,6FACzB,aAAc,QASQ,wFAAA,8FAAA,uFAAA,wFAAA,8FAAA,uFAElB,ONuLkB,IMvLU,MAAM,QAKxC,+EAAA,+EACE,iBNOE,QMNF,MNvBG,KMyBD,8FAEC,oGAFD,8FAEC,oGACC,iBAAkB,QAClB,MN7BD,KMoCyB,4EAAA,4EAExB,aAAc,QAGhB,iGAAA,iGACE,iBNbF,QMcE,aAAc,QACd,MN5CD,KM+CD,yGAAA,yGACE,MNhDD,qBM+CiC,+GAAA,+GAI9B,MNnDH,KMwDwB,+FAAA,+FACzB,aAAc,QA1DhB,+FACE,aAAc,QAIS,gGACzB,aAAc,QASQ,2FAAA,iGAAA,0FAAA,2FAAA,iGAAA,0FAElB,ONuLkB,IMvLU,MAAM,QAKxC,kFAAA,kFACE,iBNQE,QMPF,MN2DQ,QMzDN,iGAEC,uGAFD,iGAEC,uGACC,iBAAkB,QAClB,MN7BD,KMoCyB,+EAAA,+EAExB,aAAc,QAGhB,oGAAA,oGACE,iBNZF,QMaE,aAAc,QACd,MNsCI,QMnCN,4GAAA,4GACE,MNkCI,kBMnC4B,kHAAA,kHAI9B,MN+BE,QM1BmB,kGAAA,kGACzB,aAAc,QA1DhB,+FACE,aAAc,QAIS,gGACzB,aAAc,QASQ,2FAAA,iGAAA,0FAAA,2FAAA,iGAAA,0FAElB,ONuLkB,IMvLU,MAAM,QAKxC,kFAAA,kFACE,iBNSE,QMRF,MN2DQ,QMzDN,iGAEC,uGAFD,iGAEC,uGACC,iBAAkB,QAClB,MNqDI,QM9CoB,+EAAA,+EAExB,aAAc,QAGhB,oGAAA,oGACE,iBNXF,QMYE,aAAc,QACd,MNsCI,QMnCN,4GAAA,4GACE,MNkCI,kBMnC4B,kHAAA,kHAI9B,MN+BE,QM1BmB,kGAAA,kGACzB,aAAc,QA1DhB,8FACE,aAAc,QAIS,+FACzB,aAAc,QASQ,0FAAA,gGAAA,yFAAA,0FAAA,gGAAA,yFAElB,ONuLkB,IMvLU,MAAM,QAKxC,iFAAA,iFACE,iBNUE,QMTF,MNvBG,KMyBD,gGAEC,sGAFD,gGAEC,sGACC,iBAAkB,QAClB,MN7BD,KMoCyB,8EAAA,8EAExB,aAAc,QAGhB,mGAAA,mGACE,iBNVF,QMWE,aAAc,QACd,MN5CD,KM+CD,2GAAA,2GACE,MNhDD,qBM+CiC,iHAAA,iHAI9B,MNnDH,KMwDwB,iGAAA,iGACzB,aAAc,QA1DhB,6FACE,aAAc,QAIS,8FACzB,aAAc,QASQ,yFAAA,+FAAA,wFAAA,yFAAA,+FAAA,wFAElB,ONuLkB,IMvLU,MAAM,QAKxC,gFAAA,gFACE,iBNWE,QMVF,MNvBG,KMyBD,+FAEC,qGAFD,+FAEC,qGACC,iBAAkB,QAClB,MN7BD,KMoCyB,6EAAA,6EAExB,aAAc,QAGhB,kGAAA,kGACE,iBNTF,QMUE,aAAc,QACd,MN5CD,KM+CD,0GAAA,0GACE,MNhDD,qBM+CiC,gHAAA,gHAI9B,MNnDH,KMwDwB,gGAAA,gGACzB,aAAc,QA1DhB,6FACE,aAAc,QAIS,8FACzB,aAAc,QASQ,yFAAA,+FAAA,wFAAA,yFAAA,+FAAA,wFAElB,ONuLkB,IMvLU,MAAM,QAKxC,gFAAA,gFACE,iBNYE,QMXF,MNvBG,KMyBD,+FAEC,qGAFD,+FAEC,qGACC,iBAAkB,QAClB,MN7BD,KMoCyB,6EAAA,6EAExB,aAAc,QAGhB,kGAAA,kGACE,iBNRF,QMSE,aAAc,QACd,MN5CD,KM+CD,0GAAA,0GACE,MNhDD,qBM+CiC,gHAAA,gHAI9B,MNnDH,KMwDwB,gGAAA,gGACzB,aAAc,QA1DhB,8FACE,aAAc,KAIS,+FACzB,aAAc,KASQ,0FAAA,gGAAA,yFAAA,0FAAA,gGAAA,yFAElB,ONuLkB,IMvLU,MAAM,KAKxC,iFAAA,iFACE,iBNtBG,KMuBH,MN2DQ,QMzDN,gGAEC,sGAFD,gGAEC,sGACC,iBAAkB,QAClB,MNqDI,QM9CoB,8EAAA,8EAExB,aAAc,KAGhB,mGAAA,mGACE,iBN1CD,KM2CC,aAAc,QACd,MNsCI,QMnCN,2GAAA,2GACE,MNkCI,kBMnC4B,iHAAA,iHAI9B,MN+BE,QM1BmB,iGAAA,iGACzB,aAAc,KA1DhB,6FACE,aAAc,QAIS,8FACzB,aAAc,QASQ,yFAAA,+FAAA,wFAAA,yFAAA,+FAAA,wFAElB,ONuLkB,IMvLU,MAAM,QAKxC,gFAAA,gFACE,iBNhBG,QMiBH,MNvBG,KMyBD,+FAEC,qGAFD,+FAEC,qGACC,iBAAkB,QAClB,MN7BD,KMoCyB,6EAAA,6EAExB,aAAc,QAGhB,kGAAA,kGACE,iBNpCD,QMqCC,aAAc,QACd,MN5CD,KM+CD,0GAAA,0GACE,MNhDD,qBM+CiC,gHAAA,gHAI9B,MNnDH,KMwDwB,gGAAA,gGACzB,aAAc,QA1DhB,kGACE,aAAc,QAIS,mGACzB,aAAc,QASQ,8FAAA,oGAAA,6FAAA,8FAAA,oGAAA,6FAElB,ONuLkB,IMvLU,MAAM,QAKxC,qFAAA,qFACE,iBNdG,QMeH,MNvBG,KMyBD,oGAEC,0GAFD,oGAEC,0GACC,iBAAkB,QAClB,MN7BD,KMoCyB,kFAAA,kFAExB,aAAc,QAGhB,uGAAA,uGACE,iBNlCD,QMmCC,aAAc,QACd,MN5CD,KM+CD,+GAAA,+GACE,MNhDD,qBM+CiC,qHAAA,qHAI9B,MNnDH,KMwDwB,qGAAA,qGACzB,aAAc,QC/DR,oBACd,QP8qB4B,GO1qBvB,wBAEH,OAAQ,KAFL,0BAKH,MAAO,KAOP,0CACE,WPaI,QOdN,4CACE,WPNK,QOKP,0CACE,WPoBI,QOrBN,uCACE,WPsBI,QOvBN,0CACE,WPmBI,QOpBN,yCACE,WPiBI,QOlBN,wCACE,WPXK,QOUP,uCACE,WPJK,QOWP,4CACE,WHxBM,QGuBR,uCACE,WHvBC,QGsBH,wCACE,WHrBE,QGoBJ,uCACE,WHpBC,QGmBH,0CACE,WHlBI,QGiBN,yCACE,WHhBG,QGeL,uCACE,WPKI,QONN,yCACE,WPMI,QOPN,yCACE,WPOI,QORN,uCACE,WPQI,QOTN,sCACE,WPSI,QOVN,yCACE,WPUI,QOXN,yCACE,WPWI,QOZN,wCACE,WPYI,QObN,uCACE,WPaI,QOdN,uCACE,WPcI,QOfN,wCACE,WPpBK,KOmBP,uCACE,WPdK,QOaP,4CACE,WPZK,QQZ4F,sGADvB,mFAE5E,aAAa,QAIsF,sGADvB,mFAE5E,aAAa,QAI2D,2EADvB,wDAEjD,iBAAiB,QACjB,aAAa,QAZwF,wGADvB,qFAE9E,aAAa,QAIwF,wGADvB,qFAE9E,aAAa,QAI6D,6EADvB,0DAEnD,iBAAiB,QACjB,aAAa,QAZsF,sGADvB,mFAE5E,aAAa,QAIsF,sGADvB,mFAE5E,aAAa,QAI2D,2EADvB,wDAEjD,iBAAiB,QACjB,aAAa,QAZmF,mGADvB,gFAEzE,aAAa,QAImF,mGADvB,gFAEzE,aAAa,QAIwD,wEADvB,qDAE9C,iBAAiB,QACjB,aAAa,QAZsF,sGADvB,mFAE5E,aAAa,QAIsF,sGADvB,mFAE5E,aAAa,QAI2D,2EADvB,wDAEjD,iBAAiB,QACjB,aAAa,QAZqF,qGADvB,kFAE3E,aAAa,QAIqF,qGADvB,kFAE3E,aAAa,QAI0D,0EADvB,uDAEhD,iBAAiB,QACjB,aAAa,QAZoF,oGADvB,iFAE1E,aAAa,QAIoF,oGADvB,iFAE1E,aAAa,QAIyD,yEADvB,sDAE/C,iBAAiB,QACjB,aAAa,QAZmF,mGADvB,gFAEzE,aAAa,QAImF,mGADvB,gFAEzE,aAAa,QAIwD,wEADvB,qDAE9C,iBAAiB,QACjB,aAAa,QAOwF,wGADvB,qFAE9E,aAAa,QAIwF,wGADvB,qFAE9E,aAAa,QAI6D,6EADvB,0DAEnD,iBAAiB,QACjB,aAAa,QAZmF,mGADvB,gFAEzE,aAAa,QAImF,mGADvB,gFAEzE,aAAa,QAIwD,wEADvB,qDAE9C,iBAAiB,QACjB,aAAa,QAZoF,oGADvB,iFAE1E,aAAa,QAIoF,oGADvB,iFAE1E,aAAa,QAIyD,yEADvB,sDAE/C,iBAAiB,QACjB,aAAa,QAZmF,mGADvB,gFAEzE,aAAa,QAImF,mGADvB,gFAEzE,aAAa,QAIwD,wEADvB,qDAE9C,iBAAiB,QACjB,aAAa,QAZsF,sGADvB,mFAE5E,aAAa,QAIsF,sGADvB,mFAE5E,aAAa,QAI2D,2EADvB,wDAEjD,iBAAiB,QACjB,aAAa,QAZqF,qGADvB,kFAE3E,aAAa,QAIqF,qGADvB,kFAE3E,aAAa,QAI0D,0EADvB,uDAEhD,iBAAiB,QACjB,aAAa,QAZmF,mGADvB,gFAEzE,aAAa,QAImF,mGADvB,gFAEzE,aAAa,QAIwD,wEADvB,qDAE9C,iBAAiB,QACjB,aAAa,QAZqF,qGADvB,kFAE3E,aAAa,QAIqF,qGADvB,kFAE3E,aAAa,QAI0D,0EADvB,uDAEhD,iBAAiB,QACjB,aAAa,QAZqF,qGADvB,kFAE3E,aAAa,QAIqF,qGADvB,kFAE3E,aAAa,QAI0D,0EADvB,uDAEhD,iBAAiB,QACjB,aAAa,QAZmF,mGADvB,gFAEzE,aAAa,QAImF,mGADvB,gFAEzE,aAAa,QAIwD,wEADvB,qDAE9C,iBAAiB,QACjB,aAAa,QAZkF,kGADvB,+EAExE,aAAa,QAIkF,kGADvB,+EAExE,aAAa,QAIuD,uEADvB,oDAE7C,iBAAiB,QACjB,aAAa,QAZqF,qGADvB,kFAE3E,aAAa,QAIqF,qGADvB,kFAE3E,aAAa,QAI0D,0EADvB,uDAEhD,iBAAiB,QACjB,aAAa,QAZqF,qGADvB,kFAE3E,aAAa,QAIqF,qGADvB,kFAE3E,aAAa,QAI0D,0EADvB,uDAEhD,iBAAiB,QACjB,aAAa,QAZoF,oGADvB,iFAE1E,aAAa,QAIoF,oGADvB,iFAE1E,aAAa,QAIyD,yEADvB,sDAE/C,iBAAiB,QACjB,aAAa,QAZmF,mGADvB,gFAEzE,aAAa,QAImF,mGADvB,gFAEzE,aAAa,QAIwD,wEADvB,qDAE9C,iBAAiB,QACjB,aAAa,QAZmF,mGADvB,gFAEzE,aAAa,QAImF,mGADvB,gFAEzE,aAAa,QAIwD,wEADvB,qDAE9C,iBAAiB,QACjB,aAAa,QAZoF,oGADvB,iFAE1E,aAAa,KAIoF,oGADvB,iFAE1E,aAAa,KAIyD,yEADvB,sDAE/C,iBAAiB,KACjB,aAAa,KAZmF,mGADvB,gFAEzE,aAAa,QAImF,mGADvB,gFAEzE,aAAa,QAIwD,wEADvB,qDAE9C,iBAAiB,QACjB,aAAa,QAZwF,wGADvB,qFAE9E,aAAa,QAIwF,wGADvB,qFAE9E,aAAa,QAI6D,6EADvB,0DAEnD,iBAAiB,QACjB,aAAa,QCjCf,aACE,SAAU,SAGZ,oBCRA,YVuO4B,iBAAiB,CAAE,aAAa,CAAE,kBAAkB,CAAE,UAAU,CAAE,MAAM,CAAE,gBAAgB,CAAE,KAAK,CAAE,UAAU,CAAE,mBAAmB,CAAE,gBAAgB,CAAE,kBUrOlL,WAAY,OACZ,YV8O4B,IU7O5B,YViP4B,IUhP5B,WAAY,KACZ,WAAY,MACZ,gBAAiB,KACjB,YAAa,KACb,eAAgB,KAChB,eAAgB,OAChB,WAAY,OACZ,aAAc,OACd,YAAa,OACb,WAAY,KCVV,cXgN0B,OYtFxB,UAtCW,QH5Eb,iBTQO,KSPP,MTHO,KSIP,QAAS,MACT,UTgqB0B,MS/pB1B,QToqB0B,OACA,MSpqB1B,SAAU,SACV,WAAY,OACZ,UAAW,WACX,QT2iBgC,KSxiBlC,kBACE,iBTbO,QScP,OAAQ,IAAI,MTVL,QSWP,QAAS,KACT,MAAO,MAGT,oBACE,iBTpBO,QSqBP,OAAQ,IAAI,MLsKc,KKrK1B,cTmL0B,OSlL1B,MLmKmB,KKlKnB,OAAQ,QACR,YAAa,IACb,OAAQ,KACR,KAAM,KACN,YAAa,KACb,aAAc,IACd,SAAU,SACV,WAAY,OACZ,IAAK,EAEL,YAAa,KACb,MAAO,KAhBE,0BAAA,2BAAA,0BAqBP,iBAAkB,QAClB,MAAO,QAIX,mBACE,YAAa,KACb,IAAK,KAGP,gBACE,IAAK,KAGP,iBACE,IAAK,KI9DT,eACA,gBACE,iBbKS,QaJT,OAAQ,IAAI,MT+LgB,KS9L5B,cb4M4B,Oa3M5B,MT4LqB,KS3LrB,OAAQ,KACR,MAAO,KAPK,qBAAA,sBAAA,qBACC,sBAAA,uBAAA,sBAWX,iBAAkB,QAClB,MAAO,QCbA,uBAEP,aAAc,qBACd,MdqCM,QcxCC,0BAOP,aAAc,qBACd,Md6BM,QcrCC,wBAYP,aAAc,qBACd,MdsBM,QcnCC,2BAiBP,aAAc,qBACd,MdNO,QcZA,0BAsBP,aAAc,qBACd,MdeM,QcbN,8CACE,aAAc,qBAGf,sDACC,iBdQI,QeTR,wBACE,iBfCM,QeER,gCACE,iBfIM,QeDR,8BACE,iBfHM,QeMR,6BACE,iBfFM,QeKR,gCACE,iBfTM,Qece,gCACH,6BACpB,UAAW,QCtDb,MACE,QhBujBkC,KgBrjBlC,qBACE,QhBojBgC,KgBjjBlC,qBACE,QhBgjBgC,KgBxiB9B,mCACE,WhBeE,QgBTN,gCACE,WhBjBK,KgBmBL,+CACE,WhBKE,QgBFJ,+CACE,iBAAkB,8JAON,sDACZ,MhBPE,mBgBcJ,0CACE,WhBfE,QgBqBN,yCACE,OAAQ,MACR,MAAO,KAFK,iDAKV,WhB1BE,QgB2BF,MhBpDG,KgBqDH,UAAW,MACX,YAAa,MACb,YAAa,IAIjB,yCACE,ahBnCI,QgBkCQ,gDAAA,iDAKV,ahBvCE,QgB8CJ,iDACE,WhB/CE,mBgBgDF,MhBzEG,KgBgFL,gDACE,ahBxDE,QgBwDmB,YAAY,YAGrB,wDACZ,ahB5DE,QgB4DmB,YAAY,YAMrC,kCACE,WhB5FK,KgB6FL,ahBpEI,QgBsEJ,iDACE,WhBvEE,QgB6EN,6BACE,MhB9EI,QgBoFJ,oDACE,WhBrFE,QgBwFU,2DACA,4DACZ,OAAQ,IAAI,MhBnHT,KgBuHS,4DACV,mBhB/FA,mBgBgGA,kBhBhGA,mBgBmGU,2DACV,iBhBpGA,mBgBqGA,oBhBrGA,mBgB4GJ,6CACE,iBhB7GE,mBgBoHJ,yCACE,WhBrHE,QgBwHJ,+CACE,WAAY,EAAE,EAAE,KhBzHd,OAAO,CgByHoB,EAAE,EAAE,IhBzH/B,QgB4HJ,yCACE,iBhB7HE,QgB8HF,kBhB9HE,QgBqIJ,+CACE,WhBtIE,QgBuIF,MhBvIE,QgBwIF,WAAY,MAAM,EhBjKf,IAAO,CgBiK6B,MAAM,EhBjK1C,KgBoKL,+CACE,WAAY,MAAM,EAAE,EAAE,EAAE,IhB5ItB,OAAO,CgB4I2B,MAAM,EAAE,EAAE,EAAE,IhBrK7C,KgB4KL,2CACE,iBhBpJE,QgBqJF,WAAY,MAAO,KAAI,EhBrJrB,OAAO,CgBqJwB,MAAM,EAAG,KhBrJxC,OAAO,CgBqJ6C,MAAM,EAAE,IhB9K3D,oBAAO,CgB8KsF,MAAM,EAAE,IhB9KrG,qBgBiLL,2CACE,iBAAkB,oEAClB,OAAQ,KAMZ,4CACE,MhBjKI,QgBhBJ,qCACE,WhBJG,QgBUP,kCACE,WhBjBK,KgBmBL,iDACE,WhBdG,QgBiBL,iDACE,iBAAkB,8JAON,wDACZ,MhB1BG,qBgBiCL,4CACE,WhBlCG,QgBwCP,2CACE,OAAQ,MACR,MAAO,KAFK,mDAKV,WhB7CG,QgB8CH,MhBpDG,KgBqDH,UAAW,MACX,YAAa,MACb,YAAa,IAIjB,2CACE,ahBtDK,QgBqDO,kDAAA,mDAKV,ahB1DG,QgBiEL,mDACE,WhBlEG,qBgBmEH,MhBzEG,KgBgFL,kDACE,ahB3EG,QgB2EkB,YAAY,YAGrB,0DACZ,ahB/EG,QgB+EkB,YAAY,YAMrC,oCACE,WhB5FK,KgB6FL,ahBvFK,QgByFL,mDACE,WhB1FG,QgBgGP,+BACE,MhBjGK,QgBuGL,sDACE,WhBxGG,QgB2GS,6DACA,8DACZ,OAAQ,IAAI,MhBnHT,KgBuHS,8DACV,mBhBlHC,qBgBmHD,kBhBnHC,qBgBsHS,6DACV,iBhBvHC,qBgBwHD,oBhBxHC,qBgB+HL,+CACE,iBhBhIG,qBgBuIL,2CACE,WhBxIG,QgB2IL,iDACE,WAAY,EAAE,EAAE,KhB5Ib,OAAO,CgB4ImB,EAAE,EAAE,IhB5I9B,QgB+IL,2CACE,iBhBhJG,QgBiJH,kBhBjJG,QgBwJL,iDACE,WhBzJG,QgB0JH,MhB1JG,QgB2JH,WAAY,MAAM,EhBjKf,IAAO,CgBiK6B,MAAM,EhBjK1C,KgBoKL,iDACE,WAAY,MAAM,EAAE,EAAE,EAAE,IhB/JrB,OAAO,CgB+J0B,MAAM,EAAE,EAAE,EAAE,IhBrK7C,KgB4KL,6CACE,iBhBvKG,QgBwKH,WAAY,MAAO,KAAI,EhBxKpB,OAAO,CgBwKuB,MAAM,EAAG,KhBxKvC,OAAO,CgBwK4C,MAAM,EAAE,IhB9K3D,oBAAO,CgB8KsF,MAAM,EAAE,IhB9KrG,qBgBiLL,6CACE,iBAAkB,oEAClB,OAAQ,KAMZ,8CACE,MhBpLK,QgBGL,mCACE,WhBsBE,QgBhBN,gCACE,WhBjBK,KgBmBL,+CACE,WhBYE,QgBTJ,+CACE,iBAAkB,8JAON,sDACZ,MhBAE,mBgBOJ,0CACE,WhBRE,QgBcN,yCACE,OAAQ,MACR,MAAO,KAFK,iDAKV,WhBnBE,QgBoBF,MhBpDG,KgBqDH,UAAW,MACX,YAAa,MACb,YAAa,IAIjB,yCACE,ahB5BI,QgB2BQ,gDAAA,iDAKV,ahBhCE,QgBuCJ,iDACE,WhBxCE,mBgByCF,MhBzEG,KgBgFL,gDACE,ahBjDE,QgBiDmB,YAAY,YAGrB,wDACZ,ahBrDE,QgBqDmB,YAAY,YAMrC,kCACE,WhB5FK,KgB6FL,ahB7DI,QgB+DJ,iDACE,WhBhEE,QgBsEN,6BACE,MhBvEI,QgB6EJ,oDACE,WhB9EE,QgBiFU,2DACA,4DACZ,OAAQ,IAAI,MhBnHT,KgBuHS,4DACV,mBhBxFA,mBgByFA,kBhBzFA,mBgB4FU,2DACV,iBhB7FA,mBgB8FA,oBhB9FA,mBgBqGJ,6CACE,iBhBtGE,mBgB6GJ,yCACE,WhB9GE,QgBiHJ,+CACE,WAAY,EAAE,EAAE,KhBlHd,OAAO,CgBkHoB,EAAE,EAAE,IhBlH/B,QgBqHJ,yCACE,iBhBtHE,QgBuHF,kBhBvHE,QgB8HJ,+CACE,WhB/HE,QgBgIF,MhBhIE,QgBiIF,WAAY,MAAM,EhBjKf,IAAO,CgBiK6B,MAAM,EhBjK1C,KgBoKL,+CACE,WAAY,MAAM,EAAE,EAAE,EAAE,IhBrItB,OAAO,CgBqI2B,MAAM,EAAE,EAAE,EAAE,IhBrK7C,KgB4KL,2CACE,iBhB7IE,QgB8IF,WAAY,MAAO,KAAI,EhB9IrB,OAAO,CgB8IwB,MAAM,EAAG,KhB9IxC,OAAO,CgB8I6C,MAAM,EAAE,IhB9K3D,oBAAO,CgB8KsF,MAAM,EAAE,IhB9KrG,qBgBiLL,2CACE,iBAAkB,oEAClB,OAAQ,KAMZ,4CACE,MhB1JI,QgBvBJ,gCACE,WhBwBE,QgBlBN,6BACE,WhBjBK,KgBmBL,4CACE,WhBcE,QgBXJ,4CACE,iBAAkB,8JAON,mDACZ,MhBEE,oBgBKJ,uCACE,WhBNE,QgBYN,sCACE,OAAQ,MACR,MAAO,KAFK,8CAKV,WhBjBE,QgBkBF,MhBpDG,KgBqDH,UAAW,MACX,YAAa,MACb,YAAa,IAIjB,sCACE,ahB1BI,QgByBQ,6CAAA,8CAKV,ahB9BE,QgBqCJ,8CACE,WhBtCE,oBgBuCF,MhBzEG,KgBgFL,6CACE,ahB/CE,QgB+CmB,YAAY,YAGrB,qDACZ,ahBnDE,QgBmDmB,YAAY,YAMrC,+BACE,WhB5FK,KgB6FL,ahB3DI,QgB6DJ,8CACE,WhB9DE,QgBoEN,0BACE,MhBrEI,QgB2EJ,iDACE,WhB5EE,QgB+EU,wDACA,yDACZ,OAAQ,IAAI,MhBnHT,KgBuHS,yDACV,mBhBtFA,oBgBuFA,kBhBvFA,oBgB0FU,wDACV,iBhB3FA,oBgB4FA,oBhB5FA,oBgBmGJ,0CACE,iBhBpGE,oBgB2GJ,sCACE,WhB5GE,QgB+GJ,4CACE,WAAY,EAAE,EAAE,KhBhHd,OAAO,CgBgHoB,EAAE,EAAE,IhBhH/B,QgBmHJ,sCACE,iBhBpHE,QgBqHF,kBhBrHE,QgB4HJ,4CACE,WhB7HE,QgB8HF,MhB9HE,QgB+HF,WAAY,MAAM,EhBjKf,IAAO,CgBiK6B,MAAM,EhBjK1C,KgBoKL,4CACE,WAAY,MAAM,EAAE,EAAE,EAAE,IhBnItB,OAAO,CgBmI2B,MAAM,EAAE,EAAE,EAAE,IhBrK7C,KgB4KL,wCACE,iBhB3IE,QgB4IF,WAAY,MAAO,KAAI,EhB5IrB,OAAO,CgB4IwB,MAAM,EAAG,KhB5IxC,OAAO,CgB4I6C,MAAM,EAAE,IhB9K3D,oBAAO,CgB8KsF,MAAM,EAAE,IhB9KrG,qBgBiLL,wCACE,iBAAkB,oEAClB,OAAQ,KAMZ,yCACE,MhBxJI,QgBzBJ,mCACE,WhBqBE,QgBfN,gCACE,WhBiEU,QgB/DV,+CACE,WhBWE,QgBRJ,+CACE,iBAAkB,qJAON,sDACZ,MhBDE,mBgBQJ,0CACE,WhBTE,QgBeN,yCACE,OAAQ,MACR,MAAO,KAFK,iDAKV,WhBpBE,QgBqBF,MhB8BQ,QgB7BR,UAAW,MACX,YAAa,MACb,YAAa,IAIjB,yCACE,ahB7BI,QgB4BQ,gDAAA,iDAKV,ahBjCE,QgBwCJ,iDACE,WhBzCE,mBgB0CF,MhBSQ,QgBFV,gDACE,ahBlDE,QgBkDmB,YAAY,YAGrB,wDACZ,ahBtDE,QgBsDmB,YAAY,YAMrC,kCACE,WhBVU,QgBWV,ahB9DI,QgBgEJ,iDACE,WhBjEE,QgBuEN,6BACE,MhBxEI,QgB8EJ,oDACE,WhB/EE,QgBkFU,2DACA,4DACZ,OAAQ,IAAI,MhBjCJ,QgBqCI,4DACV,mBhBzFA,mBgB0FA,kBhB1FA,mBgB6FU,2DACV,iBhB9FA,mBgB+FA,oBhB/FA,mBgBsGJ,6CACE,iBhBvGE,mBgB8GJ,yCACE,WhB/GE,QgBkHJ,+CACE,WAAY,EAAE,EAAE,KhBnHd,OAAO,CgBmHoB,EAAE,EAAE,IhBnH/B,QgBsHJ,yCACE,iBhBvHE,QgBwHF,kBhBxHE,QgB+HJ,+CACE,WhBhIE,QgBiIF,MhBjIE,QgBkIF,WAAY,MAAM,EhB/EV,OAAO,CgB+EwB,MAAM,EhB/ErC,QgBkFV,+CACE,WAAY,MAAM,EAAE,EAAE,EAAE,IhBtItB,OAAO,CgBsI2B,MAAM,EAAE,EAAE,EAAE,IhBnFxC,QgB0FV,2CACE,iBhB9IE,QgB+IF,WAAY,MAAO,KAAI,EhB/IrB,OAAO,CgB+IwB,MAAM,EAAG,KhB/IxC,OAAO,CgB+I6C,MAAM,EAAE,IhB5FtD,iBAAO,CgB4FiF,MAAM,EAAE,IhB5FhG,kBgB+FV,2CACE,iBAAkB,8DAClB,OAAQ,KAMZ,4CACE,MhB3JI,QgBtBJ,kCACE,WhBmBE,QgBbN,+BACE,WhBjBK,KgBmBL,8CACE,WhBSE,QgBNJ,8CACE,iBAAkB,8JAON,qDACZ,MhBHE,mBgBUJ,yCACE,WhBXE,QgBiBN,wCACE,OAAQ,MACR,MAAO,KAFK,gDAKV,WhBtBE,QgBuBF,MhBpDG,KgBqDH,UAAW,MACX,YAAa,MACb,YAAa,IAIjB,wCACE,ahB/BI,QgB8BQ,+CAAA,gDAKV,ahBnCE,QgB0CJ,gDACE,WhB3CE,mBgB4CF,MhBzEG,KgBgFL,+CACE,ahBpDE,QgBoDmB,YAAY,YAGrB,uDACZ,ahBxDE,QgBwDmB,YAAY,YAMrC,iCACE,WhB5FK,KgB6FL,ahBhEI,QgBkEJ,gDACE,WhBnEE,QgByEN,4BACE,MhB1EI,QgBgFJ,mDACE,WhBjFE,QgBoFU,0DACA,2DACZ,OAAQ,IAAI,MhBnHT,KgBuHS,2DACV,mBhB3FA,mBgB4FA,kBhB5FA,mBgB+FU,0DACV,iBhBhGA,mBgBiGA,oBhBjGA,mBgBwGJ,4CACE,iBhBzGE,mBgBgHJ,wCACE,WhBjHE,QgBoHJ,8CACE,WAAY,EAAE,EAAE,KhBrHd,OAAO,CgBqHoB,EAAE,EAAE,IhBrH/B,QgBwHJ,wCACE,iBhBzHE,QgB0HF,kBhB1HE,QgBiIJ,8CACE,WhBlIE,QgBmIF,MhBnIE,QgBoIF,WAAY,MAAM,EhBjKf,IAAO,CgBiK6B,MAAM,EhBjK1C,KgBoKL,8CACE,WAAY,MAAM,EAAE,EAAE,EAAE,IhBxItB,OAAO,CgBwI2B,MAAM,EAAE,EAAE,EAAE,IhBrK7C,KgB4KL,0CACE,iBhBhJE,QgBiJF,WAAY,MAAO,KAAI,EhBjJrB,OAAO,CgBiJwB,MAAM,EAAG,KhBjJxC,OAAO,CgBiJ6C,MAAM,EAAE,IhB9K3D,oBAAO,CgB8KsF,MAAM,EAAE,IhB9KrG,qBgBiLL,0CACE,iBAAkB,oEAClB,OAAQ,KAMZ,2CACE,MhB7JI,QgBpBJ,iCACE,WhBTG,QgBeP,8BACE,WhBiEU,QgB/DV,6CACE,WhBnBG,QgBsBL,6CACE,iBAAkB,qJAON,oDACZ,MhB/BG,qBgBsCL,wCACE,WhBvCG,QgB6CP,uCACE,OAAQ,MACR,MAAO,KAFK,+CAKV,WhBlDG,QgBmDH,MhB8BQ,QgB7BR,UAAW,MACX,YAAa,MACb,YAAa,IAIjB,uCACE,ahB3DK,QgB0DO,8CAAA,+CAKV,ahB/DG,QgBsEL,+CACE,WhBvEG,qBgBwEH,MhBSQ,QgBFV,8CACE,ahBhFG,QgBgFkB,YAAY,YAGrB,sDACZ,ahBpFG,QgBoFkB,YAAY,YAMrC,gCACE,WhBVU,QgBWV,ahB5FK,QgB8FL,+CACE,WhB/FG,QgBqGP,2BACE,MhBtGK,QgB4GL,kDACE,WhB7GG,QgBgHS,yDACA,0DACZ,OAAQ,IAAI,MhBjCJ,QgBqCI,0DACV,mBhBvHC,qBgBwHD,kBhBxHC,qBgB2HS,yDACV,iBhB5HC,qBgB6HD,oBhB7HC,qBgBoIL,2CACE,iBhBrIG,qBgB4IL,uCACE,WhB7IG,QgBgJL,6CACE,WAAY,EAAE,EAAE,KhBjJb,OAAO,CgBiJmB,EAAE,EAAE,IhBjJ9B,QgBoJL,uCACE,iBhBrJG,QgBsJH,kBhBtJG,QgB6JL,6CACE,WhB9JG,QgB+JH,MhB/JG,QgBgKH,WAAY,MAAM,EhB/EV,OAAO,CgB+EwB,MAAM,EhB/ErC,QgBkFV,6CACE,WAAY,MAAM,EAAE,EAAE,EAAE,IhBpKrB,OAAO,CgBoK0B,MAAM,EAAE,EAAE,EAAE,IhBnFxC,QgB0FV,yCACE,iBhB5KG,QgB6KH,WAAY,MAAO,KAAI,EhB7KpB,OAAO,CgB6KuB,MAAM,EAAG,KhB7KvC,OAAO,CgB6K4C,MAAM,EAAE,IhB5FtD,iBAAO,CgB4FiF,MAAM,EAAE,IhB5FhG,kBgB+FV,yCACE,iBAAkB,8DAClB,OAAQ,KAMZ,0CACE,MhBzLK,QgBQL,gCACE,WhBFG,QgBQP,6BACE,WhBjBK,KgBmBL,4CACE,WhBZG,QgBeL,4CACE,iBAAkB,8JAON,mDACZ,MhBxBG,kBgB+BL,uCACE,WhBhCG,QgBsCP,sCACE,OAAQ,MACR,MAAO,KAFK,8CAKV,WhB3CG,QgB4CH,MhBpDG,KgBqDH,UAAW,MACX,YAAa,MACb,YAAa,IAIjB,sCACE,ahBpDK,QgBmDO,6CAAA,8CAKV,ahBxDG,QgB+DL,8CACE,WhBhEG,kBgBiEH,MhBzEG,KgBgFL,6CACE,ahBzEG,QgByEkB,YAAY,YAGrB,qDACZ,ahB7EG,QgB6EkB,YAAY,YAMrC,+BACE,WhB5FK,KgB6FL,ahBrFK,QgBuFL,8CACE,WhBxFG,QgB8FP,0BACE,MhB/FK,QgBqGL,iDACE,WhBtGG,QgByGS,wDACA,yDACZ,OAAQ,IAAI,MhBnHT,KgBuHS,yDACV,mBhBhHC,kBgBiHD,kBhBjHC,kBgBoHS,wDACV,iBhBrHC,kBgBsHD,oBhBtHC,kBgB6HL,0CACE,iBhB9HG,kBgBqIL,sCACE,WhBtIG,QgByIL,4CACE,WAAY,EAAE,EAAE,KhB1Ib,OAAO,CgB0ImB,EAAE,EAAE,IhB1I9B,QgB6IL,sCACE,iBhB9IG,QgB+IH,kBhB/IG,QgBsJL,4CACE,WhBvJG,QgBwJH,MhBxJG,QgByJH,WAAY,MAAM,EhBjKf,IAAO,CgBiK6B,MAAM,EhBjK1C,KgBoKL,4CACE,WAAY,MAAM,EAAE,EAAE,EAAE,IhB7JrB,OAAO,CgB6J0B,MAAM,EAAE,EAAE,EAAE,IhBrK7C,KgB4KL,wCACE,iBhBrKG,QgBsKH,WAAY,MAAO,KAAI,EhBtKpB,OAAO,CgBsKuB,MAAM,EAAG,KhBtKvC,OAAO,CgBsK4C,MAAM,EAAE,IhB9K3D,oBAAO,CgB8KsF,MAAM,EAAE,IhB9KrG,qBgBiLL,wCACE,iBAAkB,oEAClB,OAAQ,KAMZ,yCACE,MhBlLK,QgBCL,qCACE,WZdI,QYoBR,kCACE,WhBjBK,KgBmBL,iDACE,WZxBI,QY2BN,iDACE,iBAAkB,8JAON,wDACZ,MZpCI,oBY2CN,4CACE,WZ5CI,QYkDR,2CACE,OAAQ,MACR,MAAO,KAFK,mDAKV,WZvDI,QYwDJ,MhBpDG,KgBqDH,UAAW,MACX,YAAa,MACb,YAAa,IAIjB,2CACE,aZhEM,QY+DM,kDAAA,mDAKV,aZpEI,QY2EN,mDACE,WZ5EI,oBY6EJ,MhBzEG,KgBgFL,kDACE,aZrFI,QYqFiB,YAAY,YAGrB,0DACZ,aZzFI,QYyFiB,YAAY,YAMrC,oCACE,WhB5FK,KgB6FL,aZjGM,QYmGN,mDACE,WZpGI,QY0GR,+BACE,MZ3GM,QYiHN,sDACE,WZlHI,QYqHQ,6DACA,8DACZ,OAAQ,IAAI,MhBnHT,KgBuHS,8DACV,mBZ5HE,oBY6HF,kBZ7HE,oBYgIQ,6DACV,iBZjIE,oBYkIF,oBZlIE,oBYyIN,+CACE,iBZ1II,oBYiJN,2CACE,WZlJI,QYqJN,iDACE,WAAY,EAAE,EAAE,KZtJZ,OAAO,CYsJkB,EAAE,EAAE,IZtJ7B,QYyJN,2CACE,iBZ1JI,QY2JJ,kBZ3JI,QYkKN,iDACE,WZnKI,QYoKJ,MZpKI,QYqKJ,WAAY,MAAM,EhBjKf,IAAO,CgBiK6B,MAAM,EhBjK1C,KgBoKL,iDACE,WAAY,MAAM,EAAE,EAAE,EAAE,IZzKpB,OAAO,CYyKyB,MAAM,EAAE,EAAE,EAAE,IhBrK7C,KgB4KL,6CACE,iBZjLI,QYkLJ,WAAY,MAAO,KAAI,EZlLnB,OAAO,CYkLsB,MAAM,EAAG,KZlLtC,OAAO,CYkL2C,MAAM,EAAE,IhB9K3D,oBAAO,CgB8KsF,MAAM,EAAE,IhB9KrG,qBgBiLL,6CACE,iBAAkB,oEAClB,OAAQ,KAMZ,8CACE,MZ9LM,QYaN,gCACE,WZbD,QYmBH,6BACE,WhBjBK,KgBmBL,4CACE,WZvBD,QY0BD,4CACE,iBAAkB,8JAON,mDACZ,MZnCD,iBY0CD,uCACE,WZ3CD,QYiDH,sCACE,OAAQ,MACR,MAAO,KAFK,8CAKV,WZtDD,QYuDC,MhBpDG,KgBqDH,UAAW,MACX,YAAa,MACb,YAAa,IAIjB,sCACE,aZ/DC,QY8DW,6CAAA,8CAKV,aZnED,QY0ED,8CACE,WZ3ED,iBY4EC,MhBzEG,KgBgFL,6CACE,aZpFD,QYoFsB,YAAY,YAGrB,qDACZ,aZxFD,QYwFsB,YAAY,YAMrC,+BACE,WhB5FK,KgB6FL,aZhGC,QYkGD,8CACE,WZnGD,QYyGH,0BACE,MZ1GC,QYgHD,iDACE,WZjHD,QYoHa,wDACA,yDACZ,OAAQ,IAAI,MhBnHT,KgBuHS,yDACV,mBZ3HH,iBY4HG,kBZ5HH,iBY+Ha,wDACV,iBZhIH,iBYiIG,oBZjIH,iBYwID,0CACE,iBZzID,iBYgJD,sCACE,WZjJD,QYoJD,4CACE,WAAY,EAAE,EAAE,KZrJjB,OAAO,CYqJuB,EAAE,EAAE,IZrJlC,QYwJD,sCACE,iBZzJD,QY0JC,kBZ1JD,QYiKD,4CACE,WZlKD,QYmKC,MZnKD,QYoKC,WAAY,MAAM,EhBjKf,IAAO,CgBiK6B,MAAM,EhBjK1C,KgBoKL,4CACE,WAAY,MAAM,EAAE,EAAE,EAAE,IZxKzB,OAAO,CYwK8B,MAAM,EAAE,EAAE,EAAE,IhBrK7C,KgB4KL,wCACE,iBZhLD,QYiLC,WAAY,MAAO,KAAI,EZjLxB,OAAO,CYiL2B,MAAM,EAAG,KZjL3C,OAAO,CYiLgD,MAAM,EAAE,IhB9K3D,oBAAO,CgB8KsF,MAAM,EAAE,IhB9KrG,qBgBiLL,wCACE,iBAAkB,oEAClB,OAAQ,KAMZ,yCACE,MZ7LC,QYYD,iCACE,WZXA,QYiBJ,8BACE,WhBjBK,KgBmBL,6CACE,WZrBA,QYwBF,6CACE,iBAAkB,8JAON,oDACZ,MZjCA,oBYwCF,wCACE,WZzCA,QY+CJ,uCACE,OAAQ,MACR,MAAO,KAFK,+CAKV,WZpDA,QYqDA,MhBpDG,KgBqDH,UAAW,MACX,YAAa,MACb,YAAa,IAIjB,uCACE,aZ7DE,QY4DU,8CAAA,+CAKV,aZjEA,QYwEF,+CACE,WZzEA,oBY0EA,MhBzEG,KgBgFL,8CACE,aZlFA,QYkFqB,YAAY,YAGrB,sDACZ,aZtFA,QYsFqB,YAAY,YAMrC,gCACE,WhB5FK,KgB6FL,aZ9FE,QYgGF,+CACE,WZjGA,QYuGJ,2BACE,MZxGE,QY8GF,kDACE,WZ/GA,QYkHY,yDACA,0DACZ,OAAQ,IAAI,MhBnHT,KgBuHS,0DACV,mBZzHF,oBY0HE,kBZ1HF,oBY6HY,yDACV,iBZ9HF,oBY+HE,oBZ/HF,oBYsIF,2CACE,iBZvIA,oBY8IF,uCACE,WZ/IA,QYkJF,6CACE,WAAY,EAAE,EAAE,KZnJhB,OAAO,CYmJsB,EAAE,EAAE,IZnJjC,QYsJF,uCACE,iBZvJA,QYwJA,kBZxJA,QY+JF,6CACE,WZhKA,QYiKA,MZjKA,QYkKA,WAAY,MAAM,EhBjKf,IAAO,CgBiK6B,MAAM,EhBjK1C,KgBoKL,6CACE,WAAY,MAAM,EAAE,EAAE,EAAE,IZtKxB,OAAO,CYsK6B,MAAM,EAAE,EAAE,EAAE,IhBrK7C,KgB4KL,yCACE,iBZ9KA,QY+KA,WAAY,MAAO,KAAI,EZ/KvB,OAAO,CY+K0B,MAAM,EAAG,KZ/K1C,OAAO,CY+K+C,MAAM,EAAE,IhB9K3D,oBAAO,CgB8KsF,MAAM,EAAE,IhB9KrG,qBgBiLL,yCACE,iBAAkB,oEAClB,OAAQ,KAMZ,0CACE,MZ3LE,QYUF,gCACE,WZVD,QYgBH,6BACE,WhBiEU,QgB/DV,4CACE,WZpBD,QYuBD,4CACE,iBAAkB,qJAON,mDACZ,MZhCD,mBYuCD,uCACE,WZxCD,QY8CH,sCACE,OAAQ,MACR,MAAO,KAFK,8CAKV,WZnDD,QYoDC,MhB8BQ,QgB7BR,UAAW,MACX,YAAa,MACb,YAAa,IAIjB,sCACE,aZ5DC,QY2DW,6CAAA,8CAKV,aZhED,QYuED,8CACE,WZxED,mBYyEC,MhBSQ,QgBFV,6CACE,aZjFD,QYiFsB,YAAY,YAGrB,qDACZ,aZrFD,QYqFsB,YAAY,YAMrC,+BACE,WhBVU,QgBWV,aZ7FC,QY+FD,8CACE,WZhGD,QYsGH,0BACE,MZvGC,QY6GD,iDACE,WZ9GD,QYiHa,wDACA,yDACZ,OAAQ,IAAI,MhBjCJ,QgBqCI,yDACV,mBZxHH,mBYyHG,kBZzHH,mBY4Ha,wDACV,iBZ7HH,mBY8HG,oBZ9HH,mBYqID,0CACE,iBZtID,mBY6ID,sCACE,WZ9ID,QYiJD,4CACE,WAAY,EAAE,EAAE,KZlJjB,OAAO,CYkJuB,EAAE,EAAE,IZlJlC,QYqJD,sCACE,iBZtJD,QYuJC,kBZvJD,QY8JD,4CACE,WZ/JD,QYgKC,MZhKD,QYiKC,WAAY,MAAM,EhB/EV,OAAO,CgB+EwB,MAAM,EhB/ErC,QgBkFV,4CACE,WAAY,MAAM,EAAE,EAAE,EAAE,IZrKzB,OAAO,CYqK8B,MAAM,EAAE,EAAE,EAAE,IhBnFxC,QgB0FV,wCACE,iBZ7KD,QY8KC,WAAY,MAAO,KAAI,EZ9KxB,OAAO,CY8K2B,MAAM,EAAG,KZ9K3C,OAAO,CY8KgD,MAAM,EAAE,IhB5FtD,iBAAO,CgB4FiF,MAAM,EAAE,IhB5FhG,kBgB+FV,wCACE,iBAAkB,8DAClB,OAAQ,KAMZ,yCACE,MZ1LC,QYSD,mCACE,WZRE,QYcN,gCACE,WhBjBK,KgBmBL,+CACE,WZlBE,QYqBJ,+CACE,iBAAkB,8JAON,sDACZ,MZ9BE,oBYqCJ,0CACE,WZtCE,QY4CN,yCACE,OAAQ,MACR,MAAO,KAFK,iDAKV,WZjDE,QYkDF,MhBpDG,KgBqDH,UAAW,MACX,YAAa,MACb,YAAa,IAIjB,yCACE,aZ1DI,QYyDQ,gDAAA,iDAKV,aZ9DE,QYqEJ,iDACE,WZtEE,oBYuEF,MhBzEG,KgBgFL,gDACE,aZ/EE,QY+EmB,YAAY,YAGrB,wDACZ,aZnFE,QYmFmB,YAAY,YAMrC,kCACE,WhB5FK,KgB6FL,aZ3FI,QY6FJ,iDACE,WZ9FE,QYoGN,6BACE,MZrGI,QY2GJ,oDACE,WZ5GE,QY+GU,2DACA,4DACZ,OAAQ,IAAI,MhBnHT,KgBuHS,4DACV,mBZtHA,oBYuHA,kBZvHA,oBY0HU,2DACV,iBZ3HA,oBY4HA,oBZ5HA,oBYmIJ,6CACE,iBZpIE,oBY2IJ,yCACE,WZ5IE,QY+IJ,+CACE,WAAY,EAAE,EAAE,KZhJd,OAAO,CYgJoB,EAAE,EAAE,IZhJ/B,QYmJJ,yCACE,iBZpJE,QYqJF,kBZrJE,QY4JJ,+CACE,WZ7JE,QY8JF,MZ9JE,QY+JF,WAAY,MAAM,EhBjKf,IAAO,CgBiK6B,MAAM,EhBjK1C,KgBoKL,+CACE,WAAY,MAAM,EAAE,EAAE,EAAE,IZnKtB,OAAO,CYmK2B,MAAM,EAAE,EAAE,EAAE,IhBrK7C,KgB4KL,2CACE,iBZ3KE,QY4KF,WAAY,MAAO,KAAI,EZ5KrB,OAAO,CY4KwB,MAAM,EAAG,KZ5KxC,OAAO,CY4K6C,MAAM,EAAE,IhB9K3D,oBAAO,CgB8KsF,MAAM,EAAE,IhB9KrG,qBgBiLL,2CACE,iBAAkB,oEAClB,OAAQ,KAMZ,4CACE,MZxLI,QYOJ,kCACE,WZNC,QYYL,+BACE,WhBjBK,KgBmBL,8CACE,WZhBC,QYmBH,8CACE,iBAAkB,8JAON,qDACZ,MZ5BC,mBYmCH,yCACE,WZpCC,QY0CL,wCACE,OAAQ,MACR,MAAO,KAFK,gDAKV,WZ/CC,QYgDD,MhBpDG,KgBqDH,UAAW,MACX,YAAa,MACb,YAAa,IAIjB,wCACE,aZxDG,QYuDS,+CAAA,gDAKV,aZ5DC,QYmEH,gDACE,WZpEC,mBYqED,MhBzEG,KgBgFL,+CACE,aZ7EC,QY6EoB,YAAY,YAGrB,uDACZ,aZjFC,QYiFoB,YAAY,YAMrC,iCACE,WhB5FK,KgB6FL,aZzFG,QY2FH,gDACE,WZ5FC,QYkGL,4BACE,MZnGG,QYyGH,mDACE,WZ1GC,QY6GW,0DACA,2DACZ,OAAQ,IAAI,MhBnHT,KgBuHS,2DACV,mBZpHD,mBYqHC,kBZrHD,mBYwHW,0DACV,iBZzHD,mBY0HC,oBZ1HD,mBYiIH,4CACE,iBZlIC,mBYyIH,wCACE,WZ1IC,QY6IH,8CACE,WAAY,EAAE,EAAE,KZ9If,OAAO,CY8IqB,EAAE,EAAE,IZ9IhC,QYiJH,wCACE,iBZlJC,QYmJD,kBZnJC,QY0JH,8CACE,WZ3JC,QY4JD,MZ5JC,QY6JD,WAAY,MAAM,EhBjKf,IAAO,CgBiK6B,MAAM,EhBjK1C,KgBoKL,8CACE,WAAY,MAAM,EAAE,EAAE,EAAE,IZjKvB,OAAO,CYiK4B,MAAM,EAAE,EAAE,EAAE,IhBrK7C,KgB4KL,0CACE,iBZzKC,QY0KD,WAAY,MAAO,KAAI,EZ1KtB,OAAO,CY0KyB,MAAM,EAAG,KZ1KzC,OAAO,CY0K8C,MAAM,EAAE,IhB9K3D,oBAAO,CgB8KsF,MAAM,EAAE,IhB9KrG,qBgBiLL,0CACE,iBAAkB,oEAClB,OAAQ,KAMZ,2CACE,MZtLG,QYKH,gCACE,WhBeE,QgBTN,6BACE,WhBjBK,KgBmBL,4CACE,WhBKE,QgBFJ,4CACE,iBAAkB,8JAON,mDACZ,MhBPE,mBgBcJ,uCACE,WhBfE,QgBqBN,sCACE,OAAQ,MACR,MAAO,KAFK,8CAKV,WhB1BE,QgB2BF,MhBpDG,KgBqDH,UAAW,MACX,YAAa,MACb,YAAa,IAIjB,sCACE,ahBnCI,QgBkCQ,6CAAA,8CAKV,ahBvCE,QgB8CJ,8CACE,WhB/CE,mBgBgDF,MhBzEG,KgBgFL,6CACE,ahBxDE,QgBwDmB,YAAY,YAGrB,qDACZ,ahB5DE,QgB4DmB,YAAY,YAMrC,+BACE,WhB5FK,KgB6FL,ahBpEI,QgBsEJ,8CACE,WhBvEE,QgB6EN,0BACE,MhB9EI,QgBoFJ,iDACE,WhBrFE,QgBwFU,wDACA,yDACZ,OAAQ,IAAI,MhBnHT,KgBuHS,yDACV,mBhB/FA,mBgBgGA,kBhBhGA,mBgBmGU,wDACV,iBhBpGA,mBgBqGA,oBhBrGA,mBgB4GJ,0CACE,iBhB7GE,mBgBoHJ,sCACE,WhBrHE,QgBwHJ,4CACE,WAAY,EAAE,EAAE,KhBzHd,OAAO,CgByHoB,EAAE,EAAE,IhBzH/B,QgB4HJ,sCACE,iBhB7HE,QgB8HF,kBhB9HE,QgBqIJ,4CACE,WhBtIE,QgBuIF,MhBvIE,QgBwIF,WAAY,MAAM,EhBjKf,IAAO,CgBiK6B,MAAM,EhBjK1C,KgBoKL,4CACE,WAAY,MAAM,EAAE,EAAE,EAAE,IhB5ItB,OAAO,CgB4I2B,MAAM,EAAE,EAAE,EAAE,IhBrK7C,KgB4KL,wCACE,iBhBpJE,QgBqJF,WAAY,MAAO,KAAI,EhBrJrB,OAAO,CgBqJwB,MAAM,EAAG,KhBrJxC,OAAO,CgBqJ6C,MAAM,EAAE,IhB9K3D,oBAAO,CgB8KsF,MAAM,EAAE,IhB9KrG,qBgBiLL,wCACE,iBAAkB,oEAClB,OAAQ,KAMZ,yCACE,MhBjKI,QgBhBJ,kCACE,WhBgBE,QgBVN,+BACE,WhBjBK,KgBmBL,8CACE,WhBME,QgBHJ,8CACE,iBAAkB,8JAON,qDACZ,MhBNE,oBgBaJ,yCACE,WhBdE,QgBoBN,wCACE,OAAQ,MACR,MAAO,KAFK,gDAKV,WhBzBE,QgB0BF,MhBpDG,KgBqDH,UAAW,MACX,YAAa,MACb,YAAa,IAIjB,wCACE,ahBlCI,QgBiCQ,+CAAA,gDAKV,ahBtCE,QgB6CJ,gDACE,WhB9CE,oBgB+CF,MhBzEG,KgBgFL,+CACE,ahBvDE,QgBuDmB,YAAY,YAGrB,uDACZ,ahB3DE,QgB2DmB,YAAY,YAMrC,iCACE,WhB5FK,KgB6FL,ahBnEI,QgBqEJ,gDACE,WhBtEE,QgB4EN,4BACE,MhB7EI,QgBmFJ,mDACE,WhBpFE,QgBuFU,0DACA,2DACZ,OAAQ,IAAI,MhBnHT,KgBuHS,2DACV,mBhB9FA,oBgB+FA,kBhB/FA,oBgBkGU,0DACV,iBhBnGA,oBgBoGA,oBhBpGA,oBgB2GJ,4CACE,iBhB5GE,oBgBmHJ,wCACE,WhBpHE,QgBuHJ,8CACE,WAAY,EAAE,EAAE,KhBxHd,OAAO,CgBwHoB,EAAE,EAAE,IhBxH/B,QgB2HJ,wCACE,iBhB5HE,QgB6HF,kBhB7HE,QgBoIJ,8CACE,WhBrIE,QgBsIF,MhBtIE,QgBuIF,WAAY,MAAM,EhBjKf,IAAO,CgBiK6B,MAAM,EhBjK1C,KgBoKL,8CACE,WAAY,MAAM,EAAE,EAAE,EAAE,IhB3ItB,OAAO,CgB2I2B,MAAM,EAAE,EAAE,EAAE,IhBrK7C,KgB4KL,0CACE,iBhBnJE,QgBoJF,WAAY,MAAO,KAAI,EhBpJrB,OAAO,CgBoJwB,MAAM,EAAG,KhBpJxC,OAAO,CgBoJ6C,MAAM,EAAE,IhB9K3D,oBAAO,CgB8KsF,MAAM,EAAE,IhB9KrG,qBgBiLL,0CACE,iBAAkB,oEAClB,OAAQ,KAMZ,2CACE,MhBhKI,QgBjBJ,kCACE,WhBiBE,QgBXN,+BACE,WhBjBK,KgBmBL,8CACE,WhBOE,QgBJJ,8CACE,iBAAkB,8JAON,qDACZ,MhBLE,oBgBYJ,yCACE,WhBbE,QgBmBN,wCACE,OAAQ,MACR,MAAO,KAFK,gDAKV,WhBxBE,QgByBF,MhBpDG,KgBqDH,UAAW,MACX,YAAa,MACb,YAAa,IAIjB,wCACE,ahBjCI,QgBgCQ,+CAAA,gDAKV,ahBrCE,QgB4CJ,gDACE,WhB7CE,oBgB8CF,MhBzEG,KgBgFL,+CACE,ahBtDE,QgBsDmB,YAAY,YAGrB,uDACZ,ahB1DE,QgB0DmB,YAAY,YAMrC,iCACE,WhB5FK,KgB6FL,ahBlEI,QgBoEJ,gDACE,WhBrEE,QgB2EN,4BACE,MhB5EI,QgBkFJ,mDACE,WhBnFE,QgBsFU,0DACA,2DACZ,OAAQ,IAAI,MhBnHT,KgBuHS,2DACV,mBhB7FA,oBgB8FA,kBhB9FA,oBgBiGU,0DACV,iBhBlGA,oBgBmGA,oBhBnGA,oBgB0GJ,4CACE,iBhB3GE,oBgBkHJ,wCACE,WhBnHE,QgBsHJ,8CACE,WAAY,EAAE,EAAE,KhBvHd,OAAO,CgBuHoB,EAAE,EAAE,IhBvH/B,QgB0HJ,wCACE,iBhB3HE,QgB4HF,kBhB5HE,QgBmIJ,8CACE,WhBpIE,QgBqIF,MhBrIE,QgBsIF,WAAY,MAAM,EhBjKf,IAAO,CgBiK6B,MAAM,EhBjK1C,KgBoKL,8CACE,WAAY,MAAM,EAAE,EAAE,EAAE,IhB1ItB,OAAO,CgB0I2B,MAAM,EAAE,EAAE,EAAE,IhBrK7C,KgB4KL,0CACE,iBhBlJE,QgBmJF,WAAY,MAAO,KAAI,EhBnJrB,OAAO,CgBmJwB,MAAM,EAAG,KhBnJxC,OAAO,CgBmJ6C,MAAM,EAAE,IhB9K3D,oBAAO,CgB8KsF,MAAM,EAAE,IhB9KrG,qBgBiLL,0CACE,iBAAkB,oEAClB,OAAQ,KAMZ,2CACE,MhB/JI,QgBlBJ,gCACE,WhBkBE,QgBZN,6BACE,WhBjBK,KgBmBL,4CACE,WhBQE,QgBLJ,4CACE,iBAAkB,8JAON,mDACZ,MhBJE,oBgBWJ,uCACE,WhBZE,QgBkBN,sCACE,OAAQ,MACR,MAAO,KAFK,8CAKV,WhBvBE,QgBwBF,MhBpDG,KgBqDH,UAAW,MACX,YAAa,MACb,YAAa,IAIjB,sCACE,ahBhCI,QgB+BQ,6CAAA,8CAKV,ahBpCE,QgB2CJ,8CACE,WhB5CE,oBgB6CF,MhBzEG,KgBgFL,6CACE,ahBrDE,QgBqDmB,YAAY,YAGrB,qDACZ,ahBzDE,QgByDmB,YAAY,YAMrC,+BACE,WhB5FK,KgB6FL,ahBjEI,QgBmEJ,8CACE,WhBpEE,QgB0EN,0BACE,MhB3EI,QgBiFJ,iDACE,WhBlFE,QgBqFU,wDACA,yDACZ,OAAQ,IAAI,MhBnHT,KgBuHS,yDACV,mBhB5FA,oBgB6FA,kBhB7FA,oBgBgGU,wDACV,iBhBjGA,oBgBkGA,oBhBlGA,oBgByGJ,0CACE,iBhB1GE,oBgBiHJ,sCACE,WhBlHE,QgBqHJ,4CACE,WAAY,EAAE,EAAE,KhBtHd,OAAO,CgBsHoB,EAAE,EAAE,IhBtH/B,QgByHJ,sCACE,iBhB1HE,QgB2HF,kBhB3HE,QgBkIJ,4CACE,WhBnIE,QgBoIF,MhBpIE,QgBqIF,WAAY,MAAM,EhBjKf,IAAO,CgBiK6B,MAAM,EhBjK1C,KgBoKL,4CACE,WAAY,MAAM,EAAE,EAAE,EAAE,IhBzItB,OAAO,CgByI2B,MAAM,EAAE,EAAE,EAAE,IhBrK7C,KgB4KL,wCACE,iBhBjJE,QgBkJF,WAAY,MAAO,KAAI,EhBlJrB,OAAO,CgBkJwB,MAAM,EAAG,KhBlJxC,OAAO,CgBkJ6C,MAAM,EAAE,IhB9K3D,oBAAO,CgB8KsF,MAAM,EAAE,IhB9KrG,qBgBiLL,wCACE,iBAAkB,oEAClB,OAAQ,KAMZ,yCACE,MhB9JI,QgBnBJ,+BACE,WhBmBE,QgBbN,4BACE,WhBjBK,KgBmBL,2CACE,WhBSE,QgBNJ,2CACE,iBAAkB,8JAON,kDACZ,MhBHE,mBgBUJ,sCACE,WhBXE,QgBiBN,qCACE,OAAQ,MACR,MAAO,KAFK,6CAKV,WhBtBE,QgBuBF,MhBpDG,KgBqDH,UAAW,MACX,YAAa,MACb,YAAa,IAIjB,qCACE,ahB/BI,QgB8BQ,4CAAA,6CAKV,ahBnCE,QgB0CJ,6CACE,WhB3CE,mBgB4CF,MhBzEG,KgBgFL,4CACE,ahBpDE,QgBoDmB,YAAY,YAGrB,oDACZ,ahBxDE,QgBwDmB,YAAY,YAMrC,8BACE,WhB5FK,KgB6FL,ahBhEI,QgBkEJ,6CACE,WhBnEE,QgByEN,yBACE,MhB1EI,QgBgFJ,gDACE,WhBjFE,QgBoFU,uDACA,wDACZ,OAAQ,IAAI,MhBnHT,KgBuHS,wDACV,mBhB3FA,mBgB4FA,kBhB5FA,mBgB+FU,uDACV,iBhBhGA,mBgBiGA,oBhBjGA,mBgBwGJ,yCACE,iBhBzGE,mBgBgHJ,qCACE,WhBjHE,QgBoHJ,2CACE,WAAY,EAAE,EAAE,KhBrHd,OAAO,CgBqHoB,EAAE,EAAE,IhBrH/B,QgBwHJ,qCACE,iBhBzHE,QgB0HF,kBhB1HE,QgBiIJ,2CACE,WhBlIE,QgBmIF,MhBnIE,QgBoIF,WAAY,MAAM,EhBjKf,IAAO,CgBiK6B,MAAM,EhBjK1C,KgBoKL,2CACE,WAAY,MAAM,EAAE,EAAE,EAAE,IhBxItB,OAAO,CgBwI2B,MAAM,EAAE,EAAE,EAAE,IhBrK7C,KgB4KL,uCACE,iBhBhJE,QgBiJF,WAAY,MAAO,KAAI,EhBjJrB,OAAO,CgBiJwB,MAAM,EAAG,KhBjJxC,OAAO,CgBiJ6C,MAAM,EAAE,IhB9K3D,oBAAO,CgB8KsF,MAAM,EAAE,IhB9KrG,qBgBiLL,uCACE,iBAAkB,oEAClB,OAAQ,KAMZ,wCACE,MhB7JI,QgBpBJ,kCACE,WhBoBE,QgBdN,+BACE,WhBiEU,QgB/DV,8CACE,WhBUE,QgBPJ,8CACE,iBAAkB,qJAON,qDACZ,MhBFE,oBgBSJ,yCACE,WhBVE,QgBgBN,wCACE,OAAQ,MACR,MAAO,KAFK,gDAKV,WhBrBE,QgBsBF,MhB8BQ,QgB7BR,UAAW,MACX,YAAa,MACb,YAAa,IAIjB,wCACE,ahB9BI,QgB6BQ,+CAAA,gDAKV,ahBlCE,QgByCJ,gDACE,WhB1CE,oBgB2CF,MhBSQ,QgBFV,+CACE,ahBnDE,QgBmDmB,YAAY,YAGrB,uDACZ,ahBvDE,QgBuDmB,YAAY,YAMrC,iCACE,WhBVU,QgBWV,ahB/DI,QgBiEJ,gDACE,WhBlEE,QgBwEN,4BACE,MhBzEI,QgB+EJ,mDACE,WhBhFE,QgBmFU,0DACA,2DACZ,OAAQ,IAAI,MhBjCJ,QgBqCI,2DACV,mBhB1FA,oBgB2FA,kBhB3FA,oBgB8FU,0DACV,iBhB/FA,oBgBgGA,oBhBhGA,oBgBuGJ,4CACE,iBhBxGE,oBgB+GJ,wCACE,WhBhHE,QgBmHJ,8CACE,WAAY,EAAE,EAAE,KhBpHd,OAAO,CgBoHoB,EAAE,EAAE,IhBpH/B,QgBuHJ,wCACE,iBhBxHE,QgByHF,kBhBzHE,QgBgIJ,8CACE,WhBjIE,QgBkIF,MhBlIE,QgBmIF,WAAY,MAAM,EhB/EV,OAAO,CgB+EwB,MAAM,EhB/ErC,QgBkFV,8CACE,WAAY,MAAM,EAAE,EAAE,EAAE,IhBvItB,OAAO,CgBuI2B,MAAM,EAAE,EAAE,EAAE,IhBnFxC,QgB0FV,0CACE,iBhB/IE,QgBgJF,WAAY,MAAO,KAAI,EhBhJrB,OAAO,CgBgJwB,MAAM,EAAG,KhBhJxC,OAAO,CgBgJ6C,MAAM,EAAE,IhB5FtD,iBAAO,CgB4FiF,MAAM,EAAE,IhB5FhG,kBgB+FV,0CACE,iBAAkB,8DAClB,OAAQ,KAMZ,2CACE,MhB5JI,QgBrBJ,kCACE,WhBqBE,QgBfN,+BACE,WhBiEU,QgB/DV,8CACE,WhBWE,QgBRJ,8CACE,iBAAkB,qJAON,qDACZ,MhBDE,mBgBQJ,yCACE,WhBTE,QgBeN,wCACE,OAAQ,MACR,MAAO,KAFK,gDAKV,WhBpBE,QgBqBF,MhB8BQ,QgB7BR,UAAW,MACX,YAAa,MACb,YAAa,IAIjB,wCACE,ahB7BI,QgB4BQ,+CAAA,gDAKV,ahBjCE,QgBwCJ,gDACE,WhBzCE,mBgB0CF,MhBSQ,QgBFV,+CACE,ahBlDE,QgBkDmB,YAAY,YAGrB,uDACZ,ahBtDE,QgBsDmB,YAAY,YAMrC,iCACE,WhBVU,QgBWV,ahB9DI,QgBgEJ,gDACE,WhBjEE,QgBuEN,4BACE,MhBxEI,QgB8EJ,mDACE,WhB/EE,QgBkFU,0DACA,2DACZ,OAAQ,IAAI,MhBjCJ,QgBqCI,2DACV,mBhBzFA,mBgB0FA,kBhB1FA,mBgB6FU,0DACV,iBhB9FA,mBgB+FA,oBhB/FA,mBgBsGJ,4CACE,iBhBvGE,mBgB8GJ,wCACE,WhB/GE,QgBkHJ,8CACE,WAAY,EAAE,EAAE,KhBnHd,OAAO,CgBmHoB,EAAE,EAAE,IhBnH/B,QgBsHJ,wCACE,iBhBvHE,QgBwHF,kBhBxHE,QgB+HJ,8CACE,WhBhIE,QgBiIF,MhBjIE,QgBkIF,WAAY,MAAM,EhB/EV,OAAO,CgB+EwB,MAAM,EhB/ErC,QgBkFV,8CACE,WAAY,MAAM,EAAE,EAAE,EAAE,IhBtItB,OAAO,CgBsI2B,MAAM,EAAE,EAAE,EAAE,IhBnFxC,QgB0FV,0CACE,iBhB9IE,QgB+IF,WAAY,MAAO,KAAI,EhB/IrB,OAAO,CgB+IwB,MAAM,EAAG,KhB/IxC,OAAO,CgB+I6C,MAAM,EAAE,IhB5FtD,iBAAO,CgB4FiF,MAAM,EAAE,IhB5FhG,kBgB+FV,0CACE,iBAAkB,8DAClB,OAAQ,KAMZ,2CACE,MhB3JI,QgBtBJ,iCACE,WhBsBE,QgBhBN,8BACE,WhBjBK,KgBmBL,6CACE,WhBYE,QgBTJ,6CACE,iBAAkB,8JAON,oDACZ,MhBAE,mBgBOJ,wCACE,WhBRE,QgBcN,uCACE,OAAQ,MACR,MAAO,KAFK,+CAKV,WhBnBE,QgBoBF,MhBpDG,KgBqDH,UAAW,MACX,YAAa,MACb,YAAa,IAIjB,uCACE,ahB5BI,QgB2BQ,8CAAA,+CAKV,ahBhCE,QgBuCJ,+CACE,WhBxCE,mBgByCF,MhBzEG,KgBgFL,8CACE,ahBjDE,QgBiDmB,YAAY,YAGrB,sDACZ,ahBrDE,QgBqDmB,YAAY,YAMrC,gCACE,WhB5FK,KgB6FL,ahB7DI,QgB+DJ,+CACE,WhBhEE,QgBsEN,2BACE,MhBvEI,QgB6EJ,kDACE,WhB9EE,QgBiFU,yDACA,0DACZ,OAAQ,IAAI,MhBnHT,KgBuHS,0DACV,mBhBxFA,mBgByFA,kBhBzFA,mBgB4FU,yDACV,iBhB7FA,mBgB8FA,oBhB9FA,mBgBqGJ,2CACE,iBhBtGE,mBgB6GJ,uCACE,WhB9GE,QgBiHJ,6CACE,WAAY,EAAE,EAAE,KhBlHd,OAAO,CgBkHoB,EAAE,EAAE,IhBlH/B,QgBqHJ,uCACE,iBhBtHE,QgBuHF,kBhBvHE,QgB8HJ,6CACE,WhB/HE,QgBgIF,MhBhIE,QgBiIF,WAAY,MAAM,EhBjKf,IAAO,CgBiK6B,MAAM,EhBjK1C,KgBoKL,6CACE,WAAY,MAAM,EAAE,EAAE,EAAE,IhBrItB,OAAO,CgBqI2B,MAAM,EAAE,EAAE,EAAE,IhBrK7C,KgB4KL,yCACE,iBhB7IE,QgB8IF,WAAY,MAAO,KAAI,EhB9IrB,OAAO,CgB8IwB,MAAM,EAAG,KhB9IxC,OAAO,CgB8I6C,MAAM,EAAE,IhB9K3D,oBAAO,CgB8KsF,MAAM,EAAE,IhB9KrG,qBgBiLL,yCACE,iBAAkB,oEAClB,OAAQ,KAMZ,0CACE,MhB1JI,QgBvBJ,gCACE,WhBuBE,QgBjBN,6BACE,WhBjBK,KgBmBL,4CACE,WhBaE,QgBVJ,4CACE,iBAAkB,8JAON,mDACZ,MhBCE,oBgBMJ,uCACE,WhBPE,QgBaN,sCACE,OAAQ,MACR,MAAO,KAFK,8CAKV,WhBlBE,QgBmBF,MhBpDG,KgBqDH,UAAW,MACX,YAAa,MACb,YAAa,IAIjB,sCACE,ahB3BI,QgB0BQ,6CAAA,8CAKV,ahB/BE,QgBsCJ,8CACE,WhBvCE,oBgBwCF,MhBzEG,KgBgFL,6CACE,ahBhDE,QgBgDmB,YAAY,YAGrB,qDACZ,ahBpDE,QgBoDmB,YAAY,YAMrC,+BACE,WhB5FK,KgB6FL,ahB5DI,QgB8DJ,8CACE,WhB/DE,QgBqEN,0BACE,MhBtEI,QgB4EJ,iDACE,WhB7EE,QgBgFU,wDACA,yDACZ,OAAQ,IAAI,MhBnHT,KgBuHS,yDACV,mBhBvFA,oBgBwFA,kBhBxFA,oBgB2FU,wDACV,iBhB5FA,oBgB6FA,oBhB7FA,oBgBoGJ,0CACE,iBhBrGE,oBgB4GJ,sCACE,WhB7GE,QgBgHJ,4CACE,WAAY,EAAE,EAAE,KhBjHd,OAAO,CgBiHoB,EAAE,EAAE,IhBjH/B,QgBoHJ,sCACE,iBhBrHE,QgBsHF,kBhBtHE,QgB6HJ,4CACE,WhB9HE,QgB+HF,MhB/HE,QgBgIF,WAAY,MAAM,EhBjKf,IAAO,CgBiK6B,MAAM,EhBjK1C,KgBoKL,4CACE,WAAY,MAAM,EAAE,EAAE,EAAE,IhBpItB,OAAO,CgBoI2B,MAAM,EAAE,EAAE,EAAE,IhBrK7C,KgB4KL,wCACE,iBhB5IE,QgB6IF,WAAY,MAAO,KAAI,EhB7IrB,OAAO,CgB6IwB,MAAM,EAAG,KhB7IxC,OAAO,CgB6I6C,MAAM,EAAE,IhB9K3D,oBAAO,CgB8KsF,MAAM,EAAE,IhB9KrG,qBgBiLL,wCACE,iBAAkB,oEAClB,OAAQ,KAMZ,yCACE,MhBzJI,QgBxBJ,gCACE,WhBwBE,QgBlBN,6BACE,WhBjBK,KgBmBL,4CACE,WhBcE,QgBXJ,4CACE,iBAAkB,8JAON,mDACZ,MhBEE,oBgBKJ,uCACE,WhBNE,QgBYN,sCACE,OAAQ,MACR,MAAO,KAFK,8CAKV,WhBjBE,QgBkBF,MhBpDG,KgBqDH,UAAW,MACX,YAAa,MACb,YAAa,IAIjB,sCACE,ahB1BI,QgByBQ,6CAAA,8CAKV,ahB9BE,QgBqCJ,8CACE,WhBtCE,oBgBuCF,MhBzEG,KgBgFL,6CACE,ahB/CE,QgB+CmB,YAAY,YAGrB,qDACZ,ahBnDE,QgBmDmB,YAAY,YAMrC,+BACE,WhB5FK,KgB6FL,ahB3DI,QgB6DJ,8CACE,WhB9DE,QgBoEN,0BACE,MhBrEI,QgB2EJ,iDACE,WhB5EE,QgB+EU,wDACA,yDACZ,OAAQ,IAAI,MhBnHT,KgBuHS,yDACV,mBhBtFA,oBgBuFA,kBhBvFA,oBgB0FU,wDACV,iBhB3FA,oBgB4FA,oBhB5FA,oBgBmGJ,0CACE,iBhBpGE,oBgB2GJ,sCACE,WhB5GE,QgB+GJ,4CACE,WAAY,EAAE,EAAE,KhBhHd,OAAO,CgBgHoB,EAAE,EAAE,IhBhH/B,QgBmHJ,sCACE,iBhBpHE,QgBqHF,kBhBrHE,QgB4HJ,4CACE,WhB7HE,QgB8HF,MhB9HE,QgB+HF,WAAY,MAAM,EhBjKf,IAAO,CgBiK6B,MAAM,EhBjK1C,KgBoKL,4CACE,WAAY,MAAM,EAAE,EAAE,EAAE,IhBnItB,OAAO,CgBmI2B,MAAM,EAAE,EAAE,EAAE,IhBrK7C,KgB4KL,wCACE,iBhB3IE,QgB4IF,WAAY,MAAO,KAAI,EhB5IrB,OAAO,CgB4IwB,MAAM,EAAG,KhB5IxC,OAAO,CgB4I6C,MAAM,EAAE,IhB9K3D,oBAAO,CgB8KsF,MAAM,EAAE,IhB9KrG,qBgBiLL,wCACE,iBAAkB,oEAClB,OAAQ,KAMZ,yCACE,MhBxJI,QgBzBJ,iCACE,WhBVG,KgBgBP,8BACE,WhBiEU,QgB/DV,6CACE,WhBpBG,KgBuBL,6CACE,iBAAkB,qJAON,oDACZ,MhBhCG,qBgBuCL,wCACE,WhBxCG,KgB8CP,uCACE,OAAQ,MACR,MAAO,KAFK,+CAKV,WhBnDG,KgBoDH,MhB8BQ,QgB7BR,UAAW,MACX,YAAa,MACb,YAAa,IAIjB,uCACE,ahB5DK,KgB2DO,8CAAA,+CAKV,ahBhEG,KgBuEL,+CACE,WhBxEG,qBgByEH,MhBSQ,QgBFV,8CACE,ahBjFG,KgBiFkB,YAAY,YAGrB,sDACZ,ahBrFG,KgBqFkB,YAAY,YAMrC,gCACE,WhBVU,QgBWV,ahB7FK,KgB+FL,+CACE,WhBhGG,KgBsGP,2BACE,MhBvGK,KgB6GL,kDACE,WhB9GG,KgBiHS,yDACA,0DACZ,OAAQ,IAAI,MhBjCJ,QgBqCI,0DACV,mBhBxHC,qBgByHD,kBhBzHC,qBgB4HS,yDACV,iBhB7HC,qBgB8HD,oBhB9HC,qBgBqIL,2CACE,iBhBtIG,qBgB6IL,uCACE,WhB9IG,KgBiJL,6CACE,WAAY,EAAE,EAAE,KhBlJb,IAAO,CgBkJmB,EAAE,EAAE,IhBlJ9B,KgBqJL,uCACE,iBhBtJG,KgBuJH,kBhBvJG,KgB8JL,6CACE,WhB/JG,KgBgKH,MhBhKG,KgBiKH,WAAY,MAAM,EhB/EV,OAAO,CgB+EwB,MAAM,EhB/ErC,QgBkFV,6CACE,WAAY,MAAM,EAAE,EAAE,EAAE,IhBrKrB,IAAO,CgBqK0B,MAAM,EAAE,EAAE,EAAE,IhBnFxC,QgB0FV,yCACE,iBhB7KG,KgB8KH,WAAY,MAAO,KAAI,EhB9KpB,IAAO,CgB8KuB,MAAM,EAAG,KhB9KvC,IAAO,CgB8K4C,MAAM,EAAE,IhB5FtD,iBAAO,CgB4FiF,MAAM,EAAE,IhB5FhG,kBgB+FV,yCACE,iBAAkB,8DAClB,OAAQ,KAMZ,0CACE,MhB1LK,KgBSL,gCACE,WhBJG,QgBUP,6BACE,WhBjBK,KgBmBL,4CACE,WhBdG,QgBiBL,4CACE,iBAAkB,8JAON,mDACZ,MhB1BG,qBgBiCL,uCACE,WhBlCG,QgBwCP,sCACE,OAAQ,MACR,MAAO,KAFK,8CAKV,WhB7CG,QgB8CH,MhBpDG,KgBqDH,UAAW,MACX,YAAa,MACb,YAAa,IAIjB,sCACE,ahBtDK,QgBqDO,6CAAA,8CAKV,ahB1DG,QgBiEL,8CACE,WhBlEG,qBgBmEH,MhBzEG,KgBgFL,6CACE,ahB3EG,QgB2EkB,YAAY,YAGrB,qDACZ,ahB/EG,QgB+EkB,YAAY,YAMrC,+BACE,WhB5FK,KgB6FL,ahBvFK,QgByFL,8CACE,WhB1FG,QgBgGP,0BACE,MhBjGK,QgBuGL,iDACE,WhBxGG,QgB2GS,wDACA,yDACZ,OAAQ,IAAI,MhBnHT,KgBuHS,yDACV,mBhBlHC,qBgBmHD,kBhBnHC,qBgBsHS,wDACV,iBhBvHC,qBgBwHD,oBhBxHC,qBgB+HL,0CACE,iBhBhIG,qBgBuIL,sCACE,WhBxIG,QgB2IL,4CACE,WAAY,EAAE,EAAE,KhB5Ib,OAAO,CgB4ImB,EAAE,EAAE,IhB5I9B,QgB+IL,sCACE,iBhBhJG,QgBiJH,kBhBjJG,QgBwJL,4CACE,WhBzJG,QgB0JH,MhB1JG,QgB2JH,WAAY,MAAM,EhBjKf,IAAO,CgBiK6B,MAAM,EhBjK1C,KgBoKL,4CACE,WAAY,MAAM,EAAE,EAAE,EAAE,IhB/JrB,OAAO,CgB+J0B,MAAM,EAAE,EAAE,EAAE,IhBrK7C,KgB4KL,wCACE,iBhBvKG,QgBwKH,WAAY,MAAO,KAAI,EhBxKpB,OAAO,CgBwKuB,MAAM,EAAG,KhBxKvC,OAAO,CgBwK4C,MAAM,EAAE,IhB9K3D,oBAAO,CgB8KsF,MAAM,EAAE,IhB9KrG,qBgBiLL,wCACE,iBAAkB,oEAClB,OAAQ,KAMZ,yCACE,MhBpLK,QgBGL,qCACE,WhBFG,QgBQP,kCACE,WhBjBK,KgBmBL,iDACE,WhBZG,QgBeL,iDACE,iBAAkB,8JAON,wDACZ,MhBxBG,kBgB+BL,4CACE,WhBhCG,QgBsCP,2CACE,OAAQ,MACR,MAAO,KAFK,mDAKV,WhB3CG,QgB4CH,MhBpDG,KgBqDH,UAAW,MACX,YAAa,MACb,YAAa,IAIjB,2CACE,ahBpDK,QgBmDO,kDAAA,mDAKV,ahBxDG,QgB+DL,mDACE,WhBhEG,kBgBiEH,MhBzEG,KgBgFL,kDACE,ahBzEG,QgByEkB,YAAY,YAGrB,0DACZ,ahB7EG,QgB6EkB,YAAY,YAMrC,oCACE,WhB5FK,KgB6FL,ahBrFK,QgBuFL,mDACE,WhBxFG,QgB8FP,+BACE,MhB/FK,QgBqGL,sDACE,WhBtGG,QgByGS,6DACA,8DACZ,OAAQ,IAAI,MhBnHT,KgBuHS,8DACV,mBhBhHC,kBgBiHD,kBhBjHC,kBgBoHS,6DACV,iBhBrHC,kBgBsHD,oBhBtHC,kBgB6HL,+CACE,iBhB9HG,kBgBqIL,2CACE,WhBtIG,QgByIL,iDACE,WAAY,EAAE,EAAE,KhB1Ib,OAAO,CgB0ImB,EAAE,EAAE,IhB1I9B,QgB6IL,2CACE,iBhB9IG,QgB+IH,kBhB/IG,QgBsJL,iDACE,WhBvJG,QgBwJH,MhBxJG,QgByJH,WAAY,MAAM,EhBjKf,IAAO,CgBiK6B,MAAM,EhBjK1C,KgBoKL,iDACE,WAAY,MAAM,EAAE,EAAE,EAAE,IhB7JrB,OAAO,CgB6J0B,MAAM,EAAE,EAAE,EAAE,IhBrK7C,KgB4KL,6CACE,iBhBrKG,QgBsKH,WAAY,MAAO,KAAI,EhBtKpB,OAAO,CgBsKuB,MAAM,EAAG,KhBtKvC,OAAO,CgBsK4C,MAAM,EAAE,IhB9K3D,oBAAO,CgB8KsF,MAAM,EAAE,IhB9KrG,qBgBiLL,6CACE,iBAAkB,oEAClB,OAAQ,KAMZ,8CACE,MhBlLK,QiBPX,kBACE,OjBqM4B,IiBrMA,MjBEnB,QiBDT,cjBuM4B,OiBtM5B,OAAQ,QACR,UAAW,IACX,QAAS,aACT,YAAa,MACb,SAAU,OACV,SAAU,SACV,WAAY,KACZ,WAAY,aAAA,YAAA,IAAA,CAAA,WAAA,YAAA,KACZ,YAAa,KACb,eAAgB,OAChB,QAAS,EAET,8CACE,cjByL0B,OiBxL1B,QAAS,aACT,IAAK,EACL,UAAW,mBAnBE,+BAwBb,WjB0S0B,EAAE,EAAE,EAFJ,MAxSpB,oBiBIR,+CADA,8CAEA,0CACE,WAAY,WACZ,OAAQ,QACR,QAAS,WACT,UAAW,KACX,YAAa,IACb,YAAa,OACb,QAAS,OAAA,MACT,eAAgB,OAIlB,+CADA,8CAEE,WAAY,OACZ,QAAS,EAFiB,wEADD,uEAMvB,WjB7CK,QiB8CL,MjBkCU,QiBxCc,wEADD,uEAYrB,WjB5BE,QiB6BF,MjBtDG,KiB0CmB,0EADD,yEAYrB,WjB/CG,QiBgDH,MjBtDG,KiB0CmB,wEADD,uEAYrB,WjBrBE,QiBsBF,MjBtDG,KiB0CmB,qEADD,oEAYrB,WjBnBE,QiBoBF,MjBtDG,KiB0CmB,wEADD,uEAYrB,WjBtBE,QiBuBF,MjB4BQ,QiBxCc,uEADD,sEAYrB,WjBxBE,QiByBF,MjBtDG,KiB0CmB,sEADD,qEAYrB,WjBpDG,QiBqDH,MjB4BQ,QiBxCc,qEADD,oEAYrB,WjB7CG,QiB8CH,MjBtDG,KiB0CmB,0EADD,yEAmBrB,WbhEI,QaiEJ,MjB7DG,KiB0CmB,qEADD,oEAmBrB,Wb/DD,QagEC,MjB7DG,KiB0CmB,sEADD,qEAmBrB,Wb7DA,Qa8DA,MjB7DG,KiB0CmB,qEADD,oEAmBrB,Wb5DD,Qa6DC,MjBqBQ,QiBxCc,wEADD,uEAmBrB,Wb1DE,Qa2DF,MjB7DG,KiB0CmB,uEADD,sEAmBrB,WbxDC,QayDD,MjB7DG,KiB0CmB,qEADD,oEAmBrB,WjBnCE,QiBoCF,MjB7DG,KiB0CmB,uEADD,sEAmBrB,WjBlCE,QiBmCF,MjB7DG,KiB0CmB,uEADD,sEAmBrB,WjBjCE,QiBkCF,MjB7DG,KiB0CmB,qEADD,oEAmBrB,WjBhCE,QiBiCF,MjB7DG,KiB0CmB,oEADD,mEAmBrB,WjB/BE,QiBgCF,MjB7DG,KiB0CmB,uEADD,sEAmBrB,WjB9BE,QiB+BF,MjBqBQ,QiBxCc,uEADD,sEAmBrB,WjB7BE,QiB8BF,MjBqBQ,QiBxCc,sEADD,qEAmBrB,WjB5BE,QiB6BF,MjB7DG,KiB0CmB,qEADD,oEAmBrB,WjB3BE,QiB4BF,MjB7DG,KiB0CmB,qEADD,oEAmBrB,WjB1BE,QiB2BF,MjB7DG,KiB0CmB,sEADD,qEAmBrB,WjB5DG,KiB6DH,MjBqBQ,QiBxCc,qEADD,oEAmBrB,WjBtDG,QiBuDH,MjB7DG,KiB0CmB,0EADD,yEAmBrB,WjBpDG,QiBqDH,MjB7DG,KiBkET,8CACE,0BApEoC,MAqEpC,uBArEoC,MAwEtC,+CACE,2BAzEoC,MA0EpC,wBA1EoC,MA8EhC,uCADA,oCAGJ,KAAM,EACN,OAAQ,EACR,QAAS,EACT,SAAU,SACV,IAAK,EACL,WAAY,OACZ,QAAS,GAKT,qEADA,oEAEA,gEACE,UAAW,QACX,YAAa,IACb,QAAS,MAAA,MAMX,sEADA,qEAEA,iEACE,UAAW,QACX,YAAa,IACb,QAAS,MAAA,MAMX,sEADA,qEAEA,iEACE,UAAW,QACX,YAAa,aACb,QAAS,MAAA,MAjHE,4CAAA,iDAAA,4CAwHb,OAAQ,QAGR,yEADA,wEAEA,oEADA,8EADA,6EAEA,yEADA,yEADA,wEAEA,oEACE,OAAQ,QAER,QAAS,GAIc,uEACzB,WAAY,YAAA,IAIZ,uEACE,cAAe,EA3ImB,MAAA,MA2I6D,EAGjG,wEACE,cA/IkC,MA+IoB,EAAE,EA/ItB,MA0JU,wFAD1B,8DAEpB,2BA3JoC,MA4JpC,wBA5JoC,MAgKS,uFADxB,+DAErB,0BAjKoC,MAkKpC,uBAlKoC,MCJxC,YACE,OAAQ,eACR,QAAS,cACT,MAAO,eAIT,mBACE,WAAY,MAGd,6BACE,OAAQ,EACR,KAAM,cACN,OAAQ,IACR,OAAQ,KACR,SAAU,OACV,QAAS,EACT,SAAU,SACV,MAAO,IAGT,gBACE,WlBjBS,QkBkBT,OAAQ,IAAI,OlBhBH,QkBiBT,cAAe,KAIjB,OACE,SAAU,OACV,SAAU", "sourcesContent": ["/*!\n *   AdminLTE v3.0.5\n *     Only Plugins\n *   Author: Colorlib\n *   Website: AdminLTE.io <http://adminlte.io>\n *   License: Open source - MIT <http://opensource.org/licenses/MIT>\n */\n// Bootstrap\n// ---------------------------------------------------\n@import '~bootstrap/scss/functions';\n@import 'bootstrap-variables';\n@import '~bootstrap/scss/mixins';\n// @import '~bootstrap/scss/bootstrap';\n\n// Variables and Mixins\n// ---------------------------------------------------\n@import 'variables';\n@import 'mixins';\n\n@import 'parts/plugins';\n", "//\n// Plugin: Full Calendar\n//\n\n// Buttons\n.fc-button {\n  background: $gray-100;\n  background-image: none;\n  border-bottom-color: #ddd;\n  border-color: #ddd;\n  color: $gray-700;\n\n  &:hover,\n  &:active,\n  &.hover {\n    background-color: #e9e9e9;\n  }\n}\n\n// Calendar title\n.fc-header-title h2 {\n  color: #666;\n  font-size: 15px;\n  line-height: 1.6em;\n  margin-left: 10px;\n}\n\n.fc-header-right {\n  padding-right: 10px;\n}\n\n.fc-header-left {\n  padding-left: 10px;\n}\n\n// Calendar table header cells\n.fc-widget-header {\n  background: #fafafa;\n}\n\n.fc-grid {\n  border: 0;\n  width: 100%;\n}\n\n.fc-widget-header:first-of-type,\n.fc-widget-content:first-of-type {\n  border-left: 0;\n  border-right: 0;\n}\n\n.fc-widget-header:last-of-type,\n.fc-widget-content:last-of-type {\n  border-right: 0;\n}\n\n.fc-toolbar,\n.fc-toolbar.fc-header-toolbar {\n  margin: 0;\n  padding: 1rem;\n}\n\n@include media-breakpoint-down(xs) {\n  .fc-toolbar {\n    flex-direction: column;\n\n    .fc-left {\n      order: 1;\n      margin-bottom: .5rem;\n    }\n\n    .fc-center {\n      order: 0;\n      margin-bottom: .375rem;\n    }\n\n    .fc-right {\n      order: 2;\n    }\n  }\n}\n\n.fc-day-number {\n  font-size: 20px;\n  font-weight: 300;\n  padding-right: 10px;\n}\n\n.fc-color-picker {\n  list-style: none;\n  margin: 0;\n  padding: 0;\n\n  > li {\n    float: left;\n    font-size: 30px;\n    line-height: 30px;\n    margin-right: 5px;\n\n    .fa,\n    .fas,\n    .far,\n    .fab,\n    .glyphicon,\n    .ion {\n      transition: transform linear .3s;\n\n      &:hover {\n        @include rotate(30deg);\n      }\n    }\n  }\n}\n\n#add-new-event {\n  transition: all linear .3s;\n}\n\n.external-event {\n  @include box-shadow($card-shadow);\n\n  border-radius: $border-radius;\n  cursor: move;\n  font-weight: bold;\n  margin-bottom: 4px;\n  padding: 5px 10px;\n\n  &:hover {\n    @include box-shadow(inset 0 0 90px rgba(0, 0, 0, 0.2));\n  }\n}\n", "// Variables\n//\n// Variables should follow the `$component-state-property-size` formula for\n// consistent naming. Ex: $nav-link-disabled-color and $modal-content-box-shadow-xs.\n\n\n//\n// Color system\n//\n\n// stylelint-disable\n$white:    #ffffff !default;\n$gray-100: #f8f9fa !default;\n$gray-200: #e9ecef !default;\n$gray-300: #dee2e6 !default;\n$gray-400: #ced4da !default;\n$gray-500: #adb5bd !default;\n$gray-600: #6c757d !default;\n$gray-700: #495057 !default;\n$gray-800: #343a40 !default;\n$gray-900: #212529 !default;\n$black:    #000 !default;\n\n$grays: () !default;\n$grays: map-merge((\n        \"100\": $gray-100,\n        \"200\": $gray-200,\n        \"300\": $gray-300,\n        \"400\": $gray-400,\n        \"500\": $gray-500,\n        \"600\": $gray-600,\n        \"700\": $gray-700,\n        \"800\": $gray-800,\n        \"900\": $gray-900\n), $grays);\n\n$blue:    #007bff !default;\n$indigo:  #6610f2 !default;\n$purple:  #6f42c1 !default;\n$pink:    #e83e8c !default;\n$red:     #dc3545 !default;\n$orange:  #fd7e14 !default;\n$yellow:  #ffc107 !default;\n$green:   #28a745 !default;\n$teal:    #20c997 !default;\n$cyan:    #17a2b8 !default;\n\n$colors: () !default;\n$colors: map-merge((\n        \"blue\":       $blue,\n        \"indigo\":     $indigo,\n        \"purple\":     $purple,\n        \"pink\":       $pink,\n        \"red\":        $red,\n        \"orange\":     $orange,\n        \"yellow\":     $yellow,\n        \"green\":      $green,\n        \"teal\":       $teal,\n        \"cyan\":       $cyan,\n        \"white\":      $white,\n        \"gray\":       $gray-600,\n        \"gray-dark\":  $gray-800\n), $colors);\n\n$primary:       $blue !default;\n$secondary:     $gray-600 !default;\n$success:       $green !default;\n$info:          $cyan !default;\n$warning:       $yellow !default;\n$danger:        $red !default;\n$light:         $gray-100 !default;\n$dark:          $gray-800 !default;\n\n$theme-colors: () !default;\n$theme-colors: map-merge((\n        \"primary\":    $primary,\n        \"secondary\":  $secondary,\n        \"success\":    $success,\n        \"info\":       $info,\n        \"warning\":    $warning,\n        \"danger\":     $danger,\n        \"light\":      $light,\n        \"dark\":       $dark\n), $theme-colors);\n// stylelint-enable\n\n// Set a specific jump point for requesting color jumps\n$theme-color-interval:      8% !default;\n\n// The yiq lightness value that determines when the lightness of color changes from \"dark\" to \"light\". Acceptable values are between 0 and 255.\n$yiq-contrasted-threshold: 150 !default;\n\n// Customize the light and dark text colors for use in our YIQ color contrast function.\n$yiq-text-dark: #1F2D3D !default;\n$yiq-text-light: $white !default;\n\n// Options\n//\n// Quickly modify global styling by enabling or disabling optional features.\n\n$enable-caret:                                true !default;\n$enable-rounded:                              true !default;\n$enable-shadows:                              true !default;\n$enable-gradients:                            false !default;\n$enable-transitions:                          true !default;\n$enable-prefers-reduced-motion-media-query:   true !default;\n$enable-hover-media-query:                    false !default; // Deprecated, no longer affects any compiled CSS\n$enable-grid-classes:                         true !default;\n$enable-pointer-cursor-for-buttons:           true !default;\n$enable-print-styles:                         true !default;\n$enable-responsive-font-sizes:                false !default;\n$enable-validation-icons:                     true !default;\n$enable-deprecation-messages:                 true !default;\n\n// Spacing\n//\n// Control the default styling of most Bootstrap elements by modifying these\n// variables. Mostly focused on spacing.\n// You can add more entries to the $spacers map, should you need more variation.\n\n// stylelint-disable\n$spacer: 1rem !default;\n$spacers: () !default;\n$spacers: map-merge((\n        0: 0,\n        1: ($spacer * .25),\n        2: ($spacer * .5),\n        3: $spacer,\n        4: ($spacer * 1.5),\n        5: ($spacer * 3)\n), $spacers);\n\n// This variable affects the `.h-*` and `.w-*` classes.\n$sizes: () !default;\n$sizes: map-merge((\n        25: 25%,\n        50: 50%,\n        75: 75%,\n        100: 100%\n), $sizes);\n// stylelint-enable\n\n// Body\n//\n// Settings for the `<body>` element.\n\n$body-bg:                   $white !default;\n$body-color:                $gray-900 !default;\n\n// Links\n//\n// Style anchor elements.\n\n$link-color:                theme-color(\"primary\") !default;\n$link-decoration:           none !default;\n$link-hover-color:          darken($link-color, 15%) !default;\n$link-hover-decoration:     none !default;\n\n// Paragraphs\n//\n// Style p element.\n\n$paragraph-margin-bottom:   1rem !default;\n\n\n// Grid breakpoints\n//\n// Define the minimum dimensions at which your layout will change,\n// adapting to different screen sizes, for use in media queries.\n\n$grid-breakpoints: (\n        xs: 0,\n        sm: 576px,\n        md: 768px,\n        lg: 992px,\n        xl: 1200px\n) !default;\n\n@include _assert-ascending($grid-breakpoints, \"$grid-breakpoints\");\n@include _assert-starts-at-zero($grid-breakpoints);\n\n\n// Grid containers\n//\n// Define the maximum width of `.container` for different screen sizes.\n\n$container-max-widths: (\n        sm: 540px,\n        md: 720px,\n        lg: 960px,\n        xl: 1140px\n) !default;\n\n@include _assert-ascending($container-max-widths, \"$container-max-widths\");\n\n\n// Grid columns\n//\n// Set the number of columns and specify the width of the gutters.\n\n$grid-columns:                12 !default;\n$grid-gutter-width:           15px !default;\n\n// Components\n//\n// Define common padding and border radius sizes and more.\n\n$line-height-lg:              1.5 !default;\n$line-height-sm:              1.5 !default;\n\n$border-width:                1px !default;\n$border-color:                $gray-300 !default;\n\n$border-radius:               .25rem !default;\n$border-radius-lg:            .3rem !default;\n$border-radius-sm:            .2rem !default;\n\n$component-active-color:      $white !default;\n$component-active-bg:         theme-color(\"primary\") !default;\n\n$caret-width:                 .3em !default;\n\n$transition-base:             all .2s ease-in-out !default;\n$transition-fade:             opacity .15s linear !default;\n$transition-collapse:         height .35s ease !default;\n\n\n// Fonts\n//\n// Font, line-height, and color for body text, headings, and more.\n\n// stylelint-disable value-keyword-case\n$font-family-sans-serif:      \"Source Sans Pro\", -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\" !default;\n$font-family-monospace:       SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace !default;\n$font-family-base:            $font-family-sans-serif !default;\n// stylelint-enable value-keyword-case\n\n$font-size-base:              1rem !default; // Assumes the browser default, typically `16px`\n$font-size-lg:                ($font-size-base * 1.25) !default;\n$font-size-sm:                ($font-size-base * .875) !default;\n\n$font-weight-light:           300 !default;\n$font-weight-normal:          400 !default;\n$font-weight-bold:            700 !default;\n\n$font-weight-base:            $font-weight-normal !default;\n$line-height-base:            1.5 !default;\n\n$h1-font-size:                $font-size-base * 2.5 !default;\n$h2-font-size:                $font-size-base * 2 !default;\n$h3-font-size:                $font-size-base * 1.75 !default;\n$h4-font-size:                $font-size-base * 1.5 !default;\n$h5-font-size:                $font-size-base * 1.25 !default;\n$h6-font-size:                $font-size-base !default;\n\n$headings-margin-bottom:      ($spacer / 2) !default;\n$headings-font-family:        inherit !default;\n$headings-font-weight:        500 !default;\n$headings-line-height:        1.2 !default;\n$headings-color:              inherit !default;\n\n$display1-size:               6rem !default;\n$display2-size:               5.5rem !default;\n$display3-size:               4.5rem !default;\n$display4-size:               3.5rem !default;\n\n$display1-weight:             300 !default;\n$display2-weight:             300 !default;\n$display3-weight:             300 !default;\n$display4-weight:             300 !default;\n$display-line-height:         $headings-line-height !default;\n\n$lead-font-size:              ($font-size-base * 1.25) !default;\n$lead-font-weight:            300 !default;\n\n$small-font-size:             80% !default;\n\n$text-muted:                  $gray-600 !default;\n\n$blockquote-small-color:      $gray-600 !default;\n$blockquote-font-size:        ($font-size-base * 1.25) !default;\n\n$hr-border-color:             rgba($black, .1) !default;\n$hr-border-width:             $border-width !default;\n\n$mark-padding:                .2em !default;\n\n$dt-font-weight:              $font-weight-bold !default;\n\n$kbd-box-shadow:              inset 0 -.1rem 0 rgba($black, .25) !default;\n$nested-kbd-font-weight:      $font-weight-bold !default;\n\n$list-inline-padding:         .5rem !default;\n\n$mark-bg:                     #fcf8e3 !default;\n\n$hr-margin-y:                 $spacer !default;\n\n\n// Tables\n//\n// Customizes the `.table` component with basic values, each used across all table variations.\n\n$table-cell-padding:          .75rem !default;\n$table-cell-padding-sm:       .3rem !default;\n\n$table-bg:                    transparent !default;\n$table-accent-bg:             rgba($black, .05) !default;\n$table-hover-bg:              rgba($black, .075) !default;\n$table-active-bg:             $table-hover-bg !default;\n\n$table-border-width:          $border-width !default;\n$table-border-color:          $gray-300 !default;\n\n$table-head-bg:               $gray-200 !default;\n$table-head-color:            $gray-700 !default;\n\n$table-dark-bg:               $gray-900 !default;\n$table-dark-accent-bg:        rgba($white, .05) !default;\n$table-dark-hover-bg:         rgba($white, .075) !default;\n$table-dark-border-color:     lighten($gray-900, 10%) !default;\n$table-dark-color:            $body-bg !default;\n\n\n// Buttons + Forms\n//\n// Shared variables that are reassigned to `$input-` and `$btn-` specific variables.\n\n$input-btn-padding-y:         .375rem !default;\n$input-btn-padding-x:         .75rem !default;\n$input-btn-line-height:       $line-height-base !default;\n\n$input-btn-focus-width:       .2rem !default;\n$input-btn-focus-color:       rgba($component-active-bg, .25) !default;\n$input-btn-focus-box-shadow:  0 0 0 $input-btn-focus-width $input-btn-focus-color !default;\n\n$input-btn-padding-y-sm:      .25rem !default;\n$input-btn-padding-x-sm:      .5rem !default;\n$input-btn-line-height-sm:    $line-height-sm !default;\n\n$input-btn-padding-y-lg:      .5rem !default;\n$input-btn-padding-x-lg:      1rem !default;\n$input-btn-line-height-lg:    $line-height-lg !default;\n\n$input-btn-border-width:      $border-width !default;\n\n\n// Buttons\n//\n// For each of Bootstrap's buttons, define text, background, and border color.\n\n$btn-padding-y:               $input-btn-padding-y !default;\n$btn-padding-x:               $input-btn-padding-x !default;\n$btn-line-height:             $input-btn-line-height !default;\n\n$btn-padding-y-sm:            $input-btn-padding-y-sm !default;\n$btn-padding-x-sm:            $input-btn-padding-x-sm !default;\n$btn-line-height-sm:          $input-btn-line-height-sm !default;\n\n$btn-padding-y-lg:            $input-btn-padding-y-lg !default;\n$btn-padding-x-lg:            $input-btn-padding-x-lg !default;\n$btn-line-height-lg:          $input-btn-line-height-lg !default;\n\n$btn-border-width:            $input-btn-border-width !default;\n\n$btn-font-weight:             $font-weight-normal !default;\n$btn-box-shadow:              none !default;\n$btn-focus-width:             0 !default;\n$btn-focus-box-shadow:        none !default;\n$btn-disabled-opacity:        .65 !default;\n$btn-active-box-shadow:       none !default;\n\n$btn-link-disabled-color:     $gray-600 !default;\n\n$btn-block-spacing-y:         .5rem !default;\n\n// Allows for customizing button radius independently from global border radius\n$btn-border-radius:           $border-radius !default;\n$btn-border-radius-lg:        $border-radius-lg !default;\n$btn-border-radius-sm:        $border-radius-sm !default;\n\n$btn-transition:              color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\n\n\n// Forms\n\n$input-padding-y:                       $input-btn-padding-y !default;\n$input-padding-x:                       $input-btn-padding-x !default;\n$input-line-height:                     $input-btn-line-height !default;\n\n$input-padding-y-sm:                    $input-btn-padding-y-sm !default;\n$input-padding-x-sm:                    $input-btn-padding-x-sm !default;\n$input-line-height-sm:                  $input-btn-line-height-sm !default;\n\n$input-padding-y-lg:                    $input-btn-padding-y-lg !default;\n$input-padding-x-lg:                    $input-btn-padding-x-lg !default;\n$input-line-height-lg:                  $input-btn-line-height-lg !default;\n\n$input-bg:                              $white !default;\n$input-disabled-bg:                     $gray-200 !default;\n\n$input-color:                           $gray-700 !default;\n$input-border-color:                    $gray-400 !default;\n$input-border-width:                    $input-btn-border-width !default;\n$input-box-shadow:                      inset 0 0 0 rgba($black, 0) !default;\n\n$input-border-radius:                   $border-radius !default;\n$input-border-radius-lg:                $border-radius-lg !default;\n$input-border-radius-sm:                $border-radius-sm !default;\n\n$input-focus-bg:                        $input-bg !default;\n$input-focus-border-color:              lighten($component-active-bg, 25%) !default;\n$input-focus-color:                     $input-color !default;\n$input-focus-width:                     0 !default;\n$input-focus-box-shadow:                none !default;\n\n$input-placeholder-color:               lighten($gray-600, 15%) !default;\n\n$input-height-border:                   $input-border-width * 2 !default;\n\n$input-height-inner:                    ($font-size-base * $input-btn-line-height) + ($input-btn-padding-y * 2) !default;\n$input-height-inner-half:               calc(#{$input-line-height * .5em} + #{$input-padding-y}) !default;\n$input-height-inner-quarter:            calc(#{$input-line-height * .25em} + #{$input-padding-y / 2}) !default;\n\n$input-height:                          calc(#{$input-height-inner} + #{$input-height-border}) !default;\n\n$input-height-inner-sm:                 ($font-size-sm * $input-btn-line-height-sm) + ($input-btn-padding-y-sm * 2) !default;\n$input-height-sm:                       calc(#{$input-height-inner-sm} + #{$input-height-border}) !default;\n\n$input-height-inner-lg:                 ($font-size-lg * $input-btn-line-height-lg) + ($input-btn-padding-y-lg * 2) !default;\n$input-height-lg:                       calc(#{$input-height-inner-lg} + #{$input-height-border}) !default;\n\n$input-transition:                      border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\n\n$form-text-margin-top:                  .25rem !default;\n\n$form-check-input-gutter:               1.25rem !default;\n$form-check-input-margin-y:             .3rem !default;\n$form-check-input-margin-x:             .25rem !default;\n\n$form-check-inline-margin-x:            .75rem !default;\n$form-check-inline-input-margin-x:      .3125rem !default;\n\n$form-group-margin-bottom:              1rem !default;\n\n$input-group-addon-color:               $input-color !default;\n$input-group-addon-bg:                  $gray-200 !default;\n$input-group-addon-border-color:        $input-border-color !default;\n\n$custom-control-gutter:                 .5rem !default;\n$custom-control-spacer-x:               1rem !default;\n\n$custom-control-indicator-size:         1rem !default;\n$custom-control-indicator-bg:           $gray-300 !default;\n$custom-control-indicator-bg-size:      50% 50% !default;\n$custom-control-indicator-box-shadow:   inset 0 .25rem .25rem rgba($black, .1) !default;\n\n$custom-control-indicator-disabled-bg:          $gray-200 !default;\n$custom-control-label-disabled-color:           $gray-600 !default;\n\n$custom-control-indicator-checked-color:        $component-active-color !default;\n$custom-control-indicator-checked-bg:           $component-active-bg !default;\n$custom-control-indicator-checked-disabled-bg:  rgba(theme-color(\"primary\"), .5) !default;\n$custom-control-indicator-checked-box-shadow:   none !default;\n\n$custom-control-indicator-focus-box-shadow:     0 0 0 1px $body-bg, $input-btn-focus-box-shadow !default;\n\n$custom-control-indicator-active-color:         $component-active-color !default;\n$custom-control-indicator-active-bg:            lighten($component-active-bg, 35%) !default;\n$custom-control-indicator-active-box-shadow:    none !default;\n\n$custom-checkbox-indicator-border-radius:       $border-radius !default;\n$custom-checkbox-indicator-icon-checked:        str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='#{$custom-control-indicator-checked-color}' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\"), \"#\", \"%23\") !default;\n\n$custom-checkbox-indicator-indeterminate-bg:    $component-active-bg !default;\n$custom-checkbox-indicator-indeterminate-color: $custom-control-indicator-checked-color !default;\n$custom-checkbox-indicator-icon-indeterminate:  str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 4'%3E%3Cpath stroke='#{$custom-checkbox-indicator-indeterminate-color}' d='M0 2h4'/%3E%3C/svg%3E\"), \"#\", \"%23\") !default;\n$custom-checkbox-indicator-indeterminate-box-shadow: none !default;\n\n$custom-radio-indicator-border-radius:          50% !default;\n$custom-radio-indicator-icon-checked:           str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='#{$custom-control-indicator-checked-color}'/%3E%3C/svg%3E\"), \"#\", \"%23\") !default;\n\n$custom-select-padding-y:           .375rem !default;\n$custom-select-padding-x:          .75rem !default;\n$custom-select-height:              $input-height !default;\n$custom-select-indicator-padding:   1rem !default; // Extra padding to account for the presence of the background-image based indicator\n$custom-select-line-height:         $input-btn-line-height !default;\n$custom-select-color:               $input-color !default;\n$custom-select-disabled-color:      $gray-600 !default;\n$custom-select-bg:                  $white !default;\n$custom-select-disabled-bg:         $gray-200 !default;\n$custom-select-bg-size:             8px 10px !default; // In pixels because image dimensions\n$custom-select-indicator-color:     $gray-800 !default;\n$custom-select-indicator:           str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 5'%3E%3Cpath fill='#{$custom-select-indicator-color}' d='M2 0L0 2h4zm0 5L0 3h4z'/%3E%3C/svg%3E\"), \"#\", \"%23\") !default;\n$custom-select-border-width:        $input-btn-border-width !default;\n$custom-select-border-color:        $input-border-color !default;\n$custom-select-border-radius:       $border-radius !default;\n\n$custom-select-focus-border-color:  $input-focus-border-color !default;\n$custom-select-focus-box-shadow:    none !default;\n\n$custom-select-font-size-sm:        75% !default;\n$custom-select-height-sm:           $input-height-sm !default;\n\n$custom-select-font-size-lg:        125% !default;\n$custom-select-height-lg:           $input-height-lg !default;\n\n$custom-file-height:                $input-height !default;\n$custom-file-focus-border-color:    $input-focus-border-color !default;\n$custom-file-focus-box-shadow:      $custom-select-focus-box-shadow !default;\n\n$custom-file-padding-y:             $input-btn-padding-y !default;\n$custom-file-padding-x:             $input-btn-padding-x !default;\n$custom-file-line-height:           $input-btn-line-height !default;\n$custom-file-color:                 $input-color !default;\n$custom-file-bg:                    $input-bg !default;\n$custom-file-border-width:          $input-btn-border-width !default;\n$custom-file-border-color:          $input-border-color !default;\n$custom-file-border-radius:         $input-border-radius !default;\n$custom-file-box-shadow:            $custom-select-focus-box-shadow !default;\n$custom-file-button-color:          $custom-file-color !default;\n$custom-file-button-bg:             $input-group-addon-bg !default;\n$custom-file-text: (\n        en: \"Browse\"\n) !default;\n\n$custom-range-thumb-focus-box-shadow:        0 0 0 1px $body-bg, $input-btn-focus-box-shadow !default;\n\n\n// Form validation\n$form-feedback-margin-top:          $form-text-margin-top !default;\n$form-feedback-font-size:           $small-font-size !default;\n$form-feedback-valid-color:         theme-color(\"success\") !default;\n$form-feedback-invalid-color:       theme-color(\"danger\") !default;\n\n\n// Dropdowns\n//\n// Dropdown menu container and contents.\n\n$dropdown-min-width:                10rem !default;\n$dropdown-padding-y:                .5rem !default;\n$dropdown-spacer:                   .125rem !default;\n$dropdown-bg:                       $white !default;\n$dropdown-border-color:             rgba($black, .15) !default;\n$dropdown-border-radius:            $border-radius !default;\n$dropdown-border-width:             $border-width !default;\n$dropdown-divider-bg:               $gray-200 !default;\n$dropdown-box-shadow:               0 .5rem 1rem rgba($black, .175) !default;\n\n$dropdown-link-color:               $gray-900 !default;\n$dropdown-link-hover-color:         darken($gray-900, 5%) !default;\n$dropdown-link-hover-bg:            $gray-100 !default;\n\n$dropdown-link-active-color:        $component-active-color !default;\n$dropdown-link-active-bg:           $component-active-bg !default;\n\n$dropdown-link-disabled-color:      $gray-600 !default;\n\n$dropdown-item-padding-y:           .25rem !default;\n$dropdown-item-padding-x:           1rem !default;\n\n$dropdown-header-color:             $gray-600 !default;\n\n\n// Z-index master list\n//\n// Warning: Avoid customizing these values. They're used for a bird's eye view\n// of components dependent on the z-axis and are designed to all work together.\n\n$zindex-dropdown:                   1000 !default;\n$zindex-sticky:                     1020 !default;\n$zindex-fixed:                      1030 !default;\n$zindex-modal-backdrop:             1040 !default;\n$zindex-modal:                      1050 !default;\n$zindex-popover:                    1060 !default;\n$zindex-tooltip:                    1070 !default;\n\n// Navs\n\n$nav-link-padding-y:                .5rem !default;\n$nav-link-padding-x:                1rem !default;\n$nav-link-disabled-color:           $gray-600 !default;\n\n$nav-tabs-border-color:             $gray-300 !default;\n$nav-tabs-border-width:             $border-width !default;\n$nav-tabs-border-radius:            $border-radius !default;\n$nav-tabs-link-hover-border-color:  $gray-200 $gray-200 $nav-tabs-border-color !default;\n$nav-tabs-link-active-color:        $gray-700 !default;\n$nav-tabs-link-active-bg:           $body-bg !default;\n$nav-tabs-link-active-border-color: $gray-300 $gray-300 $nav-tabs-link-active-bg !default;\n\n$nav-pills-border-radius:           $border-radius !default;\n$nav-pills-link-active-color:       $component-active-color !default;\n$nav-pills-link-active-bg:          $component-active-bg !default;\n\n// Navbar\n\n$navbar-padding-y:                  ($spacer / 2) !default;\n$navbar-padding-x:                  ($spacer / 2) !default;\n\n$navbar-nav-link-padding-x:         1rem !default;\n\n$navbar-brand-font-size:            $font-size-lg !default;\n// Compute the navbar-brand padding-y so the navbar-brand will have the same height as navbar-text and nav-link\n$nav-link-height:                   ($font-size-base * $line-height-base + $nav-link-padding-y * 2) !default;\n$navbar-brand-height:               $navbar-brand-font-size * $line-height-base !default;\n$navbar-brand-padding-y:            ($nav-link-height - $navbar-brand-height) / 2 !default;\n\n$navbar-toggler-padding-y:          .25rem !default;\n$navbar-toggler-padding-x:          .75rem !default;\n$navbar-toggler-font-size:          $font-size-lg !default;\n$navbar-toggler-border-radius:      $btn-border-radius !default;\n\n$navbar-dark-color:                 rgba($white, .75) !default;\n$navbar-dark-hover-color:           rgba($white, 1) !default;\n$navbar-dark-active-color:          $white !default;\n$navbar-dark-disabled-color:        rgba($white, .25) !default;\n$navbar-dark-toggler-icon-bg:       str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath stroke='#{$navbar-dark-color}' stroke-width='2' stroke-linecap='round' stroke-miterlimit='10' d='M4 7h22M4 15h22M4 23h22'/%3E%3C/svg%3E\"), \"#\", \"%23\") !default;\n$navbar-dark-toggler-border-color:  rgba($white, .1) !default;\n\n$navbar-light-color:                rgba($black, .5) !default;\n$navbar-light-hover-color:          rgba($black, .7) !default;\n$navbar-light-active-color:         rgba($black, .9) !default;\n$navbar-light-disabled-color:       rgba($black, .3) !default;\n$navbar-light-toggler-icon-bg:      str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath stroke='#{$navbar-light-color}' stroke-width='2' stroke-linecap='round' stroke-miterlimit='10' d='M4 7h22M4 15h22M4 23h22'/%3E%3C/svg%3E\"), \"#\", \"%23\") !default;\n$navbar-light-toggler-border-color: rgba($black, .1) !default;\n\n// Pagination\n\n$pagination-padding-y:              .5rem !default;\n$pagination-padding-x:              .75rem !default;\n$pagination-padding-y-sm:           .25rem !default;\n$pagination-padding-x-sm:           .5rem !default;\n$pagination-padding-y-lg:           .75rem !default;\n$pagination-padding-x-lg:           1.5rem !default;\n$pagination-line-height:            1.25 !default;\n\n$pagination-color:                  $link-color !default;\n$pagination-bg:                     $white !default;\n$pagination-border-width:           $border-width !default;\n$pagination-border-color:           $gray-300 !default;\n\n$pagination-focus-box-shadow:       $input-btn-focus-box-shadow !default;\n\n$pagination-hover-color:            $link-hover-color !default;\n$pagination-hover-bg:               $gray-200 !default;\n$pagination-hover-border-color:     $gray-300 !default;\n\n$pagination-active-color:           $component-active-color !default;\n$pagination-active-bg:              $component-active-bg !default;\n$pagination-active-border-color:    $pagination-active-bg !default;\n\n$pagination-disabled-color:         $gray-600 !default;\n$pagination-disabled-bg:            $white !default;\n$pagination-disabled-border-color:  $gray-300 !default;\n\n\n// Jumbotron\n\n$jumbotron-padding:                 2rem !default;\n$jumbotron-bg:                      $gray-200 !default;\n\n\n// Cards\n\n$card-spacer-y:                     .75rem !default;\n$card-spacer-x:                     1.25rem !default;\n$card-border-width:                 0 !default; //$border-width !default;\n$card-border-radius:                $border-radius !default;\n$card-border-color:                 rgba($black, .125) !default;\n$card-inner-border-radius:          calc(#{$card-border-radius} - #{$card-border-width}) !default;\n$card-cap-bg:                       rgba($black, .03) !default;\n$card-bg:                           $white !default;\n\n$card-img-overlay-padding:          1.25rem !default;\n\n$card-group-margin:                 ($grid-gutter-width / 2) !default;\n$card-deck-margin:                  $card-group-margin !default;\n\n$card-columns-count:                3 !default;\n$card-columns-gap:                  1.25rem !default;\n$card-columns-margin:               $card-spacer-y !default;\n\n\n// Tooltips\n\n$tooltip-font-size:           $font-size-sm !default;\n$tooltip-max-width:           200px !default;\n$tooltip-color:               $white !default;\n$tooltip-bg:                  $black !default;\n$tooltip-border-radius:        $border-radius !default;\n$tooltip-opacity:             .9 !default;\n$tooltip-padding-y:           .25rem !default;\n$tooltip-padding-x:           .5rem !default;\n$tooltip-margin:              0 !default;\n\n$tooltip-arrow-width:         .8rem !default;\n$tooltip-arrow-height:        .4rem !default;\n$tooltip-arrow-color:         $tooltip-bg !default;\n\n// Form tooltips must come after regular tooltips\n$form-feedback-tooltip-padding-y:     $tooltip-padding-y !default;\n$form-feedback-tooltip-padding-x:     $tooltip-padding-x !default;\n$form-feedback-tooltip-font-size:     $tooltip-font-size !default;\n$form-feedback-tooltip-line-height:   $line-height-base !default;\n$form-feedback-tooltip-opacity:       $tooltip-opacity !default;\n$form-feedback-tooltip-border-radius: $tooltip-border-radius !default;\n\n// Popovers\n\n$popover-font-size:                 $font-size-sm !default;\n$popover-bg:                        $white !default;\n$popover-max-width:                 276px !default;\n$popover-border-width:              $border-width !default;\n$popover-border-color:              rgba($black, .2) !default;\n$popover-border-radius:             $border-radius-lg !default;\n$popover-box-shadow:                0 .25rem .5rem rgba($black, .2) !default;\n\n$popover-header-bg:                 darken($popover-bg, 3%) !default;\n$popover-header-color:              $headings-color !default;\n$popover-header-padding-y:          .5rem !default;\n$popover-header-padding-x:          .75rem !default;\n\n$popover-body-color:                $body-color !default;\n$popover-body-padding-y:            $popover-header-padding-y !default;\n$popover-body-padding-x:            $popover-header-padding-x !default;\n\n$popover-arrow-width:               1rem !default;\n$popover-arrow-height:              .5rem !default;\n$popover-arrow-color:               $popover-bg !default;\n\n$popover-arrow-outer-color:         fade-in($popover-border-color, .05) !default;\n\n\n// Badges\n\n$badge-font-size:                   75% !default;\n$badge-font-weight:                 $font-weight-bold !default;\n$badge-padding-y:                   .25em !default;\n$badge-padding-x:                   .4em !default;\n$badge-border-radius:               $border-radius !default;\n\n$badge-pill-padding-x:              .6em !default;\n// Use a higher than normal value to ensure completely rounded edges when\n// customizing padding or font-size on labels.\n$badge-pill-border-radius:          10rem !default;\n\n\n// Modals\n\n// Padding applied to the modal body\n$modal-inner-padding:         1rem !default;\n\n$modal-dialog-margin:         .5rem !default;\n$modal-dialog-margin-y-sm-up: 1.75rem !default;\n\n$modal-title-line-height:           $line-height-base !default;\n\n$modal-content-bg:               $white !default;\n$modal-content-border-color:     rgba($black, .2) !default;\n$modal-content-border-width:     $border-width !default;\n$modal-content-box-shadow-xs:    0 .25rem .5rem rgba($black, .5) !default;\n$modal-content-box-shadow-sm-up: 0 .5rem 1rem rgba($black, .5) !default;\n\n$modal-backdrop-bg:           $black !default;\n$modal-backdrop-opacity:      .5 !default;\n$modal-header-border-color:   $gray-200 !default;\n$modal-footer-border-color:   $modal-header-border-color !default;\n$modal-header-border-width:   $modal-content-border-width !default;\n$modal-footer-border-width:   $modal-header-border-width !default;\n$modal-header-padding:        1rem !default;\n\n$modal-lg:                          800px !default;\n$modal-md:                          500px !default;\n$modal-sm:                          300px !default;\n\n$modal-transition:                  transform .3s ease-out !default;\n\n\n// Alerts\n//\n// Define alert colors, border radius, and padding.\n\n$alert-padding-y:                   .75rem !default;\n$alert-padding-x:                   1.25rem !default;\n$alert-margin-bottom:               1rem !default;\n$alert-border-radius:               $border-radius !default;\n$alert-link-font-weight:            $font-weight-bold !default;\n$alert-border-width:                $border-width !default;\n\n$alert-bg-level:                    -10 !default;\n$alert-border-level:                -9 !default;\n$alert-color-level:                 6 !default;\n\n\n// Progress bars\n\n$progress-height:                   1rem !default;\n$progress-font-size:                ($font-size-base * .75) !default;\n$progress-bg:                       $gray-200 !default;\n$progress-border-radius:            $border-radius !default;\n$progress-box-shadow:               inset 0 .1rem .1rem rgba($black, .1) !default;\n$progress-bar-color:                $white !default;\n$progress-bar-bg:                   theme-color(\"primary\") !default;\n$progress-bar-animation-timing:     1s linear infinite !default;\n$progress-bar-transition:           width .6s ease !default;\n\n// List group\n\n$list-group-bg:                     $white !default;\n$list-group-border-color:           rgba($black, .125) !default;\n$list-group-border-width:           $border-width !default;\n$list-group-border-radius:          $border-radius !default;\n\n$list-group-item-padding-y:         .75rem !default;\n$list-group-item-padding-x:         1.25rem !default;\n\n$list-group-hover-bg:               $gray-100 !default;\n$list-group-active-color:           $component-active-color !default;\n$list-group-active-bg:              $component-active-bg !default;\n$list-group-active-border-color:    $list-group-active-bg !default;\n\n$list-group-disabled-color:         $gray-600 !default;\n$list-group-disabled-bg:            $list-group-bg !default;\n\n$list-group-action-color:           $gray-700 !default;\n$list-group-action-hover-color:     $list-group-action-color !default;\n\n$list-group-action-active-color:    $body-color !default;\n$list-group-action-active-bg:       $gray-200 !default;\n\n\n// Image thumbnails\n\n$thumbnail-padding:                 .25rem !default;\n$thumbnail-bg:                      $body-bg !default;\n$thumbnail-border-width:            $border-width !default;\n$thumbnail-border-color:            $gray-300 !default;\n$thumbnail-border-radius:           $border-radius !default;\n$thumbnail-box-shadow:              0 1px 2px rgba($black, .075) !default;\n\n\n// Figures\n\n$figure-caption-font-size:          90% !default;\n$figure-caption-color:              $gray-600 !default;\n\n\n// Breadcrumbs\n\n$breadcrumb-padding-y:              .75rem !default;\n$breadcrumb-padding-x:              1rem !default;\n$breadcrumb-item-padding:           .5rem !default;\n\n$breadcrumb-margin-bottom:          1rem !default;\n\n$breadcrumb-bg:                     $gray-200 !default;\n$breadcrumb-divider-color:          $gray-600 !default;\n$breadcrumb-active-color:           $gray-600 !default;\n$breadcrumb-divider:                \"/\" !default;\n\n\n// Carousel\n\n$carousel-control-color:            $white !default;\n$carousel-control-width:            15% !default;\n$carousel-control-opacity:          .5 !default;\n\n$carousel-indicator-width:          30px !default;\n$carousel-indicator-height:         3px !default;\n$carousel-indicator-spacer:         3px !default;\n$carousel-indicator-active-bg:      $white !default;\n\n$carousel-caption-width:            70% !default;\n$carousel-caption-color:            $white !default;\n\n$carousel-control-icon-width:       20px !default;\n\n$carousel-control-prev-icon-bg:     str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='#{$carousel-control-color}' viewBox='0 0 8 8'%3E%3Cpath d='M5.25 0l-4 4 4 4 1.5-1.5-2.5-2.5 2.5-2.5-1.5-1.5z'/%3E%3C/svg%3E\"), \"#\", \"%23\") !default;\n$carousel-control-next-icon-bg:     str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='#{$carousel-control-color}' viewBox='0 0 8 8'%3E%3Cpath d='M2.75 0l-1.5 1.5 2.5 2.5-2.5 2.5 1.5 1.5 4-4-4-4z'/%3E%3C/svg%3E\"), \"#\", \"%23\") !default;\n\n$carousel-transition:               transform .6s ease !default;\n\n\n// Close\n\n$close-font-size:                   $font-size-base * 1.5 !default;\n$close-font-weight:                 $font-weight-bold !default;\n$close-color:                       $black !default;\n$close-text-shadow:                 0 1px 0 $white !default;\n\n// Code\n\n$code-font-size:                    87.5% !default;\n$code-color:                        $pink !default;\n\n$kbd-padding-y:                     .2rem !default;\n$kbd-padding-x:                     .4rem !default;\n$kbd-font-size:                     $code-font-size !default;\n$kbd-color:                         $white !default;\n$kbd-bg:                            $gray-900 !default;\n\n$pre-color:                         $gray-900 !default;\n$pre-scrollable-max-height:         340px !default;\n\n\n// Printing\n$print-page-size:                   a3 !default;\n$print-body-min-width:              map-get($grid-breakpoints, \"lg\") !default;\n", "// Breakpoint viewport sizes and media queries.\n//\n// Breakpoints are defined as a map of (name: minimum width), order from small to large:\n//\n//    (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px)\n//\n// The map defined in the `$grid-breakpoints` global variable is used as the `$breakpoints` argument by default.\n\n// Name of the next breakpoint, or null for the last breakpoint.\n//\n//    >> breakpoint-next(sm)\n//    md\n//    >> breakpoint-next(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    md\n//    >> breakpoint-next(sm, $breakpoint-names: (xs sm md lg xl))\n//    md\n@function breakpoint-next($name, $breakpoints: $grid-breakpoints, $breakpoint-names: map-keys($breakpoints)) {\n  $n: index($breakpoint-names, $name);\n  @return if($n != null and $n < length($breakpoint-names), nth($breakpoint-names, $n + 1), null);\n}\n\n// Minimum breakpoint width. Null for the smallest (first) breakpoint.\n//\n//    >> breakpoint-min(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    576px\n@function breakpoint-min($name, $breakpoints: $grid-breakpoints) {\n  $min: map-get($breakpoints, $name);\n  @return if($min != 0, $min, null);\n}\n\n// Maximum breakpoint width. Null for the largest (last) breakpoint.\n// The maximum value is calculated as the minimum of the next one less 0.02px\n// to work around the limitations of `min-` and `max-` prefixes and viewports with fractional widths.\n// See https://www.w3.org/TR/mediaqueries-4/#mq-min-max\n// Uses 0.02px rather than 0.01px to work around a current rounding bug in Safari.\n// See https://bugs.webkit.org/show_bug.cgi?id=178261\n//\n//    >> breakpoint-max(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    767.98px\n@function breakpoint-max($name, $breakpoints: $grid-breakpoints) {\n  $next: breakpoint-next($name, $breakpoints);\n  @return if($next, breakpoint-min($next, $breakpoints) - .02, null);\n}\n\n// Returns a blank string if smallest breakpoint, otherwise returns the name with a dash in front.\n// Useful for making responsive utilities.\n//\n//    >> breakpoint-infix(xs, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"\"  (Returns a blank string)\n//    >> breakpoint-infix(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"-sm\"\n@function breakpoint-infix($name, $breakpoints: $grid-breakpoints) {\n  @return if(breakpoint-min($name, $breakpoints) == null, \"\", \"-#{$name}\");\n}\n\n// Media of at least the minimum breakpoint width. No query for the smallest breakpoint.\n// Makes the @content apply to the given breakpoint and wider.\n@mixin media-breakpoint-up($name, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($name, $breakpoints);\n  @if $min {\n    @media (min-width: $min) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media of at most the maximum breakpoint width. No query for the largest breakpoint.\n// Makes the @content apply to the given breakpoint and narrower.\n@mixin media-breakpoint-down($name, $breakpoints: $grid-breakpoints) {\n  $max: breakpoint-max($name, $breakpoints);\n  @if $max {\n    @media (max-width: $max) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media that spans multiple breakpoint widths.\n// Makes the @content apply between the min and max breakpoints\n@mixin media-breakpoint-between($lower, $upper, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($lower, $breakpoints);\n  $max: breakpoint-max($upper, $breakpoints);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($lower, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($upper, $breakpoints) {\n      @content;\n    }\n  }\n}\n\n// Media between the breakpoint's minimum and maximum widths.\n// No minimum for the smallest breakpoint, and no maximum for the largest one.\n// Makes the @content apply only to the given breakpoint, not viewports any wider or narrower.\n@mixin media-breakpoint-only($name, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($name, $breakpoints);\n  $max: breakpoint-max($name, $breakpoints);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($name, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($name, $breakpoints) {\n      @content;\n    }\n  }\n}\n", "//\n// Mixins: Miscellaneous\n//\n\n// ETC\n@mixin translate($x, $y) {\n  transform: translate($x, $y);\n}\n\n// Different radius each side\n@mixin border-radius-sides($top-left, $top-right, $bottom-left, $bottom-right) {\n  border-radius: $top-left $top-right $bottom-left $bottom-right;\n}\n\n@mixin calc($property, $expression) {\n  #{$property}: calc(#{$expression});\n}\n\n@mixin rotate($value) {\n  transform: rotate($value);\n}\n\n@mixin animation($animation) {\n  animation: $animation;\n}\n\n// Gradient background\n@mixin gradient($color: #F5F5F5, $start: #EEE, $stop: #FFF) {\n  background: $color;\n  background: -webkit-gradient(linear, left bottom, left top, color-stop(0, $start), color-stop(1, $stop));\n  background: -ms-linear-gradient(bottom, $start, $stop);\n  background: -moz-linear-gradient(center bottom, $start 0%, $stop 100%);\n  background: -o-linear-gradient($stop, $start);\n}\n\n", "@mixin box-shadow($shadow...) {\n  @if $enable-shadows {\n    $result: ();\n\n    @if (length($shadow) == 1) {\n      // We can pass `@include box-shadow(none);`\n      $result: $shadow;\n    } @else {\n      // Filter to avoid invalid properties for example `box-shadow: none, 1px 1px black;`\n      @for $i from 1 through length($shadow) {\n        @if nth($shadow, $i) != \"none\" {\n          $result: append($result, nth($shadow, $i), \"comma\");\n        }\n      }\n    }\n    @if (length($result) > 0) {\n      box-shadow: $result;\n    }\n  }\n}\n", "//\n// Core: Variables\n//\n\n// COLORS\n// --------------------------------------------------------\n$blue: #0073b7 !default;\n$lightblue: #3c8dbc !default;\n$navy: #001f3f !default;\n$teal: #39cccc !default;\n$olive: #3d9970 !default;\n$lime: #01ff70 !default;\n$orange: #ff851b !default;\n$fuchsia: #f012be !default;\n$purple: #605ca8 !default;\n$maroon: #d81b60 !default;\n$black: #111 !default;\n$gray-x-light: #d2d6de !default;\n\n$colors: map-merge((\n    'lightblue': $lightblue,\n    'navy': $navy,\n    'olive': $olive,\n    'lime': $lime,\n    'fuchsia': $fuchsia,\n    'maroon': $maroon,\n), $colors);\n\n// LAYOUT\n// --------------------------------------------------------\n\n$font-size-root: 1rem !default;\n\n// Sidebar\n$sidebar-width: 250px !default;\n$sidebar-padding-x: 0.5rem !default;\n$sidebar-padding-y: 0 !default;\n\n// Boxed layout maximum width\n$boxed-layout-max-width: 1250px !default;\n\n// When to show the smaller logo\n$screen-header-collapse: map-get($grid-breakpoints, md) !default;\n\n// Body background (Affects main content background only)\n$main-bg: #f4f6f9 !default;\n\n// Content padding\n$content-padding-y: 0 !default;\n$content-padding-x: $navbar-padding-x !default;\n\n// IMAGE SIZES\n// --------------------------------------------------------\n$img-size-sm: 1.875rem !default;\n$img-size-md: 3.75rem !default;\n$img-size-lg: 6.25rem !default;\n$img-size-push: .625rem !default;\n\n// MAIN HEADER\n// --------------------------------------------------------\n$main-header-bottom-border-width: $border-width !default;\n$main-header-bottom-border-color: $gray-300 !default;\n$main-header-bottom-border: $main-header-bottom-border-width solid $main-header-bottom-border-color !default;\n$main-header-link-padding-y: $navbar-padding-y !default;\n$main-header-link-padding-x: $navbar-padding-x !default;\n$main-header-brand-padding-y: $navbar-brand-padding-y !default;\n$main-header-brand-padding-x: $navbar-padding-x !default;\n$main-header-height-inner: ($nav-link-height + ($main-header-link-padding-y * 2)) !default;\n$main-header-height: calc(#{$main-header-height-inner} + #{$main-header-bottom-border-width}) !default;\n$nav-link-sm-padding-y: .35rem !default;\n$nav-link-sm-height: ($font-size-sm * $line-height-sm + $nav-link-sm-padding-y * 1.785) !default;\n$main-header-height-sm-inner: ($nav-link-sm-height + ($main-header-link-padding-y * 2)) !default;\n$main-header-height-sm: calc(#{$main-header-height-sm-inner} + #{$main-header-bottom-border-width}) !default;\n\n\n// Main header skins\n$main-header-dark-form-control-bg: hsla(100, 100%, 100%, 0.2) !default;\n$main-header-dark-form-control-focused-bg: hsla(100, 100%, 100%, 0.6) !default;\n$main-header-dark-form-control-focused-color: $gray-800 !default;\n$main-header-dark-form-control-border: 0 !default;\n$main-header-dark-form-control-focused-border: 0 !default;\n$main-header-dark-placeholder-color: hsla(100, 100%, 100%, 0.6) !default;\n\n$main-header-light-form-control-bg: darken($gray-100, 2%) !default;\n$main-header-light-form-control-focused-bg: $gray-200 !default;\n$main-header-light-form-control-focused-color: $gray-800 !default;\n$main-header-light-form-control-border: 0 !default;\n$main-header-light-form-control-focused-border: 0 !default;\n$main-header-light-placeholder-color: hsla(0, 0%, 0%, 0.6) !default;\n\n// MAIN FOOTER\n// --------------------------------------------------------\n$main-footer-padding: 1rem !default;\n$main-footer-padding-sm: $main-footer-padding * .812 !default;\n$main-footer-border-top-width: 1px !default;\n$main-footer-border-top-color: $gray-300 !default;\n$main-footer-border-top: $main-footer-border-top-width solid $main-footer-border-top-color !default;\n$main-footer-height-inner: (($font-size-root * $line-height-base) + ($main-footer-padding * 2)) !default;\n$main-footer-height: calc(#{$main-footer-height-inner} + #{$main-footer-border-top-width}) !default;\n$main-footer-height-sm-inner: (($font-size-sm * $line-height-base) + ($main-footer-padding-sm * 2)) !default;\n$main-footer-height-sm: calc(#{$main-footer-height-sm-inner} + #{$main-footer-border-top-width}) !default;\n$main-footer-bg: $white !default;\n\n// SIDEBAR SKINS\n// --------------------------------------------------------\n\n// Dark sidebar\n$sidebar-dark-bg: $dark !default;\n$sidebar-dark-hover-bg: hsla(100, 100%, 100%, 0.1) !default;\n$sidebar-dark-color: #C2C7D0 !default;\n$sidebar-dark-hover-color: $white !default;\n$sidebar-dark-active-color: $white !default;\n$sidebar-dark-submenu-bg: transparent !default;\n$sidebar-dark-submenu-color: #C2C7D0 !default;\n$sidebar-dark-submenu-hover-color: $white !default;\n$sidebar-dark-submenu-hover-bg: $sidebar-dark-hover-bg !default;\n$sidebar-dark-submenu-active-color: $sidebar-dark-bg !default;\n$sidebar-dark-submenu-active-bg: hsla(100, 100%, 100%, 0.9) !default;\n$sidebar-dark-header-color: $white !default;\n\n// Light sidebar\n$sidebar-light-bg: $white !default;\n$sidebar-light-hover-bg: rgba($black, .1) !default;\n$sidebar-light-color: $gray-800 !default;\n$sidebar-light-hover-color: $gray-900 !default;\n$sidebar-light-active-color: $black !default;\n$sidebar-light-submenu-bg: transparent !default;\n$sidebar-light-submenu-color: #777 !default;\n$sidebar-light-submenu-hover-color: #000 !default;\n$sidebar-light-submenu-hover-bg: $sidebar-light-hover-bg !default;\n$sidebar-light-submenu-active-color: $sidebar-light-hover-color !default;\n$sidebar-light-submenu-active-bg: $sidebar-light-submenu-hover-bg !default;\n$sidebar-light-header-color: $gray-800 !default;\n\n// SIDEBAR MINI\n// --------------------------------------------------------\n$sidebar-mini-width: ($nav-link-padding-x + $sidebar-padding-x + .8rem) * 2 !default;\n$sidebar-nav-icon-width: $sidebar-mini-width - (($sidebar-padding-x + $nav-link-padding-x) * 2) !default;\n$sidebar-user-image-width: $sidebar-nav-icon-width + ($nav-link-padding-x / 2) !default;\n\n// CONTROL SIDEBAR\n// --------------------------------------------------------\n$control-sidebar-width: $sidebar-width !default;\n\n// Cards\n// --------------------------------------------------------\n$card-border-color: $gray-100 !default;\n$card-dark-border-color: lighten($gray-900, 10%) !default;\n$card-shadow: 0 0 1px rgba(0, 0, 0, .125), 0 1px 3px rgba(0, 0, 0, .2) !default;\n$card-title-font-size: 1.1rem !default;\n$card-title-font-size-sm: 1rem !default;\n$card-title-font-weight: $font-weight-normal !default;\n$card-nav-link-padding-sm-y: .4rem !default;\n$card-nav-link-padding-sm-x: .8rem !default;\n$card-img-size: $img-size-sm !default;\n\n// PROGRESS BARS\n// --------------------------------------------------------\n$progress-bar-border-radius: 1px !default;\n$progress-bar-sm-border-radius: 1px !default;\n$progress-bar-xs-border-radius: 1px !default;\n\n// DIRECT CHAT\n// --------------------------------------------------------\n$direct-chat-height: 250px !default;\n$direct-chat-default-msg-bg: $gray-x-light !default;\n$direct-chat-default-font-color: #444 !default;\n$direct-chat-default-msg-border-color: $gray-x-light !default;\n\n// CHAT WIDGET\n// --------------------------------------------------------\n$attachment-border-radius: 3px !default;\n\n// Z-INDEX\n// --------------------------------------------------------\n$zindex-main-header: $zindex-fixed + 4 !default;\n$zindex-main-sidebar: $zindex-fixed + 8 !default;\n$zindex-main-footer: $zindex-fixed + 2 !default;\n$zindex-control-sidebar: $zindex-fixed + 1 !default;\n$zindex-sidebar-mini-links: 010 !default;\n$zindex-toasts: $zindex-main-sidebar + 2 !default;\n\n// TRANSITIONS SETTINGS\n// --------------------------------------------------------\n\n// Transition global options\n$transition-speed: 0.3s !default;\n$transition-fn: ease-in-out !default;\n\n// TEXT\n// --------------------------------------------------------\n$font-size-xs: ($font-size-base * .75) !default;\n$font-size-xl: ($font-size-base * 2) !default;\n\n\n// BUTTON\n// --------------------------------------------------------\n$button-default-background-color: $gray-100 !default;\n$button-default-color: #444 !default;\n$button-default-border-color: #ddd !default;\n\n$button-padding-y-xs: .125rem !default;\n$button-padding-x-xs: .25rem !default;\n$button-line-height-xs: $line-height-sm !default;\n$button-font-size-xs: ($font-size-base * .75) !default;\n$button-border-radius-xs: .15rem !default;\n\n  \n// ELEVATION\n// --------------------------------------------------------\n$elevations: ();\n$elevations: map-merge((\n    1: unquote('0 1px 3px ' + rgba($black, 0.12) + ', 0 1px 2px ' + rgba($black, 0.24)),\n    2: unquote('0 3px 6px ' + rgba($black, 0.16) + ', 0 3px 6px ' + rgba($black, 0.23)),\n    3: unquote('0 10px 20px ' + rgba($black, 0.19) + ', 0 6px 6px ' + rgba($black, 0.23)),\n    4: unquote('0 14px 28px ' + rgba($black, 0.25) + ', 0 10px 10px ' + rgba($black, 0.22)),\n    5: unquote('0 19px 38px ' + rgba($black, 0.30) + ', 0 15px 12px ' + rgba($black, 0.22)),\n), $elevations);\n  \n// RIBBON\n// --------------------------------------------------------\n$ribbon-border-size: 3px !default;\n$ribbon-line-height: 100% !default;\n$ribbon-padding: .375rem 0 !default;\n$ribbon-font-size: .8rem !default;\n$ribbon-width: 90px !default;\n$ribbon-wrapper-size: 70px !default;\n$ribbon-top: 10px !default;\n$ribbon-right: -2px !default;\n$ribbon-lg-wrapper-size: 120px !default;\n$ribbon-lg-width: 160px !default;\n$ribbon-lg-top: 26px !default;\n$ribbon-lg-right: 0px !default;\n$ribbon-xl-wrapper-size: 180px !default;\n$ribbon-xl-width: 240px !default;\n$ribbon-xl-top: 47px !default;\n$ribbon-xl-right: 4px !default;\n", "//\n// Plugin: Select2\n//\n\n//Signle select\n// .select2-container--default,\n// .select2-selection {\n//   &.select2-container--focus,\n//   &:focus,\n//   &:active {\n//     outline: none;\n//   }\n// }\n\n.select2-container--default {\n  .select2-selection--single {\n    border: $input-border-width solid $input-border-color;\n    //border-radius: $input-radius;\n    padding: ($input-padding-y * 1.25) $input-padding-x;\n    height: $input-height;\n  }\n\n  &.select2-container--open {\n    .select2-selection--single {\n      border-color: lighten($primary, 25%);\n    }\n  }\n\n  & .select2-dropdown {\n    border: $input-border-width solid $input-border-color;\n    //border-radius: $input-radius;\n  }\n\n  & .select2-results__option {\n    padding: 6px 12px;\n    user-select: none;\n    -webkit-user-select: none;\n  }\n\n  & .select2-selection--single .select2-selection__rendered {\n    padding-left: 0;\n    //padding-right: 0;\n    height: auto;\n    margin-top: -3px;\n  }\n\n  &[dir=\"rtl\"] .select2-selection--single .select2-selection__rendered {\n    padding-right: 6px;\n    padding-left: 20px;\n  }\n\n  & .select2-selection--single .select2-selection__arrow {\n    height: 31px;\n    right: 6px;\n  }\n\n  & .select2-selection--single .select2-selection__arrow b {\n    margin-top: 0;\n  }\n\n  .select2-dropdown,\n  .select2-search--inline {\n    .select2-search__field {\n      border: $input-border-width solid $input-border-color;\n\n      &:focus {\n        outline: none;\n        border: $input-border-width solid $input-focus-border-color;\n      }\n    }\n  }\n\n  .select2-dropdown {\n    &.select2-dropdown--below {\n      border-top: 0;\n    }\n\n    &.select2-dropdown--above {\n      border-bottom: 0;\n    }\n  }\n\n  .select2-results__option {\n    &[aria-disabled='true'] {\n      color: $gray-600;\n    }\n\n    &[aria-selected='true'] {\n      $color: $gray-300;\n\n      background-color: $color;\n\n      &,\n      &:hover {\n        color: color-yiq($color);\n      }\n    }\n  }\n\n  .select2-results__option--highlighted {\n    $color: $primary;\n    background-color: $color;\n    color: color-yiq($color);\n\n    &[aria-selected] {\n      $color: darken($color, 3%);\n\n      &,\n      &:hover {\n        background-color: $color;\n        color: color-yiq($color);\n      }\n    }\n  }\n\n  //Multiple select\n  & {\n    .select2-selection--multiple {\n      border: $input-border-width solid $input-border-color;\n      min-height: $input-height;\n\n      &:focus {\n        border-color: $input-focus-border-color;\n      }\n\n      .select2-selection__rendered {\n        padding: 0 $input-padding-x / 2 $input-padding-y;\n        margin-bottom: -$input-padding-x / 2;\n\n        li:first-child.select2-search.select2-search--inline {\n          width: 100%;\n          margin-left: $input-padding-x / 2;\n\n          .select2-search__field {\n            width: 100% !important;\n          }\n        }\n\n\n        .select2-search.select2-search--inline {\n          .select2-search__field {\n            border: 0;\n            margin-top: 6px;\n          }\n        }\n      }\n\n      .select2-selection__choice {\n        background-color: $primary;\n        border-color: darken($primary, 5%);\n        color: color-yiq($primary);\n        padding: 0 10px;\n        margin-top: .31rem;\n      }\n\n      .select2-selection__choice__remove {\n        color: rgba(255, 255, 255, 0.7);\n        float: right;\n        margin-left: 5px;\n        margin-right: -2px;\n\n        &:hover {\n          color: $white;\n        }\n      }\n\n      .text-sm &,\n      &.text-sm {\n        .select2-search.select2-search--inline {\n          .select2-search__field {\n            margin-top: 8px;\n          }\n        }\n\n        .select2-selection__choice {\n          margin-top: .4rem;\n        }\n      }\n    }\n\n    &.select2-container--focus {\n      .select2-selection--single,\n      .select2-selection--multiple {\n        border-color: $input-focus-border-color;\n      }\n\n      .select2-search__field {\n        border: 0;\n      }\n    }\n  }\n\n  & .select2-selection--single .select2-selection__rendered li {\n    padding-right: 10px;\n  }\n\n  .input-group-prepend ~ & {\n    .select2-selection {\n      border-bottom-left-radius: 0;\n      border-top-left-radius: 0;\n    }\n  }\n\n  .input-group > &:not(:last-child) {\n    .select2-selection {\n      border-bottom-right-radius: 0;\n      border-top-right-radius: 0;\n    }\n  }\n}\n\n// Select2 Bootstrap4 Theme overrides\n.select2-container--bootstrap4 {\n  &.select2-container--focus .select2-selection {\n    box-shadow: none;\n  }\n}\n\n// text-sm / form-control-sm override\nselect.form-control-sm ~ {\n  .select2-container--default {\n    font-size: $font-size-sm;\n  }\n}\n\n.text-sm,\nselect.form-control-sm ~ {\n  .select2-container--default {\n    .select2-selection--single {\n      height: $input-height-sm;\n      \n      .select2-selection__rendered {\n        margin-top: -.4rem;\n      }\n\n      .select2-selection__arrow {\n        top: -.12rem;\n      }\n    }\n\n    .select2-selection--multiple {\n      min-height: $input-height-sm;\n      \n      .select2-selection__rendered {\n        padding: 0 $input-padding-x-sm / 2 $input-padding-y-sm;\n        margin-top: -($input-padding-x-sm / 5);\n\n        li:first-child.select2-search.select2-search--inline {\n          margin-left: $input-padding-x-sm / 2;\n        }\n\n        .select2-search.select2-search--inline {\n          .select2-search__field {\n            margin-top: 6px;\n          }\n        }\n      }\n    }\n  }\n}\n\n// Dropdown Fix inside maximized card\n.maximized-card .select2-dropdown {\n  z-index: 9999;\n}\n\n// Background colors (theme colors)\n@each $name, $color in $theme-colors {\n  @include select2-variant($name, $color);\n}\n\n// Background colors (colors)\n@each $name, $color in $colors {\n  @include select2-variant($name, $color);\n}\n", "//\n// General: Mixins\n//\n\n// Select2 Variant\n@mixin select2-variant($name, $color) {\n  .select2-#{$name} {\n\n    + .select2-container--default {\n      &.select2-container--open {\n        .select2-selection--single {\n          border-color: lighten($color, 25%);\n        }\n      }\n\n      &.select2-container--focus .select2-selection--single {\n        border-color: lighten($color, 25%);\n      }\n    }\n\n    .select2-container--default &,\n    .select2-container--default {\n      &.select2-dropdown,\n      .select2-dropdown,\n      .select2-search--inline {\n        .select2-search__field {\n          &:focus {\n            border: $input-border-width solid lighten($color, 25%);\n          }\n        }\n      }\n\n      .select2-results__option--highlighted {\n        background-color: $color;\n        color: color-yiq($color);\n\n        &[aria-selected] {\n          &,\n          &:hover {\n            background-color: darken($color, 3%);\n            color: color-yiq(darken($color, 3%));\n          }\n        }\n      }\n\n      //Multiple select\n      & {\n        .select2-selection--multiple {\n          &:focus {\n            border-color: lighten($color, 25%);\n          }\n\n          .select2-selection__choice {\n            background-color: $color;\n            border-color: darken($color, 5%);\n            color: color-yiq($color);\n          }\n\n          .select2-selection__choice__remove {\n            color: rgba(color-yiq($color), 0.7);\n\n            &:hover {\n              color: color-yiq($color);\n            }\n          }\n        }\n  \n        &.select2-container--focus .select2-selection--multiple {\n          border-color: lighten($color, 25%);\n        }\n      }\n    }\n  }\n}\n", "//\n// Plugin: Bootstrap Slider\n//\n\n// Tooltip fix\n.slider .tooltip.in {\n  opacity: $tooltip-opacity;\n}\n\n// Style override\n.slider {\n  &.slider-vertical {\n    height: 100%;\n  }\n  &.slider-horizontal {\n    width: 100%;\n  }\n}\n\n// Colors\n@each $name, $color in $theme-colors {\n  .slider-#{$name} .slider {\n    .slider-selection {\n      background: $color;\n    }\n  }\n}\n\n@each $name, $color in $colors {\n  .slider-#{$name} .slider {\n    .slider-selection {\n      background: $color;\n    }\n  }\n}\n", "//\n// Plugin: iCheck Bootstrap\n//\n\n// iCheck colors (theme colors)\n@each $name, $color in $theme-colors {\n  .icheck-#{$name} > input:first-child:not(:checked):not(:disabled):hover + label::before,\n  .icheck-#{$name} > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n    border-color: #{$color};\n  }\n\n  .icheck-#{$name} > input:first-child:not(:checked):not(:disabled):focus + label::before,\n  .icheck-#{$name} > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n    border-color: #{$color};\n  }\n\n  .icheck-#{$name} > input:first-child:checked + label::before,\n  .icheck-#{$name} > input:first-child:checked + input[type=\"hidden\"] + label::before {\n    background-color: #{$color};\n    border-color: #{$color};\n  }\n}\n\n// iCheck colors (colors)\n@each $name, $color in $colors {\n  .icheck-#{$name} > input:first-child:not(:checked):not(:disabled):hover + label::before,\n  .icheck-#{$name} > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n    border-color: #{$color};\n  }\n\n  .icheck-#{$name} > input:first-child:not(:checked):not(:disabled):focus + label::before,\n  .icheck-#{$name} > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n    border-color: #{$color};\n  }\n\n  .icheck-#{$name} > input:first-child:checked + label::before,\n  .icheck-#{$name} > input:first-child:checked + input[type=\"hidden\"] + label::before {\n    background-color: #{$color};\n    border-color: #{$color};\n  }\n}\n", "//\n// Plugins: jQ<PERSON>y <PERSON>\n//\n\n.mapael {\n  .map {\n    position: relative;\n  }\n\n  .mapTooltip {\n    @include reset-text();\n    @include border-radius($tooltip-border-radius);\n    @include font-size($tooltip-font-size);\n    background-color: $tooltip-bg;\n    color: $tooltip-color;\n    display: block;\n    max-width: $tooltip-max-width;\n    padding: $tooltip-padding-y $tooltip-padding-x;\n    position: absolute;\n    text-align: center;\n    word-wrap: break-word;\n    z-index: $zindex-tooltip;\n  }\n\n  .myLegend {\n    background-color: $gray-100;\n    border: 1px solid $gray-500;\n    padding: 10px;\n    width: 600px;\n  }\n\n  .zoomButton {\n    background-color: $button-default-background-color;\n    border: 1px solid $button-default-border-color;\n    border-radius: $btn-border-radius;\n    color: $button-default-color;\n    cursor: pointer;\n    font-weight: bold;\n    height: 16px;\n    left: 10px;\n    line-height: 14px;\n    padding-left: 1px;\n    position: absolute;\n    text-align: center;\n    top: 0;\n\n    user-select: none;\n    width: 16px;\n\n    &:hover,\n    &:active,\n    &.hover {\n      background-color: darken($button-default-background-color, 5%);\n      color: darken($button-default-color, 10%);\n    }\n  }\n\n  .zoomReset {\n    line-height: 12px;\n    top: 10px;\n  }\n\n  .zoomIn {\n    top: 30px;\n  }\n\n  .zoomOut {\n    top: 50px;\n  }\n}\n", "@mixin reset-text() {\n  font-family: $font-family-base;\n  // We deliberately do NOT reset font-size or word-wrap.\n  font-style: normal;\n  font-weight: $font-weight-normal;\n  line-height: $line-height-base;\n  text-align: left; // Fallback for where `start` is not supported\n  text-align: start;\n  text-decoration: none;\n  text-shadow: none;\n  text-transform: none;\n  letter-spacing: normal;\n  word-break: normal;\n  word-spacing: normal;\n  white-space: normal;\n  line-break: auto;\n}\n", "// stylelint-disable property-blacklist\n// Single side border-radius\n\n@mixin border-radius($radius: $border-radius, $fallback-border-radius: false) {\n  @if $enable-rounded {\n    border-radius: $radius;\n  }\n  @else if $fallback-border-radius != false {\n    border-radius: $fallback-border-radius;\n  }\n}\n\n@mixin border-top-radius($radius) {\n  @if $enable-rounded {\n    border-top-left-radius: $radius;\n    border-top-right-radius: $radius;\n  }\n}\n\n@mixin border-right-radius($radius) {\n  @if $enable-rounded {\n    border-top-right-radius: $radius;\n    border-bottom-right-radius: $radius;\n  }\n}\n\n@mixin border-bottom-radius($radius) {\n  @if $enable-rounded {\n    border-bottom-right-radius: $radius;\n    border-bottom-left-radius: $radius;\n  }\n}\n\n@mixin border-left-radius($radius) {\n  @if $enable-rounded {\n    border-top-left-radius: $radius;\n    border-bottom-left-radius: $radius;\n  }\n}\n\n@mixin border-top-left-radius($radius) {\n  @if $enable-rounded {\n    border-top-left-radius: $radius;\n  }\n}\n\n@mixin border-top-right-radius($radius) {\n  @if $enable-rounded {\n    border-top-right-radius: $radius;\n  }\n}\n\n@mixin border-bottom-right-radius($radius) {\n  @if $enable-rounded {\n    border-bottom-right-radius: $radius;\n  }\n}\n\n@mixin border-bottom-left-radius($radius) {\n  @if $enable-rounded {\n    border-bottom-left-radius: $radius;\n  }\n}\n", "// stylelint-disable property-blacklist, scss/dollar-variable-default\n\n// SCSS RFS mixin\n//\n// Automated font-resizing\n//\n// See https://github.com/twbs/rfs\n\n// Configuration\n\n// Base font size\n$rfs-base-font-size: 1.25rem !default;\n$rfs-font-size-unit: rem !default;\n\n// Breakpoint at where font-size starts decreasing if screen width is smaller\n$rfs-breakpoint: 1200px !default;\n$rfs-breakpoint-unit: px !default;\n\n// Resize font-size based on screen height and width\n$rfs-two-dimensional: false !default;\n\n// Factor of decrease\n$rfs-factor: 10 !default;\n\n@if type-of($rfs-factor) != \"number\" or $rfs-factor <= 1 {\n  @error \"`#{$rfs-factor}` is not a valid  $rfs-factor, it must be greater than 1.\";\n}\n\n// Generate enable or disable classes. Possibilities: false, \"enable\" or \"disable\"\n$rfs-class: false !default;\n\n// 1 rem = $rfs-rem-value px\n$rfs-rem-value: 16 !default;\n\n// Safari iframe resize bug: https://github.com/twbs/rfs/issues/14\n$rfs-safari-iframe-resize-bug-fix: false !default;\n\n// Disable RFS by setting $enable-responsive-font-sizes to false\n$enable-responsive-font-sizes: true !default;\n\n// Cache $rfs-base-font-size unit\n$rfs-base-font-size-unit: unit($rfs-base-font-size);\n\n// Remove px-unit from $rfs-base-font-size for calculations\n@if $rfs-base-font-size-unit == \"px\" {\n  $rfs-base-font-size: $rfs-base-font-size / ($rfs-base-font-size * 0 + 1);\n}\n@else if $rfs-base-font-size-unit == \"rem\" {\n  $rfs-base-font-size: $rfs-base-font-size / ($rfs-base-font-size * 0 + 1 / $rfs-rem-value);\n}\n\n// Cache $rfs-breakpoint unit to prevent multiple calls\n$rfs-breakpoint-unit-cache: unit($rfs-breakpoint);\n\n// Remove unit from $rfs-breakpoint for calculations\n@if $rfs-breakpoint-unit-cache == \"px\" {\n  $rfs-breakpoint: $rfs-breakpoint / ($rfs-breakpoint * 0 + 1);\n}\n@else if $rfs-breakpoint-unit-cache == \"rem\" or $rfs-breakpoint-unit-cache == \"em\" {\n  $rfs-breakpoint: $rfs-breakpoint / ($rfs-breakpoint * 0 + 1 / $rfs-rem-value);\n}\n\n// Responsive font-size mixin\n@mixin rfs($fs, $important: false) {\n  // Cache $fs unit\n  $fs-unit: if(type-of($fs) == \"number\", unit($fs), false);\n\n  // Add !important suffix if needed\n  $rfs-suffix: if($important, \" !important\", \"\");\n\n  // If $fs isn't a number (like inherit) or $fs has a unit (not px or rem, like 1.5em) or $ is 0, just print the value\n  @if not $fs-unit or $fs-unit != \"\" and $fs-unit != \"px\" and $fs-unit != \"rem\" or $fs == 0 {\n    font-size: #{$fs}#{$rfs-suffix};\n  }\n  @else {\n    // Variables for storing static and fluid rescaling\n    $rfs-static: null;\n    $rfs-fluid: null;\n\n    // Remove px-unit from $fs for calculations\n    @if $fs-unit == \"px\" {\n      $fs: $fs / ($fs * 0 + 1);\n    }\n    @else if $fs-unit == \"rem\" {\n      $fs: $fs / ($fs * 0 + 1 / $rfs-rem-value);\n    }\n\n    // Set default font-size\n    @if $rfs-font-size-unit == rem {\n      $rfs-static: #{$fs / $rfs-rem-value}rem#{$rfs-suffix};\n    }\n    @else if $rfs-font-size-unit == px {\n      $rfs-static: #{$fs}px#{$rfs-suffix};\n    }\n    @else {\n      @error \"`#{$rfs-font-size-unit}` is not a valid unit for $rfs-font-size-unit. Use `px` or `rem`.\";\n    }\n\n    // Only add media query if font-size is bigger as the minimum font-size\n    // If $rfs-factor == 1, no rescaling will take place\n    @if $fs > $rfs-base-font-size and $enable-responsive-font-sizes {\n      $min-width: null;\n      $variable-unit: null;\n\n      // Calculate minimum font-size for given font-size\n      $fs-min: $rfs-base-font-size + ($fs - $rfs-base-font-size) / $rfs-factor;\n\n      // Calculate difference between given font-size and minimum font-size for given font-size\n      $fs-diff: $fs - $fs-min;\n\n      // Base font-size formatting\n      // No need to check if the unit is valid, because we did that before\n      $min-width: if($rfs-font-size-unit == rem, #{$fs-min / $rfs-rem-value}rem, #{$fs-min}px);\n\n      // If two-dimensional, use smallest of screen width and height\n      $variable-unit: if($rfs-two-dimensional, vmin, vw);\n\n      // Calculate the variable width between 0 and $rfs-breakpoint\n      $variable-width: #{$fs-diff * 100 / $rfs-breakpoint}#{$variable-unit};\n\n      // Set the calculated font-size.\n      $rfs-fluid: calc(#{$min-width} + #{$variable-width}) #{$rfs-suffix};\n    }\n\n    // Rendering\n    @if $rfs-fluid == null {\n      // Only render static font-size if no fluid font-size is available\n      font-size: $rfs-static;\n    }\n    @else {\n      $mq-value: null;\n\n      // RFS breakpoint formatting\n      @if $rfs-breakpoint-unit == em or $rfs-breakpoint-unit == rem {\n        $mq-value: #{$rfs-breakpoint / $rfs-rem-value}#{$rfs-breakpoint-unit};\n      }\n      @else if $rfs-breakpoint-unit == px {\n        $mq-value: #{$rfs-breakpoint}px;\n      }\n      @else {\n        @error \"`#{$rfs-breakpoint-unit}` is not a valid unit for $rfs-breakpoint-unit. Use `px`, `em` or `rem`.\";\n      }\n\n      @if $rfs-class == \"disable\" {\n        // Adding an extra class increases specificity,\n        // which prevents the media query to override the font size\n        &,\n        .disable-responsive-font-size &,\n        &.disable-responsive-font-size {\n          font-size: $rfs-static;\n        }\n      }\n      @else {\n        font-size: $rfs-static;\n      }\n\n      @if $rfs-two-dimensional {\n        @media (max-width: #{$mq-value}), (max-height: #{$mq-value}) {\n          @if $rfs-class == \"enable\" {\n            .enable-responsive-font-size &,\n            &.enable-responsive-font-size {\n              font-size: $rfs-fluid;\n            }\n          }\n          @else {\n            font-size: $rfs-fluid;\n          }\n\n          @if $rfs-safari-iframe-resize-bug-fix {\n            // stylelint-disable-next-line length-zero-no-unit\n            min-width: 0vw;\n          }\n        }\n      }\n      @else {\n        @media (max-width: #{$mq-value}) {\n          @if $rfs-class == \"enable\" {\n            .enable-responsive-font-size &,\n            &.enable-responsive-font-size {\n              font-size: $rfs-fluid;\n            }\n          }\n          @else {\n            font-size: $rfs-fluid;\n          }\n\n          @if $rfs-safari-iframe-resize-bug-fix {\n            // stylelint-disable-next-line length-zero-no-unit\n            min-width: 0vw;\n          }\n        }\n      }\n    }\n  }\n}\n\n// The font-size & responsive-font-size mixin uses RFS to rescale font sizes\n@mixin font-size($fs, $important: false) {\n  @include rfs($fs, $important);\n}\n\n@mixin responsive-font-size($fs, $important: false) {\n  @include rfs($fs, $important);\n}\n", "//\n// Plugins: JQVMap\n//\n\n// Zoom Button size fixes\n.jqvmap-zoomin,\n.jqvmap-zoomout {\n  background-color: $button-default-background-color;\n  border: 1px solid $button-default-border-color;\n  border-radius: $btn-border-radius;\n  color: $button-default-color;\n  height: 15px;\n  width: 15px;\n\n  &:hover,\n  &:active,\n  &.hover {\n    background-color: darken($button-default-background-color, 5%);\n    color: darken($button-default-color, 10%);\n  }\n}\n", "//\n// Plugin: SweetAlert2\n//\n\n// Icon Colors\n.swal2-icon {\n  &.swal2-info {\n    border-color: ligthen($info, 20%);\n    color: $info;\n  }\n\n  &.swal2-warning {\n    border-color: ligthen($warning, 20%);\n    color: $warning;\n  }\n\n  &.swal2-error {\n    border-color: ligthen($danger, 20%);\n    color: $danger;\n  }\n\n  &.swal2-question {\n    border-color: ligthen($secondary, 20%);\n    color: $secondary;\n  }\n\n  &.swal2-success {\n    border-color: ligthen($success, 20%);\n    color: $success;\n\n    .swal2-success-ring {\n      border-color: ligthen($success, 20%);\n    }\n\n    [class^='swal2-success-line'] {\n      background-color: $success;\n    }\n  }\n\n}\n", "//\n// Plugin: Toastr\n//\n\n// Background to FontAwesome Icons\n// #toast-container > .toast {\n//     background-image: none !important;\n// }\n// #toast-container > .toast .toast-message:before {\n//     font-family: 'Font Awesome 5 Free';\n//     font-size: 24px;\n//     font-weight: 900;\n//     line-height: 18px;\n//     float: left;\n//     color: #FFF;\n//     padding-right: 0.5em;\n//     margin: auto 0.5em auto -1.5em;\n// }        \n// #toast-container > .toast-warning .toast-message:before {\n//     content: \"\\f06a\";\n// }\n// #toast-container > .toast-error .toast-message:before {\n//     content: \"\\f071\";\n// }\n// #toast-container > .toast-info .toast-message:before {\n//     content: \"\\f05a\";\n// }\n// #toast-container > .toast-success .toast-message:before {\n//     content: \"\\f058\";\n// }\n\n\n#toast-container {\n  // Background color\n  .toast {\n    background-color: $primary;\n  }\n\n  .toast-success {\n    background-color: $success;\n  }\n\n  .toast-error {\n    background-color: $danger;\n  }\n\n  .toast-info {\n    background-color: $info;\n  }\n\n  .toast-warning {\n    background-color: $warning;\n  }\n}\n\n// full width fix\n.toast-bottom-full-width .toast,\n.toast-top-full-width .toast {\n  max-width: inherit;\n}\n", "//\n// Plugin: Pace\n//\n\n.pace {\n  z-index: $zindex-main-sidebar + 10;\n\n  .pace-progress {\n    z-index: $zindex-main-sidebar + 11;\n  }\n\n  .pace-activity {\n    z-index: $zindex-main-sidebar + 12;\n  }\n}\n\n// Mixin \n@mixin pace-variant($name, $color) {\n  .pace-#{$name} {\n    .pace {\n      .pace-progress {\n        background: $color;\n      }\n    }\n  }\n\n  .pace-barber-shop-#{$name} {\n    .pace {\n      background: color-yiq($color);\n\n      .pace-progress {\n        background: $color;\n      }\n\n      .pace-activity {\n        background-image: linear-gradient(45deg, rgba(color-yiq($color), 0.2) 25%, transparent 25%, transparent 50%, rgba(color-yiq($color), 0.2) 50%, rgba(color-yiq($color), 0.2) 75%, transparent 75%, transparent);\n      }\n    }\n  }\n\n  .pace-big-counter-#{$name} {\n    .pace {\n      .pace-progress::after {\n        color: rgba($color, .19999999999999996);\n      }\n    }\n  }\n\n  .pace-bounce-#{$name} {\n    .pace {\n      .pace-activity {\n        background: $color;\n      }\n    }\n  }\n\n  .pace-center-atom-#{$name} {\n    .pace-progress {\n      height: 100px;\n      width: 80px;\n\n      &::before {\n        background: $color;\n        color: color-yiq($color);\n        font-size: .8rem;\n        line-height: .7rem;\n        padding-top: 17%;\n      }\n    }\n\n    .pace-activity {\n      border-color: $color;\n\n      &::after,\n      &::before {\n        border-color: $color;\n      }\n    }\n  }\n\n  .pace-center-circle-#{$name} {\n    .pace {\n      .pace-progress {\n        background: rgba($color, .8);\n        color: color-yiq($color);\n      }\n    }\n  }\n\n  .pace-center-radar-#{$name} {\n    .pace {\n      .pace-activity {\n        border-color: $color transparent transparent;\n      }\n\n      .pace-activity::before {\n        border-color: $color transparent transparent;\n      }\n    }\n  }\n\n  .pace-center-simple-#{$name} {\n    .pace {\n      background: color-yiq($color);\n      border-color: $color;\n\n      .pace-progress {\n        background: $color;\n      }\n    }\n  }\n\n  .pace-material-#{$name} {\n    .pace {\n      color: $color;\n    }\n  }\n\n  .pace-corner-indicator-#{$name} {\n    .pace {\n      .pace-activity {\n        background: $color;\n      }\n\n      .pace-activity::after,\n      .pace-activity::before {\n        border: 5px solid color-yiq($color);\n      }\n\n\n      .pace-activity::before {\n          border-right-color: rgba($color, .2);\n          border-left-color: rgba($color, .2);\n      }\n\n      .pace-activity::after {\n          border-top-color: rgba($color, .2);\n          border-bottom-color: rgba($color, .2);\n      }\n    }\n  }\n\n  .pace-fill-left-#{$name} {\n    .pace {\n      .pace-progress {\n        background-color: rgba($color, 0.19999999999999996);\n      }\n    }\n  }\n\n  .pace-flash-#{$name} {\n    .pace {\n      .pace-progress {\n        background: $color;\n      }\n\n      .pace-progress-inner {\n        box-shadow: 0 0 10px $color, 0 0 5px $color;\n      }\n\n      .pace-activity {\n        border-top-color: $color;\n        border-left-color: $color;\n      }\n    }\n  }\n\n  .pace-loading-bar-#{$name} {\n    .pace {\n      .pace-progress {\n        background: $color;\n        color: $color;\n        box-shadow: 120px 0 color-yiq($color), 240px 0 color-yiq($color);\n      }\n\n      .pace-activity {\n        box-shadow: inset 0 0 0 2px $color, inset 0 0 0 7px color-yiq($color);\n      }\n    }\n  }\n\n  .pace-mac-osx-#{$name} {\n    .pace {\n      .pace-progress {\n        background-color: $color;\n        box-shadow: inset -1px 0 $color, inset 0 -1px $color, inset 0 2px rgba(color-yiq($color), 0.5), inset 0 6px rgba(color-yiq($color), .3);\n      }\n\n      .pace-activity {\n        background-image: radial-gradient(rgba(color-yiq($color), .65) 0%, rgba(color-yiq($color), .15) 100%);\n        height: 12px;\n      }\n    }\n  }\n\n  .pace-progress-color-#{$name} {\n    .pace-progress {\n      color: $color;\n    }\n  }\n}\n\n\n@each $name, $color in $theme-colors {\n  @include pace-variant($name, $color);\n}\n\n@each $name, $color in $colors {\n  @include pace-variant($name, $color);\n}\n\n", "/**\n  * bootstrap-switch - Turn checkboxes and radio buttons into toggle switches.\n  *\n  * @version v3.4 (MODDED)\n  * @homepage https://bttstrp.github.io/bootstrap-switch\n  * <AUTHOR> <<EMAIL>> (http://larentis.eu)\n  * @license MIT\n  */\n\n$bootstrap-switch-border-radius: $btn-border-radius;\n$bootstrap-switch-handle-border-radius: .1rem;\n\n.bootstrap-switch {\n  border: $input-border-width solid $input-border-color;\n  border-radius: $bootstrap-switch-border-radius;\n  cursor: pointer;\n  direction: ltr;\n  display: inline-block;\n  line-height: .5rem;\n  overflow: hidden;\n  position: relative;\n  text-align: left;\n  transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;\n  user-select: none;\n  vertical-align: middle;\n  z-index: 0;\n\n  .bootstrap-switch-container {\n    border-radius: $bootstrap-switch-border-radius;\n    display: inline-block;\n    top: 0;\n    transform: translate3d(0, 0, 0);\n\n  }\n\n  &:focus-within {\n    box-shadow: $input-btn-focus-box-shadow;\n  }\n\n  .bootstrap-switch-handle-on,\n  .bootstrap-switch-handle-off,\n  .bootstrap-switch-label {\n    box-sizing: border-box;\n    cursor: pointer;\n    display: table-cell;\n    font-size: 1rem;\n    font-weight: 500;\n    line-height: 1.2rem;\n    padding: .25rem .5rem;\n    vertical-align: middle;\n  }\n\n  .bootstrap-switch-handle-on,\n  .bootstrap-switch-handle-off {\n    text-align: center;\n    z-index: 1;\n\n    &.bootstrap-switch-default {\n      background: $gray-200;\n      color: color-yiq($gray-200);\n    }\n\n    @each $name, $color in $theme-colors {\n      &.bootstrap-switch-#{$name} {\n        background: $color;\n        color: color-yiq($color);\n      }\n    }\n\n    @each $name, $color in $colors {\n      &.bootstrap-switch-#{$name} {\n        background: $color;\n        color: color-yiq($color);\n      }\n    }\n  }\n\n  .bootstrap-switch-handle-on {\n    border-bottom-left-radius: $bootstrap-switch-handle-border-radius;\n    border-top-left-radius: $bootstrap-switch-handle-border-radius;\n  }\n\n  .bootstrap-switch-handle-off {\n    border-bottom-right-radius: $bootstrap-switch-handle-border-radius;\n    border-top-right-radius: $bootstrap-switch-handle-border-radius;\n  }\n\n  input[type='radio'],\n  input[type='checkbox'] {\n    filter: alpha(opacity=0);\n    left: 0;\n    margin: 0;\n    opacity: 0;\n    position: absolute;\n    top: 0;\n    visibility: hidden;\n    z-index: -1;\n  }\n\n  &.bootstrap-switch-mini {\n    .bootstrap-switch-handle-on,\n    .bootstrap-switch-handle-off,\n    .bootstrap-switch-label {\n      font-size: .875rem;\n      line-height: 1.5;\n      padding: .1rem .3rem;\n    }\n  }\n\n  &.bootstrap-switch-small {\n    .bootstrap-switch-handle-on,\n    .bootstrap-switch-handle-off,\n    .bootstrap-switch-label {\n      font-size: .875rem;\n      line-height: 1.5;\n      padding: .2rem .4rem;\n    }\n  }\n\n  &.bootstrap-switch-large {\n    .bootstrap-switch-handle-on,\n    .bootstrap-switch-handle-off,\n    .bootstrap-switch-label {\n      font-size: 1.25rem;\n      line-height: 1.3333333rem;\n      padding: .3rem .5rem;\n    }\n  }\n\n  &.bootstrap-switch-disabled,\n  &.bootstrap-switch-readonly,\n  &.bootstrap-switch-indeterminate {\n    cursor: default;\n\n    .bootstrap-switch-handle-on,\n    .bootstrap-switch-handle-off,\n    .bootstrap-switch-label {\n      cursor: default;\n      filter: alpha(opacity=50);\n      opacity: .5;\n    }\n  }\n\n  &.bootstrap-switch-animate .bootstrap-switch-container {\n    transition: margin-left .5s;\n  }\n\n  &.bootstrap-switch-inverse {\n    .bootstrap-switch-handle-on {\n      border-radius: 0 $bootstrap-switch-handle-border-radius $bootstrap-switch-handle-border-radius 0;\n    }\n\n    .bootstrap-switch-handle-off {\n      border-radius: $bootstrap-switch-handle-border-radius 0 0 $bootstrap-switch-handle-border-radius;\n    }\n  }\n\n  // &.bootstrap-switch-focused {\n  //   border-color: $input-btn-focus-color;\n  //   box-shadow: $input-btn-focus-box-shadow;\n  //   outline: 0;\n  // }\n\n  &.bootstrap-switch-on .bootstrap-switch-label,\n  &.bootstrap-switch-inverse.bootstrap-switch-off .bootstrap-switch-label {\n    border-bottom-right-radius: $bootstrap-switch-handle-border-radius;\n    border-top-right-radius: $bootstrap-switch-handle-border-radius;\n  }\n\n  &.bootstrap-switch-off .bootstrap-switch-label,\n  &.bootstrap-switch-inverse.bootstrap-switch-on .bootstrap-switch-label {\n    border-bottom-left-radius: $bootstrap-switch-handle-border-radius;\n    border-top-left-radius: $bootstrap-switch-handle-border-radius;\n  }\n}\n", "//\n// Plugins: Miscellaneous\n// Old plugin codes\n//\n\n// _fix for sparkline tooltip\n.jqstooltip {\n  height: auto !important;\n  padding: 5px !important;\n  width: auto !important;\n}\n\n// jQueryUI\n.connectedSortable {\n  min-height: 100px;\n}\n\n.ui-helper-hidden-accessible {\n  border: 0;\n  clip: rect(0 0 0 0);\n  height: 1px;\n  margin: -1px;\n  overflow: hidden;\n  padding: 0;\n  position: absolute;\n  width: 1px;\n}\n\n.sort-highlight {\n  background: $gray-100;\n  border: 1px dashed $gray-300;\n  margin-bottom: 10px;\n}\n\n// Charts\n.chart {\n  overflow: hidden;\n  position: relative;\n}\n"]}