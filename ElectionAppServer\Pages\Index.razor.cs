﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using ElectionAppServer.DTO;
using ElectionAppServer.Model;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Syncfusion.Blazor.Grids;
using Syncfusion.Blazor.Popups;

namespace ElectionAppServer.Pages;

public partial class Index
{
    public SfGrid<ElectionDTO> dgPhase;
    private SfDialog dlgPhase;
    public double ExplodeIndex = 1;
    public string OuterRadius = "70%", ExplodeRadius = "10%";
    public List<ElectionDTO> PhasesList = new();
    public int StartAngle = 0, value = 0, EndAngle = 360, radiusValue = 90, exploderadius = 10;
    [CascadingParameter] private Task<AuthenticationState> authenticationStateTask { get; set; }
    public List<ElectionDTO> ElectionsList { get; set; } = new();
    public int TotalAssemblies { get; set; }
    public int TotalConstituencies { get; set; }
    public int TotalCandidates { get; set; }
    public int TotalNominations { get; set; }
    public int TotalParties { get; set; }
    public int TotalNamanigars { get; set; }

    //private List<PieDataDTO> assemblyWiseNominations = new List<PieDataDTO>();
    //private List<PieDataDTO> partyWiseNominations = new List<PieDataDTO>();
    //private List<PieDataDTO> dataSource = new List<PieDataDTO> { new PieDataDTO { xValue = "Chrome", yValue = 37 }, new PieDataDTO { xValue = "UC Browse", yValue = 17 }, new PieDataDTO { xValue = "iPhone", yValue = 19 }, new PieDataDTO { xValue = "Others", yValue = 4 }, new PieDataDTO { xValue = "Opera", yValue = 11 }, new PieDataDTO { xValue = "Android", yValue = 12 } };
    //public ElectionStateDTO elecState { get; set; } = new ElectionStateDTO();
    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        //return base.OnAfterRenderAsync(firstRender);
        if (firstRender)
        {
            var user = (await authenticationStateTask).User;
            if (user.Identity.IsAuthenticated)
                //ElectionsList = await Service.GetAll();
                //var dd =  await ProtectedSessionStore.GetAsync<ElectionStateDTO>("election");
                try
                {
                    var dd = await localStorage.GetAsync<ElectionStateDTO>(
                        await confreader.GetSettingValue("electionkey"));
                    state.SetState(dd.Value);
                    var mm = firstRender;
                    TotalAssemblies = await Service.GetTotalAssemblies(state.state.ElectionId);
                    TotalCandidates = await Service.GetTotalCandidatges();
                    TotalConstituencies = await Service.GetTotalConstituencies(state.state.PhaseId);
                    TotalNominations = await Service.GetTotalNominations(state.state.ElectionId, state.state.PhaseId);
                    TotalParties = await Service.GetTotalParties(state.state.PhaseId);
                    TotalNamanigars = await Service.GetTotalNamanigars();
                    //assemblyWiseNominations = await Service.GetAssemblyWiseNominations(state.state.ElectionId, state.state.PhaseId);
                    //partyWiseNominations = await Service.GetPartyWiseNominations(state.state.ElectionId, state.state.PhaseId);
                    StateHasChanged();
                }
                catch (Exception)
                {
                    await localStorage.DeleteAsync(await confreader.GetSettingValue("electionkey"));
                    state.SetState(new ElectionStateDTO
                        { ElectionId = 0, ElectionTitle = "", PhaseId = 0, PhaseTitle = "" });
                }
            else
                try
                {
                    //await  ProtectedSessionStore.DeleteAsync("election");
                    await localStorage.DeleteAsync(await confreader.GetSettingValue("electionkey"));
                    state.SetState(new ElectionStateDTO
                        { ElectionId = 0, ElectionTitle = "", PhaseId = 0, PhaseTitle = "" });
                }
                catch (Exception)
                {
                    // ignored
                }

            StateHasChanged();
        }

        if (state != null && state.state != null && firstRender)
        {
            TotalAssemblies = await Service.GetTotalAssemblies(state.state.ElectionId);
            TotalCandidates = await Service.GetTotalCandidatges();
            TotalConstituencies = await Service.GetTotalConstituencies(state.state.PhaseId);
            TotalNominations = await Service.GetTotalNominations(state.state.ElectionId, state.state.PhaseId);
            TotalParties = await Service.GetTotalParties(state.state.PhaseId);
            TotalNamanigars = await Service.GetTotalNamanigars();
            //assemblyWiseNominations = await Service.GetAssemblyWiseNominations(state.state.ElectionId, state.state.PhaseId);
            //partyWiseNominations = await Service.GetPartyWiseNominations(state.state.ElectionId, state.state.PhaseId);
            StateHasChanged();
        }
    }

    protected override async Task OnInitializedAsync()
    {
        state.OnChange += StateHasChanged;
        var user = (await authenticationStateTask).User;
        if (user.Identity.IsAuthenticated)
        {
            if (user.IsInRole("Namanigar")) NavigationManager.NavigateTo("/namanigar/results");
            ElectionsList = await Service.GetAllElections(user.Identity.Name);
            //var dd = await ProtectedSessionStore.GetAsync<ElectionStateDTO>("election");
            //state.SetState(dd.Value);
        }
        else
        {
            NavigationManager.NavigateTo("/Identity/Account/Login");
            try
            {
            }
            catch (Exception)
            {
                //throw;
            }
        }
        //try
        //{
        //   //var electionId = await ProtectedSessionStore.GetAsync<int>("electionId");
        //   //var electionTitle = await ProtectedSessionStore.GetAsync<string>("electionTitle");
        //   //var phaseId = await ProtectedSessionStore.GetAsync<int>("phaseId");
        //   //var phaseTitle = await ProtectedSessionStore.GetAsync<string>("phaseTitle");
        //   var dd = await ProtectedSessionStore.GetAsync<ElectionStateDTO>("election");
        //   state.SetState(dd.Value);
        //}
        //catch (Exception)
        //{
        //}
    }

    private async Task ClearElection()
    {
        try
        {
            state.state = new ElectionStateDTO { ElectionId = 0, ElectionTitle = "", PhaseId = 0, PhaseTitle = "" };
            //await  ProtectedSessionStore.DeleteAsync("election");
            await localStorage.DeleteAsync(await confreader.GetSettingValue("electionkey"));
            state.SetState(new ElectionStateDTO { ElectionId = 0, ElectionTitle = "", PhaseId = 0, PhaseTitle = "" });
            //await ProtectedSessionStore.SetAsync("election", state.state);
        }
        catch (Exception)
        {
            // ignored
        }

        StateHasChanged();
    }

    //private async Task ClearElection2()
    //{
    //   try
    //   {
    //      await ProtectedSessionStore.DeleteAsync("election");
    //   }
    //   catch (Exception)
    //   {
    //   }
    //   state.SetState(new ElectionStateDTO { PhaseTitle = "", PhaseId = 0, ElectionTitle = "", ElectionId = 0 });
    //   StateHasChanged();
    //}
    //private async Task SelectElection3(int electionId, string electionTitle, int phaseId, string phaseTitle)
    //{
    //   try
    //   {
    //      elecState = new ElectionStateDTO
    //      {
    //         ElectionId = electionId,
    //         ElectionTitle = electionTitle,
    //         PhaseId = phaseId,
    //         PhaseTitle = phaseTitle
    //      };
    //      await ProtectedSessionStore.SetAsync("election", elecState);
    //      StateHasChanged();
    //   }
    //   catch (Exception)
    //   {
    //      throw;
    //   }
    //}
    private async Task SelectPhase(int electionId, string electionTitle, int phaseId, string phaseTitle,
        ElectionType? electionType)
    {
        var dd = new ElectionStateDTO
        {
            ElectionId = electionId, ElectionTitle = electionTitle, PhaseId = phaseId, PhaseTitle = phaseTitle,
            ElectionType = electionType
        };
        await localStorage.SetAsync(await confreader.GetSettingValue("electionkey"), dd);
        state.SetState(dd);
        TotalAssemblies = await Service.GetTotalAssemblies(state.state.ElectionId);
        TotalCandidates = await Service.GetTotalCandidatges();
        TotalConstituencies = await Service.GetTotalConstituencies(state.state.PhaseId);
        TotalNominations = await Service.GetTotalNominations(state.state.ElectionId, state.state.PhaseId);
        TotalParties = await Service.GetTotalParties(state.state.PhaseId);
        TotalNamanigars = await Service.GetTotalNamanigars();
        await dlgPhase.HideAsync();
        StateHasChanged();
    }

    private async Task SelectElection(int electionId, string electionTitle, int phaseId, string phaseTitle,
        ElectionType? electionType)
    {
        try
        {
            PhasesList = await Service.GetAllElectionDates(electionId);
            //await ProtectedSessionStore.SetAsync("election", dd);
            //assemblyWiseNominations = await Service.GetAssemblyWiseNominations(state.state.ElectionId, state.state.PhaseId);
            //partyWiseNominations = await Service.GetPartyWiseNominations(state.state.ElectionId, state.state.PhaseId);
            //NavigationManager.NavigateTo("/search");
            StateHasChanged();
            //await JSRuntime.InvokeVoidAsync("alert", "done");
            await dlgPhase.ShowAsync();
        }
        catch (Exception ex)
        {
            //string msg = ex.Message;
            //if (ex.InnerException != null)
            //	msg += " Detail: " + ex.InnerException.Message;
            ////await JSRuntime.InvokeVoidAsync("alert", msg);
        }
    }
}