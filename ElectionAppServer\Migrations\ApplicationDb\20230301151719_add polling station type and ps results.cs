﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace ElectionAppServer.Migrations.ApplicationDb
{
    /// <inheritdoc />
    public partial class addpollingstationtypeandpsresults : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "pollingStationType",
                table: "PollingStations",
                type: "int",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "PollingStationCombinedResult",
                columns: table => new
                {
                    PollingStationId = table.Column<int>(type: "int", nullable: false),
                    ElectionPhaseId = table.Column<int>(type: "int", nullable: false),
                    Votes = table.Column<int>(type: "int", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PollingStationCombinedResult", x => new { x.ElectionPhaseId, x.PollingStationId });
                    table.ForeignKey(
                        name: "FK_PollingStationCombinedResult_ElectionPhases_ElectionPhaseId",
                        column: x => x.ElectionPhaseId,
                        principalTable: "ElectionPhases",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_PollingStationCombinedResult_PollingStations_PollingStationId",
                        column: x => x.PollingStationId,
                        principalTable: "PollingStations",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_PollingStationCombinedResult_PollingStationId",
                table: "PollingStationCombinedResult",
                column: "PollingStationId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "PollingStationCombinedResult");

            migrationBuilder.DropColumn(
                name: "pollingStationType",
                table: "PollingStations");
        }
    }
}
