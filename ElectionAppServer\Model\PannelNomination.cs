﻿using System;

namespace ElectionAppServer.Model;

public class PannelNomination
{
    public int Id { get; set; }
    public int PhaseId { get; set; }
    public int AssemblyId { get; set; }
    public int PanelId { get; set; }
    public string CandidateUrduName { get; set; }
    public string CandidateEnglishName { get; set; }
    public int SeatTypeId { get; set; }

    public ElectionPhase Phase { get; set; }
    public ElectionAssembly Assembly { get; set; }
    public Candidate Panel { get; set; }
    public SeatType seatType { get; set; }
    public int OrderNumber { get; set; }

    #region Audit Log

    public DateTime CreatedDate { get; set; }
    public string CreatedBy { get; set; }
    public DateTime? ModifiedDate { get; set; }
    public string ModifiedBy { get; set; }

    #endregion Audit Log
}