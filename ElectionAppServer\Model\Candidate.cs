﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace ElectionAppServer.Model;

public class Candidate
{
    public int Id { get; set; }
    public NominationType CandidateType { get; set; }
    public string UrduName { get; set; }
    public string EnglishName { get; set; }
    public string FatherEnglishName { get; set; }
    public string FatherUrduName { get; set; }
    public Gender? Gender { get; set; }
    public DateTime? DateOfBirth { get; set; }

    //public virtual List<ElectionCandidate> CandidateElections { get; set; }
    public string Trivia { get; set; }

    public string StrDistrict { get; set; }
    public int? DistrictId { get; set; }
    public virtual District District { get; set; }
    public int? ProfessionId { get; set; }
    public virtual Profession Profession { get; set; }
    public int? EducationId { get; set; }
    public virtual Education Education { get; set; }
    public int? LanguageId { get; set; }
    public virtual Language Language { get; set; }
    public int? CasteId { get; set; }
    public virtual Caste Caste { get; set; }
    public string TotalAssets { get; set; }
    public string ContactNumber { get; set; }
    public bool IsFresh { get; set; } = false;
    public virtual List<CandidatePartyAffiliation> PartyAffiliations { get; set; }
    public virtual List<CandidtePoliticalCareer> CandidtePoliticalCareers { get; set; }
    public virtual List<Nomination> Nominations { get; set; }
    public virtual List<CandidateDistrict> CandidateDistricts { get; set; }
    public string EducationDegree { get; set; }
    public int? Day { get; set; }
    public int? Year { get; set; }
    public int? Month { get; set; }
    public bool IsBigName { get; set; } = false;
    public bool HasRealDOB { get; set; } = true;

    [DefaultValue(CandidatePoll.GeneralElection)]
    public CandidatePoll CandidatePoll { get; set; } = CandidatePoll.GeneralElection;

    public virtual List<ResultLogDetail> ResultLogDetails { get; set; }

    public string Source { get; set; }
    public int? SourceId { get; set; }

    //public virtual List<PanelNomination> PanelNominations { get; set; }
    //public virtual List<Candidate> PanelCandidates { get; set; }
    //public int? PanelId { get; set; }
    //public virtual Candidate Panel { get; set; }
    //[ForeignKey("CurrentParty")]
    //public int? CurrentPartyId { get; set; }
    //public virtual Party CurrentParty { get; set; }

    #region Audit Log Fields

    public DateTime CreatedDate { get; set; } = DateTime.Now;
    public DateTime? ModifiedDate { get; set; }
    [StringLength(450)] public string CreatedBy { get; set; }
    [StringLength(450)] public string ModifiedBy { get; set; }

    #endregion Audit Log Fields
}

public enum CandidatePoll
{
    GeneralElection = 1,
    GilgitElection = 2,
    KashmirElection = 3,
    LocalBodyElectionKPK = 4,
    Cantonment = 5,
    LocalBodyElectionBalochistan = 6,
    LocalBodyElectionSindh = 7,
    LocalBodyElectionPunjab = 8,
    LocalBodyAJK = 9,
    LocalBodyIslamabad = 10
}

public enum NominationType
{
    //Politician = 1,
    //Councillor = 2,
    //Both = 3
    Candidate = 1,
    Panel = 2
}

public enum Gender
{
    Male = 1,
    Female = 2,
    Transgender = 3
}