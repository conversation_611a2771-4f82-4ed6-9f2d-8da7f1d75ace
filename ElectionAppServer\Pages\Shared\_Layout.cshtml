﻿@using Microsoft.AspNetCore.Hosting
@using Microsoft.AspNetCore.Mvc.ViewEngines
@inject IWebHostEnvironment Environment
@inject ICompositeViewEngine Engine
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <title>Election Portal</title>
    <link rel="stylesheet" href="~/Identity/lib/bootstrap/dist/css/bootstrap.min.css"/>
    <link rel="stylesheet" href="~/Identity/css/site.css"/>
</head>
<body>

@RenderBody()

@*<footer class="footer border-top text-muted">
         <div class="container">
            &copy; 2020 - ElectionAppServer - <a asp-area="" asp-page="Privacy">Privacy</a>
         </div>
      </footer>*@
<script src="~/Identity/lib/jquery/dist/jquery.min.js"></script>
<script src="~/Identity/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
<script src="~/Identity/js/site.js" asp-append-version="true"></script>
@RenderSection("Scripts", false)
</body>
</html>