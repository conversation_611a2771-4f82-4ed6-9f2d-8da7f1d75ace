﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace ElectionAppServer.Migrations.ApplicationDb
{
	public partial class CombineContext : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropTable(
				 name: "NamnigarConstituencies");

			migrationBuilder.DropTable(
				 name: "<PERSON><PERSON><PERSON><PERSON>");

			//migrationBuilder.CreateTable(
			//    name: "AspNetRoles",
			//    columns: table => new
			//    {
			//        Id = table.Column<string>(nullable: false),
			//        Name = table.Column<string>(maxLength: 256, nullable: true),
			//        NormalizedName = table.Column<string>(maxLength: 256, nullable: true),
			//        ConcurrencyStamp = table.Column<string>(nullable: true)
			//    },
			//    constraints: table =>
			//    {
			//        table.PrimaryKey("PK_AspNetRoles", x => x.Id);
			//    });

			//migrationBuilder.CreateTable(
			//    name: "AspNetUsers",
			//    columns: table => new
			//    {
			//        Id = table.Column<string>(nullable: false),
			//        UserName = table.Column<string>(maxLength: 256, nullable: true),
			//        NormalizedUserName = table.Column<string>(maxLength: 256, nullable: true),
			//        Email = table.Column<string>(maxLength: 256, nullable: true),
			//        NormalizedEmail = table.Column<string>(maxLength: 256, nullable: true),
			//        EmailConfirmed = table.Column<bool>(nullable: false),
			//        PasswordHash = table.Column<string>(nullable: true),
			//        SecurityStamp = table.Column<string>(nullable: true),
			//        ConcurrencyStamp = table.Column<string>(nullable: true),
			//        PhoneNumber = table.Column<string>(nullable: true),
			//        PhoneNumberConfirmed = table.Column<bool>(nullable: false),
			//        TwoFactorEnabled = table.Column<bool>(nullable: false),
			//        LockoutEnd = table.Column<DateTimeOffset>(nullable: true),
			//        LockoutEnabled = table.Column<bool>(nullable: false),
			//        AccessFailedCount = table.Column<int>(nullable: false),
			//        FullName = table.Column<string>(maxLength: 200, nullable: true),
			//        Address = table.Column<string>(maxLength: 500, nullable: true),
			//        Gender = table.Column<int>(nullable: true)
			//    },
			//    constraints: table =>
			//    {
			//        table.PrimaryKey("PK_AspNetUsers", x => x.Id);
			//    });

			//migrationBuilder.CreateTable(
			//    name: "AspNetRoleClaims",
			//    columns: table => new
			//    {
			//        Id = table.Column<int>(nullable: false)
			//            .Annotation("SqlServer:Identity", "1, 1"),
			//        RoleId = table.Column<string>(nullable: false),
			//        ClaimType = table.Column<string>(nullable: true),
			//        ClaimValue = table.Column<string>(nullable: true)
			//    },
			//    constraints: table =>
			//    {
			//        table.PrimaryKey("PK_AspNetRoleClaims", x => x.Id);
			//        table.ForeignKey(
			//            name: "FK_AspNetRoleClaims_AspNetRoles_RoleId",
			//            column: x => x.RoleId,
			//            principalTable: "AspNetRoles",
			//            principalColumn: "Id",
			//            onDelete: ReferentialAction.Cascade);
			//    });

			//migrationBuilder.CreateTable(
			//    name: "AspNetUserClaims",
			//    columns: table => new
			//    {
			//        Id = table.Column<int>(nullable: false)
			//            .Annotation("SqlServer:Identity", "1, 1"),
			//        UserId = table.Column<string>(nullable: false),
			//        ClaimType = table.Column<string>(nullable: true),
			//        ClaimValue = table.Column<string>(nullable: true)
			//    },
			//    constraints: table =>
			//    {
			//        table.PrimaryKey("PK_AspNetUserClaims", x => x.Id);
			//        table.ForeignKey(
			//            name: "FK_AspNetUserClaims_AspNetUsers_UserId",
			//            column: x => x.UserId,
			//            principalTable: "AspNetUsers",
			//            principalColumn: "Id",
			//            onDelete: ReferentialAction.Cascade);
			//    });

			//migrationBuilder.CreateTable(
			//    name: "AspNetUserLogins",
			//    columns: table => new
			//    {
			//        LoginProvider = table.Column<string>(maxLength: 128, nullable: false),
			//        ProviderKey = table.Column<string>(maxLength: 128, nullable: false),
			//        ProviderDisplayName = table.Column<string>(nullable: true),
			//        UserId = table.Column<string>(nullable: false)
			//    },
			//    constraints: table =>
			//    {
			//        table.PrimaryKey("PK_AspNetUserLogins", x => new { x.LoginProvider, x.ProviderKey });
			//        table.ForeignKey(
			//            name: "FK_AspNetUserLogins_AspNetUsers_UserId",
			//            column: x => x.UserId,
			//            principalTable: "AspNetUsers",
			//            principalColumn: "Id",
			//            onDelete: ReferentialAction.Cascade);
			//    });

			//migrationBuilder.CreateTable(
			//    name: "AspNetUserRoles",
			//    columns: table => new
			//    {
			//        UserId = table.Column<string>(nullable: false),
			//        RoleId = table.Column<string>(nullable: false)
			//    },
			//    constraints: table =>
			//    {
			//        table.PrimaryKey("PK_AspNetUserRoles", x => new { x.UserId, x.RoleId });
			//        table.ForeignKey(
			//            name: "FK_AspNetUserRoles_AspNetRoles_RoleId",
			//            column: x => x.RoleId,
			//            principalTable: "AspNetRoles",
			//            principalColumn: "Id",
			//            onDelete: ReferentialAction.Cascade);
			//        table.ForeignKey(
			//            name: "FK_AspNetUserRoles_AspNetUsers_UserId",
			//            column: x => x.UserId,
			//            principalTable: "AspNetUsers",
			//            principalColumn: "Id",
			//            onDelete: ReferentialAction.Cascade);
			//    });

			//migrationBuilder.CreateTable(
			//    name: "AspNetUserTokens",
			//    columns: table => new
			//    {
			//        UserId = table.Column<string>(nullable: false),
			//        LoginProvider = table.Column<string>(maxLength: 128, nullable: false),
			//        Name = table.Column<string>(maxLength: 128, nullable: false),
			//        Value = table.Column<string>(nullable: true)
			//    },
			//    constraints: table =>
			//    {
			//        table.PrimaryKey("PK_AspNetUserTokens", x => new { x.UserId, x.LoginProvider, x.Name });
			//        table.ForeignKey(
			//            name: "FK_AspNetUserTokens_AspNetUsers_UserId",
			//            column: x => x.UserId,
			//            principalTable: "AspNetUsers",
			//            principalColumn: "Id",
			//            onDelete: ReferentialAction.Cascade);
			//    });

			migrationBuilder.CreateTable(
				 name: "NamanigarConstituencies",
				 columns: table => new
				 {
					 NamanigarId = table.Column<string>(nullable: false),
					 ElectionAssemblyId = table.Column<int>(nullable: false),
					 StructureId = table.Column<int>(nullable: false)
				 },
				 constraints: table =>
				 {
					 table.PrimaryKey("PK_NamanigarConstituencies", x => new { x.ElectionAssemblyId, x.NamanigarId, x.StructureId });
					 table.ForeignKey(
							  name: "FK_NamanigarConstituencies_ElectionAssemblies_ElectionAssemblyId",
							  column: x => x.ElectionAssemblyId,
							  principalTable: "ElectionAssemblies",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Restrict);
					 table.ForeignKey(
							  name: "FK_NamanigarConstituencies_AspNetUsers_NamanigarId",
							  column: x => x.NamanigarId,
							  principalTable: "AspNetUsers",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Cascade);
					 table.ForeignKey(
							  name: "FK_NamanigarConstituencies_Structures_StructureId",
							  column: x => x.StructureId,
							  principalTable: "Structures",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Restrict);
				 });

			//migrationBuilder.CreateIndex(
			//    name: "IX_AspNetRoleClaims_RoleId",
			//    table: "AspNetRoleClaims",
			//    column: "RoleId");

			//migrationBuilder.CreateIndex(
			//    name: "RoleNameIndex",
			//    table: "AspNetRoles",
			//    column: "NormalizedName",
			//    unique: true,
			//    filter: "[NormalizedName] IS NOT NULL");

			//migrationBuilder.CreateIndex(
			//    name: "IX_AspNetUserClaims_UserId",
			//    table: "AspNetUserClaims",
			//    column: "UserId");

			//migrationBuilder.CreateIndex(
			//    name: "IX_AspNetUserLogins_UserId",
			//    table: "AspNetUserLogins",
			//    column: "UserId");

			//migrationBuilder.CreateIndex(
			//    name: "IX_AspNetUserRoles_RoleId",
			//    table: "AspNetUserRoles",
			//    column: "RoleId");

			//migrationBuilder.CreateIndex(
			//    name: "EmailIndex",
			//    table: "AspNetUsers",
			//    column: "NormalizedEmail");

			//migrationBuilder.CreateIndex(
			//    name: "UserNameIndex",
			//    table: "AspNetUsers",
			//    column: "NormalizedUserName",
			//    unique: true,
			//    filter: "[NormalizedUserName] IS NOT NULL");

			//migrationBuilder.CreateIndex(
			//    name: "IX_NamanigarConstituencies_NamanigarId",
			//    table: "NamanigarConstituencies",
			//    column: "NamanigarId");

			//migrationBuilder.CreateIndex(
			//    name: "IX_NamanigarConstituencies_StructureId",
			//    table: "NamanigarConstituencies",
			//    column: "StructureId");
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			//migrationBuilder.DropTable(
			//    name: "AspNetRoleClaims");

			//migrationBuilder.DropTable(
			//    name: "AspNetUserClaims");

			//migrationBuilder.DropTable(
			//    name: "AspNetUserLogins");

			//migrationBuilder.DropTable(
			//    name: "AspNetUserRoles");

			//migrationBuilder.DropTable(
			//    name: "AspNetUserTokens");

			migrationBuilder.DropTable(
				 name: "NamanigarConstituencies");

			//migrationBuilder.DropTable(
			//    name: "AspNetRoles");

			//migrationBuilder.DropTable(
			//    name: "AspNetUsers");

			//migrationBuilder.CreateTable(
			//    name: "Namanigars",
			//    columns: table => new
			//    {
			//        Id = table.Column<int>(type: "int", nullable: false)
			//            .Annotation("SqlServer:Identity", "1, 1"),
			//        Code = table.Column<string>(type: "nvarchar(max)", nullable: true),
			//        CreatedBy = table.Column<string>(type: "nvarchar(450)", maxLength: 450, nullable: true),
			//        CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
			//        DistrictId = table.Column<int>(type: "int", nullable: false),
			//        ElectionId = table.Column<int>(type: "int", nullable: false),
			//        Email = table.Column<string>(type: "nvarchar(250)", maxLength: 250, nullable: true),
			//        Mobile = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
			//        ModifiedBy = table.Column<string>(type: "nvarchar(450)", maxLength: 450, nullable: true),
			//        ModifiedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
			//        Name = table.Column<string>(type: "nvarchar(max)", nullable: true)
			//    },
			//    constraints: table =>
			//    {
			//        table.PrimaryKey("PK_Namanigars", x => x.Id);
			//        table.ForeignKey(
			//            name: "FK_Namanigars_Structures_DistrictId",
			//            column: x => x.DistrictId,
			//            principalTable: "Structures",
			//            principalColumn: "Id",
			//            onDelete: ReferentialAction.Restrict);
			//        table.ForeignKey(
			//            name: "FK_Namanigars_Elections_ElectionId",
			//            column: x => x.ElectionId,
			//            principalTable: "Elections",
			//            principalColumn: "Id",
			//            onDelete: ReferentialAction.Restrict);
			//    });

			migrationBuilder.CreateTable(
				 name: "NamnigarConstituencies",
				 columns: table => new
				 {
					 ElectionAssemblyId = table.Column<int>(type: "int", nullable: false),
					 NamanigarId = table.Column<int>(type: "int", nullable: false),
					 StructureId = table.Column<int>(type: "int", nullable: false)
				 },
				 constraints: table =>
				 {
					 table.PrimaryKey("PK_NamnigarConstituencies", x => new { x.ElectionAssemblyId, x.NamanigarId, x.StructureId });
					 table.ForeignKey(
							  name: "FK_NamnigarConstituencies_ElectionAssemblies_ElectionAssemblyId",
							  column: x => x.ElectionAssemblyId,
							  principalTable: "ElectionAssemblies",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Restrict);
					 table.ForeignKey(
							  name: "FK_NamnigarConstituencies_Namanigars_NamanigarId",
							  column: x => x.NamanigarId,
							  principalTable: "Namanigars",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Restrict);
					 table.ForeignKey(
							  name: "FK_NamnigarConstituencies_Structures_StructureId",
							  column: x => x.StructureId,
							  principalTable: "Structures",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Restrict);
				 });

			migrationBuilder.CreateIndex(
				 name: "IX_Namanigars_DistrictId",
				 table: "Namanigars",
				 column: "DistrictId");

			migrationBuilder.CreateIndex(
				 name: "IX_Namanigars_ElectionId",
				 table: "Namanigars",
				 column: "ElectionId");

			migrationBuilder.CreateIndex(
				 name: "IX_NamnigarConstituencies_NamanigarId",
				 table: "NamnigarConstituencies",
				 column: "NamanigarId");

			migrationBuilder.CreateIndex(
				 name: "IX_NamnigarConstituencies_StructureId",
				 table: "NamnigarConstituencies",
				 column: "StructureId");
		}
	}
}