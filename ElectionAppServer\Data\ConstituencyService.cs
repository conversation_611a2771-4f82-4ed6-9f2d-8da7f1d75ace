﻿using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Dapper;
using ElectionAppServer.DTO;
using ElectionAppServer.Model;
using ElectionAppServer.Model.Structures;
using Microsoft.EntityFrameworkCore;

namespace ElectionAppServer.Data;

public class ConstituencyService
{
    private readonly IDbContextFactory<ApplicationDbContext> _contextFactory;

    public ConstituencyService(IDbContextFactory<ApplicationDbContext> contextFactory)
    {
        _contextFactory = contextFactory;
    }

    public Task<string> AddConstituencyToAssembly(int electionAssemblyId, List<int> structureIds,
        ElectionAssemblyType typeId, string user)
    {
        using var dc = _contextFactory.CreateDbContext();
        using var tran = dc.Database.BeginTransaction();
        foreach (var cc in structureIds)
        {
            var q = (from aa in dc.Structures
                where aa.Id == cc
                select aa).FirstOrDefault();
            if (q != null)
            {
                q.AssemblyId = electionAssemblyId;
                q.ModifiedBy = user;
                q.ModifiedDate = DateTime.Now;
                dc.SaveChanges();
            }
        }

        tran.Commit();
        return Task.FromResult("OK");
    }

    public Task<string> CreateStructure(CreateStructureDTO obj, string user, int phaseId)
    {
        using var dc = _contextFactory.CreateDbContext();
        obj.EnglishTitle = obj.EnglishTitle.Trim();
        obj.UrduTitle = obj.UrduTitle.Trim();
        var electionId = dc.ElectionAssemblies.Find(obj.ElectionAssemblyId).ElectionId;

        var eaId = dc.ElectionAssemblies.Find(obj.ElectionAssemblyId).ElectionId;
        // Validate
        if (obj.StructureType == StructureType.UnionCouncil)
        {
            var urduTitleExist = (from aa in dc.Structures.OfType<UnionCouncil>()
                where aa.TownId == obj.TownId
                      && aa.UrduTitle == obj.UrduTitle
                      && aa.Id != obj.Id
                      && aa.ElectionId == electionId
                select aa).Any();
            if (urduTitleExist)
                throw new Exception($"Union Council {obj.UrduTitle} already exist");

            var engTitleExist = (from aa in dc.Structures.OfType<UnionCouncil>()
                where aa.TownId == obj.TownId
                      && aa.EnglishTitle == obj.EnglishTitle
                      && aa.ElectionId == electionId
                      && aa.Id != obj.Id
                select aa).Any();

            if (engTitleExist)
                throw new Exception($"Union Council {obj.EnglishTitle} already exist");

            engTitleExist = (from aa in dc.Structures.OfType<UnionCouncil>()
                where aa.TownId == obj.TownId
                      && aa.Code == obj.Code
                      && aa.ElectionId == electionId
                      && aa.Id != obj.Id
                select aa).Any();

            if (engTitleExist)
                throw new Exception($"Union Council with code {obj.Code} already exist");
        }

        if (obj.StructureType == StructureType.Ward)
        {
            var ute = (from aa in dc.Structures.OfType<Ward>()
                where aa.UnionCouncilId == obj.UCId
                      && aa.UrduTitle == obj.UrduTitle
                      && aa.ElectionId == electionId
                      && aa.Id != obj.Id
                select aa).Any();
            if (ute)
                throw new Exception($"Ward \"{obj.UrduTitle}\" already Exist");

            var ete = (from aa in dc.Structures.OfType<Ward>()
                where aa.UnionCouncilId == obj.UCId
                      && aa.EnglishTitle == obj.EnglishTitle
                      && aa.ElectionId == electionId
                      && aa.Id != obj.Id
                select aa).Any();
            if (ete)
                throw new Exception($"Ward \"{obj.EnglishTitle}\" already Exist");

            ete = (from aa in dc.Structures.OfType<Ward>()
                where aa.UnionCouncilId == obj.UCId
                      && aa.Code == obj.Code
                      && aa.ElectionId == electionId
                      && aa.Id != obj.Id
                select aa).Any();
            if (ete)
                throw new Exception($"Ward with code\"{obj.Code}\" already Exist");
        }

        if (obj.StructureType == StructureType.NA)
        {
            var engExist = (from aa in dc.Structures.OfType<NationalAssemblyHalka>()
                where aa.ElectionId == electionId &&
                      aa.EnglishTitle == obj.EnglishTitle
                      && aa.Id != obj.Id
                select aa).Any();
            if (engExist)
                throw new Exception($"Constituency with title {obj.EnglishTitle} already exist");

            var urdExist = (from aa in dc.Structures.OfType<NationalAssemblyHalka>()
                where aa.ElectionId == electionId &&
                      aa.UrduTitle == obj.UrduTitle
                      && aa.Id != obj.Id
                select aa).Any();
            if (urdExist)
                throw new Exception($"Constituency with title {obj.UrduTitle} already exist");

            var codeExist = (from aa in dc.Structures.OfType<NationalAssemblyHalka>()
                where aa.ElectionId == electionId &&
                      aa.Code == obj.Code
                      && aa.Id != obj.Id
                select aa).Any();
            if (codeExist)
                throw new Exception($"Constituency with Code {obj.Code} already exist");
        }

        if (obj.StructureType == StructureType.PA)
        {
            var engExist = (from aa in dc.Structures.OfType<ProvincialAssemblyHalka>()
                where aa.ElectionId == electionId &&
                      aa.EnglishTitle == obj.EnglishTitle
                      && aa.Id != obj.Id
                select aa).Any();
            if (engExist)
                throw new Exception($"Constituency with title {obj.EnglishTitle} already exist");

            var urdExist = (from aa in dc.Structures.OfType<ProvincialAssemblyHalka>()
                where aa.ElectionId == electionId &&
                      aa.UrduTitle == obj.UrduTitle
                      && aa.Id != obj.Id
                select aa).Any();
            if (urdExist)
                throw new Exception($"Constituency with title {obj.UrduTitle} already exist");

            var codeExist = (from aa in dc.Structures.OfType<ProvincialAssemblyHalka>()
                where aa.ElectionId == electionId &&
                      aa.Code == obj.Code
                      && aa.Id != obj.Id
                select aa).Any();
            if (codeExist)
                throw new Exception($"Constituency with Code {obj.Code} already exist");
        }

        using var tran = dc.Database.BeginTransaction();
        // Create Structure
        try
        {
            if (obj.Id == 0)
            {
                if (obj.StructureType == StructureType.UnionCouncil)
                {
                    if (obj.TownId == null) throw new Exception("Please select town");

                    var uc = new UnionCouncil
                    {
                        CreatedBy = user,
                        CreatedDate = DateTime.Now,
                        EnglishTitle = obj.EnglishTitle,
                        TownId = (int)obj.TownId,
                        UrduTitle = obj.UrduTitle,
                        Code = obj.Code,
                        //CasteId = obj.CasteId,
                        Castes = obj.Castes,
                        ElectionId = electionId,
                        FemaleVoters = obj.FemaleVoters,
                        ImportantAreas = obj.ImportantAreas,
                        ImportantPoliticalPersonalities = obj.ImportantPoliticalPersonalities,
                        //LanguageId = obj.LanguageId,
                        Languages = obj.Languages,
                        MaleVoters = obj.MaleVoters,

                        Problems = obj.Problems,
                        RuralAreaPer = obj.RuralAreaPer,
                        TotalPollingStations = obj.TotalPollingStations,
                        Trivia = obj.Trivia,
                        UrbanAreaPer = obj.UrbanAreaPer,
                        Area = obj.Area,
                        AverageHouseholdIncome = obj.AverageHouseholdIncome,
                        LiteracyRate = obj.LiteracyRate,
                        MajorityIncomeSource = obj.MajorityIncomeSource,
                        Population = obj.Population,
                        AssemblyId = obj.ElectionAssemblyId,
                        GeneralTrivia = obj.GeneralTrivia,
                        HasBilaMokablia = false,
                        IsPostponed = false
                    };
                    dc.Add(uc);
                    dc.SaveChanges();
                    obj.Id = uc.Id;
                    SavePollingScheme(dc, obj, phaseId, user);
                    //var cons = new ElectionAssemblyConstituency
                    //{
                    //   CreatedBy = user,
                    //   CreatedDate = DateTime.Now,
                    //   ElectionAssemblyId = obj.ElectionAssemblyId,
                    //   StructureId = uc.Id,
                    //};
                    //dc.Add(cons);
                    //dc.SaveChanges();
                    tran.Commit();
                    return Task.FromResult("OK");
                }

                if (obj.StructureType == StructureType.Ward)
                {
                    if (obj.UCId == null) throw new Exception("Please select Union Council");
                    var w = new Ward
                    {
                        CreatedBy = user,
                        CreatedDate = DateTime.Now,
                        EnglishTitle = obj.EnglishTitle,
                        UnionCouncilId = (int)obj.UCId,
                        UrduTitle = obj.UrduTitle,
                        Code = obj.Code,
                        //CasteId = obj.CasteId,
                        ElectionId = electionId,
                        FemaleVoters = obj.FemaleVoters,
                        ImportantAreas = obj.ImportantAreas,
                        ImportantPoliticalPersonalities = obj.ImportantPoliticalPersonalities,
                        //LanguageId = obj.LanguageId,
                        Languages = obj.Languages,
                        Castes = obj.Castes,
                        MaleVoters = obj.MaleVoters,

                        Problems = obj.Problems,
                        RuralAreaPer = obj.RuralAreaPer,
                        TotalPollingStations = obj.TotalPollingStations,
                        Trivia = obj.Trivia,
                        UrbanAreaPer = obj.UrbanAreaPer,
                        Area = obj.Area,
                        AverageHouseholdIncome = obj.AverageHouseholdIncome,
                        LiteracyRate = obj.LiteracyRate,
                        MajorityIncomeSource = obj.MajorityIncomeSource,
                        Population = obj.Population,
                        AssemblyId = obj.ElectionAssemblyId,
                        GeneralTrivia = obj.GeneralTrivia,
                        HasBilaMokablia = false,
                        IsPostponed = false
                    };
                    dc.Add(w);
                    //var cons = new ElectionAssemblyConstituency
                    //{
                    //   CreatedBy = user,
                    //   CreatedDate = DateTime.Now,
                    //   ElectionAssemblyId = obj.ElectionAssemblyId,
                    //   StructureId = w.Id,
                    //};
                    //dc.Add(cons);
                    obj.Id = w.Id;
                    SavePollingScheme(dc, obj, phaseId, user);
                    dc.SaveChanges();
                    return Task.FromResult("OK");
                }

                if (obj.StructureType == StructureType.NA)
                {
                    //if (obj.UCId != null) parentAdminUnitId = (int)obj.UCId;
                    //else if (obj.TownId != null) parentAdminUnitId = (int)obj.TownId;
                    //else if (obj.DistrictId != null) parentAdminUnitId = (int)obj.DistrictId;
                    //else if (obj.DivisionId != null) parentAdminUnitId = (int)obj.DivisionId;
                    //else if (obj.ProvinceId != null) parentAdminUnitId = (int)obj.ProvinceId;

                    if (obj.DistrictId == null)
                        throw new Exception("Please select district from list");
                    var parentAdminUnitId = (int)obj.DistrictId;
                    var na = new NationalAssemblyHalka
                    {
                        UrduTitle = obj.UrduTitle,
                        EnglishTitle = obj.EnglishTitle,
                        ElectionId = electionId,
                        //CasteId = obj.CasteId,
                        Castes = obj.Castes,
                        Languages = obj.Languages,
                        CreatedBy = user,
                        CreatedDate = DateTime.Now,
                        FemaleVoters = obj.FemaleVoters,
                        ImportantAreas = obj.ImportantAreas,
                        ImportantPoliticalPersonalities = obj.ImportantPoliticalPersonalities,
                        IsSeat = true,
                        //LanguageId = obj.LanguageId,
                        MaleVoters = obj.MaleVoters,

                        Problems = obj.Problems,
                        RuralAreaPer = obj.RuralAreaPer,
                        DistrictId = parentAdminUnitId,
                        TotalPollingStations = obj.TotalPollingStations,
                        Trivia = obj.Trivia,
                        UrbanAreaPer = obj.UrbanAreaPer,
                        Code = obj.Code,
                        Area = obj.Area,
                        AverageHouseholdIncome = obj.AverageHouseholdIncome,
                        LiteracyRate = obj.LiteracyRate,
                        MajorityIncomeSource = obj.MajorityIncomeSource,
                        Population = obj.Population,
                        AssemblyId = obj.ElectionAssemblyId,
                        GeneralTrivia = obj.GeneralTrivia,
                        IsPostponed = false,
                        HasBilaMokablia = false
                    };
                    dc.Structures.Add(na);
                    dc.SaveChanges();
                    //var nah = new ElectionAssemblyConstituency
                    //{
                    //   ElectionAssemblyId = obj.ElectionAssemblyId,
                    //   StructureId = na.Id
                    //};
                    //dc.ElectionAssemblyConstituencies.Add(nah);
                    //dc.SaveChanges();
                    obj.Id = na.Id;
                    SavePollingScheme(dc, obj, phaseId, user);
                    tran.Commit();
                    return Task.FromResult("OK");
                }

                if (obj.StructureType == StructureType.PA)
                {
                    //if (obj.UCId != null) parentAdminUnitId = (int)obj.UCId;
                    //else if (obj.TownId != null) parentAdminUnitId = (int)obj.TownId;
                    //else if (obj.DistrictId != null) parentAdminUnitId = (int)obj.DistrictId;
                    //else if (obj.DivisionId != null) parentAdminUnitId = (int)obj.DivisionId;
                    //else if (obj.ProvinceId != null) parentAdminUnitId = (int)obj.ProvinceId;

                    if (obj.DistrictId == null)
                        throw new Exception("Please select district from list");
                    var parentAdminUnitId = (int)obj.DistrictId;

                    var na = new ProvincialAssemblyHalka
                    {
                        UrduTitle = obj.UrduTitle,
                        EnglishTitle = obj.EnglishTitle,
                        ElectionId = electionId,
                        //CasteId = obj.CasteId,
                        CreatedBy = user,
                        CreatedDate = DateTime.Now,
                        FemaleVoters = obj.FemaleVoters,
                        ImportantAreas = obj.ImportantAreas,
                        ImportantPoliticalPersonalities = obj.ImportantPoliticalPersonalities,
                        IsSeat = true,
                        //LanguageId = obj.LanguageId,
                        Castes = obj.Castes,
                        Languages = obj.Languages,
                        MaleVoters = obj.MaleVoters,

                        Problems = obj.Problems,
                        RuralAreaPer = obj.RuralAreaPer,
                        DistrictId = parentAdminUnitId,
                        TotalPollingStations = obj.TotalPollingStations,
                        Trivia = obj.Trivia,
                        UrbanAreaPer = obj.UrbanAreaPer,
                        Code = obj.Code,
                        Area = obj.Area,
                        AverageHouseholdIncome = obj.AverageHouseholdIncome,
                        LiteracyRate = obj.LiteracyRate,
                        MajorityIncomeSource = obj.MajorityIncomeSource,
                        Population = obj.Population,
                        AssemblyId = obj.ElectionAssemblyId,
                        GeneralTrivia = obj.GeneralTrivia,
                        IsPostponed = false,
                        HasBilaMokablia = false
                    };
                    dc.Structures.Add(na);
                    dc.SaveChanges();
                    na.Code = obj.Code;
                    dc.SaveChanges();
                    //var nah = new ElectionAssemblyConstituency
                    //{
                    //   ElectionAssemblyId = obj.ElectionAssemblyId,
                    //   StructureId = na.Id
                    //};
                    //dc.ElectionAssemblyConstituencies.Add(nah);
                    //dc.SaveChanges();
                    obj.Id = na.Id;
                    SavePollingScheme(dc, obj, phaseId, user);
                    tran.Commit();
                    return Task.FromResult("OK");
                }


                tran.Rollback();
                throw new Exception("Invalid constituency type");
            }

            if (obj.StructureType == StructureType.UnionCouncil)
            {
                if (obj.TownId == null || obj.DistrictId == null || obj.DivisionId == null || obj.ProvinceId == null)
                {
                    tran.Rollback();
                    throw new Exception("Please select respective administrative units");
                }

                var uc = (from aa in dc.Structures.OfType<UnionCouncil>()
                    where aa.Id == obj.Id
                    select aa).FirstOrDefault();
                if (uc == null) throw new Exception($"Union council record not found having id {obj.Id}");
                uc.Area = obj.Area;
                uc.AverageHouseholdIncome = obj.AverageHouseholdIncome;
                uc.Castes = obj.Castes;
                uc.Code = obj.Code;
                uc.EnglishTitle = obj.EnglishTitle;
                uc.FemaleVoters = obj.FemaleVoters;
                uc.GeneralTrivia = obj.GeneralTrivia;
                uc.ImportantAreas = obj.ImportantAreas;
                uc.Languages = obj.Languages;
                uc.LiteracyRate = obj.LiteracyRate;
                uc.MajorityIncomeSource = obj.MajorityIncomeSource;
                uc.MaleVoters = obj.MaleVoters;

                uc.ModifiedBy = user;
                uc.ModifiedDate = DateTime.Now;
                uc.Population = obj.Population;
                uc.Problems = obj.Problems;
                uc.RuralAreaPer = obj.RuralAreaPer;
                uc.TotalPollingStations = obj.TotalPollingStations;
                uc.TownId = obj.TownId.Value;
                uc.Trivia = obj.Trivia;
                uc.UrbanAreaPer = obj.UrbanAreaPer;
                uc.UrduTitle = obj.UrduTitle;
                uc.IsPostponed = obj.IsPostponed;
                uc.HasBilaMokablia = obj.HasBilaMokablia;
                dc.SaveChanges();
                obj.Id = uc.Id;
                SavePollingScheme(dc, obj, phaseId, user);
            }
            else if (obj.StructureType == StructureType.Ward)
            {
                if (obj.TownId == null || obj.DistrictId == null || obj.DivisionId == null ||
                    obj.ProvinceId == null || obj.UCId == null)
                {
                    tran.Rollback();
                    throw new Exception("Please select respective administrative units");
                }

                var wrd = (from aa in dc.Structures.OfType<Ward>()
                    where aa.Id == obj.Id
                    select aa).FirstOrDefault();
                if (wrd == null) throw new Exception($"Ward not found have id {obj.Id}");
                wrd.Area = obj.Area;
                wrd.AverageHouseholdIncome = obj.AverageHouseholdIncome;
                wrd.Castes = obj.Castes;
                wrd.Code = obj.Code;
                wrd.EnglishTitle = obj.EnglishTitle;
                wrd.FemaleVoters = obj.FemaleVoters;
                wrd.GeneralTrivia = obj.GeneralTrivia;
                wrd.ImportantAreas = obj.ImportantAreas;
                wrd.Languages = obj.Languages;
                wrd.LiteracyRate = obj.LiteracyRate;
                wrd.MajorityIncomeSource = obj.MajorityIncomeSource;
                wrd.MaleVoters = obj.MaleVoters;

                wrd.ModifiedBy = user;
                wrd.ModifiedDate = DateTime.Now;
                wrd.Population = obj.Population;
                wrd.Problems = obj.Problems;
                wrd.RuralAreaPer = obj.RuralAreaPer;
                wrd.TotalPollingStations = obj.TotalPollingStations;
                wrd.UnionCouncilId = obj.UCId.Value;
                wrd.Trivia = obj.Trivia;
                wrd.UrbanAreaPer = obj.UrbanAreaPer;
                wrd.UrduTitle = obj.UrduTitle;
                wrd.IsPostponed = obj.IsPostponed;
                wrd.HasBilaMokablia = obj.HasBilaMokablia;
                obj.Id = wrd.Id;
                SavePollingScheme(dc, obj, phaseId, user);
                dc.SaveChanges();
            }
            else if (obj.StructureType == StructureType.NA)
            {
                if (obj.ProvinceId == null || obj.DivisionId == null || obj.DivisionId == null)
                {
                    tran.Rollback();
                    throw new Exception("Please select respective administrative units");
                }

                var na = (from aa in dc.Structures.OfType<NationalAssemblyHalka>()
                    where aa.Id == obj.Id
                    select aa).FirstOrDefault();
                if (na == null) throw new Exception($"National assembly constituency not found having id {obj.Id}");
                na.Area = obj.Area;
                na.AverageHouseholdIncome = obj.AverageHouseholdIncome;
                na.Castes = obj.Castes;
                na.Code = obj.Code;
                na.EnglishTitle = obj.EnglishTitle;
                na.FemaleVoters = obj.FemaleVoters;
                na.GeneralTrivia = obj.GeneralTrivia;
                na.ImportantAreas = obj.ImportantAreas;
                na.Languages = obj.Languages;
                na.LiteracyRate = obj.LiteracyRate;
                na.MajorityIncomeSource = obj.MajorityIncomeSource;
                na.MaleVoters = obj.MaleVoters;

                na.ModifiedBy = user;
                na.ModifiedDate = DateTime.Now;
                na.Population = obj.Population;
                na.Problems = obj.Problems;
                na.RuralAreaPer = obj.RuralAreaPer;
                na.TotalPollingStations = obj.TotalPollingStations;
                if (obj.DistrictId != null) na.DistrictId = obj.DistrictId.Value;
                na.Trivia = obj.Trivia;
                na.UrbanAreaPer = obj.UrbanAreaPer;
                na.UrduTitle = obj.UrduTitle;
                na.IsPostponed = obj.IsPostponed;
                na.HasBilaMokablia = obj.HasBilaMokablia;
                na.ImportantPoliticalPersonalities = obj.ImportantPoliticalPersonalities;
                dc.SaveChanges();
                obj.Id = na.Id;
                SavePollingScheme(dc, obj, phaseId, user);
            }
            else if (obj.StructureType == StructureType.PA)
            {
                if (obj.ProvinceId == null || obj.DivisionId == null || obj.DivisionId == null)
                {
                    tran.Rollback();
                    throw new Exception("Please select respective administrative units");
                }

                var pa = (from aa in dc.Structures.OfType<ProvincialAssemblyHalka>()
                    where aa.Id == obj.Id
                    select aa).FirstOrDefault();
                if (pa == null) throw new Exception($"Provincial assembly constituency not found having id {obj.Id}");
                pa.Area = obj.Area;
                pa.AverageHouseholdIncome = obj.AverageHouseholdIncome;
                pa.Castes = obj.Castes;
                pa.Code = obj.Code;
                pa.EnglishTitle = obj.EnglishTitle;
                pa.FemaleVoters = obj.FemaleVoters;
                pa.GeneralTrivia = obj.GeneralTrivia;
                pa.ImportantAreas = obj.ImportantAreas;
                pa.Languages = obj.Languages;
                pa.LiteracyRate = obj.LiteracyRate;
                pa.MajorityIncomeSource = obj.MajorityIncomeSource;
                pa.MaleVoters = obj.MaleVoters;

                pa.ModifiedBy = user;
                pa.ModifiedDate = DateTime.Now;
                pa.Population = obj.Population;
                pa.Problems = obj.Problems;
                pa.RuralAreaPer = obj.RuralAreaPer;
                pa.TotalPollingStations = obj.TotalPollingStations;
                if (obj.DistrictId != null) pa.DistrictId = obj.DistrictId.Value;
                pa.Trivia = obj.Trivia;
                pa.UrbanAreaPer = obj.UrbanAreaPer;
                pa.UrduTitle = obj.UrduTitle;
                pa.IsPostponed = obj.IsPostponed;
                pa.HasBilaMokablia = obj.HasBilaMokablia;
                pa.ImportantPoliticalPersonalities = obj.ImportantPoliticalPersonalities;
                dc.SaveChanges();
                obj.Id = pa.Id;
                SavePollingScheme(dc, obj, phaseId, user);
            }
            else
            {
                tran.Rollback();
                throw new Exception("Invalid constituency type");
            }

            tran.Commit();
            return Task.FromResult("OK");
        }
        catch (Exception)
        {
            tran.Rollback();
            throw;
        }
    }

    private void SavePollingScheme(ApplicationDbContext dc, CreateStructureDTO obj, int phaseId, string userId)
    {
        var ps = (from a in dc.PollingSchemes
            where a.StructureId == obj.Id && a.PhaseId == phaseId
            select a).FirstOrDefault();
        if (ps == null)
        {
            ps = new PollingScheme
            {
                StructureId = obj.Id,
                PhaseId = phaseId,
                CreatedBy = userId,
                CreatedDate = DateTime.Now
            };
            dc.PollingSchemes.Add(ps);
            dc.SaveChanges();
        }

        ps.MaleVoters = obj.MaleVoters;

        ps.FemaleVoters = obj.FemaleVoters;
        ps.TotalPollingStations = obj.TotalPollingStations;
        ps.Languages = (obj.Languages ?? "").Trim();
        ps.Castes = (obj.Castes ?? "").Trim();
        ps.Trivia = (obj.Trivia ?? "").Trim();
        ps.GeneralTrivia = (obj.GeneralTrivia ?? "").Trim();
        ps.ImportantPoliticalPersonalities = (obj.ImportantPoliticalPersonalities ?? "").Trim();
        ps.Problems = (obj.Problems ?? "").Trim();
        ps.ImportantAreas = (obj.ImportantAreas ?? "").Trim();
        ps.RuralAreaPer = obj.RuralAreaPer;
        ps.UrbanAreaPer = obj.UrbanAreaPer;
        ps.Population = obj.Population;
        ps.MajorityIncomeSource = (obj.MajorityIncomeSource ?? "").Trim();
        ps.LiteracyRate = (obj.LiteracyRate ?? "").Trim();
        ps.AverageHouseholdIncome = (obj.AverageHouseholdIncome ?? "").Trim();
        ps.Area = (obj.Area ?? "").Trim();
        ps.ModifiedBy = userId;
        ps.ModifiedDate = DateTime.Now;
        ps.Profile = obj.Profile;
        dc.SaveChanges();
    }

    public Task<string> GeneratePSs(int constituencyId, int totalPollingStations, string userId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.PollingStations
            where aa.ConstituencyId == constituencyId
            select aa).Any();
        if (q) return Task.FromResult("Polling Station Already Exists");

        var cd = DateTime.Now;

        for (var i = 0; i < totalPollingStations; i++)
        {
            var ps = new PollingStation
            {
                ConstituencyId = constituencyId,
                MaleVoters = 500,
                FemaleVoters = 500,
                CreatedBy = userId,
                CreatedDate = cd,
                EnglishTitle = $"PS {i + 1}",
                Number = $"{i + 1}",
                UrduTitle = $"پی ایس {i + 1}"
            };
            dc.PollingStations.Add(ps);
            dc.SaveChanges();
        }

        var cc = dc.Structures.Find(constituencyId);
        if (cc != null)
        {
            cc.ModifiedBy = userId;
            cc.ModifiedDate = cd;
            cc.TotalPollingStations = totalPollingStations;
            dc.SaveChanges();
        }

        return Task.FromResult("OK");
    }

    public Task<string> SavePreviousConstData(PrevConstituencyDTO previousConst)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.StructureMappings
            where aa.StructureId == previousConst.ParentStructureId
                  && aa.PreviousStructureId == previousConst.PreviousConstituencyId
            select aa).FirstOrDefault();
        if (q == null)
        {
            if (previousConst.PreviousConstituencyId != null)
                q = new StructureMapping
                {
                    Description = previousConst.Description,
                    PreviousStructureId = (int)previousConst.PreviousConstituencyId,
                    StructureId = previousConst.ParentStructureId
                };
            if (q != null) dc.StructureMappings.Add(q);
            dc.SaveChanges();
        }
        else
        {
            q.Description = previousConst.Description;
            dc.SaveChanges();
        }

        return Task.FromResult("OK");
    }

    public Task<List<PrevConstituencyDTO>> GetAssignedPreviousConstituencies(int id)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.StructureMappings
                //join bb in dc.Structures on aa.PreviousStructureId equals bb.Id
                where aa.StructureId == id
                select new PrevConstituencyDTO
                {
                    Description = aa.Description,
                    Election = aa.PreviousStructure.EnglishTitle,
                    EnglishTitle = $"{aa.PreviousStructure.Code} - {aa.PreviousStructure.EnglishTitle}",
                    ParentStructureId = aa.StructureId,
                    PreviousConstituencyId = aa.PreviousStructureId,
                    UrduTitle = $"{aa.PreviousStructure.Code} - {aa.PreviousStructure.UrduTitle}"
                }
            ).ToList();
        return Task.FromResult(q);
    }

    public Task<string> RemovePreviousConstituency(int id, int prevConId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.StructureMappings
            where aa.StructureId == id &&
                  aa.PreviousStructureId == prevConId
            select aa).FirstOrDefault();
        if (q != null)
        {
            dc.StructureMappings.Remove(q);
            dc.SaveChanges();
            return Task.FromResult("OK");
        }

        return Task.FromResult("Not Found or Already deleted");
    }

    public Task<string> GetPreviousElection(int id)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.Structures
            where aa.Id == id
            select aa.Election.ParentElection.EnglishTitle).FirstOrDefault();

        return Task.FromResult(q);
    }

    public Task<List<GeneralItemDTO>> GetPreviousConstituencies(int electionId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var previousElectionId = (from aa in dc.Elections
                where aa.Id == electionId
                select aa.ParentElectionId
            ).FirstOrDefault();

        if (previousElectionId != null)
        {
            var q = (from aa in dc.Structures
                where aa.ElectionId == previousElectionId &&
                      aa.AssemblyId != null
                orderby aa.Assembly.EnglishTitle, aa.Code.Replace(" ", string.Empty).Trim()
                select new GeneralItemDTO
                {
                    EnglishTitle =
                        $"{aa.Code.Replace(" ", string.Empty).Trim()} - {aa.EnglishTitle} ({aa.Assembly.EnglishTitle})",
                    Id = aa.Id
                }).ToList();
            return Task.FromResult(q);
        }

        return Task.FromResult(new List<GeneralItemDTO>());
    }

    public Task<string> DeleteStructure(int structureId, int assemblyId)
    {
        using var dc = _contextFactory.CreateDbContext();
        using var tran = dc.Database.BeginTransaction();
        try
        {
            var q = (from aa in dc.Structures
                where aa.Id == structureId &&
                      aa.AssemblyId == assemblyId
                select aa).FirstOrDefault();
            if (q != null)
            {
                dc.Structures.Remove(q);
                dc.SaveChanges();
                tran.Commit();
            }

            return Task.FromResult("OK");
        }
        catch (Exception)
        {
            tran.Rollback();

            throw;
        }
    }

    public Task<List<GeneralItemDTO>> GetAllElections()
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.Elections
            orderby aa.EnglishTitle
            select new GeneralItemDTO
            {
                Id = aa.Id,
                EnglishTitle = aa.EnglishTitle
            }).ToList();
        return Task.FromResult(q);
    }

    public Task<ElectionAssemblyType?> GetAssemblyType(int electionAssemblyId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.ElectionAssemblies
            where aa.Id == electionAssemblyId
            select aa.ElectionAssemblyType).FirstOrDefault();

        return Task.FromResult(q);
    }

    public Task<List<ConstituencyDTO>> GetAvailableConstituencies(int electionAssemblyId, StructureType typeId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var electionId = dc.ElectionAssemblies.Find(electionAssemblyId).ElectionId;
        if (typeId == StructureType.UnionCouncil)
        {
            var q1 = (from s in dc.Structures.OfType<UnionCouncil>()
                orderby s.Town.District.Division.Province.EnglishTitle,
                    s.Town.District.Division.EnglishTitle,
                    s.Town.District.EnglishTitle,
                    s.Town.EnglishTitle,
                    s.EnglishTitle
                where s.ElectionId == electionId &&
                      s.AssemblyId == null
                select new ConstituencyDTO
                {
                    Id = s.Id,
                    EnglishTitle = $"{s.EnglishTitle}<br><span class='urdu-sm'>{s.UrduTitle}</span>",
                    Code = s.Code,
                    Town = $"{s.Town.EnglishTitle}<br><span class='urdu-sm'>{s.Town.UrduTitle}</span>",
                    District =
                        $"{s.Town.District.EnglishTitle}<br><span class='urdu-sm'>{s.Town.District.UrduTitle}</span>",
                    Division =
                        $"{s.Town.District.Division.EnglishTitle}<br><span class='urdu-sm'>{s.Town.District.Division.UrduTitle}</span>",
                    Province =
                        $"{s.Town.District.Division.Province.EnglishTitle}<br><span class='urdu-sm'>{s.Town.District.Division.Province.EnglishTitle}</span>",
                    Type = "UC"
                }).ToList();
            return Task.FromResult(q1);
        }

        if (typeId == StructureType.Ward)
        {
            var q1 = (from s in dc.Structures.OfType<Ward>()
                orderby s.Code
                where s.ElectionId == electionId &&
                      s.AssemblyId == null
                select new ConstituencyDTO
                {
                    Id = s.Id,
                    EnglishTitle = $"{s.EnglishTitle}<br><span class='urdu-sm'>{s.UrduTitle}</span>",
                    Code = s.Code,
                    Town =
                        $"{s.UnionCouncil.Town.EnglishTitle}<br><span class='urdu-sm'>{s.UnionCouncil.Town.UrduTitle}</span>",
                    District =
                        $"{s.UnionCouncil.Town.District.EnglishTitle}<br><span class='urdu-sm'>{s.UnionCouncil.Town.District.UrduTitle}</span>",
                    Division =
                        $"{s.UnionCouncil.Town.District.Division.EnglishTitle}<br><span class='urdu-sm'>{s.UnionCouncil.Town.District.Division.UrduTitle}</span>",
                    Province =
                        $"{s.UnionCouncil.Town.District.Division.Province.EnglishTitle}<br><span class='urdu-sm'>{s.UnionCouncil.Town.District.Division.Province.EnglishTitle}</span>",
                    UC = $"{s.UnionCouncil.EnglishTitle}<br><span class='urdu-sm'>{s.UnionCouncil.UrduTitle}</span>",
                    Type = "Ward"
                }).ToList();

            return Task.FromResult(q1);
        }

        if (typeId == StructureType.PA)
        {
            var q1 = (from s in dc.Structures.OfType<ProvincialAssemblyHalka>()
                where s.AssemblyId == null &&
                      s.ElectionId == electionId
                select new ConstituencyDTO
                {
                    Id = s.Id,
                    EnglishTitle = $"{s.EnglishTitle}<br><span class='urdu-sm'>{s.UrduTitle}</span>",
                    Code = s.Code,
                    Town = string.Empty,
                    District = $"{s.District.EnglishTitle}<br><span class='urdu-sm'>{s.District.UrduTitle}</span>",
                    Division =
                        $"{s.District.Division.EnglishTitle}<br><span class='urdu-sm'>{s.District.Division.UrduTitle}</span>",
                    Province =
                        $"{s.District.Division.Province.EnglishTitle}<br><span class='urdu-sm'>{s.District.Division.Province.EnglishTitle}</span>",
                    UC = string.Empty,
                    Type = "NA"
                }).ToList();
            return Task.FromResult(q1);
        }

        if (typeId == StructureType.NA)
        {
            var q1 = (from s in dc.Structures.OfType<NationalAssemblyHalka>()
                where s.AssemblyId == null &&
                      s.ElectionId == electionId
                select new ConstituencyDTO
                {
                    Id = s.Id,
                    EnglishTitle = $"{s.EnglishTitle}<br><span class='urdu-sm'>{s.UrduTitle}</span>",
                    Code = s.Code,
                    Town = string.Empty,
                    District = $"{s.District.EnglishTitle}<br><span class='urdu-sm'>{s.District.UrduTitle}</span>",
                    Division =
                        $"{s.District.Division.EnglishTitle}<br><span class='urdu-sm'>{s.District.Division.UrduTitle}</span>",
                    Province =
                        $"{s.District.Division.Province.EnglishTitle}<br><span class='urdu-sm'>{s.District.Division.Province.EnglishTitle}</span>",
                    UC = string.Empty,
                    Type = "PA"
                }).ToList();
            return Task.FromResult(q1);
        }

        return null;
    }

    public Task<List<GeneralItemDTO>> GetCastesList()
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.Castes
            orderby aa.EnglishTitle
            select new GeneralItemDTO
            {
                Id = aa.Id,
                EnglishTitle = aa.EnglishTitle,
                UrduTitle = aa.UrduTitle
            }).ToList();
        return Task.FromResult(q);
    }

    public Task<ConstituencyDTO> GetConstituency(int structureId, int assemblyId, int phaseId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.Structures
            where aa.Id == structureId //&&
            //aa.AssemblyId == assemblyId
            select new ConstituencyDTO
            {
                Id = aa.Id,
                AssemblyId = aa.AssemblyId ?? 0,
                Code = aa.Code,
                EnglishTitle = aa.EnglishTitle,
                //FemaleVoters = aa.FemaleVoters,
                //ImportantAreas = aa.ImportantAreas,
                //MaleVoters = aa.MaleVoters,
                //Problems = aa.Problems,
                //ImportantPoliticalPersonalities = aa.ImportantPoliticalPersonalities,
                //RuralAreaPer = aa.RuralAreaPer,
                //TotalPollingStations = aa.TotalPollingStations,
                //Trivia = aa.Trivia,
                //UrbanAreaPer = aa.UrbanAreaPer,
                Assembly = aa.Assembly.EnglishTitle,
                UrduTitle = aa.UrduTitle,
                //Population = aa.Population,
                //Languages = aa.Languages,
                //Castes = aa.Castes,
                //GeneralTrivia = aa.GeneralTrivia,
                //Area = aa.Area,
                //MajorityIncomeSource = aa.MajorityIncomeSource,
                //LiteracyRate = aa.LiteracyRate,
                //AverageHouseholdIncome = aa.AverageHouseholdIncome,
                IsPostponed = aa.IsPostponed,
                HasBilaMokablia = aa.HasBilaMokablia,
                PrevConstituencies = (from bb in dc.StructureMappings
                    where bb.StructureId == aa.Id
                    select new PrevConstituencyDTO
                    {
                        Description = bb.Description,
                        EnglishTitle = bb.PreviousStructure.Code + " " + bb.PreviousStructure.EnglishTitle,
                        UrduTitle = bb.PreviousStructure.Code + " " + bb.PreviousStructure.UrduTitle,
                        ParentStructureId = bb.PreviousStructureId,
                        Election = bb.PreviousStructure.Election.EnglishTitle,
                        PreviousConstituencyId = bb.PreviousStructureId
                    }).ToList(),
                IsPollingStationExist = (from bb in dc.PollingStations
                    where bb.ConstituencyId == structureId
                    select bb).Any(),
                PSMaleVoters = (from bb in dc.PollingStations
                    where bb.ConstituencyId == structureId
                    select bb.MaleVoters ?? 0).Sum(),
                PSFemaleVoters = (from bb in dc.PollingStations
                    where bb.ConstituencyId == structureId
                    select bb.FemaleVoters ?? 0).Sum(),
                PSCount = (from bb in dc.PollingStations
                    where bb.ConstituencyId == structureId
                    select bb).Count()
            }).FirstOrDefault();
        if (q == null) return null;
        q.Namanigars = GetConstituencyNamanigars(q.Id).Result;

        var ps = (from a in dc.PollingSchemes
            where a.StructureId == structureId && a.PhaseId == phaseId
            select a).FirstOrDefault();
        if (ps != null)
        {
            q.MaleVoters = ps.MaleVoters;
            q.YouthVoters = ps.YouthVoters;
            q.FemaleVoters = ps.FemaleVoters;
            q.TotalPollingStations = ps.TotalPollingStations;
            q.Languages = (ps.Languages ?? "").Trim();
            q.Castes = (ps.Castes ?? "").Trim();
            q.Trivia = (ps.Trivia ?? "").Trim();
            q.GeneralTrivia = (ps.GeneralTrivia ?? "").Trim();
            q.ImportantPoliticalPersonalities = (ps.ImportantPoliticalPersonalities ?? "").Trim();
            q.Problems = (ps.Problems ?? "").Trim();
            q.ImportantAreas = (ps.ImportantAreas ?? "").Trim();
            q.RuralAreaPer = ps.RuralAreaPer;
            q.UrbanAreaPer = ps.UrbanAreaPer;
            q.Population = ps.Population;
            q.MajorityIncomeSource = (ps.MajorityIncomeSource ?? "").Trim();
            q.LiteracyRate = (ps.LiteracyRate ?? "").Trim();
            q.AverageHouseholdIncome = (ps.AverageHouseholdIncome ?? "").Trim();
            q.Area = (ps.Area ?? "").Trim();
            q.Profile = ps.Profile ?? "";
        }

        return Task.FromResult(q);
    }

    public Task<ConstInfoDTO> GetConstituencyInfo(int structureId, int phaseId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var cc = dc.Structures.Find(structureId);
        var type = string.Empty;
        if (cc is UnionCouncil) type = "UC";
        if (cc is Ward) type = "Ward";
        if (cc is NationalAssemblyHalka) type = "NA";
        if (cc is ProvincialAssemblyHalka) type = "PA";

        if (type == "UC")
        {
            var strInfo = (from aa in dc.Structures.OfType<UnionCouncil>()
                where aa.Id == structureId &&
                      aa.AssemblyId != null
                select new ConstInfoDTO
                {
                    Id = aa.Id,
                    AssemblyEnglish = aa.Assembly.EnglishTitle,
                    AssemblyUrdu = aa.Assembly.UrduTitle,

                    //AverageHouseHoldIncome = aa.AverageHouseholdIncome ?? string.Empty,
                    //Castes = aa.Castes,
                    //Economy = aa.Trivia ?? string.Empty,
                    //FemaleVoters = aa.FemaleVoters ?? 0,
                    //GeneralTrivia = aa.GeneralTrivia ?? "",
                    //GeneralTrivia = aa.GeneralTrivia ?? string.Empty,
                    //ImportantAreas = aa.ImportantAreas ?? "",,
                    //ImportantAreas = aa.ImportantAreas ?? string.Empty,
                    //ImportantPoliticalParties = aa.ImportantPoliticalPersonalities ?? string.Empty,
                    //LanguageSpoken = aa.Languages ?? "",
                    //LanguageSpoken = aa.Languages ?? string.Empty,
                    //ListeracyRate = aa.LiteracyRate ?? "",
                    //MajorIncomeSource = aa.MajorityIncomeSource ?? "",
                    //MajorIncomeSource = aa.MajorityIncomeSource ?? string.Empty,
                    //MaleVoters = aa.MaleVoters ?? 0,
                    //Population = aa.Population ?? 0,
                    //Problems = aa.Problems ?? string.Empty,
                    //RuralArea = aa.RuralAreaPer ,
                    //RuralArea = aa.RuralAreaPer,
                    //TotalPollingStations = aa.TotalPollingStations ?? 0,
                    //UrbanArea = aa.UrbanAreaPer,
                    //UrbanArea = aa.UrbanAreaPer,
                    //TotalArea = aa.Area ?? string.Empty,


                    UrduTitle = aa.UrduTitle,
                    EnglishTitle = aa.EnglishTitle,
                    Code = aa.Code,
                    TownEnglish = aa.Town.EnglishTitle,
                    DistrictEnglish = aa.Town.District.EnglishTitle,
                    DistrictUrdu = aa.Town.District.UrduTitle,
                    DivisionEnglish = aa.Town.District.Division.EnglishTitle,
                    DivisionUrdu = aa.Town.District.Division.UrduTitle,
                    LiteracyRate = aa.LiteracyRate ?? string.Empty,
                    ProvinceEnglish = aa.Town.District.Division.Province.EnglishTitle,
                    ProvinceUrdu = aa.Town.District.Division.Province.UrduTitle,
                    RegionEnglish = aa.Town.District.Region.EnglishTitle,
                    RegionUrdu = aa.Town.District.Region.UrduTitle,
                    TownUrdu = aa.Town.UrduTitle,
                    ElectionId = aa.ElectionId,
                    AssemblyId = aa.AssemblyId,
                    ElectionEnglish = aa.Election.EnglishTitle,
                    ElectionUrdu = aa.Election.UrduTitle
                }).FirstOrDefault();

            SetPollingScheme(phaseId, dc, strInfo);
            if (strInfo == null)
                return null;

            //strInfo.Phases = new List<ConstElectionPhaseDTO>();
            //strInfo.Phases = (from aa in dc.ElectionPhases
            //						where aa.ElectionId == strInfo.ElectionId
            //						select new ConstElectionPhaseDTO
            //						{
            //							Date = aa.StartDate.ToString("d MMM, yyyy"),
            //							Id = aa.Id,
            //							Title = aa.Title,
            //							Seats = new List<ConstSeatsDTO>(),
            //							SubSeats = new List<ConstSeatsDTO>()
            //						}).ToList();

            strInfo.Phases = (from a in dc.Nominations
                where a.StructureId == structureId
                select new ConstElectionPhaseDTO
                {
                    Date = a.ElectionPhase.StartDate.ToString("d MMM, yyyy"),
                    Id = a.ElectionPhaseId,
                    Title = a.ElectionPhase.Title,
                    Seats = new List<ConstSeatsDTO>(),
                    SubSeats = new List<ConstSeatsDTO>()
                }).Distinct().ToList();
            //var seatTypes =

            foreach (var ph in strInfo.Phases)
            {
                ph.Seats = (from aa in dc.ElectionAssemblySeats
                    where aa.ElectionAssemblyId == strInfo.AssemblyId
                          //&& aa.SeatType.EnglishTitle != "General Member"
                          && aa.ConstituencyType == StructureType.UnionCouncil
                    select new ConstSeatsDTO
                    {
                        Nominations = new List<ConstNominationDTO>(),
                        SeatTypeEnglish = aa.SeatType.EnglishTitle,
                        SeatTypeUrdu = aa.SeatType.UrduTitle
                    }).ToList();

                foreach (var st in ph.Seats)
                {
                    st.Nominations = (from aa in dc.Nominations
                        //orderby aa.Votes descending, aa.Weight descending
                        orderby aa.Weight == 0 ? 100000 : aa.Weight, aa.Candidate.EnglishName
                        where aa.ElectionPhaseId == ph.Id &&
                              aa.ElectionAssemblyId == strInfo.AssemblyId &&
                              aa.SeatType.EnglishTitle == st.SeatTypeEnglish &&
                              aa.ElectionAssemblyId == strInfo.AssemblyId &&
                              aa.StructureId == structureId
                        select new ConstNominationDTO
                        {
                            CandidateId = aa.CandidteId,
                            NameEnglish = aa.Candidate.EnglishName,
                            NameUrdu = aa.Candidate.UrduName,
                            PartyEnglish = aa.Party.EnglishTitle,
                            PartyUrdu = aa.Party.UrduTitle,
                            PartyId = aa.PartyId,
                            SymbolEnglish = aa.Symbol.EnglishTitle,
                            SymbolId = aa.SymbolId,
                            SymbolUrdu = aa.Symbol.UrduTitle,
                            VoteObtain = aa.Votes ?? 0,
                            Weight = aa.Weight
                        }).ToList();
                    SetMissingPicsURL(st);
                }

                var wards = (from wrd in dc.Structures.OfType<Ward>()
                    where wrd.UnionCouncilId == structureId &&
                          wrd.AssemblyId == strInfo.AssemblyId
                    select wrd).ToList();

                var sts = new List<GeneralItemDTO>();
                ph.SubSeats = new List<ConstSeatsDTO>();
                //foreach (var wrd in wards)
                //{
                //	var kk = (from aa in dc.Nominations
                //				 where aa.StructureId == wrd.Id
                //				 select new GeneralItemDTO
                //				 {
                //					 EnglishTitle = aa.SeatType.EnglishTitle,
                //					 UrduTitle = aa.SeatType.UrduTitle,
                //					 Id = aa.SeatTypeId
                //				 }).Distinct().ToList();

                //	// for each seat type in this structure
                //	foreach (var s in kk)
                //	{
                //		// if seat type does not exit then add it list
                //		var aa = (from oo in sts
                //					 where oo.EnglishTitle == s.EnglishTitle
                //					 select oo).Any();
                //		// if seat type does not exst
                //		if (!aa)
                //		{
                //			sts.Add(new GeneralItemDTO { EnglishTitle = s.EnglishTitle, Id = s.Id, Province = "", UrduTitle = s.UrduTitle });
                //		}
                //	}
                //}

                sts = (from aa in dc.ElectionAssemblySeats
                    where aa.ElectionAssemblyId == strInfo.AssemblyId &&
                          aa.ConstituencyType != StructureType.UnionCouncil
                    select new GeneralItemDTO
                    {
                        EnglishTitle = aa.SeatType.EnglishTitle,
                        UrduTitle = aa.SeatType.UrduTitle,
                        Id = aa.SeatTypeId,
                        Province = string.Empty
                    }).ToList();

                ph.SubSeats = new List<ConstSeatsDTO>();
                // Loop throuth all wards of this union councils
                foreach (var vrd in wards)
                    // Loop through all associated seat type

                foreach (var uu in sts)
                {
                    var wst = new ConstSeatsDTO
                    {
                        SeatTypeEnglish = vrd.Code + " - " + vrd.EnglishTitle,
                        SeatTypeUrdu = vrd.Code + " - " + vrd.UrduTitle,
                        Nominations = new List<ConstNominationDTO>()
                    };

                    wst.Nominations = (from nmm in dc.Nominations
                            orderby nmm.Votes descending, nmm.Weight descending
                            where nmm.StructureId == vrd.Id &&
                                  nmm.SeatTypeId == uu.Id &&
                                  nmm.ElectionPhaseId == ph.Id
                            select new ConstNominationDTO
                            {
                                CandidateId = nmm.CandidteId,
                                NameEnglish = nmm.Candidate.EnglishName,
                                NameUrdu = nmm.Candidate.UrduName,
                                PartyEnglish = nmm.Party.EnglishTitle,
                                PartyId = nmm.PartyId,
                                PartyUrdu = nmm.Party.UrduTitle,
                                SymbolEnglish = nmm.Symbol.EnglishTitle,
                                SymbolId = nmm.SymbolId,
                                SymbolUrdu = nmm.Symbol.UrduTitle,
                                VoteObtain = nmm.Votes ?? 0,
                                Weight = nmm.Weight
                            }
                        ).ToList();
                    ph.SubSeats.Add(wst);
                }
            }

            return Task.FromResult(strInfo);
        }

        if (type == "Ward")
        {
            var strInfo = (from aa in dc.Structures.OfType<Ward>()
                where aa.Id == structureId &&
                      aa.AssemblyId != null
                select new ConstInfoDTO
                {
                    Id = aa.Id,
                    AssemblyEnglish = aa.Assembly.EnglishTitle,
                    AssemblyUrdu = aa.Assembly.UrduTitle,
                    AverageHouseHoldIncome = aa.AverageHouseholdIncome ?? string.Empty,
                    UrduTitle = aa.UrduTitle,
                    EnglishTitle = aa.EnglishTitle,
                    Castes = aa.Castes,
                    Code = aa.Code,
                    TownEnglish = aa.UnionCouncil.Town.EnglishTitle,
                    DistrictEnglish = aa.UnionCouncil.Town.District.EnglishTitle,
                    DistrictUrdu = aa.UnionCouncil.Town.District.UrduTitle,
                    DivisionEnglish = aa.UnionCouncil.Town.District.Division.EnglishTitle,
                    DivisionUrdu = aa.UnionCouncil.Town.District.Division.UrduTitle,
                    Economy = aa.Trivia ?? string.Empty,
                    FemaleVoters = aa.FemaleVoters ?? 0,
                    GeneralTrivia = aa.GeneralTrivia ?? string.Empty,
                    ImportantAreas = aa.ImportantAreas ?? string.Empty,
                    ImportantPoliticalParties = aa.ImportantPoliticalPersonalities,
                    LanguageSpoken = aa.Languages ?? string.Empty,
                    LiteracyRate = aa.LiteracyRate ?? string.Empty,
                    MajorIncomeSource = aa.MajorityIncomeSource ?? string.Empty,
                    MaleVoters = aa.MaleVoters ?? 0,

                    Population = aa.Population ?? 0,
                    Problems = aa.Problems ?? string.Empty,
                    ProvinceEnglish = aa.UnionCouncil.Town.District.Division.Province.EnglishTitle,
                    ProvinceUrdu = aa.UnionCouncil.Town.District.Division.Province.UrduTitle,
                    RegionEnglish = aa.UnionCouncil.Town.District.Region.EnglishTitle,
                    RegionUrdu = aa.UnionCouncil.Town.District.Region.UrduTitle,
                    RuralArea = aa.RuralAreaPer,
                    TotalPollingStations = aa.TotalPollingStations ?? 0,
                    TownUrdu = aa.UnionCouncil.Town.UrduTitle,
                    UrbanArea = aa.UrbanAreaPer,
                    TotalArea = aa.Area ?? string.Empty,
                    ElectionId = aa.ElectionId,
                    AssemblyId = aa.AssemblyId,
                    ElectionEnglish = aa.Election.EnglishTitle,
                    ElectionUrdu = aa.Election.UrduTitle,
                    UCEnglish = aa.UnionCouncil.EnglishTitle,
                    UCUrdu = aa.UnionCouncil.UrduTitle
                }).FirstOrDefault();
            SetPollingScheme(phaseId, dc, strInfo);
            if (strInfo == null)
                return null;

            //strInfo.Phases = new List<ConstElectionPhaseDTO>();
            strInfo.Phases = (from a in dc.Nominations
                where a.StructureId == structureId
                select new ConstElectionPhaseDTO
                {
                    Date = a.ElectionPhase.StartDate.ToString("d MMM, yyyy"),
                    Id = a.ElectionPhaseId,
                    Title = a.ElectionPhase.Title,
                    Seats = new List<ConstSeatsDTO>(),
                    SubSeats = new List<ConstSeatsDTO>()
                }).Distinct().ToList();

            //var seatTypes =

            foreach (var ph in strInfo.Phases)
            {
                ph.Seats = (from aa in dc.ElectionAssemblySeats
                    where aa.ElectionAssemblyId == strInfo.AssemblyId
                          && aa.ConstituencyType == StructureType.Ward
                    select new ConstSeatsDTO
                    {
                        Nominations = new List<ConstNominationDTO>(),
                        SeatTypeEnglish = aa.SeatType.EnglishTitle,
                        SeatTypeUrdu = aa.SeatType.UrduTitle
                    }).ToList();

                foreach (var st in ph.Seats)
                {
                    st.Nominations = (from aa in dc.Nominations
                        orderby aa.Votes descending, aa.Weight descending
                        where aa.ElectionPhaseId == ph.Id &&
                              aa.ElectionAssemblyId == strInfo.AssemblyId &&
                              aa.SeatType.EnglishTitle == st.SeatTypeEnglish &&
                              aa.ElectionAssemblyId == strInfo.AssemblyId &&
                              aa.StructureId == structureId
                        select new ConstNominationDTO
                        {
                            CandidateId = aa.CandidteId,
                            NameEnglish = aa.Candidate.EnglishName,
                            NameUrdu = aa.Candidate.UrduName,
                            PartyEnglish = aa.Party.EnglishTitle,
                            PartyUrdu = aa.Party.UrduTitle,
                            PartyId = aa.PartyId,
                            SymbolEnglish = aa.Symbol.EnglishTitle,
                            SymbolId = aa.SymbolId,
                            SymbolUrdu = aa.Symbol.UrduTitle,
                            VoteObtain = aa.Votes ?? 0,
                            Weight = aa.Weight
                        }).ToList();
                    SetMissingPicsURL(st);
                }
            }

            return Task.FromResult(strInfo);
        }

        if (type == "NA")
        {
            var strInfo = (from aa in dc.Structures.OfType<NationalAssemblyHalka>()
                where aa.Id == structureId &&
                      aa.AssemblyId != null
                select new ConstInfoDTO
                {
                    Id = aa.Id,
                    AssemblyEnglish = aa.Assembly.EnglishTitle,
                    AssemblyUrdu = aa.Assembly.UrduTitle,
                    AverageHouseHoldIncome = aa.AverageHouseholdIncome ?? string.Empty,
                    UrduTitle = aa.UrduTitle,
                    EnglishTitle = aa.EnglishTitle,
                    Castes = aa.Castes,
                    Code = aa.Code,
                    TownEnglish = string.Empty,
                    DistrictEnglish = aa.District.EnglishTitle,
                    DistrictUrdu = aa.District.UrduTitle,
                    DivisionEnglish = aa.District.Division.EnglishTitle,
                    DivisionUrdu = aa.District.Division.UrduTitle,
                    Economy = aa.Trivia ?? string.Empty,
                    FemaleVoters = aa.FemaleVoters ?? 0,
                    GeneralTrivia = aa.GeneralTrivia ?? string.Empty,
                    ImportantAreas = aa.ImportantAreas ?? string.Empty,
                    ImportantPoliticalParties = aa.ImportantPoliticalPersonalities,
                    LanguageSpoken = aa.Languages ?? string.Empty,
                    LiteracyRate = aa.LiteracyRate ?? string.Empty,
                    MajorIncomeSource = aa.MajorityIncomeSource ?? string.Empty,
                    MaleVoters = aa.MaleVoters ?? 0,
                    Population = aa.Population ?? 0,
                    Problems = aa.Problems ?? string.Empty,
                    ProvinceEnglish = aa.District.Division.Province.EnglishTitle,
                    ProvinceUrdu = aa.District.Division.Province.UrduTitle,
                    RegionEnglish = aa.District.Region.EnglishTitle,
                    RegionUrdu = aa.District.Region.UrduTitle,
                    RuralArea = aa.RuralAreaPer,
                    TotalPollingStations = aa.TotalPollingStations ?? 0,
                    TownUrdu = string.Empty,
                    UrbanArea = aa.UrbanAreaPer,
                    TotalArea = aa.Area ?? string.Empty,
                    ElectionId = aa.ElectionId,
                    AssemblyId = aa.AssemblyId,
                    ElectionEnglish = aa.Election.EnglishTitle,
                    ElectionUrdu = aa.Election.UrduTitle,
                    UCEnglish = string.Empty,
                    UCUrdu = string.Empty
                }).FirstOrDefault();
            SetPollingScheme(phaseId, dc, strInfo);
            if (strInfo == null)
                return null;

            //strInfo.Phases = new List<ConstElectionPhaseDTO>();
            strInfo.Phases = (from a in dc.Nominations
                where a.StructureId == structureId
                select new ConstElectionPhaseDTO
                {
                    Date = a.ElectionPhase.StartDate.ToString("d MMM, yyyy"),
                    Id = a.ElectionPhaseId,
                    Title = a.ElectionPhase.Title,
                    Seats = new List<ConstSeatsDTO>(),
                    SubSeats = new List<ConstSeatsDTO>()
                }).Distinct().ToList();

            //var seatTypes =

            foreach (var ph in strInfo.Phases)
            {
                ph.Seats = (from aa in dc.ElectionAssemblySeats
                    where aa.ElectionAssemblyId == strInfo.AssemblyId &&
                          aa.ConstituencyType == StructureType.NA
                    select new ConstSeatsDTO
                    {
                        Nominations = new List<ConstNominationDTO>(),
                        SeatTypeEnglish = aa.SeatType.EnglishTitle,
                        SeatTypeUrdu = aa.SeatType.UrduTitle
                    }).ToList();

                foreach (var st in ph.Seats)
                {
                    st.Nominations = (from aa in dc.Nominations
                        orderby aa.Votes descending, aa.Weight descending
                        where aa.ElectionPhaseId == ph.Id &&
                              aa.ElectionAssemblyId == strInfo.AssemblyId &&
                              aa.SeatType.EnglishTitle == st.SeatTypeEnglish &&
                              aa.ElectionAssemblyId == strInfo.AssemblyId &&
                              aa.StructureId == structureId
                        select new ConstNominationDTO
                        {
                            CandidateId = aa.CandidteId,
                            NameEnglish = aa.Candidate.EnglishName,
                            NameUrdu = aa.Candidate.UrduName,
                            PartyEnglish = aa.Party.EnglishTitle,
                            PartyUrdu = aa.Party.UrduTitle,
                            PartyId = aa.PartyId,
                            SymbolEnglish = aa.Symbol.EnglishTitle,
                            SymbolId = aa.SymbolId,
                            SymbolUrdu = aa.Symbol.UrduTitle,
                            VoteObtain = aa.Votes ?? 0,
                            Weight = aa.Weight
                        }).ToList();
                    SetMissingPicsURL(st);
                }
            }

            var prvcon = (from a in dc.StructureMappings
                where a.StructureId == structureId
                select new
                {
                    a.StructureId, a.PreviousStructureId,
                    PreviousCon = a.PreviousStructure.Election.EnglishTitle + ": " + a.PreviousStructure.Code + " - " +
                                  a.PreviousStructure.EnglishTitle
                }).FirstOrDefault();
            if (prvcon != null)
            {
                strInfo.PreviousConstId = prvcon.PreviousStructureId;
                strInfo.PreviousConst = prvcon.PreviousCon;
            }


            return Task.FromResult(strInfo);
        }

        if (type == "PA")
        {
            var strInfo = (from aa in dc.Structures.OfType<ProvincialAssemblyHalka>()
                where aa.Id == structureId &&
                      aa.AssemblyId != null
                select new ConstInfoDTO
                {
                    Id = aa.Id,
                    AssemblyEnglish = aa.Assembly.EnglishTitle,
                    AssemblyUrdu = aa.Assembly.UrduTitle,
                    AverageHouseHoldIncome = aa.AverageHouseholdIncome ?? string.Empty,
                    UrduTitle = aa.UrduTitle,
                    EnglishTitle = aa.EnglishTitle,
                    Castes = aa.Castes,
                    Code = aa.Code,
                    TownEnglish = string.Empty,
                    DistrictEnglish = aa.District.EnglishTitle,
                    DistrictUrdu = aa.District.UrduTitle,
                    DivisionEnglish = aa.District.Division.EnglishTitle,
                    DivisionUrdu = aa.District.Division.UrduTitle,
                    Economy = aa.Trivia ?? string.Empty,
                    FemaleVoters = aa.FemaleVoters ?? 0,
                    GeneralTrivia = aa.GeneralTrivia ?? string.Empty,
                    ImportantAreas = aa.ImportantAreas ?? string.Empty,
                    ImportantPoliticalParties = aa.ImportantPoliticalPersonalities,
                    LanguageSpoken = aa.Languages ?? string.Empty,
                    LiteracyRate = aa.LiteracyRate ?? string.Empty,
                    MajorIncomeSource = aa.MajorityIncomeSource ?? string.Empty,
                    MaleVoters = aa.MaleVoters ?? 0,
                    Population = aa.Population ?? 0,
                    Problems = aa.Problems ?? string.Empty,
                    ProvinceEnglish = aa.District.Division.Province.EnglishTitle,
                    ProvinceUrdu = aa.District.Division.Province.UrduTitle,
                    RegionEnglish = aa.District.Region.EnglishTitle,
                    RegionUrdu = aa.District.Region.UrduTitle,
                    RuralArea = aa.RuralAreaPer,
                    TotalPollingStations = aa.TotalPollingStations ?? 0,
                    TownUrdu = string.Empty,
                    UrbanArea = aa.UrbanAreaPer,
                    TotalArea = aa.Area ?? string.Empty,
                    ElectionId = aa.ElectionId,
                    AssemblyId = aa.AssemblyId,
                    ElectionEnglish = aa.Election.EnglishTitle,
                    ElectionUrdu = aa.Election.UrduTitle,
                    UCEnglish = string.Empty,
                    UCUrdu = string.Empty
                }).FirstOrDefault();
            SetPollingScheme(phaseId, dc, strInfo);
            if (strInfo == null)
                return null;

            //strInfo.Phases = new List<ConstElectionPhaseDTO>();
            strInfo.Phases = (from a in dc.Nominations
                where a.StructureId == structureId
                select new ConstElectionPhaseDTO
                {
                    Date = a.ElectionPhase.StartDate.ToString("d MMM, yyyy"),
                    Id = a.ElectionPhaseId,
                    Title = a.ElectionPhase.Title,
                    Seats = new List<ConstSeatsDTO>(),
                    SubSeats = new List<ConstSeatsDTO>()
                }).Distinct().ToList();

            //var seatTypes =

            foreach (var ph in strInfo.Phases)
            {
                ph.Seats = (from aa in dc.ElectionAssemblySeats
                    where aa.ElectionAssemblyId == strInfo.AssemblyId &&
                          aa.ConstituencyType == StructureType.PA
                    select new ConstSeatsDTO
                    {
                        Nominations = new List<ConstNominationDTO>(),
                        SeatTypeEnglish = aa.SeatType.EnglishTitle,
                        SeatTypeUrdu = aa.SeatType.UrduTitle
                    }).ToList();

                foreach (var st in ph.Seats)
                {
                    st.Nominations = (from aa in dc.Nominations
                        orderby aa.Votes descending, aa.Weight descending
                        where aa.ElectionPhaseId == ph.Id &&
                              aa.ElectionAssemblyId == strInfo.AssemblyId &&
                              aa.SeatType.EnglishTitle == st.SeatTypeEnglish &&
                              aa.ElectionAssemblyId == strInfo.AssemblyId &&
                              aa.StructureId == structureId
                        select new ConstNominationDTO
                        {
                            CandidateId = aa.CandidteId,
                            NameEnglish = aa.Candidate.EnglishName,
                            NameUrdu = aa.Candidate.UrduName,
                            PartyEnglish = aa.Party.EnglishTitle,
                            PartyUrdu = aa.Party.UrduTitle,
                            PartyId = aa.PartyId,
                            SymbolEnglish = aa.Symbol.EnglishTitle,
                            SymbolId = aa.SymbolId,
                            SymbolUrdu = aa.Symbol.UrduTitle,
                            VoteObtain = aa.Votes ?? 0,
                            Weight = aa.Weight
                        }).ToList();
                    SetMissingPicsURL(st);
                }
            }


            foreach (var ph in strInfo.Phases)
            {
                ph.Seats = (from aa in dc.ElectionAssemblySeats
                    where aa.ElectionAssemblyId == strInfo.AssemblyId &&
                          aa.ConstituencyType == StructureType.PA
                    select new ConstSeatsDTO
                    {
                        Nominations = new List<ConstNominationDTO>(),
                        SeatTypeEnglish = aa.SeatType.EnglishTitle,
                        SeatTypeUrdu = aa.SeatType.UrduTitle
                    }).ToList();

                foreach (var st in ph.Seats)
                {
                    st.Nominations = (from aa in dc.Nominations
                        orderby aa.Votes descending, aa.Weight descending
                        where aa.ElectionPhaseId == ph.Id &&
                              aa.ElectionAssemblyId == strInfo.AssemblyId &&
                              aa.SeatType.EnglishTitle == st.SeatTypeEnglish &&
                              aa.ElectionAssemblyId == strInfo.AssemblyId &&
                              aa.StructureId == structureId
                        select new ConstNominationDTO
                        {
                            CandidateId = aa.CandidteId,
                            NameEnglish = aa.Candidate.EnglishName,
                            NameUrdu = aa.Candidate.UrduName,
                            PartyEnglish = aa.Party.EnglishTitle,
                            PartyUrdu = aa.Party.UrduTitle,
                            PartyId = aa.PartyId,
                            SymbolEnglish = aa.Symbol.EnglishTitle,
                            SymbolId = aa.SymbolId,
                            SymbolUrdu = aa.Symbol.UrduTitle,
                            VoteObtain = aa.Votes ?? 0,
                            Weight = aa.Weight
                        }).ToList();
                    SetMissingPicsURL(st);
                }
            }

            var prvcon = (from a in dc.StructureMappings
                where a.StructureId == structureId
                select new
                {
                    a.StructureId, a.PreviousStructureId,
                    PreviousCon = a.PreviousStructure.Election.EnglishTitle + ": " + a.PreviousStructure.Code + " - " +
                                  a.PreviousStructure.EnglishTitle
                }).FirstOrDefault();
            if (prvcon != null)
            {
                strInfo.PreviousConstId = prvcon.PreviousStructureId;
                strInfo.PreviousConst = prvcon.PreviousCon;
            }

            return Task.FromResult(strInfo);
        }

        return null;
    }

    private static void SetPollingScheme(int phaseId, ApplicationDbContext dc, ConstInfoDTO strInfo)
    {
        var ps = (from a in dc.PollingSchemes
            where a.StructureId == strInfo.Id
                  && a.PhaseId == phaseId
            select a).FirstOrDefault();
        if (ps != null)
        {
            //strInfo.Castes = (ps.Castes ?? "").Trim();
            //strInfo.UrbanArea = ps.UrbanAreaPer;
            //strInfo.YouthVoters = ps.YouthVoters;
            //strInfo.Economy = (ps.Trivia ?? "").Trim();
            //strInfo.GeneralTrivia = (ps.GeneralTrivia ?? "").Trim();
            //strInfo.FemaleVoters = ps.FemaleVoters ?? 0;
            //strInfo.ImportantAreas = (ps.ImportantAreas ?? "").Trim();
            //strInfo.ImportantPoliticalParties = (ps.ImportantPoliticalPersonalities ?? "").Trim();
            //strInfo.LanguageSpoken = (ps.Languages ?? "").Trim();
            //strInfo.LiteracyRate = (ps.LiteracyRate ?? "").Trim();
            //strInfo.MajorIncomeSource = (ps.MajorityIncomeSource ?? "").Trim();
            //strInfo.MaleVoters = ps.MaleVoters ?? 0;
            //strInfo.Population = ps.Population ?? 0;
            //strInfo.RuralArea = ps.RuralAreaPer ?? 0;
            //strInfo.Profile = ps.Profile ?? "";
            //strInfo.TotalArea = ps.Area ?? "";
            //strInfo.Problems = ps.Problems ?? "";

            //AverageHouseHoldIncome = aa.AverageHouseholdIncome ?? string.Empty,
            strInfo.Castes = ps.Castes ?? "";
            strInfo.Economy = ps.Trivia ?? "";
            strInfo.FemaleVoters = ps.FemaleVoters ?? 0;
            strInfo.GeneralTrivia = ps.GeneralTrivia ?? "";
            strInfo.GeneralTrivia = ps.GeneralTrivia ?? "";
            strInfo.ImportantAreas = ps.ImportantAreas ?? "";
            strInfo.ImportantAreas = ps.ImportantAreas ?? string.Empty;
            strInfo.ImportantPoliticalParties = ps.ImportantPoliticalPersonalities ?? string.Empty;
            strInfo.LanguageSpoken = ps.Languages ?? "";
            strInfo.LanguageSpoken = ps.Languages ?? string.Empty;
            strInfo.LiteracyRate = ps.LiteracyRate ?? "";
            strInfo.MajorIncomeSource = ps.MajorityIncomeSource ?? "";
            strInfo.MajorIncomeSource = ps.MajorityIncomeSource ?? string.Empty;
            strInfo.MaleVoters = ps.MaleVoters ?? 0;
            strInfo.Population = ps.Population ?? 0;
            strInfo.Problems = ps.Problems ?? string.Empty;
            strInfo.RuralArea = ps.RuralAreaPer;
            strInfo.RuralArea = ps.RuralAreaPer;
            strInfo.TotalPollingStations = ps.TotalPollingStations ?? 0;
            strInfo.UrbanArea = ps.UrbanAreaPer;
            strInfo.UrbanArea = ps.UrbanAreaPer;
            strInfo.TotalArea = ps.Area ?? string.Empty;
            strInfo.Profile = ps.Profile ?? "";
        }

        strInfo.AllDemographic = (from a in dc.PollingSchemes
            where a.StructureId == strInfo.Id
            orderby a.Phase.StartDate descending
            select new PhaseWiseDemographic
            {
                Phase = a.Phase.Title, AverageHouseHoldIncome = a.AverageHouseholdIncome,
                Castes = a.Castes, Economy = a.Trivia, FemaleVoters = a.FemaleVoters ?? 0,
                GeneralTrivia = a.GeneralTrivia,
                ImportantAreas = a.ImportantAreas, ImportantPoliticalParties = a.ImportantPoliticalPersonalities,
                LanguageSpoken = a.Languages, LiteracyRate = a.LiteracyRate, MajorIncomeSource = a.MajorityIncomeSource,
                MaleVoters = a.MaleVoters ?? 0, Population = a.Population ?? 0, Problems = a.Problems,
                RuralArea = a.RuralAreaPer,
                TotalPollingStations = a.TotalPollingStations ?? 0, TotalArea = a.Area, YouthVoters = a.YouthVoters,
                UrbanArea = a.UrbanAreaPer, Profile = a.Profile
            }).ToList();
    }

    private static void SetMissingPicsURL(ConstSeatsDTO st)
    {
        foreach (var cc1 in st.Nominations)
        {
            cc1.CandidateUrl =
                !File.Exists(Directory.GetCurrentDirectory() + $"\\wwwroot\\media\\candidates\\{cc1.CandidateId}.jpg")
                    ? "/media/candidates/Blank.jpg"
                    : $"/media/candidates/{cc1.CandidateId}.jpg";

            cc1.PartyFlagUrl =
                File.Exists(Directory.GetCurrentDirectory() + $"\\wwwroot\\media\\flags\\{cc1.PartyId}.jpg")
                    ? $"/media/flags/{cc1.PartyId}.jpg"
                    : "/media/flags/missing-flag.png";

            if (cc1.SymbolId == null)
                cc1.SymbolUrl = "/media/symbols/missing-symbol.png";
            else if (File.Exists(Directory.GetCurrentDirectory() + $"\\wwwroot\\media\\symbols\\{cc1.SymbolId}.jpg"))
                cc1.SymbolUrl = $"/media/symbols/{cc1.SymbolId}.jpg";
            else
                //C:\Users\<USER>\Source\repos\ElectionAppServer\ElectionAppServer\wwwroot\media\symbols\1.jpg
                cc1.SymbolUrl = "/media/symbols/missing-symbol.png";
        }
    }

    public Task<List<GeneralItemDTO>> GetConstituencyTypes(int assemblyId)
    {
        using var dc = _contextFactory.CreateDbContext();

        var q = (from aa in dc.ElectionAssemblySeats
            where aa.ElectionAssemblyId == assemblyId
            select aa.ConstituencyType).Distinct().ToList();

        var obj = new List<GeneralItemDTO>();
        //Legislative Assembly
        foreach (var item in q)
            if (item == StructureType.NA)
                obj.Add(new GeneralItemDTO { Id = 7, EnglishTitle = "National/Legislative Assembly" });
            else if (item == StructureType.PA)
                obj.Add(new GeneralItemDTO { Id = 8, EnglishTitle = "Provincial Assembly" });
            else if (item == StructureType.UnionCouncil)
                obj.Add(new GeneralItemDTO { Id = 5, EnglishTitle = "LG Tier 2" });
            else if (item == StructureType.Ward) obj.Add(new GeneralItemDTO { Id = 6, EnglishTitle = "LG Tier 3" });

        ////var obj = new List<GeneralItemDTO>
        ////{
        ////   new GeneralItemDTO { Id = 1, EnglishTitle = "Union Council" },
        ////   new GeneralItemDTO { Id = 7, EnglishTitle = "National Assembly" },
        ////   new GeneralItemDTO { Id = 8, EnglishTitle = "Provincial Assembly" },
        ////   new GeneralItemDTO { Id = 4, EnglishTitle = "Ward" }
        ////};
        //var obj = new List<GeneralItemDTO>();
        ////public enum StructureType { Province = 1, Division = 2, District = 3, Town = 4, UnionCouncil = 5, Ward = 6, NA = 7, PA = 8 };
        //obj.Add(new GeneralItemDTO { Id = 5, EnglishTitle = "Union Council" });
        //obj.Add(new GeneralItemDTO { Id = 6, EnglishTitle = "Ward" });
        //obj.Add(new GeneralItemDTO { Id = 7, EnglishTitle = "National Assembly" });
        //obj.Add(new GeneralItemDTO { Id = 8, EnglishTitle = "Provincial Assembly" });

        return Task.FromResult(obj);
    }

    public Task<List<GeneralItemDTO>> GetDistricts(int divisionId, int electionId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.Structures.OfType<District>()
            where aa.DivisionId == divisionId && aa.ElectionId == electionId
            orderby aa.EnglishTitle
            select new GeneralItemDTO { EnglishTitle = aa.EnglishTitle + " District", Id = aa.Id }).ToList();

        return Task.FromResult(q);
    }

    public Task<List<GeneralItemDTO>> GetDivisions(int provinceId, int electionId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.Structures.OfType<Division>()
            where aa.ProvinceId == provinceId && aa.ElectionId == electionId
            orderby aa.EnglishTitle
            select new GeneralItemDTO { EnglishTitle = aa.EnglishTitle + " Division", Id = aa.Id }).ToList();

        return Task.FromResult(q);
    }

    //	return Task.FromResult(k);
    //}
    public Task<List<GeneralItemDTO>> GetElectionAssemblies(int electionId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.ElectionAssemblies
            orderby aa.EnglishTitle
            where aa.ElectionId == electionId
            select new GeneralItemDTO { Id = aa.Id, EnglishTitle = $"{aa.EnglishTitle} - {aa.UrduTitle}" }).ToList();

        return Task.FromResult(q);
    }

    public Task<List<GeneralItemDTO>> GetElectionDistrictsStructure(int electionId, int divisionId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.Structures.OfType<District>()
            orderby aa.EnglishTitle
            where aa.ElectionId == electionId &&
                  aa.DivisionId == divisionId
            select new GeneralItemDTO { Id = aa.Id, EnglishTitle = $"{aa.EnglishTitle} - {aa.UrduTitle}" }).ToList();
        return Task.FromResult(q);
    }

    public Task<List<GeneralItemDTO>> GetElectionDivisionsStructure(int electionId, int provinceId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.Structures.OfType<Division>()
            orderby aa.EnglishTitle
            where aa.ElectionId == electionId &&
                  aa.ProvinceId == provinceId
            select new GeneralItemDTO { Id = aa.Id, EnglishTitle = $"{aa.EnglishTitle} - {aa.UrduTitle}" }).ToList();
        return Task.FromResult(q);
    }

    //public Task<string> RemoveContituenciesToAssembly_Old(int electionAssemblyId, List<int> structureIds, string user)
    //{
    //   using var tran = dc.Database.BeginTransaction();
    //   try
    //   {
    //      foreach (var item in structureIds)
    //      {
    //         var q = (from aa in dc.ElectionAssemblyConstituencies
    //                  where aa.StructureId == item && aa.ElectionAssemblyId == electionAssemblyId
    //                  select aa).ToList();
    //         dc.ElectionAssemblyConstituencies.RemoveRange(q);
    //         dc.SaveChanges();
    //      }
    //      tran.Commit();
    //      return Task.FromResult("OK");
    //   }
    //   catch (Exception)
    //   {
    //      throw;
    //   }
    //}
    //public Task<string> AddContituenciesToAssembly_Old(int electionAssemblyId, List<int> structureIds, ElectionAssemblyType typeId, string user)
    //{
    //   try
    //   {
    //      var ea = (from aa in dc.ElectionAssemblies
    //                where aa.Id == electionAssemblyId
    //                select aa).FirstOrDefault();
    //      if (ea.ElectionAssemblyType != typeId)
    //      {
    //         ea.ElectionAssemblyType = typeId;
    //         ea.ModifiedBy = user;
    //         ea.ModifiedDate = DateTime.Now;
    //         dc.SaveChanges();
    //      }
    //      foreach (var stId in structureIds)
    //      {
    //         var c = new ElectionAssemblyConstituency
    //         {
    //            ElectionAssemblyId = electionAssemblyId,
    //            StructureId = stId,
    //            CreatedBy = user,
    //            CreatedDate = DateTime.Now
    //         };
    //         dc.ElectionAssemblyConstituencies.Add(c);
    //         dc.SaveChanges();
    //      }
    //   }
    //   catch (Exception)
    //   {
    //   }
    //   return Task.FromResult("OK");
    //}
    public Task<int> GetElectionId(int electionAssemblyId)
    {
        using var dc = _contextFactory.CreateDbContext();
        return Task.FromResult(dc.ElectionAssemblies.Find(electionAssemblyId).ElectionId);
    }

    public Task<List<GeneralItemDTO>> GetElectionInfo(int eaId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.ElectionAssemblies
            where aa.Id == eaId
            select new GeneralItemDTO { Id = aa.ElectionId, EnglishTitle = aa.Election.EnglishTitle }).FirstOrDefault();

        var q2 = (from aa in dc.ElectionAssemblies
            where aa.Id == eaId
            select new GeneralItemDTO { Id = aa.Id, EnglishTitle = aa.EnglishTitle }).FirstOrDefault();

        var q3 = new List<GeneralItemDTO>
        {
            q,
            q2
        };

        return Task.FromResult(q3);
    }

    //	var k = (resUC.Union(resWard).Union(na_pro).Union(na_div).Union(na_dis).Union(na_town).Union(na_UC)
    //										 .Union(pa_pro).Union(pa_div).Union(pa_dis).Union(pa_town).Union(pa_UC)).Distinct().ToList();
    public Task<List<GeneralItemDTO>> GetElectionProvincesStructure(int electionId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.Structures.OfType<Province>()
            orderby aa.EnglishTitle
            where aa.ElectionId == electionId
            select new GeneralItemDTO { Id = aa.Id, EnglishTitle = $"{aa.EnglishTitle} - {aa.UrduTitle}" }).ToList();
        return Task.FromResult(q);
    }

    //	#endregion Provincial Assembly
    public Task<List<GeneralItemDTO>> GetElectionTownsStructure(int electionId, int districtId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.Structures.OfType<Town>()
            orderby aa.EnglishTitle
            where aa.ElectionId == electionId &&
                  aa.DistrictId == districtId
            select new GeneralItemDTO { Id = aa.Id, EnglishTitle = $"{aa.EnglishTitle} - {aa.UrduTitle}" }).ToList();
        return Task.FromResult(q);
    }

    //	var pa_UC = (from aa in dc.Structures.OfType<ProvincialAssemblyHalka>() // Get All National Assembly Constituencies
    //					 join eac in dc.ElectionAssemblyConstituencies on aa.Id equals eac.StructureId // Which are in Election Assembly
    //					 join str in dc.Structures.OfType<UnionCouncil>() on aa.DistrictId equals str.Id // and in under province structure
    //					 where aa.ElectionId == electionId &&
    //					 (electionAssemblyId == null || eac.ElectionAssemblyId == electionAssemblyId) &&
    //					 (provinceId == null && str.Town.District.Division.ProvinceId == provinceId) &&
    //					 (divisionId == null && str.Town.District.Division.Id == divisionId) &&
    //					 (districtId == null && str.Town.District.Id == districtId) &&
    //					 (townId == null && str.Town.Id == townId) &&
    //					 (unionCouncild == null && str.Id == unionCouncild)
    //					 select new ConstituencyDTO
    //					 {
    //						 Id = aa.Id,
    //						 Code = aa.Code,
    //						 EnglishTitle = aa.EnglishTitle,
    //						 Assembly = eac.ElectionAssembly.EnglishTitle,
    //						 MaleVoters = aa.MaleVoters,
    //						 FemaleVoters = aa.FemaleVoters,
    //						 TotalPollingStations = aa.TotalPollingStations
    //					 }).ToList();
    public Task<List<GeneralItemDTO>> GetElectionUnionCouncilsStructure(int electionId, int townId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.Structures.OfType<UnionCouncil>()
            orderby aa.EnglishTitle
            where aa.ElectionId == electionId &&
                  aa.TownId == townId
            select new GeneralItemDTO { Id = aa.Id, EnglishTitle = $"{aa.EnglishTitle} - {aa.UrduTitle}" }).ToList();
        return Task.FromResult(q);
    }

    //	var pa_town = (from aa in dc.Structures.OfType<ProvincialAssemblyHalka>() // Get All National Assembly Constituencies
    //						join eac in dc.ElectionAssemblyConstituencies on aa.Id equals eac.StructureId // Which are in Election Assembly
    //						join str in dc.Structures.OfType<Town>() on aa.DistrictId equals str.Id // and in under province structure
    //						where aa.ElectionId == electionId &&
    //						(electionAssemblyId == null || eac.ElectionAssemblyId == electionAssemblyId) &&
    //						(provinceId == null && str.District.Division.ProvinceId == provinceId) &&
    //						(divisionId == null && str.District.Division.Id == divisionId) &&
    //						(districtId == null && str.District.Id == districtId)
    //						select new ConstituencyDTO
    //						{
    //							Id = aa.Id,
    //							Code = aa.Code,
    //							EnglishTitle = aa.EnglishTitle,
    //							Assembly = eac.ElectionAssembly.EnglishTitle,
    //							MaleVoters = aa.MaleVoters,
    //							FemaleVoters = aa.FemaleVoters,
    //							TotalPollingStations = aa.TotalPollingStations
    //						}).ToList();
    public Task<List<GeneralItemDTO>> GetElectionUnionWardStructure(int electionId, int unionCouncilId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.Structures.OfType<Ward>()
            orderby aa.EnglishTitle
            where aa.ElectionId == electionId &&
                  aa.UnionCouncilId == unionCouncilId
            select new GeneralItemDTO { Id = aa.Id, EnglishTitle = $"{aa.EnglishTitle} - {aa.UrduTitle}" }).ToList();
        return Task.FromResult(q);
    }

    public Task<List<GeneralItemDTO>> GetLanguages()
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.Languages
            orderby aa.EnglishTitle
            select new GeneralItemDTO
            {
                Id = aa.Id,
                EnglishTitle = aa.EnglishTitle,
                UrduTitle = aa.UrduTitle
            }).ToList();

        return Task.FromResult(q);
    }

    public Task<List<GeneralItemDTO>> GetProvinceList(int electionId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.Structures.OfType<Province>()
            orderby aa.EnglishTitle
            where aa.ElectionId == electionId
            select new GeneralItemDTO { EnglishTitle = aa.EnglishTitle, Id = aa.Id }).ToList();

        return Task.FromResult(q);
    }

    public Task<List<ConstituencyDTO>> GetSelectedConstituencies(int electionAssemblyId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q1 = (from aa in dc.Structures.OfType<UnionCouncil>()
            where aa.AssemblyId == electionAssemblyId
            select new ConstituencyDTO
            {
                Id = aa.Id,
                Code = aa.Code,
                EnglishTitle = $"{aa.EnglishTitle}<br><span class='urdu-sm'>{aa.UrduTitle}</span>",
                UrduTitle = aa.UrduTitle,
                Town = $"{aa.Town.EnglishTitle}<br><span class='urdu-sm'>{aa.Town.UrduTitle}</span>",
                District =
                    $"{aa.Town.District.EnglishTitle}<br><span class='urdu-sm'>{aa.Town.District.UrduTitle}</span>",
                Division =
                    $"{aa.Town.District.Division.EnglishTitle}<br><span class='urdu-sm'>{aa.Town.District.Division.UrduTitle}</span>",
                Province =
                    $"{aa.Town.District.Division.Province.EnglishTitle}<br><span class='urdu-sm'>{aa.Town.District.Division.Province.UrduTitle}</span>",
                IsNominationExist = aa.Nominations.Any(),
                Region =
                    $"{aa.Town.District.Region.EnglishTitle}<br><span class='urdu-sm'>{aa.Town.District.Region.UrduTitle}</span>",
                Type = "UC"
            }).ToList();

        var q2 = (from aa in dc.Structures.OfType<Ward>()
            where aa.AssemblyId == electionAssemblyId
            select new ConstituencyDTO
            {
                Id = aa.Id,
                Code = aa.Code,
                EnglishTitle = $"{aa.EnglishTitle}<br><span class='urdu-sm'>{aa.UrduTitle}</span>",
                UrduTitle = aa.UrduTitle,
                UC = $"{aa.UnionCouncil.EnglishTitle}<br><span class='urdu-sm'>{aa.UnionCouncil.UrduTitle}</span>",
                Town =
                    $"{aa.UnionCouncil.Town.EnglishTitle}<br><span class='urdu-sm'>{aa.UnionCouncil.Town.UrduTitle}</span>",
                District =
                    $"{aa.UnionCouncil.Town.District.EnglishTitle}<br><span class='urdu-sm'>{aa.UnionCouncil.Town.District.UrduTitle}</span>",
                Division =
                    $"{aa.UnionCouncil.Town.District.Division.EnglishTitle}<br><span class='urdu-sm'>{aa.UnionCouncil.Town.District.Division.UrduTitle}</span>",
                Province =
                    $"{aa.UnionCouncil.Town.District.Division.Province.EnglishTitle}<br><span class='urdu-sm'>{aa.UnionCouncil.Town.District.Division.Province.UrduTitle}</span>",
                Region =
                    $"{aa.UnionCouncil.Town.District.Region.EnglishTitle}<br><span class='urdu-sm'>{aa.UnionCouncil.Town.District.Region.UrduTitle}</span>",
                IsNominationExist = aa.Nominations.Any(),
                Type = "Ward"
            }).ToList();

        var q3 = (from aa in dc.Structures.OfType<NationalAssemblyHalka>()
            where aa.AssemblyId == electionAssemblyId
            select new ConstituencyDTO
            {
                Id = aa.Id,
                Code = aa.Code,
                EnglishTitle = $"{aa.EnglishTitle}<br><span class='urdu-sm'>{aa.UrduTitle}</span>",
                UrduTitle = aa.UrduTitle,
                UC = string.Empty,
                Town = string.Empty,
                District = $"{aa.District.EnglishTitle}<br><span class='urdu-sm'>{aa.District.UrduTitle}</span>",
                Division =
                    $"{aa.District.Division.EnglishTitle}<br><span class='urdu-sm'>{aa.District.Division.UrduTitle}</span>",
                Province =
                    $"{aa.District.Division.Province.EnglishTitle}<br><span class='urdu-sm'>{aa.District.Division.Province.UrduTitle}</span>",
                Region =
                    $"{aa.District.Region.EnglishTitle}<br><span class='urdu-sm'>{aa.District.Region.UrduTitle}</span>",
                IsNominationExist = aa.Nominations.Any(),
                Type = "NA"
            }).ToList();

        var q4 = (from aa in dc.Structures.OfType<ProvincialAssemblyHalka>()
            where aa.AssemblyId == electionAssemblyId
            select new ConstituencyDTO
            {
                Id = aa.Id,
                Code = aa.Code,
                EnglishTitle = $"{aa.EnglishTitle}<br><span class='urdu-sm'>{aa.UrduTitle}</span>",
                UrduTitle = aa.UrduTitle,
                UC = string.Empty,
                Town = string.Empty,
                District = $"{aa.District.EnglishTitle}<br><span class='urdu-sm'>{aa.District.UrduTitle}</span>",
                Division =
                    $"{aa.District.Division.EnglishTitle}<br><span class='urdu-sm'>{aa.District.Division.UrduTitle}</span>",
                Province =
                    $"{aa.District.Division.Province.EnglishTitle}<br><span class='urdu-sm'>{aa.District.Division.Province.UrduTitle}</span>",
                Region =
                    $"{aa.District.Region.EnglishTitle}<br><span class='urdu-sm'>{aa.District.Region.UrduTitle}</span>",
                IsNominationExist = aa.Nominations.Any(),
                Type = "PA"
            }).ToList();

        var q = q1.Union(q2).Union(q3).Union(q4).ToList();
        var qn = (from aa in q
            orderby aa.Province, aa.Division, aa.District, aa.Town, aa.UC, aa.EnglishTitle
            select aa).ToList();

        //var q1 = (from aa in dc.Structures.OfType<UnionCouncil>()
        // where aa.AssemblyId == electionAssemblyId
        // orderby aa.EnglishTitle
        // select new ConstituencyDTO
        // {
        //  Id = aa.Id, EnglishTitle = aa.EnglishTitle, UrduTitle = aa.UrduTitle, Code = aa.Code,
        //  Town = aa.Town.EnglishTitle, District = aa.Town.District.EnglishTitle,
        //  Division = aa.Town.District.Division.EnglishTitle,
        //  Province = aa.Town.District.Division.Province.EnglishTitle
        // }).Union(from aa in dc.Structures.OfType<ProvincialAssemblyHalka>()
        // where aa.AssemblyId == electionAssemblyId
        // select new ConstituencyDTO
        // {
        //  Id = aa.Id,
        //  EnglishTitle = aa.EnglishTitle,
        //  UrduTitle = aa.UrduTitle,
        //  Code = aa.Code,
        //  Town = "",
        //  District = aa.District.EnglishTitle,
        //  Division = aa.District.Division.EnglishTitle,
        //  Province = aa.District.Division.Province.EnglishTitle
        // }).Union(from aa in dc.Structures.OfType<Ward>()
        // where aa.AssemblyId == electionAssemblyId
        // select new ConstituencyDTO
        // {
        //  Id = aa.Id,
        //  EnglishTitle = aa.EnglishTitle,
        //  UrduTitle = aa.UrduTitle,
        //  Code = aa.Code,
        //  Town = aa.UnionCouncil.Town.EnglishTitle,
        //  District = aa.UnionCouncil.Town.District.EnglishTitle,
        //  Division = aa.UnionCouncil.Town.District.Division.EnglishTitle,
        //  Province = aa.UnionCouncil.Town.District.Division.Province.EnglishTitle
        // }).Union(from aa in dc.Structures.OfType<NationalAssemblyHalka>()
        // where aa.AssemblyId == electionAssemblyId
        // select new ConstituencyDTO
        // {
        //  Id = aa.Id,
        //  EnglishTitle = aa.EnglishTitle,
        //  UrduTitle = aa.UrduTitle,
        //  Code = aa.Code,
        //  Town = "",
        //  District = aa.District.EnglishTitle,
        //  Division = aa.District.Division.EnglishTitle,
        //  Province = aa.District.Division.Province.EnglishTitle
        // }).OrderBy(c => new {c.Province, c.Division, c.District, c.Town, c.UC, c.EnglishTitle}).ToList();
        return Task.FromResult(qn);
    }

    public Task<List<GeneralItemDTO>> GetTowns(int districtId, int electionId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.Structures.OfType<Town>()
            where aa.DistrictId == districtId && aa.ElectionId == electionId
            orderby aa.EnglishTitle
            select new GeneralItemDTO { EnglishTitle = aa.EnglishTitle + " Town", Id = aa.Id }).ToList();

        return Task.FromResult(q);
    }

    public Task<List<GeneralItemDTO>> GetUnionCouncils(int townId, int electionId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.Structures.OfType<UnionCouncil>()
            where aa.TownId == townId && aa.ElectionId == electionId
            orderby aa.EnglishTitle
            select new GeneralItemDTO { EnglishTitle = aa.EnglishTitle, Id = aa.Id }).ToList();

        return Task.FromResult(q);
    }

    //public Task<List<ConstituencyDTO>> GetAvailableConstituencies_Old(int electionAssemblyId, StructureType typeId)
    //{
    //   var electionId = dc.ElectionAssemblies.Find(electionAssemblyId).ElectionId;

    //   var eacList = (from aa in dc.ElectionAssemblyConstituencies
    //                  where aa.ElectionAssembly.ElectionId == electionId
    //                  select aa.StructureId).Distinct().ToList();

    //   if (typeId == StructureType.UnionCouncil)
    //   {
    //      var q1 = (from s in dc.Structures.OfType<UnionCouncil>()
    //                orderby s.Town.District.Division.Province.EnglishTitle,
    //                        s.Town.District.Division.EnglishTitle,
    //                        s.Town.District.EnglishTitle,
    //                        s.Town.EnglishTitle,
    //                        s.EnglishTitle
    //                where s.ElectionId == electionId &&
    //                !eacList.Contains(s.Id)
    //                select new ConstituencyDTO
    //                {
    //                   Id = s.Id,
    //                   EnglishTitle = s.EnglishTitle,
    //                   Code = s.Code,
    //                   Town = s.Town.EnglishTitle,
    //                   District = s.Town.District.EnglishTitle,
    //                   Division = s.Town.District.Division.EnglishTitle,
    //                   Province = s.Town.District.Division.Province.EnglishTitle
    //                }).ToList();
    //      return Task.FromResult(q1);
    //   }
    //   else if (typeId == StructureType.Ward)
    //   {
    //      var q1 = (from s in dc.Structures.OfType<Ward>()
    //                orderby s.Code
    //                where s.ElectionId == electionId &&
    //                !eacList.Contains(s.Id)
    //                select new ConstituencyDTO
    //                {
    //                   Id = s.Id,
    //                   EnglishTitle = s.EnglishTitle,
    //                   Code = s.Code,
    //                   UC = s.UnionCouncil.EnglishTitle,
    //                   Town = s.UnionCouncil.Town.EnglishTitle,
    //                   District = s.UnionCouncil.Town.District.EnglishTitle,
    //                   Division = s.UnionCouncil.Town.District.Division.EnglishTitle,
    //                   Province = s.UnionCouncil.Town.District.Division.Province.EnglishTitle
    //                }).ToList();

    //      return Task.FromResult(q1);
    //   }
    //   else if (typeId == StructureType.PA)
    //   {
    //      var q1 = (from s in dc.Structures.OfType<ProvincialAssemblyHalka>()
    //                join pr in dc.Structures.OfType<Province>() on s.DistrictId equals pr.Id
    //                where !eacList.Contains(s.Id) &&
    //                s.ElectionId == electionId
    //                select new ConstituencyDTO
    //                {
    //                   Id = s.Id,
    //                   EnglishTitle = s.EnglishTitle,
    //                   Code = s.Code,
    //                   UC = "",
    //                   Town = "",
    //                   District = "",
    //                   Division = "",
    //                   Province = pr.EnglishTitle
    //                }).ToList();

    //      var q2 = (from s in dc.Structures.OfType<ProvincialAssemblyHalka>()
    //                join pr in dc.Structures.OfType<Division>() on s.DistrictId equals pr.Id
    //                where !eacList.Contains(s.Id) &&
    //                s.ElectionId == electionId
    //                select new ConstituencyDTO
    //                {
    //                   Id = s.Id,
    //                   EnglishTitle = s.EnglishTitle,
    //                   Code = s.Code,
    //                   UC = "",
    //                   Town = "",
    //                   District = "",
    //                   Division = pr.EnglishTitle,
    //                   Province = pr.Province.EnglishTitle
    //                }).ToList();

    //      var q3 = (from s in dc.Structures.OfType<ProvincialAssemblyHalka>()
    //                join pr in dc.Structures.OfType<District>() on s.DistrictId equals pr.Id
    //                where !eacList.Contains(s.Id) &&
    //                s.ElectionId == electionId
    //                select new ConstituencyDTO
    //                {
    //                   Id = s.Id,
    //                   EnglishTitle = s.EnglishTitle,
    //                   Code = s.Code,
    //                   UC = "",
    //                   Town = "",
    //                   District = pr.EnglishTitle,
    //                   Division = pr.Division.EnglishTitle,
    //                   Province = pr.Division.Province.EnglishTitle
    //                }).ToList();

    //      var q4 = (from s in dc.Structures.OfType<ProvincialAssemblyHalka>()
    //                join pr in dc.Structures.OfType<Town>() on s.DistrictId equals pr.Id
    //                where !eacList.Contains(s.Id) &&
    //                s.ElectionId == electionId
    //                select new ConstituencyDTO
    //                {
    //                   Id = s.Id,
    //                   EnglishTitle = s.EnglishTitle,
    //                   Code = s.Code,
    //                   UC = "",
    //                   Town = pr.EnglishTitle,
    //                   District = pr.District.EnglishTitle,
    //                   Division = pr.District.Division.EnglishTitle,
    //                   Province = pr.District.Division.Province.EnglishTitle
    //                }).ToList();
    //      var q5 = (q1.Union(q2).Union(q3).Union(q4)).ToList();
    //      return Task.FromResult(q5);
    //   }
    //   else if (typeId == StructureType.NA)
    //   {
    //      var q1 = (from s in dc.Structures.OfType<NationalAssemblyHalka>()
    //                join pr in dc.Structures.OfType<Province>() on s.DistrictId equals pr.Id
    //                where !eacList.Contains(s.Id) &&
    //                s.ElectionId == electionId
    //                select new ConstituencyDTO
    //                {
    //                   Id = s.Id,
    //                   EnglishTitle = s.EnglishTitle,
    //                   Code = s.Code,
    //                   UC = "",
    //                   Town = "",
    //                   District = "",
    //                   Division = "",
    //                   Province = pr.EnglishTitle
    //                }).ToList();

    //      var q2 = (from s in dc.Structures.OfType<NationalAssemblyHalka>()
    //                join pr in dc.Structures.OfType<Division>() on s.DistrictId equals pr.Id
    //                where !eacList.Contains(s.Id) &&
    //                s.ElectionId == electionId
    //                select new ConstituencyDTO
    //                {
    //                   Id = s.Id,
    //                   EnglishTitle = s.EnglishTitle,
    //                   Code = s.Code,
    //                   UC = "",
    //                   Town = "",
    //                   District = "",
    //                   Division = pr.EnglishTitle,
    //                   Province = pr.Province.EnglishTitle
    //                }).ToList();

    //      var q3 = (from s in dc.Structures.OfType<NationalAssemblyHalka>()
    //                join pr in dc.Structures.OfType<District>() on s.DistrictId equals pr.Id
    //                where !eacList.Contains(s.Id) &&
    //                s.ElectionId == electionId
    //                select new ConstituencyDTO
    //                {
    //                   Id = s.Id,
    //                   EnglishTitle = s.EnglishTitle,
    //                   Code = s.Code,
    //                   UC = "",
    //                   Town = "",
    //                   District = pr.EnglishTitle,
    //                   Division = pr.Division.EnglishTitle,
    //                   Province = pr.Division.Province.EnglishTitle
    //                }).ToList();

    //      var q4 = (from s in dc.Structures.OfType<NationalAssemblyHalka>()
    //                join pr in dc.Structures.OfType<Town>() on s.DistrictId equals pr.Id
    //                where !eacList.Contains(s.Id) &&
    //                s.ElectionId == electionId
    //                select new ConstituencyDTO
    //                {
    //                   Id = s.Id,
    //                   EnglishTitle = s.EnglishTitle,
    //                   Code = s.Code,
    //                   UC = "",
    //                   Town = pr.EnglishTitle,
    //                   District = pr.District.EnglishTitle,
    //                   Division = pr.District.Division.EnglishTitle,
    //                   Province = pr.District.Division.Province.EnglishTitle
    //                }).ToList();
    //      var q5 = (q1.Union(q2).Union(q3).Union(q4)).ToList();
    //      return Task.FromResult(q5);
    //   }
    //   var kk = new List<ConstituencyDTO>();
    //   return Task.FromResult(kk);
    //}

    //public Task<List<ConstituencyDTO>> GetConstituencies(int electionAssemblyId, StructureType? typeId,
    // int? provinceId, int? divisionId, int? districtId, int? townId, int? unionCouncilId)
    //{
    // var electionId = dc.ElectionAssemblies.Find(electionAssemblyId).ElectionId;
    // if (typeId == StructureType.UnionCouncil)
    // {
    // }
    //}

    //public Task<List<ConstituencyDTO>> GetConstituencies_Old(int electionAssemblyId, StructureType? typeId, int? provinceId, int? divisionId, int? districtId, int? townId, int? unionCouncilId)
    //{
    //   var electionId = dc.ElectionAssemblies.Find(electionAssemblyId).ElectionId;
    //   var eacList = (from aa in dc.ElectionAssemblyConstituencies
    //                  where aa.ElectionAssembly.ElectionId == electionId
    //                  select aa.StructureId).Distinct().ToList();

    //   if (typeId == StructureType.UnionCouncil)
    //   {
    //      var q1 = (from s in dc.Structures.OfType<UnionCouncil>()
    //                orderby s.Town.District.Division.Province.EnglishTitle,
    //                        s.Town.District.Division.EnglishTitle,
    //                        s.Town.District.EnglishTitle,
    //                        s.Town.EnglishTitle,
    //                        s.EnglishTitle
    //                where s.ElectionId == electionId &&
    //                !eacList.Contains(s.Id) &&
    //                (provinceId == null || s.Town.District.Division.ProvinceId == provinceId) &&
    //                (divisionId == null || s.Town.District.DivisionId == divisionId) &&
    //                (districtId == null || s.Town.DistrictId == districtId) &&
    //                (townId == null || s.TownId == townId)
    //                select new ConstituencyDTO
    //                {
    //                   Id = s.Id,
    //                   EnglishTitle = s.EnglishTitle,
    //                   Code = s.Code,
    //                   Town = s.Town.EnglishTitle,
    //                   District = s.Town.District.EnglishTitle,
    //                   Division = s.Town.District.Division.EnglishTitle,
    //                   Province = s.Town.District.Division.Province.EnglishTitle
    //                }).ToList();

    //      return Task.FromResult(q1);
    //   }
    //   if (typeId == StructureType.Ward)
    //   {
    //      var q1 = (from s in dc.Structures.OfType<Ward>()
    //                orderby s.Code
    //                where s.ElectionId == electionId &&
    //                !eacList.Contains(s.Id) &&
    //                (provinceId == null || s.UnionCouncil.Town.District.Division.ProvinceId == provinceId) &&
    //                (divisionId == null || s.UnionCouncil.Town.District.DivisionId == divisionId) &&
    //                (districtId == null || s.UnionCouncil.Town.DistrictId == districtId) &&
    //                (townId == null || s.UnionCouncil.TownId == townId) &&
    //                (unionCouncilId == null || s.UnionCouncilId == unionCouncilId)
    //                select new ConstituencyDTO
    //                {
    //                   Id = s.Id,
    //                   EnglishTitle = s.EnglishTitle,
    //                   Code = s.Code,
    //                   UC = s.UnionCouncil.EnglishTitle,
    //                   Town = s.UnionCouncil.Town.EnglishTitle,
    //                   District = s.UnionCouncil.Town.District.EnglishTitle,
    //                   Division = s.UnionCouncil.Town.District.Division.EnglishTitle,
    //                   Problems = s.UnionCouncil.Town.District.Division.Province.EnglishTitle
    //                }).ToList();

    //      return Task.FromResult(q1);
    //   }
    //   if (typeId == StructureType.NA)
    //   {
    //      var q1 = (from aa in dc.Structures.OfType<NationalAssemblyHalka>()
    //                orderby aa.EnglishTitle
    //                where aa.ElectionId == electionId &&
    //                !eacList.Contains(aa.Id) &&
    //                (provinceId == null || aa.DistrictId == provinceId)
    //                select new ConstituencyDTO
    //                {
    //                   Id = aa.Id,
    //                   EnglishTitle = aa.EnglishTitle,
    //                   Code = aa.Code,
    //                }).ToList();
    //      var q2 = (from aa in dc.Structures.OfType<NationalAssemblyHalka>()
    //                orderby aa.EnglishTitle
    //                where aa.ElectionId == electionId &&
    //                !eacList.Contains(aa.Id) &&
    //                (divisionId == null || aa.DistrictId == divisionId)
    //                select new ConstituencyDTO
    //                {
    //                   Id = aa.Id,
    //                   EnglishTitle = aa.EnglishTitle,
    //                   Code = aa.Code,
    //                   Province = ""
    //                }).ToList();

    //      var q3 = (from aa in dc.Structures.OfType<NationalAssemblyHalka>()
    //                orderby aa.EnglishTitle
    //                where aa.ElectionId == electionId &&
    //                !eacList.Contains(aa.Id) &&
    //                (districtId == null || aa.DistrictId == districtId)
    //                select new ConstituencyDTO
    //                {
    //                   Id = aa.Id,
    //                   EnglishTitle = aa.EnglishTitle,
    //                   Code = aa.Code,
    //                   Province = ""
    //                }).ToList();

    //      var q4 = (from aa in dc.Structures.OfType<NationalAssemblyHalka>()
    //                orderby aa.EnglishTitle
    //                where aa.ElectionId == electionId &&
    //                !eacList.Contains(aa.Id) &&
    //                (townId == null || aa.DistrictId == townId
    //                )
    //                select new ConstituencyDTO
    //                {
    //                   Id = aa.Id,
    //                   EnglishTitle = aa.EnglishTitle,
    //                   Code = aa.Code
    //                   ,
    //                   Province = ""
    //                }).ToList();
    //      List<ConstituencyDTO> gp = Combine(q1, q2, q3, q4);
    //      //var qq = q1.Concat(q2).Concat(q3).Concat(q4);
    //      //var qf = qq.Distinct().OrderBy(d => d.EnglishTitle).ToList();
    //      return Task.FromResult(gp);
    //   }
    //   if (typeId == StructureType.PA)
    //   {
    //      var q1 = (from aa in dc.Structures.OfType<ProvincialAssemblyHalka>()
    //                orderby aa.EnglishTitle
    //                where aa.ElectionId == electionId &&
    //                !eacList.Contains(aa.Id) &&
    //                (provinceId == null || aa.DistrictId == provinceId)
    //                select new ConstituencyDTO
    //                {
    //                   Id = aa.Id,
    //                   EnglishTitle = aa.EnglishTitle,
    //                   Code = aa.Code
    //                }).ToList();
    //      var q2 = (from aa in dc.Structures.OfType<ProvincialAssemblyHalka>()
    //                orderby aa.EnglishTitle
    //                where aa.ElectionId == electionId &&
    //                !eacList.Contains(aa.Id) &&
    //                (divisionId == null || aa.DistrictId == divisionId)
    //                select new ConstituencyDTO
    //                {
    //                   Id = aa.Id,
    //                   EnglishTitle = aa.EnglishTitle,
    //                   Code = aa.Code
    //                }).ToList();

    //      var q3 = (from aa in dc.Structures.OfType<ProvincialAssemblyHalka>()
    //                orderby aa.EnglishTitle
    //                where aa.ElectionId == electionId &&
    //                !eacList.Contains(aa.Id) &&
    //                (districtId == null || aa.DistrictId == districtId)
    //                select new ConstituencyDTO
    //                {
    //                   Id = aa.Id,
    //                   EnglishTitle = aa.EnglishTitle,
    //                   Code = aa.Code
    //                }).ToList();

    //      var q4 = (from aa in dc.Structures.OfType<ProvincialAssemblyHalka>()
    //                orderby aa.EnglishTitle
    //                where aa.ElectionId == electionId &&
    //                !eacList.Contains(aa.Id) &&
    //                (townId == null || aa.DistrictId == townId
    //                )
    //                select new ConstituencyDTO
    //                {
    //                   Id = aa.Id,
    //                   EnglishTitle = aa.EnglishTitle,
    //                   Code = aa.Code
    //                }).ToList();

    //      List<ConstituencyDTO> gp = Combine(q1, q2, q3, q4);
    //      return Task.FromResult(gp);
    //   }
    //   List<ConstituencyDTO> jj = new List<ConstituencyDTO>();
    //   return Task.FromResult(jj);
    //}

    public Task<string> RemoveContituenciesToAssembly(int electionAssmeblyId, List<int> structureIds, string user)
    {
        using var dc = _contextFactory.CreateDbContext();
        using (var tran = dc.Database.BeginTransaction())
        {
            try
            {
                foreach (var cc in structureIds)
                {
                    var q = (from aa in dc.Structures
                        where aa.Id == cc && aa.AssemblyId == electionAssmeblyId
                        select aa).FirstOrDefault();
                    if (q != null)
                    {
                        q.AssemblyId = null;
                        q.ModifiedBy = user;
                        q.ModifiedDate = DateTime.Now;
                        dc.SaveChanges();
                    }
                }

                tran.Commit();
                return Task.FromResult("OK");
            }
            catch (Exception)
            {
                tran.Rollback();
                throw;
            }
        }
    }

    public Task<string> RemoveStructure(int structureId, int assemblyId, string user)
    {
        using var dc = _contextFactory.CreateDbContext();
        using var tran = dc.Database.BeginTransaction();
        try
        {
            var q = (from aa in dc.Structures
                where aa.Id == structureId && aa.AssemblyId == assemblyId
                select aa).FirstOrDefault();
            if (q != null)
            {
                q.AssemblyId = null;
                q.ModifiedBy = user;
                q.ModifiedDate = DateTime.Now;
                dc.SaveChanges();
                tran.Commit();
            }

            return Task.FromResult("OK");
        }
        catch (Exception)
        {
            tran.Rollback();
            throw;
        }
    }

    //      item.IsNominationExist = q;
    //   }
    //   return Task.FromResult(res);
    //}

    public Task<List<ConstituencyDTO>> Search(int electionId, int? electionAssemblyId, int? provinceId,
        int? divisionId, int? districtId, int? townId, int? unionCouncilId, int? wardId, bool inUrdu = true)
    {
        using var dc = _contextFactory.CreateDbContext();
        using (var con = dc.Database.GetDbConnection())
        {
            var parms = new
            {
                ElectionId = electionId,
                AssemblyId = electionAssemblyId,
                ProvinceId = provinceId,
                DistrictId = districtId,
                TownId = townId
            };
            var res = con
                .Query<ConstituencyDTO>("dbo.ep_SearchConstituencies", parms, commandType: CommandType.StoredProcedure)
                .ToList();
            return Task.FromResult(res);
        }
    }


    public Task<List<ConstituencyDTO>> Search2(int electionId, int? electionAssemblyId, int? provinceId,
        int? divisionId, int? districtId, int? townId, int? unionCouncilId, int? wardId, bool inUrdu = true)
    {
        using var dc = _contextFactory.CreateDbContext();
        var UCs = (from aa in dc.Structures.OfType<UnionCouncil>()
            where aa.ElectionId == electionId
                  && (electionAssemblyId == null || aa.AssemblyId == electionAssemblyId)
                  && (unionCouncilId == null || aa.Id == unionCouncilId)
                  && (townId == null || aa.TownId == townId)
                  && (districtId == null || aa.Town.DistrictId == districtId)
                  && (divisionId == null || aa.Town.District.DivisionId == divisionId)
                  && (provinceId == null || aa.Town.District.Division.ProvinceId == provinceId)
                  && aa.AssemblyId != null
            select new ConstituencyDTO
            {
                Id = aa.Id,
                Assembly = $"{aa.Assembly.EnglishTitle}<br><span class='urdu-sm'>{aa.Assembly.UrduTitle}</span>",
                Code = aa.Code,
                EnglishTitle = $"{aa.EnglishTitle}<br><span class='urdu-sm'>{aa.UrduTitle}</span>",
                MaleVoters = aa.MaleVoters,
                FemaleVoters = aa.FemaleVoters,
                TotalPollingStations = aa.TotalPollingStations,
                AssemblyId = (int)aa.AssemblyId,
                Province =
                    $"{aa.Town.District.Division.Province.EnglishTitle}<br><span class='urdu-sm'>{aa.Town.District.Division.Province.UrduTitle}</span>",
                Division =
                    $"{aa.Town.District.Division.EnglishTitle}<br><span class='urdu-sm'>{aa.Town.District.Division.UrduTitle}</span>",
                District =
                    $"{aa.Town.District.EnglishTitle}<br><span class='urdu-sm'>{aa.Town.District.UrduTitle}</span>",
                Region =
                    $"{aa.Town.District.Region.EnglishTitle}<br><span class='urdu-sm'>{aa.Town.District.Region.UrduTitle}</span>",
                Town = $"{aa.Town.EnglishTitle}<br><span class='urdu-sm'>{aa.Town.UrduTitle}</span>",
                UC = string.Empty,
                Type = "UC"
            }).ToList();

        var wards = (from aa in dc.Structures.OfType<Ward>()
            where aa.ElectionId == electionId
                  && (electionAssemblyId == null || aa.AssemblyId == electionAssemblyId)
                  && (unionCouncilId == null || aa.UnionCouncilId == unionCouncilId)
                  && (townId == null || aa.UnionCouncil.TownId == townId)
                  && (districtId == null || aa.UnionCouncil.Town.DistrictId == districtId)
                  && (divisionId == null || aa.UnionCouncil.Town.District.DivisionId == divisionId)
                  && (provinceId == null || aa.UnionCouncil.Town.District.Division.ProvinceId == provinceId)
                  && aa.AssemblyId != null
            select new ConstituencyDTO
            {
                Id = aa.Id,
                Assembly = $"{aa.Assembly.EnglishTitle}<br><span class='urdu-sm'>{aa.Assembly.UrduTitle}</span>",
                Code = aa.Code,
                EnglishTitle = $"{aa.EnglishTitle}<br><span class='urdu-sm'>{aa.UrduTitle}</span>",
                MaleVoters = aa.MaleVoters,
                FemaleVoters = aa.FemaleVoters,
                TotalPollingStations = aa.TotalPollingStations,
                AssemblyId = (int)aa.AssemblyId,
                Province =
                    $"{aa.UnionCouncil.Town.District.Division.Province.EnglishTitle}<br><span class='urdu-sm'>{aa.UnionCouncil.Town.District.Division.Province.UrduTitle}</span>",
                Region =
                    $"{aa.UnionCouncil.Town.District.Region.EnglishTitle}<br><span class='urdu-sm'>{aa.UnionCouncil.Town.District.Region.UrduTitle}</span>",
                Division =
                    $"{aa.UnionCouncil.Town.District.Division.EnglishTitle}<br><span class='urdu-sm'>{aa.UnionCouncil.Town.District.Division.UrduTitle}</span>",
                District =
                    $"{aa.UnionCouncil.Town.District.EnglishTitle}<br><span class='urdu-sm'>{aa.UnionCouncil.Town.District.UrduTitle}</span>",
                Town =
                    $"{aa.UnionCouncil.Town.EnglishTitle}<br><span class='urdu-sm'>{aa.UnionCouncil.Town.UrduTitle}</span>",
                UC = $"{aa.UnionCouncil.EnglishTitle}<br><span class='urdu-sm'>{aa.UnionCouncil.UrduTitle}</span>",
                Type = "Ward"
            }).ToList();

        var NAs = (from aa in dc.Structures.OfType<NationalAssemblyHalka>()
            where aa.ElectionId == electionId
                  && (electionAssemblyId == null || aa.AssemblyId == electionAssemblyId)
                  && (districtId == null || aa.DistrictId == districtId)
                  && (divisionId == null || aa.District.DivisionId == divisionId)
                  && (provinceId == null || aa.District.Division.ProvinceId == provinceId)
                  && aa.AssemblyId != null
            select new ConstituencyDTO
            {
                Id = aa.Id,
                Assembly = $"{aa.Assembly.EnglishTitle}<br><span class='urdu-sm'>{aa.Assembly.UrduTitle}</span>",
                Code = aa.Code,
                EnglishTitle = $"{aa.EnglishTitle}<br><span class='urdu-sm'>{aa.UrduTitle}</span>",
                MaleVoters = aa.MaleVoters,
                FemaleVoters = aa.FemaleVoters,
                TotalPollingStations = aa.TotalPollingStations,
                AssemblyId = (int)aa.AssemblyId,
                Province =
                    $"{aa.District.Division.Province.EnglishTitle}<br><span class='urdu-sm'>{aa.District.Division.Province.UrduTitle}</span>",
                Region =
                    $"{aa.District.Region.EnglishTitle}<br><span class='urdu-sm'>{aa.District.Region.UrduTitle}</span>",
                Division =
                    $"{aa.District.Division.EnglishTitle}<br><span class='urdu-sm'>{aa.District.Division.UrduTitle}</span>",
                District = $"{aa.District.EnglishTitle}<br><span class='urdu-sm'>{aa.District.UrduTitle}</span>",
                Town = string.Empty,
                UC = string.Empty,
                Type = "NA"
            }).ToList();

        var PAs = (from aa in dc.Structures.OfType<ProvincialAssemblyHalka>()
            where aa.ElectionId == electionId
                  && (electionAssemblyId == null || aa.AssemblyId == electionAssemblyId)
                  && (districtId == null || aa.DistrictId == districtId)
                  && (divisionId == null || aa.District.DivisionId == divisionId)
                  && (provinceId == null || aa.District.Division.ProvinceId == provinceId)
                  && aa.AssemblyId != null
            select new ConstituencyDTO
            {
                Id = aa.Id,
                Assembly = $"{aa.Assembly.EnglishTitle}<br><span class='urdu-sm'>{aa.Assembly.UrduTitle}</span>",
                Code = aa.Code,
                EnglishTitle = $"{aa.EnglishTitle}<br><span class='urdu-sm'>{aa.UrduTitle}</span>",
                MaleVoters = aa.MaleVoters,
                FemaleVoters = aa.FemaleVoters,
                TotalPollingStations = aa.TotalPollingStations,
                AssemblyId = (int)aa.AssemblyId,
                Province =
                    $"{aa.District.Division.Province.EnglishTitle}<br><span class='urdu-sm'>{aa.District.Division.Province.UrduTitle}</span>",
                Region =
                    $"{aa.District.Region.EnglishTitle}<br><span class='urdu-sm'>{aa.District.Region.UrduTitle}</span>",
                Division =
                    $"{aa.District.Division.EnglishTitle}<br><span class='urdu-sm'>{aa.District.Division.UrduTitle}</span>",
                District = $"{aa.District.EnglishTitle}<br><span class='urdu-sm'>{aa.District.UrduTitle}</span>",
                Town = string.Empty,
                UC = string.Empty,
                Type = "PA"
            }).ToList();

        var q = UCs.Union(wards).Union(NAs).Union(PAs).ToList();
        var q2 = (from aa in q
            orderby aa.Type, aa.Province, aa.Division, aa.District, aa.Town, aa.UC, aa.UrduTitle
            select aa).ToList();

        return Task.FromResult(q2);
    }

    //	var pa_dis = (from aa in dc.Structures.OfType<ProvincialAssemblyHalka>() // Get All National Assembly Constituencies
    //					  join eac in dc.ElectionAssemblyConstituencies on aa.Id equals eac.StructureId // Which are in Election Assembly
    //					  join str in dc.Structures.OfType<District>() on aa.DistrictId equals str.Id // and in under province structure
    //					  where aa.ElectionId == electionId &&
    //					  (electionAssemblyId == null || eac.ElectionAssemblyId == electionAssemblyId) &&
    //					  (provinceId == null && str.Division.ProvinceId == provinceId) &&
    //					  (divisionId == null && str.Division.Id == divisionId) &&
    //					  (districtId == null && str.Id == districtId)
    //					  select new ConstituencyDTO
    //					  {
    //						  Id = aa.Id,
    //						  Code = aa.Code,
    //						  EnglishTitle = aa.EnglishTitle,
    //						  Assembly = eac.ElectionAssembly.EnglishTitle,
    //						  MaleVoters = aa.MaleVoters,
    //						  FemaleVoters = aa.FemaleVoters,
    //						  TotalPollingStations = aa.TotalPollingStations
    //					  }).ToList();
    public Task<string> Update(List<ConstituencyDTO> search_result, string user)
    {
        using var dc = _contextFactory.CreateDbContext();
        foreach (var res in search_result)
        {
            var q = (from aa in dc.Structures
                where aa.Id == res.Id
                select aa).FirstOrDefault();

            q.MaleVoters = res.MaleVoters;
            q.FemaleVoters = res.FemaleVoters;
            q.TotalPollingStations = res.TotalPollingStations;
            q.Languages = res.Languages;
            q.Castes = res.Castes;
            q.AverageHouseholdIncome = res.AverageHouseholdIncome;

            q.ModifiedBy = user;
            q.ModifiedDate = DateTime.Now;
            dc.SaveChanges();
        }

        return Task.FromResult("OK");
    }

    //				var str = (from aa in dc.Structures
    //							  where aa.Id == structureId
    //							  select aa).FirstOrDefault();
    //				dc.Structures.Remove(str);
    //				dc.SaveChanges();
    //			}
    //			tran.Commit();
    //			return Task.FromResult("OK");
    //		}
    //		catch (Exception)
    //		{
    //			tran.Rollback();
    //			throw;
    //		}
    //	}
    //}
    //public Task<string> RemoveStructure(int structureId, int assemblyId)
    //{
    //	using (var tran = dc.Database.BeginTransaction())
    //	{
    //		try
    //		{
    //			var q = (from aa in dc.ElectionAssemblyConstituencies
    //						where aa.ElectionAssemblyId == assemblyId &&
    //						aa.StructureId == structureId
    //						select aa).FirstOrDefault();
    //			dc.ElectionAssemblyConstituencies.Remove(q);
    //			dc.SaveChanges();
    //			tran.Commit();
    //			return Task.FromResult("OK");
    //		}
    //		catch (Exception)
    //		{
    //			throw;
    //		}
    //	}
    //}
    public Task<string> UpdateConstituency(ConstituencyDTO obj, string user, int phaseId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var electionId = dc.ElectionAssemblies.Find(obj.AssemblyId).ElectionId;

        var q = (from aa in dc.Structures
            where aa.Id == obj.Id
            select aa).FirstOrDefault();

        if (q == null)
            throw new Exception("Record not found");

        // Save Phase wise polling scheme
        var ps = (from a in dc.PollingSchemes
            where a.StructureId == obj.Id && a.PhaseId == phaseId
            select a).FirstOrDefault();
        var isNewPollingScheme = false;
        if (ps == null)
        {
            ps = new PollingScheme
            {
                StructureId = obj.Id,
                PhaseId = phaseId,
                CreatedBy = user,
                CreatedDate = DateTime.Now
            };
            isNewPollingScheme = true;
        }

        ps.MaleVoters = obj.MaleVoters;
        ps.YouthVoters = obj.YouthVoters;
        ps.FemaleVoters = obj.FemaleVoters;
        ps.TotalPollingStations = obj.TotalPollingStations;
        ps.Languages = (obj.Languages ?? "").Trim();
        ps.Castes = (obj.Castes ?? "").Trim();
        ps.Trivia = (obj.Trivia ?? "").Trim();
        ps.GeneralTrivia = (obj.GeneralTrivia ?? "").Trim();
        ps.ImportantPoliticalPersonalities = (obj.ImportantPoliticalPersonalities ?? "").Trim();
        ps.Problems = (obj.Problems ?? "").Trim();
        ps.ImportantAreas = (obj.ImportantAreas ?? "").Trim();
        ps.RuralAreaPer = obj.RuralAreaPer;
        ps.UrbanAreaPer = obj.UrbanAreaPer;
        ps.Population = obj.Population;
        ps.MajorityIncomeSource = (obj.MajorityIncomeSource ?? "").Trim();
        ps.LiteracyRate = (obj.LiteracyRate ?? "").Trim();
        ps.AverageHouseholdIncome = (obj.AverageHouseholdIncome ?? "").Trim();
        ps.Area = (obj.Area ?? "").Trim();
        ps.ModifiedBy = user;
        ps.ModifiedDate = DateTime.Now;
        ps.Profile = (obj.Profile ?? "").Trim();
        ps.Problems = (obj.Problems ?? "").Trim();
        //q.ImportantAreas = (obj.ImportantAreas ?? string.Empty).Trim();
        q.EnglishTitle = obj.EnglishTitle.Trim();
        //q.FemaleVoters = obj.FemaleVoters;
        //q.ImportantAreas = (obj.ImportantAreas ?? string.Empty).Trim();
        //q.ImportantPoliticalPersonalities = (obj.ImportantPoliticalPersonalities ?? string.Empty).Trim();
        //q.MaleVoters = obj.MaleVoters;
        //q.ModifiedBy = user;
        //q.ModifiedDate = DateTime.Now;
        //q.Problems = (obj.Problems ?? string.Empty).Trim();
        //q.RuralAreaPer = obj.RuralAreaPer;
        //q.TotalPollingStations = obj.TotalPollingStations;
        //q.Trivia = obj.Trivia;
        //q.UrbanAreaPer = obj.UrbanAreaPer;
        q.UrduTitle = (obj.UrduTitle ?? string.Empty).Trim();
        q.Code = (obj.Code ?? string.Empty).Trim();
        //q.AverageHouseholdIncome = (obj.AverageHouseholdIncome ?? string.Empty).Trim();
        ;
        //q.Castes = (obj.Castes ?? string.Empty).Trim();
        //q.ImportantAreas = (obj.ImportantAreas ?? string.Empty).Trim();
        //q.ImportantPoliticalPersonalities = (obj.ImportantPoliticalPersonalities ?? string.Empty).Trim();
        //q.Languages = (obj.Languages ?? string.Empty).Trim();
        //q.LiteracyRate = (obj.LiteracyRate ?? string.Empty).Trim();
        //q.MajorityIncomeSource = (obj.MajorityIncomeSource ?? string.Empty).Trim();
        //q.Population = obj.Population;
        //q.Problems = (obj.Problems ?? string.Empty).Trim();
        //q.GeneralTrivia = (obj.GeneralTrivia ?? string.Empty).Trim();
        q.IsPostponed = obj.IsPostponed;
        q.HasBilaMokablia = obj.HasBilaMokablia;
        if (isNewPollingScheme) dc.PollingSchemes.Add(ps);
        dc.SaveChanges();


        return Task.FromResult("OK");
    }

    private static List<ConstituencyDTO> Combine(List<ConstituencyDTO> q1, List<ConstituencyDTO> q2,
        List<ConstituencyDTO> q3, List<ConstituencyDTO> q4)
    {
        var gp = new List<ConstituencyDTO>();
        foreach (var i in q1)
        {
            var isExist = gp.Any(c => c.Id == i.Id);
            if (!isExist)
                gp.Add(new ConstituencyDTO
                {
                    Id = i.Id,
                    EnglishTitle = i.EnglishTitle,
                    Province = i.Province,
                    UrduTitle = i.UrduTitle,
                    Code = i.Code,
                    UC = i.UC,
                    Town = i.Town,
                    District = i.District,
                    Division = i.Division
                });
        }

        foreach (var i in q2)
        {
            var isExist = gp.Any(c => c.Id == i.Id);
            if (!isExist)
                gp.Add(new ConstituencyDTO
                {
                    Id = i.Id,
                    EnglishTitle = i.EnglishTitle,
                    Province = i.Province,
                    UrduTitle = i.UrduTitle,
                    Code = i.Code,
                    UC = i.UC,
                    Town = i.Town,
                    District = i.District,
                    Division = i.Division
                });
        }

        foreach (var i in q3)
        {
            var isExist = gp.Any(c => c.Id == i.Id);
            if (!isExist)
                gp.Add(new ConstituencyDTO
                {
                    Id = i.Id,
                    EnglishTitle = i.EnglishTitle,
                    Province = i.Province,
                    UrduTitle = i.UrduTitle,
                    Code = i.Code,
                    UC = i.UC,
                    Town = i.Town,
                    District = i.District,
                    Division = i.Division
                });
        }

        foreach (var i in q4)
        {
            var isExist = gp.Any(c => c.Id == i.Id);
            if (!isExist)
                gp.Add(new ConstituencyDTO
                {
                    Id = i.Id,
                    EnglishTitle = i.EnglishTitle,
                    Province = i.Province,
                    UrduTitle = i.UrduTitle,
                    Code = i.Code,
                    UC = i.UC,
                    Town = i.Town,
                    District = i.District,
                    Division = i.Division
                });
        }

        return gp;
    }

    //public Task<List<GeneralItemDTO>> GetConstituencies2(int electionAssemblyId, StructureType? typeId, int? provinceId, int? divisionId, int? districtId, int? townId, int? unionCouncilId)
    //{
    //   var electionId = dc.ElectionAssemblies.Find(electionAssemblyId).ElectionId;

    //   if (typeId == StructureType.UnionCouncil)
    //   {
    //      // Election assembly contituencies list
    //      var eacList = (from aa in dc.ElectionAssemblyConstituencies
    //                     where aa.ElectionAssemblyId == electionAssemblyId &&
    //                     aa.ElectionAssembly.ElectionId == electionId
    //                     select aa.StructureId).Distinct().ToList();

    //      var q = (from aa in dc.Structures.OfType<UnionCouncil>()
    //               orderby aa.Code
    //               //aa.ElectionId == electionId &&
    //               where (provinceId == null || aa.Town.District.Division.ProvinceId == provinceId)
    //               //where (provinceId == null || aa.Town.District.Division.ProvinceId == provinceId) &&
    //               //      (divisionId == null || aa.Town.District.DivisionId == divisionId) &&
    //               //      (districtId == null || aa.Town.DistrictId == districtId) &&
    //               //      (townId == null || aa.TownId == townId) &&
    //               //      (unionCouncilId == null || aa.Id == unionCouncilId) &&
    //               //      (aa.ElectionId == electionId) &&
    //               //      !eacList.Contains(aa.Id)

    //               select new GeneralItemDTO
    //               {
    //                  Id = aa.Id,
    //                  EnglishTitle = aa.Code + " - " + aa.EnglishTitle
    //               }).ToList();

    //      return Task.FromResult(q);
    //   }
    //   else if (typeId == StructureType.NA)
    //   {
    //      var eacList = (from aa in dc.ElectionAssemblyConstituencies
    //                     where aa.ElectionAssemblyId == electionAssemblyId
    //                     select aa.StructureId).Distinct().ToList();

    //      //var q = (from aa in dc.Structures.OfType<NationalAssemblyHalka>()
    //      //         orderby aa.EnglishTitle
    //      //         where (provinceId == null || aa.District.Division.ProvinceId == provinceId) &&
    //      //               (divisionId == null || aa.District.DivisionId == divisionId) &&
    //      //               (districtId == null || aa.DistrictId == districtId) &&
    //      //               !eacList.Contains(aa.Id)
    //      //         select new GeneralItemDTO
    //      //         {
    //      //            Id = aa.Id,
    //      //            EnglishTitle = aa.EnglishTitle
    //      //         }).ToList();

    //      var q = (from aa in dc.Structures.OfType<NationalAssemblyHalka>()
    //               orderby aa.EnglishTitle
    //               where aa.ElectionId == electionId &&
    //               !eacList.Contains(aa.Id)
    //               select new GeneralItemDTO
    //               {
    //                  Id = aa.Id,
    //                  EnglishTitle = aa.Code + " - " + aa.EnglishTitle
    //               }).ToList();

    //      return Task.FromResult(q);
    //   }
    //   else if (typeId == StructureType.PA)
    //   {
    //      var eacList = (from aa in dc.ElectionAssemblyConstituencies
    //                     where aa.ElectionAssemblyId == electionAssemblyId
    //                     select aa.StructureId).Distinct().ToList();

    //      var q = (from aa in dc.Structures.OfType<ProvincialAssemblyHalka>()
    //               orderby aa.EnglishTitle
    //               where aa.ElectionId == electionId &&
    //               !eacList.Contains(aa.Id)
    //               select new GeneralItemDTO
    //               {
    //                  Id = aa.Id,
    //                  EnglishTitle = aa.Code + " - " + aa.EnglishTitle
    //               }).ToList();

    //      return Task.FromResult(q);
    //   }
    //   else if (typeId == StructureType.Ward)
    //   {
    //      var eacList = (from aa in dc.ElectionAssemblyConstituencies
    //                     where aa.ElectionAssemblyId == electionAssemblyId
    //                     select aa.StructureId).Distinct().ToList();

    //      var q = (from aa in dc.Structures.OfType<Ward>()
    //               orderby aa.EnglishTitle
    //               where (provinceId == null || aa.UnionCouncil.Town.District.Division.ProvinceId == provinceId) &&
    //                     (divisionId == null || aa.UnionCouncil.Town.District.DivisionId == divisionId) &&
    //                     (districtId == null || aa.UnionCouncil.Town.DistrictId == districtId) &&
    //                     (districtId == null || aa.UnionCouncil.TownId == townId) &&
    //                     (districtId == null || aa.UnionCouncilId == unionCouncilId) &&
    //                     !eacList.Contains(aa.Id)
    //               select new GeneralItemDTO
    //               {
    //                  Id = aa.Id,
    //                  EnglishTitle = $"{aa.Code} - {aa.EnglishTitle} ({aa.UnionCouncil.EnglishTitle} - {aa.UnionCouncil.Town.EnglishTitle} Town)"
    //               }).ToList();
    //      return Task.FromResult(q);
    //   }

    //   var op = new List<GeneralItemDTO>();
    //   return Task.FromResult(op);
    //}
    //public Task<List<ConstituencyDTO>> GetSelectedConstituencies_Old(int electionAssemblyId)
    //{
    //   var q1 = (from aa in dc.ElectionAssemblyConstituencies
    //             join ss in dc.Structures.OfType<UnionCouncil>() on aa.StructureId equals ss.Id
    //             where aa.ElectionAssemblyId == electionAssemblyId
    //             orderby aa.Structure.EnglishTitle
    //             select new ConstituencyDTO
    //             {
    //                Id = ss.Id,
    //                EnglishTitle = ss.EnglishTitle,
    //                Code = ss.Code,
    //                Town = ss.Town.EnglishTitle,
    //                District = ss.Town.District.EnglishTitle,
    //                Division = ss.Town.District.Division.EnglishTitle,
    //                Province = ss.Town.District.Division.Province.EnglishTitle
    //             }).Distinct().ToList();

    //   var q2 = (from aa in dc.ElectionAssemblyConstituencies
    //             join ss in dc.Structures.OfType<Ward>() on aa.StructureId equals ss.Id
    //             where aa.ElectionAssemblyId == electionAssemblyId
    //             orderby aa.Structure.EnglishTitle
    //             select new ConstituencyDTO
    //             {
    //                Id = ss.Id,
    //                EnglishTitle = ss.EnglishTitle,
    //                Code = ss.Code,
    //                UC = ss.UnionCouncil.EnglishTitle,
    //                Town = ss.UnionCouncil.Town.EnglishTitle,
    //                District = ss.UnionCouncil.Town.District.EnglishTitle,
    //                Division = ss.UnionCouncil.Town.District.Division.EnglishTitle,
    //                Province = ss.UnionCouncil.Town.District.Division.Province.EnglishTitle
    //             }).Distinct().ToList();
    //   //select new GeneralItemDTO
    //   //{
    //   //   Id = aa.StructureId,
    //   //   EnglishTitle = (ss.Code == null ? "" : ss.Code) + " - " + ss.EnglishTitle + " (" + ss.UnionCouncil.EnglishTitle + ")"
    //   //}).Distinct().ToList();

    //   var q3 = (from aa in dc.ElectionAssemblyConstituencies
    //             join ss in dc.Structures.OfType<NationalAssemblyHalka>() on aa.StructureId equals ss.Id
    //             where aa.ElectionAssemblyId == electionAssemblyId
    //             orderby aa.Structure.EnglishTitle
    //             select new ConstituencyDTO
    //             {
    //                Id = ss.Id,
    //                EnglishTitle = ss.EnglishTitle,
    //                Code = ss.Code,
    //                District = ss.District.EnglishTitle,
    //                Division = ss.District.Division.EnglishTitle,
    //                Province = ss.District.Division.Province.EnglishTitle
    //             }).Distinct().ToList();
    //   //select new GeneralItemDTO
    //   //{
    //   //   Id = aa.StructureId,
    //   //   EnglishTitle = (ss.Code == null ? "" : ss.Code) + " - " + ss.EnglishTitle
    //   //}).Distinct().ToList();

    //   var q4 = (from aa in dc.ElectionAssemblyConstituencies
    //             join ss in dc.Structures.OfType<ProvincialAssemblyHalka>() on aa.StructureId equals ss.Id
    //             where aa.ElectionAssemblyId == electionAssemblyId
    //             orderby aa.Structure.EnglishTitle
    //             select new ConstituencyDTO
    //             {
    //                Id = ss.Id,
    //                EnglishTitle = ss.EnglishTitle,
    //                Code = ss.Code,
    //                District = ss.District.EnglishTitle,
    //                Division = ss.District.Division.EnglishTitle,
    //                Province = ss.District.Division.Province.EnglishTitle
    //             }).Distinct().ToList();
    //   //select new GeneralItemDTO
    //   //{
    //   //   Id = aa.StructureId,
    //   //   EnglishTitle = (ss.Code == null ? "" : ss.Code) + " - " + ss.EnglishTitle
    //   //}).Distinct().ToList();

    //   var res = new List<ConstituencyDTO>();

    //   foreach (var item in q1)
    //   {
    //      res.Add(item);
    //   }

    //   foreach (var item in q2)
    //   {
    //      res.Add(item);
    //   }

    //   foreach (var item in q3)
    //   {
    //      res.Add(item);
    //   }

    //   foreach (var item in q4)
    //   {
    //      res.Add(item);
    //   }
    //   foreach (var item in res)
    //   {
    //      var q = (from aa in dc.Nominations
    //               where aa.StructureId == item.Id &&
    //               aa.ElectionAssemblyId == electionAssemblyId
    //               select aa).Any();
    //public Task<List<ConstituencyDTO>> Search3(int electionId, int? electionAssemblyId, int? provinceId, int? divisionId, int? districtId, int? townId, int? unionCouncild, int? wardId)
    //{
    //	var resUC = (from aa in dc.ElectionAssemblyConstituencies
    //					 join s in dc.Structures.OfType<UnionCouncil>() on aa.StructureId equals s.Id
    //					 where aa.ElectionAssembly.ElectionId == electionId &&
    //							 (electionAssemblyId == null || aa.ElectionAssemblyId == electionAssemblyId) &&
    //							 (provinceId == null || s.Town.District.Division.ProvinceId == provinceId) &&
    //							 (divisionId == null || s.Town.District.DivisionId == divisionId) &&
    //							 (districtId == null || s.Town.DistrictId == districtId) &&
    //							 (townId == null || s.TownId == townId) &&
    //							 (unionCouncild == null || s.Id == unionCouncild)
    //					 select new ConstituencyDTO
    //					 {
    //						 Id = aa.StructureId,
    //						 Assembly = aa.ElectionAssembly.UrduTitle,
    //						 Code = aa.Structure.Code,
    //						 EnglishTitle = aa.Structure.Code + " " + s.UrduTitle,
    //						 MaleVoters = s.MaleVoters,
    //						 FemaleVoters = s.FemaleVoters,
    //						 TotalPollingStations = s.TotalPollingStations,
    //						 AssemblyId = aa.ElectionAssemblyId,
    //						 Province = s.Town.District.Division.Province.UrduTitle,
    //						 Division = s.Town.District.Division.UrduTitle,
    //						 District = s.Town.District.UrduTitle,
    //						 Town = s.Town.UrduTitle
    //					 }).ToList();

    //	var resWards = (from aa in dc.ElectionAssemblyConstituencies
    //						 join s in dc.Structures.OfType<Ward>() on aa.StructureId equals s.Id
    //						 where aa.ElectionAssembly.ElectionId == electionId &&
    //								 (electionAssemblyId == null || aa.ElectionAssemblyId == electionAssemblyId) &&
    //								 (provinceId == null || s.UnionCouncil.Town.District.Division.ProvinceId == provinceId) &&
    //								 (divisionId == null || s.UnionCouncil.Town.District.DivisionId == divisionId) &&
    //								 (districtId == null || s.UnionCouncil.Town.DistrictId == districtId) &&
    //								 (townId == null || s.UnionCouncil.TownId == divisionId) &&
    //								 (unionCouncild == null || s.UnionCouncil.Id == unionCouncild) &&
    //								 (wardId == null || s.Id == wardId)
    //						 select new ConstituencyDTO
    //						 {
    //							 Id = aa.StructureId,
    //							 Assembly = aa.ElectionAssembly.UrduTitle,
    //							 Code = aa.Structure.Code,
    //							 EnglishTitle = s.UrduTitle,
    //							 MaleVoters = s.MaleVoters,
    //							 FemaleVoters = s.FemaleVoters,
    //							 TotalPollingStations = s.TotalPollingStations,
    //							 AssemblyId = aa.ElectionAssemblyId,
    //							 Province = s.UnionCouncil.Town.District.Division.Province.UrduTitle,
    //							 District = s.UnionCouncil.Town.District.UrduTitle,
    //							 Division = s.UnionCouncil.Town.District.Division.UrduTitle,
    //							 UC = s.UnionCouncil.Code + " - " + s.UnionCouncil.UrduTitle + "(" + s.UnionCouncil.Town.UrduTitle + ")",
    //							 Town = s.UnionCouncil.Town.UrduTitle
    //						 }).ToList();

    //	#region National Assembly

    //	var resNA_pro = (from aa in dc.ElectionAssemblyConstituencies
    //						  join s in dc.Structures.OfType<NationalAssemblyHalka>() on aa.StructureId equals s.Id
    //						  join p in dc.Structures.OfType<Province>() on s.DistrictId equals p.Id
    //						  where aa.ElectionAssembly.ElectionId == electionId &&
    //						  (electionAssemblyId == null || aa.ElectionAssemblyId == electionAssemblyId) &&
    //						  (provinceId == null || p.Id == provinceId)
    //						  select new ConstituencyDTO
    //						  {
    //							  Id = aa.StructureId,
    //							  Assembly = aa.ElectionAssembly.UrduTitle,
    //							  Code = aa.Structure.Code,
    //							  EnglishTitle = s.UrduTitle,
    //							  MaleVoters = s.MaleVoters,
    //							  FemaleVoters = s.FemaleVoters,
    //							  TotalPollingStations = s.TotalPollingStations,
    //							  AssemblyId = aa.ElectionAssemblyId,
    //							  Province = p.UrduTitle,
    //							  District = "",
    //							  UC = "",
    //							  Division = "",
    //							  Town = ""
    //						  }).ToList();

    //	var resNA_div = (from aa in dc.ElectionAssemblyConstituencies
    //						  join s in dc.Structures.OfType<NationalAssemblyHalka>() on aa.StructureId equals s.Id
    //						  join p in dc.Structures.OfType<Division>() on s.DistrictId equals p.Id
    //						  where aa.ElectionAssembly.ElectionId == electionId &&
    //						  (electionAssemblyId == null || aa.ElectionAssemblyId == electionAssemblyId) &&
    //						  (divisionId == null || p.Id == divisionId)
    //						  select new ConstituencyDTO
    //						  {
    //							  Id = aa.StructureId,
    //							  Assembly = aa.ElectionAssembly.UrduTitle,
    //							  Code = aa.Structure.Code,
    //							  EnglishTitle = s.UrduTitle,
    //							  MaleVoters = s.MaleVoters,
    //							  FemaleVoters = s.FemaleVoters,
    //							  TotalPollingStations = s.TotalPollingStations,
    //							  AssemblyId = aa.ElectionAssemblyId,
    //							  Province = p.Province.UrduTitle,
    //							  Division = p.UrduTitle,
    //							  District = "",
    //							  Town = "",
    //							  UC = ""
    //						  }).ToList();

    //	var resNA_dis = (from aa in dc.ElectionAssemblyConstituencies
    //						  join s in dc.Structures.OfType<NationalAssemblyHalka>() on aa.StructureId equals s.Id
    //						  join p in dc.Structures.OfType<District>() on s.DistrictId equals p.Id
    //						  where aa.ElectionAssembly.ElectionId == electionId &&
    //						  (electionAssemblyId == null || aa.ElectionAssemblyId == electionAssemblyId) &&
    //						  (districtId == null || p.Id == districtId)
    //						  select new ConstituencyDTO
    //						  {
    //							  Id = aa.StructureId,
    //							  Assembly = aa.ElectionAssembly.UrduTitle,
    //							  Code = aa.Structure.Code,
    //							  EnglishTitle = s.UrduTitle,
    //							  MaleVoters = s.MaleVoters,
    //							  FemaleVoters = s.FemaleVoters,
    //							  TotalPollingStations = s.TotalPollingStations,
    //							  AssemblyId = aa.ElectionAssemblyId,
    //							  District = p.UrduTitle,
    //							  Division = p.Division.UrduTitle,
    //							  Province = p.Division.Province.UrduTitle,
    //							  UC = "",
    //							  Town = ""
    //						  }).ToList();

    //	var resNA_town = (from aa in dc.ElectionAssemblyConstituencies
    //							join s in dc.Structures.OfType<NationalAssemblyHalka>() on aa.StructureId equals s.Id
    //							join p in dc.Structures.OfType<Town>() on s.DistrictId equals p.Id
    //							where aa.ElectionAssembly.ElectionId == electionId &&
    //							(electionAssemblyId == null || aa.ElectionAssemblyId == electionAssemblyId) &&
    //							(townId == null || p.Id == townId)
    //							select new ConstituencyDTO
    //							{
    //								Id = aa.StructureId,
    //								Assembly = aa.ElectionAssembly.UrduTitle,
    //								Code = aa.Structure.Code,
    //								EnglishTitle = s.UrduTitle,
    //								MaleVoters = s.MaleVoters,
    //								FemaleVoters = s.FemaleVoters,
    //								TotalPollingStations = s.TotalPollingStations,
    //								AssemblyId = aa.ElectionAssemblyId,
    //								Town = p.UrduTitle,
    //								District = p.District.UrduTitle,
    //								Division = p.District.Division.UrduTitle,
    //								Province = p.District.Division.Province.UrduTitle,
    //								UC = ""
    //							}).ToList();

    //	#endregion National Assembly

    //	#region Provincial Assembly

    //	var resPA_pro = (from aa in dc.ElectionAssemblyConstituencies
    //						  join s in dc.Structures.OfType<ProvincialAssemblyHalka>() on aa.StructureId equals s.Id
    //						  join p in dc.Structures.OfType<Province>() on s.DistrictId equals p.Id
    //						  where aa.ElectionAssembly.ElectionId == electionId &&
    //						  (electionAssemblyId == null || aa.ElectionAssemblyId == electionAssemblyId) &&
    //						  (provinceId == null || p.Id == provinceId)
    //						  select new ConstituencyDTO
    //						  {
    //							  Id = aa.StructureId,
    //							  Assembly = aa.ElectionAssembly.UrduTitle,
    //							  Code = aa.Structure.Code,
    //							  EnglishTitle = s.UrduTitle,
    //							  MaleVoters = s.MaleVoters,
    //							  FemaleVoters = s.FemaleVoters,
    //							  TotalPollingStations = s.TotalPollingStations,
    //							  AssemblyId = aa.ElectionAssemblyId,
    //							  Province = p.UrduTitle,
    //							  Division = "",
    //							  District = "",
    //							  Town = "",
    //							  UC = ""
    //						  }).ToList();

    //	var resPA_div = (from aa in dc.ElectionAssemblyConstituencies
    //						  join s in dc.Structures.OfType<ProvincialAssemblyHalka>() on aa.StructureId equals s.Id
    //						  join p in dc.Structures.OfType<Division>() on s.DistrictId equals p.Id
    //						  where aa.ElectionAssembly.ElectionId == electionId &&
    //						  (electionAssemblyId == null || aa.ElectionAssemblyId == electionAssemblyId) &&
    //						  (divisionId == null || p.Id == divisionId)
    //						  select new ConstituencyDTO
    //						  {
    //							  Id = aa.StructureId,
    //							  Assembly = aa.ElectionAssembly.UrduTitle,
    //							  Code = aa.Structure.Code,
    //							  EnglishTitle = s.UrduTitle,
    //							  MaleVoters = s.MaleVoters,
    //							  FemaleVoters = s.FemaleVoters,
    //							  TotalPollingStations = s.TotalPollingStations,
    //							  AssemblyId = aa.ElectionAssemblyId,
    //							  Division = p.UrduTitle,
    //							  Province = p.Province.UrduTitle,
    //							  District = "",
    //							  Town = "",
    //							  UC = ""
    //						  }).ToList();

    //	var resPA_dis = (from aa in dc.ElectionAssemblyConstituencies
    //						  join s in dc.Structures.OfType<ProvincialAssemblyHalka>() on aa.StructureId equals s.Id
    //						  join p in dc.Structures.OfType<District>() on s.DistrictId equals p.Id
    //						  where aa.ElectionAssembly.ElectionId == electionId &&
    //						  (electionAssemblyId == null || aa.ElectionAssemblyId == electionAssemblyId) &&
    //						  (districtId == null || p.Id == districtId)
    //						  select new ConstituencyDTO
    //						  {
    //							  Id = aa.StructureId,
    //							  Assembly = aa.ElectionAssembly.UrduTitle,
    //							  Code = aa.Structure.Code,
    //							  EnglishTitle = s.UrduTitle,
    //							  MaleVoters = s.MaleVoters,
    //							  FemaleVoters = s.FemaleVoters,
    //							  TotalPollingStations = s.TotalPollingStations,
    //							  AssemblyId = aa.ElectionAssemblyId,
    //							  District = p.UrduTitle,
    //							  Division = p.Division.UrduTitle,
    //							  Province = p.Division.Province.UrduTitle,
    //							  Town = "",
    //							  UC = ""
    //						  }).ToList();

    //	var resPA_town = (from aa in dc.ElectionAssemblyConstituencies
    //							join s in dc.Structures.OfType<ProvincialAssemblyHalka>() on aa.StructureId equals s.Id
    //							join p in dc.Structures.OfType<Town>() on s.DistrictId equals p.Id
    //							where aa.ElectionAssembly.ElectionId == electionId &&
    //							(electionAssemblyId == null || aa.ElectionAssemblyId == electionAssemblyId) &&
    //							(townId == null || p.Id == townId)
    //							select new ConstituencyDTO
    //							{
    //								Id = aa.StructureId,
    //								Assembly = aa.ElectionAssembly.UrduTitle,
    //								Code = aa.Structure.Code,
    //								EnglishTitle = s.UrduTitle,
    //								MaleVoters = s.MaleVoters,
    //								FemaleVoters = s.FemaleVoters,
    //								TotalPollingStations = s.TotalPollingStations,
    //								AssemblyId = aa.ElectionAssemblyId,
    //								Town = p.UrduTitle,
    //								District = p.District.UrduTitle,
    //								Division = p.District.Division.UrduTitle,
    //								Province = p.District.Division.Province.UrduTitle
    //							}).ToList();

    //	#endregion Provincial Assembly

    //	var k = resUC.Union(resWards).Union(resNA_pro).Union(resNA_div).Union(resNA_dis).Union(resNA_town)
    //										  .Union(resPA_pro).Union(resPA_div).Union(resPA_dis).Union(resPA_town)
    //										  .Distinct().ToList();

    //	return Task.FromResult(k);
    //}

    //public Task<List<ConstituencyDTO>> Search2(int electionId, int? electionAssemblyId, int? provinceId, int? divisionId, int? districtId, int? townId, int? unionCouncild, int? wardId)
    //{
    //	var resUC = (from aa in dc.Structures.OfType<UnionCouncil>()
    //					 join eac in dc.ElectionAssemblyConstituencies on aa.Id equals eac.StructureId
    //					 where aa.ElectionId == electionId &&
    //							 (electionAssemblyId == null || eac.ElectionAssemblyId == electionAssemblyId) &&
    //							 (provinceId == null || aa.Town.District.Division.ProvinceId == provinceId) &&
    //							 (divisionId == null || aa.Town.District.DivisionId == divisionId) &&
    //							 (districtId == null || aa.Town.DistrictId == districtId) &&
    //							 (townId == null || aa.TownId == townId) &&
    //							 (unionCouncild == null || aa.Id == unionCouncild)
    //					 select new ConstituencyDTO
    //					 {
    //						 Id = aa.Id,
    //						 Assembly = eac.ElectionAssembly.EnglishTitle,
    //						 Code = aa.Code,
    //						 EnglishTitle = aa.EnglishTitle,
    //						 MaleVoters = aa.MaleVoters,
    //						 FemaleVoters = aa.FemaleVoters,
    //						 TotalPollingStations = aa.TotalPollingStations
    //					 }).ToList();

    //	var resWard = (from aa in dc.Structures.OfType<Ward>()
    //						join eac in dc.ElectionAssemblyConstituencies on aa.Id equals eac.StructureId
    //						where aa.ElectionId == electionId &&
    //								(electionAssemblyId == null || eac.ElectionAssemblyId == electionAssemblyId) &&
    //								(provinceId == null || aa.UnionCouncil.Town.District.Division.ProvinceId == provinceId) &&
    //								(divisionId == null || aa.UnionCouncil.Town.District.DivisionId == divisionId) &&
    //								(districtId == null || aa.UnionCouncil.Town.DistrictId == districtId) &&
    //								(townId == null || aa.UnionCouncil.TownId == townId) &&
    //								(unionCouncild == null || aa.UnionCouncilId == unionCouncild) &&
    //								(wardId == null || aa.Id == wardId)
    //						select new ConstituencyDTO
    //						{
    //							Id = aa.Id,
    //							Code = aa.Code,
    //							EnglishTitle = aa.EnglishTitle,
    //							Assembly = eac.ElectionAssembly.EnglishTitle,
    //							MaleVoters = aa.MaleVoters,
    //							FemaleVoters = aa.FemaleVoters,
    //							TotalPollingStations = aa.TotalPollingStations
    //						}).ToList();

    //	#region National Assembly

    //	var na_pro = (from aa in dc.ElectionAssemblyConstituencies
    //					  orderby aa.Structure.EnglishTitle
    //					  where (electionAssemblyId == null || aa.ElectionAssemblyId == electionAssemblyId) &&
    //							  (aa.ElectionAssembly.ElectionId == electionId)
    //					  select new ConstituencyDTO
    //					  {
    //						  Id = aa.StructureId,
    //						  Code = aa.Structure.Code,
    //						  EnglishTitle = aa.Structure.EnglishTitle,
    //						  Assembly = aa.ElectionAssembly.EnglishTitle,
    //						  FemaleVoters = aa.Structure.FemaleVoters,
    //						  MaleVoters = aa.Structure.MaleVoters,
    //						  TotalPollingStations = aa.Structure.TotalPollingStations
    //					  }).ToList();

    //	//var na_pro = (from aa in dc.Structures.OfType<NationalAssemblyHalka>() // Get All National Assembly Constituencies
    //	//              join eac in dc.ElectionAssemblyConstituencies on aa.Id equals eac.StructureId // Which are in Election Assembly
    //	//              join str in dc.Structures.OfType<Province>() on aa.StructureId equals str.Id // and in under province structure
    //	//              where aa.ElectionId == electionId &&
    //	//              (electionAssemblyId == null || eac.ElectionAssemblyId == electionAssemblyId) &&
    //	//              (provinceId == null && str.Id == provinceId)
    //	//              select new ConstituencyDTO
    //	//              {
    //	//                 Id = aa.Id,
    //	//                 Code = aa.Code,
    //	//                 EnglishTitle = aa.EnglishTitle,
    //	//                 Assembly = eac.ElectionAssembly.EnglishTitle,
    //	//                 MaleVoters = aa.MaleVoters,
    //	//                 FemaleVoters = aa.FemaleVoters,
    //	//                 TotalPollingStations = aa.TotalPollingStations
    //	//              }).ToList();

    //	var na_div = (from aa in dc.Structures.OfType<NationalAssemblyHalka>() // Get All National Assembly Constituencies
    //					  join eac in dc.ElectionAssemblyConstituencies on aa.Id equals eac.StructureId // Which are in Election Assembly
    //					  join str in dc.Structures.OfType<Division>() on aa.DistrictId equals str.Id // and in under province structure
    //					  where aa.ElectionId == electionId &&
    //					  (electionAssemblyId == null || eac.ElectionAssemblyId == electionAssemblyId) &&
    //					  (provinceId == null && str.ProvinceId == provinceId) &&
    //					  (divisionId == null && str.Id == divisionId)
    //					  select new ConstituencyDTO
    //					  {
    //						  Id = aa.Id,
    //						  Code = aa.Code,
    //						  EnglishTitle = aa.EnglishTitle,
    //						  Assembly = eac.ElectionAssembly.EnglishTitle,
    //						  MaleVoters = aa.MaleVoters,
    //						  FemaleVoters = aa.FemaleVoters,
    //						  TotalPollingStations = aa.TotalPollingStations
    //					  }).ToList();

    //	var na_dis = (from aa in dc.Structures.OfType<NationalAssemblyHalka>() // Get All National Assembly Constituencies
    //					  join eac in dc.ElectionAssemblyConstituencies on aa.Id equals eac.StructureId // Which are in Election Assembly
    //					  join str in dc.Structures.OfType<District>() on aa.DistrictId equals str.Id // and in under province structure
    //					  where aa.ElectionId == electionId &&
    //					  (electionAssemblyId == null || eac.ElectionAssemblyId == electionAssemblyId) &&
    //					  (provinceId == null && str.Division.ProvinceId == provinceId) &&
    //					  (divisionId == null && str.Division.Id == divisionId) &&
    //					  (districtId == null && str.Id == districtId)
    //					  select new ConstituencyDTO
    //					  {
    //						  Id = aa.Id,
    //						  Code = aa.Code,
    //						  EnglishTitle = aa.EnglishTitle,
    //						  Assembly = eac.ElectionAssembly.EnglishTitle,
    //						  MaleVoters = aa.MaleVoters,
    //						  FemaleVoters = aa.FemaleVoters,
    //						  TotalPollingStations = aa.TotalPollingStations
    //					  }).ToList();

    //	var na_town = (from aa in dc.Structures.OfType<NationalAssemblyHalka>() // Get All National Assembly Constituencies
    //						join eac in dc.ElectionAssemblyConstituencies on aa.Id equals eac.StructureId // Which are in Election Assembly
    //						join str in dc.Structures.OfType<Town>() on aa.DistrictId equals str.Id // and in under province structure
    //						where aa.ElectionId == electionId &&
    //						(electionAssemblyId == null || eac.ElectionAssemblyId == electionAssemblyId) &&
    //						(provinceId == null && str.District.Division.ProvinceId == provinceId) &&
    //						(divisionId == null && str.District.Division.Id == divisionId) &&
    //						(districtId == null && str.District.Id == districtId)
    //						select new ConstituencyDTO
    //						{
    //							Id = aa.Id,
    //							Code = aa.Code,
    //							EnglishTitle = aa.EnglishTitle,
    //							Assembly = eac.ElectionAssembly.EnglishTitle,
    //							MaleVoters = aa.MaleVoters,
    //							FemaleVoters = aa.FemaleVoters,
    //							TotalPollingStations = aa.TotalPollingStations
    //						}).ToList();

    //	var na_UC = (from aa in dc.Structures.OfType<NationalAssemblyHalka>() // Get All National Assembly Constituencies
    //					 join eac in dc.ElectionAssemblyConstituencies on aa.Id equals eac.StructureId // Which are in Election Assembly
    //					 join str in dc.Structures.OfType<UnionCouncil>() on aa.DistrictId equals str.Id // and in under province structure
    //					 where aa.ElectionId == electionId &&
    //					 (electionAssemblyId == null || eac.ElectionAssemblyId == electionAssemblyId) &&
    //					 (provinceId == null && str.Town.District.Division.ProvinceId == provinceId) &&
    //					 (divisionId == null && str.Town.District.Division.Id == divisionId) &&
    //					 (districtId == null && str.Town.District.Id == districtId) &&
    //					 (townId == null && str.Town.Id == townId) &&
    //					 (unionCouncild == null && str.Id == unionCouncild)
    //					 select new ConstituencyDTO
    //					 {
    //						 Id = aa.Id,
    //						 Code = aa.Code,
    //						 EnglishTitle = aa.EnglishTitle,
    //						 Assembly = eac.ElectionAssembly.EnglishTitle,
    //						 MaleVoters = aa.MaleVoters,
    //						 FemaleVoters = aa.FemaleVoters,
    //						 TotalPollingStations = aa.TotalPollingStations
    //					 }).ToList();

    //	#endregion National Assembly

    //	#region Provincial Assembly

    //	var pa_pro = (from aa in dc.Structures.OfType<ProvincialAssemblyHalka>() // Get All National Assembly Constituencies
    //					  join eac in dc.ElectionAssemblyConstituencies on aa.Id equals eac.StructureId // Which are in Election Assembly
    //					  join str in dc.Structures.OfType<Province>() on aa.DistrictId equals str.Id // and in under province structure
    //					  where aa.ElectionId == electionId &&
    //					  (electionAssemblyId == null || eac.ElectionAssemblyId == electionAssemblyId) &&
    //					  (provinceId == null && str.Id == provinceId)
    //					  select new ConstituencyDTO
    //					  {
    //						  Id = aa.Id,
    //						  Code = aa.Code,
    //						  EnglishTitle = aa.EnglishTitle,
    //						  Assembly = eac.ElectionAssembly.EnglishTitle,
    //						  MaleVoters = aa.MaleVoters,
    //						  FemaleVoters = aa.FemaleVoters,
    //						  TotalPollingStations = aa.TotalPollingStations
    //					  }).ToList();

    //	var pa_div = (from aa in dc.Structures.OfType<ProvincialAssemblyHalka>() // Get All National Assembly Constituencies
    //					  join eac in dc.ElectionAssemblyConstituencies on aa.Id equals eac.StructureId // Which are in Election Assembly
    //					  join str in dc.Structures.OfType<Division>() on aa.DistrictId equals str.Id // and in under province structure
    //					  where aa.ElectionId == electionId &&
    //					  (electionAssemblyId == null || eac.ElectionAssemblyId == electionAssemblyId) &&
    //					  (provinceId == null && str.ProvinceId == provinceId) &&
    //					  (divisionId == null && str.Id == divisionId)
    //					  select new ConstituencyDTO
    //					  {
    //						  Id = aa.Id,
    //						  Code = aa.Code,
    //						  EnglishTitle = aa.EnglishTitle,
    //						  Assembly = eac.ElectionAssembly.EnglishTitle,
    //						  MaleVoters = aa.MaleVoters,
    //						  FemaleVoters = aa.FemaleVoters,
    //						  TotalPollingStations = aa.TotalPollingStations
    //					  }).ToList();
    //public Task<string> DeleteStructureOld(int structureId, int assemblyId)
    //{
    //	using (var tran = dc.Database.BeginTransaction())
    //	{
    //		try
    //		{
    //			var q = (from aa in dc.ElectionAssemblyConstituencies
    //						where aa.ElectionAssemblyId == assemblyId && aa.StructureId == structureId
    //						select aa).FirstOrDefault();

    //			if (q != null)
    //			{
    //				dc.ElectionAssemblyConstituencies.Remove(q);
    //				dc.SaveChanges();

    public Task<CreateStructureDTO> GetConstituencyInfoForEdit(int id, int phaseId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var obj = dc.Structures.Find(id);
        if (obj.GetType() == typeof(NationalAssemblyHalka))
        {
            var q = (from aa in dc.Structures.OfType<NationalAssemblyHalka>()
                where aa.Id == id
                select new CreateStructureDTO
                {
                    Id = aa.Id,
                    Area = aa.Area,
                    AverageHouseholdIncome = aa.AverageHouseholdIncome,
                    Castes = aa.Castes,
                    Code = aa.Code,
                    ConstTypeId = (int)StructureType.NA,
                    StructureType = StructureType.NA,
                    DistrictId = aa.DistrictId,
                    DivisionId = aa.District.DivisionId,
                    EnglishTitle = aa.EnglishTitle,
                    ElectionId = aa.ElectionId,
                    ElectionAssemblyId = aa.AssemblyId.Value,
                    //FemaleVoters = aa.FemaleVoters,
                    //GeneralTrivia = aa.GeneralTrivia,
                    //ImportantAreas = aa.ImportantAreas,
                    //ImportantPoliticalPersonalities = aa.ImportantPoliticalPersonalities,
                    //Languages = aa.Languages,
                    //LiteracyRate = aa.LiteracyRate,
                    //MajorityIncomeSource = aa.MajorityIncomeSource,
                    //MaleVoters = aa.MaleVoters,
                    ParentId = aa.DistrictId,
                    //Population = aa.Population,
                    //Problems = aa.Problems,
                    ProvinceId = aa.District.Division.ProvinceId,
                    //RuralAreaPer = aa.RuralAreaPer,
                    //TotalPollingStations = aa.TotalPollingStations,
                    UrduTitle = aa.UrduTitle,
                    //UrbanAreaPer = aa.UrbanAreaPer,
                    //Trivia = aa.Trivia,
                    IsPostponed = aa.IsPostponed,
                    HasBilaMokablia = aa.HasBilaMokablia
                }).FirstOrDefault();
            q = GetPollingScheme(dc, q, id, phaseId);
            return Task.FromResult(q);
        }

        if (obj.GetType() == typeof(ProvincialAssemblyHalka))
        {
            var q = (from aa in dc.Structures.OfType<ProvincialAssemblyHalka>()
                where aa.Id == id
                select new CreateStructureDTO
                {
                    Id = aa.Id,
                    //Area = aa.Area,
                    //AverageHouseholdIncome = aa.AverageHouseholdIncome,
                    //Castes = aa.Castes,
                    Code = aa.Code,
                    ConstTypeId = (int)StructureType.PA,
                    StructureType = StructureType.PA,
                    DistrictId = aa.DistrictId,
                    DivisionId = aa.District.DivisionId,
                    EnglishTitle = aa.EnglishTitle,
                    ElectionId = aa.ElectionId,
                    ElectionAssemblyId = aa.AssemblyId.Value,
                    //FemaleVoters = aa.FemaleVoters,
                    //GeneralTrivia = aa.GeneralTrivia,
                    //ImportantAreas = aa.ImportantAreas,
                    //ImportantPoliticalPersonalities = aa.ImportantPoliticalPersonalities,
                    //Languages = aa.Languages,
                    //LiteracyRate = aa.LiteracyRate,
                    //MajorityIncomeSource = aa.MajorityIncomeSource,
                    //MaleVoters = aa.MaleVoters,
                    //Population = aa.Population,
                    //Problems = aa.Problems,
                    //RuralAreaPer = aa.RuralAreaPer,
                    //TotalPollingStations = aa.TotalPollingStations,
                    //UrbanAreaPer = aa.UrbanAreaPer,
                    //Trivia = aa.Trivia,
                    ParentId = aa.DistrictId,
                    ProvinceId = aa.District.Division.ProvinceId,
                    UrduTitle = aa.UrduTitle,
                    IsPostponed = aa.IsPostponed
                }).FirstOrDefault();
            q = GetPollingScheme(dc, q, id, phaseId);
            return Task.FromResult(q);
        }

        if (obj.GetType() == typeof(UnionCouncil))
        {
            var q = (from aa in dc.Structures.OfType<UnionCouncil>()
                where aa.Id == id
                select new CreateStructureDTO
                {
                    Id = aa.Id,
                    //Area = aa.Area,
                    //AverageHouseholdIncome = aa.AverageHouseholdIncome,
                    //Castes = aa.Castes,
                    Code = aa.Code,
                    ConstTypeId = (int)StructureType.UnionCouncil,
                    StructureType = StructureType.UnionCouncil,
                    DistrictId = aa.Town.DistrictId,
                    DivisionId = aa.Town.District.DivisionId,
                    EnglishTitle = aa.EnglishTitle,
                    ElectionId = aa.ElectionId,
                    ElectionAssemblyId = aa.AssemblyId.Value,
                    //FemaleVoters = aa.FemaleVoters,
                    //GeneralTrivia = aa.GeneralTrivia,
                    //ImportantAreas = aa.ImportantAreas,
                    //ImportantPoliticalPersonalities = aa.ImportantPoliticalPersonalities,
                    //Languages = aa.Languages,
                    //LiteracyRate = aa.LiteracyRate,
                    //MajorityIncomeSource = aa.MajorityIncomeSource,
                    //MaleVoters = aa.MaleVoters,
                    ParentId = aa.TownId,
                    //Population = aa.Population,
                    //Problems = aa.Problems,
                    ProvinceId = aa.Town.District.Division.ProvinceId,
                    //RuralAreaPer = aa.RuralAreaPer,
                    //TotalPollingStations = aa.TotalPollingStations,
                    UrduTitle = aa.UrduTitle,
                    //UrbanAreaPer = aa.UrbanAreaPer,
                    //Trivia = aa.Trivia,
                    TownId = aa.TownId,
                    TotalWards = aa.Wards.Count(),
                    UCId = aa.Id,
                    IsPostponed = aa.IsPostponed
                }).FirstOrDefault();
            q = GetPollingScheme(dc, q, id, phaseId);
            return Task.FromResult(q);
        }

        if (obj.GetType() == typeof(Ward))
        {
            var q = (from aa in dc.Structures.OfType<Ward>()
                where aa.Id == id
                select new CreateStructureDTO
                {
                    Id = aa.Id,
                    //Area = aa.Area,
                    //AverageHouseholdIncome = aa.AverageHouseholdIncome,
                    //Castes = aa.Castes,
                    Code = aa.Code,
                    ConstTypeId = (int)StructureType.Ward,
                    StructureType = StructureType.Ward,
                    DistrictId = aa.UnionCouncil.Town.DistrictId,
                    DivisionId = aa.UnionCouncil.Town.District.DivisionId,
                    EnglishTitle = aa.EnglishTitle,
                    ElectionId = aa.ElectionId,
                    ElectionAssemblyId = aa.AssemblyId.Value,
                    //FemaleVoters = aa.FemaleVoters,
                    //GeneralTrivia = aa.GeneralTrivia,
                    //ImportantAreas = aa.ImportantAreas,
                    //ImportantPoliticalPersonalities = aa.ImportantPoliticalPersonalities,
                    //Languages = aa.Languages,
                    //LiteracyRate = aa.LiteracyRate,
                    //MajorityIncomeSource = aa.MajorityIncomeSource,
                    //MaleVoters = aa.MaleVoters,
                    ParentId = aa.UnionCouncilId,
                    //Population = aa.Population,
                    //Problems = aa.Problems,
                    ProvinceId = aa.UnionCouncil.Town.District.Division.ProvinceId,
                    //RuralAreaPer = aa.RuralAreaPer,
                    //TotalPollingStations = aa.TotalPollingStations,
                    UrduTitle = aa.UrduTitle,
                    //UrbanAreaPer = aa.UrbanAreaPer,
                    //Trivia = aa.Trivia,
                    TownId = aa.UnionCouncil.TownId,
                    UCId = aa.UnionCouncilId,
                    IsPostponed = aa.IsPostponed
                }).FirstOrDefault();
            q = GetPollingScheme(dc, q, id, phaseId);
            return Task.FromResult(q);
        }

        throw new Exception("Not supported constituency type");
    }

    private CreateStructureDTO GetPollingScheme(ApplicationDbContext dc, CreateStructureDTO ps, int id, int phaseId)
    {
        var obj = (from a in dc.PollingSchemes
            where a.StructureId == id && a.PhaseId == phaseId
            select a).FirstOrDefault();
        if (obj != null)
        {
            ps.MaleVoters = obj.MaleVoters;
            ps.YouthVoters = obj.YouthVoters;
            ps.FemaleVoters = obj.FemaleVoters;
            ps.TotalPollingStations = obj.TotalPollingStations;
            ps.Languages = (obj.Languages ?? "").Trim();
            ps.Castes = (obj.Castes ?? "").Trim();
            ps.Trivia = (obj.Trivia ?? "").Trim();
            ps.GeneralTrivia = (obj.GeneralTrivia ?? "").Trim();
            ps.ImportantPoliticalPersonalities = (obj.ImportantPoliticalPersonalities ?? "").Trim();
            ps.Problems = (obj.Problems ?? "").Trim();
            ps.ImportantAreas = (obj.ImportantAreas ?? "").Trim();
            ps.RuralAreaPer = obj.RuralAreaPer;
            ps.UrbanAreaPer = obj.UrbanAreaPer;
            ps.Population = obj.Population;
            ps.MajorityIncomeSource = (obj.MajorityIncomeSource ?? "").Trim();
            ps.LiteracyRate = (obj.LiteracyRate ?? "").Trim();
            ps.AverageHouseholdIncome = (obj.AverageHouseholdIncome ?? "").Trim();
            ps.Area = (obj.Area ?? "").Trim();
            ps.Profile = (obj.Profile ?? "").Trim();
        }

        return ps;
    }

    public Task<string> DeleteConstituency(int id, int eaID)
    {
        using var dc = _contextFactory.CreateDbContext();
        using (var tran = dc.Database.BeginTransaction())
        {
            var q = (from aa in dc.Structures
                    where aa.AssemblyId == eaID &&
                          aa.Id == id
                    select aa
                ).FirstOrDefault();
            if (q != null)
                try
                {
                    var nomExist = (from aa in dc.Nominations
                        where aa.StructureId == q.Id
                        select aa).Any();
                    if (nomExist) return Task.FromResult("One or more nomination exist on this constituency");

                    var existAsParent = (from aa in dc.Structures
                        where aa.PrevStructureId == id
                        select aa).Any();

                    if (existAsParent) return Task.FromResult("This constituency has linked constituency");

                    // constituency mappings
                    var cms = (from aa in dc.StructureMappings
                        where aa.StructureId == id
                        select aa).ToList();
                    dc.StructureMappings.RemoveRange(cms);
                    dc.SaveChanges();

                    // user constituencies
                    var ucs = (from aa in dc.UserConstituencies
                        where aa.StructureId == id
                        select aa).ToList();
                    dc.UserConstituencies.RemoveRange(ucs);
                    dc.SaveChanges();

                    // Remove structure
                    dc.Structures.RemoveRange(q);
                    dc.SaveChanges();

                    tran.Commit();
                    return Task.FromResult("OK");
                }
                catch (Exception)
                {
                    tran.Rollback();
                    throw;
                }

            return Task.FromResult("Record not found");
        }
    }

    public Task<List<GeneralItemDTO>> GetProviceDistricts(int provinceId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.Structures.OfType<District>()
            where aa.Division.ProvinceId == provinceId
            orderby aa.EnglishTitle
            select new GeneralItemDTO
            {
                Id = aa.Id,
                EnglishTitle = aa.EnglishTitle, // + " - " + aa.Division.EnglishTitle + " Division",
                UrduTitle = aa.UrduTitle // + " - " + aa.Division.UrduTitle + " ڈویژن"
            }).ToList();
        return Task.FromResult(q);
    }


    public Task<List<GeneralItemDTO>> GetThisElectionAssemblies(int electionId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.ElectionAssemblies
            where aa.ElectionId == electionId
            orderby aa.EnglishTitle
            select new GeneralItemDTO
            {
                Id = aa.Id,
                EnglishTitle = aa.EnglishTitle,
                UrduTitle = aa.UrduTitle
            }).ToList();

        return Task.FromResult(q);
    }

    public Task<List<GeneralItemDTO>> GetAllDistricts(int stateElectionId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (
            from aa in dc.Structures.OfType<District>()
            where aa.ElectionId == stateElectionId
            orderby aa.EnglishTitle
            select new GeneralItemDTO
            {
                Id = aa.Id,
                EnglishTitle = aa.EnglishTitle + " (" + aa.Division.Province.EnglishTitle + ")"
            }).ToList();
        return Task.FromResult(q);
    }

    public Task<string> AssignAssemblyToConstituency(int? selectedAssemblyId, List<ConstituencyDTO> constiuencies,
        string user)
    {
        using var dc = _contextFactory.CreateDbContext();
        var asmSEatTypes = (
            from aa in dc.ElectionAssemblySeats
            where aa.ElectionAssemblyId == selectedAssemblyId
            select aa.ConstituencyType).ToList();

        if (selectedAssemblyId != null && constiuencies.Any())
        {
            foreach (var c in constiuencies)
            {
                if (asmSEatTypes.Contains(StructureType.Town))
                {
                    var q = (from aa in dc.Structures.OfType<Town>()
                        where aa.Id == c.Id
                        select aa).FirstOrDefault();
                    if (q != null)
                    {
                        q.AssemblyId = selectedAssemblyId;
                        q.ModifiedBy = user;
                        q.ModifiedDate = DateTime.Now;
                        dc.SaveChanges();
                    }
                }

                if (asmSEatTypes.Contains(StructureType.UnionCouncil))
                {
                    var q = (from aa in dc.Structures.OfType<UnionCouncil>()
                        where aa.Id == c.Id
                        select aa).FirstOrDefault();
                    if (q != null)
                    {
                        q.AssemblyId = selectedAssemblyId;
                        q.ModifiedBy = user;
                        q.ModifiedDate = DateTime.Now;
                        dc.SaveChanges();
                    }
                }

                if (asmSEatTypes.Contains(StructureType.Ward))
                {
                    var q = (from aa in dc.Structures.OfType<Ward>()
                        where aa.Id == c.Id
                        select aa).FirstOrDefault();
                    if (q != null)
                    {
                        q.AssemblyId = selectedAssemblyId;
                        q.ModifiedBy = user;
                        q.ModifiedDate = DateTime.Now;
                        dc.SaveChanges();
                    }
                }
            }

            return Task.FromResult("OK");
        }

        return Task.FromResult("No record found");
    }

    public Task<List<ConstituencyDTO>> GetThisElectionConstituencies(int electionId, int districtId)
    {
        using var dc = _contextFactory.CreateDbContext();
        using var con = dc.Database.GetDbConnection();
        var pars = new { ElectionId = electionId, DistrictId = districtId };
        var res = con
            .Query<ConstituencyDTO>("ep_GetElectionConstituencies", pars, commandType: CommandType.StoredProcedure)
            .ToList();
        return Task.FromResult(res);
    }

    public async Task<string> SaveConstituencyBasicInfo(BasicConstituencyInfoDTO basicInfo, string user)
    {
        using var dc = _contextFactory.CreateDbContext();


        var q = await (from aa in dc.Structures
            where aa.Id == basicInfo.Id
            select aa).FirstOrDefaultAsync();
        if (q != null)
        {
            q.Code = basicInfo.Code;
            q.EnglishTitle = basicInfo.EnglishTitle;
            q.UrduTitle = basicInfo.UrduTitle;
            q.MaleVoters = basicInfo.MaleVoters;
            q.FemaleVoters = basicInfo.FemaleVoters;
            q.TotalPollingStations = basicInfo.TotalPollingStations;
            q.Population = basicInfo.Population;
            q.ImportantAreas = basicInfo.ImportantAreas;
            q.Problems = basicInfo.Problems;
            q.Trivia = basicInfo.Trivia;
            q.GeneralTrivia = basicInfo.GeneralTrivia;
            q.ModifiedBy = user;
            q.ModifiedDate = DateTime.Now;
            try
            {
                await dc.SaveChangesAsync();
                return await Task.FromResult("OK");
            }
            catch (Exception e)
            {
                return await Task.FromResult(e.Message);
            }
        }

        return await Task.FromResult("Record Not Found");
    }


    public async Task<BasicConstituencyInfoDTO> GetBasicConstituencyInfo(int id)
    {
        using var dc = _contextFactory.CreateDbContext();

        await using var con = dc.Database.GetDbConnection();

        var prams = new { Id = id };
        var res = con.Query<BasicConstituencyInfoDTO>("ep_GetConstituencyBasicInfo", prams,
            commandType: CommandType.StoredProcedure).FirstOrDefault();
        return await Task.FromResult(res);
    }


    #region Namanigar

    public Task<List<NamanigarDTO>> GetAllNamanigars()
    {
        using var dc = _contextFactory.CreateDbContext();
        var obj = (from aa in dc.Namanigars
            orderby aa.Name
            select new NamanigarDTO

            {
                Id = aa.Id,
                Name = $"{aa.Code} - {aa.Name}",
                Code = aa.Code,
                Email = aa.Email,
                Phone = aa.Phone
            }).ToList();
        return Task.FromResult(obj);
    }

    public Task<NamanigarDTO> GetNamanigarById(int id, int constituencyId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var obj = (from aa in dc.NamanigarConstituencies
            where aa.NamanigarId == id && aa.ConstituencyId == constituencyId
            select new NamanigarDTO
            {
                Id = aa.NamanigarId,
                Code = aa.Namanigar.Code,
                Email = aa.Namanigar.Email,
                Phone = aa.Namanigar.Phone,
                Name = aa.Namanigar.Name,
                PSDetail = aa.PSDetail,
                District = aa.Namanigar.District
            }).FirstOrDefault();

        if (obj == null)
            obj = (from aa in dc.Namanigars
                where aa.Id == id
                select new NamanigarDTO
                {
                    Id = aa.Id,
                    Code = aa.Code,
                    ConstituencyId = constituencyId,
                    Email = aa.Email,
                    Name = aa.Name,
                    Phone = aa.Phone,
                    PSDetail = string.Empty,
                    District = aa.District
                }).FirstOrDefault();
        return Task.FromResult(obj);
    }

    public Task<string> AddNamanigarToConstituency(NamanigarDTO obj, string user)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.NamanigarConstituencies
                where aa.NamanigarId == obj.Id &&
                      aa.ConstituencyId == obj.ConstituencyId
                select aa
            ).FirstOrDefault();

        if (q == null)
        {
            q = new NamanigarConstituency
            {
                ConstituencyId = obj.ConstituencyId,
                CreatedBy = user,
                CreatedDate = DateTime.Now,
                NamanigarId = (int)obj.Id,
                PSDetail = obj.PSDetail
            };
            dc.NamanigarConstituencies.Add(q);
            dc.SaveChanges();
            return Task.FromResult("OK");
        }

        q.PSDetail = obj.PSDetail;
        q.ModifiedBy = user;
        q.ModifiedDate = DateTime.Now;
        dc.SaveChanges();
        return Task.FromResult("OK");
    }

    public Task<List<NamanigarDTO>> GetConstituencyNamanigars(int constituencyId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.NamanigarConstituencies
                where aa.ConstituencyId == constituencyId
                orderby aa.Namanigar.Name
                select new NamanigarDTO
                {
                    Id = aa.Namanigar.Id,
                    Name = aa.Namanigar.Name,
                    Code = aa.Namanigar.Code,
                    Email = aa.Namanigar.Email,
                    ConstituencyId = aa.ConstituencyId,
                    Phone = aa.Namanigar.Phone,
                    PSDetail = aa.PSDetail,
                    District = aa.Namanigar.District
                }
            ).ToList();
        return Task.FromResult(q);
    }

    public Task<string> RemoveNamanigarFromConstituency(int namanigarId, int constituencyId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.NamanigarConstituencies
            where aa.NamanigarId == namanigarId &&
                  aa.ConstituencyId == constituencyId
            select aa).FirstOrDefault();
        if (q != null)
        {
            dc.NamanigarConstituencies.Remove(q);
            dc.SaveChanges();
            return Task.FromResult("OK");
        }

        return Task.FromResult("Record not found");
    }

    #endregion Namanigar
}