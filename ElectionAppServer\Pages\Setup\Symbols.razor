﻿@page "/symbols"
@using ButtonType = MudBlazor.ButtonType
@using FilterType = Syncfusion.Blazor.Grids.FilterType
@using Size = MudBlazor.Size
@inherits OwningComponentBase<SymbolService>
@*<PageCaption Title="Symbols"></PageCaption>*@
<MudText Typo="Typo.h5">Symbols</MudText>

<SfDialog Width="500px" @bind-Visible="@isDlgVisible" @ref="dlgForm" IsModal="true" ShowCloseIcon="true">
    <DialogEvents OnOpen="beforeOpen"></DialogEvents>
    <DialogTemplates>
        <Header>@FormTitle</Header>
        <Content>
            <EditForm Model="@selectedObj" OnValidSubmit="@SaveSymbolData">
                <DataAnnotationsValidator/>
                <div class="row">
                    <div class="input-field col ">
                        <MudTextField @bind-Value="selectedObj.English" Label="Title (English)"
                                      OnBlur="TranslateToUrdu" For="@(() => selectedObj.English)"/>
                    </div>
                    <div class="input-field col ">
                        <MudTextField @bind-Value="selectedObj.Urdu" Label="Title (اردو)"
                                      Style="direction:rtl"
                                      For="@(() => selectedObj.Urdu)"/>
                    </div>
                </div>
                <div class="row">
                    <div class="input-field col  ">
                        <BlazorInputFile.InputFile OnChange="HandleFileSelected"/>
                    </div>
                    <div class="input-field col  ">
                        <img width="160px" alt="" src="data:image/jpg;base64,@FileBase64String"/>
                    </div>
                </div>
                <div class="row">
                    <div class="col ">
                        <MudButton ButtonType="ButtonType.Submit"
                                   Color="Color.Primary"
                                   Size="Size.Small"
                                   Variant="Variant.Filled"
                                   StartIcon="@Icons.Material.Filled.Save"
                                   Class="mt-1 mb-1"
                                   Disabled="@IsDisable" IsPrimary="true">
                            @SaveButtonText
                        </MudButton>
                        @*<p>@status</p>*@
                    </div>
                </div>
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>
<SfToast @ref="ToastObj" Title="Alert" ShowProgressBar="true" Timeout="3000">
    <ToastPosition X="Right" Y="Bottom"></ToastPosition>
</SfToast>
<!-- AuthorizeView allows us to only show sections of the page -->
<!-- based on the security on the current user -->
<AuthorizeView>
    <!-- Show this section if the user is logged in -->
    <Authorized>
        <section>
            @if (objList == null)
            {
                <p>
                    <em>Loading...</em>
                </p>
            }
            else
            {
                <MudButton Class="mb-2" OnClick="OpenCreateForm"
                           Color="Color.Primary"
                           Size="Size.Small"
                           Variant="Variant.Filled"
                           StartIcon="@Icons.Material.Filled.Add">
                    Create Symbol
                </MudButton>
                <div class="row">
                    <div class="col">
                        <SfGrid DataSource="objList" TValue="SymbolDTO" AllowFiltering="true"
                                EnableVirtualization="true"
                                AllowSorting="true" @ref="Grid" AllowTextWrap="true" Width="100%" Height="calc(100vh - 192px)">
                            <GridFilterSettings Type="FilterType.Excel"></GridFilterSettings>
                            <GridEvents QueryCellInfo="CustomizeCell" TValue="SymbolDTO"></GridEvents>
                            <GridColumns>
                                <GridColumn Field="Id" HeaderText="Id" AutoFit="true"></GridColumn>
                                <GridColumn Width="210px" HeaderText="Name (Urdu)" Field="Urdu" TextAlign="TextAlign.Right"></GridColumn>
                                <GridColumn Width="210px" HeaderText="Name (English)" Field="English"></GridColumn>
                                <GridColumn HeaderText="Picture" Width="130px" TextAlign="TextAlign.Center">
                                    <Template Context="cc">
                                        @{
                                            var obj = cc as SymbolDTO;
                                            var img = $"/media/symbols/{obj.Id}.jpg";
                                            <img src="@img" alt="@obj.English" style="max-width:100px; max-height:100px;"/>
                                        }
                                    </Template>
                                </GridColumn>
                                <GridColumn AutoFit="true" HeaderText="Audit Info">
                                    <Template Context="kk">
                                        @{
                                            var oo = kk as SymbolDTO;
                                        <div style="font-size:11px">
                                            <span>Created By: @oo.CreatedBy</span><span> On @oo.CreatedOn</span>
                                            @if (!string.IsNullOrEmpty(oo.ModifiedBy))
                                            {
                                                <br/>
                                                <span>Modified By: @oo.ModifiedBy</span>
                                                <span> On @oo.ModifiedOn</span>
                                            }
                                        </div>}
                                    </Template>
                                </GridColumn>
                                <GridColumn HeaderText="Action" Width="120px">
                                    <Template Context="cc">
                                        @{
                                            var s = cc as SymbolDTO;
                                            <MudFab Size="Size.Small" Color="Color.Error" StartIcon="@Icons.Material.Filled.Delete" OnClick="@(() => DeleteRecord(s.Id))">
                                                <i class="fas fa-trash-alt"></i>
                                            </MudFab>
                                            <MudFab Size="Size.Small" Color="Color.Primary" StartIcon="@Icons.Material.Filled.Edit" OnClick="@(() => OpenEditForm(s))">
                                                <i class="fas fa-pencil-alt"></i>
                                            </MudFab>
                                        }
                                    </Template>
                                </GridColumn>
                            </GridColumns>
                        </SfGrid>
                    </div>
                </div>
                @*<div class="row">
                                 @foreach (var s in objList)
                                 {
                                      string uu = "";
                                      if (!string.IsNullOrEmpty(s.ChangeKey))
                                            uu = s.URL + "?" + s.ChangeKey;
                                      else
                                            uu = s.URL;
                                      <div class="card m-1" style="width: 8rem;">
                                            <div class="card-body p-1">
                                                 <SfButton IsPrimary="true" CssClass="e-warning" OnClick="@(()=>DeleteRecord(s.Id))"><i class="fas fa-trash-alt"></i></SfButton>
                                                 <SfButton IsPrimary="true" CssClass="e-info" OnClick="@(()=>OpenEditForm(s))"><i class="fas fa-pencil-alt"></i></SfButton>
                                                 <p class="card-title"><b>@s.English</b></p>
                                                 <p class="card-text">@s.Urdu</p>
                                            </div>
                                            <img src="@uu" class="card-img-top" alt="...">
                                      </div>
                                 }
                            </div>*@
            }
        </section>
    </Authorized>
    <!-- Show this section if the user is not logged in -->
    <NotAuthorized>
        <p>You're not signed in.</p>
    </NotAuthorized>
</AuthorizeView>