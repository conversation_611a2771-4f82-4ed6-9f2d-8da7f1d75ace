﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ElectionAppServer.DTO;
using ElectionAppServer.Model;
using Microsoft.EntityFrameworkCore;

namespace ElectionAppServer.Data;

public class DistrictService
{
    private readonly IDbContextFactory<ApplicationDbContext>
        _contextFactory; // private  readonly  ApplicationDbContext dc;

    public DistrictService(IDbContextFactory<ApplicationDbContext> contextFactory) // (ApplicationDbContext  context)
    {
        _contextFactory = contextFactory; // dc  =  context;
    }

    public Task<List<GeneralItemDTO>> GetList(int divisionId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.Structures.OfType<District>()
            orderby aa.EnglishTitle
            where aa.DivisionId == divisionId
            select new GeneralItemDTO
                { Id = aa.Id, EnglishTitle = aa.EnglishTitle, UrduTitle = aa.UrduTitle }).ToList();
        return Task.FromResult(q);
    }

    public Task<District> CreateDistrict(District obj)
    {
        using var dc = _contextFactory.CreateDbContext();
        dc.Structures.Add(obj);
        dc.SaveChanges();
        return Task.FromResult(obj);
    }

    public Task<District> UpdateDistrict(District obj)
    {
        using var dc = _contextFactory.CreateDbContext();
        var st = (from aa in dc.Structures.OfType<District>() where aa.Id == obj.Id select aa).FirstOrDefault();
        if (st != null)
        {
            st.UrduTitle = obj.UrduTitle.Trim();
            st.EnglishTitle = obj.EnglishTitle.Trim();
            st.ModifiedBy = obj.ModifiedBy;
            st.ModifiedDate = DateTime.Now;
            st.DivisionId = obj.DivisionId;
        }

        dc.SaveChanges();
        return Task.FromResult(obj);
    }

    public async Task<District> DeleteDistrict(int id)
    {
        await using var dc = _contextFactory.CreateDbContext();
        var st = await (from aa in dc.Structures.OfType<District>() where aa.Id == id select aa).FirstOrDefaultAsync();
        dc.Structures.Remove(st);
        await dc.SaveChangesAsync();
        return await Task.FromResult(st);
    }

    public async Task<string> GetDivsionName(int divisionId)
    {
        using var dc = _contextFactory.CreateDbContext();
        try
        {
            var rs = dc.Structures.OfType<Division>().FirstOrDefault(c => c.Id == divisionId).EnglishTitle;
            return await Task.FromResult(rs);
        }
        catch (Exception)
        {
            return string.Empty;
        }
    }

    public async Task<string> GetDivisionProvince(int divisionId)
    {
        await using var dc = _contextFactory.CreateDbContext();
        var rs = (from aa in dc.Structures.OfType<Division>()
                //join bb in dc.Provinces on aa.ProvinceId equals bb.Id
                where aa.Id == divisionId
                select aa)
            .Include("Province")
            .FirstOrDefault();

        //var rs = dc.Divisions.FirstOrDefault(c => c.Id == divisionId).Province.EnglishTitle;
        //return await Task.FromResult(rs);
        return await Task.FromResult(rs.Province.EnglishTitle);
    }

    public async Task<int> GetDivisionProvinceId(int divisionId)
    {
        await using var dc = _contextFactory.CreateDbContext();
        var rs = (from aa in dc.Structures.OfType<Division>() where aa.Id == divisionId select aa)
            .FirstOrDefault();

        return await Task.FromResult(rs.ProvinceId);
    }

    public Task<List<GeneralItemDTO>> GetAllStructure(int ElectionId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q1 = (from aa in dc.Structures.OfType<Province>()
            orderby aa.EnglishTitle
            where aa.ElectionId == ElectionId
            select new GeneralItemDTO
            {
                Id = aa.Id,
                EnglishTitle = aa.EnglishTitle + " (Province)",
                UrduTitle = $"/administrativeunits/{ElectionId}/{aa.Id}"
            }).ToList();

        var q1a = (from aa in dc.Structures.OfType<Province>()
            orderby aa.UrduTitle
            where aa.ElectionId == ElectionId
            select new GeneralItemDTO
            {
                Id = aa.Id,
                EnglishTitle = aa.UrduTitle + " (صوبہ)",
                UrduTitle = $"/administrativeunits/{ElectionId}/{aa.Id}"
            }).ToList();

        //var q2 = (from aa in dc.Structures.OfType<Division>()
        //          where aa.Province.ElectionId == ElectionId
        //          orderby aa.EnglishTitle
        //          select new GeneralItemDTO
        //          {
        //             Id = aa.Id,
        //             EnglishTitle = aa.Province.EnglishTitle + " - " +
        //                            aa.EnglishTitle + " (Division)",
        //             UrduTitle = $"/divisions/{aa.ProvinceId}/{aa.Id}"
        //          }).ToList();

        //var q2a = (from aa in dc.Structures.OfType<Division>()
        //           where aa.Province.ElectionId == ElectionId
        //           orderby aa.UrduTitle
        //           select new GeneralItemDTO
        //           {
        //              Id = aa.Id,
        //              EnglishTitle = aa.Province.UrduTitle + " - " +
        //                             aa.UrduTitle + " (ڈویژن)",
        //              UrduTitle = $"/divisions/{aa.ProvinceId}/{aa.Id}"
        //           }).ToList();

        var q3 = (from aa in dc.Structures.OfType<District>()
            where aa.Division.Province.ElectionId == ElectionId
            orderby aa.EnglishTitle
            select new GeneralItemDTO
            {
                Id = aa.Id,
                EnglishTitle =
                    aa.Division.Province.EnglishTitle +
                    " - " +
                    aa.Division.EnglishTitle +
                    " - " +
                    aa.EnglishTitle +
                    " (District)",
                UrduTitle = $"/districts/{aa.Division.ProvinceId}/{aa.Id}"
            }).ToList();

        var q3a = (from aa in dc.Structures.OfType<District>()
            where aa.Division.Province.ElectionId == ElectionId
            orderby aa.UrduTitle
            select new GeneralItemDTO
            {
                Id = aa.Id,
                EnglishTitle =
                    aa.Division.Province.UrduTitle + " - " + aa.Division.UrduTitle + " - " + aa.UrduTitle + " (ڈسٹرکٹ)",
                UrduTitle = $"/districts/{aa.Division.ProvinceId}/{aa.Id}"
            }).ToList();

        var q4 = (from aa in dc.Structures.OfType<Town>()
            where aa.District.Division.Province.ElectionId == ElectionId
            orderby aa.EnglishTitle
            select new GeneralItemDTO
            {
                Id = aa.Id,
                EnglishTitle =
                    aa.District.Division.Province.EnglishTitle +
                    " - " +
                    aa.District.Division.EnglishTitle +
                    " - " +
                    aa.District.EnglishTitle +
                    " - " +
                    aa.EnglishTitle +
                    " (Town)",
                UrduTitle = $"/towns/{aa.DistrictId}/{aa.Id}"
            }).ToList();

        var q4a = (from aa in dc.Structures.OfType<Town>()
            where aa.District.Division.Province.ElectionId == ElectionId
            orderby aa.UrduTitle
            select new GeneralItemDTO
            {
                Id = aa.Id,
                EnglishTitle =
                    aa.District.Division.Province.UrduTitle +
                    " - " +
                    aa.District.Division.UrduTitle +
                    " - " +
                    aa.District.UrduTitle +
                    " - " +
                    aa.UrduTitle +
                    " (ٹاون)",
                UrduTitle = $"/towns/{aa.DistrictId}/{aa.Id}"
            }).ToList();

        var q5 = (from aa in dc.Structures.OfType<UnionCouncil>()
            where aa.Town.District.Division.Province.ElectionId == ElectionId
            orderby aa.EnglishTitle
            select new GeneralItemDTO
            {
                Id = aa.Id,
                EnglishTitle =
                    aa.Town.District.Division.Province.EnglishTitle +
                    " - " +
                    aa.Town.District.Division.EnglishTitle +
                    " - " +
                    aa.Town.District.EnglishTitle +
                    " - " +
                    aa.Town.EnglishTitle +
                    " - " +
                    aa.EnglishTitle +
                    " (Union Council)",
                UrduTitle = $"/ucs/{aa.TownId}/{aa.Id}"
            }).ToList();

        var q5a = (from aa in dc.Structures.OfType<UnionCouncil>()
            where aa.Town.District.Division.Province.ElectionId == ElectionId
            orderby aa.UrduTitle
            select new GeneralItemDTO
            {
                Id = aa.Id,
                EnglishTitle =
                    aa.Town.District.Division.Province.UrduTitle +
                    " - " +
                    aa.Town.District.Division.UrduTitle +
                    " - " +
                    aa.Town.District.UrduTitle +
                    " - " +
                    aa.Town.UrduTitle +
                    " - " +
                    aa.UrduTitle +
                    " (ضلعی کونسل)",
                UrduTitle = $"/ucs/{aa.TownId}/{aa.Id}"
            }).ToList();

        var q6 = (from aa in dc.Structures.OfType<Ward>()
            where aa.UnionCouncil.Town.District.Division.Province.ElectionId == ElectionId
            orderby aa.EnglishTitle
            select new GeneralItemDTO
            {
                Id = aa.Id,
                EnglishTitle =
                    aa.UnionCouncil.Town.District.Division.Province.EnglishTitle +
                    " - " +
                    aa.UnionCouncil.Town.District.Division.EnglishTitle +
                    " - " +
                    aa.UnionCouncil.Town.District.EnglishTitle +
                    " - " +
                    aa.UnionCouncil.Town.EnglishTitle +
                    " - " +
                    aa.UnionCouncil.EnglishTitle +
                    " - " +
                    aa.EnglishTitle +
                    " (Ward)",
                UrduTitle = $"/wards/{aa.UnionCouncilId}/{aa.Id}"
            }).ToList();

        var q6a = (from aa in dc.Structures.OfType<Ward>()
            where aa.UnionCouncil.Town.District.Division.Province.ElectionId == ElectionId
            orderby aa.UrduTitle
            select new GeneralItemDTO
            {
                Id = aa.Id,
                EnglishTitle =
                    aa.UnionCouncil.Town.District.Division.Province.UrduTitle +
                    " - " +
                    aa.UnionCouncil.Town.District.Division.UrduTitle +
                    " - " +
                    aa.UnionCouncil.Town.District.UrduTitle +
                    " - " +
                    aa.UnionCouncil.Town.UrduTitle +
                    " - " +
                    aa.UnionCouncil.UrduTitle +
                    " - " +
                    aa.UrduTitle +
                    " (وارڈ)",
                UrduTitle = $"/wards/{aa.UnionCouncilId}/{aa.Id}"
            }).ToList();

        //var q6 = (from aa in dc.Structures.OfType<NationalAssemblyHalka>()
        //          where aa.U.Town.District.Division.Province.ElectionId == ElectionId
        //          orderby aa.EnglishTitle
        //          select new GeneralItemDTO
        //          {
        //             Id = aa.Id,
        //             EnglishTitle = aa.Code + " " + aa.District.Division.Province.EnglishTitle + " - " +
        //                            aa.District.Division.EnglishTitle + " - " +
        //                            aa.District.EnglishTitle + " - " +
        //                            aa.EnglishTitle + " - " +
        //                            aa.EnglishTitle + " (National Assembly)",
        //             UrduTitle = "/nas/" + aa.DistrictId
        //          }).ToList();

        //var q7 = (from aa in dc.Structures.OfType<ProvincialAssemblyHalka>()
        //          orderby aa.EnglishTitle
        //          select new GeneralItemDTO
        //          {
        //             Id = aa.Id,
        //             EnglishTitle = aa.Code + " " + aa.District.Division.Province.EnglishTitle + " - " +
        //                            aa.District.Division.EnglishTitle + " - " +
        //                            aa.District.EnglishTitle + " - " +
        //                            aa.EnglishTitle + " - " +
        //                            aa.EnglishTitle + " (Provincial Assembly)",
        //             UrduTitle = "/pas/" + aa.DistrictId
        //          }).ToList();

        var qq = q1.Union(q3)
            .Union(q4)
            .Union(q5)
            .Union(q6)
            .Union(q1a)
            .Union(q3a)
            .Union(q4a)
            .Union(q5a)
            .Union(q6a)
            .ToList();

        return Task.FromResult(qq);
    }
}