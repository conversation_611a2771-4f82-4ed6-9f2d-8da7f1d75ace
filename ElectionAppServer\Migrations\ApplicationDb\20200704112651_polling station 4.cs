﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace ElectionAppServer.Migrations.ApplicationDb
{
	public partial class pollingstation4 : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.AlterColumn<long>(
				 name: "MaleVoters",
				 table: "PollingStations",
				 nullable: false,
				 oldClrType: typeof(int),
				 oldType: "int");

			migrationBuilder.AlterColumn<long>(
				 name: "FemaleVoters",
				 table: "PollingStations",
				 nullable: false,
				 oldClrType: typeof(int),
				 oldType: "int");
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.AlterColumn<int>(
				 name: "MaleVoters",
				 table: "PollingStations",
				 type: "int",
				 nullable: false,
				 oldClrType: typeof(long));

			migrationBuilder.AlterColumn<int>(
				 name: "FemaleVoters",
				 table: "PollingStations",
				 type: "int",
				 nullable: false,
				 oldClrType: typeof(long));
		}
	}
}
