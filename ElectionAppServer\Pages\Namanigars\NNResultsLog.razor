﻿@layout ReportLayout

@page "/namanigar/resultslog/{ConstituencyId:int}/{PhaseId:int}/{SeatTypeId:int}"
@inject NavigationManager NavigationManager
@inject UserManager<ApplicationUser> _UserManager
@inject RoleManager<IdentityRole> _RoleManager
@inject AuthenticationStateProvider AuthenticationStateProvider
@using FilterType = Syncfusion.Blazor.Grids.FilterType
@inherits OwningComponentBase<ElectionResultsDataService>
<SfDialog ShowCloseIcon="true" @bind-Visible="IsDetailOpen" IsModal="true" Width="98%">
    <DialogEvents OnOpen="beforeOpen"></DialogEvents>
    <DialogTemplates>
        <Header>Results</Header>
        <Content>
            <div class="row mb-2">
                <div class="col-md">
                    <b>Date:</b> @LogDate
                </div>
                <div class="col-md">
                    <b>Namanigar:</b> @Namanigar
                </div>
                <div class="col-md">
                    <b>PS (Change/Previous):</b> @PSChange
                </div>
                <div class="col-md">
                    <b>PS:</b> @PS
                </div>
                <div class="col-md">
                    <b>Action:</b> @Action
                </div>
            </div>
            <div class="row mb-2">
                <div class="col-md">
                    <table class="table table-striped table-sm">
                        <thead>
                        <tr>
                            <th>Candidate</th>
                            <th>Party</th>
                            <th>Change/Previous Votes</th>
                            <th>Votes</th>
                        </tr>
                        </thead>
                        <tbody>
                        @foreach (var rk in LogResult)
                        {
                            <tr>
                                <td>@rk.Candidate</td>
                                <td>@rk.Party</td>
                                <td>@rk.ChangeVotes</td>
                                <td>@rk.Votes</td>
                            </tr>
                        }
                        </tbody>
                    </table>
                </div>
            </div>
        </Content>
    </DialogTemplates>
</SfDialog>
<section style="font-size: 12px;padding: 5px !important">
    <SfButton CssClass="e-primary" style="font-size:12px" OnClick=@(() => { NavigationManager.NavigateTo("/namanigar/results"); })>Back</SfButton>
    @if (logList != null && logList.Any())
    {
        <h5>@logList[0].Constituency</h5>
        <SfGrid DataSource="@logList" AllowFiltering="true" @ref="Grid" AllowSorting="true" AllowTextWrap="true" Width="100%">
            <GridFilterSettings Type="FilterType.Excel"></GridFilterSettings>
            <GridColumns>
                <GridColumn AutoFit="true" AllowSorting="false" AllowFiltering="false">
                    <Template Context="ss">
                        @{
                            if (ss is NNResultsLogDTO obj)
                            {
                                <SfButton CssClass="e-primary" OnClick=@(() => ViewDetail(obj.Id))>View</SfButton>
                            }
                        }
                    </Template>
                </GridColumn>
                <GridColumn AutoFit="true" Field=@nameof(NNResultsLogDTO.Date) HeaderText="Date"></GridColumn>
                <GridColumn AutoFit="true" Field=@nameof(NNResultsLogDTO.Namanigar) HeaderText="Namanigar"></GridColumn>
                <GridColumn AutoFit="true" Field=@nameof(NNResultsLogDTO.Action) HeaderText="Action"></GridColumn>
                <GridColumn AutoFit="true" Field=@nameof(NNResultsLogDTO.PS) HeaderText="PS"></GridColumn>
            </GridColumns>
        </SfGrid>
    }
    else
    {
        <h5>No log data found</h5>
    }
</section>