﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace ElectionAppServer.Migrations.ApplicationDb
{
	public partial class UpdateProblemFieldLength : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.AlterColumn<string>(
				 name: "Problems",
				 table: "Structures",
				 type: "nvarchar(2000)",
				 maxLength: 2000,
				 nullable: true,
				 oldClrType: typeof(string),
				 oldType: "nvarchar(500)",
				 oldMaxLength: 500,
				 oldNullable: true);

			migrationBuilder.AlterColumn<string>(
				 name: "ImportantPoliticalPersonalities",
				 table: "Structures",
				 type: "nvarchar(2000)",
				 maxLength: 2000,
				 nullable: true,
				 oldClrType: typeof(string),
				 oldType: "nvarchar(500)",
				 oldMaxLength: 500,
				 oldNullable: true);

			migrationBuilder.AlterColumn<string>(
				 name: "ImportantAreas",
				 table: "Structures",
				 type: "nvarchar(2000)",
				 maxLength: 2000,
				 nullable: true,
				 oldClrType: typeof(string),
				 oldType: "nvarchar(500)",
				 oldMaxLength: 500,
				 oldNullable: true);
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.AlterColumn<string>(
				 name: "Problems",
				 table: "Structures",
				 type: "nvarchar(500)",
				 maxLength: 500,
				 nullable: true,
				 oldClrType: typeof(string),
				 oldType: "nvarchar(2000)",
				 oldMaxLength: 2000,
				 oldNullable: true);

			migrationBuilder.AlterColumn<string>(
				 name: "ImportantPoliticalPersonalities",
				 table: "Structures",
				 type: "nvarchar(500)",
				 maxLength: 500,
				 nullable: true,
				 oldClrType: typeof(string),
				 oldType: "nvarchar(2000)",
				 oldMaxLength: 2000,
				 oldNullable: true);

			migrationBuilder.AlterColumn<string>(
				 name: "ImportantAreas",
				 table: "Structures",
				 type: "nvarchar(500)",
				 maxLength: 500,
				 nullable: true,
				 oldClrType: typeof(string),
				 oldType: "nvarchar(2000)",
				 oldMaxLength: 2000,
				 oldNullable: true);
		}
	}
}
