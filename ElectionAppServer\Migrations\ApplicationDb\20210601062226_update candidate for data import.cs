﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace ElectionAppServer.Migrations.ApplicationDb
{
	public partial class updatecandidatefordataimport : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.AlterColumn<string>(
				 name: "UrduName",
				 table: "Candidates",
				 type: "nvarchar(200)",
				 maxLength: 200,
				 nullable: false,
				 oldClrType: typeof(string),
				 oldType: "nvarchar(100)",
				 oldMaxLength: 100);

			migrationBuilder.AlterColumn<string>(
				 name: "EnglishName",
				 table: "Candidates",
				 type: "varchar(200)",
				 unicode: false,
				 maxLength: 200,
				 nullable: false,
				 oldClrType: typeof(string),
				 oldType: "varchar(100)",
				 oldUnicode: false,
				 oldMaxLength: 100);

			migrationBuilder.AddColumn<string>(
				 name: "Source",
				 table: "Candidates",
				 type: "varchar(100)",
				 unicode: false,
				 maxLength: 100,
				 nullable: true);

			migrationBuilder.AddColumn<int>(
				 name: "SourceId",
				 table: "Candidates",
				 type: "int",
				 nullable: true);
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropColumn(
				 name: "Source",
				 table: "Candidates");

			migrationBuilder.DropColumn(
				 name: "SourceId",
				 table: "Candidates");

			migrationBuilder.AlterColumn<string>(
				 name: "UrduName",
				 table: "Candidates",
				 type: "nvarchar(100)",
				 maxLength: 100,
				 nullable: false,
				 oldClrType: typeof(string),
				 oldType: "nvarchar(200)",
				 oldMaxLength: 200);

			migrationBuilder.AlterColumn<string>(
				 name: "EnglishName",
				 table: "Candidates",
				 type: "varchar(100)",
				 unicode: false,
				 maxLength: 100,
				 nullable: false,
				 oldClrType: typeof(string),
				 oldType: "varchar(200)",
				 oldUnicode: false,
				 oldMaxLength: 200);
		}
	}
}
