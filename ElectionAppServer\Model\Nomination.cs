﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ElectionAppServer.Model;

public class Nomination
{
    public int Id { get; set; }
    public virtual List<OfflineResult> OfflineResults { get; set; }
    [ForeignKey("ElectionPhase")] public int ElectionPhaseId { get; set; }

    [ForeignKey("ElectionAssembly")] public int ElectionAssemblyId { get; set; }

    [ForeignKey("Structure")] public int StructureId { get; set; }

    [ForeignKey("SeatType")] public int SeatTypeId { get; set; }

    [ForeignKey("Candidate")] public int CandidteId { get; set; }

    [ForeignKey("Party")] public int PartyId { get; set; }

    [ForeignKey("Symbol")] public int? SymbolId { get; set; }

    public int Weight { get; set; } = 0;
    public int? Votes { get; set; }
    public int? TotalPollingStations { get; set; }
    public int? ResultPollingStations { get; set; }
    public virtual ElectionAssembly ElectionAssembly { get; set; }
    public virtual Structure Structure { get; set; }
    public virtual SeatType SeatType { get; set; }
    public virtual Candidate Candidate { get; set; }
    public virtual ElectionPhase ElectionPhase { get; set; }
    public virtual Party Party { get; set; }
    public virtual Symbol Symbol { get; set; }
    public bool IsWinner { get; set; }
    public bool IsWithdraw { get; set; }
    public virtual List<PSResult> PSResults { get; set; }
    public virtual NamanigarResult NamanigarResult { get; set; }
    public bool IsBilaMokabilaWinner { get; set; } = false;
    public virtual List<NominationPanelist> NominationPanelists { get; set; }

    public string ResultUpdateBy { get; set; }
    public DateTime? ResultUpdateDate { get; set; }
    public bool Form45Verified { get; set; } = false;

    #region Audit Log Fields

    public DateTime CreatedDate { get; set; } = DateTime.Now;
    public DateTime? ModifiedDate { get; set; }

    [StringLength(450)] public string CreatedBy { get; set; }

    [StringLength(450)] public string ModifiedBy { get; set; }

    #endregion Audit Log Fields
}