﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using ElectionAppServer.Data;
using ElectionAppServer.DTO;
using Microsoft.AspNetCore.Components;
using Syncfusion.Blazor.Grids;

namespace ElectionAppServer.Pages.AdminUnits;

public partial class AdminUnitForm
{
    [Parameter] public ElcStructureDTO SelectedObj { get; set; }
    [Parameter] public bool? CanSave { get; set; }
    private List<RegionDTO> _regionsList = new();
    private List<AdminDivisionDTO> _divisionsList = new();
    [Parameter] public EventCallback<ElcStructureDTO> OnValidDataSubmit { get; set; }
    private List<GeneralItemDTO> _assembliesList = new();
    private List<ProblemDto> _allProblemsList = new();

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        if (SelectedObj.ProvinceId != null)
        {
            _regionsList = await Service.GetRegions((int)SelectedObj.ProvinceId);
            _divisionsList = await Service.GetDivisions((int)SelectedObj.ProvinceId);
            _allProblemsList = await Service.GetAllProblems();
        }

        _assembliesList = await Service.GetAssemblies(SelectedObj.ElectionId);
    }

    private async Task TranslateToUrdu()
    {
        try
        {
            if (string.IsNullOrEmpty(SelectedObj.UrduTitle))
                SelectedObj.UrduTitle = await Translation.TranslateText(SelectedObj.EnglishTitle);
        }
        catch (Exception)
        {
            // ignored
        }
    }

    private async Task RaiseSaveData()
    {
        await OnValidDataSubmit.InvokeAsync(SelectedObj);
    }

    public void CustomizeCell(QueryCellInfoEventArgs<PrevConstituencyDTO> args)
    {
        if (args.Column.Field == "UrduTitle")
        {
            args.Cell.AddClass(["urdu-column"]);
            args.Cell.AddStyle(["font-size:18px;line-height: 1.4;text-align: right"]);
        }
    }
}