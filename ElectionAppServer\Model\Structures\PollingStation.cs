﻿using System;
using System.Collections.Generic;

namespace ElectionAppServer.Model.Structures;

public class PollingStation
{
    public int Id { get; set; }
    public string Number { get; set; }
    public virtual List<PSTotalVoteCaste> PSTotalVoteCastes { get; set; }
    public string EnglishTitle { get; set; }
    public string UrduTitle { get; set; }
    public int? MaleVoters { get; set; }
    public int? FemaleVoters { get; set; }

    public int TotalVoters => (MaleVoters ?? 0) + (FemaleVoters ?? 0);

    public int ConstituencyId { get; set; }
    public virtual Structure Constituency { get; set; }

    public virtual List<PSResult> PSResults { get; set; }
    public PollingStationType? pollingStationType { get; set; }

    #region Audit Log

    public DateTime CreatedDate { get; set; }
    public string CreatedBy { get; set; }
    public DateTime? ModifiedDate { get; set; }
    public string ModifiedBy { get; set; }

    public virtual List<PollingStationCombinedResult> PollingStationCombinedResults { get; set; }

    #endregion Audit Log
}

public enum PollingStationType
{
    Male = 1,
    Female = 2,
    Combined = 3
}