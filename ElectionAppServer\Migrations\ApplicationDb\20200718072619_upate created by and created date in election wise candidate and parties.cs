﻿using Microsoft.EntityFrameworkCore.Migrations;
using System;

namespace ElectionAppServer.Migrations.ApplicationDb
{
	public partial class upatecreatedbyandcreateddateinelectionwisecandidateandparties : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.AddColumn<string>(
				 name: "CreatedBy",
				 table: "ElectionParties",
				 unicode: false,
				 maxLength: 430,
				 nullable: false,
				 defaultValue: "");

			migrationBuilder.AddColumn<DateTime>(
				 name: "CreatedDate",
				 table: "ElectionParties",
				 nullable: false,
				 defaultValue: new DateTime(2020, 7, 18, 12, 26, 18, 978, DateTimeKind.Local).AddTicks(455));

			migrationBuilder.AddColumn<string>(
				 name: "CreatedBy",
				 table: "ElectionCandidates",
				 unicode: false,
				 maxLength: 430,
				 nullable: false,
				 defaultValue: "");

			migrationBuilder.AddColumn<DateTime>(
				 name: "CreatedDate",
				 table: "ElectionCandidates",
				 nullable: false,
				 defaultValue: new DateTime(2020, 7, 18, 12, 26, 18, 984, DateTimeKind.Local).AddTicks(3659));
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropColumn(
				 name: "CreatedBy",
				 table: "ElectionParties");

			migrationBuilder.DropColumn(
				 name: "CreatedDate",
				 table: "ElectionParties");

			migrationBuilder.DropColumn(
				 name: "CreatedBy",
				 table: "ElectionCandidates");

			migrationBuilder.DropColumn(
				 name: "CreatedDate",
				 table: "ElectionCandidates");
		}
	}
}
