﻿using Microsoft.EntityFrameworkCore.Migrations;
using System;

namespace ElectionAppServer.Migrations.ApplicationDb
{
	public partial class removeconstituency : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropTable(
				 name: "ElectionAssemblyConstituencies");
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.CreateTable(
				 name: "ElectionAssemblyConstituencies",
				 columns: table => new
				 {
					 ElectionAssemblyId = table.Column<int>(type: "int", nullable: false),
					 StructureId = table.Column<int>(type: "int", nullable: false),
					 Code = table.Column<string>(type: "nvarchar(max)", nullable: true),
					 CreatedBy = table.Column<string>(type: "nvarchar(450)", maxLength: 450, nullable: true),
					 CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
					 ModifiedBy = table.Column<string>(type: "nvarchar(450)", maxLength: 450, nullable: true),
					 ModifiedDate = table.Column<DateTime>(type: "datetime2", nullable: true)
				 },
				 constraints: table =>
				 {
					 table.PrimaryKey("PK_ElectionAssemblyConstituencies", x => new { x.ElectionAssemblyId, x.StructureId });
					 table.ForeignKey(
							  name: "FK_ElectionAssemblyConstituencies_ElectionAssemblies_ElectionAssemblyId",
							  column: x => x.ElectionAssemblyId,
							  principalTable: "ElectionAssemblies",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Cascade);
					 table.ForeignKey(
							  name: "FK_ElectionAssemblyConstituencies_Structures_StructureId",
							  column: x => x.StructureId,
							  principalTable: "Structures",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Restrict);
				 });

			migrationBuilder.CreateIndex(
				 name: "IX_ElectionAssemblyConstituencies_StructureId",
				 table: "ElectionAssemblyConstituencies",
				 column: "StructureId");
		}
	}
}
