﻿using ElectionAppServer.DTO;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Syncfusion.Blazor.Notifications;
using Syncfusion.Blazor.Popups;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ElectionAppServer.Pages.Election
{
	public partial class Nominations
	{
		// AuthenticationState is available as a CascadingParameter
		[CascadingParameter] private Task<AuthenticationState> authenticationStateTask { get; set; }

		public bool IsDialogOpen { get; set; } = false;
		public SfDialog dlgForm { get; set; }
		public SfToast ToastObj { get; set; }
		public NominationDTO selectedObj { get; set; }
		public int? electionId { get; set; }
		public int? phaseId { get; set; }
		[Parameter] public int? constituencyId { get; set; }
		public int? seatTypeId { get; set; }
		[Parameter] public int? assemblyId { get; set; }

		public List<GeneralItemDTO> elections_list { get; set; }
		public List<GeneralItemDTO> phases_list { get; set; }
		public List<GeneralItemDTO> constituency_list { get; set; }
		public List<GeneralItemDTO> seatTypes_list { get; set; }
		public List<GeneralItemDTO> assemblies_list { get; set; }
		public List<NominationDTO> nominations { get; set; }
		public List<GeneralItemDTO> parties_list { get; set; }
		public List<GeneralItemDTO> symbols_list { get; set; }
		public List<GeneralItemDTO> candidates_List { get; set; }
		//public Query Query = new Query();

		private bool isDlgVisible = false;
		public bool isElecStrcVisible { get; set; } = true;

		public async Task SetParameters(int asmbId, int strcId)
		{
			try
			{
				selectedObj = new NominationDTO();
				elections_list = await Service.GetAllElections();
				parties_list = await Service.GetParties_list(state.state.ElectionId);
				symbols_list = await Service.GetSymbols_list();
				candidates_List = await Service.GetCandidatesList(state.state.ElectionId);

				//electionId = await Service.GetElectionId(assemblyId);
				electionId = state.state.ElectionId;
				await FillAsm_Phase();
				assemblyId = asmbId;
				phaseId = state.state.PhaseId;
				await FillConstituencies();
				constituencyId = strcId;
				//await FillNominations();

				isElecStrcVisible = false;
				//await FillAsm_Phase();
				await FillSeatTypes();
				//await FillNominations();
				StateHasChanged();
			}
			catch (Exception)
			{
			}
		}

		protected override async Task OnAfterRenderAsync(bool firstRender)
		{
			//return base.OnAfterRenderAsync(firstRender);
			if (firstRender)
			{
				var user = (await authenticationStateTask).User;
				if (user.Identity.IsAuthenticated)
				{
					//var dd =  await ProtectedSessionStore.GetAsync<ElectionStateDTO>("election");
					var dd = await localStorage.GetAsync<ElectionStateDTO>(await confreader.GetSettingValue("electionkey"));
					state.SetState(dd.Value);
					isElecStrcVisible = true;
					electionId = state.state.ElectionId;
					phaseId = state.state.PhaseId;
					await FillAsm_Phase();
					parties_list = await Service.GetParties_list(state.state.ElectionId);
					candidates_List = await Service.GetCandidatesList(state.state.ElectionId);

					//await FillConstituencies();
					//await FillSeatTypes();
					//await FillNominations();
				}
				else
				{
					try
					{
						//await  ProtectedSessionStore.DeleteAsync("election");
						await localStorage.DeleteAsync(await confreader.GetSettingValue("electionkey"));
						state.SetState(new ElectionStateDTO { ElectionId = 0, ElectionTitle = "", PhaseId = 0, PhaseTitle = "" });
					}
					catch (Exception)
					{
					}
					NavigationManager.NavigateTo("");
				}
				StateHasChanged();
			}
		}

		protected override async Task OnInitializedAsync()
		{
			try
			{
				selectedObj = new NominationDTO();
				elections_list = await Service.GetAllElections();

				symbols_list = await Service.GetSymbols_list();

				//electionId = state.state.ElectionId;
				//phaseId = state.state.PhaseId;
				//isElecStrcVisible = true;
				//await FillAsm_Phase();
				//await FillSeatTypes();
				//await FillNominations();

				//if (assemblyId != null && constituencyId != null)
				//{
				//   //electionId = await Service.GetElectionId(assemblyId);
				//   electionId = state.ElectionId;
				//   phaseId = state.PhaseId;
				//   isElecStrcVisible = false;
				//   await FillAsm_Phase();
				//   await FillSeatTypes();
				//   await FillNominations();
				//}
			}
			catch (Exception)
			{
			}
		}

		public async Task FillAsm_Phase()
		{
			if (electionId == null)
			{
				assemblies_list = new List<GeneralItemDTO>();
				//assemblies_list.Add(new GeneralItemDTO { Province = "", UrduTitle = "", EnglishTitle = "Select - Assembly", Id = 0 });
			}
			else
			{
				assemblies_list = await Service.GetAssmblies((int)electionId);

				//await FillConstituencies();
			}
			assemblyId = null;
			constituencyId = null;
			seatTypeId = null;
			constituency_list = new List<GeneralItemDTO>();
			//constituency_list.Add(new GeneralItemDTO { UrduTitle = "", Province = "", Id = 0, EnglishTitle = "Select - Constituency" });
			seatTypes_list = new List<GeneralItemDTO>();
			//seatTypes_list.Add(new GeneralItemDTO { EnglishTitle = "Select - Seat Type", Id = 0, Province = "", UrduTitle = "" });
		}

		public async Task FillNominations()
		{
			nominations = new List<NominationDTO>();
			if ((assemblyId ?? 0) > 0 && (constituencyId ?? 0) > 0 && (seatTypeId ?? 0) > 0 && (phaseId ?? 0) > 0)
			{
				nominations = await Service.GetNominations((int)assemblyId, (int)constituencyId, (int)seatTypeId, (int)phaseId);
			}
		}

		public async Task FillSeatTypes()
		{
			if (assemblyId != null && constituencyId != null)
			{
				seatTypes_list = await Service.GetSeatType((int)assemblyId, constituencyId);
			}
			else
			{
				seatTypes_list = new List<GeneralItemDTO>();
			}
			await Task.CompletedTask;
		}

		public async Task OnChangeAssembly(Syncfusion.Blazor.DropDowns.ChangeEventArgs<int?, GeneralItemDTO> args)
		{
			assemblyId = args.Value;
			await FillConstituencies();
			seatTypeId = null;
			constituencyId = null;
			StateHasChanged();
		}

		public async Task OnChangeConstituency(Syncfusion.Blazor.DropDowns.ChangeEventArgs<int?, GeneralItemDTO> args)
		{
			constituencyId = args.Value;
			await FillSeatTypes();
			seatTypeId = null;
			StateHasChanged();
		}

		public async Task OnChangeSeatType(Syncfusion.Blazor.DropDowns.ChangeEventArgs<int?, GeneralItemDTO> args)
		{
			seatTypeId = args.Value;
			await FillNominations();
			await Task.CompletedTask;
		}

		private async Task SaveData()
		{
			try
			{
				selectedObj.AssemblyId = (int)assemblyId;
				selectedObj.PhaseId = (int)phaseId;
				selectedObj.ConstituencyId = (int)constituencyId;
				selectedObj.SeatTypeId = (int)seatTypeId;
				selectedObj.ElectionId = (int)electionId;
				var user = (await authenticationStateTask).User;
				await Service.SaveNomination(selectedObj, user.Identity.Name);
				await FillNominations();
				await dlgForm.HideAsync();
			}
			catch (Exception ex)
			{
				var msg = ex.Message;
				if (ex.InnerException != null)
					msg += "<br>" + ex.InnerException.Message;

				ToastModel mm = new ToastModel { Title = "Error", Content = ex.Message, CssClass = "e-toast-danger", ShowProgressBar = true, Timeout = 5000, ShowCloseButton = true };
				await ToastObj.ShowAsync(mm);
			}
		}

		private async Task OpenCreateForm()
		{
			selectedObj = new NominationDTO
			{
				Id = 0,
				AssemblyId = (int)assemblyId,
				ElectionId = (int)electionId,
				SeatTypeId = (int)seatTypeId,
				ConstituencyId = (int)seatTypeId,
				PhaseId = (int)phaseId,
				Weight = 0
			};
			await dlgForm.ShowAsync();
		}

		private async Task OpenEditForm(NominationDTO obj)
		{
			selectedObj = new NominationDTO
			{
				Id = obj.Id,
				CandidateId = obj.CandidateId,
				AssemblyId = obj.AssemblyId,
				ElectionId = obj.ElectionId,
				PartyId = obj.PartyId,
				PhaseId = obj.PhaseId,
				SeatTypeId = obj.SeatTypeId,
				ConstituencyId = obj.ConstituencyId,
				SymbolId = obj.SymbolId,
				Weight = obj.Weight
			};
			await dlgForm.ShowAsync();
		}

		private async Task DeleteRecord(int Id)
		{
			var para = new string[] { "Are you sure want to delete this nomination?" };
			var res = await JSRuntime.InvokeAsync<bool>("confirm", para);
			if (res == false)
				return;

			try
			{
				await Service.DeleteRecord(Id, state.state.ElectionId);
				await FillNominations();
			}
			catch (Exception ex)
			{
				var mm = new ToastModel { Title = "Error", Content = ex.Message, CssClass = "e-toast-danger", ShowProgressBar = true, Timeout = 5000, ShowCloseButton = true };
				await ToastObj.ShowAsync(mm);
			}
		}

		public async Task LoadData(int electionId, int assemblyId, int phaseId, int constituencyId, int seatTypeId)
		{
			this.electionId = electionId;
			this.assemblyId = assemblyId;
			this.phaseId = phaseId;
			this.constituencyId = constituencyId;
			this.seatTypeId = seatTypeId;
			await FillNominations();
			StateHasChanged();
		}

		public async Task FillConstituencies()
		{
			if (assemblyId != null && assemblyId > 0)
			{
				constituency_list = await Service.GetConstituencies((int)assemblyId);
				seatTypes_list = new List<GeneralItemDTO>();
			}
			else
			{
				constituency_list = new List<GeneralItemDTO>();
				seatTypes_list = new List<GeneralItemDTO>();
			}
			await Task.CompletedTask;
		}

		public async Task OnCandidateSelect(Syncfusion.Blazor.DropDowns.ChangeEventArgs<int?, GeneralItemDTO> args)
		//protected async Task OnCandidateSelect(Syncfusion.EJ2.Blazor.DropDowns.ChangeEventArgs<int?> args)
		{
			if (selectedObj.CandidateId != null)
			{
				selectedObj.PartyId = await Service.GetCandidateCurrentParty((int)selectedObj.CandidateId);
				if (selectedObj.PartyId != null)
				{
					selectedObj.SymbolId = await Service.GetPartySymbolId((int)selectedObj.PartyId);
				}
				else
				{
					selectedObj.SymbolId = null;
				}
			}
			else
			{
				selectedObj.PartyId = null;
				selectedObj.SymbolId = null;
			}
			StateHasChanged();
		}
		public async Task OnPartyChange(Syncfusion.Blazor.DropDowns.ChangeEventArgs<int?, GeneralItemDTO> args)
		{
			selectedObj.PartyId = args.Value;
			selectedObj.SymbolId = await Service.GetPartySymbolId((int)selectedObj.PartyId);
			StateHasChanged();
		}
	}
}