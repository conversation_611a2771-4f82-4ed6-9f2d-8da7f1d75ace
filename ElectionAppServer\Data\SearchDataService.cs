﻿using Microsoft.EntityFrameworkCore;

namespace ElectionAppServer.Data;

public class SearchDataService
{
    private readonly IDbContextFactory<ApplicationDbContext>
        _contextFactory; // private  readonly  ApplicationDbContext dc;

    public SearchDataService(IDbContextFactory<ApplicationDbContext> contextFactory) // (ApplicationDbContext  dc)
    {
        //this.dc = dc;
        _contextFactory = contextFactory;
    }
}