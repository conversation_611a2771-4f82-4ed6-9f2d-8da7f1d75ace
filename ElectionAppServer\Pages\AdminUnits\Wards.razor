﻿@page "/wards/{ucId:int}"
@page "/wards/{ucId:int}/{cid:int}"
@using FilterType = Syncfusion.Blazor.Grids.FilterType
@*@inherits OwningComponentBase<ElectionStructureService>*@
@inject ElectionStructureService Service 
@attribute [Authorize(Roles = "Administrators,Data Viewer")]
<MudText Typo="Typo.h5">Wards/LG Tier 3</MudText>
@*<PageCaption Title="Wards"></PageCaption>*@
@*<a href="/elections">@ElectionTitle</a> <span>></span>*@
<SfDialog Width="900px" @bind-Visible="@isDlgVisible" @ref="dlgForm" IsModal="true" ShowCloseIcon="true">
    <DialogEvents OnOpen="beforeOpen"></DialogEvents>
    <DialogTemplates>
        <Header>Ward Detail</Header>
        <Content>
            <AdminUnitForm SelectedObj="@selectedObj" OnValidDataSubmit="SaveData"></AdminUnitForm>
            <!--<EditForm Model="selectedObj" OnValidSubmit="SaveData">
            <FluentValidator TValidator="ElcStructureDTOValidator"></FluentValidator>
            @*<SfTab CssClass="e-fill">
               <TabItems>
               <TabItem>
               <ChildContent>
               <TabHeader Text="Basic Information"></TabHeader>
               </ChildContent>
               <ContentTemplate>*@
            <div class="row">
            <div class="input-field col-2">
            <SfTextBox Placeholder="Code" @bind-Value="@selectedObj.Code" FloatLabelType="FloatLabelType.Always">
            </SfTextBox>
            <ValidationMessage For="@(() => selectedObj.Code)" />
            </div>
            <div class="input-field col-5">
            <SfTextBox Placeholder="Title (English)" @bind-Value="@selectedObj.EnglishTitle" OnBlur="TranslateToUrdu" FloatLabelType="FloatLabelType.Always"></SfTextBox>
            <ValidationMessage For="@(() => selectedObj.EnglishTitle)" />
            </div>
            <div class="input-field col-5">
            <SfTextBox Placeholder="Title (Urdu)" @bind-Value="@selectedObj.UrduTitle" FloatLabelType="FloatLabelType.Always">
            </SfTextBox>
            <ValidationMessage For="@(() => selectedObj.UrduTitle)" />
            </div>
            </div>
            @*</ContentTemplate>
               </TabItem>
               <TabItem>
               <ChildContent>
               <TabHeader Text="Voters/Polling Stations"></TabHeader>
               </ChildContent>
               <ContentTemplate>*@
            <div class="tabheader mb-2">Voters/Polling Stations</div>
            <div class="row">
            <div class="col">
            <SfNumericTextBox Decimals="0" Format="n" @bind-Value="@selectedObj.MaleVoters" Min="1" FloatLabelType="FloatLabelType.Always" Placeholder="Male Voters"></SfNumericTextBox>
            </div>
            <div class="col">
            <SfNumericTextBox Decimals="0" Format="n" @bind-Value="@selectedObj.FemaleVoters" Min="1" FloatLabelType="FloatLabelType.Always" Placeholder="Female Voters"></SfNumericTextBox>
            </div>
            <div class="col">
            Total Voters<br />
            <b>@selectedObj.TotalVoters</b>
            </div>
            </div>
            <div class="row">
            <div class="col">
            <SfNumericTextBox Decimals="0" Format="n" @bind-Value="@selectedObj.Population" Min="1" FloatLabelType="FloatLabelType.Always" Placeholder="Poulation"></SfNumericTextBox>
            </div>
            <div class="col">
            <SfNumericTextBox Decimals="0" Format="n" @bind-Value="@selectedObj.TotalPollingStations" Min="1" FloatLabelType="FloatLabelType.Always" Placeholder="Total Polling Stations"></SfNumericTextBox>
            </div>
            </div>
            @*</ContentTemplate>
               </TabItem>
               <TabItem>
               <ChildContent>
               <TabHeader Text="Demographics"></TabHeader>
               </ChildContent>
               <ContentTemplate>*@
            <div class="tabheader mb-2">Demographics</div>
            <div class="row">
            <div class="col">
            <SfTextBox Multiline="true" @bind-Value="selectedObj.Trivia" FloatLabelType="FloatLabelType.Always" Placeholder="Economy: (Trivia)"></SfTextBox>
            <ValidationMessage For="@(() => selectedObj.Trivia)" />
            </div>
            </div>
            <div class="row">
            <div class="col">
            <SfTextBox @bind-Value="selectedObj.Languages" Placeholder="Languages Spoken" FloatLabelType="FloatLabelType.Always"></SfTextBox>
            <ValidationMessage For="@(()=>selectedObj.Languages)"></ValidationMessage>
            </div>
            <div class="col">
            <SfTextBox @bind-Value="selectedObj.Castes" Placeholder="Castes" FloatLabelType="FloatLabelType.Always"></SfTextBox>
            <ValidationMessage For="@(()=>selectedObj.Castes)"></ValidationMessage>
            </div>
            </div>
            <div class="row">
            <div class="col">
            <SfTextBox @bind-Value="selectedObj.ImportantPoliticalPersonalities" FloatLabelType="FloatLabelType.Always" Placeholder="Important Political Personalities"></SfTextBox>
            <ValidationMessage For="@(() => selectedObj.ImportantPoliticalPersonalities)" />
            </div>
            </div>
            <div class="row">
            <div class="col">
            <SfTextBox Multiline="true" @bind-Value="selectedObj.Problems" FloatLabelType="FloatLabelType.Always" Placeholder="Problems"></SfTextBox>
            <ValidationMessage For="@(()=>selectedObj.Problems)" />
            </div>
            <div class="col">
            <SfTextBox Multiline="true" @bind-Value="selectedObj.ImportantAreas" FloatLabelType="FloatLabelType.Always" Placeholder="Important Cities/Areas"></SfTextBox>
            </div>
            </div>
            <div class="row">
            <div class="col">
            @*<SfTextBox @bind-Value="selectedObj.UrbanAreaPer" FloatLabelType="FloatLabelType.Always" Placeholder="Urban Area (%)"></SfTextBox>*@
            <SfNumericTextBox @bind-Value="@selectedObj.UrbanAreaPer" FloatLabelType="FloatLabelType.Always" Placeholder="Urban Area (%)" Min="0" Max="100"></SfNumericTextBox>
            </div>
            <div class="col">
            @*<SfTextBox @bind-Value="selectedObj.RuralAreaPer" FloatLabelType="FloatLabelType.Always" Placeholder="Rural Area (%)"></SfTextBox>*@
            <SfNumericTextBox @bind-Value="@selectedObj.RuralAreaPer" FloatLabelType="FloatLabelType.Always" Placeholder="Rural Area (%)" Min="0" Max="100"></SfNumericTextBox>
            </div>
            <div class="col">
            <SfTextBox @bind-Value="@selectedObj.Area" FloatLabelType="FloatLabelType.Always" Placeholder="Total Area"></SfTextBox>
            <ValidationMessage For="@(()=>selectedObj.Problems)" />
            </div>
            </div>
            <div class="row">
            <div class="col">
            <SfTextBox @bind-Value="@selectedObj.MajorityIncomeSource" FloatLabelType="FloatLabelType.Always" Placeholder="Majority Income Source"></SfTextBox>
            <ValidationMessage For="@(()=>selectedObj.MajorityIncomeSource)" />
            </div>
            <div class="col">
            <SfTextBox @bind-Value="@selectedObj.LiteracyRate" FloatLabelType="FloatLabelType.Always" Placeholder="Literacy Rate"></SfTextBox>
            <ValidationMessage For="@(()=>selectedObj.LiteracyRate)" />
            </div>
            <div class="col">
            <SfTextBox @bind-Value="@selectedObj.AverageHouseholdIncome" FloatLabelType="FloatLabelType.Always" Placeholder="Average Household Income"></SfTextBox>
            <ValidationMessage For="@(()=>selectedObj.AverageHouseholdIncome)" />
            </div>
            </div>
            @*</ContentTemplate>
               </TabItem>
               </TabItems>
               </SfTab>*@
            <div class="row">
            <div class="col">
            <SfButton CssClass="e-primary">Save</SfButton>
            </div>
            </div>
            </EditForm>-->
        </Content>
    </DialogTemplates>
</SfDialog>
<SfToast @ref="ToastObj">
    <ToastPosition X="Right"></ToastPosition>
</SfToast>
<section style="padding: 15px 1rem; margin-top: -35px">
    @foreach (var bc in BredCrum)
    {
        <a href="@bc.UrduTitle">@bc.EnglishTitle</a>
        <span>></span>
    }
    Wards
    @if (objList == null)
    {
        <p>
            <em>Loading...</em>
        </p>
    }
    else
    {
        <AuthorizeView Roles="Administrators">
            <div class="row">
                <SfButton IsPrimary="true" CssClass="e-primary mb-" OnClick="OpenCreateForm">Create Ward</SfButton>
            </div>
        </AuthorizeView>
        <div class="row">
            <SearchStructure ElectionId="@electionId"></SearchStructure>
        </div>
        <div class="row">
            <SfGrid DataSource="@objList" AllowFiltering="true" ModelType="@selectedObj" @ref="Grid" AllowSorting="true" AllowPaging="true" AllowTextWrap="true" Width="100%" Height="calc(100vh - 310px)">
                <GridEvents QueryCellInfo="CustomizeCell" TValue="ElcStructureDTO"></GridEvents>
                <GridFilterSettings Type="FilterType.Excel"></GridFilterSettings>
                <GridPageSettings PageSize="10"></GridPageSettings>
                <GridColumns>
                    <GridColumn Field="Id" HeaderText="Id" AutoFit="true"></GridColumn>
                    <GridColumn AutoFit="true" Field="Code" HeaderText="Code"></GridColumn>
                    <GridColumn AutoFit="true" Field="UrduTitle" HeaderText="Urdu Title" TextAlign="TextAlign.Right"></GridColumn>
                    <GridColumn AutoFit="true" Field="EnglishTitle" HeaderText="English Title"></GridColumn>
                    <GridColumn MinWidth="200" HeaderText="Audit Info">
                        <Template Context="kk">
                            @{
                                var oo = kk as ElcStructureDTO;
                            <div style="font-size:11px">
                                <span>Created By: @oo.CreateBy</span><span> On @oo.CreatedDate</span>
                                @if (!string.IsNullOrEmpty(oo.ModifiedBy))
                                {
                                    <br/>
                                    <span>Modified By: @oo.ModifiedBy</span>
                                    <span> On @oo.ModifiedDate</span>
                                }
                            </div>}
                        </Template>
                    </GridColumn>
                    <GridColumn MinWidth="150px" HeaderText="Actions" AllowFiltering="false">
                        <Template Context="ss">
                            @{
                                var kk = ss as ElcStructureDTO;
                                <a target="@($"au{kk.Id}")" href="report/adminunit/@kk.Id">
                                    <span class="e-control e-btn e-lib e-success">
                                        <i class="fas fa-clipboard-check"></i>
                                    </span>
                                </a>
                                <AuthorizeView Roles="Administrators">
                                    <SfButton IsPrimary="true" CssClass="e-danger" OnClick="@(() => DeleteRecord(kk.Id))">
                                        <i class="fas fa-trash-alt"></i>
                                    </SfButton>
                                </AuthorizeView>
                                <SfButton IsPrimary="true" CssClass="e-primary" OnClick="@(() => OpenEditForm(kk))">
                                    <i class="fas fa-pencil-alt"></i>
                                </SfButton>
                            }
                        </Template>
                    </GridColumn>
                </GridColumns>
            </SfGrid>
        </div>
    }
</section>

@code {
    // AuthenticationState is available as a CascadingParameter

}