﻿@page "/otherresults"
@attribute [Authorize(Roles = "Administrators,OfflineUser")]
@using FilterType = Syncfusion.Blazor.DropDowns.FilterType
@using Size = MudBlazor.Size
@*@attribute [Authorize(Roles="Offline User")]*@
@inject NavigationManager NavigationManager
@inject UserManager<ApplicationUser> _UserManager
@inject RoleManager<IdentityRole> _RoleManager
@inject AuthenticationStateProvider AuthenticationStateProvider



@inject ElectionResultsDataService Service


<SfToast @ref="ToastObj" Title="Alert" ShowProgressBar="true" Timeout="3000">
    <ToastPosition X="Right" Y="Bottom"></ToastPosition>
</SfToast>
<div class="mt-1">

    <div class="card border border-primary bg-primary mb-2 text-white">
        <div class="card-header p-2 pl-3">Result Posting</div>
        <div class="card-body text-black bg-light" style="color:black; padding: 8px;">

            <div class="row">

                <div class="col-md">
                    @if (AssembliesList != null)
                    {
                        <SfDropDownList TItem="GeneralItemDTO" TValue="int?" Placeholder="Select Assembly"
                                        @bind-Value="SelectedAssemblyId"
                                        DataSource="@AssembliesList" FloatLabelType="FloatLabelType.Always" AllowFiltering="true"
                                        FilterType="FilterType.Contains" ShowClearButton="true">
                            <DropDownListFieldSettings Value="Id" Text="EnglishTitle"></DropDownListFieldSettings>
                            <DropDownListEvents TValue="int?" TItem="GeneralItemDTO" ValueChange="@OnAssemblyChange"></DropDownListEvents>
                        </SfDropDownList>
                    }
                    else
                    {
                        <p></p>
                    }
                </div>
                <div class="col-md">
                    @if (ConstituenciesList != null)
                    {
                        @if (ConstituenciesList.Any())
                        {
                            <SfDropDownList AllowFiltering="true" TItem="GeneralItemDTO" TValue="int?"
                                            @bind-Value="SelectedStructureId"
                                            Placeholder="Constituency" DataSource="@ConstituenciesList" FloatLabelType="FloatLabelType.Always"
                                            ShowClearButton="true"
                                            FilterType="FilterType.Contains">
                                <DropDownListFieldSettings Value="Id" Text="EnglishTitle"></DropDownListFieldSettings>
                                <DropDownListEvents TValue="int?" TItem="GeneralItemDTO" ValueChange="@OnConstChange"></DropDownListEvents>
                            </SfDropDownList>
                        }
                        else
                        {
                            <p>No constituency is defined on this assembly</p>
                        }
                    }

                    else
                    {
                        <p></p>
                    }
                </div>
                @if (SeatTypeList is { Count: > 1 })
                {
                    <div class="col-md-3">
                        <SfDropDownList TValue="int?" TItem="GeneralItemDTO" Placeholder="Seat" DataSource="@SeatTypeList" FloatLabelType="FloatLabelType.Always" ShowClearButton="true"
                                        @bind-Value="SelectedSeatTypeId"
                                        AllowFiltering="true" FilterType="FilterType.Contains">
                            <DropDownListFieldSettings Value="Id" Text="EnglishTitle"></DropDownListFieldSettings>
                            <DropDownListEvents TValue="int?" TItem="GeneralItemDTO" ValueChange="@OnSeatTypChange"></DropDownListEvents>
                        </SfDropDownList>
                    </div>
                }

                <div class="col-md">
                    @if (ResultSourceList != null)
                    {
                        @if (ResultSourceList.Any())
                        {
                            <SfDropDownList AllowFiltering="true" TItem="GeneralItemDTO" TValue="int?"
                                            Placeholder="Result Source" DataSource="@ResultSourceList"
                                            FloatLabelType="FloatLabelType.Always"
                                            ShowClearButton="true"
                                            @bind-Value="SelectedSourceId"
                                            FilterType="FilterType.Contains">
                                <DropDownListFieldSettings Value="Id" Text="EnglishTitle"></DropDownListFieldSettings>
                                <DropDownListEvents TValue="int?" TItem="GeneralItemDTO" ValueChange="@OnConstChange"></DropDownListEvents>
                            </SfDropDownList>


                            //FilterType="FilterType.Contains">
                            //	<DropDownListFieldSettings Value="Id" Text="Title"></DropDownListFieldSettings>
                            //</SfDropDownList>
                        }
                        else
                        {
                            <p>No source is defined yet</p>
                        }
                    }
                    else
                    {
                        <p></p>
                    }
                </div>
            </div>
        </div>
    </div>

    @if (SelectedAssemblyId != null && SelectedStructureId != null && SelectedSourceId != null)
    {
        @if (ResultDetail == null || ResultDetail.Count == 0)
        {
            <h3>No nomination found on this constituency</h3>
            <a href="/nominations">Nominations</a>
        }
        else if (ResultDetail[0].TotalPollingStations == 0)
        {
            <h3>Total Polling Stations is not define on this constituency</h3>
        }
        else if (ResultDetail[0].VotersCount == 0)
        {
            <h3>Total Voters are not defined on this constituency</h3>
        }
        else
        {
            <div class="row">
                <div class="col-md-3">
                    <div class="card border border-success bg-success text-white mb-2">
                        <div class="card-header p-2 ">
                            Result
                        </div>
                        <div class="card-body text-black bg-light" style="padding: 8px">

                            <table class="table table-sm">
                                @if (!ResultDetail.Any(k => k.IsBilaMokabilaWinner))
                                {
                                    <tr>
                                        <td>

                                            <SfNumericTextBox ShowSpinButton="false" Format="########"
                                                              Decimals="0" Min="0" Placeholder="Polling Stations" FloatLabelType="FloatLabelType.Always"
                                                              @bind-Value="ResultDetail[0].ResultPollingStations" Max="ResultDetail[0].TotalPollingStations">
                                            </SfNumericTextBox>
                                        </td>
                                    </tr>
                                }


                                <tr>
                                    <td>
                                        @if (ResultDetail.Any(k => k.IsBilaMokabilaWinner))
                                        {
                                            <p>امیدوار پہلے سے ہی بلا مقابلہ جیت چکا ہے</p>
                                            <p>Candidate has already won unopposed</p>
                                        }

                                    </td>
                                </tr>

                                <tr>
                                    <td style="text-align:right">
                                        <MudButton Size="Size.Small" Variant="Variant.Filled" Color="Color.Primary"
                                                   StartIcon="@Icons.Material.Filled.Save" OnClick="SaveRecord">
                                            Save
                                        </MudButton>
                                    </td>
                                </tr>

                            </table>
                        </div>
                    </div>
                </div>
                <div class="col-md-9">
                    <div class="card border border-secondary bg-secondary mb-2">
                        <div class="card-header p-2 pl-3">
                            Constituency Info

                        </div>
                        <div class="card-body bg-light" style="padding: 8px;">
                            <table class="table table">
                                <tr>
                                    <td>
                                        Total Polling Stations: <b>@ResultDetail[0].TotalPollingStations</b>
                                    </td>
                                    <td>
                                        Total Voters: <b>@ResultDetail[0].VotersCount</b>
                                    </td>
                                    <td>
                                        Male Voters: <b>@ResultDetail[0].MaleVoters</b>
                                    </td>
                                    <td>
                                        Female Voters: <b>@ResultDetail[0].FemaleVoters</b>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        Polling Stations Completed: <b>@NewResPollingStations</b> - (@(Math.Round(1.0 * (float)NewResPollingStations / (float)ResultDetail[0].TotalPollingStations * 100.0, 2))%)<br/>
                                    </td>
                                    <td>
                                        Votes Caste: <b>@(ResultDetail.Sum(c => c.Votes))</b>
                                    </td>
                                    <td>
                                        Voter Turnout (%): <b>@(Math.Round(1.0 * (float)ResultDetail.Sum(c => c.Votes) / (float)ResultDetail[0].VotersCount * 100.0, 2)) %</b>
                                    </td>

                                    @if (ResultDetail[0].IsRePoll)
                                    {
                                        <td style="background-color: #f9d4e1; display: flex; align-items: center; justify-content: center; border: 1px solid red; border-radius: 10px;">
                                            <span style="color:red;font-weight:bold"> RE-POLL</span>
                                        </td>
                                    }
                                    else
                                    {
                                        <td>

                                            <span>&nbsp;</span>
                                        </td>
                                    }
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card border-info border bg-info text-white mb-2 ">
                <div class="card-header p-2 pl-3">Candidates</div>
                <div class="card-body bg-light text-black" style="color:black; padding:8px">
                    <table class="table table-sm table-striped">
                        <thead>
                        <tr class="bg-info text-white">
                            <th>&nbsp;</th>
                            <th>Name</th>
                            <th colspan="2">Party</th>
                            <th colspan="2">Symbol</th>
                            <th>Weight</th>
                            <th>Votes</th>

                        </tr>
                        </thead>
                        <tbody>

                        @foreach (var res in ResultDetail)
                        {
                            var stl = res.IsWithdraw ? "background-color:#dc35457a" : "";

                            <tr style="@stl">
                                <td style="width:65px; text-align:center">
                                    <img src="@res.candidateImg" style="width:60px" align="left" alt="1"/>
                                </td>
                                <td>
                                    @res.EnglishName<br/>
                                    <span class="urdu-column">@res.UrduName</span>
                                    @if (res.IsWithdraw)
                                    {
                                        <span style='color:red; font-weight:bold'> (Withdraw)</span>
                                    }
                                </td>
                                <td style="width:65px; text-align:center">
                                    <img src="@res.flagImg" style="width:60px" align="left" alt="PTI"/>
                                </td>
                                <td>
                                    @res.Party<br/>
                                    <span class="urdu-column">@res.PartyUrdu</span>
                                </td>
                                <td style="width:65px; text-align:center">
                                    <img src="@res.symbolImg" style="width:60px" align="left" alt="BAT"/>
                                </td>
                                <td>
                                    @res.Symbol<br/>
                                    <span class="urdu-column">@res.SymbolUrdu</span>
                                </td>
                                <td>@res.Weight</td>


                                <td>
                                    <SfNumericTextBox Format="########" @bind-Value="res.Votes" TValue="int" Decimals="0" Min="0" Placeholder="Votes" ShowSpinButton="false"></SfNumericTextBox>
                                </td>

                            </tr>
                        }
                        </tbody>
                    </table>
                </div>
            </div>
        }
    }
</div>