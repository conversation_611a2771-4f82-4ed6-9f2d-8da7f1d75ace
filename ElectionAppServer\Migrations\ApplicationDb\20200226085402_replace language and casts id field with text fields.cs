﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace ElectionAppServer.Migrations.ApplicationDb
{
	public partial class replacelanguageandcastsidfieldwithtextfields : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropForeignKey(
				 name: "FK_Structures_Structures_StructureId",
				 table: "Structures");

			migrationBuilder.DropForeignKey(
				 name: "FK_Structures_Castes_CasteId",
				 table: "Structures");

			migrationBuilder.DropForeignKey(
				 name: "FK_Structures_Languages_LanguageId",
				 table: "Structures");

			migrationBuilder.DropIndex(
				 name: "IX_Structures_StructureId",
				 table: "Structures");

			migrationBuilder.DropIndex(
				 name: "IX_Structures_CasteId",
				 table: "Structures");

			migrationBuilder.DropIndex(
				 name: "IX_Structures_LanguageId",
				 table: "Structures");

			migrationBuilder.DropColumn(
				 name: "StructureId",
				 table: "Structures");

			migrationBuilder.DropColumn(
				 name: "CasteId",
				 table: "Structures");

			migrationBuilder.DropColumn(
				 name: "LanguageId",
				 table: "Structures");

			migrationBuilder.AddColumn<string>(
				 name: "Castes",
				 table: "Structures",
				 maxLength: 200,
				 nullable: true);

			migrationBuilder.AddColumn<string>(
				 name: "Languages",
				 table: "Structures",
				 maxLength: 200,
				 nullable: true);

			migrationBuilder.AddColumn<int>(
				 name: "Town_DistrictId",
				 table: "Structures",
				 nullable: true);

			migrationBuilder.CreateIndex(
				 name: "IX_Structures_Town_DistrictId",
				 table: "Structures",
				 column: "Town_DistrictId");

			migrationBuilder.AddForeignKey(
				 name: "FK_Structures_Structures_Town_DistrictId",
				 table: "Structures",
				 column: "Town_DistrictId",
				 principalTable: "Structures",
				 principalColumn: "Id",
				 onDelete: ReferentialAction.Restrict);
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropForeignKey(
				 name: "FK_Structures_Structures_Town_DistrictId",
				 table: "Structures");

			migrationBuilder.DropIndex(
				 name: "IX_Structures_Town_DistrictId",
				 table: "Structures");

			migrationBuilder.DropColumn(
				 name: "Castes",
				 table: "Structures");

			migrationBuilder.DropColumn(
				 name: "Languages",
				 table: "Structures");

			migrationBuilder.DropColumn(
				 name: "Town_DistrictId",
				 table: "Structures");

			migrationBuilder.AddColumn<int>(
				 name: "StructureId",
				 table: "Structures",
				 type: "int",
				 nullable: true);

			migrationBuilder.AddColumn<int>(
				 name: "CasteId",
				 table: "Structures",
				 type: "int",
				 nullable: true);

			migrationBuilder.AddColumn<int>(
				 name: "LanguageId",
				 table: "Structures",
				 type: "int",
				 nullable: true);

			migrationBuilder.CreateIndex(
				 name: "IX_Structures_StructureId",
				 table: "Structures",
				 column: "StructureId");

			migrationBuilder.CreateIndex(
				 name: "IX_Structures_CasteId",
				 table: "Structures",
				 column: "CasteId");

			migrationBuilder.CreateIndex(
				 name: "IX_Structures_LanguageId",
				 table: "Structures",
				 column: "LanguageId");

			migrationBuilder.AddForeignKey(
				 name: "FK_Structures_Structures_StructureId",
				 table: "Structures",
				 column: "StructureId",
				 principalTable: "Structures",
				 principalColumn: "Id",
				 onDelete: ReferentialAction.Restrict);

			migrationBuilder.AddForeignKey(
				 name: "FK_Structures_Castes_CasteId",
				 table: "Structures",
				 column: "CasteId",
				 principalTable: "Castes",
				 principalColumn: "Id",
				 onDelete: ReferentialAction.Restrict);

			migrationBuilder.AddForeignKey(
				 name: "FK_Structures_Languages_LanguageId",
				 table: "Structures",
				 column: "LanguageId",
				 principalTable: "Languages",
				 principalColumn: "Id",
				 onDelete: ReferentialAction.Restrict);
		}
	}
}
