﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace ElectionAppServer.Migrations.ApplicationDb
{
	public partial class Candidtedisgtricts2 : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.AlterColumn<string>(
				 name: "StrDistrict",
				 table: "Candidates",
				 maxLength: 200,
				 nullable: true,
				 oldClrType: typeof(string),
				 oldType: "nvarchar(max)",
				 oldNullable: true);
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.AlterColumn<string>(
				 name: "StrDistrict",
				 table: "Candidates",
				 type: "nvarchar(max)",
				 nullable: true,
				 oldClrType: typeof(string),
				 oldMaxLength: 200,
				 oldNullable: true);
		}
	}
}
