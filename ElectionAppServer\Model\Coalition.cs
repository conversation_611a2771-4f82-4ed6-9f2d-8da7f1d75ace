﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ElectionAppServer.Model;

public class Coalition
{
    public int Id { get; set; }
    public string EnglishTitle { get; set; }
    public string UrduTitle { get; set; }
    public virtual List<PartyCoalition> PartyCoalitions { get; set; }

    #region Audit Log Fields

    public DateTime CreatedDate { get; set; } = DateTime.Now;
    public DateTime? ModifiedDate { get; set; }

    [StringLength(450)] public string CreatedBy { get; set; }

    [StringLength(450)] public string ModifiedBy { get; set; }

    #endregion Audit Log Fields
}

public class PartyCoalition
{
    [ForeignKey("ElectionId")] public int ElectionId { get; set; }

    [ForeignKey("Coalition")] public int CoalitionId { get; set; }

    [Foreign<PERSON>ey("Party")] public int PartyId { get; set; }

    public virtual Election Election { get; set; }
    public virtual Coalition Coalition { get; set; }
    public virtual Party Party { get; set; }
}