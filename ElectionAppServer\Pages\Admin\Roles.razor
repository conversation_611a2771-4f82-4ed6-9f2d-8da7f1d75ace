﻿@page "/admin/roles"
@inject RoleManager<IdentityRole> RoleManager

<h1>Manage Roles</h1>

<ul>
    @foreach (var role in lstRoles)
    {
        <li>@role.Name</li>
    }
</ul>

<h3>Create Role</h3>

<EditForm Model="@NewRole">
    <DataAnnotationsValidator/>
    <div class="form-group">
        <label for="name">Name:</label>
        <InputText id="name" @bind-Value="NewRole.Name"/>
    </div>
    <button type="submit" @onclick="CreateRole">Create</button>
</EditForm>

@code {
    private List<IdentityRole> lstRoles { get; set; }
    private IdentityRole NewRole { get; set; } = new();

    protected override async Task OnInitializedAsync()
    {
        lstRoles = RoleManager.Roles.ToList();
        await Task.CompletedTask;
    }

    private async Task CreateRole()
    {
        await RoleManager.CreateAsync(NewRole);
        lstRoles = RoleManager.Roles.ToList();
        NewRole = new IdentityRole();
    }

}