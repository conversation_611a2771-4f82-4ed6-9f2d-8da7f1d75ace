﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace ElectionAppServer.Migrations.ApplicationDb
{
	public partial class Adddemographic2 : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.AddColumn<string>(
				 name: "AverageHouseholdIncome",
				 table: "Structures",
				 maxLength: 50,
				 nullable: true);

			migrationBuilder.AddColumn<string>(
				 name: "LiteracyRate",
				 table: "Structures",
				 maxLength: 50,
				 nullable: true);

			migrationBuilder.AddColumn<string>(
				 name: "MajorityIncomeSource",
				 table: "Structures",
				 maxLength: 200,
				 nullable: true);
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropColumn(
				 name: "AverageHouseholdIncome",
				 table: "Structures");

			migrationBuilder.DropColumn(
				 name: "LiteracyRate",
				 table: "Structures");

			migrationBuilder.DropColumn(
				 name: "MajorityIncomeSource",
				 table: "Structures");
		}
	}
}
