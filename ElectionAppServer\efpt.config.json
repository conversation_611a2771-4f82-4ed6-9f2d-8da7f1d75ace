﻿{
   "ContextClassName": "ApplicationDbContext",
   "DefaultDacpacSchema": null,
   "IdReplace": false,
   "IncludeConnectionString": false,
   "OutputPath": "",
   "ProjectRootNamespace": "ElectionAppServer.Model",
   "SelectedToBeGenerated": 0,
   "Tables": [
      {
         "HasPrimaryKey": true,
         "Name": "dbo.Castes"
      },
      {
         "HasPrimaryKey": true,
         "Name": "dbo.Districts"
      },
      {
         "HasPrimaryKey": true,
         "Name": "dbo.Divisions"
      },
      {
         "HasPrimaryKey": true,
         "Name": "dbo.Educations"
      },
      {
         "HasPrimaryKey": true,
         "Name": "dbo.Languages"
      },
      {
         "HasPrimaryKey": true,
         "Name": "dbo.LocalBodyStructures"
      },
      {
         "HasPrimaryKey": true,
         "Name": "dbo.LocalBodyStructureSeatTypes"
      },
      {
         "HasPrimaryKey": true,
         "Name": "dbo.Professions"
      },
      {
         "HasPrimaryKey": true,
         "Name": "dbo.Provinces"
      },
      {
         "HasPrimaryKey": true,
         "Name": "dbo.SeatTypes"
      },
      {
         "HasPrimaryKey": true,
         "Name": "dbo.Symbols"
      },
      {
         "HasPrimaryKey": true,
         "Name": "dbo.Tehsils"
      },
      {
         "HasPrimaryKey": true,
         "Name": "dbo.UnionCouncils"
      },
      {
         "HasPrimaryKey": true,
         "Name": "dbo.Wards"
      }
   ],
   "UseDatabaseNames": false,
   "UseFluentApiOnly": false,
   "UseHandleBars": false,
   "UseInflector": true
}