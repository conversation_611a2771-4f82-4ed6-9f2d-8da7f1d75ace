﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace ElectionAppServer.Migrations
{
	public partial class Optionaldistrictincandidate : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropForeignKey(
				 name: "FK_Candidates_Structures_DistrictId",
				 table: "Candidates");

			migrationBuilder.AlterColumn<int>(
				 name: "DistrictId",
				 table: "Candidates",
				 nullable: true,
				 oldClrType: typeof(int),
				 oldType: "int");

			migrationBuilder.AddForeignKey(
				 name: "FK_Candidates_Structures_DistrictId",
				 table: "Candidates",
				 column: "DistrictId",
				 principalTable: "Structures",
				 principalColumn: "Id",
				 onDelete: ReferentialAction.Restrict);
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropForeignKey(
				 name: "FK_Candidates_Structures_DistrictId",
				 table: "Candidates");

			migrationBuilder.AlterColumn<int>(
				 name: "DistrictId",
				 table: "Candidates",
				 type: "int",
				 nullable: false,
				 oldClrType: typeof(int),
				 oldNullable: true);

			migrationBuilder.AddForeignKey(
				 name: "FK_Candidates_Structures_DistrictId",
				 table: "Candidates",
				 column: "DistrictId",
				 principalTable: "Structures",
				 principalColumn: "Id",
				 onDelete: ReferentialAction.Cascade);
		}
	}
}
