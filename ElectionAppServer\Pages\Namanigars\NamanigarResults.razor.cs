using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ElectionAppServer.DTO;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.SignalR.Client;
using Microsoft.JSInterop;
using Syncfusion.Blazor.DropDowns;
using Syncfusion.Blazor.Notifications;

namespace ElectionAppServer.Pages.Namanigars;

public partial class NamanigarResults
{
    private readonly Dictionary<string, object> telInput = new() { { "type", "tel" } };

    private int ElectionId;
    private HubConnection hubConnection;
    private SfToast ToastObj;
    public bool IsConnected => hubConnection.State == HubConnectionState.Connected;

    private bool IsPostingLocked
    {
        get
        {
            if (result_detail != null && result_detail.Any())
                return result_detail[0].TotalPollingStations == result_detail[0].ResultPollingStations;

            return false;
        }
    }

    private int PhaseId { get; set; }
    public List<GeneralItemDTO> assmblieslist { get; set; }
    public List<ItemDTO> strAssembliesList { get; set; }
    public int ChangePollingStations { get; set; }
    public List<NamanigarConstituencyItemDTO> constituencylist { get; set; }
    public string EditMode { get; set; } = "";
    public bool IsPSWiseResultsExist { get; set; } = false;

    public int NewResPollingStations
    {
        get
        {
            if (EditMode == "Add") return result_detail[0].ResultPollingStations + ChangePollingStations;

            if (EditMode == "Update") return ChangePollingStations;
            return result_detail[0].ResultPollingStations;
        }
    }

    //public int NewResPollingStations { get; set; }

    public List<ResultDetailDTO> result_detail { get; set; } = new();
    public List<GeneralItemDTO> seatypelist { get; set; }
    public List<NamanigarDTO> namanigarList { get; set; }
    public int? SelectedAssemblyId { get; set; }
    public string SelectedAssemblyStrId { get; set; }
    public int? SelectedSeatTypeId { get; set; }
    public int? SelectedStructureId { get; set; }
    public int? selectedNamanigarId { get; set; } = 1;

    public string UserId { get; set; }

    [CascadingParameter] private Task<AuthenticationState> authenticationStateTask { get; set; }

    public async Task CancelRecord()
    {
        ChangePollingStations = 0;
        EditMode = "";
        result_detail = await Service.GetNamanigarElectionResults((int)SelectedAssemblyId, PhaseId,
            (int)SelectedStructureId, (int)SelectedSeatTypeId);

        StateHasChanged();
        await Task.CompletedTask;
    }

    public async Task FillDDLs()
    {
        PhaseId = await Service.GetCurrentPhase();
        ElectionId = await Service.GetCurrentElection();
        assmblieslist = await Service.GetUserAssemblies(UserId, ElectionId, state.state.PhaseId);
        strAssembliesList = (from aa in assmblieslist
            orderby aa.EnglishTitle
            select new ItemDTO
            {
                Id = $"{aa.Id}",
                Title = aa.EnglishTitle
            }).ToList();
        //if (assmblieslist.Count == 1)
        //{
        constituencylist = new List<NamanigarConstituencyItemDTO>(); //await Service.GetNamanigarConstituencies(UserId);
        //	constituencylist = await Service.GetUsersConstituencies(UserId, state.state.ElectionId, (int)SelectedAssemblyId);
        //	if (seatypelist.Count == 1)
        //	{
        //		SelectedSeatTypeId = seatypelist[0].Id;
        //	}
        //}
        //StateHasChanged();
    }

    public async Task FillResult()
    {
        if (SelectedAssemblyId != null && SelectedStructureId != null && SelectedSeatTypeId != null)
            result_detail = await Service.GetNamanigarElectionResults((int)SelectedAssemblyId, PhaseId,
                (int)SelectedStructureId, (int)SelectedSeatTypeId);
        //IsPSWiseResultsExist = await Service.IsPSWiseResultsExist(PhaseId, (int)SelectedStructureId, (int)SelectedSeatTypeId);
        else
            result_detail = new List<ResultDetailDTO>();
    }

    //public async Task OnAssemblyChange(Syncfusion.Blazor.DropDowns.ChangeEventArgs<int?> args)
    public async Task OnAssemblyChange(ChangeEventArgs<int?, GeneralItemDTO> args)
    {
        SelectedAssemblyId = args.Value;
        SelectedStructureId = null;
        namanigarList = null;

        StateHasChanged();
        if (SelectedAssemblyId == null)
        {
            constituencylist = null;
            seatypelist = null;
            selectedNamanigarId = null;
            SelectedSeatTypeId = null;
        }
        else
        {
            constituencylist = await Service.GetNamanigarConstituencies(UserId, SelectedAssemblyId);
            seatypelist = await Service.GetAssemblySeatTypes((int)SelectedAssemblyId);
            if (seatypelist.Any())
            {
                if (seatypelist.Count == 1)
                {
                    SelectedSeatTypeId = seatypelist[0].Id;
                }
                else
                {
                    if (!seatypelist.Any(k => k.Id == SelectedSeatTypeId)) SelectedSeatTypeId = null;
                }
            }
            else
            {
                SelectedAssemblyId = null;
            }
        }
    }

    public async Task OnConstChange(ChangeEventArgs<int?, NamanigarConstituencyItemDTO> args)
    {
        SelectedStructureId = args.Value;
        if (SelectedStructureId != null)
        {
            SelectedAssemblyId = args.ItemData.AssemblyId;
            seatypelist = await Service.GetAssemblySeatTypes((int)SelectedAssemblyId);
            if (seatypelist != null && seatypelist.Count == 1) SelectedSeatTypeId = seatypelist[0].Id;
            await FillResult();
        }
        else
        {
            result_detail = null;
        }
    }

    public async Task OnSeatTypChange(ChangeEventArgs<int?, GeneralItemDTO> args)
    {
        SelectedSeatTypeId = args.Value;
        await FillResult();
    }

    public async Task SaveRecord()
    {
        var user = (await authenticationStateTask).User;

        try
        {
            //await Service.SaveResults(result_detail, ChangePollingStations, EditMode, user.Identity.Name, (int)selectedNamanigarId);
            var res = await Service.SaveNamanigarResults(result_detail, (int)SelectedAssemblyId, PhaseId,
                (int)SelectedStructureId, (int)SelectedSeatTypeId, ChangePollingStations, NewResPollingStations,
                EditMode, user.Identity.Name);
            result_detail = await Service.GetNamanigarElectionResults((int)SelectedAssemblyId, PhaseId,
                (int)SelectedStructureId, (int)SelectedSeatTypeId);
            await hubConnection.SendAsync("SendNamanigarResult", (int)SelectedStructureId);
            EditMode = "";
            ChangePollingStations = 0;
            StateHasChanged();
        }
        catch (Exception ex)
        {
            var mm = new ToastModel
            {
                Title = "Error", Content = ex.Message, CssClass = "e-toast-danger", ShowProgressBar = true,
                Timeout = 5000, ShowCloseButton = true
            };
            await ToastObj.ShowAsync(mm);
        }

        //EditMode = "";
        //await Task.CompletedTask;
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        //return base.OnAfterRenderAsync(firstRender);
        if (firstRender)
        {
            var user = (await authenticationStateTask).User;
            if (user.Identity.IsAuthenticated)
            {
                //ElectionsList = await Service.GetAll();
                //var dd =  await ProtectedSessionStore.GetAsync<ElectionStateDTO>("election");
                var dd = await localStorage.GetAsync<ElectionStateDTO>(await confreader.GetSettingValue("electionkey"));
                state.SetState(dd.Value);
                var info = await _UserManager.GetUserAsync(user);
                UserId = info.Id;
                await Task.CompletedTask;
                await FillDDLs();
                StateHasChanged();
            }
            else
            {
                try
                {
                    //await  ProtectedSessionStore.DeleteAsync("election");
                    await localStorage.DeleteAsync(await confreader.GetSettingValue("electionkey"));
                    state.SetState(new ElectionStateDTO
                        { ElectionId = 0, ElectionTitle = "", PhaseId = 0, PhaseTitle = "" });
                    StateHasChanged();
                }
                catch (Exception)
                {
                    // ignored
                }
            }
        }
    }

    protected override async Task OnInitializedAsync()
    {
        hubConnection = new HubConnectionBuilder().WithUrl(NavigationManager.ToAbsoluteUri("/nnresultshub")).Build();
        await hubConnection.StartAsync();

        await Task.CompletedTask;
    }

    private async Task OpenForm(string mode)
    {
        if (ChangePollingStations <= 0)
        {
            await JSRuntime.InvokeVoidAsync("alert", "Please enter polling stations");
            EditMode = "";
            mode = "";
            return;
        }

        if (mode == "Add")
            if (ChangePollingStations + result_detail[0].ResultPollingStations > result_detail[0].TotalPollingStations)
            {
                await JSRuntime.InvokeVoidAsync("alert",
                    "The result of polling stations count is exceeding against total polling stations.");
                mode = "";
                EditMode = "";
                return;
            }

        //if (ChangePollingStations <= 0)
        //{
        //	await JSRuntime.InvokeVoidAsync("alert", new string[] { "Please enter polling stations" });
        //	return;
        //}
        EditMode = mode;
        //ChangePollingStations = 0;
        //await dlgForm.ShowAsync();
        //if (result_detail.Count > 0)
        //{
        //	if (mode == "Add")
        //	{
        //		ChangePollingStations = 0;
        //	}
        //	else
        //	{
        //		ChangePollingStations = result_detail[0].ResultPollingStations;
        //	}
        //}
        foreach (var res in result_detail)
        {
            res.Mode = mode;
            if (mode == "Add")
                res.ChangeVotes = 0;
            else
                res.ChangeVotes = res.Votes;
        }

        StateHasChanged();
        await Task.CompletedTask;
    }

    private async Task OpenResultsLog()
    {
        NavigationManager.NavigateTo($"/namanigar/resultslog/{SelectedStructureId}/{PhaseId}/{SelectedSeatTypeId}");
        await Task.CompletedTask;
    }

    //public async ValueTask DisposeAsync()
    //{
    //	await hubConnection.DisposeAsync();
    //}
}