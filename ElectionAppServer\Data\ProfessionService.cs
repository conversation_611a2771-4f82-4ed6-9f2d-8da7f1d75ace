﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ElectionAppServer.Model;
using Microsoft.EntityFrameworkCore;

namespace ElectionAppServer.Data;

public class ProfessionService
{
    private readonly IDbContextFactory<ApplicationDbContext>
        _contextFactory; // private  readonly  ApplicationDbContext dc;

    public ProfessionService(IDbContextFactory<ApplicationDbContext> contextFactory) // (ApplicationDbContext  context)
    {
        _contextFactory = contextFactory; // dc  =  context;
    }

    public Task<List<Profession>> GetList()
    {
        using var dc = _contextFactory.CreateDbContext();
        var res = dc.Professions.OrderBy(c => c.EnglishTitle).ToList();
        return Task.FromResult(res);
    }

    public Task<Profession> CreateProfession(Profession obj)
    {
        using var dc = _contextFactory.CreateDbContext();
        dc.Professions.Add(obj);
        dc.SaveChanges();
        return Task.FromResult(obj);
    }

    public Task<Profession> UpdateProfession(Profession obj)
    {
        using var dc = _contextFactory.CreateDbContext();
        var st = (from aa in dc.Professions
            where aa.Id == obj.Id
            select aa).FirstOrDefault();
        st.UrduTitle = obj.UrduTitle.Trim();
        st.EnglishTitle = obj.EnglishTitle.Trim();
        st.ModifiedBy = obj.ModifiedBy;
        st.ModifiedDate = DateTime.Now;
        dc.SaveChanges();
        return Task.FromResult(obj);
    }

    public async Task<Profession> DeleteProfession(int Id)
    {
        using var dc = _contextFactory.CreateDbContext();
        var st = await (from aa in dc.Professions
            where aa.Id == Id
            select aa).FirstOrDefaultAsync();
        dc.Professions.Remove(st);
        await dc.SaveChangesAsync();
        return await Task.FromResult(st);
    }
}