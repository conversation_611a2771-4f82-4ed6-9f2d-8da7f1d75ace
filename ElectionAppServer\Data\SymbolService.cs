﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ElectionAppServer.DTO;
using ElectionAppServer.Model;
using Microsoft.EntityFrameworkCore;

namespace ElectionAppServer.Data;

public class SymbolService
{
    private readonly IDbContextFactory<ApplicationDbContext>
        _contextFactory; // private  readonly  ApplicationDbContext dc;

    public SymbolService(IDbContextFactory<ApplicationDbContext> contextFactory) // (ApplicationDbContext  context)
    {
        _contextFactory = contextFactory; // dc  =  context;
    }

    public Task<List<SymbolDTO>> GetList()
    {
        using var dc = _contextFactory.CreateDbContext();
        //var res = dc.Symbols.OrderBy(c => c.EnglishTitle).ToList();
        var q = (from aa in dc.Symbols
            orderby aa.EnglishTitle
            select new SymbolDTO
            {
                English = aa.EnglishTitle,
                Id = aa.Id,
                Urdu = aa.UrduTitle,
                URL = $"/media/symbols/{aa.Id}.jpg",
                CreatedBy = aa.CreatedBy,
                CreatedOn = aa.CreatedDate.ToString("d MMM, yyyy h:mm"),
                ModifiedBy = aa.ModifiedBy ?? "",
                ModifiedOn = aa.ModifiedDate != null ? aa.ModifiedDate.Value.ToString("d MMM, yyyy h:mm") : ""
                //Code = aa.Code
            }).ToList();
        return Task.FromResult(q);
    }

    public Task<SymbolDTO> CreateSymbol(SymbolDTO obj, string user)
    {
        using var dc = _contextFactory.CreateDbContext();
        obj.English = obj.English.Trim();
        obj.Urdu = obj.Urdu.Trim();

        #region Exist

        //var isExist = dc.Symbols.Any(c => c.Code == obj.Code);
        //if (isExist) throw new Exception("Code Already exist");

        var isExist = dc.Symbols.Any(c => c.EnglishTitle == obj.English);
        if (isExist) throw new Exception("English Title already exist");

        isExist = dc.Symbols.Any(c => c.UrduTitle == obj.Urdu);
        if (isExist) throw new Exception("Urdu Title already exist");

        #endregion Exist

        var sm = new Symbol
        {
            //Code = obj.Code,
            CreatedBy = user,
            CreatedDate = DateTime.Now,
            EnglishTitle = obj.English.Trim(),
            UrduTitle = obj.Urdu.Trim()
        };
        dc.Symbols.Add(sm);
        dc.SaveChanges();
        obj.Id = sm.Id;
        return Task.FromResult(obj);
    }

    public Task<SymbolDTO> UpdateSymbol(SymbolDTO obj, string user)
    {
        using var dc = _contextFactory.CreateDbContext();
        obj.English = obj.English.Trim();
        obj.Urdu = obj.Urdu.Trim();

        #region Exist

        //var isExist = dc.Symbols.Any(c => c.Code == obj.Code && c.Id != obj.Id);
        //if (isExist) throw new Exception("Code Already exist");

        var isExist = dc.Symbols.Any(c => c.EnglishTitle == obj.English && c.Id != obj.Id);
        if (isExist) throw new Exception("English Title already exist");

        isExist = dc.Symbols.Any(c => c.UrduTitle == obj.Urdu && c.Id != obj.Id);
        if (isExist) throw new Exception("Urdu Title already exist");

        #endregion Exist

        var st = (from aa in dc.Symbols
            where aa.Id == obj.Id
            select aa).FirstOrDefault();

        st.UrduTitle = obj.Urdu;
        st.EnglishTitle = obj.English;
        st.ModifiedBy = user;
        //st.Code = (int)obj.Code;
        st.ModifiedDate = DateTime.Now;
        dc.SaveChanges();
        return Task.FromResult(obj);
    }

    public async Task<Symbol> DeleteSymbol(int Id)
    {
        using var dc = _contextFactory.CreateDbContext();
        var st = await (from aa in dc.Symbols
            where aa.Id == Id
            select aa).FirstOrDefaultAsync();
        dc.Symbols.Remove(st);
        await dc.SaveChangesAsync();
        return await Task.FromResult(st);
    }
}