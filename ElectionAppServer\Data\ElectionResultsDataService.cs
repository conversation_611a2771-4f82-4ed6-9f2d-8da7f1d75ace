﻿using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Dapper;
using ElectionAppServer.DTO;
using ElectionAppServer.Model;
using ElectionAppServer.Model.Structures;
using Microsoft.EntityFrameworkCore;

namespace ElectionAppServer.Data;

public class ElectionResultsDataService
{
    private readonly IDbContextFactory<ApplicationDbContext>
        _contextFactory; //public ApplicationDbContext  dc  { get; }

    public ElectionResultsDataService(
        IDbContextFactory<ApplicationDbContext> contextFactory) // (ApplicationDbContext  dc)
    {
        _contextFactory = contextFactory;
    }

    public Task<LiveResult> ApproveNamanigarResult(NamanigarResultDTO obj, string user)
    {
        using var dc = _contextFactory.CreateDbContext();
        using var tran = dc.Database.BeginTransaction();

        try
        {
            var batchExist = (from aa in dc.NamanigarResults
                join n in dc.Nominations on aa.NominationId equals n.Id
                where aa.BatchId == obj.BatchId &&
                      n.StructureId == obj.ConstituencyId &&
                      n.SeatType.EnglishTitle == obj.SeatType
                select aa).FirstOrDefault();
            // if batch exist on same batch code
            if (batchExist != null)
            {
                // First Save Results Log

                var actionDate = DateTime.Now;

                #region Save Results Log

                var prvData = (from aa in dc.Nominations
                    where aa.StructureId == obj.ConstituencyId &&
                          aa.SeatTypeId == obj.SeatTypeId
                    select aa).FirstOrDefault();
                var prvPollingStations = prvData.ResultPollingStations ?? 0;
                var lr = new ResultLog
                {
                    Action = "Import",
                    AssemblyId = obj.AssemblyId,
                    ConstituencyId = obj.ConstituencyId,
                    CreatedByUserId = user,
                    CreatedDate = actionDate,
                    NamanigarId = 1,
                    NewPollingStations = obj.NamangiarPollingStations,
                    PhaseId = obj.PhaseId,

                    PreviousPollingStations = prvPollingStations,
                    SeatTypeId = obj.SeatTypeId
                };
                dc.ResultLogs.Add(lr);
                dc.SaveChanges();

                foreach (var d in obj.Details)
                {
                    var prvDataNom = (from aa in dc.Nominations
                        where aa.Id == d.NominationId
                        select aa).FirstOrDefault();
                    var prvVotes = prvDataNom.Votes ?? 0;
                    var lrd = new ResultLogDetail
                    {
                        CandidateId = d.CandidateId,
                        ResultLogId = lr.Id,
                        NewVotes = d.NamanigarVotes,
                        PreviousVotes = prvVotes,
                        Votes = d.NamanigarVotes
                    };

                    dc.ResultLogDetails.Add(lrd);
                    dc.SaveChanges();
                }

                #endregion Save Results Log

                #region Save Namanigar Results to master table

                foreach (var rd in obj.Details)
                {
                    var n = (from aa in dc.Nominations
                        where aa.Id == rd.NominationId
                        select aa).FirstOrDefault();

                    n.Votes = rd.NamanigarVotes;
                    n.ModifiedBy = user;
                    n.ModifiedDate = actionDate;
                    n.IsWinner = false;
                    n.ResultPollingStations = obj.NamangiarPollingStations;
                    dc.SaveChanges();
                }

                #endregion Save Namanigar Results to master table

                #region set winner status

                // Get Winner Info
                var winner1 = (from aa in dc.Nominations
                    where aa.StructureId == obj.ConstituencyId &&
                          aa.ElectionPhaseId == obj.PhaseId &&
                          aa.ElectionAssemblyId == obj.AssemblyId &&
                          aa.SeatTypeId == obj.SeatTypeId &&
                          aa.Votes > 0
                    orderby aa.Votes descending
                    select aa).FirstOrDefault();
                // If Winner Exist
                if (winner1 != null)
                {
                    var winner2 = (from aa in dc.Nominations
                        where aa.StructureId == obj.ConstituencyId &&
                              aa.ElectionPhaseId == obj.PhaseId &&
                              aa.ElectionAssemblyId == obj.AssemblyId &&
                              aa.SeatTypeId == obj.SeatTypeId &&
                              aa.Votes == winner1.Votes
                        select aa).FirstOrDefault();
                    // If another candidate has same votes
                    winner1.IsWinner = true;
                }

                #endregion set winner status

                #region Live Result Log Genrate

                var qResult = (from aa in dc.Nominations
                    where aa.StructureId == obj.ConstituencyId &&
                          aa.ElectionPhaseId == obj.PhaseId &&
                          aa.ElectionAssemblyId == obj.AssemblyId &&
                          aa.SeatTypeId == obj.SeatTypeId
                    select new ResultDTO
                    {
                        AssemblyId = obj.AssemblyId,
                        Assembly = aa.ElectionAssembly.EnglishTitle,
                        Constituency = $"{aa.Structure.Code} - {aa.Structure.EnglishTitle}",
                        ConstituencyId = aa.StructureId,
                        ElectionId = aa.ElectionPhase.ElectionId,
                        Election = aa.ElectionPhase.Election.EnglishTitle,
                        LastUpdatedOn = DateTime.Now,
                        Phase = aa.ElectionPhase.Title,
                        PhaseId = aa.ElectionPhaseId,
                        SeatTypeId = obj.SeatTypeId,
                        SeatType = aa.SeatType.EnglishTitle,
                        ResultOfPollingStations = aa.ResultPollingStations ?? 0,
                        TotalPollingStations = aa.Structure.TotalPollingStations ?? 0,
                        TotalVoters = aa.Structure.TotalVoters
                    }).FirstOrDefault();

                var winner = (from aa in dc.Nominations
                    where aa.StructureId == obj.ConstituencyId &&
                          aa.ElectionPhaseId == obj.PhaseId &&
                          aa.ElectionAssemblyId == obj.AssemblyId &&
                          aa.SeatTypeId == obj.SeatTypeId &&
                          aa.Votes != null &&
                          aa.Votes > 0
                    orderby aa.Votes ?? 0 descending
                    select new
                    {
                        aa.CandidteId,
                        aa.Candidate.EnglishName,
                        VotesObtain = aa.Votes ?? 0,
                        Party = aa.Party.EnglishTitle
                    }).FirstOrDefault();

                if (winner != null)
                {
                    qResult.WinnerCandidateId = winner.CandidteId;
                    qResult.WinnerName = winner.EnglishName;
                    qResult.WinnerParty = winner.Party;
                    qResult.WinnerVotes = winner.VotesObtain;

                    var runnerUp = (from aa in dc.Nominations
                        where aa.StructureId == obj.ConstituencyId &&
                              aa.CandidteId != winner.CandidteId &&
                              aa.ElectionPhaseId == obj.PhaseId &&
                              aa.ElectionAssemblyId == obj.AssemblyId &&
                              aa.SeatTypeId == obj.SeatTypeId &&
                              aa.Votes != null &&
                              aa.Votes > 0
                        orderby aa.Votes ?? 0 descending
                        select new
                        {
                            aa.CandidteId,
                            aa.Candidate.EnglishName,
                            VotesObtain = aa.Votes ?? 0,
                            Party = aa.Party.EnglishTitle
                        }).FirstOrDefault();

                    if (runnerUp != null)
                    {
                        qResult.RunnerUpCandidateId = runnerUp.CandidteId;
                        qResult.RunnerUpName = runnerUp.EnglishName;
                        qResult.RunnerUpParty = runnerUp.Party;
                        qResult.RunnerUpVotes = runnerUp.VotesObtain;
                    }
                    else
                    {
                        qResult.RunnerUpName = string.Empty;
                        qResult.RunnerUpCandidateId = 0;
                        qResult.RunnerUpParty = string.Empty;
                        qResult.RunnerUpVotes = 0;
                    }
                }
                else
                {
                    if (qResult != null)
                    {
                        qResult.WinnerCandidateId = 0;
                        qResult.WinnerName = string.Empty;
                        qResult.WinnerParty = string.Empty;
                        qResult.WinnerVotes = 0;
                        qResult.RunnerUpCandidateId = 0;
                        qResult.RunnerUpName = string.Empty;
                        qResult.RunnerUpParty = string.Empty;
                        qResult.RunnerUpVotes = 0;
                    }

                    //return null;
                }

                var electionId = (from bb in dc.ElectionPhases
                    where bb.Id == obj.PhaseId
                    select bb.ElectionId).FirstOrDefault();

                var liveResult = (from aa in dc.LiveResults
                    where aa.ElectionId == electionId &&
                          aa.PhaseId == obj.PhaseId &&
                          aa.AssemblyId == obj.AssemblyId &&
                          aa.ConstituencyId == obj.ConstituencyId &&
                          aa.SeatTypeId == obj.SeatTypeId
                    select aa).FirstOrDefault();
                if (liveResult == null)
                {
                    liveResult = new LiveResult
                    {
                        AssemblyId = obj.AssemblyId,
                        ElectionId = electionId,
                        PhaseId = obj.PhaseId,
                        ConstituencyId = obj.ConstituencyId,
                        SeatTypeId = obj.SeatTypeId,
                        Election = qResult.Election,
                        Assembly = qResult.Assembly,
                        Constituency = qResult.Constituency,
                        SeatType = qResult.SeatType,
                        CreatedBy = user,
                        CreatedDate = DateTime.Now,
                        Phase = qResult.Phase,
                        TotalVoters = qResult.TotalVoters,
                        TotalPollingStations = qResult.TotalPollingStations,
                        PollingStationResult = qResult.ResultOfPollingStations,
                        Winner = qResult.WinnerName,
                        WinnerParty = qResult.WinnerParty,
                        WinnerVotes = qResult.WinnerVotes,
                        RunnerUp = qResult.RunnerUpName,
                        RunnerUpParty = qResult.RunnerUpParty,
                        RunnerUpVotes = qResult.RunnerUpVotes,
                        IsPublished = false,
                        ModifiedBy = user,
                        ModifiedDate = DateTime.Now, IsTickerPublish = false
                    };
                    dc.LiveResults.Add(liveResult);
                    dc.SaveChanges();
                }
                else
                {
                    liveResult.TotalVoters = qResult.TotalVoters;
                    liveResult.TotalPollingStations = qResult.TotalPollingStations;
                    liveResult.PollingStationResult = qResult.ResultOfPollingStations;
                    liveResult.Winner = qResult.WinnerName;
                    liveResult.WinnerParty = qResult.WinnerParty;
                    liveResult.WinnerVotes = qResult.WinnerVotes;
                    liveResult.RunnerUp = qResult.RunnerUpName;
                    liveResult.RunnerUpParty = qResult.RunnerUpParty;
                    liveResult.RunnerUpVotes = qResult.RunnerUpVotes;
                    liveResult.ModifiedBy = user;
                    liveResult.ModifiedDate = DateTime.Now;
                    liveResult.IsPublished = false;
                    liveResult.IsTickerPublish = false;
                    dc.SaveChanges();
                }

                #endregion Live Result Log Genrate

                var nnResults = (from aa in dc.NamanigarResults
                    where aa.BatchId == obj.BatchId
                    select aa).ToList();
                foreach (var nnResult in nnResults)
                {
                    nnResult.Status = ApprovalStatus.Approved;
                    nnResult.ModifiedBy = user;
                    nnResult.ModifiedOn = actionDate;
                    dc.SaveChanges();
                }

                tran.Commit();
                return Task.FromResult(liveResult);
            }

            tran.Rollback();
            return null;
        }
        catch (Exception)
        {
            tran.Rollback();
            return null;
        }
    }

    public Task<List<AssemblyAndSeatTypeDTO>> GetAssemblyAndSeatTypes(int phaseId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var con = dc.Database.GetDbConnection();
        var prams = new { PhaseId = phaseId };
        var op = con
            .Query<AssemblyAndSeatTypeDTO>("app.spGetAsmblyAndSeatTypes", prams,
                commandType: CommandType.StoredProcedure)
            .ToList();
        return Task.FromResult(op);
    }

    internal Task<List<GeneralItemDTO>> GetAssemblyTypes(int phaseId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var res = (from aa in dc.Nominations
            orderby aa.ElectionAssembly.AssemblyType.EnglishTitle
            where aa.ElectionPhaseId == phaseId
            select new GeneralItemDTO
            {
                EnglishTitle = aa.ElectionAssembly.AssemblyType.EnglishTitle,
                Id = aa.ElectionAssembly.AssemblyTypeId
            }).Distinct().ToList();
        return Task.FromResult(res);
    }

    internal Task<ResultLogInfoDTO> GetLastLog(int selectedAssemblyId, int phaseId, int selectedStructureId,
        int selectedSeatTypeId)
    {
        using var dc = _contextFactory.CreateDbContext();

        var q = (from aa in dc.ResultLogs
            where aa.PhaseId == phaseId &&
                  aa.AssemblyId == selectedAssemblyId &&
                  aa.SeatTypeId == selectedSeatTypeId &&
                  aa.ConstituencyId == selectedStructureId
            orderby aa.CreatedDate descending
            select new ResultLogInfoDTO
            {
                Action = aa.Action,
                PostedBy = aa.CreatedByUserId,
                PostingDateTime = aa.CreatedDate,
                PSCount = aa.NewPollingStations,
                PrevPSCount = aa.PreviousPollingStations,
                Candidates = (from bb in aa.ResultLogDetails
                    select new ResultLogInfoDetailDTO
                    {
                        Candidate = bb.Candidate.EnglishName,
                        Votes = bb.NewVotes,
                        PrevVotes = bb.PreviousVotes
                    }).ToList()
            }).FirstOrDefault();

        return Task.FromResult(q);
    }

    public Task<List<GeneralItemDTO>> GetAssemblyList(int phaseId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.Nominations
            where aa.ElectionPhaseId == phaseId
            select new GeneralItemDTO
            {
                EnglishTitle = aa.ElectionAssembly.EnglishTitle,
                UrduTitle = aa.ElectionAssembly.UrduTitle,
                Id = aa.ElectionAssemblyId
            }).Distinct().ToList();

        return Task.FromResult(q);
    }

    public Task<List<GeneralItemDTO>> GetAssemblySeatTypes(int assemblyId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.ElectionAssemblySeats
            where aa.ElectionAssemblyId == assemblyId
            orderby aa.SeatType.EnglishTitle
            select new GeneralItemDTO
            {
                Id = aa.SeatTypeId,
                EnglishTitle = aa.SeatType.EnglishTitle,
                UrduTitle = aa.SeatType.UrduTitle
            }).Distinct().ToList();

        return Task.FromResult(q);
    }

    public Task<List<NNViewResultDTO>> GetBatchResults(int batchId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.NamanigarResultLogs
            join n in dc.Nominations on aa.NominationId equals n.Id
            where aa.BatchId == batchId
            select new NNViewResultDTO
            {
                ActionDate = aa.ActionDateTime,
                Namanigar = aa.NamanigarUserId,
                PS = aa.ResultPollingStations,
                PSChange = aa.ChangePollingStations,
                Candidate = n.Candidate.EnglishName,
                Party = n.Party.ShortEnglishTitle,
                ChangeVotes = aa.ChangeVotes,
                Votes = aa.Votes,
                Action = aa.LastActionMode
            }).ToList();
        return Task.FromResult(q);
    }

    public Task<List<GeneralItemDTO>> GetConstituenciesByAssembly(int assemblyId, int phaseId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.Nominations
            where aa.ElectionAssemblyId == assemblyId &&
                  aa.ElectionPhaseId == phaseId
            select new GeneralItemDTO
            {
                EnglishTitle = aa.Structure.EnglishTitle,
                UrduTitle = aa.Structure.UrduTitle,
                Id = aa.StructureId
            }).Distinct().ToList();
        return Task.FromResult(q);
    }

    public Task<List<NamanigarDTO>> GetConstituencyNamanigars(int constituencyId, int phaseId)
    {
        using var dc = _contextFactory.CreateDbContext();

        var phn = dc.PhaseWiseNamanigars.Where(c => c.PhaseId == phaseId)
            .Select(c => c.NamanigarId)
            .Distinct()
            .ToList();

        
        var q = (from aa in dc.NamanigarConstituencies
            where aa.ConstituencyId == constituencyId &&
                  aa.Namanigar.IsActive && 
                  phn.Contains(aa.NamanigarId)
                 orderby aa.Namanigar.Code
            select new NamanigarDTO
            {
                Name = $"{aa.Namanigar.Code} - {aa.Namanigar.Name}",
                ConstituencyId = constituencyId,
                Code = aa.Namanigar.Code,
                Email = aa.Namanigar.Email,
                Id = aa.NamanigarId,
                Phone = aa.Namanigar.Phone,
                District = aa.Namanigar.District,
                PSDetail = aa.PSDetail
            }).ToList();
        return Task.FromResult(q);
    }

    public Task<int> GetCurrentPhase()
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.ElectionPhases
            where aa.IsActive
            select aa).FirstOrDefault();
        return q != null ? Task.FromResult(q.Id) : Task.FromResult(0);
    }


    public Task<int> GetPSVotesCast(int pollingStationId, int phaseId)
    {
        using var dc = _contextFactory.CreateDbContext();

        var q = (from a in dc.PSTotalVoteCastes
            where a.PhaseId == phaseId &&
                  a.PollingStationId == pollingStationId
            select a).FirstOrDefault();
        if (q == null)
            return Task.FromResult(0);
        return Task.FromResult(q.Votes);
    }

    public Task<List<ResultDetailDTO>> GetElectionResults(int assemblyId, int phaseId, int structureId,
        int seatTypeId, int? pollingStationId = null)
    {
        using var dc = _contextFactory.CreateDbContext();

        var isRePoll = (from aa in dc.RePolls
            where aa.PhaseId == phaseId &&
                  aa.ConstituencyId == structureId
            select aa).Any();

        var psQ = (from aa in dc.Structures
            where aa.Id == structureId &&
                  aa.AssemblyId == assemblyId &&
                  aa.IsPostponed == false
            select new
            {
                aa.TotalPollingStations,
                aa.MaleVoters,
                aa.FemaleVoters
            }).FirstOrDefault();
        var totalPollingStations = 0;
        var totalVoters = 0;
        if (psQ != null) totalPollingStations = psQ.TotalPollingStations ?? 0;
        if (psQ.MaleVoters == null || psQ.FemaleVoters == null)
        {
            totalVoters = 0;
        }
        else
        {
            var mv = psQ.MaleVoters ?? 0;
            var fv = psQ.FemaleVoters ?? 0;
            totalVoters = mv + fv;
        }

        var q = (from aa in dc.Nominations
            where aa.ElectionAssemblyId == assemblyId &&
                  aa.ElectionPhaseId == phaseId &&
                  aa.StructureId == structureId &&
                  aa.SeatTypeId == seatTypeId
            orderby aa.Votes ?? 0 descending, aa.Weight == 0 ? 1000000 : aa.Weight
            select new ResultDetailDTO
            {
                AssemblyId = aa.ElectionAssemblyId,
                CandidateId = aa.CandidteId,
                ElectionId = aa.ElectionAssembly.ElectionId,
                EnglishName = aa.Candidate.EnglishName,
                Id = aa.Id,
                PartyId = aa.PartyId,
                IsNew = false,
                Party = aa.Party.EnglishTitle,
                PartyUrdu = aa.Party.UrduTitle,
                PhaseId = aa.ElectionPhaseId,
                SeatTypeId = aa.SeatTypeId,
                ConstituencyId = aa.StructureId,
                Symbol = aa.Symbol.EnglishTitle,
                SymbolId = aa.SymbolId,
                SymbolUrdu = aa.Symbol.UrduTitle,
                UrduName = aa.Candidate.UrduName,
                Weight = aa.Weight,
                Votes = aa.Votes ?? 0,
                PSVotes = aa.PSResults.Where(k => k.NominationId == aa.Id && k.Votes > 0).Sum(k => k.Votes),
                MaleVoters = aa.Structure.MaleVoters ?? 0,

                FemaleVoters = aa.Structure.FemaleVoters ?? 0,
                //VotersCount = (aa.Structure.MaleVoters ?? 0) + (aa.Structure.FemaleVoters ?? 0),
                TotalPollingStations = totalPollingStations,
                ResultPollingStations = aa.ResultPollingStations ?? 0,
                ChangeVotes = 0,
                Mode = "Add",
                IsWithdraw = aa.IsWithdraw,
                FemaleVotersPS = aa.Structure.PollingStations.Sum(k => k.FemaleVoters ?? 0),
                MaleVotersPS = aa.Structure.PollingStations.Sum(k => k.MaleVoters ?? 0),
                TotalPollingStationsPS = aa.Structure.PollingStations.Count(),
                PollingStationCompletedPS = aa.PSResults.Select(k => k.PollingStationId).Distinct().Count(),
                VotersCastePS = (from kk in dc.PSResults
                    where kk.Nomination.ElectionPhaseId == phaseId &&
                          kk.Nomination.ElectionAssemblyId == assemblyId &&
                          kk.Nomination.SeatTypeId == seatTypeId &&
                          kk.Nomination.StructureId == aa.StructureId
                    select kk.Votes).Sum(),
                TotalVotesPS = (from mm in dc.PSResults
                    where mm.NominationId == aa.Id
                    select mm.Votes).Sum(),
                IsBilaMokabilaWinner = aa.IsBilaMokabilaWinner,
                IsRePoll = isRePoll
            }).ToList();

        foreach (var cc in q)
        {
            if (cc.FemaleVotersPS + cc.MaleVotersPS > 0)
            {
                double total = cc.FemaleVotersPS + cc.MaleVotersPS;
                double vc = cc.VotersCastePS;
                cc.VoterTurnOutPS = (float)Math.Round(vc / total * 100.0, 2);
            }

            cc.candidateImg =
                !File.Exists(
                    Directory.GetCurrentDirectory() + $"\\wwwroot\\media\\candidates\\{cc.CandidateId}.jpg")
                    ? "/media/candidates/Blank.jpg"
                    : $"/media/candidates/{cc.CandidateId}.jpg";

            cc.flagImg = File.Exists(Directory.GetCurrentDirectory() + $"\\wwwroot\\media\\flags\\{cc.PartyId}.jpg")
                ? $"/media/flags/{cc.PartyId}.jpg"
                : "/media/flags/missing-flag.png";

            cc.symbolImg =
                File.Exists(Directory.GetCurrentDirectory() + $"\\wwwroot\\media\\symbols\\{cc.SymbolId}.jpg")
                    ? $"/media/symbols/{cc.SymbolId}.jpg"
                    : "/media/symbols/missing-symbol.png";
        }

        if (pollingStationId != null)
            foreach (var nm in q)
            {
                var vt = (from aa in dc.PSResults
                    where aa.NominationId == nm.Id &&
                          aa.PollingStationId == (int)pollingStationId
                    select aa).FirstOrDefault();
                nm.PSVotes = vt?.Votes ?? 0;
            }


        var ps = (from a in dc.PollingSchemes
            where a.StructureId == structureId &&
                  a.PhaseId == phaseId
            select a).FirstOrDefault();

        foreach (var i in q)
        {
            i.TotalPollingStations = 0;
            i.MaleVoters = 0;
            i.FemaleVoters = 0;
        }

        if (ps != null)
            foreach (var i in q)
            {
                i.TotalPollingStations = ps.TotalPollingStations ?? 0;
                i.MaleVoters = ps.MaleVoters ?? 0;
                i.FemaleVoters = ps.FemaleVoters ?? 0;
                i.ResultMode = ps.ResultMode;
                i.RejectedVotes = ps.RejectedVotes;
            }

        return Task.FromResult(q);
    }

    public Task<ResultDTO> GetHubResult(int constituencyId, int assemblyId, int phaseId, int seatTypeId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q1 = (from aa in dc.Nominations
                where aa.StructureId == constituencyId &&
                      aa.SeatTypeId == seatTypeId &&
                      aa.ElectionPhaseId == phaseId &&
                      aa.SeatTypeId == seatTypeId &&
                      aa.Votes > 0
                orderby aa.Votes ?? 0 descending
                select new ResultDTO
                {
                    Assembly = aa.ElectionAssembly.EnglishTitle,
                    AssemblyId = aa.ElectionAssemblyId,
                    Constituency = aa.Structure.EnglishTitle,
                    ConstituencyId = aa.StructureId,
                    Election = aa.ElectionPhase.Election.EnglishTitle,
                    ElectionId = aa.ElectionPhase.ElectionId,
                    LastUpdatedOn = aa.ModifiedDate ?? aa.CreatedDate,
                    Phase = aa.ElectionPhase.Title,
                    PhaseId = aa.ElectionPhaseId,
                    ResultOfPollingStations = aa.ResultPollingStations ?? 0,
                    SeatType = aa.SeatType.EnglishTitle,
                    SeatTypeId = aa.SeatTypeId,
                    TotalPollingStations = aa.Structure.TotalPollingStations ?? 0,
                    TotalVoters = aa.Structure.TotalVoters,
                    WinnerCandidateId = aa.CandidteId,
                    WinnerName = aa.Candidate.EnglishName,
                    WinnerParty = aa.Party.ShortEnglishTitle,
                    WinnerVotes = aa.Votes ?? 0
                }
            ).FirstOrDefault();

        if (q1 != null)
        {
            var q2 = (from aa in dc.Nominations
                    where aa.StructureId == constituencyId &&
                          aa.SeatTypeId == seatTypeId &&
                          aa.ElectionPhaseId == phaseId &&
                          aa.SeatTypeId == seatTypeId &&
                          aa.CandidteId != q1.WinnerCandidateId &&
                          aa.Votes > 0
                    orderby aa.Votes ?? 0 descending
                    select new ResultDTO
                    {
                        RunnerUpCandidateId = aa.CandidteId,
                        RunnerUpName = aa.Candidate.EnglishName,
                        RunnerUpParty = aa.Party.ShortEnglishTitle,
                        RunnerUpVotes = aa.Votes ?? 0
                    }
                ).FirstOrDefault();
            if (q2 != null)
            {
                q1.RunnerUpVotes = q2.RunnerUpVotes;
                q1.RunnerUpCandidateId = q2.RunnerUpCandidateId;
                q1.RunnerUpName = q2.RunnerUpName;
                q1.RunnerUpParty = q2.RunnerUpParty;
            }

            return Task.FromResult(q1);
        }

        return null;
    }

    public Task<ImmResultDTO> GetImmResult(int electionId, int phaseId, int assemblyId, int constituencyId,
        int seatTypeId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.Nominations
            where aa.ElectionPhaseId == phaseId &&
                  aa.ElectionAssemblyId == assemblyId &&
                  aa.StructureId == constituencyId &&
                  aa.SeatTypeId == seatTypeId
            select new ImmResultDTO
            {
                constituency_id = aa.StructureId,
                constituency_name =
                    aa.Structure.Code.Replace(" ", "").Replace("-", ""), //aa.Structure.EnglishTitle.Trim(),
                number_of_polling_station = aa.ResultPollingStations ?? 0
            }).FirstOrDefault();
        q.candidate_info = (from aa in dc.Nominations
            where aa.ElectionPhaseId == phaseId &&
                  aa.ElectionAssemblyId == assemblyId &&
                  aa.StructureId == constituencyId &&
                  aa.SeatTypeId == seatTypeId
            select new ImmCandidateInfoDTO
            {
                candidate_id = aa.CandidteId,
                nomination_id = aa.Id,
                party_id = aa.PartyId,
                status = "PUBLISHED",
                votes = aa.Votes ?? 0
            }).ToList();

        return Task.FromResult(q);
    }

    public Task<ImmResultDTO> GetImmResultPS(int electionId, int phaseId, int assemblyId, int constituencyId,
        int seatTypeId, int pollingStationId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.Nominations
            where aa.ElectionPhaseId == phaseId &&
                  aa.ElectionAssemblyId == assemblyId &&
                  aa.StructureId == constituencyId &&
                  aa.SeatTypeId == seatTypeId
            select new ImmResultDTO
            {
                constituency_id = aa.StructureId,
                constituency_name = aa.Structure.EnglishTitle.Trim(),
                number_of_polling_station = aa.ResultPollingStations ?? 0,
                polling_station_id = pollingStationId,
                polling_station_name = dc.PollingStations.Find(pollingStationId).EnglishTitle
            }).FirstOrDefault();
        q.candidate_info = (from mm in dc.PSResults
            where mm.Nomination.ElectionPhaseId == phaseId &&
                  mm.Nomination.ElectionAssemblyId == assemblyId &&
                  mm.Nomination.StructureId == constituencyId &&
                  mm.Nomination.SeatTypeId == seatTypeId &&
                  mm.PollingStationId == pollingStationId
            select new ImmCandidateInfoDTO
            {
                candidate_id = mm.Nomination.CandidteId,
                nomination_id = mm.NominationId,
                party_id = mm.Nomination.PartyId,
                status = "PUBLISHED",
                votes = mm.Votes
            }).ToList();
        return Task.FromResult(q);
    }

    public Task<List<ResultDTO>> GetLivePSResults(int phaseId)
    {
        using (var dc = _contextFactory.CreateDbContext())
        {
            var con = dc.Database.GetDbConnection();
            var res = con.Query<TempPSResultDto>("app.GetPSLiveTickerList", commandType: CommandType.StoredProcedure)
                .ToList();
            var op = new List<ResultDTO>();

            foreach (var r in res)
                if (!op.Any(m => m.ConstituencyId == r.ConstituencyId))
                {
                    var k = new ResultDTO
                    {
                        ConstituencyId = r.ConstituencyId,
                        Constituency = r.Constituency,
                        Assembly = r.Assembly,
                        AssemblyId = r.AssemblyId,
                        PhaseId = phaseId,
                        Phase = r.Phase,
                        WinnerName = r.Candidate,
                        WinnerParty = r.Party,
                        WinnerVotes = r.Votes,
                        RunnerUpName = r.RunnerUp,
                        RunnerUpParty = r.RunnerUpParty,
                        RunnerUpVotes = r.RunnerUpVotes,
                        TotalPollingStations = r.TotalPollingStations,
                        ResultOfPollingStations = r.PSCount,
                        WinnerKaKee = r.KaKee,
                        RunnerUpKaKee = r.RunnerUpKaKee
                    };
                    k.Ticker = $"{k.Constituency} / ضمنی الیکشن" + "<br />";
                    k.Ticker +=
                        $"{k.Constituency} : {k.ResultOfPollingStations} پولنگ اسٹیشنز کا غیر سرکاری غیر حتمی نتیجہ" +
                        "<br />";
                    if (k.WinnerParty == "آزاد")
                    {
                        if (k.TotalPollingStations == k.ResultOfPollingStations)
                            k.Ticker += $"آزاد امیدوار {k.WinnerName} {k.WinnerVotes} ووٹ لے کر کامیاب<br />";
                        else
                            k.Ticker += $"آزاد امیدوار {k.WinnerName} {k.WinnerVotes} ووٹ لے کر آگے" + "<br />";
                    }
                    else
                    {
                        if (k.TotalPollingStations == k.ResultOfPollingStations)
                            k.Ticker +=
                                $"{k.WinnerParty} {k.WinnerKaKee} {k.WinnerName} {k.WinnerVotes} ووٹ لے کر کامیاب<br />";
                        else
                            k.Ticker +=
                                $"{k.WinnerParty} {k.WinnerKaKee} {k.WinnerName} {k.WinnerVotes} ووٹ لے کر آگے" +
                                "<br />";
                        op.Add(k);
                    }

                    if (k.RunnerUpParty == "آزاد")
                        k.Ticker += $"آزاد امیدوار {k.RunnerUpName} نے {k.RunnerUpVotes} ووٹ حاصل کیے";
                    else
                        k.Ticker +=
                            $"{k.RunnerUpParty} {k.RunnerUpKaKee} {k.RunnerUpName} نے {k.RunnerUpVotes} ووٹ حاصل کیے";
                }

            return Task.FromResult(op);
        }
    }


    public Task<string> GetLiveResultDetailLB(int electionId, int assemblyId, int phaseId, int constituencyId,
        int seatTypeId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var assemblyTypeId = dc.ElectionAssemblies.Find(assemblyId).AssemblyTypeId;
        var con = dc.Database.GetDbConnection();

        var prams = new { ConstituencyId = constituencyId, SeatTypeId = seatTypeId, PhaseId = phaseId };
        var ticker = con.QueryFirstAsync<string>("app.spGetTickerText", prams, commandType: CommandType.StoredProcedure)
            .Result;
        return Task.FromResult(ticker);

        //}
    }

    public Task<string> GetLiveResultDetailLB_WithoutDapper(int electionId, int assemblyId, int phaseId,
        int constituencyId, int seatTypeId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.Structures
            where aa.Id == constituencyId
            select new LiveResultDetailDTO
            {
                Code = aa.Code,
                EnglishTitle = aa.EnglishTitle.Trim(),
                UrduTitle = aa
                    .UrduTitle, //aa.Assembly.AssemblyTypeId == 3 || aa.Assembly.AssemblyTypeId == 4 ? $"{aa.Assembly.UrduTitle} کی {aa.Assembly.AssemblyType.UrduTitle} {aa.Code}  کا نتیجہ" : aa.Assembly.UrduTitle.Trim() + " " + aa.UrduTitle.Trim(),
                Assembly = aa.Assembly.UrduTitle,
                RegisterVoters = aa.MaleVoters ?? 0 + aa.FemaleVoters ?? 0,
                TotalPollingStations = aa.TotalPollingStations ?? 0,
                AssemblyType = aa.Assembly.AssemblyType.UrduTitle
            }).FirstOrDefault();
        if (q != null)
        {
            q.ResultPollingStations = (from aa in dc.Nominations
                where aa.ElectionPhaseId == phaseId &&
                      aa.ElectionAssemblyId == assemblyId &&
                      aa.StructureId == constituencyId &&
                      aa.SeatTypeId == seatTypeId
                select aa.ResultPollingStations).FirstOrDefault() ?? 0;

            q.Nominations = (from aa in dc.Nominations
                orderby aa.Votes ?? 0 descending, aa.Weight, aa.Candidate.EnglishName
                where aa.ElectionPhaseId == phaseId &&
                      aa.ElectionAssemblyId == assemblyId &&
                      aa.StructureId == constituencyId &&
                      aa.SeatTypeId == seatTypeId &&
                      aa.Votes > 0
                select new LiveResultCandidateDetailDTO
                {
                    Party = aa.Party.EnglishTitle,
                    PartyUrdu = aa.Party.UrduTitle,
                    Candidate = aa.Candidate.EnglishName,
                    CandidateUrdu = aa.Candidate.UrduName,
                    Votes = aa.Votes ?? 0,
                    Gender = aa.Candidate.Gender ?? Gender.Male,
                    LastUpdate = aa.ModifiedDate,
                    SeatType = aa.SeatType.UrduTitle
                }).Take(2).ToList();

            var op = "خیبر پختونخوا بلدیاتی اتخابات / نتائج<br/>";

            if (q.AssemblyType == "سٹی کونسل" || q.AssemblyType == "تحصیل کونسل")
            {
                var seat = q.AssemblyType == "سٹی کونسل" ? "میئر" : "چیئرمین";
                op += $"{q.Assembly} کا غیر سرکاری نتیجہ<br/>";
                if (q.Nominations == null || q.Nominations.Count == 0) return null;

                if (q.Nominations.Count == 1)
                {
                    var winerKaKi = q.Nominations[0].Gender == Gender.Male ? "کے" : "کی";
                    var aagayYaKamyab = q.TotalPollingStations == q.ResultPollingStations ? "کامیاب" : "آگے";
                    //op += $"{q.Assembly}-{q.Nominations[0].SeatType} کے {q.TotalPollingStations} میں سے {q.ResultPollingStations} کا نتیجہ<br/>";

                    if (q.Nominations[0].PartyUrdu == "آزاد")
                        //op += $"آزاد امیدوار {q.Nominations[0].CandidateUrdu} {q.Nominations[0].Votes} ووٹ لیکر {aagayYaKamyab}";
                        op +=
                            $"آزاد امیدوار {q.Nominations[0].CandidateUrdu} {q.ResultPollingStations} پولنگ اسٹیشنز میں {q.Nominations[0].Votes} ووٹ لے کر {seat} کی نشست پر {aagayYaKamyab}";
                    else
                        //op += $"{q.Nominations[0].PartyUrdu} {winerKaKi} {q.Nominations[0].CandidateUrdu} {q.Nominations[0].Votes}ووٹ لیکر {aagayYaKamyab}";
                        op +=
                            $"{q.Nominations[0].PartyUrdu} {winerKaKi} {q.Nominations[0].CandidateUrdu} {q.ResultPollingStations} پولنگ اسٹیشنز میں {q.Nominations[0].Votes} ووٹ لے کر {seat} کی نشست پر {aagayYaKamyab}";

                    if (q.Nominations[0].LastUpdate != null)
                        op += "<br/><span class='tiker-last-update'>Last Update: " +
                              q.Nominations[0].LastUpdate.Value.ToString("d MMM, yyyy h:mm:ss") + "</span>";

                    return Task.FromResult(op);
                }

                if (q.Nominations.Count == 2)
                {
                    var winnerKaKi = q.Nominations[0].Gender == Gender.Male ? "کے" : "کی";
                    if (q.Nominations[0].PartyUrdu == "آزاد")
                    {
                        if (q.TotalPollingStations != q.ResultPollingStations)
                            //op += $"آزاد امیدوار {q.Nominations[0].CandidateUrdu} {q.Nominations[0].Votes} ووٹ لیکر {aagayYaKamyab}";
                            op +=
                                $"آزاد امیدوار {q.Nominations[0].CandidateUrdu} {q.ResultPollingStations} پولنگ اسٹیشنز میں {q.Nominations[0].Votes} ووٹ لے کر {seat} کی نشست پر آگے<br/>";
                        else
                            op +=
                                $"آزاد امیدوار {q.Nominations[0].CandidateUrdu} {q.Nominations[0].Votes} ووٹ لے کر {seat} {q.Assembly} منتخب<br/>";
                    }
                    else
                    {
                        if (q.TotalPollingStations != q.ResultPollingStations)
                            //op += $"{q.Nominations[0].PartyUrdu} {winerKaKi} {q.Nominations[0].CandidateUrdu} {q.Nominations[0].Votes}ووٹ لیکر {aagayYaKamyab}";
                            op +=
                                $"{q.Nominations[0].PartyUrdu} {winnerKaKi} {q.Nominations[0].CandidateUrdu} {q.ResultPollingStations} پولنگ اسٹیشنز میں {q.Nominations[0].Votes} ووٹ لے کر {seat} کی نشست پر آگے<br/>";
                        else
                            op +=
                                $"{q.Nominations[0].PartyUrdu} {winnerKaKi} {q.Nominations[0].CandidateUrdu} {q.Nominations[0].Votes} ووٹ لے کر {seat} {q.Assembly}  منتخب<br/>";
                    }

                    var runnerKaKi = q.Nominations[1].Gender == Gender.Male ? "کے" : "کی";
                    if (q.Nominations[1].PartyUrdu == "آزاد")
                    {
                        // پیپلز پارٹی کے کامران خان 600 ووٹ لے کر میئر کی نشست پر دوسرے نمبر
                        //op += $"آزاد امیدوار {q.Nominations[1].CandidateUrdu} {q.Nominations[1].Votes} ووٹ لیکر دوسرے نمبر پر<br/>";
                        if (q.TotalPollingStations != q.ResultPollingStations)
                            op +=
                                $"آزاد امیدوار {q.Nominations[1].CandidateUrdu} {q.Nominations[1].Votes} ووٹ لے کر {seat} کی نشست پر دوسرے نمبر<br/>";
                        else
                            op +=
                                $"{seat} کی نشست پر دوسرے نمبر پر آزاد امیدوار {q.Nominations[1].CandidateUrdu} کو {q.Nominations[1].Votes} ووٹ ملے<br/>";
                    }
                    else
                    {
                        //op += $"{q.Nominations[1].PartyUrdu} {runnerKaKi} {q.Nominations[1].CandidateUrdu} {q.Nominations[1].Votes} ووٹ لیکر دوسرے نمبر پر<br/>";
                        if (q.TotalPollingStations != q.ResultPollingStations)
                            op +=
                                $"{q.Nominations[1].PartyUrdu} {runnerKaKi} {q.Nominations[1].CandidateUrdu} {q.Nominations[1].Votes} ووٹ لے کر {seat} کی نشست پر دوسرے نمبر<br/>";
                        else
                            op +=
                                $"{seat} کی نشست پر دوسرے نمبر پر {q.Nominations[1].PartyUrdu} {runnerKaKi} {q.Nominations[1].CandidateUrdu} {q.Nominations[1].Votes} ووٹ ملے<br/>";
                    }

                    if (q.Nominations[0].LastUpdate != null)
                        op += "<span class='tiker-last-update'>Last Update: " +
                              q.Nominations[0].LastUpdate.Value.ToString("d MMM, yyyy h:mm:ss") + "</span>";

                    return Task.FromResult(op);
                }
            }
            else if (q.AssemblyType == "دیہی کونسل" || q.AssemblyType == "ہمسایہ کونسل")
            {
                q.Assembly = q.Assembly.Replace("ویلیج کونسلز", string.Empty);
                q.Assembly = q.Assembly.Replace("بنیبر ہڈ کونسلز", string.Empty);
                q.Assembly = q.Assembly.Replace("دیہی کاونسلز", string.Empty);
                q.Assembly = q.Assembly.Replace("نیبرہڈ کونسلز", string.Empty);
                q.Assembly = q.Assembly.Replace("نیبرہڈ کونسلز", string.Empty);
                q.Assembly = q.Assembly.Replace("نیبر ہڈ کونسلز", string.Empty);
                op += $"{q.Assembly} کی {q.AssemblyType} {q.Code} {q.UrduTitle} کا نتیجہ<br/>";
                var winnerKaKi = q.Nominations[0].Gender == Gender.Male ? "کے" : "کی";
                var party = q.Nominations[0].PartyUrdu == "آزاد"
                    ? "آزاد امیدوار"
                    : $"{q.Nominations[0].PartyUrdu} {winnerKaKi}";
                op += $"{party} {q.Nominations[0].CandidateUrdu} {q.Nominations[0].SeatType} منتخب<br/>";
                if (q.Nominations[0].LastUpdate != null)
                    op += "<span class='tiker-last-update'>Last Update: " +
                          q.Nominations[0].LastUpdate.Value.ToString("d MMM, yyyy h:mm:ss") + "</span>";

                return Task.FromResult(op);
            }
            else if (q.AssemblyType == "کنٹونمنٹ بورڈ")
            {
            }
        }

        return Task.FromResult(string.Empty);
    }

    public Task<string> GetLiveResultDetailLBActual(int electionId, int assemblyId, int phaseId,
        int constituencyId, int seatTypeId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.Structures
            where aa.Id == constituencyId
            select new LiveResultDetailDTO
            {
                Code = aa.Code,
                EnglishTitle = aa.EnglishTitle.Trim(),
                UrduTitle = aa
                    .UrduTitle, //aa.Assembly.AssemblyTypeId == 3 || aa.Assembly.AssemblyTypeId == 4 ? $"{aa.Assembly.UrduTitle} کی {aa.Assembly.AssemblyType.UrduTitle} {aa.Code}  کا نتیجہ" : aa.Assembly.UrduTitle.Trim() + " " + aa.UrduTitle.Trim(),
                Assembly = aa.Assembly.UrduTitle,
                RegisterVoters = aa.MaleVoters ?? 0 + aa.FemaleVoters ?? 0,
                TotalPollingStations = aa.TotalPollingStations ?? 0,
                AssemblyType = aa.Assembly.AssemblyType.UrduTitle
            }).FirstOrDefault();
        if (q != null)
        {
            q.ResultPollingStations = (from aa in dc.Nominations
                where aa.ElectionPhaseId == phaseId &&
                      aa.ElectionAssemblyId == assemblyId &&
                      aa.StructureId == constituencyId &&
                      aa.SeatTypeId == seatTypeId
                select aa.ResultPollingStations).FirstOrDefault() ?? 0;

            q.Nominations = (from aa in dc.Nominations
                orderby aa.Votes ?? 0 descending, aa.Weight, aa.Candidate.EnglishName
                where aa.ElectionPhaseId == phaseId &&
                      aa.ElectionAssemblyId == assemblyId &&
                      aa.StructureId == constituencyId &&
                      aa.SeatTypeId == seatTypeId &&
                      aa.Votes > 0
                select new LiveResultCandidateDetailDTO
                {
                    Party = aa.Party.EnglishTitle,
                    PartyUrdu = aa.Party.UrduTitle,
                    Candidate = aa.Candidate.EnglishName,
                    CandidateUrdu = aa.Candidate.UrduName,
                    Votes = aa.Votes ?? 0,
                    Gender = aa.Candidate.Gender ?? Gender.Male,
                    LastUpdate = aa.ModifiedDate,
                    SeatType = aa.SeatType.UrduTitle
                }).Take(2).ToList();

            var op = string.Empty;

            if (q.AssemblyType == "سٹی کونسل" || q.AssemblyType == "تحصیل کونسل")
            {
                if (q.Nominations == null || q.Nominations.Count == 0) return null;

                if (q.Nominations.Count == 1)
                {
                    var winerKaKi = q.Nominations[0].Gender == Gender.Male ? "کے" : "کی";
                    var aagayYaKamyab = q.TotalPollingStations == q.ResultPollingStations ? "کامیاب" : "آگے";
                    op +=
                        $"{q.Assembly}-{q.Nominations[0].SeatType} کے {q.TotalPollingStations} میں سے {q.ResultPollingStations} کا نتیجہ<br/>";

                    if (q.Nominations[0].PartyUrdu == "آزاد")
                        op +=
                            $"آزاد امیدوار {q.Nominations[0].CandidateUrdu} {q.Nominations[0].Votes} ووٹ لیکر {aagayYaKamyab}";
                    else
                        op +=
                            $"{q.Nominations[0].PartyUrdu} {winerKaKi} {q.Nominations[0].CandidateUrdu} {q.Nominations[0].Votes}ووٹ لیکر {aagayYaKamyab}";

                    if (q.Nominations[0].LastUpdate != null)
                        op += "<span class='tiker-last-update'>Last Update: " +
                              q.Nominations[0].LastUpdate.Value.ToString("d MMM, yyyy h:mm:ss") + "</span>";

                    return Task.FromResult(op);
                }

                if (q.Nominations.Count == 2)
                {
                    var winerKaKi = q.Nominations[0].Gender == Gender.Male ? "کے" : "کی";
                    var aagayYaKamyab = q.TotalPollingStations == q.ResultPollingStations ? "کامیاب" : "آگے";
                    op +=
                        $"{q.Assembly}-{q.Nominations[0].SeatType} کے {q.TotalPollingStations} میں سے {q.ResultPollingStations} کا نتیجہ<br/>";
                    if (q.Nominations[0].PartyUrdu == "آزاد")
                        op +=
                            $"آزاد امیدوار {q.Nominations[0].CandidateUrdu} {q.Nominations[0].Votes} ووٹ لیکر {aagayYaKamyab}<br/>";
                    else
                        op +=
                            $"{q.Nominations[0].PartyUrdu} {winerKaKi} {q.Nominations[1].CandidateUrdu} {q.Nominations[0].Votes} ووٹ لیکر {aagayYaKamyab}<br/>";

                    var runnerKaKi = q.Nominations[1].Gender == Gender.Male ? "کے" : "کی";
                    if (q.Nominations[1].PartyUrdu == "آزاد")
                        op +=
                            $"آزاد امیدوار {q.Nominations[1].CandidateUrdu} {q.Nominations[1].Votes} ووٹ لیکر دوسرے نمبر پر<br/>";
                    else
                        op +=
                            $"{q.Nominations[1].PartyUrdu} {runnerKaKi} {q.Nominations[1].CandidateUrdu} {q.Nominations[1].Votes} ووٹ لیکر دوسرے نمبر پر<br/>";

                    if (q.Nominations[0].LastUpdate != null)
                        op += "<span class='tiker-last-update'>Last Update: " +
                              q.Nominations[0].LastUpdate.Value.ToString("d MMM, yyyy h:mm:ss") + "</span>";

                    return Task.FromResult(op);
                }
            }
            else if (q.AssemblyType == "دیہی کونسل" || q.AssemblyType == "ہمسایہ کونسل")
            {
                q.Assembly = q.Assembly.Replace("ویلیج کونسلز", string.Empty);
                q.Assembly = q.Assembly.Replace("بنیبر ہڈ کونسلز", string.Empty);
                q.Assembly = q.Assembly.Replace("دیہی کاونسلز", string.Empty);
                q.Assembly = q.Assembly.Replace("نیبرہڈ کونسلز", string.Empty);
                q.Assembly = q.Assembly.Replace("نیبرہڈ کونسلز", string.Empty);
                op += $"{q.Assembly} کی {q.AssemblyType} {q.Code} {q.UrduTitle} کا نتیجہ<br/>";
                var winnerKaKi = q.Nominations[0].Gender == Gender.Male ? "کے" : "کی";
                var party = q.Nominations[0].PartyUrdu == "آزاد"
                    ? "آزاد امیدوار"
                    : $"{q.Nominations[0].PartyUrdu} {winnerKaKi}";
                op += $"{party} {q.Nominations[0].CandidateUrdu} {q.Nominations[0].SeatType} منتخب<br/>";
                if (q.Nominations[0].LastUpdate != null)
                    op += "<span class='tiker-last-update'>Last Update: " +
                          q.Nominations[0].LastUpdate.Value.ToString("d MMM, yyyy h:mm:ss") + "</span>";

                return Task.FromResult(op);
            }
            else if (q.AssemblyType == "کنٹونمنٹ بورڈ")
            {
            }
        }

        return Task.FromResult(string.Empty);
    }

    public Task<List<ResultDTO>> GetPendingBBApprovals(int? phaseId)
    {
        if (phaseId != null)
        {
            using var dc = _contextFactory.CreateDbContext();
            var con = dc.Database.GetDbConnection();
            var parms = new { PhaseId = phaseId };
            var op = con
                .Query<ResultDTO>("app.spGetPendingBBApprovals", parms, commandType: CommandType.StoredProcedure)
                .ToList();

            return Task.FromResult(op);
        }

        return Task.FromResult(new List<ResultDTO>());
    }

    public Task<LiveResultDetailDTO> GetLiveResultDetail(int electionId, int assemblyId, int phaseId,
        int constituencyId, int seatTypeId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.Structures
            where aa.Id == constituencyId
            select new LiveResultDetailDTO
            {
                Code = aa.Code,
                EnglishTitle = aa.EnglishTitle.Trim(),
                UrduTitle = aa.Assembly.AssemblyTypeId == 3 || aa.Assembly.AssemblyTypeId == 4
                    ? $"{aa.Assembly.UrduTitle} کی {aa.Assembly.AssemblyType.UrduTitle} {aa.Code}  کا نتیجہ"
                    : aa.Assembly.UrduTitle.Trim() + " " + aa.UrduTitle.Trim(),
                RegisterVoters = aa.MaleVoters ?? 0 + aa.FemaleVoters ?? 0,
                TotalPollingStations = aa.TotalPollingStations ?? 0,
                AssemblyType = aa.Assembly.AssemblyType.UrduTitle
            }).FirstOrDefault();
        q.UrduTitle = q.UrduTitle.Replace("دیہی کاونسلز", string.Empty);
        q.UrduTitle = q.UrduTitle.Replace("ہمسایہ کاونسلز", string.Empty);
        q.UrduTitle = q.UrduTitle.Replace("سٹی کونسل", string.Empty);

        if (q != null)
        {
            q.ResultPollingStations = (from aa in dc.Nominations
                where aa.ElectionPhaseId == phaseId &&
                      aa.ElectionAssemblyId == assemblyId &&
                      aa.StructureId == constituencyId &&
                      aa.SeatTypeId == seatTypeId
                select aa.ResultPollingStations).FirstOrDefault() ?? 0;
            // Get All Nominations of this constituency
            q.Nominations = (from aa in dc.Nominations
                orderby aa.Votes ?? 0 descending, aa.Candidate.EnglishName
                where aa.ElectionPhaseId == phaseId &&
                      aa.ElectionAssemblyId == assemblyId &&
                      aa.StructureId == constituencyId &&
                      aa.SeatTypeId == seatTypeId
                select new LiveResultCandidateDetailDTO
                {
                    Party = aa.Party.EnglishTitle,
                    PartyUrdu = aa.Party.UrduTitle,
                    Candidate = aa.Candidate.EnglishName,
                    CandidateUrdu = aa.Candidate.UrduName,
                    Votes = aa.Votes ?? 0,
                    Gender = aa.Candidate.Gender ?? Gender.Male,
                    LastUpdate = aa.ModifiedDate
                }).Take(2).ToList();
            if (q == null || q.Nominations == null || q.Nominations.Count == 0) return null;

            var winnerName = q.Nominations[0].CandidateUrdu;
            var winnerParty = q.Nominations[0].PartyUrdu;
            var winnerVotes = q.Nominations[0].Votes;
            var winnerGender = q.Nominations[0].Gender;
            var winnerKaKee = winnerGender == Gender.Male ? "کے" : "کی";
            var lastUpdate = q.Nominations[0].LastUpdate;

            var runnerUpName = string.Empty;
            var runnerUpParty = string.Empty;
            var runnerUpVotes = 0;
            var runnerUpGender = Gender.Male;
            var runnerUpKaKee = "کے";

            try
            {
                runnerUpName = q.Nominations[1].CandidateUrdu;
                runnerUpParty = q.Nominations[1].PartyUrdu;
                runnerUpVotes = q.Nominations[1].Votes;
                runnerUpGender = q.Nominations[1].Gender;
                runnerUpKaKee = runnerUpGender == Gender.Male ? "کے" : "کی";
            }
            catch (Exception)
            {
                // ignore
            }

            q.Code = q.Code.Trim().ToUpper();

            if (q.Code.StartsWith("NC") || q.Code.StartsWith("VC"))
            {
                var op = " غیر حتمی غیر سرکاری نتائج " + "<br />";
                op += "تحصیل " + q.UrduTitle;
                op += "<br />";
                if (winnerParty == "آزاد")
                    op += $"آزاد امیدوار {winnerName} چیئِرمین منتخب";
                else
                    op += $"{winnerParty} {winnerKaKee} {winnerName} چیئِرمین منتخب";

                if (lastUpdate != null)
                    op += "<br /><span class='tiker-last-update'>Last Update: " +
                          lastUpdate.Value.ToString("d MMM, yyyy h:mm:ss") + "</span>";
                q.Ticker = op;
            }
            else
            {
                if (q.TotalPollingStations != q.ResultPollingStations)
                {
                    if (string.IsNullOrEmpty(runnerUpName))
                    {
                        var op = " غیر حتمی غیر سرکاری نتائج " + "<br />";
                        op +=
                            $" {q.UrduTitle} کے {q.TotalPollingStations} میں سے {q.ResultPollingStations} پولنگ اسٹیشن کا نتیجہ " +
                            "<br />";
                        if (winnerParty == "آزاد")
                            op += $" {q.UrduTitle}، {winnerParty} امیدوار {winnerName} {winnerVotes} ووٹ لیکرآگے " +
                                  "<br />";
                        else
                            op +=
                                $" {q.UrduTitle}، {winnerParty} {winnerKaKee} امیدوار {winnerName} {winnerVotes} ووٹ لیکرآگے " +
                                "<br />";

                        if (lastUpdate != null)
                            op += "<br /><span class='tiker-last-update'>Last Update: " +
                                  lastUpdate.Value.ToString("d MMM, yyyy h:mm:ss") + "</span>";
                        q.Ticker = op;
                    }
                    else
                    {
                        var op = "    غیر حتمی غیر سرکاری نتائج" + "<br />";
                        op +=
                            $" {q.UrduTitle} کے {q.TotalPollingStations} میں سے {q.ResultPollingStations} پولنگ اسٹیشن کا نتیجہ " +
                            "<br />";
                        if (winnerParty == "آزاد")
                            op += $" {q.UrduTitle}، {winnerParty} امیدوار {winnerName} {winnerVotes} ووٹ لیکرآگے " +
                                  "<br />";
                        else
                            op +=
                                $" {q.UrduTitle}، {winnerParty} {winnerKaKee} امیدوار {winnerName} {winnerVotes} ووٹ لیکرآگے " +
                                "<br />";

                        if (runnerUpParty == "آزاد")
                            op += $" {q.UrduTitle}، {runnerUpParty} امیدوار {runnerUpVotes} ووٹ لیکر دوسرے نمبر پر " +
                                  "<br/>";
                        else
                            op +=
                                $" {q.UrduTitle}، {runnerUpParty} {runnerUpKaKee} امیدوار {runnerUpName} {runnerUpVotes} ووٹ لیکر دوسرے نمبر پر " +
                                "<br/>";

                        if (lastUpdate != null)
                            op += "<br /><span class='tiker-last-update'>Last Update: " +
                                  lastUpdate.Value.ToString("d MMM, yyyy h:mm:ss") + "</span>";
                        q.Ticker = op;
                        //return Task.FromResult(q);
                    }
                }
                else
                {
                    var op = "  غیر حتمی غیرسرکاری نتیجہ " + "<br />";
                    op += $" {q.UrduTitle} کا مکمل نتیجہ ";
                    if (winnerParty == "آزاد")
                        op += $" {winnerParty} امیدوار {winnerName} {winnerVotes} ووٹ لے کر کامیاب ";
                    else
                        op += $" {winnerParty} {winnerKaKee} {winnerName} {winnerVotes} ووٹ لے کر کامیاب ";

                    if (runnerUpVotes > 0)
                    {
                        if (runnerUpParty == "آزاد")
                            op +=
                                $" {runnerUpParty} امیدوار {runnerUpName} {runnerUpVotes} ووٹ لے کر دوسرے نمبر پر رہے ";
                        else
                            op +=
                                $" {runnerUpParty} {runnerUpKaKee} {runnerUpName} {runnerUpVotes} ووٹ لے کر دوسرے نمبر پر رہے ";
                    }

                    //return Task.FromResult(op);
                    if (lastUpdate != null)
                        op += "<br /><span class='tiker-last-update'>Last Update: " +
                              lastUpdate.Value.ToString("d MMM, yyyy h:mm:ss") + "</span>";
                    q.Ticker = op;
                }
            }

            return Task.FromResult(q);
        }

        return null;
    }

    public Task<LiveResultDetailDTO> GetLiveResultDetailBackup(int electionId, int assemblyId, int phaseId,
        int constituencyId, int seatTypeId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.Structures
            where aa.Id == constituencyId
            select new LiveResultDetailDTO
            {
                Code = aa.Code,
                EnglishTitle = aa.EnglishTitle.Trim(),
                UrduTitle = aa.Assembly.UrduTitle.Trim() + " " + aa.UrduTitle.Trim(),
                RegisterVoters = aa.MaleVoters ?? 0 + aa.FemaleVoters ?? 0,
                TotalPollingStations = aa.TotalPollingStations ?? 0
            }).FirstOrDefault();

        if (q != null)
        {
            q.ResultPollingStations = (from aa in dc.Nominations
                where aa.ElectionPhaseId == phaseId &&
                      aa.ElectionAssemblyId == assemblyId &&
                      aa.StructureId == constituencyId &&
                      aa.SeatTypeId == seatTypeId
                select aa.ResultPollingStations).FirstOrDefault() ?? 0;

            q.Nominations = (from aa in dc.Nominations
                orderby aa.Votes ?? 0 descending, aa.Candidate.EnglishName
                where aa.ElectionPhaseId == phaseId &&
                      aa.ElectionAssemblyId == assemblyId &&
                      aa.StructureId == constituencyId &&
                      aa.SeatTypeId == seatTypeId
                select new LiveResultCandidateDetailDTO
                {
                    Party = aa.Party.EnglishTitle,
                    PartyUrdu = aa.Party.UrduTitle,
                    Candidate = aa.Candidate.EnglishName,
                    CandidateUrdu = aa.Candidate.UrduName,
                    Votes = aa.Votes ?? 0
                }).ToList();
            if (q == null || q.Nominations == null || q.Nominations.Count == 0) return null;

            var winnerName = q.Nominations[0].CandidateUrdu;
            var winnerParty = q.Nominations[0].PartyUrdu;
            var winnerVotes = q.Nominations[0].Votes;

            var runnerUpName = string.Empty;
            var runnerUpParty = string.Empty;
            var runnerUpVotes = 0;

            try
            {
                runnerUpName = q.Nominations[1].CandidateUrdu;
                runnerUpParty = q.Nominations[1].PartyUrdu;
                runnerUpVotes = q.Nominations[1].Votes;
            }
            catch (Exception)
            {
                // ignored
            }

            if (q.TotalPollingStations != q.ResultPollingStations)
            {
                if (string.IsNullOrEmpty(runnerUpName))
                {
                    var op = " غیر حتمی غیر سرکاری نتائج " + "<br />";
                    op +=
                        $" {q.UrduTitle} کے {q.TotalPollingStations} میں سے {q.ResultPollingStations} پولنگ اسٹیشن کا نتیجہ " +
                        "<br />";
                    if (winnerParty == "آزاد")
                        op += $" {q.UrduTitle}، {winnerParty} امیدوار {winnerName} {winnerVotes} ووٹ لیکرآگے " +
                              "<br />";
                    else
                        op += $" {q.UrduTitle}، {winnerParty} کےامیدوار {winnerName} {winnerVotes} ووٹ لیکرآگے " +
                              "<br />";

                    q.Ticker = op;
                }
                else
                {
                    var op = "    غیر حتمی غیر سرکاری نتائج" + "<br />";
                    op +=
                        $" {q.UrduTitle} کے {q.TotalPollingStations} میں سے {q.ResultPollingStations} پولنگ اسٹیشن کا نتیجہ " +
                        "<br />";
                    if (winnerParty == "آزاد")
                        op += $" {q.UrduTitle}، {winnerParty} امیدوار {winnerName} {winnerVotes} ووٹ لیکرآگے " +
                              "<br />";
                    else
                        op += $" {q.UrduTitle}، {winnerParty} کےامیدوار {winnerName} {winnerVotes} ووٹ لیکرآگے " +
                              "<br />";

                    if (runnerUpParty == "آزاد")
                        op += $" {q.UrduTitle}، {runnerUpParty} امیدوار {runnerUpVotes} ووٹ لیکر دوسرے نمبر پر " +
                              "<br/>";
                    else
                        op += $" {q.UrduTitle}، {runnerUpParty} کے امیدوار {runnerUpVotes} ووٹ لیکر دوسرے نمبر پر " +
                              "<br/>";

                    q.Ticker = op;
                    //return Task.FromResult(q);
                }
            }
            else
            {
                var op = "  غیر حتمی غیرسرکاری نتیجہ " + "<br />";
                op += $" {q.UrduTitle} کا مکمل نتیجہ ";
                if (winnerParty == "آزاد")
                    op += $" {winnerParty} امیدوار {winnerName} {winnerVotes} ووٹ لے کر کامیاب ";
                else
                    op += $" {winnerParty} کے {winnerName} {winnerVotes} ووٹ لے کر کامیاب ";

                if (runnerUpVotes > 0)
                {
                    if (runnerUpParty == "آزاد")
                        op += $" {runnerUpParty} امیدوار {runnerUpName} {runnerUpVotes} ووٹ لے کر دوسرے نمبر پر رہے ";
                    else
                        op += $" {runnerUpParty} کے {runnerUpName} {runnerUpVotes} ووٹ لے کر دوسرے نمبر پر رہے ";
                }

                //return Task.FromResult(op);
                q.Ticker = op;
            }

            return Task.FromResult(q);
        }

        return null;
    }

    public Task<LiveResultDetailDTO> GetLiveResultDetailPS(int pollingStationId, int assemblyId, int constituencyId,
        int seatTypeId, int PhaseId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.PSResults
            where aa.PollingStationId == pollingStationId &&
                  aa.Nomination.ElectionAssemblyId == assemblyId &&
                  aa.Nomination.StructureId == constituencyId &&
                  aa.Nomination.SeatTypeId == seatTypeId &&
                  aa.Nomination.ElectionPhaseId == PhaseId
            select new LiveResultDetailDTO
            {
                Code = aa.Nomination.Structure.Code,
                EnglishTitle = aa.Nomination.Structure.EnglishTitle,
                RegisterVoters = (from kk in dc.PollingStations
                    where kk.Id == pollingStationId
                    select kk.MaleVoters ?? 0 + kk.FemaleVoters ?? 0).Sum(),
                TotalPollingStations = (from kk in dc.PollingStations
                    where kk.ConstituencyId == constituencyId
                    select kk).Count(),
                UrduTitle = aa.Nomination.Structure.UrduTitle,
                PSNumber = aa.PollingStation.Number,
                PSEnglish = aa.PollingStation.EnglishTitle,
                PSUrdu = aa.PollingStation.UrduTitle,
                PSRegisterVoters = aa.PollingStation.MaleVoters ?? 0 + aa.PollingStation.FemaleVoters ?? 0,
                Nominations = (from kk in dc.PSResults
                    orderby kk.Votes descending, kk.Nomination.Weight
                    where kk.Nomination.SeatTypeId == seatTypeId &&
                          kk.PollingStationId == pollingStationId &&
                          kk.Nomination.ElectionAssemblyId == assemblyId &&
                          kk.Nomination.StructureId == constituencyId
                    select new LiveResultCandidateDetailDTO
                    {
                        Candidate = kk.Nomination.Candidate.EnglishName,
                        CandidateUrdu = kk.Nomination.Candidate.UrduName,
                        Party = kk.Nomination.Party.EnglishTitle,
                        PartyUrdu = kk.Nomination.Party.UrduTitle,
                        Votes = kk.Votes,
                        VotesOA = (from jj in dc.PSResults
                            where jj.NominationId == kk.NominationId &&
                                  jj.Nomination.ElectionAssemblyId == assemblyId &&
                                  jj.Nomination.ElectionPhaseId == PhaseId &&
                                  jj.Nomination.StructureId == constituencyId &&
                                  jj.Nomination.SeatTypeId == seatTypeId
                            select jj.Votes).Sum(),
                        KaKee = kk.Nomination.Candidate.Gender == Gender.Male ? " کے " : " کی "
                    }).ToList()
            }).FirstOrDefault();

        var winnerName = q.Nominations[0].CandidateUrdu;
        var winnerParty = q.Nominations[0].PartyUrdu;

        var winnerVotes = q.Nominations[0].Votes;
        var winnerKaKee = q.Nominations[0].KaKee;

        if (winnerParty == "آزاد")
            winnerParty = "آزاد امیدوار";
        else
            winnerParty = winnerParty + " " + winnerKaKee;

        var runnerUpName = string.Empty;
        var runnerUpParty = string.Empty;
        var runnerUpVotes = 0;
        var runnerUpKaKee = string.Empty;
        if (q.Nominations.Count > 1)
        {
            runnerUpName = q.Nominations[1].CandidateUrdu;
            runnerUpParty = q.Nominations[1].PartyUrdu;
            runnerUpVotes = q.Nominations[1].Votes;
            runnerUpKaKee = q.Nominations[1].KaKee;
            if (runnerUpParty == "آزاد")
                runnerUpParty = "آزاد امیدوار";
            else
                runnerUpParty = runnerUpParty + " " + runnerUpKaKee;
        }

        var oaRes = (from bb in q.Nominations
            orderby bb.VotesOA descending
            select bb).ToList();

        var PSResults = (from aa in dc.PSResults
            where aa.Nomination.ElectionAssemblyId == assemblyId &&
                  aa.Nomination.ElectionPhaseId == PhaseId &&
                  aa.Nomination.StructureId == constituencyId &&
                  aa.Nomination.SeatTypeId == seatTypeId
            select aa.PollingStationId).Distinct().Count();
        var PSTotal = (from aa in dc.PollingStations
            where aa.ConstituencyId == constituencyId
            select aa).Count();
        var winnerNameOa = oaRes[0].CandidateUrdu;
        var winnerPartyOa = oaRes[0].PartyUrdu;
        var winnerVotesOa = oaRes[0].VotesOA;
        var runnerUpNameOa = oaRes[1].CandidateUrdu;
        var runnerUpPartyOa = oaRes[1].PartyUrdu;
        var runnerUpVotesOa = oaRes[1].VotesOA;

        var op2 = $"{q.UrduTitle} - پولنگ اسٹیشن نمبر {q.PSNumber} / ضمنی الیکشن";
        op2 += "<br/>";
        op2 += $"{winnerParty} {winnerName} {winnerVotes} ووٹ لے کر پہلے نمبر پر";
        op2 += "<br/>";
        op2 += $"{runnerUpParty} {runnerUpName} {runnerUpVotes} ووٹ لے کر دوسرے نمبر پر";

        q.Ticker = op2;

        return Task.FromResult(q);
    }

    public async Task<List<ResultDTO>> GetLiveResultsAnchors(int phaseId)

    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.LiveResults
            join asm in dc.ElectionAssemblies on aa.AssemblyId equals asm.Id
            where aa.PhaseId == phaseId
                  && aa.WinnerVotes > 0
            //&& aa.IsTickerPublish == false
            orderby aa.IsPublished ? "Yes" : "No", aa.ModifiedDate descending
            select new ResultDTO
            {
                Assembly = aa.Assembly,
                Constituency = aa.Constituency,
                LastUpdatedOn = aa.ModifiedDate,
                OnAir = aa.IsPublished,
                ResultOfPollingStations = aa.PollingStationResult,
                RunnerUpName = aa.RunnerUp,
                RunnerUpParty = aa.RunnerUpParty,
                RunnerUpVotes = aa.RunnerUpVotes ?? 0,
                Status = aa.IsPublished ? "Yes" : "No",
                TotalPollingStations = aa.TotalPollingStations,
                WinnerName = aa.Winner,
                WinnerParty = aa.WinnerParty,
                WinnerVotes = aa.WinnerVotes,
                SeatType = aa.SeatType,
                AssemblyId = aa.AssemblyId,
                ConstituencyId = aa.ConstituencyId,
                Election = aa.Election,
                ElectionId = aa.ElectionId,
                Phase = aa.Phase,
                PhaseId = aa.PhaseId,
                SeatTypeId = aa.SeatTypeId,
                TotalVoters = aa.TotalVoters,
                DistrictId = 0,
                ProvinceId = 0,
                WinnerCandidateId = aa.WinnerCandidateId,
                RunnerUpCandidateId = aa.RunnerUpCandidateId,
                AssemblyTypeId = asm.AssemblyTypeId
            }).ToList();

        foreach (var dd in q)
        {
            var na = (from a in dc.Structures.OfType<NationalAssemblyHalka>()
                where a.Id == dd.ConstituencyId
                select new
                {
                    a.DistrictId,
                    a.District.Division.ProvinceId
                }).FirstOrDefault();

            if (na != null)
            {
                dd.DistrictId = na.DistrictId;
                dd.ProvinceId = na.ProvinceId;
            }

            var pa = (from a in dc.Structures.OfType<ProvincialAssemblyHalka>()
                where a.Id == dd.ConstituencyId
                select new
                {
                    a.DistrictId,
                    a.District.Division.ProvinceId
                }).FirstOrDefault();

            if (pa != null)
            {
                dd.DistrictId = pa.DistrictId;
                dd.ProvinceId = pa.ProvinceId;
            }


            var ticker = await GetLiveResultDetailLB(dd.ElectionId, dd.AssemblyId, dd.PhaseId, dd.ConstituencyId,
                dd.SeatTypeId);
            dd.District = GetConstituencyDistrict(dd.ConstituencyId, dc);
            dd.Ticker = ticker;
        }

        return q;
    }

    public async Task<List<ResultDTO>> GetLiveResultsLB(int phaseId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.LiveResults
            join asm in dc.ElectionAssemblies on aa.AssemblyId equals asm.Id
            where aa.PhaseId == phaseId
                  && aa.WinnerVotes > 0
                  && aa.IsTickerPublish == false
            orderby aa.IsPublished ? "Yes" : "No", aa.ModifiedDate descending
            select new ResultDTO
            {
                Assembly = aa.Assembly,
                Constituency = aa.Constituency,
                LastUpdatedOn = aa.ModifiedDate,
                OnAir = aa.IsPublished,
                ResultOfPollingStations = aa.PollingStationResult,
                RunnerUpName = aa.RunnerUp,
                RunnerUpParty = aa.RunnerUpParty,
                RunnerUpVotes = aa.RunnerUpVotes ?? 0,
                Status = aa.IsPublished ? "Yes" : "No",
                TotalPollingStations = aa.TotalPollingStations,
                WinnerName = aa.Winner,
                WinnerParty = aa.WinnerParty,
                WinnerVotes = aa.WinnerVotes,
                SeatType = aa.SeatType,
                AssemblyId = aa.AssemblyId,
                ConstituencyId = aa.ConstituencyId,
                Election = aa.Election,
                ElectionId = aa.ElectionId,
                Phase = aa.Phase,
                PhaseId = aa.PhaseId,
                SeatTypeId = aa.SeatTypeId,
                TotalVoters = aa.TotalVoters,
                DistrictId = 0,
                ProvinceId = 0,
                WinnerCandidateId = aa.WinnerCandidateId,
                RunnerUpCandidateId = aa.RunnerUpCandidateId,
                AssemblyTypeId = asm.AssemblyTypeId
            }).ToList();

        foreach (var dd in q)
        {
            //var uc = (from u in dc.Structures.OfType<UnionCouncil>()
            //	where u.Id == dd.ConstituencyId
            //	select new { u.Town.DistrictId, u.Town.District.Division.ProvinceId }).FirstOrDefault();
            //if (uc != null)
            //{
            //	dd.DistrictId = uc.DistrictId;
            //	dd.ProvinceId = uc.ProvinceId;
            //}
            //else
            //{
            //	var wrd = (from u in dc.Structures.OfType<Ward>()
            //			where u.Id == dd.ConstituencyId
            //			select new { u.UnionCouncil.Town.DistrictId, u.UnionCouncil.Town.District.Division.ProvinceId })
            //		.FirstOrDefault();
            //	if (wrd != null)
            //	{
            //		dd.DistrictId = wrd.DistrictId;
            //		dd.ProvinceId = wrd.ProvinceId;
            //	}
            //	else
            //	{
            //		var pa = (from aa in dc.Structures.OfType<ProvincialAssemblyHalka>()
            //			where aa.Id == dd.ConstituencyId
            //			select new { aa.DistrictId, aa.District.Division.ProvinceId }).FirstOrDefault();
            //		if (pa != null)
            //		{
            //			dd.DistrictId = pa.DistrictId;
            //			dd.ProvinceId = pa.ProvinceId;
            //		}
            //		else
            //		{
            //			var na = (from aa in dc.Structures.OfType<NationalAssemblyHalka>()
            //				where aa.Id == dd.ConstituencyId
            //				select new { aa.DistrictId, aa.District.Division.ProvinceId }).FirstOrDefault();
            //			if (na != null)
            //			{
            //				dd.DistrictId = na.DistrictId;
            //				dd.ProvinceId = na.ProvinceId;
            //			}
            //		}
            //	}
            //}

            var na = (from a in dc.Structures.OfType<NationalAssemblyHalka>()
                where a.Id == dd.ConstituencyId
                select new
                {
                    a.DistrictId,
                    a.District.Division.ProvinceId
                }).FirstOrDefault();

            if (na != null)
            {
                dd.DistrictId = na.DistrictId;
                dd.ProvinceId = na.ProvinceId;
            }

            var pa = (from a in dc.Structures.OfType<ProvincialAssemblyHalka>()
                where a.Id == dd.ConstituencyId
                select new
                {
                    a.DistrictId,
                    a.District.Division.ProvinceId
                }).FirstOrDefault();

            if (pa != null)
            {
                dd.DistrictId = pa.DistrictId;
                dd.ProvinceId = pa.ProvinceId;
            }


            var ticker = await GetLiveResultDetailLB(dd.ElectionId, dd.AssemblyId, dd.PhaseId, dd.ConstituencyId,
                dd.SeatTypeId);
            dd.District = GetConstituencyDistrict(dd.ConstituencyId, dc);
            dd.Ticker = ticker;
        }

        return q;
    }

    public async Task<List<ResultDTO>> GetLiveResults(int phaseId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.LiveResults
            join asm in dc.ElectionAssemblies on aa.AssemblyId equals asm.Id
            where aa.PhaseId == phaseId
                  && aa.WinnerVotes > 0
                  && aa.IsPublished == false
            // aa.ConstituencyId == 158
            orderby aa.IsPublished ? "Yes" : "No", aa.ModifiedDate descending
            select new ResultDTO
            {
                Assembly = aa.Assembly,
                Constituency = aa.Constituency,
                LastUpdatedOn = aa.ModifiedDate,
                OnAir = aa.IsPublished,
                ResultOfPollingStations = aa.PollingStationResult,
                RunnerUpName = aa.RunnerUp,
                RunnerUpParty = aa.RunnerUpParty,
                RunnerUpVotes = aa.RunnerUpVotes ?? 0,
                Status = aa.IsPublished ? "Yes" : "No",
                TotalPollingStations = aa.TotalPollingStations,
                WinnerName = aa.Winner,
                WinnerParty = aa.WinnerParty,
                WinnerVotes = aa.WinnerVotes,
                SeatType = aa.SeatType,
                AssemblyId = aa.AssemblyId,
                ConstituencyId = aa.ConstituencyId,
                Election = aa.Election,
                ElectionId = aa.ElectionId,
                Phase = aa.Phase,
                PhaseId = aa.PhaseId,
                SeatTypeId = aa.SeatTypeId,
                TotalVoters = aa.TotalVoters,
                DistrictId = 0,
                ProvinceId = 0,
                WinnerCandidateId = aa.WinnerCandidateId,
                RunnerUpCandidateId = aa.RunnerUpCandidateId,
                AssemblyTypeId = asm.AssemblyTypeId
            }).ToList();

        foreach (var dd in q)
        {
            var uc = (from u in dc.Structures.OfType<UnionCouncil>()
                where u.Id == dd.ConstituencyId
                select new { u.Town.DistrictId, u.Town.District.Division.ProvinceId }).FirstOrDefault();
            if (uc != null)
            {
                dd.DistrictId = uc.DistrictId;
                dd.ProvinceId = uc.ProvinceId;
            }

            var op = await GetLiveResultDetail(dd.ElectionId, dd.AssemblyId, dd.PhaseId, dd.ConstituencyId,
                dd.SeatTypeId);
            dd.Ticker = op.Ticker;
        }

        return q;
    }

    public async Task<List<ResultDTO>> GetLiveResultsPR(int phaseId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = await (from aa in dc.LiveResults
            join asm in dc.ElectionAssemblies on aa.AssemblyId equals asm.Id
            where aa.PhaseId == phaseId
                  && aa.WinnerVotes > 0
                  && (asm.AssemblyTypeId == 7 || asm.AssemblyTypeId == 10)
            // aa.ConstituencyId == 158
            orderby aa.IsPublished ? "Yes" : "No", aa.ModifiedDate descending
            select new ResultDTO
            {
                Assembly = aa.Assembly,
                Constituency = aa.Constituency,
                LastUpdatedOn = aa.ModifiedDate,
                OnAir = aa.IsPublished,
                ResultOfPollingStations = aa.PollingStationResult,
                RunnerUpName = aa.RunnerUp,
                RunnerUpParty = aa.RunnerUpParty,
                RunnerUpVotes = aa.RunnerUpVotes ?? 0,
                Status = aa.IsPublished ? "Yes" : "No",
                TotalPollingStations = aa.TotalPollingStations,
                WinnerName = aa.Winner,
                WinnerParty = aa.WinnerParty,
                WinnerVotes = aa.WinnerVotes,
                SeatType = aa.SeatType,
                AssemblyId = aa.AssemblyId,
                ConstituencyId = aa.ConstituencyId,
                Election = aa.Election,
                ElectionId = aa.ElectionId,
                Phase = aa.Phase,
                PhaseId = aa.PhaseId,
                SeatTypeId = aa.SeatTypeId,
                TotalVoters = aa.TotalVoters,
                DistrictId = 0,
                ProvinceId = 0,
                WinnerCandidateId = aa.WinnerCandidateId,
                RunnerUpCandidateId = aa.RunnerUpCandidateId,
                AssemblyTypeId = asm.AssemblyTypeId
            }).ToListAsync();

        foreach (var dd in q)
        {
            var uc = (from u in dc.Structures.OfType<UnionCouncil>()
                where u.Id == dd.ConstituencyId
                select new { u.Town.DistrictId, u.Town.District.Division.ProvinceId }).FirstOrDefault();
            if (uc != null)
            {
                dd.DistrictId = uc.DistrictId;
                dd.ProvinceId = uc.ProvinceId;
            }
        }

        return q;
    }

    public Task<ResultLogDTO> GetLogData(int constituencyId, int phaseId, int assemblyId, int seatTypeId)
    {
        using var dc = _contextFactory.CreateDbContext();
        string[] heads = { "Current", "1st Last", "2nd Last", "Third List" };
        var q = (from aa in dc.ResultLogs
            where aa.ConstituencyId == constituencyId
            orderby aa.CreatedDate descending
            select aa).Take(4).ToList();
        //list of all namanigars
        var namanigars = (from aa in dc.Namanigars
            select new GeneralItemDTO
            {
                Id = aa.Id,
                EnglishTitle = aa.Code + " - " + aa.Name
            }).ToList();
        // if there is not data in log then return null
        if (q == null) return null;

        var cnt = 0;
        ResultLogDTO op = new();
        // for each entry in log data
        foreach (var l in q)
        {
            PSLogDTO d = new()
            {
                Action = l.Action,
                ActionDateTime = l.CreatedDate,
                //Namanigar = l.Namanigar == null  ? "" : l.Namanigar.Code + " - " + l.Namanigar.Name,
                PSCount = l.PollingStations,
                Title = heads[cnt],
                User = l.CreatedByUserId,
                LogId = l.Id
            };
            d.Namanigar = namanigars.Find(x => x.Id == l.NamanigarId).EnglishTitle;

            op.PS.Add(d);
            cnt++;
        }

        // Fill Reming Columns
        for (var i = q.Count; i < 4; i++)
        {
            PSLogDTO d = new()
            {
                Action = string.Empty,
                ActionDateTime = null,
                Namanigar = string.Empty,
                PSCount = null,
                Title = heads[i],
                User = string.Empty,
                LogId = 0
            };
            op.PS.Add(d);
        }

        // Get All nominations for this constituenices
        op.Votes = (from kk in dc.Nominations
                where kk.StructureId == constituencyId &&
                      kk.ElectionAssemblyId == assemblyId &&
                      kk.ElectionPhaseId == phaseId &&
                      kk.SeatTypeId == seatTypeId
                orderby kk.Votes descending, kk.Candidate.EnglishName
                select new VotesLogDTO
                {
                    Candidate = kk.Candidate.EnglishName + " - " + kk.Party.ShortEnglishTitle,
                    CandidateId = kk.CandidteId
                }
            ).ToList();
        // Get Each candidates current, first, second, and third last updated votes
        foreach (var c in op.Votes)
        {
            var rd1 = (from mm in dc.ResultLogDetails
                where mm.CandidateId == c.CandidateId &&
                      mm.ResultLogId == op.PS[0].LogId
                select mm).FirstOrDefault();
            if (rd1 != null) c.Current = rd1.Votes;

            var rd2 = (from mm in dc.ResultLogDetails
                where mm.CandidateId == c.CandidateId &&
                      mm.ResultLogId == op.PS[1].LogId
                select mm).FirstOrDefault();
            if (rd2 != null) c.FirstLast = rd2.Votes;

            var rd3 = (from mm in dc.ResultLogDetails
                where mm.CandidateId == c.CandidateId &&
                      mm.ResultLogId == op.PS[2].LogId
                select mm).FirstOrDefault();
            if (rd3 != null) c.SecondLast = rd3.Votes;

            var rd4 = (from mm in dc.ResultLogDetails
                where mm.CandidateId == c.CandidateId &&
                      mm.ResultLogId == op.PS[3].LogId
                select mm).FirstOrDefault();
            if (rd4 != null) c.ThirdLast = rd4.Votes;
        }

        return Task.FromResult(op);
    }

    public Task<List<MyConstitencyDTO>> GetMyConstituencies(string userId, int electionId, int phaseId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var myCont = (from aa in dc.UserConstituencies
            where aa.UserId == userId && aa.ElectionAssembly.ElectionId == electionId
            select aa.StructureId).ToList();

        var q = (from aa in dc.Nominations
            where myCont.Contains(aa.StructureId) &&
                  aa.ElectionAssembly.ElectionId == electionId &&
                  aa.ElectionPhaseId == phaseId
            select new MyConstitencyDTO
            {
                Assembly = aa.ElectionAssembly.EnglishTitle,
                AssemblyId = aa.ElectionAssemblyId,
                Constituecy = aa.Structure.Code + " - " + aa.Structure.EnglishTitle,
                ConstituencyId = aa.StructureId,
                SeatType = aa.SeatType.EnglishTitle,
                SeatTypeId = aa.SeatTypeId,
                MaleVoters = aa.Structure.MaleVoters ?? 0,
                FemaleVoters = aa.Structure.FemaleVoters ?? 0,
                TotalVotes = aa.Structure.MaleVoters ?? 0 + aa.Structure.FemaleVoters ?? 0,
                TotalPollingStatiosn = aa.Structure.TotalPollingStations ?? 0,
                PhaseId = aa.ElectionPhaseId,
                StructureId = aa.StructureId
            }).Distinct().ToList();

        foreach (var m in q)
        {
            var q3 = (from aa in dc.Nominations
                where aa.SeatTypeId == m.SeatTypeId &&
                      aa.ElectionPhaseId == m.PhaseId &&
                      aa.ElectionAssemblyId == m.AssemblyId &&
                      aa.StructureId == m.StructureId
                select aa).Count();
            m.TotalNominations = q3;
        }

        return Task.FromResult(q);
    }

    public Task<List<NamanigarConstituencyItemDTO>> GetNamanigarConstituencies(string userId, int? assemblyId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var userCon = (from aa in dc.UserConstituencies
            where //aa.ElectionAssembly.ElectionId == electionId &&
                (assemblyId == null || aa.ElectionAssemblyId == assemblyId) &&
                aa.UserId == userId
            select aa.StructureId).ToList();

        var q = (from aa in dc.Structures.OfType<UnionCouncil>()
            join ep in dc.ElectionPhases on aa.ElectionId equals ep.ElectionId
            where userCon.Contains(aa.Id) &&
                  aa.IsPostponed == false &&
                  ep.IsActive
            select new NamanigarConstituencyItemDTO
            {
                Id = aa.Id,
                EnglishTitle = aa.EnglishTitle + " - " + aa.Assembly.EnglishTitle,
                UrduTitle = aa.UrduTitle + " - " + aa.Assembly.UrduTitle,
                Assembly = aa.Assembly.EnglishTitle,
                AssemblyId = aa.AssemblyId.Value
            }).Distinct().ToList();

        var qWrd = (from aa in dc.Structures.OfType<Ward>()
            where userCon.Contains(aa.Id) &&
                  aa.IsPostponed == false
            select new NamanigarConstituencyItemDTO
            {
                Id = aa.Id,
                EnglishTitle = aa.EnglishTitle + " " + aa.UnionCouncil.Code + " - " + aa.Assembly.EnglishTitle,
                UrduTitle = aa.UrduTitle + " " + aa.UnionCouncil.Code + " - " + aa.Assembly.UrduTitle,
                Assembly = aa.Assembly.EnglishTitle,
                AssemblyId = aa.AssemblyId.Value
            }).ToList();

        var qNAs = (from aa in dc.Structures.OfType<NationalAssemblyHalka>()
            where userCon.Contains(aa.Id) &&
                  aa.IsPostponed == false
            select new NamanigarConstituencyItemDTO
            {
                Id = aa.Id,
                EnglishTitle = $"{aa.EnglishTitle} - {aa.Assembly.EnglishTitle}",
                UrduTitle = $"{aa.UrduTitle} - {aa.Assembly.UrduTitle}",
                Assembly = aa.Assembly.EnglishTitle,
                AssemblyId = aa.AssemblyId.Value
            }).ToList();

        var qPAs = (from aa in dc.Structures.OfType<ProvincialAssemblyHalka>()
            where userCon.Contains(aa.Id) &&
                  aa.IsPostponed == false
            select new NamanigarConstituencyItemDTO
            {
                Id = aa.Id,

                EnglishTitle = $"{aa.EnglishTitle} - {aa.Assembly.EnglishTitle}",
                UrduTitle = $"{aa.UrduTitle} - {aa.Assembly.UrduTitle}",
                Assembly = aa.Assembly.EnglishTitle,
                AssemblyId = aa.AssemblyId.Value
            }).ToList();

        var q1 = q.Union(qWrd).Union(qNAs).Union(qPAs);
        var q2 = (from aa in q1
            orderby aa.EnglishTitle
            select aa).ToList();
        return Task.FromResult(q2);
    }

    public Task<List<ResultDetailDTO>> GetNamanigarElectionResults(int assemblyId, int phaseId, int structureId,
        int seatTypeId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var psQ = (from aa in dc.Structures
            where aa.Id == structureId &&
                  aa.AssemblyId == assemblyId &&
                  aa.IsPostponed == false
            select new
            {
                aa.TotalPollingStations,
                aa.MaleVoters,
                aa.FemaleVoters
            }).FirstOrDefault();
        var totalPollingStations = 0;
        var totalVoters = 0;
        if (psQ != null) totalPollingStations = psQ.TotalPollingStations ?? 0;
        if (psQ.MaleVoters == null || psQ.FemaleVoters == null)
        {
            totalVoters = 0;
        }
        else
        {
            var mv = psQ.MaleVoters ?? 0;
            var fv = psQ.FemaleVoters ?? 0;
            totalVoters = mv + fv;
        }

        var q = (from aa in dc.Nominations
            where aa.ElectionAssemblyId == assemblyId &&
                  aa.ElectionPhaseId == phaseId &&
                  aa.StructureId == structureId &&
                  aa.SeatTypeId == seatTypeId
            orderby aa.Votes ?? 0 descending, aa.Weight == 0 ? 1000000 : aa.Weight
            select new ResultDetailDTO
            {
                AssemblyId = aa.ElectionAssemblyId,
                CandidateId = aa.CandidteId,
                ElectionId = aa.ElectionAssembly.ElectionId,
                EnglishName = aa.Candidate.EnglishName,
                Id = aa.Id,
                PartyId = aa.PartyId,
                IsNew = false,
                Party = aa.Party.ShortEnglishTitle,
                PartyUrdu = aa.Party.UrduTitle,
                PhaseId = aa.ElectionPhaseId,
                SeatTypeId = aa.SeatTypeId,
                ConstituencyId = aa.StructureId,
                Symbol = aa.Symbol.EnglishTitle,
                SymbolId = aa.SymbolId,
                SymbolUrdu = aa.Symbol.UrduTitle,
                UrduName = aa.Candidate.UrduName,
                Weight = aa.Weight,
                Votes = 0, //aa.NamanigarResult == null ? 0 : aa.NamanigarResult.Votes,
                PSVotes = 0,
                MaleVoters = aa.Structure.MaleVoters ?? 0,
                FemaleVoters = aa.Structure.FemaleVoters ?? 0,
                //VotersCount = (aa.Structure.MaleVoters ?? 0) + (aa.Structure.FemaleVoters ?? 0),
                TotalPollingStations = totalPollingStations,
                ResultPollingStations = 0, //aa.NamanigarResult == null ? 0 : aa.NamanigarResult.ResultPollingStations,
                ChangeVotes = 0,
                Mode = "Add",
                IsWithdraw = aa.IsWithdraw,
                FemaleVotersPS = aa.Structure.PollingStations.Sum(k => k.FemaleVoters ?? 0),
                MaleVotersPS = aa.Structure.PollingStations.Sum(k => k.MaleVoters ?? 0),
                TotalPollingStationsPS = aa.Structure.PollingStations.Count,
                PollingStationCompletedPS = 0,
                VotersCastePS = 0,
                TotalVotesPS = 0
            }).ToList();

        foreach (var cc in q)
        {
            var nnr = (from aa in dc.NamanigarResults
                where aa.NominationId == cc.Id
                select aa).FirstOrDefault();
            if (nnr != null)
            {
                cc.Votes = nnr.Votes;
                cc.ResultPollingStations = nnr.ResultPollingStations;
            }

            cc.candidateImg =
                !File.Exists(
                    Directory.GetCurrentDirectory() + $"\\wwwroot\\media\\candidates\\{cc.CandidateId}.jpg")
                    ? "/media/candidates/Blank.jpg"
                    : $"/media/candidates/{cc.CandidateId}.jpg";

            cc.flagImg = File.Exists(Directory.GetCurrentDirectory() + $"\\wwwroot\\media\\flags\\{cc.PartyId}.jpg")
                ? $"/media/flags/{cc.PartyId}.jpg"
                : "/media/flags/missing-flag.png";

            cc.symbolImg =
                File.Exists(Directory.GetCurrentDirectory() + $@"\wwwroot\media\symbols\{cc.SymbolId}.jpg")
                    ? $"/media/symbols/{cc.SymbolId}.jpg"
                    : "/media/symbols/missing-symbol.png";
        }

        return Task.FromResult(q);
    }

    public Task<List<NNResultsLogDTO>> GetNamanigarResultsLog(int constituencyId, int phaseId, int seatTypeId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.NamanigarResultLogs
            join bb in dc.Nominations on aa.NominationId equals bb.Id
            where bb.StructureId == constituencyId &&
                  bb.SeatTypeId == seatTypeId &&
                  bb.ElectionPhaseId == phaseId
            orderby aa.BatchId
            select new NNResultsLogDTO
            {
                Id = aa.BatchId,
                Action = aa.LastActionMode,
                Namanigar = aa.NamanigarUserId,
                Date = aa.ActionDateTime,
                PS = aa.ResultPollingStations,
                Constituency = bb.Structure.EnglishTitle + " - " + bb.ElectionAssembly.EnglishTitle
            }).Distinct().ToList();

        return Task.FromResult(q);
    }

    public Task<string> GetNNConstituencyInfo(int constituencyId, int seatTypeId, int phaseId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.Structures
            where aa.Id == constituencyId
            select new
            {
                Assembly = aa.Assembly.EnglishTitle,
                Constituency = aa.EnglishTitle,
                MaleVoters = aa.MaleVoters ?? 0,
                FemaleVoters = aa.FemaleVoters ?? 0,
                TotalPS = aa.TotalPollingStations ?? 0,
                SeatType = dc.SeatTypes.Find(seatTypeId).EnglishTitle,
                CurrentPS = dc.Nominations
                    .FirstOrDefault(k => k.StructureId == constituencyId && k.SeatTypeId == seatTypeId)
                    .ResultPollingStations,
                NamanigarPS = 0
            }).FirstOrDefault();

        var q2 = (from aa in dc.NamanigarResults
            join nm in dc.Nominations on aa.NominationId equals nm.Id
            orderby aa.CreatedOn descending
            select new { aa.CreatedOn, aa.ResultPollingStations }).ToList();


        return Task.FromResult("Okay");
    }

    public Task<NamanigarResultDTO> GetNNResults(int constituencyId, int seatTypeId, int phaseId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.NamanigarResults
            join n in dc.Nominations on aa.NominationId equals n.Id
            where n.StructureId == constituencyId &&
                  n.SeatTypeId == seatTypeId &&
                  n.ElectionPhaseId == phaseId
            select new NamanigarResultDTO
            {
                Assembly = n.ElectionAssembly.EnglishTitle,
                AssemblyId = n.ElectionAssemblyId,
                SeatTypeId = n.SeatTypeId,
                PartyId = n.PartyId,
                SymbolId = n.SymbolId.Value,
                BatchId = aa.BatchId,
                PhaseId = n.ElectionPhaseId,
                Constituency = n.Structure.EnglishTitle,
                ConstituencyId = n.StructureId,
                FemaleVoters = n.Structure.FemaleVoters ?? 0,
                LastAction = aa.LastActionMode,
                LastActionDate = aa.ModifiedOn ?? aa.CreatedOn,
                MaleVoters = n.Structure.MaleVoters ?? 0,
                Namanigar = string.IsNullOrEmpty(aa.ModifiedBy) ? aa.CreatedBy : aa.ModifiedBy,
                NamangiarPollingStations = aa.ResultPollingStations,
                ResultPollingStations = n.ResultPollingStations ?? 0,
                SeatType = n.SeatType.EnglishTitle,
                TotalPollingStations = n.Structure.TotalPollingStations ?? 0
            }).Distinct().FirstOrDefault();

        q.Details = (from aa in dc.NamanigarResults
            join n in dc.Nominations on aa.NominationId equals n.Id
            where n.ElectionPhaseId == phaseId &&
                  n.StructureId == constituencyId &&
                  n.SeatTypeId == seatTypeId &&
                  aa.BatchId == q.BatchId
            select new NamanigarResultDetailDTO
            {
                Candidate = n.Candidate.EnglishName,
                Party = n.Party.EnglishTitle,
                Symbol = n.Symbol.EnglishTitle,
                NamanigarVotes = aa.Votes,
                NominationId = n.Id,
                Votes = n.Votes ?? 0,
                CandidateId = n.CandidteId
            }).ToList();

        return Task.FromResult(q);
    }

    public Task<List<NamanigarResultsListViewDTO>> GetNNResultsList(int phaseId)

    {
        using var dc = _contextFactory.CreateDbContext();
        // Get All Constituencies in Log Table
        var q = (from aa in dc.NamanigarResults
            join nm in dc.Nominations on aa.NominationId equals nm.Id
            where nm.ElectionPhaseId == phaseId
            orderby aa.ModifiedOn ?? aa.CreatedOn descending
            select new
            {
                nm.StructureId,
                nm.SeatTypeId,
                nm.ResultPollingStations
            }).Distinct().ToList();
        // For each constituencies
        List<NamanigarResultsListViewDTO> lst = new();

        var q2 = (from aa in dc.NamanigarResults
            join n in dc.Nominations on aa.NominationId equals n.Id
            where n.ElectionPhaseId == phaseId
            select new NamanigarResultsListViewDTO
            {
                Action = aa.LastActionMode,
                ActionDate = aa.ModifiedOn ?? aa.CreatedOn,
                Assembly = n.ElectionAssembly.EnglishTitle,
                BatchId = aa.BatchId,
                Constituency = n.Structure.EnglishTitle,
                ConstituencyId = n.StructureId,
                CurrentPS = 0,
                Namanigar = string.IsNullOrEmpty(aa.ModifiedBy) ? aa.CreatedBy : aa.ModifiedBy,
                PS = aa.ResultPollingStations,
                SeatType = n.SeatType.EnglishTitle,
                SeatTypeId = n.SeatTypeId,
                Status = aa.Status == ApprovalStatus.Approved ? "Approved" :
                    aa.Status == ApprovalStatus.Pending ? "Pending" : string.Empty,
                IsViewed = aa.IsViewed
            }).Distinct().ToList();
        foreach (var i in q2)
        {
            var cp = (from aa in q
                where aa.SeatTypeId == i.SeatTypeId &&
                      aa.StructureId == i.ConstituencyId
                select aa).FirstOrDefault();
            if (cp != null) i.CurrentPS = cp.ResultPollingStations ?? 0;
        }

        var q3 = (from aa in q2
            orderby aa.Status descending, aa.ActionDate descending
            select aa).ToList();
        return Task.FromResult(q3);
    }

    public Task<List<GeneralItemDTO>> GetParties()
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.Parties
            orderby aa.EnglishTitle
            select new GeneralItemDTO
            {
                Id = aa.Id,
                EnglishTitle = aa.EnglishTitle,
                UrduTitle = aa.UrduTitle
            }).ToList();
        return Task.FromResult(q);
    }

    public Task<int?> GetPartySymbol(int selectedPartyId)
    {
        using var dc = _contextFactory.CreateDbContext();

        var q = (from aa in dc.Parties
            where aa.Id == selectedPartyId
            select aa).FirstOrDefault();
        return q != null ? Task.FromResult(q.SymbolId) : null;
    }

    public Task<List<ResultDTO>> GetSenateResults(int phaseId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.Nominations
            where aa.ElectionPhaseId == phaseId
                  && aa.Votes >= 1
            select new ResultDTO
            {
                Assembly = aa.ElectionAssembly.EnglishTitle,
                AssemblyId = aa.ElectionAssemblyId,
                Constituency = aa.Structure.Code + " - " + aa.Structure.EnglishTitle,
                ConstituencyId = aa.StructureId,
                Election = aa.ElectionPhase.Election.EnglishTitle,
                ElectionId = aa.ElectionPhase.ElectionId,
                LastUpdatedOn = aa.ModifiedDate ?? aa.CreatedDate,
                Phase = aa.ElectionPhase.Title,
                PhaseId = aa.ElectionPhaseId,
                SeatType = aa.SeatType.EnglishTitle,
                SeatTypeId = aa.SeatTypeId,
                WinnerCandidateId = aa.CandidteId,
                WinnerName = aa.Candidate.EnglishName,
                WinnerParty = aa.Party.EnglishTitle,
                WinnerVotes = aa.Votes ?? 0
            }).ToList();
        return Task.FromResult(q);
    }

    public Task<List<GeneralItemDTO>> GetSymbols()
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.Symbols
            orderby aa.EnglishTitle
            select new GeneralItemDTO
            {
                Id = aa.Id,
                EnglishTitle = aa.EnglishTitle,
                UrduTitle = aa.UrduTitle
            }).ToList();
        return Task.FromResult(q);
    }

    public Task<List<GeneralItemDTO>> GetUserAssembliesByType(string userId, int electionId, int assemblyTypeId)
    {
        using var dc = _contextFactory.CreateDbContext();


        var q = (from aa in dc.UserConstituencies
            orderby aa.ElectionAssembly.EnglishTitle
            where aa.UserId == userId &&
                  aa.ElectionAssembly.ElectionId == electionId &&
                  aa.ElectionAssembly.AssemblyTypeId == assemblyTypeId
            select new GeneralItemDTO
            {
                Id = aa.ElectionAssemblyId,
                UrduTitle = aa.ElectionAssembly.UrduTitle,
                EnglishTitle = aa.ElectionAssembly.EnglishTitle
            }).Distinct().ToList();

        var q2 = (from aa in q
            orderby aa.EnglishTitle
            select aa).ToList();
        return Task.FromResult(q2);
    }

    public Task<List<GeneralItemDTO>> GetUserAssemblies(string userId, int electionId, int phaseId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var phaseAssemblies = (from a in dc.Nominations
            where a.ElectionPhaseId == phaseId
            select a.ElectionAssemblyId).Distinct().ToList();

        var q = (from aa in dc.UserConstituencies
            orderby aa.ElectionAssembly.EnglishTitle
            where aa.UserId == userId &&
                  aa.ElectionAssembly.ElectionId == electionId
            select new GeneralItemDTO
            {
                Id = aa.ElectionAssemblyId,
                UrduTitle = aa.ElectionAssembly.UrduTitle,
                EnglishTitle = aa.ElectionAssembly.EnglishTitle
            }).Distinct().ToList();

        var q2 = (from aa in q
            where phaseAssemblies.Contains(aa.Id)
            orderby aa.EnglishTitle
            select aa).ToList();
        return Task.FromResult(q2);
    }

    public Task<List<GeneralItemDTO>> GetUsersConstituencies(string userId, int electionId, int assemblyId, int phaseId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var con = dc.Database.GetDbConnection();
        var prams = new
        {
            UserId = userId,
            ElectionId = electionId,
            AssemblyId = assemblyId,
            PhaseId = phaseId
        };
        var op = con.Query<GeneralItemDTO>("app.sp_GetUsersConstituencies", prams,
            commandType: CommandType.StoredProcedure).ToList();
        return Task.FromResult(op);
    }

    public Task<List<GeneralItemDTO>> GetUsersConstituencies3(string userId, int electionId, int assemblyId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var userCon = (from aa in dc.UserConstituencies
            where aa.ElectionAssembly.ElectionId == electionId &&
                  aa.ElectionAssemblyId == assemblyId &&
                  aa.UserId == userId
            select aa.StructureId).ToList();

        var q = (from aa in dc.Structures.OfType<UnionCouncil>()
            where userCon.Contains(aa.Id) &&
                  aa.IsPostponed == false
            select new GeneralItemDTO
            {
                Id = aa.Id,
                EnglishTitle = aa.Code + " - " + aa.EnglishTitle + " - " + aa.Town.EnglishTitle,

                UrduTitle = aa.Code + " - " + aa.UrduTitle + " - " + aa.Town.UrduTitle
            }).ToList();

        var qWrd = (from aa in dc.Structures.OfType<Ward>()
            where userCon.Contains(aa.Id) &&
                  aa.IsPostponed == false
            select new GeneralItemDTO
            {
                Id = aa.Id,
                EnglishTitle = $"{aa.UnionCouncil.Code} {aa.Code} {aa.EnglishTitle}",

                UrduTitle =
                    $"{aa.UnionCouncil.Code} {aa.Code} {aa.UrduTitle}"
            }).ToList();

        var qNAs = (from aa in dc.Structures.OfType<NationalAssemblyHalka>()
            where userCon.Contains(aa.Id) &&
                  aa.IsPostponed == false
            select new GeneralItemDTO
            {
                Id = aa.Id,
                EnglishTitle =
                    $"{aa.Code} - {aa.EnglishTitle}  {aa.District.EnglishTitle} district - {aa.District.Division.Province.EnglishTitle}",
                UrduTitle =
                    $"{aa.Code} - {aa.UrduTitle} {aa.District.UrduTitle} ڈسٹرکٹ - {aa.District.Division.Province.UrduTitle}"
            }).ToList();

        var qPAs = (from aa in dc.Structures.OfType<ProvincialAssemblyHalka>()
            where userCon.Contains(aa.Id) &&
                  aa.IsPostponed == false
            select new GeneralItemDTO
            {
                Id = aa.Id,
                EnglishTitle =
                    $"{aa.Code} - {aa.EnglishTitle}  {aa.District.EnglishTitle} district - {aa.District.Division.Province.EnglishTitle}",
                UrduTitle =
                    $"{aa.Code} - {aa.UrduTitle} {aa.District.UrduTitle} ڈسٹرکٹ - {aa.District.Division.Province.UrduTitle}"
            }).ToList();

        var q1 = q.Union(qWrd).Union(qNAs).Union(qPAs);
        var q2 = (from aa in q1
            orderby aa.EnglishTitle
            select aa).ToList();
        return Task.FromResult(q2);
    }

    public Task<List<GeneralItemDTO>> GetUsersConstituenciesVCNC(string userId, int electionId, int assemblyId,
        int phaseId)
    {
        using var dc = _contextFactory.CreateDbContext();
        // Get Existing Constituencies where nomination exist
        var resultPostedConst = (from aa in dc.Nominations
            where aa.ElectionPhaseId == phaseId
            select aa.StructureId).Distinct().ToList();
        // get current users's constituencies
        var userConTmp = (from aa in dc.UserConstituencies
            where aa.ElectionAssembly.ElectionId == electionId &&
                  aa.ElectionAssemblyId == assemblyId &&
                  aa.UserId == userId
            //emptyCon.Contains(aa.StructureId)
            select aa.StructureId).ToList();
        // exclude those constituncies where nominations already exists
        var userCon = (from bb in userConTmp
            where !resultPostedConst.Contains(bb)
            select bb).ToList();

        var q = (from aa in dc.Structures.OfType<UnionCouncil>()
            where userCon.Contains(aa.Id) &&
                  aa.IsPostponed == false
            select new GeneralItemDTO
            {
                Id = aa.Id,
                EnglishTitle = aa.Code + " - " + aa.EnglishTitle + " - " + aa.Town.EnglishTitle + "  - " +
                               aa.Town.District.EnglishTitle + " district",
                UrduTitle = aa.Code + " - " + aa.UrduTitle + " - " + aa.Town.UrduTitle + " - " +
                            aa.Town.District.UrduTitle + " ڈسٹرکٹ"
            }).ToList();

        var qWrd = (from aa in dc.Structures.OfType<Ward>()
            where userCon.Contains(aa.Id) &&
                  aa.IsPostponed == false
            select new GeneralItemDTO
            {
                Id = aa.Id,
                EnglishTitle =
                    $"{aa.Code} - {aa.EnglishTitle} {aa.UnionCouncil.EnglishTitle} - {aa.UnionCouncil.Town.EnglishTitle} - {aa.UnionCouncil.Town.District.EnglishTitle} district - {aa.UnionCouncil.Town.District.Division.Province.EnglishTitle}",
                UrduTitle =
                    $"{aa.Code} - {aa.UrduTitle} {aa.UnionCouncil.UrduTitle} - {aa.UnionCouncil.Town.UrduTitle} - {aa.UnionCouncil.Town.District.UrduTitle} ڈسٹرکٹ - {aa.UnionCouncil.Town.District.Division.Province.UrduTitle}"
            }).ToList();

        var qNAs = (from aa in dc.Structures.OfType<NationalAssemblyHalka>()
            where userCon.Contains(aa.Id) &&
                  aa.IsPostponed == false
            select new GeneralItemDTO
            {
                Id = aa.Id,
                EnglishTitle =
                    $"{aa.Code} - {aa.EnglishTitle}  {aa.District.EnglishTitle} district - {aa.District.Division.Province.EnglishTitle}",
                UrduTitle =
                    $"{aa.Code} - {aa.UrduTitle} {aa.District.UrduTitle} ڈسٹرکٹ - {aa.District.Division.Province.UrduTitle}"
            }).ToList();

        var qPAs = (from aa in dc.Structures.OfType<ProvincialAssemblyHalka>()
            where userCon.Contains(aa.Id) &&
                  aa.IsPostponed == false
            select new GeneralItemDTO
            {
                Id = aa.Id,
                EnglishTitle =
                    $"{aa.Code} - {aa.EnglishTitle}  {aa.District.EnglishTitle} district - {aa.District.Division.Province.EnglishTitle}",
                UrduTitle =
                    $"{aa.Code} - {aa.UrduTitle} {aa.District.UrduTitle} ڈسٹرکٹ - {aa.District.Division.Province.UrduTitle}"
            }).ToList();

        var q1 = q.Union(qWrd).Union(qNAs).Union(qPAs);
        var q2 = (from aa in q1
            orderby aa.EnglishTitle
            select aa).ToList();
        return Task.FromResult(q2);
    }

    public Task<string> MarkPublish(int electionId, int phaseId, int assemblyId, int constituencyId, int seatTypeId,
        string user)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.LiveResults
            where aa.ElectionId == electionId &&
                  aa.PhaseId == phaseId &&
                  aa.AssemblyId == assemblyId &&
                  aa.ConstituencyId == constituencyId &&
                  aa.SeatTypeId == seatTypeId
            select aa).FirstOrDefault();

        if (q != null)
        {
            q.IsPublished = true;
            q.PublishedByUser = user;
            q.PublishedDate = DateTime.Now;
            dc.SaveChanges();
            return Task.FromResult("OK");
        }

        return Task.FromResult("Record not found");
    }

    public Task<string> MarkPublishPS(ResultDTO r, string user, DateTime logDate)
    {
        using var dc = _contextFactory.CreateDbContext();

        var publishedDate = DateTime.Now;
        using var tran = dc.Database.BeginTransaction();
        try
        {
            var q = (from aa in dc.PSResults
                where aa.PollingStationId == r.PollingStationId
                      && aa.Nomination.StructureId == r.ConstituencyId &&
                      aa.Nomination.ElectionPhaseId == r.PhaseId &&
                      aa.Nomination.SeatTypeId == r.SeatTypeId &&
                      aa.Nomination.ElectionAssemblyId == r.AssemblyId
                select aa).ToList();

            foreach (var psResult in q)
            {
                psResult.IsPublished = true;
                psResult.PublishedBy = user;
                psResult.PublishedDate = logDate;
                dc.SaveChanges();

                var psLog = new PSResultLog
                {
                    IsPublished = true,
                    LogDate = DateTime.Now,
                    NominationId = psResult.NominationId,
                    PollingStationId = psResult.PollingStationId,
                    PublishedBy = user,
                    PublishedDate = logDate,
                    UserId = psResult.ModifiedBy ?? psResult.CreatedBy,
                    Votes = psResult.Votes
                };
                dc.PSResultLogs.Add(psLog);
                dc.SaveChanges();
            }

            tran.Commit();
            return Task.FromResult("OK");
        }
        catch (Exception)
        {
            tran.Rollback();
            return Task.FromResult("Unable to Update Publish Status");
        }
    }

    public Task<string> SaveNamanigarResults(List<ResultDetailDTO> result_detail, int selectedAssemblyId, int phaseId,
        int selectedStructureId, int selectedSeatTypeId, int changePollingStations, int NewResPollingStations,
        string editMode, string user)
    {
        using var dc = _contextFactory.CreateDbContext();
        var tran = dc.Database.BeginTransaction();
        var status = "Ok";
        try
        {
            var batchId = GetNextBatchId(dc);
            var actionDateTime = DateTime.Now;
            foreach (var r in result_detail)
            {
                var v = (from aa in dc.NamanigarResults
                    where aa.NominationId == r.Id
                    select aa).FirstOrDefault();
                if (v == null)
                {
                    v = new NamanigarResult
                    {
                        CreatedBy = user,
                        CreatedOn = actionDateTime,
                        NominationId = r.Id,
                        ResultPollingStations = NewResPollingStations,
                        ChangePollingStations = changePollingStations,
                        Status = ApprovalStatus.Pending,
                        Votes = r.ChangeVotes,
                        ChangeVotes = r.ChangeVotes,
                        LastActionMode = editMode,
                        BatchId = batchId,
                        IsViewed = false
                    };
                    dc.NamanigarResults.Add(v);
                    dc.SaveChanges();

                    SaveResultsLog(changePollingStations, NewResPollingStations, editMode, user, dc, v, batchId,
                        actionDateTime);
                }
                else
                {
                    if (editMode == "Add")
                    {
                        v.Votes = r.Votes + r.ChangeVotes;
                        v.ChangeVotes = r.ChangeVotes;
                        v.BatchId = batchId;
                        //v.ResultPollingStations = v.ResultPollingStations + changePollingStations;
                        v.ChangePollingStations = changePollingStations;
                        v.ResultPollingStations = NewResPollingStations;
                        v.ModifiedBy = user;
                        v.ModifiedOn = actionDateTime;
                        v.LastActionMode = editMode;
                        v.Status = ApprovalStatus.Pending;
                        v.IsViewed = false;
                        dc.SaveChanges();
                        SaveResultsLog(changePollingStations, NewResPollingStations, editMode, user, dc, v, batchId,
                            actionDateTime);
                    }
                    else if (editMode == "Update")
                    {
                        v.Votes = r.ChangeVotes;
                        v.ChangeVotes = r.Votes;
                        v.BatchId = batchId;
                        //v.ResultPollingStations = changePollingStations;
                        v.ChangePollingStations = v.ResultPollingStations;
                        v.ResultPollingStations = NewResPollingStations;
                        //v.ChangePollingStations = changePollingStations;
                        v.ModifiedBy = user;
                        v.ModifiedOn = actionDateTime;
                        v.LastActionMode = editMode;
                        v.Status = ApprovalStatus.Pending;
                        v.IsViewed = false;
                        dc.SaveChanges();
                        SaveResultsLog(changePollingStations, NewResPollingStations, editMode, user, dc, v, batchId,
                            actionDateTime);
                    }
                }
            }

            tran.Commit();
        }
        catch (Exception ex)
        {
            tran.Rollback();
            status = ex.Message;
        }

        return Task.FromResult(status);
    }


    public Task<ResultDTO> SaveResults(List<ResultDetailDTO> results_detail, int assemblyId, int phaseId,
        int constituencyId, int seatTypeId, int changePollingStations, string mode, string user, int namanigarId)
    {
        using var dc = _contextFactory.CreateDbContext();
        using var tran = dc.Database.BeginTransaction();


        // Save Log Info Save Result Previous Polling Stations
        var pps = (from aa in dc.Nominations
            where aa.StructureId == constituencyId &&
                  aa.ElectionAssemblyId == assemblyId &&
                  aa.ElectionPhaseId == phaseId &&
                  aa.SeatTypeId == seatTypeId
            select aa).FirstOrDefault();

        var ppsValue = 0;
        if (pps != null) ppsValue = pps.ResultPollingStations ?? 0;

        //var totalPollingStations = dc.Structures.Find(constituencyId)?.TotalPollingStations ?? 0;
        var totalPollingStations = dc.PollingSchemes
            .FirstOrDefault(m => m.PhaseId == phaseId && m.StructureId == constituencyId)?.TotalPollingStations ?? 0;
        //dc.Structures.Find(constituencyId)?.TotalPollingStations ?? 0;
        var resultPollingStations = mode == "Add" ? changePollingStations + ppsValue : changePollingStations;

        if (resultPollingStations == 0)
        {
            tran.Rollback();
            throw new Exception("Result Polling Station Count Must Be Greater than Zero");
        }

        if (resultPollingStations == 0 && results_detail.Sum(m => m.ChangeVotes) > 0)
        {
            tran.Rollback();
            throw new Exception("Result Polling Station Count Must Be Greater than Zero");
        }

        if (totalPollingStations == 0)
        {
            tran.Rollback();
            throw new Exception(
                "Total polling station count on this constituency is invalid, it must be greater than Zero");
        }

        if (resultPollingStations > totalPollingStations)
        {
            tran.Rollback();
            throw new Exception(
                "Result Polling count station cannot be greater than Polling Station count on this constituency");
        }

        var rl = new ResultLog
        {
            Action = mode,
            AssemblyId = assemblyId,
            ConstituencyId = constituencyId,
            CreatedByUserId = user,
            CreatedDate = DateTime.Now,
            NamanigarId = namanigarId,
            NewPollingStations = changePollingStations,
            PhaseId = phaseId,
            PreviousPollingStations = ppsValue,
            PollingStations = mode == "Add" ? changePollingStations + ppsValue : changePollingStations,
            SeatTypeId = seatTypeId,
            ResultLogDetails = new List<ResultLogDetail>()
        };

        foreach (var rd in results_detail)
        {
            // Previous Candidate Result
            var pvObj = (from aa in dc.Nominations
                where aa.Id == rd.Id
                select aa).FirstOrDefault();
            var pv = 0;

            if (pvObj != null) pv = pvObj.Votes ?? 0;

            rl.ResultLogDetails.Add(new ResultLogDetail
            {
                CandidateId = (int)rd.CandidateId,
                Votes = mode == "Add" ? rd.ChangeVotes + pv : rd.ChangeVotes,
                NewVotes = rd.ChangeVotes,
                PreviousVotes = pv
            });
        }

        dc.ResultLogs.Add(rl);
        dc.SaveChanges();
        foreach (var res in results_detail)
        {
            var q = (from aa in dc.Nominations
                where aa.Id == res.Id
                select aa).FirstOrDefault();
            q.Votes = res.TotalVotes;
            if (mode == "Add")
                q.ResultPollingStations = (q.ResultPollingStations ?? 0) + changePollingStations;
            else
                q.ResultPollingStations = changePollingStations;
            q.ModifiedBy = user;
            q.ModifiedDate = DateTime.Now;
            q.IsWinner = false;
            dc.SaveChanges();
        }

        #region set winner status

        // Get Winner Info
        var winner1 = (from aa in dc.Nominations
            where aa.StructureId == constituencyId &&
                  aa.ElectionPhaseId == phaseId &&
                  aa.ElectionAssemblyId == assemblyId &&
                  aa.SeatTypeId == seatTypeId &&
                  aa.Votes > 0
            orderby aa.Votes descending
            select aa).FirstOrDefault();
        // If Winner Exist
        if (winner1 != null)
        {
            var winner2 = (from aa in dc.Nominations
                where aa.StructureId == constituencyId &&
                      aa.ElectionPhaseId == phaseId &&
                      aa.ElectionAssemblyId == assemblyId &&
                      aa.SeatTypeId == seatTypeId &&
                      aa.Votes == winner1.Votes
                select aa).FirstOrDefault();
            // If another candidate has same votes
            if (winner2 != null)
                winner1.IsWinner = true;
            else
                winner1.IsWinner = true;
        }

        #endregion set winner status

        var qResult = (from aa in dc.Nominations
            where aa.StructureId == constituencyId &&
                  aa.ElectionPhaseId == phaseId &&
                  aa.ElectionAssemblyId == assemblyId &&
                  aa.SeatTypeId == seatTypeId
            select new ResultDTO
            {
                AssemblyId = assemblyId,
                Assembly = aa.ElectionAssembly.EnglishTitle,
                Constituency = $"{aa.Structure.Code} - {aa.Structure.EnglishTitle}",
                ConstituencyId = aa.StructureId,
                ElectionId = aa.ElectionPhase.ElectionId,
                Election = aa.ElectionPhase.Election.EnglishTitle,
                LastUpdatedOn = DateTime.Now,
                Phase = aa.ElectionPhase.Title,
                PhaseId = aa.ElectionPhaseId,
                SeatTypeId = seatTypeId,
                SeatType = aa.SeatType.EnglishTitle,
                ResultOfPollingStations = aa.ResultPollingStations ?? 0,
                TotalPollingStations = aa.Structure.TotalPollingStations ?? 0,
                TotalVoters = aa.Structure.TotalVoters,
                AssemblyTypeId = aa.ElectionAssembly.AssemblyTypeId
            }).FirstOrDefault();

        var winner = (from aa in dc.Nominations
            where aa.StructureId == constituencyId &&
                  aa.ElectionPhaseId == phaseId &&
                  aa.ElectionAssemblyId == assemblyId &&
                  aa.SeatTypeId == seatTypeId &&
                  aa.Votes != null &&
                  aa.Votes > 0
            orderby aa.Votes ?? 0 descending
            select new
            {
                aa.CandidteId,
                aa.Candidate.EnglishName,
                VotesObtain = aa.Votes ?? 0,
                Party = aa.Party.EnglishTitle
            }).FirstOrDefault();

        if (winner != null)
        {
            qResult.WinnerCandidateId = winner.CandidteId;
            qResult.WinnerName = winner.EnglishName;
            qResult.WinnerParty = winner.Party;
            qResult.WinnerVotes = winner.VotesObtain;

            var runnerUp = (from aa in dc.Nominations
                where aa.StructureId == constituencyId &&
                      aa.CandidteId != winner.CandidteId &&
                      aa.ElectionPhaseId == phaseId &&
                      aa.ElectionAssemblyId == assemblyId &&
                      aa.SeatTypeId == seatTypeId &&
                      aa.Votes != null &&
                      aa.Votes > 0
                orderby aa.Votes ?? 0 descending
                select new
                {
                    aa.CandidteId,
                    aa.Candidate.EnglishName,
                    VotesObtain = aa.Votes ?? 0,
                    Party = aa.Party.EnglishTitle
                }).FirstOrDefault();

            if (runnerUp != null)
            {
                qResult.RunnerUpCandidateId = runnerUp.CandidteId;
                qResult.RunnerUpName = runnerUp.EnglishName;
                qResult.RunnerUpParty = runnerUp.Party;
                qResult.RunnerUpVotes = runnerUp.VotesObtain;
            }
            else
            {
                qResult.RunnerUpName = string.Empty;
                qResult.RunnerUpCandidateId = 0;
                qResult.RunnerUpParty = string.Empty;
                qResult.RunnerUpVotes = 0;
                qResult.RunnerUpCandidateId = null;
            }
        }
        else
        {
            if (qResult != null)
            {
                qResult.WinnerCandidateId = 0;
                qResult.WinnerName = string.Empty;
                qResult.WinnerParty = string.Empty;
                qResult.WinnerVotes = 0;
                qResult.RunnerUpCandidateId = 0;
                qResult.RunnerUpName = string.Empty;
                qResult.RunnerUpParty = string.Empty;
                qResult.RunnerUpVotes = 0;
                qResult.WinnerCandidateId = null;
                qResult.RunnerUpCandidateId = null;
            }

            //return null;
        }

        var electionId = (from bb in dc.ElectionPhases
            where bb.Id == phaseId
            select bb.ElectionId).FirstOrDefault();

        var dis = GetConstituencyDistrict(constituencyId, dc);

        var liveResult = (from aa in dc.LiveResults
            where aa.ElectionId == electionId &&
                  aa.PhaseId == phaseId &&
                  aa.AssemblyId == assemblyId &&
                  aa.ConstituencyId == constituencyId &&
                  aa.SeatTypeId == seatTypeId
            select aa).FirstOrDefault();
        if (liveResult == null)
        {
            liveResult = new LiveResult
            {
                AssemblyId = assemblyId,
                ElectionId = electionId,
                PhaseId = phaseId,
                ConstituencyId = constituencyId,
                SeatTypeId = seatTypeId,
                Election = qResult.Election,
                Assembly = qResult.Assembly,
                Constituency = qResult.Constituency,
                SeatType = qResult.SeatType,
                CreatedBy = user,
                CreatedDate = DateTime.Now,
                Phase = qResult.Phase,
                TotalVoters = qResult.TotalVoters,
                TotalPollingStations = qResult.TotalPollingStations,
                PollingStationResult = qResult.ResultOfPollingStations,
                Winner = qResult.WinnerName,
                WinnerParty = qResult.WinnerParty,
                WinnerVotes = qResult.WinnerVotes,
                RunnerUp = qResult.RunnerUpName,
                RunnerUpParty = qResult.RunnerUpParty,
                RunnerUpVotes = qResult.RunnerUpVotes,
                IsPublished = false,
                ModifiedBy = user,
                ModifiedDate = DateTime.Now,
                WinnerCandidateId = qResult.WinnerCandidateId,
                RunnerUpCandidateId = qResult.RunnerUpCandidateId, IsTickerPublish = false
            };
            liveResult.District = dis;
            dc.LiveResults.Add(liveResult);
            dc.SaveChanges();
        }
        else
        {
            liveResult.TotalVoters = qResult.TotalVoters;
            liveResult.TotalPollingStations = qResult.TotalPollingStations;
            liveResult.PollingStationResult = qResult.ResultOfPollingStations;
            liveResult.Winner = qResult.WinnerName;
            liveResult.WinnerParty = qResult.WinnerParty;
            liveResult.WinnerVotes = qResult.WinnerVotes;
            liveResult.RunnerUp = qResult.RunnerUpName;
            liveResult.RunnerUpParty = qResult.RunnerUpParty;
            liveResult.RunnerUpVotes = qResult.RunnerUpVotes;
            liveResult.ModifiedBy = user;
            liveResult.ModifiedDate = DateTime.Now;
            liveResult.IsPublished = false;
            liveResult.WinnerCandidateId = qResult.WinnerCandidateId;
            liveResult.RunnerUpCandidateId = qResult.RunnerUpCandidateId;
            liveResult.District = dis;
            liveResult.IsTickerPublish = false;
            dc.SaveChanges();
        }

        //dc.Database.ExecuteSqlRaw("dbo.spDBGenerateResult", new string[] { phaseId.ToString() });
        // ==> Need to update <== dc.Database.ExecuteSqlInterpolated($"EXECUTE dbo.spDBGenerateResult {phaseId}");

        tran.Commit();

        //var ps = (from a in dc.PollingSchemes
        //		  where a.StructureId == constituencyId
        //		  && a.PhaseId == phaseId
        //		  && a.ResultMode == ResultMode.Default
        //		  select a).ToList();

        //foreach (var pp in ps)
        //{
        //	pp.ResultMode = ResultMode.Lumsum;
        //	pp.ResultModeChangedBy = user;
        //	pp.ResultModeChangedDate = DateTime.Now;
        //	dc.SaveChanges();
        //}
        var na = (from a in dc.Structures.OfType<NationalAssemblyHalka>()
            where a.Id == constituencyId
            select new
            {
                a.DistrictId,
                a.District.Division.ProvinceId
            }).FirstOrDefault();
        if (na != null)
        {
            qResult.DistrictId = na.DistrictId;
            qResult.ProvinceId = na.ProvinceId;
        }


        var pa = (from a in dc.Structures.OfType<ProvincialAssemblyHalka>()
            where a.Id == constituencyId
            select new
            {
                a.DistrictId,
                a.District.Division.ProvinceId
            }).FirstOrDefault();
        if (pa != null)
        {
            qResult.DistrictId = pa.DistrictId;
            qResult.ProvinceId = pa.ProvinceId;
        }

        //qResult.District = dis;
        return Task.FromResult(qResult);
    }

    public string GetConstituencyDistrict(int id, ApplicationDbContext dc)
    {
        var q = (from aa in dc.Structures.OfType<NationalAssemblyHalka>()
            where aa.Id == id
            select new { aa.DistrictId, District = aa.District.EnglishTitle }).FirstOrDefault();
        if (q != null)
            return q.District;

        q = (from aa in dc.Structures.OfType<ProvincialAssemblyHalka>()
            where aa.Id == id
            select new { aa.DistrictId, District = aa.District.EnglishTitle }).FirstOrDefault();
        if (q != null)
            return q.District;

        q = (from aa in dc.Structures.OfType<UnionCouncil>()
            where aa.Id == id
            select new { aa.Town.DistrictId, District = aa.Town.District.EnglishTitle }).FirstOrDefault();
        if (q != null)
            return q.District;

        q = (from aa in dc.Structures.OfType<Ward>()
                where aa.Id == id
                select new { aa.UnionCouncil.Town.DistrictId, District = aa.UnionCouncil.Town.District.EnglishTitle })
            .FirstOrDefault();
        if (q != null)
            return q.District;

        return string.Empty;
    }

    public Task<string> SaveResults2(List<ResultDetailDTO> results_detail, int changePollingStations, string mode,
        string user, int namanigarId)
    {
        using var dc = _contextFactory.CreateDbContext();
        using (var tran = dc.Database.BeginTransaction())
        {
            try
            {
                foreach (var res in results_detail)
                {
                    var q = (from aa in dc.Nominations
                        where aa.Id == res.Id
                        select aa).FirstOrDefault();
                    q.Votes = res.TotalVotes;
                    if (mode == "Add")
                        q.ResultPollingStations = (q.ResultPollingStations ?? 0) + changePollingStations;
                    else
                        q.ResultPollingStations = changePollingStations;
                    q.ModifiedBy = user;
                    q.ModifiedDate = DateTime.Now;
                    dc.SaveChanges();
                }

                tran.Commit();
                return Task.FromResult("OK");
            }
            catch (Exception)
            {
                tran.Rollback();

                throw;
            }
        }
    }

    public Task<ResultDTO> SaveResultsFromNamanigar(List<ResultDetailDTO> results_detail, int assemblyId, int phaseId,
        int constituencyId, int seatTypeId, int newPollingStations, string mode, string user, int namanigarId)
    {
        using var dc = _contextFactory.CreateDbContext();
        using (var tran = dc.Database.BeginTransaction())
        {
            // Save Log Info Save Result Previous Polling Stations
            var pps = (from aa in dc.Nominations
                where aa.StructureId == constituencyId &&
                      aa.ElectionAssemblyId == assemblyId &&
                      aa.ElectionPhaseId == phaseId
                select aa).FirstOrDefault();

            var ppsValue = 0;
            if (pps != null) ppsValue = pps.ResultPollingStations ?? 0;
            // since we importing from namanigar results then we use current polling station as
            // previous polling stations
            var prvPollingStations = pps.ResultPollingStations;
            var rl = new ResultLog
            {
                Action = mode,
                AssemblyId = assemblyId,
                ConstituencyId = constituencyId,
                CreatedByUserId = user,
                CreatedDate = DateTime.Now,
                NamanigarId = 1,
                NewPollingStations = newPollingStations,
                PhaseId = phaseId,
                PreviousPollingStations = ppsValue,
                PollingStations =
                    newPollingStations, //mode == "Add" ? changePollingStations + ppsValue : changePollingStations,
                SeatTypeId = seatTypeId,
                ResultLogDetails = new List<ResultLogDetail>()
            };

            foreach (var rd in results_detail)
            {
                // Previous Candidate Result Previous nomination data
                var pvObj = (from aa in dc.Nominations
                    where aa.Id == rd.Id
                    select aa).FirstOrDefault();
                // previous votes
                var pv = 0;

                // if there is no voted defined yet then set 0 as previous votes
                if (pvObj != null) pv = pvObj.Votes ?? 0;

                rl.ResultLogDetails.Add(new ResultLogDetail
                {
                    CandidateId = (int)rd.CandidateId,
                    Votes = rd.Votes, //= mode == "Add" ? rd.ChangeVotes + pv : rd.ChangeVotes,
                    NewVotes = rd.Votes,
                    PreviousVotes = pv
                });
            }

            dc.ResultLogs.Add(rl);
            dc.SaveChanges();

            // Update votes of all nominations get all nominations
            foreach (var res in results_detail)
            {
                var q = (from aa in dc.Nominations
                    where aa.Id == res.Id
                    select aa).FirstOrDefault();
                q.Votes = res.Votes;
                //if (mode == "Add")
                //	q.ResultPollingStations = (q.ResultPollingStations ?? 0) + newPollingStations;
                //else
                //	q.ResultPollingStations = newPollingStations;
                q.ResultPollingStations = newPollingStations;
                q.ModifiedBy = user;
                q.ModifiedDate = DateTime.Now;
                q.IsWinner = false;
                dc.SaveChanges();
            }

            #region set winner status

            // Get Winner Info
            var winner1 = (from aa in dc.Nominations
                where aa.StructureId == constituencyId &&
                      aa.ElectionPhaseId == phaseId &&
                      aa.ElectionAssemblyId == assemblyId &&
                      aa.SeatTypeId == seatTypeId &&
                      aa.Votes > 0
                orderby aa.Votes descending
                select aa).FirstOrDefault();
            // If Winner Exist
            if (winner1 != null)
            {
                var winner2 = (from aa in dc.Nominations
                    where aa.StructureId == constituencyId &&
                          aa.ElectionPhaseId == phaseId &&
                          aa.ElectionAssemblyId == assemblyId &&
                          aa.SeatTypeId == seatTypeId &&
                          aa.Votes == winner1.Votes
                    select aa).FirstOrDefault();
                // If another candidate has same votes
                if (winner2 != null)
                    winner1.IsWinner = true;
                else
                    winner1.IsWinner = true;
            }

            #endregion set winner status

            var qResult = (from aa in dc.Nominations
                where aa.StructureId == constituencyId &&
                      aa.ElectionPhaseId == phaseId &&
                      aa.ElectionAssemblyId == assemblyId &&
                      aa.SeatTypeId == seatTypeId
                select new ResultDTO
                {
                    AssemblyId = assemblyId,
                    Assembly = aa.ElectionAssembly.EnglishTitle,
                    Constituency = $"{aa.Structure.Code} - {aa.Structure.EnglishTitle}",
                    ConstituencyId = aa.StructureId,
                    ElectionId = aa.ElectionPhase.ElectionId,
                    Election = aa.ElectionPhase.Election.EnglishTitle,
                    LastUpdatedOn = DateTime.Now,
                    Phase = aa.ElectionPhase.Title,
                    PhaseId = aa.ElectionPhaseId,
                    SeatTypeId = seatTypeId,
                    SeatType = aa.SeatType.EnglishTitle,
                    ResultOfPollingStations = aa.ResultPollingStations ?? 0,
                    TotalPollingStations = aa.Structure.TotalPollingStations ?? 0,
                    TotalVoters = aa.Structure.TotalVoters
                }).FirstOrDefault();

            var winner = (from aa in dc.Nominations
                where aa.StructureId == constituencyId &&
                      aa.ElectionPhaseId == phaseId &&
                      aa.ElectionAssemblyId == assemblyId &&
                      aa.SeatTypeId == seatTypeId &&
                      aa.Votes != null &&
                      aa.Votes > 0
                orderby aa.Votes ?? 0 descending
                select new
                {
                    aa.CandidteId,
                    aa.Candidate.EnglishName,
                    VotesObtain = aa.Votes ?? 0,
                    Party = aa.Party.EnglishTitle
                }).FirstOrDefault();

            if (winner != null)
            {
                qResult.WinnerCandidateId = winner.CandidteId;
                qResult.WinnerName = winner.EnglishName;
                qResult.WinnerParty = winner.Party;
                qResult.WinnerVotes = winner.VotesObtain;

                var runnerUp = (from aa in dc.Nominations
                    where aa.StructureId == constituencyId &&
                          aa.CandidteId != winner.CandidteId &&
                          aa.ElectionPhaseId == phaseId &&
                          aa.ElectionAssemblyId == assemblyId &&
                          aa.SeatTypeId == seatTypeId &&
                          aa.Votes != null &&
                          aa.Votes > 0
                    orderby aa.Votes ?? 0 descending
                    select new
                    {
                        aa.CandidteId,
                        aa.Candidate.EnglishName,
                        VotesObtain = aa.Votes ?? 0,
                        Party = aa.Party.EnglishTitle
                    }).FirstOrDefault();

                if (runnerUp != null)
                {
                    qResult.RunnerUpCandidateId = runnerUp.CandidteId;
                    qResult.RunnerUpName = runnerUp.EnglishName;
                    qResult.RunnerUpParty = runnerUp.Party;
                    qResult.RunnerUpVotes = runnerUp.VotesObtain;
                }
                else
                {
                    qResult.RunnerUpName = string.Empty;
                    qResult.RunnerUpCandidateId = 0;
                    qResult.RunnerUpParty = string.Empty;
                    qResult.RunnerUpVotes = 0;
                }
            }
            else
            {
                if (qResult != null)
                {
                    qResult.WinnerCandidateId = 0;
                    qResult.WinnerName = string.Empty;
                    qResult.WinnerParty = string.Empty;
                    qResult.WinnerVotes = 0;
                    qResult.RunnerUpCandidateId = 0;
                    qResult.RunnerUpName = string.Empty;
                    qResult.RunnerUpParty = string.Empty;
                    qResult.RunnerUpVotes = 0;
                }

                //return null;
            }

            var electionId = (from bb in dc.ElectionPhases
                where bb.Id == phaseId
                select bb.ElectionId).FirstOrDefault();

            var liveResult = (from aa in dc.LiveResults
                where aa.ElectionId == electionId &&
                      aa.PhaseId == phaseId &&
                      aa.AssemblyId == assemblyId &&
                      aa.ConstituencyId == constituencyId &&
                      aa.SeatTypeId == seatTypeId
                select aa).FirstOrDefault();
            if (liveResult == null)
            {
                liveResult = new LiveResult
                {
                    AssemblyId = assemblyId,
                    ElectionId = electionId,
                    PhaseId = phaseId,
                    ConstituencyId = constituencyId,
                    SeatTypeId = seatTypeId,
                    Election = qResult.Election,
                    Assembly = qResult.Assembly,
                    Constituency = qResult.Constituency,
                    SeatType = qResult.SeatType,
                    CreatedBy = user,
                    CreatedDate = DateTime.Now,
                    Phase = qResult.Phase,
                    TotalVoters = qResult.TotalVoters,
                    TotalPollingStations = qResult.TotalPollingStations,
                    PollingStationResult = qResult.ResultOfPollingStations,
                    Winner = qResult.WinnerName,
                    WinnerParty = qResult.WinnerParty,
                    WinnerVotes = qResult.WinnerVotes,
                    RunnerUp = qResult.RunnerUpName,
                    RunnerUpParty = qResult.RunnerUpParty,
                    RunnerUpVotes = qResult.RunnerUpVotes,
                    IsPublished = false,
                    ModifiedBy = user,
                    ModifiedDate = DateTime.Now,
                    WinnerCandidateId = qResult.WinnerCandidateId,
                    RunnerUpCandidateId = qResult.RunnerUpCandidateId, IsTickerPublish = false
                };
                dc.LiveResults.Add(liveResult);
                dc.SaveChanges();
            }
            else
            {
                liveResult.TotalVoters = qResult.TotalVoters;
                liveResult.TotalPollingStations = qResult.TotalPollingStations;
                liveResult.PollingStationResult = qResult.ResultOfPollingStations;
                liveResult.Winner = qResult.WinnerName;
                liveResult.WinnerParty = qResult.WinnerParty;
                liveResult.WinnerVotes = qResult.WinnerVotes;
                liveResult.RunnerUp = qResult.RunnerUpName;
                liveResult.RunnerUpParty = qResult.RunnerUpParty;
                liveResult.RunnerUpVotes = qResult.RunnerUpVotes;
                liveResult.ModifiedBy = user;
                liveResult.ModifiedDate = DateTime.Now;
                liveResult.IsPublished = false;
                liveResult.WinnerCandidateId = qResult.WinnerCandidateId;
                liveResult.RunnerUpCandidateId = qResult.RunnerUpCandidateId;
                liveResult.IsTickerPublish = false;
                dc.SaveChanges();
            }

            //dc.Database.ExecuteSqlRaw("dbo.spDBGenerateResult", new string[] { phaseId.ToString() });
            // ==> Need to update <== dc.Database.ExecuteSqlInterpolated($"EXECUTE dbo.spDBGenerateResult {phaseId}");

            tran.Commit();
            return Task.FromResult(qResult);
        }
    }

    public Task<ResultDTO> SaveResultsPS(List<ResultDetailDTO> resultDetail, int pollingStationId, string user,
        int constituencyId, int seatTypeId, int electionId, int assemblyId, int phaseId, int TotalVotesCast)
    {
        var output = new ResultDTO();
        using var dc = _contextFactory.CreateDbContext();
        using var tran = dc.Database.BeginTransaction();
        var lastUpdateDate = DateTime.Now;
        try
        {
            #region Save Polling Station Wise Nominated Candidate Votes

            foreach (var r in resultDetail)
            {
                //var logDate = DateTime.Now;
                var res = (from aa in dc.PSResults
                    where aa.NominationId == r.Id &&
                          aa.PollingStationId == pollingStationId
                    select aa).FirstOrDefault();
                if (res == null)
                {
                    res = new PSResult
                    {
                        CreatedBy = user,
                        CreatedDate = lastUpdateDate,
                        NominationId = r.Id,
                        PollingStationId = pollingStationId,
                        Votes = r.PSVotes,
                        IsPublished = false
                    };
                    dc.PSResults.Add(res);
                    dc.SaveChanges();
                }
                else
                {
                    res.ModifiedBy = user;
                    res.ModifiedDate = lastUpdateDate;
                    res.Votes = r.PSVotes;
                    res.IsPublished = false;
                    dc.SaveChanges();
                }

                var resLog = new PSResultLog
                {
                    IsPublished = false,
                    LogDate = lastUpdateDate,
                    NominationId = r.Id,
                    PollingStationId = pollingStationId,
                    PublishedBy = string.Empty,
                    PublishedDate = null,
                    UserId = user,
                    Votes = r.PSVotes
                };
                dc.PSResultLogs.Add(resLog);
                dc.SaveChanges();
            }

            #endregion Save Polling Station Wise Nominated Candidate Votes

            foreach (var r in resultDetail)
            {
                var psCount = (from aa in dc.PSResults
                    where aa.NominationId == r.Id
                    select aa.PollingStationId).Distinct().Count();
                var comVotes = (from aa in dc.PSResults
                    where aa.NominationId == r.Id
                          && aa.Votes > 0
                    select aa.Votes).Sum();
                var nn = (from aa in dc.Nominations
                    where aa.Id == r.Id
                    select aa).FirstOrDefault();
                nn.Votes = comVotes;
                nn.ResultPollingStations = psCount;
                nn.IsWinner = false;
                //nn.ModifiedBy = user;
                //nn.ModifiedDate = lastUpdateDate;
                nn.ResultUpdateBy = user;
                nn.ResultUpdateDate = DateTime.Now;
                dc.SaveChanges();
            }

            var wc = (from aa in dc.Nominations
                where aa.StructureId == constituencyId &&
                      aa.SeatTypeId == seatTypeId &&
                      aa.ElectionPhaseId == phaseId &&
                      aa.ElectionAssemblyId == assemblyId
                orderby aa.Votes descending
                select aa).FirstOrDefault();
            wc.IsWinner = true;
            dc.SaveChanges();

            output = (from aa in dc.Nominations
                join hl in dc.Structures.OfType<ProvincialAssemblyHalka>() on aa.StructureId equals hl.Id
                where aa.StructureId == constituencyId &&
                      aa.SeatTypeId == seatTypeId &&
                      aa.ElectionAssemblyId == assemblyId &&
                      aa.ElectionPhaseId == phaseId
                select new ResultDTO
                {
                    Constituency = aa.Structure.EnglishTitle,
                    Assembly = aa.ElectionAssembly.EnglishTitle,
                    AssemblyId = aa.ElectionAssemblyId,
                    AssemblyTypeId = aa.ElectionAssembly.AssemblyTypeId,
                    ConstituencyId = aa.StructureId,
                    Election = aa.ElectionAssembly.Election.EnglishTitle,
                    ElectionId = aa.ElectionPhase.ElectionId,
                    LastUpdate = aa.ModifiedDate ?? aa.CreatedDate,
                    Phase = aa.ElectionPhase.Title,
                    LastUpdatedOn = aa.ModifiedDate ?? aa.CreatedDate,
                    PhaseId = aa.ElectionPhaseId,
                    SeatType = aa.SeatType.EnglishTitle,
                    SeatTypeId = aa.SeatTypeId,
                    TotalPollingStations = aa.Structure.MaleVoters ?? 0 + aa.Structure.FemaleVoters ?? 0,
                    TotalVoters = aa.Structure.TotalVoters,
                    DistrictId = hl.DistrictId,
                    ProvinceId = hl.District.Division.ProvinceId,
                    Ticker = "غیر سرکاری غیر حتمی نتیجہ"
                }).FirstOrDefault();

            #region Live Result Log Genrate

            var qResult = (from aa in dc.Nominations
                where aa.StructureId == constituencyId &&
                      aa.ElectionPhaseId == phaseId &&
                      aa.ElectionAssemblyId == assemblyId &&
                      aa.SeatTypeId == seatTypeId
                select new ResultDTO
                {
                    AssemblyId = assemblyId,
                    Assembly = aa.ElectionAssembly.EnglishTitle,
                    Constituency = $"{aa.Structure.Code} - {aa.Structure.EnglishTitle}",
                    ConstituencyId = aa.StructureId,
                    ElectionId = aa.ElectionPhase.ElectionId,
                    Election = aa.ElectionPhase.Election.EnglishTitle,
                    LastUpdatedOn = DateTime.Now,
                    Phase = aa.ElectionPhase.Title,
                    PhaseId = aa.ElectionPhaseId,
                    SeatTypeId = seatTypeId,
                    SeatType = aa.SeatType.EnglishTitle,
                    ResultOfPollingStations = aa.ResultPollingStations ?? 0,
                    TotalPollingStations = aa.Structure.TotalPollingStations ?? 0,
                    TotalVoters = aa.Structure.TotalVoters,
                    Status = "No"
                }).FirstOrDefault();

            var winner = (from aa in dc.Nominations
                where aa.StructureId == constituencyId &&
                      aa.ElectionPhaseId == phaseId &&
                      aa.ElectionAssemblyId == assemblyId &&
                      aa.SeatTypeId == seatTypeId &&
                      aa.Votes != null &&
                      aa.Votes > 0
                orderby aa.Votes ?? 0 descending
                select new
                {
                    aa.CandidteId,
                    aa.Candidate.EnglishName,
                    VotesObtain = aa.Votes ?? 0,
                    Party = aa.Party.EnglishTitle
                }).FirstOrDefault();

            if (winner != null)
            {
                qResult.WinnerCandidateId = winner.CandidteId;
                qResult.WinnerName = winner.EnglishName;
                qResult.WinnerParty = winner.Party;
                qResult.WinnerVotes = winner.VotesObtain;

                var runnerUp = (from aa in dc.Nominations
                    where aa.StructureId == constituencyId &&
                          aa.CandidteId != winner.CandidteId &&
                          aa.ElectionPhaseId == phaseId &&
                          aa.ElectionAssemblyId == assemblyId &&
                          aa.SeatTypeId == seatTypeId &&
                          aa.Votes != null &&
                          aa.Votes > 0
                    orderby aa.Votes ?? 0 descending
                    select new
                    {
                        aa.CandidteId,
                        aa.Candidate.EnglishName,
                        VotesObtain = aa.Votes ?? 0,
                        Party = aa.Party.EnglishTitle
                    }).FirstOrDefault();

                if (runnerUp != null)
                {
                    qResult.RunnerUpCandidateId = runnerUp.CandidteId;
                    qResult.RunnerUpName = runnerUp.EnglishName;
                    qResult.RunnerUpParty = runnerUp.Party;
                    qResult.RunnerUpVotes = runnerUp.VotesObtain;
                }
                else
                {
                    qResult.RunnerUpName = string.Empty;
                    qResult.RunnerUpCandidateId = 0;
                    qResult.RunnerUpParty = string.Empty;
                    qResult.RunnerUpVotes = 0;
                }
            }
            else
            {
                if (qResult != null)
                {
                    qResult.WinnerCandidateId = 0;
                    qResult.WinnerName = string.Empty;
                    qResult.WinnerParty = string.Empty;
                    qResult.WinnerVotes = 0;
                    qResult.RunnerUpCandidateId = 0;
                    qResult.RunnerUpName = string.Empty;
                    qResult.RunnerUpParty = string.Empty;
                    qResult.RunnerUpVotes = 0;
                }

                //return null;
            }

            //var electionId = (from bb in dc.ElectionPhases
            //						where bb.Id == obj.PhaseId
            //						select bb.ElectionId).FirstOrDefault();

            var dis = GetConstituencyDistrict(constituencyId, dc);

            var liveResult = (from aa in dc.LiveResults
                where aa.ElectionId == electionId &&
                      aa.PhaseId == phaseId &&
                      aa.AssemblyId == assemblyId &&
                      aa.ConstituencyId == constituencyId &&
                      aa.SeatTypeId == seatTypeId
                select aa).FirstOrDefault();
            if (liveResult == null)
            {
                liveResult = new LiveResult
                {
                    AssemblyId = assemblyId,
                    ElectionId = electionId,
                    PhaseId = phaseId,
                    ConstituencyId = constituencyId,
                    SeatTypeId = seatTypeId,
                    Election = qResult.Election,
                    Assembly = qResult.Assembly,
                    Constituency = qResult.Constituency,
                    SeatType = qResult.SeatType,
                    CreatedBy = user,
                    CreatedDate = DateTime.Now,
                    Phase = qResult.Phase,
                    TotalVoters = qResult.TotalVoters,
                    TotalPollingStations = qResult.TotalPollingStations,
                    PollingStationResult = qResult.ResultOfPollingStations,
                    Winner = qResult.WinnerName,
                    WinnerParty = qResult.WinnerParty,
                    WinnerVotes = qResult.WinnerVotes,
                    RunnerUp = qResult.RunnerUpName,
                    RunnerUpParty = qResult.RunnerUpParty,
                    RunnerUpVotes = qResult.RunnerUpVotes,
                    IsPublished = false,
                    ModifiedBy = user,
                    ModifiedDate = DateTime.Now,
                    District = dis, IsTickerPublish = false
                };
                dc.LiveResults.Add(liveResult);
                dc.SaveChanges();
            }
            else
            {
                liveResult.TotalVoters = qResult.TotalVoters;
                liveResult.TotalPollingStations = qResult.TotalPollingStations;
                liveResult.PollingStationResult = qResult.ResultOfPollingStations;
                liveResult.Winner = qResult.WinnerName;
                liveResult.WinnerParty = qResult.WinnerParty;
                liveResult.WinnerVotes = qResult.WinnerVotes;
                liveResult.RunnerUp = qResult.RunnerUpName;
                liveResult.RunnerUpParty = qResult.RunnerUpParty;
                liveResult.RunnerUpVotes = qResult.RunnerUpVotes;
                liveResult.ModifiedBy = user;
                liveResult.ModifiedDate = DateTime.Now;
                liveResult.IsPublished = false;
                liveResult.District = dis;
                liveResult.IsTickerPublish = false;
                dc.SaveChanges();
            }

            #endregion Live Result Log Genrate

            qResult.District = dis;


            var psr = (from a in dc.PSTotalVoteCastes
                where a.PollingStationId == pollingStationId &&
                      a.PhaseId == phaseId
                select a).FirstOrDefault();
            if (psr == null)
            {
                psr = new PSTotalVoteCaste
                    { PhaseId = phaseId, PollingStationId = pollingStationId, Votes = TotalVotesCast };
                dc.PSTotalVoteCastes.Add(psr);
                dc.SaveChanges();
            }
            else
            {
                psr.Votes = TotalVotesCast;
                dc.SaveChanges();
            }


            tran.Commit();

            return Task.FromResult(qResult);
        }
        catch (Exception)
        {
            tran.Rollback();
            throw;
            //var msg = ex.Message;
            //if (ex.InnerException != null)
            //	msg += " - Detail: " + ex.InnerException.Message;
            //return Task.FromResult(msg);
        }
    }

    internal Task<int> GetCurrentElection()
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.ElectionPhases
            where aa.IsActive
            select aa).FirstOrDefault();
        if (q != null)
            return Task.FromResult(q.ElectionId);
        return Task.FromResult(0);
    }

    internal Task<List<GeneralItemDTO>> GetDistricts(int? selectedProvince, int electionId)
    {
        if (selectedProvince == null) return Task.FromResult(new List<GeneralItemDTO>());

        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.Structures.OfType<District>()
            where aa.ElectionId == electionId &&
                  aa.Division.ProvinceId == selectedProvince
            orderby aa.EnglishTitle
            select new GeneralItemDTO
            {
                Id = aa.Id,
                EnglishTitle = aa.EnglishTitle,
                UrduTitle = aa.UrduTitle
            }).ToList();
        return Task.FromResult(q);
    }

    internal Task<int?> GetFemaleVoters(int? selectedStructureId, int? pollingStationId)
    {
        using var dc = _contextFactory.CreateDbContext();
        if (pollingStationId == null)
        {
            var q = (from aa in dc.PollingStations
                where aa.ConstituencyId == selectedStructureId &&
                      aa.FemaleVoters != null
                select aa.FemaleVoters).Sum();
            return Task.FromResult(q);
        }
        else
        {
            var q = (from bb in dc.PollingStations
                where bb.Id == pollingStationId
                select bb.FemaleVoters).FirstOrDefault();
            return Task.FromResult(q);
        }
    }

    internal Task<int?> GetMaleVoters(int? selectedStructureId, int? pollingStationId)
    {
        using var dc = _contextFactory.CreateDbContext();
        if (pollingStationId == null)
        {
            var q = (from aa in dc.PollingStations
                where aa.ConstituencyId == selectedStructureId
                      && aa.MaleVoters != null
                select aa.MaleVoters).Sum();
            return Task.FromResult(q);
        }
        else
        {
            var q = (from bb in dc.PollingStations
                where bb.Id == pollingStationId
                select bb.MaleVoters).FirstOrDefault();
            return Task.FromResult(q);
        }
    }

    internal Task<List<NamanigarResultsListViewDTO>> GetNNResultsList2(int phaseId)
    {
        using var dc = _contextFactory.CreateDbContext();
        // Get All Constituencies in Log Table
        var q = (from aa in dc.NamanigarResultLogs
            join nm in dc.Nominations on aa.NominationId equals nm.Id
            where nm.ElectionPhaseId == phaseId
            orderby aa.ActionDateTime descending
            select new
            {
                nm.StructureId,
                nm.SeatTypeId,
                nm.ResultPollingStations
            }).Distinct().ToList();
        // For each constituencies
        List<NamanigarResultsListViewDTO> lst = new();

        var q2 = (from aa in dc.NamanigarResults
            join n in dc.Nominations on aa.NominationId equals n.Id
            where n.ElectionPhaseId == phaseId
            select new NamanigarResultsListViewDTO
            {
                Action = aa.LastActionMode,
                ActionDate = aa.ModifiedOn ?? aa.CreatedOn,
                Assembly = n.ElectionAssembly.EnglishTitle,
                BatchId = aa.BatchId,
                Constituency = n.Structure.EnglishTitle,
                ConstituencyId = n.StructureId,
                CurrentPS = 0,
                Namanigar = string.IsNullOrEmpty(aa.ModifiedBy) ? aa.CreatedBy : aa.ModifiedBy,
                PS = aa.ResultPollingStations,
                SeatType = n.SeatType.EnglishTitle,
                SeatTypeId = n.SeatTypeId,
                Status = aa.Status == ApprovalStatus.Approved ? "Approved" :
                    aa.Status == ApprovalStatus.Pending ? "Pending" : string.Empty
            }).Distinct().ToList();
        foreach (var i in q2)
        {
            var cp = (from aa in q
                where aa.SeatTypeId == i.SeatTypeId &&
                      aa.StructureId == i.ConstituencyId
                select aa).FirstOrDefault();
            if (cp != null) i.CurrentPS = cp.ResultPollingStations ?? 0;
        }


        var q3 = (from aa in q2
            orderby aa.Status descending, aa.ActionDate descending
            select aa).ToList();
        return Task.FromResult(q3);
    }

    internal Task<int> GetPollingStationCount(int? selectedStructureId, int? pollingStationId)
    {
        using var dc = _contextFactory.CreateDbContext();
        if (pollingStationId == null)
        {
            var q = (from aa in dc.PollingStations
                where aa.ConstituencyId == selectedStructureId
                select aa).Count();
            return Task.FromResult(q);
        }

        return Task.FromResult(1);
    }

    internal Task<List<GeneralItemDTO>> GetPollingStations(int selectedStructureId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q1 = (from aa in dc.PollingStations
            where aa.ConstituencyId == selectedStructureId
            orderby aa.Number
            select new
            {
                aa.Id,
                aa.Number,
                EnglishTitle = $"{aa.Number} {aa.EnglishTitle}"
            }).ToList();

        var q = (from aa in q1
            orderby Convert.ToInt32(aa.Number)
            select new GeneralItemDTO
            {
                Id = aa.Id,
                EnglishTitle = aa.EnglishTitle
            }).ToList();
        return Task.FromResult(q);
    }

    internal Task<List<GeneralItemDTO>> GetProvinces(int electionId)
    {
        //throw new NotImplementedException();
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.Structures.OfType<Province>()
            where aa.ElectionId == electionId
            select new GeneralItemDTO
            {
                Id = aa.Id,
                EnglishTitle = aa.EnglishTitle,
                UrduTitle = aa.UrduTitle
            }).ToList();
        return Task.FromResult(q);
    }

    internal Task<List<GeneralItemDTO>> GetUserAssemblyTypes(string userId, int electionId, int phaseId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var uc = (from aa in dc.UserConstituencies
            orderby aa.Structure.Assembly.AssemblyType.EnglishTitle
            where aa.Structure.ElectionId == electionId &&
                  aa.Structure.AssemblyId != null
            select new GeneralItemDTO
            {
                Id = aa.Structure.Assembly.AssemblyTypeId,
                EnglishTitle = aa.Structure.Assembly.AssemblyType.EnglishTitle
            }).Distinct().ToList();

        var op = (from aa in uc
            orderby aa.EnglishTitle
            select aa).ToList();
        return Task.FromResult(op);
    }

    internal Task<int> GetVoteCaste(int? selectedStructureId, int? pollingStationId, int? seatTypeId)
    {
        if (seatTypeId == null)
            return Task.FromResult(0);
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.PSResults
            where aa.Nomination.StructureId == selectedStructureId &&
                  (pollingStationId == null || aa.PollingStationId == pollingStationId) &&
                  aa.Nomination.SeatTypeId == seatTypeId
            select aa.Votes).Sum();

        return Task.FromResult(q);
    }

    internal Task<bool> IsPSWiseResultsExist(int phaseId, int selectedStructureId, int selectedSeatTypeId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.PSResults
            where aa.Nomination.ElectionPhaseId == phaseId &&
                  aa.Nomination.StructureId == selectedStructureId &&
                  aa.Nomination.SeatTypeId == selectedSeatTypeId
            select aa).Any();

        return Task.FromResult(q);
    }

    internal Task<string> MarkViewNamanigarResults(int constituencyId, int seatTypeId, int phaseId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.NamanigarResults
            where aa.Nomination.StructureId == constituencyId &&
                  aa.Nomination.SeatTypeId == seatTypeId &&
                  aa.Nomination.ElectionPhaseId == phaseId
            select aa).ToList();
        foreach (var nr in q)
        {
            nr.IsViewed = true;
            dc.SaveChanges();
        }

        return Task.FromResult("OK");
    }

    internal Task<string> SaveVCNCResults(VCResultDTO obj, int phaseId, string user)
    {
        using var dc = _contextFactory.CreateDbContext();
        using var tran = dc.Database.BeginTransaction();

        // Create Candidate
        var cand = new Candidate
        {
            EnglishName = obj.CandidateName,
            UrduName = obj.CandidateName,
            CandidateType = NominationType.Candidate,
            Gender = Gender.Male,
            IsFresh = true,
            CreatedBy = user,
            CreatedDate = DateTime.Now
        };
        dc.Candidates.Add(cand);
        dc.SaveChanges();


        // Create Nominaion and REsults
        var nom = new Nomination
        {
            CandidteId = cand.Id,
            CreatedBy = user,
            CreatedDate = DateTime.Now,
            ElectionAssemblyId = obj.AssemblyId ?? 0,
            ElectionPhaseId = phaseId,
            IsWinner = true,
            PartyId = obj.PartyId ?? 0,
            SeatTypeId = obj.SeatTypeId ?? 0,
            SymbolId = obj.SymbolId,
            StructureId = obj.ConstituencyId ?? 0,
            Votes = obj.Votes,
            ResultPollingStations = 1
        };
        dc.Nominations.Add(nom);
        dc.SaveChanges();
        tran.Commit();
        return Task.FromResult("Ok");
    }

    private static int GetNextBatchId(ApplicationDbContext dc)
    {
        try
        {
            return (from aa in dc.NamanigarResultLogs
                select aa.BatchId).Max() + 1;
        }
        catch (Exception)
        {
            return 1;
        }
    }

    private static void SaveResultsLog(int changePollingStations, int NewResultPollingStations, string editMode,
        string user, ApplicationDbContext dc, NamanigarResult v, int batchId, DateTime actionDateTime)
    {
        var lg = new NamanigarResultLog
        {
            ActionDateTime = actionDateTime,
            LastActionMode = editMode,
            NamanigarUserId = user,
            NominationId = v.NominationId,
            ResultPollingStations = v.ResultPollingStations,
            ChangePollingStations = v.ChangePollingStations,
            Status = ApprovalStatus.Pending,
            ChangeVotes = v.ChangeVotes,
            Votes = v.Votes,
            BatchId = batchId
        };
        dc.NamanigarResultLogs.Add(lg);
        dc.SaveChanges();
    }

    public Task<string> BlockConstituencyForBB(int constituencyId, int seatTypeId, string userId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var con = dc.Database.GetDbConnection();
        var param = new { ConstituencyId = constituencyId, SeatTypeId = seatTypeId, UserId = userId };
        con.Execute("app.spBlockConstituencyForBB", param,
            commandType: CommandType.StoredProcedure);
        return Task.FromResult("OK");
    }

    public Task<string> ApproveConstituencyForBB(int constituencyId, int seatTypeId, string userId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var con = dc.Database.GetDbConnection();
        var param = new { ConstituencyId = constituencyId, SeatTypeId = seatTypeId, UserId = userId };
        con.Execute("app.spApproveConstituencyForBB", param,
            commandType: CommandType.StoredProcedure);
        return Task.FromResult("OK");
    }

    // function that will return a+b
    public Task<int> Add(int a, int b)
    {
        return Task.FromResult(a + b);
    }

    public Task<List<GeneralItemDTO>> GetPhaseWiseDistricts(int phaseId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var con = dc.Database.GetDbConnection();
        var param = new { PhaseId = phaseId };

        var res = con
            .Query<GeneralItemDTO>("app.GetElectionPhaseDistricts", param, commandType: CommandType.StoredProcedure)
            .ToList();

        return Task.FromResult(res);
    }

    public Task<List<GeneralItemDTO>> GetPunjabByElection22Districts()
    {
        var halkas = new List<int>
        {
            473, 474, 475, 476, 477, 478, 479, 480, 481,
            482, 483, 484, 485, 486, 487, 488, 489, 490,
            491, 492, 479
        };
        var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.Structures.OfType<ProvincialAssemblyHalka>()
                orderby aa.District.EnglishTitle
                where halkas.Contains(aa.Id)
                select new GeneralItemDTO { Id = aa.DistrictId, EnglishTitle = aa.District.EnglishTitle }).Distinct()
            .ToList();
        return Task.FromResult(q);
    }

    public Task<string> GeneratePSs(int constituencyId, int totalPollingStations, string userId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.PollingStations
            where aa.ConstituencyId == constituencyId
            select aa).Any();
        if (q) return Task.FromResult("Polling Station Already Exists");

        var cd = DateTime.Now;

        for (var i = 0; i < totalPollingStations; i++)
        {
            var ps = new PollingStation
            {
                ConstituencyId = constituencyId,
                MaleVoters = 500,
                FemaleVoters = 500,
                CreatedBy = userId,
                CreatedDate = cd,
                EnglishTitle = $"PS {i + 1}",
                Number = $"{i + 1}",
                UrduTitle = $"پی ایس {i + 1}"
            };
            dc.PollingStations.Add(ps);
            dc.SaveChanges();
        }

        var cc = dc.Structures.Find(constituencyId);
        if (cc != null)
        {
            cc.ModifiedBy = userId;
            cc.ModifiedDate = cd;
            cc.TotalPollingStations = totalPollingStations;
            dc.SaveChanges();
        }

        return Task.FromResult("OK");
    }

    public Task<List<ResultDetailDTO>> GetOfflineResults(int? selectedAssemblyId, int? selectedStructureId,
        int? selectedSourceId, int PhaseId)
    {
        var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.Nominations
            where aa.StructureId == selectedStructureId &&
                  aa.ElectionPhaseId == PhaseId &&
                  aa.ElectionAssemblyId == selectedAssemblyId
            select new ResultDetailDTO
            {
                AssemblyId = aa.ElectionAssemblyId,
                CandidateId = aa.CandidteId,
                ElectionId = aa.ElectionAssembly.ElectionId,
                EnglishName = aa.Candidate.EnglishName,
                Id = aa.Id,
                PartyId = aa.PartyId,
                IsNew = false,
                Party = aa.Party.EnglishTitle,
                PartyUrdu = aa.Party.UrduTitle,
                PhaseId = aa.ElectionPhaseId,
                SeatTypeId = aa.SeatTypeId,
                ConstituencyId = aa.StructureId,
                Symbol = aa.Symbol.EnglishTitle,
                SymbolId = aa.SymbolId,
                SymbolUrdu = aa.Symbol.UrduTitle,
                UrduName = aa.Candidate.UrduName,
                Weight = aa.Weight,
                Votes = aa.Votes ?? 0,
                PSVotes = aa.PSResults.Where(k => k.NominationId == aa.Id && k.Votes > 0).Sum(k => k.Votes),
                MaleVoters = aa.Structure.MaleVoters ?? 0,

                FemaleVoters = aa.Structure.FemaleVoters ?? 0,
                //VotersCount = (aa.Structure.MaleVoters ?? 0) + (aa.Structure.FemaleVoters ?? 0),
                TotalPollingStations = 0,
                ResultPollingStations = aa.ResultPollingStations ?? 0,
                ChangeVotes = 0,
                Mode = "Add",
                IsWithdraw = aa.IsWithdraw,
                FemaleVotersPS = aa.Structure.PollingStations.Sum(k => k.FemaleVoters ?? 0),
                MaleVotersPS = aa.Structure.PollingStations.Sum(k => k.MaleVoters ?? 0),
                TotalPollingStationsPS = aa.Structure.PollingStations.Count(),
                PollingStationCompletedPS = aa.PSResults.Select(k => k.PollingStationId).Distinct().Count(),
                IsBilaMokabilaWinner = aa.IsBilaMokabilaWinner
            }).ToList();

        foreach (var cc in q)
        {
            if (cc.FemaleVotersPS + cc.MaleVotersPS > 0)
            {
                double total = cc.FemaleVotersPS + cc.MaleVotersPS;
                double vc = cc.VotersCastePS;
                cc.VoterTurnOutPS = (float)Math.Round(vc / total * 100.0, 2);
            }

            cc.candidateImg =
                !File.Exists(
                    Directory.GetCurrentDirectory() + $"\\wwwroot\\media\\candidates\\{cc.CandidateId}.jpg")
                    ? "/media/candidates/Blank.jpg"
                    : $"/media/candidates/{cc.CandidateId}.jpg";

            cc.flagImg = File.Exists(Directory.GetCurrentDirectory() + $"\\wwwroot\\media\\flags\\{cc.PartyId}.jpg")
                ? $"/media/flags/{cc.PartyId}.jpg"
                : "/media/flags/missing-flag.png";

            cc.symbolImg =
                File.Exists(Directory.GetCurrentDirectory() + $"\\wwwroot\\media\\symbols\\{cc.SymbolId}.jpg")
                    ? $"/media/symbols/{cc.SymbolId}.jpg"
                    : "/media/symbols/missing-symbol.png";
        }


        var ps = (from a in dc.PollingSchemes
            where a.StructureId == selectedStructureId &&
                  a.PhaseId == PhaseId
            select a).FirstOrDefault();
        if (ps != null)
            foreach (var i in q)
            {
                i.TotalPollingStations = ps.TotalPollingStations ?? 0;
                i.MaleVoters = ps.MaleVoters ?? 0;
                i.FemaleVoters = ps.FemaleVoters ?? 0;
            }


        foreach (var rd in q)
        {
            var m = (from a in dc.OfflineResult
                where a.NominationId == rd.Id
                      && a.ResultSourceId == selectedSourceId
                select a).FirstOrDefault();
            if (m != null)
            {
                rd.Votes = m.Votes;
                rd.ResultPollingStations = m.ResPS ?? 0;
            }
            else
            {
                rd.Votes = 0;
                rd.ResultPollingStations = 0;
            }
        }

        return Task.FromResult(q);
    }

    public Task<List<GeneralItemDTO>> GetResultSources()
    {
        var dc = _contextFactory.CreateDbContext();
        var q = (from a in dc.ResultSource
            orderby a.Title
            where a.Id != 4
            select new GeneralItemDTO
            {
                Id = a.Id,
                EnglishTitle = a.Title
            }).ToList();
        return Task.FromResult(q);
    }

#pragma warning disable VSSpell001
    public Task<string> SaveOfflineResults(List<ResultDetailDTO> resultDetail, int resultSourceId, int resultPS,
#pragma warning restore VSSpell001
        string user)
    {
        var dc = _contextFactory.CreateDbContext();
        var logDate = DateTime.Now;

        foreach (var dd in resultDetail)
        {
            var q = (from a in dc.OfflineResult
                where a.NominationId == dd.Id &&
                      a.ResultSourceId == resultSourceId
                select a).FirstOrDefault();

            if (q == null)
            {
                q = new OfflineResult
                {
                    NominationId = dd.Id,
                    ResultSourceId = resultSourceId,
                    Votes = dd.Votes,
                    ResPS = resultPS,
                    CreatedBy = user,
                    CreatedDate = logDate
                };
                dc.OfflineResult.Add(q);
            }
            else
            {
                q.Votes = dd.Votes;
                q.ResPS = resultPS;
                q.ModifiedBy = user;
                q.ModifiedDate = logDate;
            }

            dc.SaveChanges();
        }

        return Task.FromResult("OK");
    }

    public Task<List<OtherSourceResultDTO>> GetOtherSourceLiveResults(int assemblyId, int districtId, int sourceId,
        int phaseId)
    {
        //var rr = new List<ResultDTO>();
        //return Task.FromResult(rr);
        var param = new { assemblyId, districtId, sourceId, phaseId };
        var dc = _contextFactory.CreateDbContext();
        var q = dc.Database.GetDbConnection()
            .Query<OtherSourceResultDTO>("app.GetOtherLiveResults", param, commandType: CommandType.StoredProcedure)
            .ToList();
        return Task.FromResult(q);
    }

    public Task<List<GeneralItemDTO>> GetOtherSources()
    {
        var dc = _contextFactory.CreateDbContext();
        var q = (from a in dc.ResultSource
            orderby a.Title
            select new GeneralItemDTO
            {
                Id = a.Id,
                EnglishTitle = a.Title
            }).ToList();
        q.Insert(0, new GeneralItemDTO { Id = 0, EnglishTitle = "Any Sources" });
        return Task.FromResult(q);
    }

    public Task<List<GeneralItemDTO>> GetOtherSourceDistricts(int PhaseId)
    {
        var dc = _contextFactory.CreateDbContext();
        //int electionId = dc.ElectionPhases.Find(PhaseId)!.ElectionId;
        var electionId = 0;
        var res = dc.ElectionPhases.FirstOrDefault(m => m.Id == PhaseId);
        if (res != null)
        {
            electionId = res.ElectionId;
            var q = (from a in dc.Structures.OfType<District>()
                where a.ElectionId == electionId
                orderby a.Division.Province.EnglishTitle, a.EnglishTitle
                select new GeneralItemDTO
                {
                    Id = a.Id,
                    EnglishTitle = a.Division.Province.EnglishTitle + " - " + a.EnglishTitle
                }).Distinct().ToList();
            q.Insert(0, new GeneralItemDTO { Id = 0, EnglishTitle = "Any District" });
            return Task.FromResult(q);
        }

        return Task.FromResult(new List<GeneralItemDTO>());
    }

    public Task<List<GeneralItemDTO>> GetOtherSourceAssemblies(int PhaseId)
    {
        var dc = _contextFactory.CreateDbContext();

        var q = (from a in dc.Nominations
            orderby a.ElectionAssembly.EnglishTitle
            where a.ElectionPhaseId == PhaseId
            select new GeneralItemDTO
            {
                Id = a.ElectionAssemblyId,
                EnglishTitle = a.ElectionAssembly.EnglishTitle
            }).Distinct().ToList();
        q.Insert(0, new GeneralItemDTO { Id = 0, EnglishTitle = "Any Assembly" });
        return Task.FromResult(q);
    }

    public Task<ResultMode> GetConstituencyResultMode(int? selectedStructureId, int phaseId)
    {
        var dc = _contextFactory.CreateDbContext();
        var q = (from a in dc.PollingSchemes
            where a.StructureId == selectedStructureId && a.PhaseId == phaseId
            select a).FirstOrDefault();
        if (q == null) return Task.FromResult(ResultMode.Default);

        return Task.FromResult(q.ResultMode);
    }

    public Task<ResultMode> DisablePSPosting(int? selectedStructureId, int phaseId, ResultMode currentResultMode,
        string userId)
    {
        var dc = _contextFactory.CreateDbContext();
        var ps = (from a in dc.PollingSchemes
                where a.StructureId == selectedStructureId &&
                      a.PhaseId == phaseId && (a.ResultMode == ResultMode.PollingStationWise ||
                                               a.ResultMode == ResultMode.Default)
                select a
            ).FirstOrDefault();
        if (ps == null) return Task.FromResult(currentResultMode);

        ps.ResultMode = ResultMode.Lumsum;
        ps.ResultModeChangedBy = userId;
        ps.ResultModeChangedDate = DateTime.Now;
        dc.SaveChanges();
        return Task.FromResult(ResultMode.Lumsum);
    }

    public Task<ResultMode> EnablePSPosting(int? selectedStructureId, int phaseId, ResultMode currentResultMode,
        string userId)
    {
        var dc = _contextFactory.CreateDbContext();
        var ps = (from a in dc.PollingSchemes
                where a.StructureId == selectedStructureId &&
                      a.PhaseId == phaseId && a.ResultMode != ResultMode.PollingStationWise
                select a
            ).FirstOrDefault();
        if (ps == null) return Task.FromResult(currentResultMode);

        ps.ResultMode = ResultMode.PollingStationWise;
        ps.ResultModeChangedBy = userId;
        ps.ResultModeChangedDate = DateTime.Now;
        dc.SaveChanges();
        return Task.FromResult(ResultMode.PollingStationWise);
    }

    public Task<List<GeneralItemDTO>> GetAllProvincs(int phaseId)
    {
        var dc = _contextFactory.CreateDbContext();

        var electionId = (from a in dc.ElectionPhases
            where a.Id == phaseId
            select a.ElectionId).FirstOrDefault();

        var op = (from a in dc.Structures.OfType<Province>()
            where a.ElectionId == electionId
            orderby a.EnglishTitle
            select new GeneralItemDTO
            {
                Id = a.Id,
                EnglishTitle = a.EnglishTitle,
                UrduTitle = a.UrduTitle
            }).ToList();
        return Task.FromResult(op);
    }


    internal Task<List<GeneralItemDTO>> GetAllDistrics(int provinceId)
    {
        var dc = _contextFactory.CreateDbContext();
        var q = (from a in dc.Structures.OfType<District>()
            where a.Division.ProvinceId == provinceId
            orderby a.EnglishTitle
            select new GeneralItemDTO
            {
                Id = a.Id,
                EnglishTitle = a.EnglishTitle,
                UrduTitle = a.UrduTitle
            }).ToList();

        return Task.FromResult(q);
    }

    internal Task<List<GeneralItemDTO>> GetAllAssemblies(int phaseId)
    {
        var dc = _contextFactory.CreateDbContext();
        var electionId = (from a in dc.ElectionPhases
            where a.Id == phaseId
            select a.ElectionId).FirstOrDefault();

        var op = (from a in dc.ElectionAssemblies
            where a.ElectionId == electionId
            orderby a.EnglishTitle
            select new GeneralItemDTO
            {
                Id = a.Id,
                EnglishTitle = a.EnglishTitle,
                UrduTitle = a.UrduTitle
            }).ToList();
        return Task.FromResult(op);
    }

    public Task<string> MarkTickerAsPublish(int constituencyId, int phaseId)
    {
        var dc = _contextFactory.CreateDbContext();
        var res = (from a in dc.LiveResults
                where a.ConstituencyId == constituencyId &&
                      a.PhaseId == phaseId
                select a
            ).FirstOrDefault();
        if (res != null)
        {
            res.IsTickerPublish = true;
            dc.SaveChanges();
            return Task.FromResult("OK");
        }

        return Task.FromResult("Record not found");
    }
}