using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ElectionAppServer.Data;
using ElectionAppServer.Model;
using Microsoft.EntityFrameworkCore;

public class ProblemService
{
    private readonly IDbContextFactory<ApplicationDbContext> _contextFactory;

    public ProblemService(IDbContextFactory<ApplicationDbContext> contextFactory)
    {
        _contextFactory = contextFactory;
    }

    public Task<Problem> CreateProblem(Problem obj)
    {
        using var dc = _contextFactory.CreateDbContext();
        
        var isExist = dc.Problems.Any(c => c.Title == obj.Title.Trim());
        if (isExist) throw new Exception("Title already exists");
        
        dc.Problems.Add(obj);
        dc.SaveChanges();
        return Task.FromResult(obj);
    }

    public async Task<Problem> DeleteProblem(int id)
    {
        await using var dc = await _contextFactory.CreateDbContextAsync();
        var st = await (from aa in dc.Problems
            where aa.Id == id
            select aa).FirstOrDefaultAsync();
        
        if (st == null) throw new Exception("Record not found");
        
        dc.Problems.Remove(st);
        await dc.SaveChangesAsync();
        return st;
    }

    public Task<List<Problem>> GetList()
    {
        using var dc = _contextFactory.CreateDbContext();
        var res = dc.Problems.OrderBy(c => c.Title).ToList();
        return Task.FromResult(res);
    }

    public Task<Problem> UpdateProblem(Problem obj)
    {
        using var dc = _contextFactory.CreateDbContext();
        
        var isExist = dc.Problems.Any(c => c.Title == obj.Title.Trim() && c.Id != obj.Id);
        if (isExist) throw new Exception("Title already exists");
        
        var st = (from aa in dc.Problems
            where aa.Id == obj.Id
            select aa).FirstOrDefault();
            
        if (st != null)
        {
            st.Title = obj.Title.Trim();
            st.ModifiedBy = obj.ModifiedBy;
            st.ModifiedDate = DateTime.Now;
        }

        dc.SaveChanges();
        return Task.FromResult(obj);
    }
}