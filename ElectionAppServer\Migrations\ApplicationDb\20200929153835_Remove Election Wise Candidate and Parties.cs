﻿using Microsoft.EntityFrameworkCore.Migrations;
using System;

namespace ElectionAppServer.Migrations.ApplicationDb
{
	public partial class RemoveElectionWiseCandidateandParties : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropTable(
				 name: "ElectionCandidates");

			migrationBuilder.DropTable(
				 name: "ElectionParties");
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.CreateTable(
				 name: "ElectionCandidates",
				 columns: table => new
				 {
					 CandidateId = table.Column<int>(type: "int", nullable: false),
					 ElectionId = table.Column<int>(type: "int", nullable: false),
					 CreatedBy = table.Column<string>(type: "varchar(430)", unicode: false, maxLength: 430, nullable: false),
					 CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValue: new DateTime(2020, 9, 17, 18, 37, 13, 557, DateTimeKind.Local).AddTicks(1983))
				 },
				 constraints: table =>
				 {
					 table.PrimaryKey("PK_ElectionCandidates", x => new { x.CandidateId, x.ElectionId });
					 table.ForeignKey(
							  name: "FK_ElectionCandidates_Candidates_CandidateId",
							  column: x => x.CandidateId,
							  principalTable: "Candidates",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Restrict);
					 table.ForeignKey(
							  name: "FK_ElectionCandidates_Elections_ElectionId",
							  column: x => x.ElectionId,
							  principalTable: "Elections",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Restrict);
				 });

			migrationBuilder.CreateTable(
				 name: "ElectionParties",
				 columns: table => new
				 {
					 PartyId = table.Column<int>(type: "int", nullable: false),
					 ElectionId = table.Column<int>(type: "int", nullable: false),
					 CreatedBy = table.Column<string>(type: "varchar(430)", unicode: false, maxLength: 430, nullable: false),
					 CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValue: new DateTime(2020, 9, 17, 18, 37, 13, 552, DateTimeKind.Local).AddTicks(3319))
				 },
				 constraints: table =>
				 {
					 table.PrimaryKey("PK_ElectionParties", x => new { x.PartyId, x.ElectionId });
					 table.ForeignKey(
							  name: "FK_ElectionParties_Elections_ElectionId",
							  column: x => x.ElectionId,
							  principalTable: "Elections",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Restrict);
					 table.ForeignKey(
							  name: "FK_ElectionParties_Parties_PartyId",
							  column: x => x.PartyId,
							  principalTable: "Parties",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Restrict);
				 });

			migrationBuilder.CreateIndex(
				 name: "IX_ElectionCandidates_ElectionId",
				 table: "ElectionCandidates",
				 column: "ElectionId");

			migrationBuilder.CreateIndex(
				 name: "IX_ElectionParties_ElectionId",
				 table: "ElectionParties",
				 column: "ElectionId");
		}
	}
}
