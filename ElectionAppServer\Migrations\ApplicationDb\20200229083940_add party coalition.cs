﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace ElectionAppServer.Migrations.ApplicationDb
{
	public partial class addpartycoalition : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropForeignKey(
				 name: "FK_PartyCoalition_Coalitions_CoalitionId",
				 table: "PartyCoalition");

			migrationBuilder.DropForeignKey(
				 name: "FK_PartyCoalition_Elections_ElectionId",
				 table: "PartyCoalition");

			migrationBuilder.DropForeignKey(
				 name: "FK_PartyCoalition_Parties_PartyId",
				 table: "PartyCoalition");

			migrationBuilder.DropPrimaryKey(
				 name: "PK_PartyCoalition",
				 table: "PartyCoalition");

			migrationBuilder.RenameTable(
				 name: "PartyCoalition",
				 newName: "PartyCoalitions");

			migrationBuilder.RenameIndex(
				 name: "IX_PartyCoalition_ElectionId",
				 table: "PartyCoalitions",
				 newName: "IX_PartyCoalitions_ElectionId");

			migrationBuilder.RenameIndex(
				 name: "IX_PartyCoalition_CoalitionId",
				 table: "PartyCoalitions",
				 newName: "IX_PartyCoalitions_CoalitionId");

			migrationBuilder.AddPrimaryKey(
				 name: "PK_PartyCoalitions",
				 table: "PartyCoalitions",
				 columns: new[] { "PartyId", "CoalitionId", "ElectionId" });

			migrationBuilder.AddForeignKey(
				 name: "FK_PartyCoalitions_Coalitions_CoalitionId",
				 table: "PartyCoalitions",
				 column: "CoalitionId",
				 principalTable: "Coalitions",
				 principalColumn: "Id",
				 onDelete: ReferentialAction.Restrict);

			migrationBuilder.AddForeignKey(
				 name: "FK_PartyCoalitions_Elections_ElectionId",
				 table: "PartyCoalitions",
				 column: "ElectionId",
				 principalTable: "Elections",
				 principalColumn: "Id",
				 onDelete: ReferentialAction.Restrict);

			migrationBuilder.AddForeignKey(
				 name: "FK_PartyCoalitions_Parties_PartyId",
				 table: "PartyCoalitions",
				 column: "PartyId",
				 principalTable: "Parties",
				 principalColumn: "Id",
				 onDelete: ReferentialAction.Restrict);
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropForeignKey(
				 name: "FK_PartyCoalitions_Coalitions_CoalitionId",
				 table: "PartyCoalitions");

			migrationBuilder.DropForeignKey(
				 name: "FK_PartyCoalitions_Elections_ElectionId",
				 table: "PartyCoalitions");

			migrationBuilder.DropForeignKey(
				 name: "FK_PartyCoalitions_Parties_PartyId",
				 table: "PartyCoalitions");

			migrationBuilder.DropPrimaryKey(
				 name: "PK_PartyCoalitions",
				 table: "PartyCoalitions");

			migrationBuilder.RenameTable(
				 name: "PartyCoalitions",
				 newName: "PartyCoalition");

			migrationBuilder.RenameIndex(
				 name: "IX_PartyCoalitions_ElectionId",
				 table: "PartyCoalition",
				 newName: "IX_PartyCoalition_ElectionId");

			migrationBuilder.RenameIndex(
				 name: "IX_PartyCoalitions_CoalitionId",
				 table: "PartyCoalition",
				 newName: "IX_PartyCoalition_CoalitionId");

			migrationBuilder.AddPrimaryKey(
				 name: "PK_PartyCoalition",
				 table: "PartyCoalition",
				 columns: new[] { "PartyId", "CoalitionId", "ElectionId" });

			migrationBuilder.AddForeignKey(
				 name: "FK_PartyCoalition_Coalitions_CoalitionId",
				 table: "PartyCoalition",
				 column: "CoalitionId",
				 principalTable: "Coalitions",
				 principalColumn: "Id",
				 onDelete: ReferentialAction.Restrict);

			migrationBuilder.AddForeignKey(
				 name: "FK_PartyCoalition_Elections_ElectionId",
				 table: "PartyCoalition",
				 column: "ElectionId",
				 principalTable: "Elections",
				 principalColumn: "Id",
				 onDelete: ReferentialAction.Restrict);

			migrationBuilder.AddForeignKey(
				 name: "FK_PartyCoalition_Parties_PartyId",
				 table: "PartyCoalition",
				 column: "PartyId",
				 principalTable: "Parties",
				 principalColumn: "Id",
				 onDelete: ReferentialAction.Restrict);
		}
	}
}
