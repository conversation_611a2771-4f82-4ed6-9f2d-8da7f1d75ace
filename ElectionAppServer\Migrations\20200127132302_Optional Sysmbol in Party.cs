﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace ElectionAppServer.Migrations
{
	public partial class OptionalSysmbolinParty : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropForeignKey(
				 name: "FK_Parties_Symbols_SymbolId",
				 table: "Parties");

			migrationBuilder.AlterColumn<int>(
				 name: "SymbolId",
				 table: "Parties",
				 nullable: true,
				 oldClrType: typeof(int),
				 oldType: "int");

			migrationBuilder.AddForeignKey(
				 name: "FK_Parties_Symbols_SymbolId",
				 table: "Parties",
				 column: "SymbolId",
				 principalTable: "Symbols",
				 principalColumn: "Id",
				 onDelete: ReferentialAction.Restrict);
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropForeignKey(
				 name: "FK_Parties_Symbols_SymbolId",
				 table: "Parties");

			migrationBuilder.AlterColumn<int>(
				 name: "SymbolId",
				 table: "Parties",
				 type: "int",
				 nullable: false,
				 oldClrType: typeof(int),
				 oldNullable: true);

			migrationBuilder.AddForeignKey(
				 name: "FK_Parties_Symbols_SymbolId",
				 table: "Parties",
				 column: "SymbolId",
				 principalTable: "Symbols",
				 principalColumn: "Id",
				 onDelete: ReferentialAction.Cascade);
		}
	}
}
