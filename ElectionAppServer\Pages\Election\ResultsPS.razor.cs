﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ElectionAppServer.DTO;
using ElectionAppServer.Model;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.SignalR.Client;
using Syncfusion.Blazor.DropDowns;
using Syncfusion.Blazor.Notifications;

namespace ElectionAppServer.Pages.Election;

public partial class ResultsPS
{
    private ResultMode _ResultMode = ResultMode.Default;
    private HubConnection hubConnection;
    private int? PollingStationId;
    private List<GeneralItemDTO> pollingStationList;
    private SfToast ToastObj;
    public List<GeneralItemDTO> assmblieslist { get; set; }
    public int ChangePollingStations { get; set; } = 0;
    public List<GeneralItemDTO> constituencylist { get; set; }
    public int? FemaleVoters { get; set; }
    public bool IsConnected => hubConnection.State == HubConnectionState.Connected;
    public int? MaleVoters { get; set; }
    public List<NamanigarDTO> namanigarList { get; set; }

    //public int NewResPollingStations
    //{
    //  get
    //  {
    //    if (EditMode == "Add")
    //    {
    //      return result_detail[0].ResultPollingStations + ChangePollingStations;
    //    }
    //    else if (EditMode == "Update")
    //    {
    //      return ChangePollingStations;
    //    }
    //    return result_detail[0].ResultPollingStations;
    //  }
    //}
    //public int NewResPollingStations { get; set; }
    public List<ResultDetailDTO> result_detail { get; set; } = new();

    public List<GeneralItemDTO> seatypelist { get; set; }
    public int? SelectedAssemblyId { get; set; }
    public string SelectedAssemblyStrId { get; set; }
    public int? selectedNamanigarId { get; set; }
    public int? SelectedSeatTypeId { get; set; }
    public int? SelectedStructureId { get; set; }
    public List<ItemDTO> strAssembliesList { get; set; }
    public int TotalPollingStations { get; set; }

    public int TotalVoters => (MaleVoters ?? 0) + (FemaleVoters ?? 0);

    private bool IfVoterExced
    {
        get
        {
            try
            {
                return result_detail.Sum(k => k.PSVotes) > TotalVoters;
            }
            catch (Exception)
            {
                return true;
            }
        }
    }

    public float? TurnOutPer
    {
        get
        {
            if (TotalVoters == 0)
                return null;
            var res = (float?)(VoteCaste / (float)TotalVoters * 100.0);
            if (res != null)
            {
                var op = Math.Round((decimal)res, 3);
                return (float?)op;
            }

            return res;
        }
    }

    public string UserId { get; set; }
    public int VoteCaste { get; set; }

    [CascadingParameter] private Task<AuthenticationState> authenticationStateTask { get; set; }

    //private async Task OpenForm(string mode)
    //{
    //  if (ChangePollingStations <= 0)
    //  {
    //    await JSRuntime.InvokeVoidAsync("alert", new string[] { "Please enter polling stations" });
    //    EditMode = "";
    //    mode = "";
    //    return;
    //  }
    //  if (mode == "Add")
    //  {
    //    if (ChangePollingStations + result_detail[0].ResultPollingStations > result_detail[0].TotalPollingStations)
    //    {
    //      await JSRuntime.InvokeVoidAsync("alert", new string[] { "The result of polling stations count is exceeding against total polling stations." });
    //      mode = "";
    //      EditMode = "";
    //      return;
    //    }
    //  }
    //  //if (ChangePollingStations <= 0)
    //  //{
    //  //	await JSRuntime.InvokeVoidAsync("alert", new string[] { "Please enter polling stations" });
    //  //	return;
    //  //}
    //  EditMode = mode;
    //  //ChangePollingStations = 0;
    //  //await dlgForm.ShowAsync();
    //  //if (result_detail.Count > 0)
    //  //{
    //  //	if (mode == "Add")
    //  //	{
    //  //		ChangePollingStations = 0;
    //  //	}
    //  //	else
    //  //	{
    //  //		ChangePollingStations = result_detail[0].ResultPollingStations;
    //  //	}
    //  //}
    //  foreach (var res in result_detail)
    //  {
    //    res.Mode = mode;
    //    if (mode == "Add")
    //    {
    //      res.ChangeVotes = 0;
    //    }
    //    else
    //    {
    //      res.ChangeVotes = res.Votes;
    //    }
    //  }
    //  StateHasChanged();
    //  await Task.CompletedTask;
    //}
    //public async ValueTask DisposeAsync()
    //{
    //}

    //public async Task CancelRecord()
    //{
    //  ChangePollingStations = 0;
    //  result_detail = await Service.GetElectionResults((int)SelectedAssemblyId, state.state.PhaseId, (int)SelectedStructureId, (int)SelectedSeatTypeId);
    //  StateHasChanged();
    //  await Task.CompletedTask;
    //}
    public async Task FillDDLs()
    {
        assmblieslist = await Service.GetUserAssemblies(UserId, state.state.ElectionId, state.state.PhaseId);
        strAssembliesList = (
            from aa in assmblieslist
            orderby aa.EnglishTitle
            select new ItemDTO { Id = $"{aa.Id}", Title = aa.EnglishTitle }).ToList();
        if (assmblieslist.Count == 1)
        {
            SelectedAssemblyId = assmblieslist[0].Id;
            SelectedAssemblyStrId = SelectedAssemblyId.ToString();
            seatypelist = await Service.GetAssemblySeatTypes((int)SelectedAssemblyId);
            constituencylist = await Service.GetUsersConstituencies(UserId, state.state.ElectionId,
                (int)SelectedAssemblyId, state.state.PhaseId);
            if (seatypelist.Count == 1) SelectedSeatTypeId = seatypelist[0].Id;
        }
        //StateHasChanged();
    }

    public async Task FillResult()
    {
        VoteCaste = 0;
        if (SelectedAssemblyId != null && SelectedStructureId != null && SelectedSeatTypeId != null)
        {
            result_detail = await Service.GetElectionResults((int)SelectedAssemblyId, state.state.PhaseId,
                (int)SelectedStructureId, (int)SelectedSeatTypeId, PollingStationId);
            if (PollingStationId != null)
                VoteCaste = await Service.GetPSVotesCast((int)PollingStationId, state.state.PhaseId);
        }
        else
        {
            result_detail = new List<ResultDetailDTO>();
        }
    }

    public async Task OnAssemblyChange(ChangeEventArgs<int?, GeneralItemDTO> args)
    {
        SelectedAssemblyId = args.Value;
        SelectedStructureId = null;
        namanigarList = null;
        StateHasChanged();
        if (SelectedAssemblyId == null)
        {
            constituencylist = null;
            seatypelist = null;
            selectedNamanigarId = null;
            SelectedSeatTypeId = null;
        }
        else
        {
            constituencylist = await Service.GetUsersConstituencies(UserId, state.state.ElectionId,
                (int)SelectedAssemblyId, state.state.PhaseId);
            seatypelist = await Service.GetAssemblySeatTypes((int)SelectedAssemblyId);

            if (seatypelist.Any())
            {
                if (seatypelist.Count == 1)
                {
                    SelectedSeatTypeId = seatypelist[0].Id;
                }
                else
                {
                    if (!seatypelist.Any(k => k.Id == SelectedSeatTypeId)) SelectedSeatTypeId = null;
                }
            }
            else
            {
                SelectedSeatTypeId = null;
            }
        }
    }

    public async Task OnConstChange(ChangeEventArgs<int?, GeneralItemDTO> args)
    {
        SelectedStructureId = args.Value;
        selectedNamanigarId = null;
        if (SelectedStructureId != null)
        {
            _ResultMode = await Service.GetConstituencyResultMode(SelectedStructureId, state.state.PhaseId);
            namanigarList = await Service.GetConstituencyNamanigars((int)SelectedStructureId, state.state.PhaseId);
            // Most Important!!!! Will be remove after testing...
            if (namanigarList == null || namanigarList.Count == 0)
                namanigarList = new List<NamanigarDTO>
                {
                    new()
                    {
                        Code = "1", Id = 1, Name = "Joker", District = "All", Email = "<EMAIL>", Phone = "123"
                    }
                };
            pollingStationList = await Service.GetPollingStations((int)SelectedStructureId);
            await FillResult();
        }
        else
        {
            result_detail = null;
            namanigarList = null;
        }
    }

    //public async Task OnAssemblyChange(Syncfusion.Blazor.DropDowns.ChangeEventArgs<int?> args)
    public async Task OnPollingStationChange(ChangeEventArgs<int?, GeneralItemDTO> args)
    {
        _ResultMode = await Service.GetConstituencyResultMode(SelectedStructureId, state.state.PhaseId);
        PollingStationId = args.Value;
        await FillConstituencyInfo();
        await FillResult();
        await Task.CompletedTask;
    }

    public async Task OnSeatTypChange(ChangeEventArgs<int?, GeneralItemDTO> args)
    {
        PollingStationId = null;
        SelectedSeatTypeId = args.Value;
        await FillResult();
        await FillConstituencyInfo();
    }

    public async Task DisablePSResultPosting()
    {
        var user = (await authenticationStateTask).User;
        //if (user.Identity.Name == "<EMAIL>" || user.Identity.Name == "<EMAIL>" || user.Identity.Name == "<EMAIL>")
        //{

        var res = await DialogService.ConfirmAsync("Are you sure want to LOCK PS Result Posting?", "Confirm");
        if (res)
        {
            _ResultMode = await Service.DisablePSPosting(SelectedStructureId, state.state.PhaseId, _ResultMode,
                user.Identity.Name);
            StateHasChanged();
        }
        //}
        //else
        //{
        //    await DialogService.AlertAsync("You are not authorized to perform this action", "Error");
        //}
    }

    public async Task EnablePSResultPosting()
    {
        var user = (await authenticationStateTask).User;
        if (user.Identity.Name == "<EMAIL>" || user.Identity.Name == "<EMAIL>" ||
            user.Identity.Name == "<EMAIL>")
        {
            var res = await DialogService.ConfirmAsync("Are you sure want to Unlock PS Result Posting?", "Confirm");
            if (res)
            {
                _ResultMode = await Service.EnablePSPosting(SelectedStructureId, state.state.PhaseId, _ResultMode,
                    user.Identity.Name);
                StateHasChanged();
            }
        }
        else
        {
            await DialogService.AlertAsync("You are not authorized to perform this action", "Error");
        }
    }


    public async Task SaveRecord()
    {
        _ResultMode = await Service.GetConstituencyResultMode(SelectedStructureId, state.state.PhaseId);

        if (_ResultMode == ResultMode.Default || _ResultMode == ResultMode.PollingStationWise)
        {
            var user = (await authenticationStateTask).User;
            var op = await Service.SaveResultsPS(result_detail, (int)PollingStationId, user.Identity.Name,
                (int)SelectedStructureId, (int)SelectedSeatTypeId, state.state.ElectionId, (int)SelectedAssemblyId,
                state.state.PhaseId, VoteCaste);
            op.Status = "Unpublished";
            await FillResult();
            var tm = new ToastModel
            {
                Content = "Record saved", Title = "Results", CssClass = "e-toast-success", ShowProgressBar = true,
                Timeout = 3000
            };
            var ticker = await Service.GetLiveResultDetailLB(state.state.ElectionId, (int)SelectedAssemblyId,
                state.state.PhaseId, (int)SelectedStructureId, (int)SelectedSeatTypeId);
            op.Ticker = ticker;
            await hubConnection.SendAsync("SendResult", op);
            await ToastObj.ShowAsync(tm);
        }
    }

    //public async Task SaveRecord()
    //{
    //  var user = (await authenticationStateTask).User;
    //  try
    //  {
    //    //await Service.SaveResults(result_detail, ChangePollingStations, EditMode, user.Identity.Name, (int)selectedNamanigarId);
    //    var r = await Service.SaveResults(result_detail, (int)SelectedAssemblyId, state.state.PhaseId, (int)SelectedStructureId, (int)SelectedSeatTypeId, ChangePollingStations, EditMode, user.Identity.Name, (int)selectedNamanigarId);
    //    r.PhaseId = state.state.PhaseId;
    //    r.OnAir = false;
    //    r.Status = "No";
    //    result_detail = await Service.GetElectionResults((int)SelectedAssemblyId, state.state.PhaseId, (int)SelectedStructureId, (int)SelectedSeatTypeId);
    //    EditMode = "";
    //    ChangePollingStations = 0;
    //    StateHasChanged();
    //  }
    //  catch (Exception ex)
    //  {
    //    var mm = new ToastModel { Title = "Error", Content = ex.Message };
    //    await ToastObj.ShowAsync(mm);
    //  }
    //  //EditMode = "";
    //  //await Task.CompletedTask;
    //}
    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        //return base.OnAfterRenderAsync(firstRender);
        if (firstRender)
        {
            var user = (await authenticationStateTask).User;
            if (user.Identity.IsAuthenticated)
            {
                //ElectionsList = await Service.GetAll();
                //var dd =  await ProtectedSessionStore.GetAsync<ElectionStateDTO>("election");
                var dd = await localStorage.GetAsync<ElectionStateDTO>(await confreader.GetSettingValue("electionkey"));
                state.SetState(dd.Value);
                var info = await _UserManager.GetUserAsync(user);
                UserId = info.Id;
                await Task.CompletedTask;
                await FillDDLs();
                StateHasChanged();
            }
            else
            {
                try
                {
                    //await  ProtectedSessionStore.DeleteAsync("election");
                    await localStorage.DeleteAsync(await confreader.GetSettingValue("electionkey"));
                    state.SetState(new ElectionStateDTO
                        { ElectionId = 0, ElectionTitle = "", PhaseId = 0, PhaseTitle = "" });
                    StateHasChanged();
                }
                catch (Exception)
                {
                    // ignored
                }
            }
        }
    }

    protected override async Task OnInitializedAsync()
    {
        hubConnection = new HubConnectionBuilder().WithUrl(NavigationManager.ToAbsoluteUri("/resultshub")).Build();
        await hubConnection.StartAsync();
        await Task.CompletedTask;
    }

    protected override async Task OnParametersSetAsync()
    {
        if (SelectedAssemblyId != null && SelectedStructureId != null && SelectedSeatTypeId != null)
        {
            // test
            assmblieslist = await Service.GetUserAssemblies(UserId, state.state.ElectionId, state.state.PhaseId);
            strAssembliesList = (
                from aa in assmblieslist
                select new ItemDTO { Id = $"{aa.Id}", Title = aa.EnglishTitle }).ToList();
            seatypelist = await Service.GetAssemblySeatTypes((int)SelectedAssemblyId);
            constituencylist = await Service.GetUsersConstituencies(UserId, state.state.ElectionId,
                (int)SelectedAssemblyId, state.state.PhaseId);
            result_detail = await Service.GetElectionResults((int)SelectedAssemblyId, state.state.PhaseId,
                (int)SelectedStructureId, (int)SelectedSeatTypeId, PollingStationId);
        }
    }

    private async Task FillConstituencyInfo()
    {
        if (SelectedStructureId != null)
        {
            MaleVoters = await Service.GetMaleVoters(SelectedStructureId, PollingStationId);
            FemaleVoters = await Service.GetFemaleVoters(SelectedStructureId, PollingStationId);
            TotalPollingStations = await Service.GetPollingStationCount(SelectedStructureId, PollingStationId);
            VoteCaste = await Service.GetVoteCaste(SelectedStructureId, PollingStationId, SelectedSeatTypeId);
        }
        else
        {
            MaleVoters = 0;
            FemaleVoters = 0;
            TotalPollingStations = 0;
            VoteCaste = 0;
        }
    }

    private async Task GeneratePS()
    {
        var user = (await authenticationStateTask).User;
        var res = await DialogService.PromptAsync("How many Polling stations want to generate?",
            "Polling station count");
        var ps = int.TryParse(res, out var parsedValue) ? parsedValue : 0;

        if (ps > 0 && user.Identity != null)
        {
            var op = await Service.GeneratePSs(SelectedStructureId ?? 0, ps, user.Identity.Name);
            if (op == "OK") pollingStationList = await Service.GetPollingStations(SelectedStructureId ?? 0);
        }
    }
}