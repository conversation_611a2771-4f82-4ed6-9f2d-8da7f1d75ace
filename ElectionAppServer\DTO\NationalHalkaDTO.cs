﻿using System.ComponentModel.DataAnnotations;

namespace ElectionAppServer.DTO;

public class NationalHalkaDTO
{
    public int Id { get; set; }

    [Required] [StringLength(5)] public string Code { get; set; }

    [Required] [StringLength(200)] public string EnglishTitle { get; set; }

    [Required] [StringLength(200)] public string UrduTitle { get; set; }
}

public class ProvincialHalkaDTO
{
    public int Id { get; set; }

    [Required] [StringLength(5)] public string Code { get; set; }

    [Required] [StringLength(200)] public string EnglishTitle { get; set; }

    [Required] [StringLength(200)] public string UrduTitle { get; set; }
}