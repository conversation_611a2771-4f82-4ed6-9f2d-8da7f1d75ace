﻿using System;

namespace ElectionAppServer.DTO;

public class ResultDTO
{
    public string Source { get; set; }
    public int AssemblyId { get; set; }
    public int ElectionId { get; set; }
    public int PhaseId { get; set; }
    public int ConstituencyId { get; set; }
    public int SeatTypeId { get; set; }

    public int PollingStationId { get; set; }
    public string PollingStation { get; set; }
    public string Assembly { get; set; }
    public string Election { get; set; }
    public string Phase { get; set; }
    public string Constituency { get; set; }
    public string SeatType { get; set; }

    public int TotalPollingStations { get; set; }
    public int ResultOfPollingStations { get; set; }
    public int ProvinceId { get; set; }
    public int DistrictId { get; set; }
    public string District { get; set; }
    public string WinnerKaKee { get; set; }
    public string RunnerUpKaKee { get; set; }

    public double PercentageCompletedPollingStations
    {
        get
        {
            if (TotalPollingStations == 0)
                return 0.0;
            if (ResultOfPollingStations == 0)
                return 0.0;
            return ResultOfPollingStations / (double)TotalPollingStations * 100.0;
        }
    }

    public int? WinnerCandidateId { get; set; }
    public int WinnerVotes { get; set; }
    public string WinnerParty { get; set; }
    public string WinnerName { get; set; }
    public int? RunnerUpCandidateId { get; set; }
    public int RunnerUpVotes { get; set; }
    public string RunnerUpParty { get; set; }
    public string RunnerUpName { get; set; }

    public int LeadByVotes => WinnerVotes - RunnerUpVotes;

    public DateTime LastUpdatedOn { get; set; }
    public string Status { get; set; }
    public int TotalVoters { get; internal set; }
    public bool OnAir { get; set; }

    public string Ticker { get; set; }
    public string PSNumberStr { get; internal set; }
    public int PSNumber { get; internal set; }
    public int AssemblyTypeId { get; internal set; }
    public string BBStatus { get; set; }
    public DateTime LastUpdate { get; set; }
}