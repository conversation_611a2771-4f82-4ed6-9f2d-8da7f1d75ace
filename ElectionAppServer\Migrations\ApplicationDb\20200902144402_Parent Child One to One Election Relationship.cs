﻿using Microsoft.EntityFrameworkCore.Migrations;
using System;

namespace ElectionAppServer.Migrations.ApplicationDb
{
	public partial class ParentChildOnetoOneElectionRelationship : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.AddColumn<int>(
				 name: "ParentElectionId",
				 table: "Elections",
				 nullable: true);

			migrationBuilder.AlterColumn<DateTime>(
				 name: "CreatedDate",
				 table: "ElectionParties",
				 nullable: false,
				 defaultValue: new DateTime(2020, 9, 2, 19, 44, 2, 312, DateTimeKind.Local).AddTicks(9430),
				 oldClrType: typeof(DateTime),
				 oldType: "datetime2",
				 oldDefaultValue: new DateTime(2020, 9, 2, 16, 45, 57, 510, DateTimeKind.Local).AddTicks(8880));

			migrationBuilder.AlterColumn<DateTime>(
				 name: "CreatedDate",
				 table: "ElectionCandidates",
				 nullable: false,
				 defaultValue: new DateTime(2020, 9, 2, 19, 44, 2, 318, DateTimeKind.Local).AddTicks(2256),
				 oldClrType: typeof(DateTime),
				 oldType: "datetime2",
				 oldDefaultValue: new DateTime(2020, 9, 2, 16, 45, 57, 516, DateTimeKind.Local).AddTicks(1274));

			migrationBuilder.CreateIndex(
				 name: "IX_Elections_ParentElectionId",
				 table: "Elections",
				 column: "ParentElectionId",
				 unique: true,
				 filter: "[ParentElectionId] IS NOT NULL");

			migrationBuilder.AddForeignKey(
				 name: "FK_Elections_Elections_ParentElectionId",
				 table: "Elections",
				 column: "ParentElectionId",
				 principalTable: "Elections",
				 principalColumn: "Id",
				 onDelete: ReferentialAction.Restrict);
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropForeignKey(
				 name: "FK_Elections_Elections_ParentElectionId",
				 table: "Elections");

			migrationBuilder.DropIndex(
				 name: "IX_Elections_ParentElectionId",
				 table: "Elections");

			migrationBuilder.DropColumn(
				 name: "ParentElectionId",
				 table: "Elections");

			migrationBuilder.AlterColumn<DateTime>(
				 name: "CreatedDate",
				 table: "ElectionParties",
				 type: "datetime2",
				 nullable: false,
				 defaultValue: new DateTime(2020, 9, 2, 16, 45, 57, 510, DateTimeKind.Local).AddTicks(8880),
				 oldClrType: typeof(DateTime),
				 oldDefaultValue: new DateTime(2020, 9, 2, 19, 44, 2, 312, DateTimeKind.Local).AddTicks(9430));

			migrationBuilder.AlterColumn<DateTime>(
				 name: "CreatedDate",
				 table: "ElectionCandidates",
				 type: "datetime2",
				 nullable: false,
				 defaultValue: new DateTime(2020, 9, 2, 16, 45, 57, 516, DateTimeKind.Local).AddTicks(1274),
				 oldClrType: typeof(DateTime),
				 oldDefaultValue: new DateTime(2020, 9, 2, 19, 44, 2, 318, DateTimeKind.Local).AddTicks(2256));
		}
	}
}
