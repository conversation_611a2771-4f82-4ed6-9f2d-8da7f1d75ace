﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace ElectionAppServer.Migrations.ApplicationDb
{
    /// <inheritdoc />
    public partial class NamanigarPhase : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "IsActive",
                table: "Namanigars",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "Phone2",
                table: "Namanigars",
                type: "varchar(150)",
                unicode: false,
                maxLength: 150,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Phone3",
                table: "Namanigars",
                type: "varchar(150)",
                unicode: false,
                maxLength: 150,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Phone4",
                table: "Namanigars",
                type: "varchar(150)",
                unicode: false,
                maxLength: 150,
                nullable: true);

            migrationBuilder.CreateTable(
                name: "PhaseWiseNamanigars",
                columns: table => new
                {
                    PhaseId = table.Column<int>(type: "int", nullable: false),
                    NamanigarId = table.Column<int>(type: "int", nullable: false),
                    CreatedBy = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PhaseWiseNamanigars", x => new { x.NamanigarId, x.PhaseId });
                    table.ForeignKey(
                        name: "FK_PhaseWiseNamanigars_ElectionPhases_PhaseId",
                        column: x => x.PhaseId,
                        principalTable: "ElectionPhases",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_PhaseWiseNamanigars_Namanigars_NamanigarId",
                        column: x => x.NamanigarId,
                        principalTable: "Namanigars",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateIndex(
                name: "IX_PhaseWiseNamanigars_PhaseId",
                table: "PhaseWiseNamanigars",
                column: "PhaseId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "PhaseWiseNamanigars");

            migrationBuilder.DropColumn(
                name: "IsActive",
                table: "Namanigars");

            migrationBuilder.DropColumn(
                name: "Phone2",
                table: "Namanigars");

            migrationBuilder.DropColumn(
                name: "Phone3",
                table: "Namanigars");

            migrationBuilder.DropColumn(
                name: "Phone4",
                table: "Namanigars");
        }
    }
}
