﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ElectionAppServer.DTO;
using ElectionAppServer.Model;
using Microsoft.EntityFrameworkCore;

namespace ElectionAppServer.Data;

public class WardService
{
    private readonly IDbContextFactory<ApplicationDbContext>
        _contextFactory; // private  readonly  ApplicationDbContext dc;

    public WardService(IDbContextFactory<ApplicationDbContext> contextFactory) // (ApplicationDbContext  context)
    {
        _contextFactory = contextFactory; // dc  =  context;
    }

    public Task<List<GeneralItemDTO>> GetList(int ucId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.Structures.OfType<Ward>()
            orderby aa.EnglishTitle
            where aa.UnionCouncilId == ucId
            select new GeneralItemDTO
                { Id = aa.Id, EnglishTitle = aa.EnglishTitle, UrduTitle = aa.UrduTitle }).ToList();
        return Task.FromResult(q);
    }

    public Task<Ward> Create(Ward obj)
    {
        using var dc = _contextFactory.CreateDbContext();
        dc.Structures.Add(obj);
        dc.SaveChanges();
        return Task.FromResult(obj);
    }

    public Task<Ward> Update(Ward obj)
    {
        using var dc = _contextFactory.CreateDbContext();
        var st = (from aa in dc.Structures.OfType<Ward>()
            where aa.Id == obj.Id
            select aa).FirstOrDefault();
        st.UrduTitle = obj.UrduTitle.Trim();
        st.EnglishTitle = obj.EnglishTitle.Trim();
        st.ModifiedBy = obj.ModifiedBy;
        st.ModifiedDate = DateTime.Now;
        st.UnionCouncilId = obj.UnionCouncilId;
        dc.SaveChanges();
        return Task.FromResult(obj);
    }

    public async Task<Ward> Delete(int Id)
    {
        using var dc = _contextFactory.CreateDbContext();
        var st = await (from aa in dc.Structures.OfType<Ward>()
            where aa.Id == Id
            select aa).FirstOrDefaultAsync();
        dc.Structures.Remove(st);
        await dc.SaveChangesAsync();
        return await Task.FromResult(st);
    }

    public Task<List<GeneralItemDTO>> GetStructure(int ucId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var op = new List<GeneralItemDTO>();
        var q = (from aa in dc.Structures.OfType<UnionCouncil>()
            where aa.Id == ucId
            select new
            {
                UcId = aa.Id,
                Uc = aa.EnglishTitle,
                aa.TownId,
                Town = aa.Town.EnglishTitle,
                aa.Town.DistrictId,
                District = aa.Town.District.EnglishTitle,
                aa.Town.District.DivisionId,
                Division = aa.Town.District.Division.EnglishTitle,
                aa.Town.District.Division.ProvinceId,
                Province = aa.Town.District.Division.Province.EnglishTitle
            }).FirstOrDefault();
        op.Add(new GeneralItemDTO { Id = q.ProvinceId, EnglishTitle = q.Province, UrduTitle = "Province" });
        op.Add(new GeneralItemDTO { Id = q.DivisionId, EnglishTitle = q.Division, UrduTitle = "Division" });
        op.Add(new GeneralItemDTO { Id = q.DistrictId, EnglishTitle = q.District, UrduTitle = "District" });
        op.Add(new GeneralItemDTO { Id = q.TownId, EnglishTitle = q.Town, UrduTitle = "Town" });
        op.Add(new GeneralItemDTO { Id = q.UcId, EnglishTitle = q.Uc, UrduTitle = "Town" });

        return Task.FromResult(op);
    }
}