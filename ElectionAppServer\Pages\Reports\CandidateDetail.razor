﻿@layout ReportLayout
@page "/report/candidate/{CandidateId:int}"
@inherits OwningComponentBase<CandidateService>
@*@attribute [Authorize(Roles ="Administrators")]*@

<style>
    body {
        user-select: none;
    }
</style>

@if (info != null)
{
    <div class="card bg-secondary mb-3">
        <div class="card-header" style="display: flex; justify-content: space-between;align-items: center">
            <img onerror="this.onerror=null; this.src='media/candidates/blank.jpg'" src="/media/candidates/@(CandidateId).jpg" style="border: 1px solid white; border-radius: 6px; max-width: 120px;margin-right: 6px; -webkit-box-shadow: 3px 3px 9px 1px rgba(0,0,0,0.4);-moz-box-shadow: 3px 3px 9px 1px rgba(0,0,0,0.4);box-shadow: 3px 3px 9px 1px rgba(0,0,0,0.4);" alt="" align="left"/>
            <div style="flex:1; font-size:30px; font-weight:bold">
                @info.EnglishName<br/>
                <span class="urdu-cap" style="font-size:34px; text-align: right;direction: rtl">@info.UrduName</span>
            </div>
        </div>
        <div class="card-body bg-light" style="color:black">
            <div class="row">
                <div class="col-md-2 rptfield">
                    <div>Code: </div>
                    <div class="rptdata">
                        <b>@info.Id</b>
                    </div>
                </div>
                @if (info.NominationType == NominationType.Candidate)
                {
                    <div class="col-md rptfield">
                        <div>Father Name: </div>
                        <div class="rptdata">
                            <b>@info.FatherEnglishName</b>
                        </div>
                    </div>
                    <div class="col-md rptfield">
                        <div>Father Name (اردو): </div>
                        <div class="rptdata">
                            <b>@info.FatherUrduName</b>
                        </div>
                    </div>
                }
            </div>
            @if (info.NominationType == NominationType.Candidate)
            {
                <div class="row">
                    <div class="col-md rptfield">
                        <div>Experience: </div>
                        <div class="rptdata">
                            <b>@(info.IsFresh ? "Fresh" : "Experienced")</b>
                        </div>
                    </div>
                    <div class="col-md rptfield">
                        <div>Gender: </div>
                        <div class="rptdata">
                            <b>@info.Gender</b>
                        </div>
                    </div>
                    <div class="col-md rptfield">
                        <div>Date of Birth: </div>
                        <div class="rptdata">
                            <b>@(info.DateOfBirth == null ? "N/A" : info.DateOfBirth.Value.ToString("d MMM, yyyy"))</b>
                        </div>
                    </div>
                    <div class="col-md rptfield">
                        <div>Contact Number: </div>
                        <div class="rptdata">
                            <b>@(string.IsNullOrEmpty(info.ContactNumber) ? "N/A" : info.ContactNumber)</b>
                        </div>
                    </div>
                </div>
            }
            <div class="row">
                @if (info.NominationType == NominationType.Candidate)
                {
                    <div class="col-md rptfield">
                        <div>District: </div>
                        <div class="rptdata">
                            <b>@info.District</b>
                        </div>
                    </div>
                }
                <div class="col-md rptfield">
                    <div>Total Assets: </div>
                    <div class="rptdata">
                        <b>@(string.IsNullOrEmpty(info.TotalAssets) ? "N/A" : info.TotalAssets)</b>
                    </div>
                </div>
            </div>
            @if (info.NominationType == NominationType.Candidate)
            {
                <div class="row">
                    <div class="col-md rptfield">
                        <div>Profession: </div>
                        <div class="rptdata">
                            <b>@info.Profession</b>
                        </div>
                    </div>
                    <div class="col-md rptfield">
                        <div>Education: </div>
                        <div class="rptdata">
                            <b>@info.Education</b>
                        </div>
                    </div>
                    <div class="col-md rptfield">
                        <div>Degree: </div>
                        <div class="rptdata">
                            <b>@(string.IsNullOrEmpty(info.EducationDegree) ? "N/A" : info.EducationDegree)</b>
                        </div>
                    </div>
                    <div class="col-md rptfield">
                        <div>Language: </div>
                        <div class="rptdata">
                            <b>@info.Language</b>
                        </div>
                    </div>
                    <div class="col-md rptfield">
                        <div>Caste: </div>
                        <div class="rptdata">
                            <b>@info.Caste</b>
                        </div>
                    </div>
                </div>
            }
            <div class="row">
                <div class="col-md rptfield">
                    <div>Trivia: </div>
                    <div class="rptdata">
                        <b>@(string.IsNullOrEmpty(info.Trivia) ? "N/A" : info.Trivia)</b>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="card bg-info mb-3">
        <div class="card-header" style="display: flex; justify-content: space-between;align-items: center">
            <div>Nominations</div>
            <div class="urdu-cap" style="text-align: right;direction: rtl">نامزدگیاں</div>
        </div>
        <div class="card-body bg-light" style="color:black">
            <table class="table table-striped table-responsive">
                <thead>
                <tr>
                    <th>
                        <b>Election</b>
                    </th>
                    <th>
                        <b>Date</b>
                    </th>
                    <th>
                        <b>Assembly</b>
                    </th>
                    <th>
                        <b>Constituency</b>
                    </th>
                    <th>
                        <b>Seat Type</b>
                    </th>
                    <th>
                        <b>Party</b>
                    </th>
                    <th>
                        <b>Symbol</b>
                    </th>
                    <th>
                        <b>Weight</b>
                    </th>
                    <th>
                        <b>Votes</b>
                    </th>
                </tr>
                </thead>
                <tbody>
                @foreach (var pc in info.Nominations)
                {
                    <tr>
                        <th>@((MarkupString)pc.Election)</th>
                        <th>@pc.StartDate.ToString("d MMM, yyyy")</th>
                        <th>@((MarkupString)pc.Assembly)</th>
                        <th>
                            @if (pc.IsWinner)
                            {
                                <img src="/images/badge.png" alt="Winner"/>
                                <span>@((MarkupString)pc.Constituency)</span>
                            }
                            else
                            {
                                <span>@((MarkupString)pc.Constituency)</span>
                            }
                        </th>
                        <th>@((MarkupString)pc.SeatType)</th>
                        <th>@((MarkupString)pc.Party)</th>
                        <th>@((MarkupString)pc.Symbol)</th>
                        <th>@pc.Weight</th>
                        <th>@pc.Votes</th>
                    </tr>
                }
                </tbody>
            </table>
        </div>
    </div>

    @if (info.NominationType == NominationType.Candidate)
    {
        <div class="card bg-success mb-3">
            <div class="card-header" style="display: flex; justify-content: space-between;align-items: center">
                <div>Political Career</div>
                <div class="urdu-cap" style="text-align: right;direction: rtl">سیاسی کیریئر</div>
            </div>
            <div class="card-body bg-light" style="color:black">
                <table class="table table-striped">
                    <thead>
                    <tr>
                        <th>
                            <b>Position</b>
                        </th>
                        <th>
                            <b>Date From</b>
                        </th>
                        <th>
                            <b>Date To</b>
                        </th>
                    </tr>
                    </thead>
                    <tbody>
                    @foreach (var pc in info.PoliticalCareers)
                    {
                        <tr>
                            <td>
                                <b>@pc.Position</b>
                            </td>
                            <td>
                                <b>@pc.DateFrom.ToString("d MMM, yyyy")</b>
                            </td>
                            <td>
                                <b>@(pc.DateTo == null ? " " : pc.DateTo.Value.ToString("d MMM, yyyy"))</b>
                            </td>
                        </tr>
                    }
                    </tbody>
                </table>
            </div>
        </div>
    }

    @if (info.NominationType == NominationType.Candidate)
    {
        <div class="card bg-success mb-3">
            <div class="card-header" style="display: flex; justify-content: space-between;align-items: center">
                <div>Party Positions</div>
                <div class="urdu-cap" style="text-align: right;direction: rtl">تنظمی عہدے</div>
            </div>
            <div class="card-body bg-light" style="color:black">
                <table class="table table-striped">
                    <thead>
                    <tr>
                        <th>
                            <b>Party</b>
                        </th>
                        <th>
                            <b>Position</b>
                        </th>
                        <th>
                            <b>Date From</b>
                        </th>
                        <th>
                            <b>Date To</b>
                        </th>
                    </tr>
                    </thead>
                    <tbody>
                    @foreach (var pc in info.PartyAffiliations)
                    {
                        <tr>
                            <th>@pc.Party</th>
                            <td>@pc.Position</td>
                            <td>@pc.DateFrom.ToString("d MMM, yyyy")</td>
                            <td>@(pc.DateTo == null ? " " : pc.DateTo.Value.ToString("d MMM, yyyy"))</td>
                        </tr>
                    }
                    </tbody>
                </table>
            </div>
        </div>
    }
}
<!--
Candidate Information
	 Political Career
	 Party affiliation
	 Election wise participation
-->

@code {
    [Parameter] public int CandidateId { get; set; }
    public CandidateDetailDTO info { get; set; }

    protected override async Task OnParametersSetAsync()
    {
        info = await Service.GetCandidateRptInfo(CandidateId);
    }

}