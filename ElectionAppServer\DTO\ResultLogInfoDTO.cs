﻿using System;
using System.Collections.Generic;

namespace ElectionAppServer.DTO;

public class ResultLogInfoDTO
{
    public string Action { get; set; }
    public DateTime PostingDateTime { get; set; }
    public string PostedBy { get; set; }
    public int PSCount { get; set; }
    public int PrevPSCount { get; set; }
    public List<ResultLogInfoDetailDTO> Candidates { get; set; }
}

public class ResultLogInfoDetailDTO
{
    public string Candidate { get; set; }
    public int Votes { get; set; }
    public int PrevVotes { get; set; }
}