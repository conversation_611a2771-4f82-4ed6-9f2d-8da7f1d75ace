﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace ElectionAppServer.Migrations.ApplicationDb
{
    public partial class addwinnerandrunnerupcandidateids : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "RunnerUpCandidateId",
                table: "LiveResults",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "WinnerCandidateId",
                table: "LiveResults",
                type: "int",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "RunnerUpCandidateId",
                table: "LiveResults");

            migrationBuilder.DropColumn(
                name: "WinnerCandidateId",
                table: "LiveResults");
        }
    }
}
