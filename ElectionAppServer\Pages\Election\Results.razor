﻿@page "/results"

@inject NavigationManager NavigationManager
@inject UserManager<ApplicationUser> _UserManager
@inject RoleManager<IdentityRole> _RoleManager
@inject AuthenticationStateProvider AuthenticationStateProvider

@using FilterType = Syncfusion.Blazor.DropDowns.FilterType
@using Size = MudBlazor.Size
@inherits OwningComponentBase<ElectionResultsDataService>
@*<a href="/myconstituencies">Back to Constituency Selection</a>*@

<SfToast @ref="ToastObj" Title="Alert" ShowProgressBar="true" Timeout="3000">
    <ToastPosition X="Right" Y="Bottom"></ToastPosition>
</SfToast>
<div class="mt-1">

<div class="card border border-primary bg-primary mb-2 text-white">
    <div class="card-header p-2 pl-3">Result Posting @(IsConnected ? "" : " - (Not Connected)")</div>
    <div class="card-body text-black bg-light" style="color:black; padding: 8px;">
        <div class="row">

            <div class="col-md">

                @if (AssembliesList != null)
                {
                    <SfDropDownList Enabled="@(EditMode == "")" TItem="GeneralItemDTO" TValue="int?" Placeholder="Select Assembly"
                                    DataSource="@AssembliesList" FloatLabelType="FloatLabelType.Always" AllowFiltering="true"
                                    FilterType="FilterType.Contains" ShowClearButton="true">
                        <DropDownListFieldSettings Value="Id" Text="EnglishTitle"></DropDownListFieldSettings>
                        <DropDownListEvents TValue="int?" TItem="GeneralItemDTO" ValueChange="@OnAssemblyChange"></DropDownListEvents>
                    </SfDropDownList>
                }
                else
                {
                    <p></p>
                }
            </div>
            <div class="col-md">
                @if (ConstituenciesList != null)
                {
                    @if (ConstituenciesList.Any())
                    {
                        <SfDropDownList Enabled="@(EditMode == "")" AllowFiltering="true" TItem="GeneralItemDTO" TValue="int?"
                                        Placeholder="Constituency" DataSource="@ConstituenciesList" FloatLabelType="FloatLabelType.Always"
                                        ShowClearButton="true"
                                        FilterType="FilterType.Contains">
                            <DropDownListFieldSettings Value="Id" Text="EnglishTitle"></DropDownListFieldSettings>
                            <DropDownListEvents TValue="int?" TItem="GeneralItemDTO" ValueChange="@OnConstChange"></DropDownListEvents>
                        </SfDropDownList>
                    }
                    else
                    {
                        <p>No constituency is defined on this assembly</p>
                    }
                }

                else
                {
                    <p></p>
                }
            </div>
            @if (SeatTypeList is { Count: > 1 })
            {
                <div class="col-md-3">
                    <SfDropDownList TValue="int?" Enabled="@(EditMode == "")" TItem="GeneralItemDTO" Placeholder="Seat" DataSource="@SeatTypeList" FloatLabelType="FloatLabelType.Always" ShowClearButton="true"
                                    AllowFiltering="true" FilterType="FilterType.Contains">
                        <DropDownListFieldSettings Value="Id" Text="EnglishTitle"></DropDownListFieldSettings>
                        <DropDownListEvents TValue="int?" TItem="GeneralItemDTO" ValueChange="@OnSeatTypChange"></DropDownListEvents>
                    </SfDropDownList>
                </div>
            }

            <div class="col-md">
                @if (NamanigarList != null)
                {
                    @if (NamanigarList.Any() && SelectedStructureId != null)
                    {
                        <SfDropDownList @ref="ddlNamanigar" TValue="int?" Enabled="@(EditMode == "")" TItem="NamanigarDTO" Placeholder="Namanigar" DataSource="@NamanigarList" FloatLabelType="FloatLabelType.Always" ShowClearButton="true"
                                        AllowFiltering="true" FilterType="FilterType.Contains">
                            <DropDownListFieldSettings Value="Id" Text="Name"></DropDownListFieldSettings>
                            <DropDownListEvents TValue="int?" TItem="NamanigarDTO" ValueChange="@OnNamanigarChange"></DropDownListEvents>

                        </SfDropDownList>
                        @*<SfComboBox Enabled="@(EditMode=="")" AllowFiltering="true" TValue="int?" TItem="NamanigarDTO" Placeholder="Namanigar" DataSource="@NamanigarList" FloatLabelType="FloatLabelType.Always" @bind-Value="SelectedNamanigarId">
                                    <ComboBoxFieldSettings Value="Id" Text="Name"></ComboBoxFieldSettings>
                                </SfComboBox>*@
                    }
                    else
                    {
                        <p>No namanigar is defined on this constituency</p>
                    }
                }
                else
                {
                    <p></p>
                }
            </div>
        </div>
    </div>
</div>

@if (SelectedAssemblyId != null && SelectedStructureId != null && SelectedSeatTypeId != null)
{
    @if (ResultDetail == null || ResultDetail.Count == 0)
    {
        <h3>No nomination found on this constituency</h3>
        <a href="/nominations">Nominations</a>
    }
    else if (ResultDetail[0].TotalPollingStations == 0)
    {
        <h3>Total Polling Stations is not define on this constituency</h3>
    }
    else if (ResultDetail[0].VotersCount == 0)
    {
        <h3>Total Voters are not defined on this constituency</h3>
    }
    else
    {
        @if (EditMode == "")
        {
            <div class="row mb-2">
                <div class="col">
                </div>
            </div>
        }

        <div class="row">
            <div class="col-md-3">
                <div class="card border border-success bg-success text-white mb-2">
                    <div class="card-header p-2 ">
                        @EditMode Result

                    </div>
                    <div class="card-body text-black bg-light" style="padding: 8px">

                        <table class="table table-sm">
                            @if (!ResultDetail.Any(k => k.IsBilaMokabilaWinner))
                            {
                                <tr>
                                    <td>
                                        <SfNumericTextBox ShowSpinButton="false" Enabled="@(EditMode == "")" Format="########" Decimals="0" Min="0" Placeholder="Polling Stations" FloatLabelType="FloatLabelType.Always" @bind-Value="ChangePollingStations" Max="ResultDetail[0].TotalPollingStations"></SfNumericTextBox>
                                    </td>
                                </tr>
                            }


                            <tr>
                                <td>
                                    @if (ResultDetail.Any(k => k.IsBilaMokabilaWinner))
                                    {
                                        <p>امیدوار پہلے سے ہی بلا مقابلہ جیت چکا ہے</p>
                                        <p>Candidate has already won unopposed</p>
                                    }
                                    else
                                    {
                                        @if (ResultDetail.Any())
                                        {
                                            <MudButton Size="Size.Small" Variant="Variant.Filled" Color="Color.Primary"
                                                       StartIcon="@Icons.Material.Filled.Add"
                                                       Disabled="@(SelectedNamanigarId == null || EditMode != "")" OnClick="@(() => OpenForm("Add"))">
                                                Add
                                            </MudButton>
                                            <MudButton Size="Size.Small" Variant="Variant.Filled" Color="Color.Info"
                                                       StartIcon="@Icons.Material.Filled.Update" Disabled="@(SelectedNamanigarId == null || EditMode != "")" OnClick="@(() => OpenForm("Update"))">
                                                Update
                                            </MudButton>
                                        }
                                    }
                                </td>
                            </tr>
                            @if (EditMode != "")
                            {
                                <tr>
                                    <td style="text-align:right">
                                        <MudButton Size="Size.Small" Variant="Variant.Filled" Color="Color.Primary"
                                                   StartIcon="@Icons.Material.Filled.Save" OnClick="SaveRecord">
                                            Save
                                        </MudButton>
                                        <MudButton Size="Size.Small" Variant="Variant.Filled" Color="Color.Secondary"
                                                   StartIcon="@Icons.Material.Filled.Cancel" OnClick="CancelRecord">
                                            Cancel
                                        </MudButton>
                                    </td>
                                </tr>
                            }
                        </table>
                    </div>
                </div>
            </div>
            <div class="col-md-9">
                <div class="card border border-secondary bg-secondary mb-2">
                    <div class="card-header p-2 pl-3">
                        Constituency Info

                    </div>
                    <div class="card-body bg-light" style="padding: 8px;">
                        <table class="table table">
                            <tr>
                                <td>
                                    Total Polling Stations: <b>@ResultDetail[0].TotalPollingStations</b>
                                    @if (IsPSWiseResultsExist)
                                    {
                                        <br/>
                                        <span style="color:blue">Total Polling Stations (PS): <b>@ResultDetail[0].TotalPollingStationsPS</b></span>
                                    }
                                </td>
                                <td>
                                    Total Voters: <b>@ResultDetail[0].VotersCount</b>
                                    @if (IsPSWiseResultsExist)
                                    {
                                        <br/>
                                        <span style="color:blue">Total Voters (PS): <b>@ResultDetail[0].VotersCount</b></span>
                                    }
                                </td>
                                <td>
                                    Male Voters: <b>@ResultDetail[0].MaleVoters</b>
                                    @if (IsPSWiseResultsExist)
                                    {
                                        <br/>
                                        <span style="color:blue">Male Voters (PS): <b>@ResultDetail[0].MaleVotersPS</b></span>
                                    }
                                </td>
                                <td>
                                    Female Voters: <b>@ResultDetail[0].FemaleVoters</b>
                                    @if (IsPSWiseResultsExist)
                                    {
                                        <br/>
                                        <span style="color:blue">Female Voters (PS): <b>@ResultDetail[0].FemaleVotersPS</b></span>
                                    }
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    @*Polling Stations: <b>@result_detail[0].ResultPollingStations</b>  - (@(Math.Round(1.0 * (float)result_detail[0].ResultPollingStations / (float)result_detail[0].TotalPollingStations * 100.0, 2))%)<br />*@
                                    Polling Stations Completed: <b>@NewResPollingStations</b> - (@(Math.Round(1.0 * (float)NewResPollingStations / (float)ResultDetail[0].TotalPollingStations * 100.0, 2))%)<br/>
                                    @if (IsPSWiseResultsExist)
                                    {
                                        <span style="color:blue">Polling Stations Completed (PS): <b>@ResultDetail[0].PollingStationCompletedPS</b> - (@Math.Round(1.0 * ResultDetail[0].PollingStationCompletedPS / (float)ResultDetail[0].TotalPollingStationsPS * 100.0, 2)%)</span>
                                    }
                                </td>
                                <td>
                                    Votes Caste: <b>@(ResultDetail.Sum(c => c.Votes))</b>
                                    @if (IsPSWiseResultsExist)
                                    {
                                        <br/>
                                        <span style="color:blue">Vote Castes (PS): <b>@ResultDetail[0].VotersCastePS</b></span>
                                    }
                                </td>
                                <td>
                                    Voter Turnout (%): <b>@(Math.Round(1.0 * (float)ResultDetail.Sum(c => c.Votes) / (float)ResultDetail[0].VotersCount * 100.0, 2)) %</b>
                                    @if (IsPSWiseResultsExist)
                                    {
                                        <br/>
                                        <span style="color:blue">Voter Turnout (PS): <b>@ResultDetail[0].VoterTurnOutPS %</b></span>
                                    }
                                </td>
                                <td>
                                    Total Rejected Votes: <b>@ResultDetail[0].RejectedVotes</b>
                                </td>

                            </tr>
                            @if (ResultDetail[0].IsRePoll)
                            {
                                <tr>
                                    <td style="background-color: #f9d4e1; display: flex; align-items: center; justify-content: center; border: 1px solid red; border-radius: 10px;">
                                        <span style="color:red;font-weight:bold"> RE-POLL</span>
                                    </td>
                                </tr>
                            }

                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="card border-info border bg-info text-white mb-2 ">
            <div class="card-header p-2 pl-3">Candidates</div>
            <div class="card-body bg-light text-black" style="color:black; padding:8px">
                <table class="table table-sm table-striped">
                    <thead>
                    <tr class="bg-info text-white">
                        <th>&nbsp;</th>
                        <th>Name</th>
                        <th colspan="2">Party</th>
                        <th colspan="2">Symbol</th>
                        <th>Weight</th>
                        @if (IsPSWiseResultsExist)
                        {
                            <th style="color:blue">Votes (PS)</th>
                        }
                        <th>Votes</th>
                        @if (EditMode != "")
                        {
                            <th style="width:170px">@EditMode</th>
                        }

                        /*<th style="width:170px">Action</th>*/
                    </tr>
                    </thead>
                    <tbody>

                    @foreach (var res in ResultDetail)
                    {
                        //var cImg = $"media/candidates/{ res.CandidateId }.jpg";
                        //var pImg = $"media/flags/{res.PartyId}.jpg";
                        //var sImg = $"media/symbols/{res.SymbolId}.jpg";
                        var stl = res.IsWithdraw ? "background-color:#dc35457a" : "";

                        <tr style="@stl">
                            <td style="width:65px; text-align:center">
                                <img src="@res.candidateImg" style="width:60px" align="left" alt="1"/>
                            </td>
                            <td>
                                @res.EnglishName<br/>
                                <span class="urdu-column">@res.UrduName</span>
                                @if (res.IsWithdraw)
                                {
                                    <span style='color:red; font-weight:bold'> (Withdraw)</span>
                                }
                            </td>
                            <td style="width:65px; text-align:center">
                                <img src="@res.flagImg" style="width:60px" align="left" alt="PTI"/>
                            </td>
                            <td>
                                @res.Party<br/>
                                <span class="urdu-column">@res.PartyUrdu</span>
                            </td>
                            <td style="width:65px; text-align:center">
                                <img src="@res.symbolImg" style="width:60px" align="left" alt="BAT"/>
                            </td>
                            <td>
                                @res.Symbol<br/>
                                <span class="urdu-column">@res.SymbolUrdu</span>
                            </td>
                            <td>@res.Weight</td>
                            @if (IsPSWiseResultsExist)
                            {
                                <td style="color: blue;">@res.PSVotes</td>
                            }

                            <td>@res.TotalVotes</td>
                            @if (EditMode != "")
                            {
                                <td>
                                    <SfNumericTextBox Format="########" @bind-Value="res.ChangeVotes" Decimals="0" Min="0" Placeholder="Votes" ShowSpinButton="false"></SfNumericTextBox>
                                </td>
                            }

                            /*
                            <td>
                                <SfButton CssClass="e-info">Edit</SfButton>
                            </td>*/
                        </tr>
                    }
                    </tbody>
                </table>
            </div>
        </div>

        <div class="card border border-secondary bg-secondary">
            <div class="card-header p-2 pl-3">Namanigars</div>
            <div class="card-body bg-light text-black" style="color:black; padding:8px">
                <SfGrid DataSource="NamanigarList" AllowSorting="true" AllowTextWrap="true" Width="100%">
                    <GridColumns>
                        <GridColumn Field="@nameof(NamanigarDTO.Code)" HeaderText="Code" AutoFit="true"></GridColumn>
                        <GridColumn Field="@nameof(NamanigarDTO.Name)" HeaderText="Code" AutoFit="true"></GridColumn>
                        <GridColumn Field="@nameof(NamanigarDTO.Phone)" HeaderText="Phone" AutoFit="true"></GridColumn>
                        <GridColumn Field="@nameof(NamanigarDTO.Email)" HeaderText="Email" AutoFit="true"></GridColumn>
                        <GridColumn Field="@nameof(NamanigarDTO.PSDetail)" HeaderText="PS Detail" AutoFit="true"></GridColumn>
                    </GridColumns>
                </SfGrid>
            </div>
        </div>
        @if (LastLog != null)
        {
            <div class="card border border-info bg-info">
                <div class="card-header p-2 pl-3">Last Posted Result</div>
                <div class="card-body bg-light text-black" style="color:black; padding:8px">
                    <div class="row m-1">
                        <div class="col-md">
                            Last Action: <b>@LastLog.Action</b>
                        </div>
                        <div class="col-md">
                            Previous Polling Station: <b>@LastLog.PrevPSCount</b>
                        </div>
                        <div class="col-md">
                            Polling Station: <b>@LastLog.PSCount</b>
                        </div>
                        <div class="col-md">
                            Posting Date Time: <b>@LastLog.PostingDateTime.ToString("d MMM, yyyy h:mm:ss")</b>
                        </div>
                        <div>
                            Posted By: <b>@LastLog.PostedBy</b>
                        </div>
                    </div>
                    <div class="row m-1 mt-2">
                        <table class="table">
                            <thead>
                            <tr>
                                <th>Candidate</th>
                                <th>Previous Votes</th>
                                <th>Votes</th>
                            </tr>
                            </thead>
                            <tbody>
                            @foreach (var c in LastLog.Candidates)
                            {
                                <tr>
                                    <td>@c.Candidate</td>
                                    <td>@c.PrevVotes</td>
                                    <td>@c.Votes</td>
                                </tr>
                            }

                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        }
    }
}
</div>