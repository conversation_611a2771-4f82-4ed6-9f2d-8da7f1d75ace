﻿using System.Collections.Generic;
using ElectionAppServer.Model.Structures;

namespace ElectionAppServer.Model;

public class District : Structure
{
    public District()
    {
        Towns = new List<Town>();
        NationalAssemblyHalkas = new List<NationalAssemblyHalka>();
        ProvincialAssemblyHalkas = new List<ProvincialAssemblyHalka>();
    }

    public int DivisionId { get; set; }
    public virtual Region Region { get; set; }
    public int? RegionId { get; set; }
    public virtual Division Division { get; set; }
    public virtual List<Town> Towns { get; set; }

    public int? AdminDivisionId { get; set; }

    public virtual AdminDivision AdminDivision { get; set; }

    //public virtual List<Namanigar> Namanigars { get; set; }
    public virtual List<NationalAssemblyHalka> NationalAssemblyHalkas { get; set; }

    public virtual List<ProvincialAssemblyHalka> ProvincialAssemblyHalkas { get; set; }

    public virtual List<CandidateDistrict> CandidateDistricts { get; set; }
    public virtual List<PhaseWiseNamanigar> PhaseWiseNamanigars { get; set; }
}