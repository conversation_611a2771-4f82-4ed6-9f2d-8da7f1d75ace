﻿@page "/educations"

@using BeforeOpenEventArgs = Syncfusion.Blazor.Popups.BeforeOpenEventArgs
@using ButtonType = MudBlazor.ButtonType
@using FilterType = Syncfusion.Blazor.Grids.FilterType
@using Size = MudBlazor.Size
@inherits OwningComponentBase<EducationService>
@*<PageCaption Title="Educations"></PageCaption>*@
<MudText Typo="Typo.h5">Educations</MudText>
<SfToast @ref="ToastObj" Title="Alert" ShowProgressBar="true" Timeout="3000">
    <ToastPosition X="Right" Y="Bottom"></ToastPosition>
</SfToast>

<SfDialog Width="500px" @bind-Visible="@isDlgVisible" @ref="dlgForm" IsModal="true" ShowCloseIcon="true">
    <DialogEvents OnOpen="beforeOpen"></DialogEvents>
    <DialogTemplates>
        <Header>@FormTitle</Header>
        <Content>
            <EditForm Model="@selectedObj" OnValidSubmit="@SaveEducationData">
                <DataAnnotationsValidator/>

                <div class="row">
                    <div class="input-field col s6">
                        <SfTextBox @bind-Value="selectedObj.EnglishTitle" Placeholder="Title (English)" FloatLabelType="FloatLabelType.Always" OnBlur="TranslateToUrdu"></SfTextBox>
                        <ValidationMessage For="@(() => selectedObj.EnglishTitle)"/>
                    </div>
                    <div class="input-field col s6">
                        <SfTextBox @bind-Value="selectedObj.UrduTitle" Placeholder="Title (Urdu)" FloatLabelType="FloatLabelType.Always"></SfTextBox>
                        <ValidationMessage For="@(() => selectedObj.UrduTitle)"/>
                    </div>
                </div>
                <div class="row">
                    <div class="col">
                        <MudButton ButtonType="ButtonType.Submit"
                                   Color="Color.Primary" Size="Size.Small"
                                   Variant="Variant.Filled">
                            @SaveButtonText
                        </MudButton>
                    </div>
                </div>
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>

<!-- AuthorizeView allows us to only show sections of the page -->
<!-- based on the security on the current user -->
<AuthorizeView>
    <!-- Show this section if the user is logged in -->
    <Authorized>
        <section>
            @if (educations_list == null)
            {
                <p>
                    <em>Loading...</em>
                </p>
            }
            else
            {
                <SfButton IsPrimary="true" CssClass="e-primary mb-2" OnClick="OpenCreateForm">Create Education</SfButton>
                <SfGrid DataSource="@educations_list" AllowFiltering="true" @ref="Grid" AllowSorting="true" AllowTextWrap="true">
                    <GridFilterSettings Type="FilterType.Excel"></GridFilterSettings>
                    <GridColumns>
                        <GridColumn Field="Id" HeaderText="Id" AutoFit="true"></GridColumn>
                        <GridColumn Field="UrduTitle" HeaderText="Urdu Title" TextAlign="TextAlign.Right"></GridColumn>
                        <GridColumn Field="EnglishTitle" HeaderText="English Title"></GridColumn>
                        <GridColumn HeaderText="Actions" AllowFiltering="false">
                            <Template Context="ss">
                                @{
                                    var kk = ss as Education;
                                    <SfButton IsPrimary="true" CssClass="e-danger" OnClick="@(() => DeleteRecord(kk.Id))">
                                        <i class="fas fa-trash-alt"></i>
                                    </SfButton>
                                    <SfButton IsPrimary="true" CssClass="e-primary" OnClick="@(() => OpenEditForm(kk))">
                                        <i class="fas fa-pencil-alt"></i>
                                    </SfButton>
                                }
                            </Template>
                        </GridColumn>
                    </GridColumns>
                </SfGrid>
            }
        </section>
    </Authorized>
    <!-- Show this section if the user is not logged in -->
    <NotAuthorized>
        <p>You're not signed in.</p>
    </NotAuthorized>
</AuthorizeView>

@code {

    // AuthenticationState is available as a CascadingParameter
    [CascadingParameter] private Task<AuthenticationState> authenticationStateTask { get; set; }

    private SfDialog dlgForm;
    List<Education> educations_list;
    private bool isDlgVisible;
    private string SaveButtonText = "Save";
    private readonly string FormTitle = "Create Education";
    private Education selectedObj = new();
    private SfToast ToastObj;
    public string ToastContent = "Unable to delete Record, Related Record(s) Exist";
    public SfGrid<Education> Grid { get; set; }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        //return base.OnAfterRenderAsync(firstRender);
        if (firstRender)
        {
            var user = (await authenticationStateTask).User;
            if (user.Identity.IsAuthenticated)
            {
                //var dd =  await ProtectedSessionStore.GetAsync<ElectionStateDTO>("election");
                var dd = await localStorage.GetAsync<ElectionStateDTO>(await confreader.GetSettingValue("electionkey"));
                state.SetState(dd.Value);
            }
            else
            {
                try
                {
                    //await  ProtectedSessionStore.DeleteAsync("election");
                    await localStorage.DeleteAsync(await confreader.GetSettingValue("electionkey"));
                    state.SetState(new ElectionStateDTO { ElectionId = 0, ElectionTitle = "", PhaseId = 0, PhaseTitle = "" });
                }
                catch (Exception)
                {
                    // ignored
                }

                NavigationManager.NavigateTo("");
            }

            StateHasChanged();
        }
    }

    protected override async Task OnInitializedAsync()
    {
        // Get the current user
        var user = (await authenticationStateTask).User;
        // Get the educations_list for the current user
        // We access Weathereducations_listervice using @Service
        educations_list = await Service.GetList();
    }

    private async Task OpenCreateForm()
    {
        ClearData();
        await dlgForm.ShowAsync();
    }

    private async Task OpenEditForm(Education st)
    {
        selectedObj = new Education { Id = st.Id, EnglishTitle = st.EnglishTitle, UrduTitle = st.UrduTitle };
        await dlgForm.ShowAsync();
        SaveButtonText = "Update";
    }

    private void ClearData()
    {
        selectedObj = new Education();
        SaveButtonText = "Save";
        //selectedObj.Id = 0;
        //selectedObj.UrduTitle = "";
        //selectedObj.EnglishTitle = "";
    }

    private async Task SaveEducationData()
    {
        // Close the Popup
        // ShowPopup = false;
        // Get the current user
        var user = (await authenticationStateTask).User;
        // A new forecast will have the Id set to 0
        if (selectedObj.Id == 0)
        {
            // Create forecast
            var st = new Education();
            st.UrduTitle = selectedObj.UrduTitle.Trim();
            st.EnglishTitle = selectedObj.EnglishTitle.Trim();
            st.CreatedDate = DateTime.Now;
            st.ModifiedDate = DateTime.Now;

            st.CreatedBy = user.Identity.Name;
            st.ModifiedBy = user.Identity.Name;
            //Service.CreateEducation(st);
            var res = Service.CreateEducation(st);
            educations_list = await Service.GetList();
            await dlgForm.HideAsync();
        }
        else
        {
            // Create forecast
            var st = new Education();
            st.UrduTitle = selectedObj.UrduTitle.Trim();
            st.EnglishTitle = selectedObj.EnglishTitle.Trim();
            st.CreatedDate = DateTime.Now;
            st.ModifiedDate = DateTime.Now;

            st.CreatedBy = user.Identity.Name;
            st.ModifiedBy = user.Identity.Name;
            st.Id = selectedObj.Id;
            //Service.CreateEducation(st);
            var res = Service.UpdateEducation(st);
            educations_list = await Service.GetList();
            await dlgForm.HideAsync();
        }

        await Grid.Refresh();
    }

    private async Task DeleteRecord(int Id)
    {
        var res = await JSRuntime.InvokeAsync<bool>("confirm", "Are you want to delete this record?");
        if (res == false)
            return;

        try
        {
            var msg2 = await Service.DeleteEducation(Id);
            educations_list = await Service.GetList();
            ClearData();
            await Grid.Refresh();
        }
        catch (Exception ee)
        {
            var mm = new ToastModel { Title = "Error", Content = ee.Message };
            if (ee.InnerException != null)
                mm.Content += " --- Detail: " + ee.InnerException.Message;
            await ToastObj.ShowAsync(mm);
        }
    }

    protected async Task TranslateToUrdu()
    {
        if (!string.IsNullOrEmpty(selectedObj.EnglishTitle))
        {
            if (string.IsNullOrEmpty(selectedObj.UrduTitle))
                selectedObj.UrduTitle = await Translation.TranslateText(selectedObj.EnglishTitle.Trim());
        }
    }

    private void beforeOpen(BeforeOpenEventArgs args)
    {
        args.MaxHeight = null;
    }

}