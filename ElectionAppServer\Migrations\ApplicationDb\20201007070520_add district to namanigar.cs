﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace ElectionAppServer.Migrations.ApplicationDb
{
	public partial class adddistricttonamanigar : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.AddColumn<string>(
				 name: "District",
				 table: "Namanigars",
				 maxLength: 200,
				 nullable: false,
				 defaultValue: "");

			migrationBuilder.AlterColumn<string>(
				 name: "PSDetail",
				 table: "NamanigarConstituencies",
				 unicode: false,
				 maxLength: 500,
				 nullable: true,
				 oldClrType: typeof(string),
				 oldType: "varchar(500)",
				 oldUnicode: false,
				 oldMaxLength: 500);
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropColumn(
				 name: "District",
				 table: "Namanigars");

			migrationBuilder.AlterColumn<string>(
				 name: "PSDetail",
				 table: "NamanigarConstituencies",
				 type: "varchar(500)",
				 unicode: false,
				 maxLength: 500,
				 nullable: false,
				 oldClrType: typeof(string),
				 oldUnicode: false,
				 oldMaxLength: 500,
				 oldNullable: true);
		}
	}
}
