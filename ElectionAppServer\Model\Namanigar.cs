﻿using System;
using System.Collections.Generic;

namespace ElectionAppServer.Model;

public class Namanigar
{
    public int Id { get; set; }
    public string Code { get; set; }
    public string Name { get; set; }
    public string Phone { get; set; }
    public string Phone2 { get; set; }
    public string Phone3 { get; set; }
    public string Phone4 { get; set; }

    public string Email { get; set; }
    public string Address { get; set; }
    public virtual List<NamanigarConstituency> NamanigarConstituencies { get; set; }
    public virtual List<ResultLog> AsNamanigarResultLogs { get; set; }
    public string District { get; set; }
    public virtual List<NamanigarResult> NamanigarResults { get; set; }
    public virtual List<NamanigarPerformance> NamanigarPerformances { get; set; }
    public virtual List<PhaseWiseNamanigar> PhaseWiseNamanigars { get; set; }    
    public bool IsActive { get; set; } = true;

    #region Audit Log

    public DateTime CreatedDate { get; set; }
    public string CreatedBy { get; set; }
    public DateTime? ModifiedDate { get; set; }
    public string ModifiedBy { get; set; }

    #endregion Audit Log
}

public class PhaseWiseNamanigar
{
    public int PhaseId { get; set; }
    public int NamanigarId { get; set; }
    public Namanigar Namanigar { get; set; }
    public ElectionPhase Phase { get; set; }
    public string CreatedBy { get; set; }
    public DateTime CreatedDate { get; set; }
    public int NumberOfHelpers { get; set; }
    

}