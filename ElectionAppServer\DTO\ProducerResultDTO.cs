﻿using System;

namespace ElectionAppServer.DTO;

public class ProducerResultDTO
{
    public string Assembly { get; set; }
    public string Constituency { get; set; }
    public string SeatType { get; set; }
    public string Winner { get; set; }
    public string WinnerParty { get; set; }
    public int WinnerVotes { get; set; }
    public string RunnerUp { get; set; }
    public string RunnerUpParty { get; set; }
    public int RunnerUpVotes { get; set; }
    public double PSPer { get; set; }
    public int TotalPS { get; set; }
    public int ResultOfPS { get; set; }
    public string Status { get; set; }
    public DateTime LastUpdateOn { get; set; }
    public bool OnAir { get; set; }
}