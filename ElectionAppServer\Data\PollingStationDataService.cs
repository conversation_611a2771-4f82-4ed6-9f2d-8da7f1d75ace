﻿using System.Linq;
using System.Threading.Tasks;
using ElectionAppServer.DTO;
using ElectionAppServer.Model;
using ElectionAppServer.Model.Structures;
using Microsoft.EntityFrameworkCore;

namespace ElectionAppServer.Data;

public class PollingStationDataService
{
    private readonly IDbContextFactory<ApplicationDbContext>
        _contextFactory; // public  ApplicationDbContext  dc { get; set; }

    public PollingStationDataService(
        IDbContextFactory<ApplicationDbContext> contextFactory) // (ApplicationDbContext  dc)
    {
        _contextFactory = contextFactory;
    }

    public Task<PollingStationInfoDTO> GetPollingStationInfo(int constituencyId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var info = new PollingStationInfoDTO();
        var s = dc.Structures.Find(constituencyId);

        if (s is UnionCouncil)
        {
            info = (from aa in dc.Structures.OfType<UnionCouncil>()
                where aa.Id == constituencyId
                select new PollingStationInfoDTO
                {
                    Code = aa.Code,
                    Id = aa.Id,
                    AssemblyEnglish = aa.Assembly.EnglishTitle,
                    AssemblyUrdu = aa.Assembly.UrduTitle,
                    UrduTitle = aa.UrduTitle,
                    EnglishTitle = aa.EnglishTitle,
                    UCEnglish = aa.EnglishTitle,
                    UCUrdu = aa.UrduTitle,
                    TownEnglish = aa.Town.EnglishTitle,
                    TownUrdu = aa.Town.UrduTitle,
                    DistrictEnglish = aa.Town.District.EnglishTitle,
                    DistrictUrdu = aa.Town.District.UrduTitle,
                    RegionEnglish = aa.Town.District.RegionId == null ? "NA" : aa.Town.District.Region.EnglishTitle,
                    RegionUrdu = aa.Town.District.RegionId == null ? "NA" : aa.Town.District.Region.UrduTitle,
                    DivisionEnglish = aa.Town.District.Division.EnglishTitle,
                    DivisionUrdu = aa.Town.District.Division.UrduTitle,
                    ProvinceEnglish = aa.Town.District.Division.Province.EnglishTitle,
                    ProvinceUrdu = aa.Town.District.Division.Province.UrduTitle,
                    ElectionEnglish = aa.Assembly.Election.EnglishTitle,
                    ElectionUrdu = aa.Assembly.Election.UrduTitle,
                    PollingStations = (from bb in dc.PollingStations
                        where bb.ConstituencyId == constituencyId
                        //orderby  ("00000"+bb.Number).Substring(("00000" + bb.Number).Length-4)
                        select new PollingStationDetailDTO
                        {
                            Id = bb.Id,
                            ConstituencyId = bb.ConstituencyId,
                            Number = ("00000" + bb.Number).Substring(("00000" + bb.Number).Length - 4),
                            EnglishTitle = bb.EnglishTitle,
                            FemaleVoters = bb.FemaleVoters,
                            MaleVoters = bb.MaleVoters,
                            UrduTitle = bb.UrduTitle,
                            Type = bb.pollingStationType == PollingStationType.Male ? 1 :
                                bb.pollingStationType == PollingStationType.Female ? 2 :
                                bb.pollingStationType == PollingStationType.Combined ? 3 :
                                null
                        }).OrderBy(k => k.Number).ToList(),
                    Type = "UC"
                }).FirstOrDefault();

            return Task.FromResult(info);
        }

        if (s is Ward)
        {
            info = (from aa in dc.Structures.OfType<Ward>()
                where aa.Id == constituencyId
                select new PollingStationInfoDTO
                {
                    Code = aa.Code,
                    Id = aa.Id,
                    AssemblyEnglish = aa.Assembly.EnglishTitle,
                    AssemblyUrdu = aa.Assembly.UrduTitle,
                    UrduTitle = aa.UrduTitle,
                    EnglishTitle = aa.EnglishTitle,
                    UCEnglish = aa.UnionCouncil.EnglishTitle,
                    UCUrdu = aa.UnionCouncil.UrduTitle,
                    TownEnglish = aa.UnionCouncil.Town.EnglishTitle,
                    TownUrdu = aa.UnionCouncil.Town.UrduTitle,
                    DistrictEnglish = aa.UnionCouncil.Town.District.EnglishTitle,
                    DistrictUrdu = aa.UnionCouncil.Town.District.UrduTitle,
                    RegionEnglish = aa.UnionCouncil.Town.District.RegionId == null
                        ? "NA"
                        : aa.UnionCouncil.Town.District.Region.EnglishTitle,
                    RegionUrdu = aa.UnionCouncil.Town.District.RegionId == null
                        ? "NA"
                        : aa.UnionCouncil.Town.District.Region.UrduTitle,
                    DivisionEnglish = aa.UnionCouncil.Town.District.Division.EnglishTitle,
                    DivisionUrdu = aa.UnionCouncil.Town.District.Division.UrduTitle,
                    ProvinceEnglish = aa.UnionCouncil.Town.District.Division.Province.EnglishTitle,
                    ProvinceUrdu = aa.UnionCouncil.Town.District.Division.Province.UrduTitle,
                    ElectionEnglish = aa.Assembly.Election.EnglishTitle,
                    ElectionUrdu = aa.Assembly.Election.UrduTitle,
                    PollingStations = (from bb in dc.PollingStations
                        where bb.ConstituencyId == constituencyId
                        //orderby ("00000" + bb.Number).Substring(("00000" + bb.Number).Length - 4)
                        select new PollingStationDetailDTO
                        {
                            Id = bb.Id,
                            ConstituencyId = bb.ConstituencyId,
                            Number = ("00000" + bb.Number).Substring(("00000" + bb.Number).Length - 4),
                            EnglishTitle = bb.EnglishTitle,
                            FemaleVoters = bb.FemaleVoters,
                            MaleVoters = bb.MaleVoters,
                            UrduTitle = bb.UrduTitle,
                            Type = bb.pollingStationType == PollingStationType.Male ? 1 :
                                bb.pollingStationType == PollingStationType.Female ? 2 :
                                bb.pollingStationType == PollingStationType.Combined ? 3 :
                                null
                        }).ToList(),
                    Type = "Ward"
                }).FirstOrDefault();

            return Task.FromResult(info);
        }

        if (s is NationalAssemblyHalka)
        {
            info = (from aa in dc.Structures.OfType<NationalAssemblyHalka>()
                where aa.Id == constituencyId
                select new PollingStationInfoDTO
                {
                    Code = aa.Code,
                    Id = aa.Id,
                    AssemblyEnglish = aa.Assembly.EnglishTitle,
                    AssemblyUrdu = aa.Assembly.UrduTitle,
                    UrduTitle = aa.UrduTitle,
                    EnglishTitle = aa.EnglishTitle,
                    UCEnglish = "",
                    UCUrdu = "",
                    TownEnglish = "",
                    TownUrdu = "",
                    DistrictEnglish = aa.District.EnglishTitle,
                    DistrictUrdu = aa.District.UrduTitle,
                    RegionEnglish = aa.District.RegionId == null ? "NA" : aa.District.Region.EnglishTitle,
                    RegionUrdu = aa.District.RegionId == null ? "NA" : aa.District.Region.UrduTitle,
                    DivisionEnglish = aa.District.Division.EnglishTitle,
                    DivisionUrdu = aa.District.Division.UrduTitle,
                    ProvinceEnglish = aa.District.Division.Province.EnglishTitle,
                    ProvinceUrdu = aa.District.Division.Province.UrduTitle,
                    ElectionEnglish = aa.Assembly.Election.EnglishTitle,
                    ElectionUrdu = aa.Assembly.Election.UrduTitle,
                    PollingStations = (from bb in dc.PollingStations
                        where bb.ConstituencyId == constituencyId
                        //orderby ("00000" + bb.Number).Substring(("00000" + bb.Number).Length - 4)
                        select new PollingStationDetailDTO
                        {
                            Id = bb.Id,
                            ConstituencyId = bb.ConstituencyId,
                            Number = ("00000" + bb.Number).Substring(("00000" + bb.Number).Length - 4),
                            EnglishTitle = bb.EnglishTitle,
                            FemaleVoters = bb.FemaleVoters,
                            MaleVoters = bb.MaleVoters,
                            UrduTitle = bb.UrduTitle,
                            Type = bb.pollingStationType == PollingStationType.Male ? 1 :
                                bb.pollingStationType == PollingStationType.Female ? 2 :
                                bb.pollingStationType == PollingStationType.Combined ? 3 :
                                null
                        }).ToList(),
                    Type = "NA"
                }).FirstOrDefault();

            return Task.FromResult(info);
        }

        if (s is ProvincialAssemblyHalka)
        {
            info = (from aa in dc.Structures.OfType<ProvincialAssemblyHalka>()
                where aa.Id == constituencyId
                select new PollingStationInfoDTO
                {
                    Code = aa.Code,
                    Id = aa.Id,
                    AssemblyEnglish = aa.Assembly.EnglishTitle,
                    AssemblyUrdu = aa.Assembly.UrduTitle,
                    UrduTitle = aa.UrduTitle,
                    EnglishTitle = aa.EnglishTitle,
                    UCEnglish = "",
                    UCUrdu = "",
                    TownEnglish = "",
                    TownUrdu = "",
                    DistrictEnglish = aa.District.EnglishTitle,
                    DistrictUrdu = aa.District.UrduTitle,
                    RegionEnglish = aa.District.RegionId == null ? "NA" : aa.District.Region.EnglishTitle,
                    RegionUrdu = aa.District.RegionId == null ? "NA" : aa.District.Region.UrduTitle,
                    DivisionEnglish = aa.District.Division.EnglishTitle,
                    DivisionUrdu = aa.District.Division.UrduTitle,
                    ProvinceEnglish = aa.District.Division.Province.EnglishTitle,
                    ProvinceUrdu = aa.District.Division.Province.UrduTitle,
                    ElectionEnglish = aa.Assembly.Election.EnglishTitle,
                    ElectionUrdu = aa.Assembly.Election.UrduTitle,
                    PollingStations = (from bb in dc.PollingStations
                        where bb.ConstituencyId == constituencyId
                        //orderby ("00000" + bb.Number).Substring(("00000" + bb.Number).Length - 4)
                        select new PollingStationDetailDTO
                        {
                            Id = bb.Id,
                            ConstituencyId = bb.ConstituencyId,
                            Number = ("00000" + bb.Number).Substring(("00000" + bb.Number).Length - 4),
                            EnglishTitle = bb.EnglishTitle,
                            FemaleVoters = bb.FemaleVoters,
                            MaleVoters = bb.MaleVoters,
                            UrduTitle = bb.UrduTitle,
                            Type = bb.pollingStationType == PollingStationType.Male ? 1 :
                                bb.pollingStationType == PollingStationType.Female ? 2 :
                                bb.pollingStationType == PollingStationType.Combined ? 3 :
                                null
                        }).ToList(),
                    Type = "PA"
                }).FirstOrDefault();

            return Task.FromResult(info);
        }

        return null;
    }

    public Task<PollingStationDetailDTO> GetPollingStation(int id)
    {
        using (var dc = _contextFactory.CreateDbContext())
        {
            var q = (from aa in dc.PollingStations
                where aa.Id == id
                select new PollingStationDetailDTO
                {
                    ConstituencyId = aa.ConstituencyId,
                    Id = aa.Id,
                    EnglishTitle = aa.EnglishTitle,
                    FemaleVoters = aa.FemaleVoters,
                    MaleVoters = aa.MaleVoters,
                    Number = aa.Number,
                    UrduTitle = aa.UrduTitle,
                    Type = (int?)aa.pollingStationType
                }).FirstOrDefault();

            return Task.FromResult(q);
        }
    }

    public Task<string> Delete(int id)
    {
        using var dc = _contextFactory.CreateDbContext();
        var obj = dc.PollingStations.Find(id);
        dc.PollingStations.Remove(obj);
        dc.SaveChanges();
        return Task.FromResult("OK");
    }

    public Task<string> SaveData(PollingStationDetailDTO obj)
    {
        using var dc = _contextFactory.CreateDbContext();

        if (obj.Id == 0)
        {
            var ps = new PollingStation
            {
                EnglishTitle = obj.EnglishTitle,
                MaleVoters = obj.MaleVoters,
                Number = obj.Number,
                UrduTitle = obj.UrduTitle,
                FemaleVoters = obj.FemaleVoters,
                ConstituencyId = obj.ConstituencyId,
                pollingStationType = obj.Type == 1 ? PollingStationType.Male :
                    obj.Type == 2 ? PollingStationType.Female :
                    obj.Type == 3 ? PollingStationType.Combined : null
            };
            dc.PollingStations.Add(ps);
            dc.SaveChanges();
            return Task.FromResult("OK");
        }
        else
        {
            var ps = (from aa in dc.PollingStations
                where aa.Id == obj.Id
                select aa).FirstOrDefault();
            ps.EnglishTitle = obj.EnglishTitle;
            ps.MaleVoters = obj.MaleVoters;
            ps.Number = obj.Number;
            ps.UrduTitle = obj.UrduTitle;
            ps.FemaleVoters = obj.FemaleVoters;
            ps.ConstituencyId = obj.ConstituencyId;
            ps.pollingStationType = obj.Type == 1 ? PollingStationType.Male :
                obj.Type == 2 ? PollingStationType.Female :
                obj.Type == 3 ? PollingStationType.Combined : null;
            dc.SaveChanges();
            return Task.FromResult("OK");
        }
    }
}