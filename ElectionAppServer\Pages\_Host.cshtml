﻿@page "/"
@namespace ElectionAppServer.Pages
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@{
    Layout = null;
}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <title>Election Portal</title>
    <base href="/"/>
    <link href="https://fonts.googleapis.com/css?family=Noto+Nastaliq+Urdu&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap" rel="stylesheet"/>
    @*<link rel="stylesheet" href="css/bootstrap/bootstrap.min.css" />*@
    @*<link href="css/dxBS/bootstrap.min.css" rel="stylesheet" />*@
    <link href="css/site.css" rel="stylesheet"/>
    <!--<link href="_content/Syncfusion.Blazor/styles/material.css" rel="stylesheet" />-->
    @*<link href="_content/DevExpress.Blazor/dx-blazor.css" rel="stylesheet" />*@
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="/plugins/fontawesome-free/css/all.min.css">
    <!-- Theme style -->
    <link rel="stylesheet" href="/dist/css/adminlte.min.css">

    <link rel="stylesheet" href="https://code.ionicframework.com/ionicons/2.0.1/css/ionicons.min.css">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    @*<link href="_content/Syncfusion.Blazor.Themes/material.css" rel="stylesheet" />*@
    <link href="_content/Syncfusion.Blazor/styles/fluent2.css" rel="stylesheet"/>
    @*<script src="https://cdnjs.cloudflare.com/ajax/libs/lodash.js/4.17.20/lodash.min.js"></script>*@
    <script src="_content/Syncfusion.Blazor/scripts/syncfusion-blazor.min.js" type="text/javascript"></script>
    <link href="_content/MudBlazor/MudBlazor.min.css" rel="stylesheet"/>

    <style>
		.e-icons {
		}

		.e-search:before {
			content: '\e993';
		}

		.e-upload:before {
			content: '\e725';
		}

		.e-edit:before {
			content: '\e338';
		}

		.e-delete:before {
			content: '\e84e';
		}

		.e-font:before {
			content: '\e34c';
		}

		.e-grid .e-rowcell {
			padding: 3px 4px;
		}

		.material-icons {
			font-size: 16px;
		}

		.e-columnheader {
			height: unset !important;
		}

		#components-reconnect-modal {
			display: none !important;
		}

		.mud-layout {
			z-index: 600 !important;
		}

		.mud-appbar.mud-appbar-fixed-top {
			background-color: white !important;
			z-index: 10000 !important;
		}
	</style>
</head>
<body class="hold-transition sidebar-mini sidebar-collapse">
<div class="wrapper">
    <app>
        <component type="typeof(App)" render-mode="ServerPrerendered"/>
    </app>
    <div id="blazor-error-ui">
        <environment include="Staging,Production">
            An error has occurred. This application may no longer respond until reloaded.
        </environment>
        <environment include="Development">
            An unhandled exception has occurred. See browser dev tools for details.
        </environment>
        <a href class="reload">Reload</a>
        <a class="dismiss">🗙</a>
    </div>
</div>
<script src="_framework/blazor.server.js"></script>
<script src="_content/BlazorInputFile/inputfile.js"></script>
<script src="_content/MudBlazor/MudBlazor.min.js"></script>

@*<script src="_content/Microsoft.AspNetCore.ProtectedBrowserStorage/protectedBrowserStorage.js"></script>*@
<script src="/js/appfunctions.js"></script>
@*<script src="/plugins/jquery/jquery.min.js"></script>*@
<!-- Bootstrap 4 -->
<script src="/plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
<!-- AdminLTE App -->
@*	<script src="/dist/js/adminlte.min.js"></script>*@
<!-- AdminLTE for demo purposes -->
@*<script src="/dist/js/demo.js"></script>*@

<script>
		new MutationObserver(() => document.querySelector('#components-reconnect-modal') && location.reload())
			.observe(document.body, { childList: true });
	</script>
</body>
</html>

@*<!DOCTYPE html>
	<html lang="en">
	<head>
		 <meta charset="utf-8" />
		 <meta name="viewport" content="width=device-width, initial-scale=1.0" />
		 <title>ElectionAppServer</title>
		 <base href="~/" />
		 <link rel="stylesheet" href="css/bootstrap/bootstrap.min.css" />
		 <link href="css/site.css" rel="stylesheet" />
		 <link href="ElectionAppServer.styles.css" rel="stylesheet" />
	</head>
	<body>
		 <component type="typeof(App)" render-mode="ServerPrerendered" />

		 <div id="blazor-error-ui">
			  <environment include="Staging,Production">
					An error has occurred. This application may no longer respond until reloaded.
			  </environment>
			  <environment include="Development">
					An unhandled exception has occurred. See browser dev tools for details.
			  </environment>
			  <a href="" class="reload">Reload</a>
			  <a class="dismiss">🗙</a>
		 </div>

		 <script src="_framework/blazor.server.js"></script>
	</body>
	</html>
*@