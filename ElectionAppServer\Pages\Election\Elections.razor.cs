﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using ElectionAppServer.Data;
using ElectionAppServer.DTO;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Syncfusion.Blazor.Grids;
using Syncfusion.Blazor.Notifications;
using Syncfusion.Blazor.Popups;

namespace ElectionAppServer.Pages.Election;

public partial class Elections
{
    public List<GeneralItemDTO> candidatesPool = new()
    {
        new GeneralItemDTO { Id = 1, EnglishTitle = "General Election" },
        new GeneralItemDTO { Id = 2, EnglishTitle = "Gilgit Baltistan Election" },
        new GeneralItemDTO { Id = 3, EnglishTitle = "Kashmir Election" },
        new GeneralItemDTO { Id = 4, EnglishTitle = "Local-body KPK" },
        new GeneralItemDTO { Id = 5, EnglishTitle = "Cantonment Board" },
        new GeneralItemDTO { Id = 6, EnglishTitle = "Local-body Balochistan" },
        new GeneralItemDTO { Id = 7, EnglishTitle = "Local-body Sindh" },
        new GeneralItemDTO { Id = 8, EnglishTitle = "Local-body Punjab" },
        new GeneralItemDTO { Id = 9, EnglishTitle = "Local-body AJK" },
        new GeneralItemDTO { Id = 10, EnglishTitle = "Local-body Islamabad" }
    };

    private SfDialog dlgForm;
    private SfDialog dlgPhaseForm;
    private ElectionPhaseDTO electionPhaseObj = new();
    private string ErrMsg = "";
    private string FormTitle = "Create Election";
    private bool isDlgVisible;
    private bool isPhaseDialogVisible;

    private List<ElectionDTO> objList;
    private string SaveButtonText = "Save";
    private ElectionDTO selectedObj = new();

    public string ToastContent = "Unable to delete Record, Related Record(s) Exist";

    private SfToast ToastObj;

    // AuthenticationState is available as a CascadingParameter
    [CascadingParameter] private Task<AuthenticationState> authenticationStateTask { get; set; }

    public SfGrid<ElectionDTO> Grid { get; set; }
    public int provinceId { get; set; } = 1;

    private void beforeOpen(BeforeOpenEventArgs args)
    {
        args.MaxHeight = null;
    }

    private void ClearData()
    {
        ErrMsg = "";
        selectedObj = new ElectionDTO();
    }

    public async Task CreatePhase(int electionId)
    {
        electionPhaseObj = new ElectionPhaseDTO { ElectionId = electionId, ElectionDate = DateTime.Today };
        await dlgPhaseForm.ShowAsync();
        //var user = (await authenticationStateTask).User;
        //var res = Service.CreatePhase(electionId, DateTime.Today, user.Identity.Name);
        //objList = await Service.GetAll();
        //StateHasChanged();
        //NavigationManager.NavigateTo("/elections");
    }

    public void CustomizeCell(QueryCellInfoEventArgs<ElectionDTO> args)
    {
        if (args.Column.Field == "UrduTitle")
        {
            args.Cell.AddClass(new[] { "urdu-column" });
            args.Cell.AddStyle(new[] { "font-size:16px" });
        }
    }

    private async Task DeleteRecord(int phaseId)
    {
        var para = new string[1] { "Are you want to delete this record?" };
        var res = await JSRuntime.InvokeAsync<bool>("confirm", para);
        if (res == false)
            return;
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        //return base.OnAfterRenderAsync(firstRender);
        if (firstRender)
        {
            var user = (await authenticationStateTask).User;
            if (user.Identity.IsAuthenticated)
            {
                //var dd =  await ProtectedSessionStore.GetAsync<ElectionStateDTO>("election");
                var dd = await localStorage.GetAsync<ElectionStateDTO>(await confreader.GetSettingValue("electionkey"));
                state.SetState(dd.Value);
            }
            else
            {
                try
                {
                    //await  ProtectedSessionStore.DeleteAsync("election");
                    await localStorage.DeleteAsync(await confreader.GetSettingValue("electionkey"));
                    state.SetState(
                        new ElectionStateDTO { ElectionId = 0, ElectionTitle = "", PhaseId = 0, PhaseTitle = "" });
                }
                catch (Exception)
                {
                    // ignored
                }

                NavigationManager.NavigateTo("");
            }

            StateHasChanged();
        }
    }

    protected override async Task OnInitializedAsync()
    {
        // var user = (await authenticationStateTask).User;
        // Get the objList for the current user
        // We access WeatherobjListervice using @Service
        objList = await Service.GetAll();
    }

    private void OpenCreateForm()
    {
        ErrMsg = "";
        selectedObj = new ElectionDTO
        {
            UrduTitle = "",
            EnglishTitle = "",
            StartDate = DateTime.Today,
            YearFrom = DateTime.Today.Year,
            YearTo = DateTime.Today.Year
        };
        SaveButtonText = "Save";
        FormTitle = "Create Election";

        dlgForm.ShowAsync();
    }

    private async void OpenEditForm(ElectionDTO st)
    {
        //selectedObj = new ElectionDTO
        //{
        //	Id = st.Id,
        //	YearFrom = st.YearFrom,
        //	Description = st.Description,
        //	YearTo = st.YearTo,
        //	StartDate = st.StartDate,
        //	ElectionId = st.ElectionId,
        //	PhaseTitle = st.PhaseTitle,
        //	EnglishTitle = st.EnglishTitle,
        //	UrduTitle = st.UrduTitle
        //};
        selectedObj = await Service.GetElectionInfo(st.Id);
        FormTitle = "Update Election";
        SaveButtonText = "Update";
        await dlgForm.ShowAsync();
    }

    private async Task SaveData()
    {
        var user = (await authenticationStateTask).User;
        try
        {
            await Service.CreateElection(selectedObj, user.Identity.Name);
            await dlgForm.HideAsync();
            objList = await Service.GetAll();

            await Grid.Refresh();
            StateHasChanged();
        }
        catch (Exception ex)
        {
            ErrMsg = ex.Message;
            if (ex.InnerException != null)
                ErrMsg += " - Detail: " + ex.InnerException.Message;

            await ToastObj.ShowAsync(new ToastModel
            {
                Title = "Error",
                Content = ErrMsg,
                CssClass = "e-toast-danger",
                ShowProgressBar = true,
                Timeout = 5000,
                ShowCloseButton = true
            });
        }
    }

    private async Task SavePhaseData()
    {
        var user = (await authenticationStateTask).User;
        try
        {
            await Service.CreateElectionPhase(electionPhaseObj, user.Identity.Name);
            await dlgPhaseForm.HideAsync();
            objList = await Service.GetAll();
            await Grid.Refresh();
            StateHasChanged();
        }
        catch (Exception ex)
        {
            ErrMsg = ex.Message;
            if (ex.InnerException != null)
                ErrMsg += " - Detail: " + ex.InnerException.Message;

            await ToastObj.ShowAsync(new ToastModel
            {
                Title = "Error",
                Content = ErrMsg,
                CssClass = "e-toast-danger",
                ShowProgressBar = true,
                Timeout = 5000,
                ShowCloseButton = true
            });
        }
    }

    private async Task TranslateToUrdu()
    {
        try
        {
            if (string.IsNullOrEmpty(selectedObj.UrduTitle))
                selectedObj.UrduTitle = await Translation.TranslateText(selectedObj.EnglishTitle);
        }
        catch (Exception)
        {
            // ignored
        }
    }

    private async Task CopyElection()
    {
        await Service.CopyElection(1, 1006, "jawaid");
    }
}