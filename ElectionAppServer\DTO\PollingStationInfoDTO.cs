﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace ElectionAppServer.DTO;

public class PollingStationInfoDTO
{
    public int Id { get; set; }
    public string Type { get; set; }
    public string EnglishTitle { get; set; }
    public string UrduTitle { get; set; }
    public string Code { get; set; }
    public string ProvinceEnglish { get; set; }
    public string ProvinceUrdu { get; set; }
    public string DivisionEnglish { get; set; }
    public string DivisionUrdu { get; set; }
    public string DistrictEnglish { get; set; }
    public string DistrictUrdu { get; set; }
    public string RegionEnglish { get; set; }
    public string RegionUrdu { get; set; }
    public string TownEnglish { get; set; }
    public string TownUrdu { get; set; }
    public string UCEnglish { get; set; }
    public string UCUrdu { get; set; }
    public List<PollingStationDetailDTO> PollingStations { get; set; }
    public string ElectionUrdu { get; set; }
    public string ElectionEnglish { get; set; }
    public string AssemblyUrdu { get; set; }
    public string AssemblyEnglish { get; set; }
}

public class PollingStationDetailDTO
{
    public int Id { get; set; }

    public int ConstituencyId { get; set; }

    [Required] [MaxLength(10)] public string Number { get; set; }

    [Required] [MaxLength(200)] public string EnglishTitle { get; set; }

    [Required] [MaxLength(200)] public string UrduTitle { get; set; }

    [Range(0, 99999999)] [Required] public int? MaleVoters { get; set; } = 0;

    [Range(0, 99999999)] [Required] public int? FemaleVoters { get; set; } = 0;

    public int TotalVoters => (MaleVoters ?? 0) + (FemaleVoters ?? 0);

    public int? Type { get; set; }

    public string PollingStationType
    {
        get
        {
            if (Type == 1) return "Male";
            if (Type == 2) return "Female";
            if (Type == 3) return "Combined";
            return "Not Defined";
        }
    }
}