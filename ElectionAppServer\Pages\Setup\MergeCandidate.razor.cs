﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ElectionAppServer.DTO;
using ElectionAppServer.Model;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Syncfusion.Blazor.Data;
using Syncfusion.Blazor.DropDowns;
using Syncfusion.Blazor.Grids;

namespace ElectionAppServer.Pages.Setup;

public partial class MergeCandidate
{
    private SfDropDownList<int?, GeneralItemDTO> ddlSource;
    private SfGrid<CandidateDTO> Grid;
    private bool IsGridVisible = false;
    private string KeepCandidateName = "007 - Jams Bond";
    public Query LocalDataQuery = new Query().Take(100);
    private List<CandidateDTO> objFilteredList;
    private int? RemoveCandidateId;
    private List<GeneralItemDTO> RemoveCandidatesList;
    private string TargetCandidate { get; set; }
    [Parameter] public int? KeepCandidateId { get; set; }
    [CascadingParameter] private Task<AuthenticationState> authenticationStateTask { get; set; }
    public ElectionType electionType { get; set; }

    protected override async Task OnParametersSetAsync()
    {
        await base.OnParametersSetAsync();
    }

    public void CustomizeCell(QueryCellInfoEventArgs<CandidateDTO> args)
    {
        if (args.Column.Field == "UrduName")
        {
            args.Cell.AddClass(new[] { "urdu-column" });
            args.Cell.AddStyle(new[] { "font-size:18px;line-height: 1.4;text-align: right" });
        }
    }

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        if (KeepCandidateId != null)
        {
            KeepCandidateName = await service.GetCandidateName(KeepCandidateId.Value);
            RemoveCandidatesList = await service.GetPossibleDuplicate(KeepCandidateId.Value);
            RemoveCandidatesList = RemoveCandidatesList.Where(m => m.Id != KeepCandidateId).ToList();
        }
    }


    private async Task FindCandidate()
    {
        var res = int.TryParse(TargetCandidate, out var cc);

        if (res)
        {
            var candidateExist = await service.CandidateExist(cc);
            if (!candidateExist)
                await DialogService.AlertAsync("Candidate does not exists", "Error");
            else
                RemoveCandidateId = cc;
        }

        await Task.CompletedTask;
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        //return base.OnAfterRenderAsync(firstRender);
        if (firstRender)
        {
            var user = (await authenticationStateTask).User;
            if (user.Identity is { IsAuthenticated: true })
            {
                //var dd =  await ProtectedSessionStore.GetAsync<ElectionStateDTO>("election");
                var dd = await localStorage.GetAsync<ElectionStateDTO>(await confreader.GetSettingValue("electionkey"));
                if (dd.Value is { ElectionType: not null })
                {
                    electionType = dd.Value.ElectionType.Value;
                    state.SetState(dd.Value);
                }
            }
            else
            {
                try
                {
                    //await  ProtectedSessionStore.DeleteAsync("election");
                    await localStorage.DeleteAsync(await confreader.GetSettingValue("electionkey"));
                    state.SetState(
                        new ElectionStateDTO
                        {
                            ElectionId = 0,
                            ElectionTitle = string.Empty,
                            PhaseId = 0,
                            PhaseTitle = string.Empty
                        });
                }
                catch (Exception)
                {
                    // ignored
                }

                NavigationManager.NavigateTo(string.Empty);
            }

            StateHasChanged();
        }
    }

    private async Task DoMerge()
    {
        var res = await DialogService.ConfirmAsync(
            $"Are you sure want to merge candidates? Keep Candidate Id: {KeepCandidateId}, Need to Remove: {RemoveCandidateId} ",
            "Confirm to Merge");
        if (res)
        {
            var user = (await authenticationStateTask).User;
            var msg = await service.MergeCandidate(KeepCandidateId, RemoveCandidateId, user.Identity.Name);

            if (msg == "OK")
            {
                RemoveCandidateId = null;
                RemoveCandidatesList = await service.GetPossibleDuplicate(KeepCandidateId.Value);
                RemoveCandidatesList = RemoveCandidatesList.Where(m => m.Id != KeepCandidateId).ToList();
            }
            else
            {
                await DialogService.AlertAsync(msg, "Error");
            }
        }
    }
}