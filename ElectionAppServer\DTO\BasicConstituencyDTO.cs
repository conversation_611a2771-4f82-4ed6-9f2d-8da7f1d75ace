﻿using System.ComponentModel.DataAnnotations;

namespace ElectionAppServer.DTO;

public class BasicConstituencyDTO
{
    // This class contains following required fields
    // MaleVoters, FemaleVoers, TotalPollingStations
    // One optional field Population
    [Required(ErrorMessage = "Male Voters Are Required")]
    [Range(0, 9999999, ErrorMessage = "Value must equeal or grater than 0")]
    public int? MaleVoters { get; set; }

    [Required(ErrorMessage = "Male Voters Are Required")]
    [Range(0, 9999999, ErrorMessage = "Value must equeal or grater than 0")]
    public int? FemaleVoters { get; set; }

    [Required(ErrorMessage = "Male Voters Are Required")]
    [Range(0, 9999, ErrorMessage = "Value must equeal or grater than 0")]
    public int? TotalPollingStations { get; set; }

    public int? Population { get; set; }
    public bool IsRePoll { get; set; }
    public bool IsBigContest { get; set; }

    public int ConstituencyId { get; set; }
    public int? YouthVoters { get; set; }
    public int? RejectedVotes { get; set; }
}