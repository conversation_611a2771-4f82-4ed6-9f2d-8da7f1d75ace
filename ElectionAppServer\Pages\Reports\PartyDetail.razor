﻿@layout ReportLayout
@page "/report/party/{PartyId:int}"
@using FilterType = Syncfusion.Blazor.Grids.FilterType
@inherits OwningComponentBase<PartyService>

<style>
    body {
        user-select: none;
    }
</style>

@{
    var initial = new[] { "Election", "Assembly" };
}

@if (Info == null)
{
    <p>Loading</p>
}
else
{
    <div class="card bg-info mb-3">
        <div class="card-header" style="display: flex; justify-content: space-between;align-items: center">
            <img onerror="this.onerror=null; this.src='media/flags/blank.jpg'" src="/media/flags/@(Info.Id).jpg" style="border: 1px solid white; border-radius: 6px; max-width: 120px;margin-right: 6px; -webkit-box-shadow: 3px 3px 9px 1px rgba(0,0,0,0.4);-moz-box-shadow: 3px 3px 9px 1px rgba(0,0,0,0.4);box-shadow: 3px 3px 9px 1px rgba(0,0,0,0.4); height:81px" alt="" align="left"/>
            <img onerror="this.onerror=null; this.src='media/flags/blank.jpg'" src="/media/symbols/@(Info.SymbolId).jpg" style="border: 1px solid white; border-radius: 6px; max-width: 120px;margin-right: 6px; -webkit-box-shadow: 3px 3px 9px 1px rgba(0,0,0,0.4);-moz-box-shadow: 3px 3px 9px 1px rgba(0,0,0,0.4);box-shadow: 3px 3px 9px 1px rgba(0,0,0,0.4); height: 81px" alt="" align="left"/>
            <div style="flex:1; font-size:30px; font-weight:bold">
                @Info.EnglishTitle <br/>
                <span class="urdu-cap" style="font-size:34px; text-align: right;direction: rtl">@Info.UrduTitle</span>
            </div>
        </div>
        <div class="card-body bg-light" style="color:black">
            <div class="row">
                <div class="col rptfield">
                    <div>Short Title (English): </div>
                    <div class="rptdata">
                        <b>@Info.ShortEnglishTitle</b>
                    </div>
                </div>
                <div class="col rptfield">
                    <div>Short Title (Urdu): </div>
                    <div class="rptdata">
                        <b>@Info.ShortUrduTitle</b>
                    </div>
                </div>
                <div class="col rptfield">
                    <div>Date of Creation: </div>
                    <div class="rptdata">
                        <b>@(Info.DateOfCreation == null ? "NA" : Info.DateOfCreation.Value.ToString("d MMM, yyyy"))</b>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col rptfield">
                    <div>Party Leader: </div>
                    <div class="rptdata">
                        @if (!string.IsNullOrEmpty(Info.LeaderPicId))
                        {
                            <img onerror="this.onerror=null; this.src='media/flags/blank.jpg'" align="left" src="/media/Leaders/@(Info.LeaderPicId)" style="max-height: 150px; max-width: 150px; margin-right: 5px; border: 1px solid gray; border-radius: 5px;"/>
                        }
                        @if (!string.IsNullOrEmpty(Info.Leader))
                        {
                            <b>@Info.Leader @(string.IsNullOrEmpty(Info.Designation) ? "" : $" - ({Info.Designation})")</b>
                        }
                        else
                        {
                            <span>NA</span>
                        }
                    </div>
                </div>
                <div class="col rptfield">
                    <div>Current Leader: </div>
                    <div class="rptdata">
                        @if (!string.IsNullOrEmpty(Info.CurrentLeaderPicId))
                        {
                            <img onerror="this.onerror=null; this.src='media/flags/blank.jpg'" align="left" src="/media/Leaders/@(Info.CurrentLeaderPicId)" style="max-height: 150px; max-width: 150px; margin-right: 5px; border: 1px solid gray; border-radius: 5px;"/>
                        }
                        @if (!string.IsNullOrEmpty(Info.CurrentLeader))
                        {
                            <b>@Info.CurrentLeader - @(string.IsNullOrEmpty(Info.CurrentLeaderDesignation) ? "" : $" - ({Info.CurrentLeaderDesignation})")</b>
                        }
                        else
                        {
                            <span>NA</span>
                        }
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col rptfield">
                    <div>Address: </div>
                    <div class="rptdata">
                        <b>@(string.IsNullOrEmpty(Info.Address) ? "NA" : Info.Address)</b>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col rptfield">
                    <div>Trivia: </div>
                    <div class="rptdata">
                        <b>@(string.IsNullOrEmpty(Info.Trivia) ? "NA" : Info.Trivia)</b>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="card bg-success mb-3">
        <div class="card-header" style="display: flex; justify-content: space-between;align-items: center">
            <div>Coalition</div>
            <div class="urdu-cap" style="text-align: right;direction: rtl">اتحاد</div>
        </div>
        <div class="card-body bg-light" style="color:black">
            @if (Info.Coalition == null || Info.Coalition.Count == 0)
            {
                <p>No coalition found</p>
            }
            else
            {
                <div class="row">
                    @foreach (var cc in Info.Coalition)
                    {
                        <div class="col-4">
                            <div class="card bg-secondary mb-3">
                                <div class="card-header">
                                    @cc.Election - @cc.Coalition
                                </div>
                                <div class="card-body bg-light" style="color:black">
                                    <b>Coalition with:</b>
                                    <ul>
                                        @foreach (var pp in cc.Parties)
                                        {
                                            <li>
                                                <a href="/report/party/@(pp.Id)">@((MarkupString)pp.EnglishTitle)</a>
                                            </li>
                                        }
                                    </ul>
                                </div>
                            </div>
                        </div>
                    }
                </div>
            }
        </div>
    </div>

    <div class="card bg-info mb-3">
        <div class="card-header">
            Nominations
        </div>
        <div class="card-body bg-light" style="color:black">
            <SfGrid AllowTextWrap="true" AllowSorting="true" AllowFiltering="true" AllowGrouping="true" DataSource="Info.Nominations">
                <GridGroupSettings Columns="@initial"></GridGroupSettings>
                <GridFilterSettings Type="FilterType.Excel"></GridFilterSettings>
                <GridColumns>
                    <GridColumn DisableHtmlEncode="false" Field=@nameof(NominationRptDTO.Election) HeaderText="Election"></GridColumn>
                    <GridColumn DisableHtmlEncode="false" Field=@nameof(NominationRptDTO.Assembly) HeaderText="Assembly"></GridColumn>
                    <GridColumn DisableHtmlEncode="false" Field=@nameof(NominationRptDTO.Phase) HeaderText="Phase"></GridColumn>
                    <GridColumn DisableHtmlEncode="false" Field=@nameof(NominationRptDTO.Date) HeaderText="Date" Format="d MMM yyyy"></GridColumn>
                    <GridColumn DisableHtmlEncode="false" Field=@nameof(NominationRptDTO.ConstituencyCode) HeaderText="Code"></GridColumn>
                    @*<GridColumn DisableHtmlEncode="false" Field=@nameof(NominationRptDTO.Constituency) HeaderText="Constituency"></GridColumn>*@
                    <GridColumn HeaderText="Constituency">
                        <Template Context="cc">
                            @{
                                if (cc is NominationRptDTO obj)
                                {
                                    <a target="@($"cc{obj.ConstituencyId}")" href="/report/constituency/@(obj.ConstituencyId)">@((MarkupString)obj.Constituency)</a>
                                }

                            }
                        </Template>
                    </GridColumn>
                    <GridColumn DisableHtmlEncode="false" Field=@nameof(NominationRptDTO.SeatType) HeaderText="Seat"></GridColumn>
                    @*<GridColumn DisableHtmlEncode="false" Field=@nameof(NominationRptDTO.Candidate) HeaderText="Candidate"></GridColumn>*@
                    <GridColumn HeaderText="Candidate">
                        <Template Context="cc">
                            @{
                                if (cc is NominationRptDTO obj)
                                {
                                    <a target="@($"can{obj.CandidateId}")" href="/report/candidate/@(obj.CandidateId)">@((MarkupString)obj.Candidate)</a>
                                }


                            }
                        </Template>
                    </GridColumn>
                    <GridColumn DisableHtmlEncode="false" Field=@nameof(NominationRptDTO.Symbol) HeaderText="Symbol"></GridColumn>
                    <GridColumn DisableHtmlEncode="false" Field=@nameof(NominationRptDTO.Weight) HeaderText="Weight"></GridColumn>
                    <GridColumn DisableHtmlEncode="false" Field=@nameof(NominationRptDTO.Votes) HeaderText="Votes"></GridColumn>
                </GridColumns>
            </SfGrid>
        </div>
    </div>
}

@code {
    [Parameter] public int PartyId { get; set; }

    public PartyRptDTO Info { get; set; }

    protected override async Task OnParametersSetAsync()
    {
        await base.OnParametersSetAsync();
        Info = await Service.GetPartyRptInfo(PartyId);
    }

}