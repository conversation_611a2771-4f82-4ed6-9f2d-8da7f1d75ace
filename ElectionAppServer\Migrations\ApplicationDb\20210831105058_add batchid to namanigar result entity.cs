﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace ElectionAppServer.Migrations.ApplicationDb
{
    public partial class addbatchidtonamanigarresultentity : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "BatchId",
                table: "NamanigarResults",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.CreateIndex(
                name: "IX_NamanigarResults_BatchId",
                table: "NamanigarResults",
                column: "BatchId");

            migrationBuilder.CreateIndex(
                name: "IX_NamanigarResultLogs_BatchId",
                table: "NamanigarResultLogs",
                column: "BatchId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_NamanigarResults_BatchId",
                table: "NamanigarResults");

            migrationBuilder.DropIndex(
                name: "IX_NamanigarResultLogs_BatchId",
                table: "NamanigarResultLogs");

            migrationBuilder.DropColumn(
                name: "BatchId",
                table: "NamanigarResults");
        }
    }
}
