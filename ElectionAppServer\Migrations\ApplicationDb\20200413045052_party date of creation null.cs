﻿using Microsoft.EntityFrameworkCore.Migrations;
using System;

namespace ElectionAppServer.Migrations.ApplicationDb
{
	public partial class partydateofcreationnull : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.AlterColumn<DateTime>(
				 name: "DateOfCreation",
				 table: "Parties",
				 nullable: true,
				 oldClrType: typeof(DateTime),
				 oldType: "datetime2");
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.AlterColumn<DateTime>(
				 name: "DateOfCreation",
				 table: "Parties",
				 type: "datetime2",
				 nullable: false,
				 oldClrType: typeof(DateTime),
				 oldNullable: true);
		}
	}
}
