﻿using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using ElectionAppServer.DTO;

namespace ElectionAppServer.Model;

public class ElectionAssemblySeat
{
    [ForeignKey("ElectionAssembly")] public int ElectionAssemblyId { get; set; }

    [ForeignKey("SeatType")] public int SeatTypeId { get; set; }

    public int? SeatCount { get; set; }

    [DefaultValue(StructureType.UnionCouncil)]
    public StructureType ConstituencyType { get; set; } = StructureType.UnionCouncil;

    public virtual ElectionAssembly ElectionAssembly { get; set; }
    public virtual SeatType SeatType { get; set; }

    #region Audit Log Fields

    public DateTime CreatedDate { get; set; } = DateTime.Now;
    public DateTime? ModifiedDate { get; set; }

    [StringLength(450)] public string CreatedBy { get; set; }

    [StringLength(450)] public string ModifiedBy { get; set; }

    #endregion Audit Log Fields
}