﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using ElectionAppServer.DTO;
using ElectionAppServer.Model;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Syncfusion.Blazor.Grids;
using Syncfusion.Blazor.Notifications;
using Syncfusion.Blazor.Popups;

namespace ElectionAppServer.Pages.AdminUnits;

public partial class Districts
{
    private List<AdminDivisionDTO> divisionsList;
    private SfDialog dlgForm;
    private ElectionType electionType;
    private bool isDlgVisible;
    private List<ElcStructureDTO> objList;

    private List<RegionDTO> regionsList;

    //private string SaveButtonText = "Save";
    //private string FormTitle = "Create District";
    private ElcStructureDTO selectedObj;
    public string ToastContent = "Unable to delete Record, Related Record(s) Exist";

    private SfToast ToastObj;

    // AuthenticationState is available as a CascadingParameter
    [CascadingParameter] private Task<AuthenticationState> authenticationStateTask { get; set; }

    [Parameter] public int provinceId { get; set; }
    public List<GeneralItemDTO> BredCrum { get; set; }
    public int electionId { get; set; }
    public List<GeneralItemDTO> languagesList { get; set; }
    public List<GeneralItemDTO> CasteList { get; set; }
    public string ElectionTitle { get; set; }
    [Parameter] public int? cid { get; set; }
    public SfGrid<ElcStructureDTO> Grid { get; set; }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        //return base.OnAfterRenderAsync(firstRender);
        if (firstRender)
        {
            var user = (await authenticationStateTask).User;
            if (user.Identity == null) return;
            if (user.Identity.IsAuthenticated)
            {
                //var dd =  await ProtectedSessionStore.GetAsync<ElectionStateDTO>("election");
                var dd = await localStorage.GetAsync<ElectionStateDTO>(await confreader.GetSettingValue("electionkey"));
                //regionsList = await Service.GetRegions((int)selectedObj.ParentId);
                regionsList = await Service.GetRegions(provinceId);
                divisionsList = await Service.GetDivisions(provinceId);

                if (dd.Value is { ElectionType: not null })
                {
                    electionType = dd.Value.ElectionType.Value;
                    state.SetState(dd.Value);
                }
            }
            else
            {
                try
                {
                    //await  ProtectedSessionStore.DeleteAsync("election");
                    await localStorage.DeleteAsync(await confreader.GetSettingValue("electionkey"));
                    state.SetState(new ElectionStateDTO
                        { ElectionId = 0, ElectionTitle = "", PhaseId = 0, PhaseTitle = "" });
                }
                catch (Exception)
                {
                    // ignored
                }

                NavigationManager.NavigateTo("");
            }

            StateHasChanged();
        }
    }

    protected override async Task OnInitializedAsync()
    {
        var user = (await authenticationStateTask).User;
        objList = await Service.GetList(StructureType.District, provinceId, state.state.PhaseId, cid);
        BredCrum = await Service.GetBreadCrum(StructureType.District, provinceId);
        languagesList = await Service.GetLanguages();
        CasteList = await Service.GetCastesList();

        electionId = BredCrum[0].Id;
        ElectionTitle = await Service.GetElectionTitle(electionId);
        ClearData();
    }

    private async Task OpenCreateForm()
    {
        ClearData();
        selectedObj = new ElcStructureDTO
            { ProvinceId = provinceId, StructureType = StructureType.District, ElectionId = state.state.ElectionId };
        await dlgForm.ShowAsync();
        //SaveButtonText = "Create";
    }

    private async Task OpenEditForm(ElcStructureDTO st)
    {
        st.ProvinceId = provinceId;
        st.ElectionId = state.state.ElectionId;
        st.StructureType = StructureType.District;
        selectedObj = await Service.GetAdminUnitDetail(st.Id, provinceId, StructureType.District, state.state.PhaseId);
        await dlgForm.ShowAsync();
        //SaveButtonText = "Update";
    }

    //private void OpenEditForm(ElcStructureDTO st)
    //{
    //	selectedObj = new ElcStructureDTO
    //	{
    //		Id = st.Id,
    //		//CasteId = st.CasteId,
    //		Castes = st.Castes,
    //		Code = st.Code,
    //		EnglishTitle = st.EnglishTitle,
    //		FemaleVoters = st.FemaleVoters,
    //		TotalPollingStations = st.TotalPollingStations,
    //		ImportantAreas = st.ImportantAreas,
    //		ImportantPoliticalPersonalities = st.ImportantPoliticalPersonalities,
    //		//LanguageId = st.LanguageId,
    //		Languages = st.Languages,
    //		MaleVoters = st.MaleVoters,
    //		ParentId = divisionId,
    //		Problems = st.Problems,
    //		RuralAreaPer = st.RuralAreaPer,
    //		Trivia = st.Trivia,
    //		UrbanAreaPer = st.UrbanAreaPer,
    //		UrduTitle = st.UrduTitle,
    //		StructureType = StructureType.District,
    //		Population = st.Population,
    //		AverageHouseholdIncome = st.AverageHouseholdIncome,
    //		LiteracyRate = st.LiteracyRate,
    //		MajorityIncomeSource = st.MajorityIncomeSource,
    //		GeneralTrivia = st.GeneralTrivia,
    //		Area = st.Area,
    //		RegionId = st.RegionId
    //	};
    //	dlgForm.ShowAsync();
    //	SaveButtonText = "Update";
    //}

    private void ClearData()
    {
        selectedObj = new ElcStructureDTO
            { ParentId = provinceId, StructureType = StructureType.District, ElectionId = electionId };
    }

    private async Task SaveData()
    {
        var user = (await authenticationStateTask).User;

        try
        {
            await Service.Save(selectedObj, user.Identity.Name, state.state.PhaseId);
            objList = await Service.GetList(StructureType.District, provinceId, state.state.PhaseId, cid);
            await Grid.Refresh();
            ClearData();
            await dlgForm.HideAsync();
        }
        catch (Exception ex)
        {
            var mm = new ToastModel
            {
                Title = "Error",
                Content = ex.Message,
                CssClass = "e-toast-danger",
                ShowProgressBar = true,
                Timeout = 5000,
                ShowCloseButton = true
            };
            if (ex.InnerException != null) mm.Content += "<br/>" + ex.InnerException.Message;
            await ToastObj.ShowAsync(mm);
        }
    }

    private async Task DeleteRecord(int Id)
    {
        var para = new[] { "Are you want to delete this record?" };
        var res = await JSRuntime.InvokeAsync<bool>("confirm", para);
        if (res == false)
            return;

        try
        {
            var msg2 = await Service.Delete(Id);
            objList = await Service.GetList(StructureType.District, provinceId, state.state.PhaseId, cid);
            await Grid.Refresh();
            ClearData();
        }
        catch (Exception ex)
        {
            var mm = new ToastModel
            {
                Title = "Error",
                Content = ex.Message,
                CssClass = "e-toast-danger",
                ShowProgressBar = true,
                Timeout = 5000,
                ShowCloseButton = true
            };
            await ToastObj.ShowAsync(mm);
        }
    }

    /*
        private async Task TranslateToUrdu()
        {
            try
            {
                if (string.IsNullOrEmpty(selectedObj.UrduTitle))
                    selectedObj.UrduTitle = await Translation.TranslateText(selectedObj.EnglishTitle);
            }
            catch (Exception)
            {
                // ignore it
            }
            //if (string.IsNullOrEmpty(selectedObj.UrduTitle))
        }
    */

    public void CustomizeCell(QueryCellInfoEventArgs<ElcStructureDTO> args)
    {
        if (args.Column.Field is "UrduTitle" or "RegionUrdu")
        {
            args.Cell.AddClass(new[] { "urdu-column" });
            args.Cell.AddStyle(new[] { "font-size:18px;line-height: 1.4;text-align: right" });
        }
    }

    private void BeforeOpen(BeforeOpenEventArgs args)
    {
        args.MaxHeight = null;
    }
}