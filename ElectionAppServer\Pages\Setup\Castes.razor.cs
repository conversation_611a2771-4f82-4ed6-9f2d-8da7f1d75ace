using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using ElectionAppServer.Data;
using ElectionAppServer.DTO;
using ElectionAppServer.Model;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.JSInterop;
using Syncfusion.Blazor.Grids;
using Syncfusion.Blazor.Notifications;
using Syncfusion.Blazor.Popups;

namespace ElectionAppServer.Pages.Setup;

public partial class Castes
{
    private SfDialog dlgForm;
    private bool isDlgVisible;
    private List<Caste> objList;

    private string SaveButtonText = "Save";

    //--> No in use //private string FormTitle = "Create Caste";
    private Caste selectedObj = new();
    public string ToastContent = "Unable to delete Record, Related Record(s) Exist";

    private SfToast ToastObj;

    // AuthenticationState is available as a CascadingParameter
    [CascadingParameter] private Task<AuthenticationState> authenticationStateTask { get; set; }

    public SfGrid<Caste> Grid { get; set; }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        //return base.OnAfterRenderAsync(firstRender);
        if (firstRender)
        {
            var user = (await authenticationStateTask).User;
            if (user.Identity.IsAuthenticated)
            {
                //var dd =  await ProtectedSessionStore.GetAsync<ElectionStateDTO>("election");
                var dd = await localStorage.GetAsync<ElectionStateDTO>(await confreader.GetSettingValue("electionkey"));
                state.SetState(dd.Value);
            }
            else
            {
                try
                {
                    //await  ProtectedSessionStore.DeleteAsync("election");
                    await localStorage.DeleteAsync(await confreader.GetSettingValue("electionkey"));
                    state.SetState(new ElectionStateDTO
                        { ElectionId = 0, ElectionTitle = "", PhaseId = 0, PhaseTitle = "" });
                }
                catch (Exception)
                {
                    // ignored
                }

                NavigationManager.NavigateTo("");
            }

            StateHasChanged();
        }
    }

    protected override async Task OnInitializedAsync()
    {
        // Get the current user
        var user = (await authenticationStateTask).User;
        // Get the objList for the current user
        // We access WeatherobjListervice using @Service
        objList = await Service.GetList();
    }

    private void OpenCreateForm()
    {
        selectedObj = new Caste();
        dlgForm.ShowAsync();
        SaveButtonText = "Create";
    }

    private void OpenEditForm(Caste st)
    {
        selectedObj = new Caste { Id = st.Id, EnglishTitle = st.EnglishTitle, UrduTitle = st.UrduTitle };
        dlgForm.ShowAsync();
        SaveButtonText = "Update";
    }

    private void ClearData()
    {
        selectedObj = new Caste();
    }

    private async Task SaveCasteData()
    {
        var user = (await authenticationStateTask).User;
        try
        {
            await Service.Save(selectedObj, user.Identity.Name);
            objList = await Service.GetList();
            ClearData();
            await Grid.Refresh();
            await dlgForm.HideAsync();
        }
        catch (Exception ex)
        {
            var m = new ToastModel { Content = ex.Message, Title = "Error" };
            await ToastObj.ShowAsync(m);
        }
    }

    //private async Task SaveCasteData2()
    //{
    //   // Close the Popup
    //   // ShowPopup = false;
    //   // Get the current user
    //   var user = (await authenticationStateTask).User;
    //   // A new forecast will have the Id set to 0
    //   if (selectedObj.Id == 0)
    //   {
    //      // Create forecast
    //      Caste st = new Caste();
    //      st.UrduTitle = selectedObj.UrduTitle.Trim();
    //      st.EnglishTitle = selectedObj.EnglishTitle.Trim();
    //      st.CreatedDate = DateTime.Now;
    //      st.ModifiedDate = DateTime.Now;
    //      st.CreatedBy = user.Identity.Name;
    //      st.ModifiedBy = user.Identity.Name;
    //      //Service.CreateCaste(st);
    //      var res = Service.CreateCaste(st);
    //      objList = await Service.GetList();
    //      ClearData();
    //      await dlgForm.HideAsync();
    //   }
    //   else
    //   {
    //      // Create forecast
    //      Caste st = new Caste();
    //      st.UrduTitle = selectedObj.UrduTitle.Trim();
    //      st.EnglishTitle = selectedObj.EnglishTitle.Trim();
    //      st.CreatedDate = DateTime.Now;
    //      st.ModifiedDate = DateTime.Now;
    //      st.CreatedBy = user.Identity.Name;
    //      st.ModifiedBy = user.Identity.Name;
    //      st.Id = selectedObj.Id;
    //      //Service.CreateCaste(st);
    //      var res = Service.UpdateCaste(st);
    //      objList = await Service.GetList();
    //      ClearData();
    //      await dlgForm.HideAsync();
    //   }
    //}
    private async Task DeleteRecord(int Id)
    {
        var res = await JSRuntime.InvokeAsync<bool>("confirm", "Are you want to delete this record?");
        if (res == false)
            return;
        try
        {
            var msg2 = await Service.DeleteCaste(Id);
            objList = await Service.GetList();
            await Grid.Refresh();
            ClearData();
        }
        catch (Exception ex)
        {
            var m = new ToastModel { Content = ex.Message, Title = "Error" };
            await ToastObj.ShowAsync(m);
        }
    }

    private async Task TranslateToUrdu()
    {
        try
        {
            if (string.IsNullOrEmpty(selectedObj.UrduTitle))
                selectedObj.UrduTitle = await Translation.TranslateText(selectedObj.EnglishTitle);
        }
        catch (Exception)
        {
            // ignored
            //var msg = ex.Message;
            //if (ex.InnerException != null)
            //   msg += "  Detail: " + ex.InnerException.Message;
            //var m = new ToastModel { Title = "Error", Content = msg };
            //await ToastObj.ShowAsync(m);
        }
    }

    private void beforeOpen(BeforeOpenEventArgs args)
    {
        args.MaxHeight = null;
    }
}