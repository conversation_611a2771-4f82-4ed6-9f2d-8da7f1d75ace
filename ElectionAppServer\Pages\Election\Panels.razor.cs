﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using ElectionAppServer.Data;
using ElectionAppServer.DTO;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Components.Web;
using Syncfusion.Blazor.DropDowns;
using Syncfusion.Blazor.Notifications;
using Syncfusion.Blazor.Popups;

namespace ElectionAppServer.Pages.Election;

public partial class Panels
{
    private readonly bool IsFormOpen = false;

    private SfDialog dlgForm;
    private List<PannelNominationsDTO> panelCandidates;
    private PanelFormDTO selectedObj = new();
    [CascadingParameter] private Task<AuthenticationState> authenticationStateTask { get; set; }
    private SfToast ToastObj { get; set; }

    private List<GeneralItemDTO> seatType_list { get; set; }
    private int? seatTypeId { get; set; }

    private List<GeneralItemDTO> Assemblies_list { get; set; }
    private int? assemblyId { get; set; }
    public int electionId { get; set; }


    private List<GeneralItemDTO> panel_list { get; set; }
    private int? panelId { get; set; }

    private async Task OnPanelSelect(ChangeEventArgs<int?, GeneralItemDTO> args)
    {
        panelId = args.Value;
        await FillPanelCandidates();
        await Task.CompletedTask;
    }

    private async Task OnSeatTypeValueChange(ChangeEventArgs<int?, GeneralItemDTO> args)
    {
        selectedObj.SeatTypeId = args.Value;
        StateHasChanged();
        await Task.CompletedTask;
    }

    private async Task OpenCreateForm(MouseEventArgs arg)
    {
        selectedObj = new PanelFormDTO();
        selectedObj.PanelId = panelId;
        selectedObj.AssemblyId = assemblyId;
        selectedObj.PhaseId = state.state.PhaseId;
        await dlgForm.ShowAsync();
        StateHasChanged();
    }

    private async Task TranslateToUrdu()
    {
        try
        {
            if (string.IsNullOrEmpty(selectedObj.CandidateUrduName))
                selectedObj.CandidateUrduName = await Translation.TranslateText(selectedObj.CandidateEnglishName);
        }
        catch (Exception)
        {
            // ignored
        }
    }


    public async Task OnAssemblySelect(ChangeEventArgs<int?, GeneralItemDTO> args)
    {
        assemblyId = args.Value;
        await FillPanelCandidates();
    }

    public async Task FillPanelCandidates()
    {
        if (assemblyId != null && panelId != null)
            panelCandidates = await Service.GetPanelCandidates(state.state.PhaseId, (int)assemblyId, (int)panelId);
        else
            panelCandidates = new List<PannelNominationsDTO>();
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        //return base.OnAfterRenderAsync(firstRender);
        if (firstRender)
        {
            var user = (await authenticationStateTask).User;
            if (user.Identity.IsAuthenticated)
            {
                var dd = await localStorage.GetAsync<ElectionStateDTO>(await confreader.GetSettingValue("electionkey"));

                electionId = dd.Value.ElectionId;

                state.SetState(dd.Value);
                await FillPhaseAndPanels();
                StateHasChanged();
            }
            else
            {
                try
                {
                    //await  ProtectedSessionStore.DeleteAsync("election");
                    await localStorage.DeleteAsync(await confreader.GetSettingValue("electionkey"));
                    state.SetState(new ElectionStateDTO
                        { ElectionId = 0, ElectionTitle = "", PhaseId = 0, PhaseTitle = "" });
                }
                catch (Exception)
                {
                    // ignored
                }
                //NavigationManager.NavigateTo("");
            }

            StateHasChanged();
        }
    }

    private async Task FillPhaseAndPanels()
    {
        Assemblies_list = await Service.GetAssemblies(electionId);
        panel_list = await Service.GetPannelList();
        seatType_list = await Service.GetSeatTypes();
    }

    private async Task OpenDelteForm(int id, int order)
    {
        var user = "jawaid";
        var paras = new[] { "Are you sure want to delete this member?" };
        var res = await JSRuntime.InvokeAsync<bool>("confirm", paras);
        if (res)
        {
            var op = await Service.DeleteMember(id, order, user);
            if (op == "OK")
            {
                await FillPanelCandidates();
            }
            else
            {
                var tm = new ToastModel
                {
                    Title = "Error", Content = op, CssClass = "e-toast-danger", ShowProgressBar = true, Timeout = 5000,
                    ShowCloseButton = true
                };
                await ToastObj.ShowAsync(tm);
            }
        }
    }

    private async Task MoveDown(int id, int order)
    {
        var user = "jawaid";
        var resp = await Service.MoveDown(id, order, user);
        await FillPanelCandidates();
    }

    private async Task MoveUp(int id, int order)
    {
        var user = "jawaid";
        var resp = await Service.MoveUp(id, order, user);
        await FillPanelCandidates();
    }

    private async Task OpenEditForm(int id)
    {
        selectedObj = await Service.GetCandidateInfo(id);
        await dlgForm.ShowAsync();
        StateHasChanged();
    }

    private async Task SavePanelCandidate()
    {
        selectedObj.CandidateEnglishName = (selectedObj.CandidateEnglishName ?? "").Trim();
        selectedObj.CandidateUrduName = (selectedObj.CandidateUrduName ?? "").Trim();
        var msg = await Service.SaveCandiadte(selectedObj, "jawaid");
        if (msg != "OK")
        {
            var tm = new ToastModel
            {
                Title = "Error", Content = msg, CssClass = "e-toast-danger", ShowProgressBar = true, Timeout = 5000,
                ShowCloseButton = true
            };
            await ToastObj.ShowAsync(tm);
        }
        else
        {
            await FillPanelCandidates();
            await dlgForm.HideAsync();
        }
    }
}