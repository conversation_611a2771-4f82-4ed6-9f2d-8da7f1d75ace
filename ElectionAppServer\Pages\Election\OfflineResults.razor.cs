﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ElectionAppServer.DTO;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.SignalR.Client;
using Syncfusion.Blazor.DropDowns;
using Syncfusion.Blazor.Notifications;

namespace ElectionAppServer.Pages.Election;

public partial class OfflineResults
{
    private HubConnection hubConnection;

    //private List<NamanigarDTO> NamanigarList { get; set; }
    private List<GeneralItemDTO> ResultSourceList = new();
    private SfToast ToastObj;
    private List<GeneralItemDTO> AssemblyTypesList { get; set; }
    private List<GeneralItemDTO> AssembliesList { get; set; }
    [CascadingParameter] private Task<AuthenticationState> AuthenticationStateTask { get; set; }
    private int ChangePollingStations { get; set; }
    private List<GeneralItemDTO> ConstituenciesList { get; set; }
    private string EditMode { get; } = "";
    private bool IsConnected => hubConnection.State == HubConnectionState.Connected;


    private int NewResPollingStations
    {
        get
        {
            return EditMode switch
            {
                "Add" => ResultDetail[0].ResultPollingStations + ChangePollingStations,
                "Update" => ChangePollingStations,
                _ => ResultDetail[0].ResultPollingStations
            };
        }
    }

    private List<ResultDetailDTO> ResultDetail { get; set; } = new();

    //public int NewResPollingStations { get; set; }
    private List<GeneralItemDTO> SeatTypeList { get; set; }

    private int? SelectedAssemblyId { get; set; }

    private string SelectedAssemblyStrId { get; set; }

    //private int? SelectedNamanigarId { get; set; }
    private int? SelectedSeatTypeId { get; set; }
    private int? SelectedStructureId { get; set; }
    private int? SelectedSourceId { get; set; }
    private List<ItemDTO> StrAssembliesList { get; set; }
    private string UserId { get; set; }

    public ResultLogInfoDTO LastLog { get; set; }


    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        //return base.OnAfterRenderAsync(firstRender);
        if (firstRender)
        {
            var user = (await AuthenticationStateTask).User;
            if (user.Identity is { IsAuthenticated: true })
            {
                //ElectionsList = await Service.GetAll();
                //var dd =  await ProtectedSessionStore.GetAsync<ElectionStateDTO>("election");
                var dd = await localStorage.GetAsync<ElectionStateDTO>(await confreader.GetSettingValue("electionkey"));
                state.SetState(dd.Value);
                var info = await _UserManager.GetUserAsync(user);
                if (info != null) UserId = info.Id;
                await Task.CompletedTask;
                await FillDDLs();
                StateHasChanged();
            }
            else
            {
                try
                {
                    //await  ProtectedSessionStore.DeleteAsync("election");
                    await localStorage.DeleteAsync(await confreader.GetSettingValue("electionkey"));
                    state.SetState(new ElectionStateDTO
                        { ElectionId = 0, ElectionTitle = "", PhaseId = 0, PhaseTitle = "" });
                    StateHasChanged();
                }
                catch (Exception)
                {
                    // ignored
                }
            }
        }
    }

    protected override async Task OnInitializedAsync()
    {
        hubConnection = new HubConnectionBuilder()
            .WithUrl(NavigationManager.ToAbsoluteUri("/resultshub"))
            .Build();
        //hubConnection.On<ResultDTO>("ReceiveResult", (result) =>
        //{
        //	var ok = result;
        //});
        await hubConnection.StartAsync();
        //var user = (await authenticationStateTask).User;
        //var info = await _UserManager.GetUserAsync(user);
        //UserId = info.Id;
        await Task.CompletedTask;
    }

    //protected override async Task OnParametersSetAsync()
    //{
    //    if (SelectedAssemblyId != null && SelectedStructureId != null && SelectedSeatTypeId != null)
    //    {
    //        AssemblyTypesList = await Service.GetUserAssemblyTypes(UserId, state.state.ElectionId, state.state.PhaseId);
    //        //
    //    }
    //}


    private async Task FillDDLs()
    {
        //AssemblyTypesList = await Service.GetUserAssemblyTypes(UserId, state.state.ElectionId, state.state.PhaseId);
        AssembliesList = await Service.GetUserAssemblies(UserId, state.state.ElectionId, state.state.PhaseId);
        ResultSourceList = await Service.GetResultSources();
    }

    private async Task FillDDLs2()
    {
        AssembliesList = await Service.GetUserAssemblies(UserId, state.state.ElectionId, state.state.PhaseId);
        StrAssembliesList = (from aa in AssembliesList
            orderby aa.EnglishTitle
            select new ItemDTO
            {
                Id = $"{aa.Id}",
                Title = aa.EnglishTitle
            }).ToList();
        if (AssembliesList.Count == 1)
        {
            SelectedAssemblyId = AssembliesList[0].Id;
            SelectedAssemblyStrId = SelectedAssemblyId.ToString();
            SeatTypeList = await Service.GetAssemblySeatTypes((int)SelectedAssemblyId);
            ConstituenciesList =
                await Service.GetUsersConstituencies(UserId, state.state.ElectionId, (int)SelectedAssemblyId,
                    state.state.PhaseId);
            if (SeatTypeList.Count == 1) SelectedSeatTypeId = SeatTypeList[0].Id;
        }
        //StateHasChanged();
    }


    private async Task OnAssemblyChange(ChangeEventArgs<int?, GeneralItemDTO> args)
    {
        SelectedStructureId = null;
        SelectedSourceId = null;
        StateHasChanged();
        if (SelectedAssemblyId == null)
        {
            ConstituenciesList = null;
            SeatTypeList = null;
            SelectedSeatTypeId = null;
        }
        else
        {
            ConstituenciesList =
                await Service.GetUsersConstituencies(UserId, state.state.ElectionId, (int)SelectedAssemblyId,
                    state.state.PhaseId);
            SeatTypeList = await Service.GetAssemblySeatTypes((int)SelectedAssemblyId);
            if (SeatTypeList.Any())
            {
                if (SeatTypeList.Count == 1)
                {
                    SelectedSeatTypeId = SeatTypeList[0].Id;
                }
                else
                {
                    if (!SeatTypeList.Any(k => k.Id == SelectedSeatTypeId)) SelectedSeatTypeId = null;
                }
            }
            else
            {
                SelectedAssemblyId = null;
            }
        }
    }


    private async Task OnConstChange(ChangeEventArgs<int?, GeneralItemDTO> args)
    {
        ResultDetail = await Service.GetOfflineResults(SelectedAssemblyId, SelectedStructureId, SelectedSourceId,
            state.state.PhaseId);
        ChangePollingStations = ResultDetail.Any() ? ResultDetail[0].ResultPollingStations : 0;
    }

    private async Task OnSeatTypChange(ChangeEventArgs<int?, GeneralItemDTO> args)
    {
        ResultDetail = await Service.GetOfflineResults(SelectedAssemblyId, SelectedStructureId, SelectedSourceId,
            state.state.PhaseId);
        ChangePollingStations = ResultDetail.Any() ? ResultDetail[0].ResultPollingStations : 0;
    }


    private async Task SaveRecord()
    {
        var user = (await AuthenticationStateTask).User;
        ChangePollingStations = ResultDetail[0].ResultPollingStations;

        var op = await Service.SaveOfflineResults(ResultDetail,
            SelectedSourceId ?? 0, ChangePollingStations,
            user.Identity.Name);

        if (op == "OK")
        {
            var mm = new ToastModel
            {
                Content = "Results saved successfully!", ShowProgressBar = true, Timeout = 5000, Title = "Results",
                ShowCloseButton = true
            };
            await ToastObj.ShowAsync(mm);
        }
    }
}