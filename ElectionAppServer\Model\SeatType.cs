﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace ElectionAppServer.Model;

public class SeatType
{
    public int Id { get; set; }
    public string UrduTitle { get; set; }
    public string EnglishTitle { get; set; }
    public virtual List<Nomination> Nominations { get; set; }
    public virtual List<ResultLog> ResultLogs { get; set; }
    public virtual List<PanelNomination> PannelNominations { get; set; }
    public virtual List<NominationPanelist> NominationPanelists { get; set; }
    public virtual List<RePollSeat> RePollSeats { get; set; }

    #region Audit Log Fields

    public DateTime CreatedDate { get; set; } = DateTime.Now;
    public DateTime? ModifiedDate { get; set; }

    [StringLength(450)] public string CreatedBy { get; set; }

    [StringLength(450)] public string ModifiedBy { get; set; }

    #endregion Audit Log Fields
}