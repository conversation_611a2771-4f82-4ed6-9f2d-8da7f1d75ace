﻿using System.Collections.Generic;
using FluentValidation;

namespace ElectionAppServer.DTO;

public class ConstituencyDTO
{
    public string Code { get; set; }
    public int ElectionId { get; set; }
    public string EnglishTitle { get; set; }
    public string UrduTitle { get; set; }
    public string Profile { get; set; }
    public int? MaleVoters { get; set; }
    public int? FemaleVoters { get; set; }
    public int? YouthVoters { get; set; }
    public int? TotalPollingStations { get; set; }
    public int Id { get; set; }
    public string Assembly { get; set; }
    public int AssemblyId { get; set; }
    public string Trivia { get; set; }
    public string GeneralTrivia { get; set; }
    public string ImportantPoliticalPersonalities { get; set; }
    public string Problems { get; set; }
    public string ImportantAreas { get; set; }
    public float? UrbanAreaPer { get; set; }
    public float? RuralAreaPer { get; set; }
    public int? LanguageId { get; set; }
    public int? CasteId { get; set; }
    public string Province { get; set; }
    public string Division { get; set; }
    public string District { get; set; }
    public string UC { get; set; }
    public string Town { get; set; }

    public bool IsSelected { get; set; }
    public string Type { get; internal set; }
    public int? Population { get; internal set; }
    public string Languages { get; internal set; }

    public string Castes { get; internal set; }
    public string Area { get; set; }
    public string MajorityIncomeSource { get; set; }
    public string LiteracyRate { get; set; }
    public string AverageHouseholdIncome { get; set; }
    public int? TotalWards { get; set; } = 0;
    public int TotalVoters => (MaleVoters ?? 0) + (FemaleVoters ?? 0);

    public bool IsNominationExist { get; internal set; }
    public string Region { get; set; }
    public string Status { get; set; }

    public List<NamanigarDTO> Namanigars { get; set; } = new();
    public List<PrevConstituencyDTO> PrevConstituencies { get; set; }
    public bool IsPostponed { get; set; }
    public bool HasBilaMokablia { get; set; }
    public bool IsPollingStationExist { get; set; }
    public int PSMaleVoters { get; set; }
    public int PSFemaleVoters { get; set; }
    public int PSCount { get; set; }
}

public class ConstituencyDTOValidator : AbstractValidator<ConstituencyDTO>
{
    public ConstituencyDTOValidator()
    {
        RuleFor(c => c.EnglishTitle).NotEmpty().MaximumLength(200).WithMessage("Title (English) is required");
        RuleFor(c => c.UrduTitle).NotEmpty().MaximumLength(200).WithMessage("Title (Urdu) is required");
        RuleFor(c => c.Code).NotEmpty().MaximumLength(20).WithMessage("Code is required");
        RuleFor(c => c.Problems).MaximumLength(4000);
        RuleFor(c => c.ImportantAreas).MaximumLength(4000);
        RuleFor(c => c.ImportantPoliticalPersonalities).MaximumLength(4000);
        RuleFor(c => c.MajorityIncomeSource).MaximumLength(50);

        //RuleFor(c => c.MaleVoters).NotEmpty().WithMessage("Male Voters is required");
        //RuleFor(c => c.FemaleVoters).NotEmpty().WithMessage("Female Voters is required");
        //RuleFor(c => c.TotalPollingStations).NotEmpty().WithMessage("Total Number of Polling Stations is required");
    }
}