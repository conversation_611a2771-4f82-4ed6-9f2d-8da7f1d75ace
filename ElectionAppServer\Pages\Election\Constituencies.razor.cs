﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ElectionAppServer.Data;
using ElectionAppServer.DTO;
using ElectionAppServer.Model;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Syncfusion.Blazor.Grids;
using Syncfusion.Blazor.Notifications;
using Syncfusion.Blazor.Popups;

namespace ElectionAppServer.Pages.Election;

public partial class Constituencies
{
    private SfDialog dlgForm;
    private List<ConstituencyDTO> GroupA;
    private List<ConstituencyDTO> GroupB;
    private SfToast ToastObj;
    [Parameter] public int eaId { get; set; }

    [CascadingParameter] private Task<AuthenticationState> authenticationStateTask { get; set; }

    //private HalkaDTO selectedObj;
    public int? ProvinceId { get; set; }

    public int? DivisionId { get; set; }

    public int? DistrictId { get; set; }

    public int? TownId { get; set; }

    public int? UnionCouncilId { get; set; }

    public int? constTypeId { get; set; }

    public List<GeneralItemDTO> provinceList { get; set; }

    public List<GeneralItemDTO> divisionList { get; set; }

    public List<GeneralItemDTO> districtList { get; set; }

    public List<GeneralItemDTO> townList { get; set; }

    public List<GeneralItemDTO> unionCouncilList { get; set; }

    public List<GeneralItemDTO> constTypesList { get; set; }

    public string ElectionAssemblyTitle { get; set; }

    public int ElectionAssemblyId { get; set; }

    public string AssemblyTitle { get; set; }

    public List<string> selectedGroupA { get; set; }

    public List<string> selectedGroupB { get; set; }

    public bool isDlgVisible { get; set; }

    public SfDialog dlgSelectForm { get; set; }

    public bool initialize_para { get; set; }

    public int RandomNumber { get; set; }

    public CreateStructureDTO formData { get; set; }

    public string StructureTitle { get; set; }

    public int electionId { get; set; }

    public List<GeneralItemDTO> languagesList { get; set; }

    public List<GeneralItemDTO> casteList { get; set; }

    public SfGrid<ConstituencyDTO> Grid { get; set; }

    public SfGrid<ConstituencyDTO> AvailableConGrid { get; set; }

    public bool isSelectDlgVisible { get; set; } = false;

    //private async Task AddToGroupList(ListBoxChangeEventArgs ev)
    //{
    //	//selectedGroupA = new List<string>();
    //	//foreach (var item in )
    //	//{
    //	//   selectedGroupA.Add(item);
    //	//}
    //	////selectedGroupA = ev.Value as string[];

    //	var kk = ev.Value as JArray;
    //	selectedGroupA = new List<string>();
    //	foreach (var k in kk) selectedGroupA.Add(k.ToString());
    //	await Task.CompletedTask;
    //}

    private void CalcRuralPer()
    {
        if (formData.UrbanAreaPer != null)
            formData.RuralAreaPer = 100 - (float)formData.UrbanAreaPer;
        else
            formData.RuralAreaPer = null;
    }

    private void CalcUrbanPer()
    {
        if (formData.RuralAreaPer != null)
            formData.UrbanAreaPer = 100 - (float)formData.RuralAreaPer;
        else
            formData.UrbanAreaPer = null;
    }

    private void CreateNewStructure()
    {
        formData = new CreateStructureDTO();
        initialize_para = true;
        RandomNumber = new Random().Next(0, 5000);
        dlgForm.ShowAsync();
    }

    //private void CreateNewStructure2()
    //{
    //	formData = new CreateStructureDTO
    //	{
    //		DistrictId = DistrictId,
    //		UCId = UnionCouncilId,
    //		ProvinceId = ProvinceId,
    //		DivisionId = DivisionId,
    //		EnglishTitle = "",
    //		StructureType = (StructureType)constTypeId,
    //		TownId = TownId,
    //		UrduTitle = "",
    //		ElectionAssemblyId = eaId
    //	};

    //	if (constTypeId == (int)StructureType.UnionCouncil)
    //	{
    //		formData.StructureType = StructureType.UnionCouncil;
    //		StructureTitle = "Union Council";
    //	}
    //	else if (constTypeId == (int)StructureType.Ward)
    //	{
    //		formData.StructureType = StructureType.Ward;
    //		StructureTitle = "Ward";
    //	}
    //	else if (constTypeId == (int)StructureType.NA)
    //	{
    //		formData.StructureType = StructureType.NA;
    //		StructureTitle = "National Assembly Constituency";
    //	}
    //	else if (constTypeId == (int)StructureType.PA)
    //	{
    //		formData.StructureType = StructureType.PA;
    //		StructureTitle = "Provincial Assembly Constituency";
    //	}

    //	initialize_para = true;
    //	RandomNumber = new Random().Next(0, 5000);
    //	dlgForm.ShowAsync();
    //	StateHasChanged();
    //}

    //public ListBoxFieldSettings FieldSettings { get; set; } =
    //   new ListBoxFieldSettings
    //   {
    //      Text = "EnglishTitle"
    //   };

    private async Task DeleteRecord()
    {
        var para = new string[1] { "Are you want to delete this record ? " };

        var res2 = await JSRuntime.InvokeAsync<bool>("confirm", para);
        if (res2 == false)
            return;

        var user = (await authenticationStateTask).User;
        //var aa = selectedGroupA;
        if (selectedGroupB.Any())
        {
            var ids = new List<int>();
            foreach (var item in selectedGroupB)
            {
                var k = GroupB.FirstOrDefault(c => c.EnglishTitle == item).Id;
                ids.Add(k);
            }

            var res = await Service.RemoveContituenciesToAssembly(eaId, ids, user.Identity.Name);
            await FillConstituencies();
            await FillSelectedConstituencies();
            selectedGroupA = new List<string>();
            selectedGroupB = new List<string>();
        }
    }

    private async Task FillConstituencies()
    {
        //GroupA = await Service.GetAvailableConstituencies(eaId, (StructureType)constTypeId);
        if (constTypeId == null)
        {
            GroupA = new List<ConstituencyDTO>();
        }
        else
        {
            GroupA = await Service.GetAvailableConstituencies(eaId, (StructureType)constTypeId);
            await AvailableConGrid.Refresh();
        }

        //if (constTypeId != null)
        //{
        //   GroupA = await Service.GetConstituencies(eaId, (StructureType)constTypeId, null, null, null, null, null);
        //}
        //else
        //{
        //   GroupA = new List<ConstituencyDTO>();
        //}
    }

    private async Task FillDistricts(object ev)
    {
        if (DivisionId != null)
        {
            districtList = await Service.GetDistricts((int)DivisionId, electionId);
            try
            {
                await Grid.Refresh();
            }
            catch (Exception)
            {
                // ignored
            }
        }
        else
        {
            districtList = new List<GeneralItemDTO>();
            try
            {
                await Grid.Refresh();
            }
            catch (Exception)
            {
                // ignored
            }
        }

        townList = new List<GeneralItemDTO>();
        unionCouncilList = new List<GeneralItemDTO>();
        DistrictId = null;
        UnionCouncilId = null;
        TownId = null;
        await FillConstituencies();
    }

    private async Task FillDistricts2()
    {
        if (formData.DivisionId != null)
            districtList = await Service.GetDistricts((int)formData.DivisionId, electionId);
        else
            districtList = new List<GeneralItemDTO>();
        DivisionId = formData.DivisionId;
        townList = new List<GeneralItemDTO>();
        unionCouncilList = new List<GeneralItemDTO>();
        TownId = null;
        UnionCouncilId = null;
        formData.DistrictId = null;
        formData.TownId = null;
        formData.UCId = null;
        //await FillConstituencies();
    }

    private async Task FillDivisions2()
    {
        if (formData.ProvinceId != null)
        {
            //
            if (state.state.ElectionType == ElectionType.LocalBody)
            {
                divisionList = await Service.GetDivisions((int)formData.ProvinceId, electionId);
            }
            else
            {
                divisionList = new List<GeneralItemDTO>();
                divisionList = await Service.GetDivisions((int)formData.ProvinceId, electionId);
                if (divisionList.Any())
                    formData.DivisionId = divisionList[0].Id;
                districtList = await Service.GetProviceDistricts(formData.ProvinceId.Value);
            }
        }
        else
        {
            divisionList = new List<GeneralItemDTO>();
        }

        ProvinceId = formData.ProvinceId;

        //districtList = new List<GeneralItemDTO>();
        townList = new List<GeneralItemDTO>();
        unionCouncilList = new List<GeneralItemDTO>();
        DivisionId = null;
        DistrictId = null;
        TownId = null;
        UnionCouncilId = null;
        formData.DivisionId = null;
        formData.DistrictId = null;
        formData.TownId = null;
        formData.UCId = null;
        //await FillConstituencies();
    }

    //private async Task FillDivisions(object oo)
    //{
    //   if (ProvinceId != null)
    //   {
    //      divisionList = await Service.GetDivisions((int)ProvinceId, electionId);

    //   }
    //   else
    //   {
    //      divisionList = new List<GeneralItemDTO>();
    //   }
    //   districtList = new List<GeneralItemDTO>();
    //   townList = new List<GeneralItemDTO>();
    //   unionCouncilList = new List<GeneralItemDTO>();
    //   DivisionId = null;
    //   DistrictId = null;
    //   TownId = null;
    //   UnionCouncilId = null;
    //   await FillConstituencies();
    //   StateHasChanged();

    //}

    //private async Task FillConstituencies()
    //{
    //   if (constTypeId != null)
    //   {
    //      GroupA = await Service.GetConstituencies(eaId, (StructureType)constTypeId, ProvinceId, DivisionId, DistrictId, TownId, UnionCouncilId);
    //      try
    //      {
    //         await Grid.Refresh();
    //      }
    //      catch (Exception)
    //      {
    //      }

    //   }
    //   else
    //   {
    //      GroupA = new List<ConstituencyDTO>();
    //      try
    //      {
    //         await Grid.Refresh();
    //      }
    //      catch (Exception)
    //      {
    //      }
    //   }
    //}

    private async Task FillSelectedConstituencies()
    {
        GroupB = await Service.GetSelectedConstituencies(eaId);
        try
        {
            await Grid.Refresh();
        }
        catch (Exception)
        {
            // ignored
        }
    }

    private async Task FillTowns(object ev)
    {
        if (DistrictId != null)
            townList = await Service.GetTowns((int)DistrictId, electionId);
        else
            townList = new List<GeneralItemDTO>();
        TownId = null;
        unionCouncilList = new List<GeneralItemDTO>();
        UnionCouncilId = null;
        await FillConstituencies();
    }

    private async Task FillTowns2()
    {
        if (formData.DistrictId != null)
            townList = await Service.GetTowns((int)formData.DistrictId, electionId);
        else
            townList = new List<GeneralItemDTO>();
        DistrictId = formData.DistrictId;
        TownId = null;
        UnionCouncilId = null;
        formData.TownId = null;
        formData.UCId = null;
        //await FillConstituencies();
    }

    private async Task FillUnionCounciels(object ev)
    {
        if (TownId != null)
            unionCouncilList = await Service.GetUnionCouncils((int)TownId, electionId);
        else
            unionCouncilList = new List<GeneralItemDTO>();
        //UnionCouncilId = null;

        await FillConstituencies();
    }

    private async Task FillUnionCounciles2()
    {
        if (formData.TownId != null)
            unionCouncilList = await Service.GetUnionCouncils((int)formData.TownId, electionId);
        else
            unionCouncilList = new List<GeneralItemDTO>();
        formData.UCId = UnionCouncilId;
        //await FillConstituencies();
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        //return base.OnAfterRenderAsync(firstRender);
        if (firstRender)
        {
            var user = (await authenticationStateTask).User;
            if (user.Identity.IsAuthenticated)
            {
                //var dd =  await ProtectedSessionStore.GetAsync<ElectionStateDTO>("election");
                var dd = await localStorage.GetAsync<ElectionStateDTO>(await confreader.GetSettingValue("electionkey"));
                state.SetState(dd.Value);
            }
            else
            {
                try
                {
                    //await  ProtectedSessionStore.DeleteAsync("election");
                    await localStorage.DeleteAsync(await confreader.GetSettingValue("electionkey"));
                    state.SetState(
                        new ElectionStateDTO
                        {
                            ElectionId = 0,
                            ElectionTitle = string.Empty,
                            PhaseId = 0,
                            PhaseTitle = string.Empty
                        });
                }
                catch (Exception)
                {
                    // ignored
                }

                NavigationManager.NavigateTo(string.Empty);
            }

            StateHasChanged();
        }
    }

    protected override async Task OnParametersSetAsync()
    {
        electionId = await Service.GetElectionId(eaId);
        constTypeId = (int?)await Service.GetAssemblyType(eaId);
        provinceList = await Service.GetProvinceList(electionId);
        constTypesList = await Service.GetConstituencyTypes(eaId);
        languagesList = await Service.GetLanguages();
        casteList = await Service.GetCastesList();
        var obj = await Service.GetElectionInfo(eaId);
        ElectionAssemblyId = obj[0].Id;
        ElectionAssemblyTitle = obj[0].EnglishTitle;
        AssemblyTitle = obj[1].EnglishTitle;
        formData = new CreateStructureDTO();
        await FillSelectedConstituencies();
        //await FillConstituencies();
    }

    //private async Task RemoveFromGroupList(ListBoxChangeEventArgs ev)
    //{
    //	var kk = ev.Value as JArray;
    //	selectedGroupB = new List<string>();
    //	foreach (var k in kk) selectedGroupB.Add(k.ToString());
    //	await Task.CompletedTask;
    //}

    private async Task RemoveSelectedCons()
    {
        var para = new string[1] { "Are you want to remove constituencies from this assembly?" };
        var res2 = await JSRuntime.InvokeAsync<bool>("confirm", para);
        if (res2 == false)
            return;

        var user = (await authenticationStateTask).User;

        var ids = new List<int>();

        foreach (var cons in GroupB)
            if (cons.IsSelected)
                ids.Add(cons.Id);

        var res = await Service.RemoveContituenciesToAssembly(eaId, ids, user.Identity.Name);
        await FillSelectedConstituencies();
        await Grid.Refresh();
        StateHasChanged();
    }

    private async Task SaveConsToAssmb()
    {
        var user = (await authenticationStateTask).User;
        var ids = new List<int>();
        foreach (var co in GroupA)
            if (co.IsSelected)
                ids.Add(co.Id);

        if (ids.Count > 0)
        {
            var res = await Service.AddConstituencyToAssembly(
                eaId,
                ids,
                (ElectionAssemblyType)constTypeId,
                user.Identity.Name);
            await dlgSelectForm.HideAsync();
            GroupB = await Service.GetSelectedConstituencies(eaId);
            await Grid.Refresh();
            StateHasChanged();
        }
        else
        {
            await ToastObj.ShowAsync(
                new ToastModel
                {
                    Title = "Error",
                    Content = "Please select at-least on constituency",
                    CssClass = "e-toast-info",
                    ShowProgressBar = true,
                    Timeout = 5000,
                    ShowCloseButton = true
                });
        }
    }

    private async Task SaveRecord()
    {
        var user = (await authenticationStateTask).User;
        //var aa = selectedGroupA;
        if (selectedGroupA.Any())
        {
            var ids = new List<int>();
            foreach (var item in selectedGroupA)
                try
                {
                    var k = GroupA.FirstOrDefault(c => c.EnglishTitle == item).Id;
                    ids.Add(k);
                }
                catch (Exception)
                {
                    // ignored
                }

            var res = await Service.AddConstituencyToAssembly(
                eaId,
                ids,
                (ElectionAssemblyType)constTypeId,
                user.Identity.Name);

            selectedGroupA = new List<string>();
            selectedGroupB = new List<string>();

            await FillConstituencies();
            await FillSelectedConstituencies();
        }
    }

    private async Task SaveStructure()
    {
        if (formData.ConstTypeId == (int)StructureType.UnionCouncil)
            formData.StructureType = StructureType.UnionCouncil;
        else if (formData.ConstTypeId == (int)StructureType.Ward)
            formData.StructureType = StructureType.Ward;
        else if (formData.ConstTypeId == (int)StructureType.NA)
            formData.StructureType = StructureType.NA;
        else if (formData.ConstTypeId == (int)StructureType.PA) formData.StructureType = StructureType.PA;

        try
        {
            var user = (await authenticationStateTask).User;
            formData.ElectionAssemblyId = eaId;
            var res = await Service.CreateStructure(formData, user.Identity.Name, state.state.PhaseId);

            if (res == "OK")
            {
                await FillConstituencies();
                await FillSelectedConstituencies();
                await dlgForm.HideAsync();
                await Grid.Refresh();
            }
            else
            {
                var m = new ToastModel
                {
                    Content = res,
                    Title = "Error",
                    CssClass = "e-toast-danger",
                    ShowProgressBar = true,
                    Timeout = 5000,
                    ShowCloseButton = true
                };
                await ToastObj.ShowAsync(m);
            }
        }
        catch (Exception ex)
        {
            var m = new ToastModel
            {
                Content = ex.Message,
                Title = "Error",
                CssClass = "e-toast-danger",
                ShowProgressBar = true,
                Timeout = 5000,
                ShowCloseButton = true
            };
            await ToastObj.ShowAsync(m);
        }
    }

    private async Task ShowSelectConstDlg()
    {
        //GroupA = await Service.GetSelectedConstituencies(ElectionAssembyId);
        //GroupA = await Service.GetConstituencies(eaId, (StructureType)constTypeId, null, null, null, null, null);
        if (constTypeId == null)
        {
            GroupA = new List<ConstituencyDTO>();
        }
        else
        {
            GroupA = await Service.GetAvailableConstituencies(eaId, (StructureType)constTypeId);
            await AvailableConGrid.Refresh();
        }

        await dlgSelectForm.ShowAsync();
    }

    private void beforeOpen(BeforeOpenEventArgs args)
    {
        args.MaxHeight = null;
    }

    private async Task DeleteConstituency(int id)
    {
        var res = await JSRuntime.InvokeAsync<bool>(
            "confirm",
            new[] { "Are you sure want to delete this constituency?" });
        if (res)
            try
            {
                var msg = await Service.DeleteConstituency(id, eaId);
                if (msg == "OK")
                {
                    await FillConstituencies();
                    await FillSelectedConstituencies();
                    await Grid.Refresh();
                }
                else
                {
                    var mm = new ToastModel
                    {
                        Title = "Error",
                        Content = msg,
                        CssClass = "e-toast-danger",
                        ShowProgressBar = true,
                        Timeout = 5000,
                        ShowCloseButton = true
                    };
                    await ToastObj.ShowAsync(mm);
                }
            }
            catch (Exception ex)
            {
                var mm = new ToastModel
                {
                    Title = "Error",
                    Content = ex.Message,
                    CssClass = "e-toast-danger",
                    ShowProgressBar = true,
                    Timeout = 5000,
                    ShowCloseButton = true
                };
                if (ex.InnerException != null)
                    mm.Content += " - " + ex.InnerException.Message;
                await ToastObj.ShowAsync(mm);
            }
    }

    private async Task OpenEditForm(int id)
    {
        formData = await Service.GetConstituencyInfoForEdit(id, state.state.PhaseId);
        constTypesList = await Service.GetConstituencyTypes(eaId);
        var divId = formData.DivisionId;
        var distId = formData.DistrictId;
        var townId = formData.TownId;
        var ucId = formData.UCId;
        if (state.state.ElectionType == ElectionType.LocalBody)
        {
            await FillDivisions2();
            formData.DivisionId = divId;
            await FillDistricts2();
            formData.DistrictId = distId;
            if (townId != null)
            {
                await FillTowns2();
                formData.TownId = townId;

                if (ucId != null)
                {
                    await FillUnionCounciles2();
                    formData.UCId = ucId;
                }
            }
        }
        else
        {
            await FillDivisions2();
            formData.DistrictId = distId;
            formData.DivisionId = 3;
        }

        initialize_para = true;
        RandomNumber = new Random().Next(0, 5000);
        await dlgForm.ShowAsync();
    }

    private async Task TranslateToUrdu()
    {
        try
        {
            if (string.IsNullOrEmpty(formData.UrduTitle))
                formData.UrduTitle = await Translation.TranslateText(formData.EnglishTitle);
        }
        catch (Exception)
        {
            // ignored
        }
    }


    //private async Task TestGroupASelected(object ev)
    //{
    //	var kk = ev;
    //}

    //private async Task SetFormTitle()
    //{
    //}
}