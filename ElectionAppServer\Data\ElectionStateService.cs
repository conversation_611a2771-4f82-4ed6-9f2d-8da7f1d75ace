﻿using System;
using ElectionAppServer.DTO;

namespace ElectionAppServer.Data;

public class ElectionStateService
{
    public ElectionStateDTO state { get; set; } =
        new() { ElectionId = 0, ElectionTitle = "", PhaseId = 0, PhaseTitle = "" };

    public event Action OnChange;

    public void SetState(ElectionStateDTO stateObj)
    {
        if (stateObj == null)
            state = new ElectionStateDTO { ElectionId = 0, ElectionTitle = "", PhaseId = 0, PhaseTitle = "" };
        else
            state = new ElectionStateDTO
            {
                ElectionId = stateObj.ElectionId, ElectionTitle = stateObj.ElectionTitle, PhaseId = stateObj.PhaseId,
                PhaseTitle = stateObj.PhaseTitle
            };

        OnChange?.Invoke();
    }
}