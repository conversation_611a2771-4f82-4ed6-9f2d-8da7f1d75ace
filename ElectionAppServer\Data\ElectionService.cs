﻿using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using Dapper;
using ElectionAppServer.DTO;
using ElectionAppServer.DTO.Dashboard;
using ElectionAppServer.Model;
using ElectionAppServer.Model.Structures;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using OfficeOpenXml;

namespace ElectionAppServer.Data;

public class ElectionService
{
    private readonly IDbContextFactory<ApplicationDbContext>
        _contextFactory; // private  readonly  ApplicationDbContext dc;

    public ElectionService(IDbContextFactory<ApplicationDbContext> contextFactory) // (ApplicationDbContext  dc)
    {
        //this.dc = dc;
        _contextFactory = contextFactory;
    }

    internal Task<ElectionDTO> GetElectionInfo(int id)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.ElectionPhases
            where aa.Id == id
            select new ElectionDTO
            {
                Id = aa.Id,
                CandidatePoll = aa.Election.CandidatePoll,
                CandidatePoolId = (int)aa.Election.CandidatePoll,
                ElectionType = aa.Election.ElectionType ?? ElectionType.General,
                YearFrom = aa.Election.YearFrom,
                Description = aa.Election.Description,
                StartDate = aa.StartDate,
                ElectionId = aa.ElectionId,
                PhaseTitle = aa.Title,
                EnglishTitle = aa.Election.EnglishTitle,
                UrduTitle = aa.Election.UrduTitle,
                YearTo = aa.Election.YearTo
            }).FirstOrDefault();
        return Task.FromResult(q);
    }

    public Task<string> CopyElectionStructureOnly(int parentElectionId, int newElectionId, string user)
    {
        using var dc = _contextFactory.CreateDbContext();
        using var tran = dc.Database.BeginTransaction();

        #region Copy Provinces/Regions/Divisions Units

        // Copy provinces
        var parentProvinces = (from aa in dc.Structures.OfType<Province>()
            where aa.ElectionId == parentElectionId
            select aa).ToList();

        foreach (var p in parentProvinces)
        {
            var newProvince = new Province
            {
                ElectionId = newElectionId,
                Area = p.Area,
                Castes = p.Castes,
                Code = p.Code,
                CreatedBy = user,
                AverageHouseholdIncome = p.AverageHouseholdIncome,
                CreatedDate = DateTime.Now,
                EnglishTitle = p.EnglishTitle,
                FemaleVoters = p.FemaleVoters,
                GeneralTrivia = p.GeneralTrivia,
                HasBilaMokablia = false,
                ImportantAreas = p.ImportantAreas,
                ImportantPoliticalPersonalities = p.ImportantPoliticalPersonalities,
                IsPostponed = false,
                IsSeat = false,
                Languages = p.Languages,
                LiteracyRate = p.LiteracyRate,
                MajorityIncomeSource = p.MajorityIncomeSource,
                MaleVoters = p.MaleVoters,
                Population = p.Population,
                PrevStructureId = p.Id,
                Problems = p.Problems,
                RuralAreaPer = p.RuralAreaPer,
                TotalPollingStations = p.TotalPollingStations,
                Trivia = p.Trivia,
                UrbanAreaPer = p.UrbanAreaPer,
                UrduTitle = p.UrduTitle
            };
            dc.Structures.Add(newProvince);
            dc.SaveChanges();

            #region Copy Regions

            var parentRegions = (from r in dc.Regions
                where r.ProvinceId == p.Id
                select r).ToList();

            foreach (var pr in parentRegions)
            {
                var newRegion = new Region
                {
                    EnglishTitle = pr.EnglishTitle,
                    CreatedBy = user,
                    CreatedDate = DateTime.Now,
                    ProvinceId = newProvince.Id,
                    UrduTitle = pr.UrduTitle
                };
                dc.Regions.Add(newRegion);
                dc.SaveChanges();
            }

            #endregion Copy Regions

            #region Copy Divisions

            var parentDivisions = (from a in dc.Structures.OfType<Division>()
                where a.ElectionId == parentElectionId && a.ProvinceId == p.Id
                select a).FirstOrDefault();
            if (parentDivisions != null)
            {
                var newDivision = new Division
                {
                    EnglishTitle = parentDivisions.EnglishTitle,
                    UrduTitle = parentDivisions.UrduTitle,
                    ProvinceId = newProvince.Id,
                    PrevStructureId = parentDivisions.Id,
                    CreatedBy = user,
                    CreatedDate = DateTime.Now,
                    ElectionId = newElectionId
                };
                dc.Structures.Add(newDivision);
                dc.SaveChanges();
            }

            #endregion Copy Divisions
        }

        // Save Regions

        #endregion Copy Provinces/Regions/Divisions Units

        var parentDistricts = (from a in dc.Structures.OfType<District>()
            where a.ElectionId == parentElectionId
            select new
            {
                a.Id,
                a.EnglishTitle,
                a.UrduTitle,
                RegionEnglish = a.Region.EnglishTitle,
                RegionUrdu = a.Region.UrduTitle,
                ProvinceUrdu = a.Region.Province.EnglishTitle,
                ProvinceEnglish = a.Region.Province.UrduTitle,
                DivisionEnglish = a.Division.EnglishTitle,
                DivisionUrdu = a.Division.UrduTitle
            }).ToList();

        foreach (var pd in parentDistricts)
        {
            var newProvince = (from a in dc.Structures.OfType<Province>()
                where a.EnglishTitle == pd.ProvinceUrdu &&
                      a.ElectionId == newElectionId
                select a).FirstOrDefault();
            var newDivision = (from a in dc.Structures.OfType<Division>()
                where a.ElectionId == newElectionId &&
                      a.ProvinceId == newProvince.Id &&
                      a.EnglishTitle == pd.DivisionEnglish
                select a).FirstOrDefault();

            var newRegion = (from a in dc.Regions
                where a.ProvinceId == newProvince.Id &&
                      a.EnglishTitle == pd.RegionEnglish
                select a).FirstOrDefault();
            var newDistrict = new District
            {
                EnglishTitle = pd.EnglishTitle,
                UrduTitle = pd.UrduTitle,
                RegionId = newRegion.Id,
                DivisionId = newDivision.Id,
                CreatedBy = user,
                CreatedDate = DateTime.Now,
                ElectionId = newElectionId
            };
            dc.Structures.Add(newDistrict);
            dc.SaveChanges();
        }

        tran.Commit();

        return Task.FromResult("OK");
    }

    public Task<string> CopyElection(int parentElectionId, int newElectionId, string user)
    {
        using var dc = _contextFactory.CreateDbContext();
        using var tran = dc.Database.BeginTransaction();

        #region If any administrative units or assembly exist on this new election head

        var AnyStructureExists = (from aa in dc.Structures where aa.ElectionId == newElectionId select aa).Any();

        if (AnyStructureExists)
            return Task.FromResult("One or more structure already exist on this election");

        var AnyAssemblyExists = (from aa in dc.ElectionAssemblies where aa.ElectionId == newElectionId select aa).Any(
        );
        if (AnyAssemblyExists)
            return Task.FromResult("One or more assemblies already exist on this election");

        #endregion If any administrative units or assembly exist on this new election head

        try
        {
            // get parent election head information
            var q = (from aa in dc.Elections where aa.Id == parentElectionId select aa).FirstOrDefault();

            // Copy all assemblies of parent election in new election head.
            // and table for parent child assembly ids mapping

            #region Assemblies

            var parentAssemblies = (from aa in dc.ElectionAssemblies
                where aa.ElectionId == parentElectionId
                select aa).ToList();
            var assmblyMappings = new List<NewPrvAssmbly>();
            foreach (var asmb in parentAssemblies)
            {
                var nAsm = new ElectionAssembly
                {
                    AssemblyTypeId = asmb.AssemblyTypeId,
                    CreatedBy = user,
                    ElectionId = newElectionId,
                    ElectionAssemblyType = asmb.ElectionAssemblyType,
                    EnglishTitle = asmb.EnglishTitle,
                    ReserveSeats = asmb.ReserveSeats,
                    UrduTitle = asmb.UrduTitle,
                    WomenSeats = asmb.WomenSeats,
                    CreatedDate = DateTime.Now
                };
                dc.ElectionAssemblies.Add(nAsm);
                dc.SaveChanges();
                assmblyMappings.Add(new NewPrvAssmbly { ParentAssemblyId = asmb.Id, NewAssemblyId = nAsm.Id });
                var parentElectionAssemblySeatTypes = (from aa in dc.ElectionAssemblySeats
                    where aa.ElectionAssemblyId == asmb.Id
                    select aa).ToList();
                foreach (var east in parentElectionAssemblySeatTypes)
                {
                    var nEast = new ElectionAssemblySeat
                    {
                        ConstituencyType = east.ConstituencyType,
                        CreatedBy = user,
                        CreatedDate = DateTime.Now,
                        ElectionAssemblyId = nAsm.Id,
                        SeatCount = east.SeatCount,
                        SeatTypeId = east.SeatTypeId
                    };
                    dc.ElectionAssemblySeats.Add(nEast);
                    dc.SaveChanges();
                }
            }

            #endregion Assemblies

            // Create All adminstrative units from province to wards
            // and add administrative units to exactly as parent assembly

            #region Create Administrative Units

            // Get all parent provinces
            var parentProvinces = (from aa in dc.Structures.OfType<Province>()
                where aa.ElectionId == parentElectionId
                select aa).ToList();
            // Create new province in new election head with previous structure head mapping
            foreach (var pro in parentProvinces)
            {
                var nProv = new Province
                {
                    Area = pro.Area,
                    AverageHouseholdIncome = pro.AverageHouseholdIncome,
                    Castes = pro.Castes,
                    Code = pro.Code,
                    CreatedBy = user,
                    CreatedDate = DateTime.Now,
                    ElectionId = newElectionId,
                    EnglishTitle = pro.EnglishTitle,
                    FemaleVoters = pro.FemaleVoters,
                    GeneralTrivia = pro.GeneralTrivia,
                    ImportantAreas = pro.ImportantAreas,
                    ImportantPoliticalPersonalities = pro.ImportantPoliticalPersonalities,
                    IsSeat = pro.IsSeat,
                    Languages = pro.Languages,
                    LiteracyRate = pro.LiteracyRate,
                    MajorityIncomeSource = pro.MajorityIncomeSource,
                    MaleVoters = pro.MaleVoters,
                    Population = pro.Population,
                    PrevStructureId = pro.Id,
                    RuralAreaPer = pro.RuralAreaPer,
                    TotalPollingStations = pro.TotalPollingStations,
                    UrbanAreaPer = pro.UrbanAreaPer,
                    Trivia = pro.Trivia,
                    UrduTitle = pro.UrduTitle,
                    Problems = pro.Problems
                };
                dc.Structures.Add(nProv);
                dc.SaveChanges();
                var strmap = new StructureMapping
                {
                    Description = string.Empty,
                    PreviousStructureId = pro.Id,
                    StructureId = nProv.Id
                };
                dc.StructureMappings.Add(strmap);
                dc.SaveChanges();
                // Copy all regions on this province to newly created province

                #region Create Regions

                var parentRegions = (from aa in dc.Regions where aa.ProvinceId == pro.Id select aa).ToList();
                foreach (var reg in parentRegions)
                {
                    var nReg = new Region
                    {
                        CreatedBy = user,
                        CreatedDate = DateTime.Now,
                        EnglishTitle = reg.EnglishTitle,
                        ProvinceId = nProv.Id,
                        UrduTitle = reg.UrduTitle
                    };
                    dc.Regions.Add(nReg);
                    dc.SaveChanges();
                }

                #endregion Create Regions

                // Copy all divisions of this province to newly created province

                #region Create Divisions

                // get all divisions of this province
                var parentDivisions = (from aa in dc.Structures.OfType<Division>()
                    where aa.ProvinceId == pro.Id && aa.ElectionId == parentElectionId
                    select aa).ToList();
                // Copy exactly same division to newly created province
                foreach (var div in parentDivisions)
                {
                    var nDiv = new Division
                    {
                        Area = div.Area,
                        AverageHouseholdIncome = div.AverageHouseholdIncome,
                        Castes = div.Castes,
                        Code = div.Code,
                        CreatedBy = user,
                        CreatedDate = DateTime.Now,
                        ElectionId = newElectionId,
                        EnglishTitle = div.EnglishTitle,
                        FemaleVoters = div.FemaleVoters,
                        GeneralTrivia = div.GeneralTrivia,
                        ImportantAreas = div.ImportantAreas,
                        ImportantPoliticalPersonalities = div.ImportantPoliticalPersonalities,
                        IsSeat = div.IsSeat,
                        Languages = div.Languages,
                        LiteracyRate = div.LiteracyRate,
                        MajorityIncomeSource = div.MajorityIncomeSource,
                        MaleVoters = div.MaleVoters,
                        Population = div.Population,
                        PrevStructureId = div.Id,
                        RuralAreaPer = div.RuralAreaPer,
                        TotalPollingStations = div.TotalPollingStations,
                        UrbanAreaPer = div.UrbanAreaPer,
                        Trivia = div.Trivia,
                        UrduTitle = div.UrduTitle,
                        ProvinceId = nProv.Id,
                        Problems = div.Problems
                    };
                    dc.Structures.Add(nDiv);
                    dc.SaveChanges();
                    strmap = new StructureMapping
                    {
                        Description = string.Empty,
                        PreviousStructureId = div.Id,
                        StructureId = nDiv.Id
                    };
                    dc.StructureMappings.Add(strmap);
                    dc.SaveChanges();

                    #region Create Districts

                    // Get All Districts of this division and copy it to newly created division
                    var parentDistricts = (from aa in dc.Structures.OfType<District>()
                        where aa.DivisionId == div.Id && aa.ElectionId == parentElectionId
                        select aa).ToList();
                    // copy district to newly created division
                    foreach (var dis in parentDistricts)
                    {
                        var nDis = new District
                        {
                            Area = dis.Area,
                            AverageHouseholdIncome = dis.AverageHouseholdIncome,
                            Castes = dis.Castes,
                            Code = dis.Code,
                            CreatedBy = user,
                            CreatedDate = DateTime.Now,
                            ElectionId = newElectionId,
                            EnglishTitle = dis.EnglishTitle,
                            FemaleVoters = dis.FemaleVoters,
                            GeneralTrivia = dis.GeneralTrivia,
                            ImportantAreas = dis.ImportantAreas,
                            ImportantPoliticalPersonalities = dis.ImportantPoliticalPersonalities,
                            IsSeat = dis.IsSeat,
                            Languages = dis.Languages,
                            LiteracyRate = dis.LiteracyRate,
                            MajorityIncomeSource = dis.MajorityIncomeSource,
                            MaleVoters = dis.MaleVoters,
                            Population = dis.Population,
                            PrevStructureId = dis.Id,
                            RuralAreaPer = dis.RuralAreaPer,
                            TotalPollingStations = dis.TotalPollingStations,
                            UrbanAreaPer = dis.UrbanAreaPer,
                            Trivia = dis.Trivia,
                            UrduTitle = dis.UrduTitle,
                            DivisionId = nDiv.Id,
                            Problems = dis.Problems
                        };
                        //a. First get parent division urdu/english title
                        //b. then get this title in newly created region
                        //c. if found then get this region id
                        if (dis.RegionId != null)
                        {
                            var pReg = (from aa in dc.Regions
                                where aa.Id == dis.RegionId
                                select new { aa.EnglishTitle, aa.UrduTitle }).FirstOrDefault();

                            var newReg = (from aa in dc.Regions
                                where aa.UrduTitle == pReg.UrduTitle &&
                                      aa.EnglishTitle == pReg.EnglishTitle &&
                                      aa.ProvinceId == nProv.Id
                                select aa).FirstOrDefault();
                            if (newReg != null) nDis.RegionId = newReg.Id;
                        }

                        dc.Structures.Add(nDis);
                        dc.SaveChanges();
                        strmap = new StructureMapping
                        {
                            Description = string.Empty,
                            PreviousStructureId = dis.Id,
                            StructureId = nDis.Id
                        };
                        dc.StructureMappings.Add(strmap);
                        dc.SaveChanges();

                        #region Copy Towns

                        // Get all towns of this district
                        var parentTowns = (from aa in dc.Structures.OfType<Town>()
                            where aa.DistrictId == dis.Id && aa.ElectionId == parentElectionId
                            select aa).ToList();
                        // copy exactly same town in newly created district
                        foreach (var twn in parentTowns)
                        {
                            var nTwn = new Town
                            {
                                Area = twn.Area,
                                AverageHouseholdIncome = twn.AverageHouseholdIncome,
                                Castes = twn.Castes,
                                Code = twn.Code,
                                CreatedBy = user,
                                CreatedDate = DateTime.Now,
                                ElectionId = newElectionId,
                                EnglishTitle = twn.EnglishTitle,
                                FemaleVoters = twn.FemaleVoters,
                                GeneralTrivia = twn.GeneralTrivia,
                                ImportantAreas = twn.ImportantAreas,
                                ImportantPoliticalPersonalities = twn.ImportantPoliticalPersonalities,
                                IsSeat = twn.IsSeat,
                                Languages = twn.Languages,
                                LiteracyRate = twn.LiteracyRate,
                                MajorityIncomeSource = twn.MajorityIncomeSource,
                                MaleVoters = twn.MaleVoters,
                                Population = twn.Population,
                                PrevStructureId = twn.Id,
                                RuralAreaPer = twn.RuralAreaPer,
                                TotalPollingStations = twn.TotalPollingStations,
                                UrbanAreaPer = twn.UrbanAreaPer,
                                Trivia = twn.Trivia,
                                UrduTitle = twn.UrduTitle,
                                Problems = twn.Problems,
                                DistrictId = nDis.Id
                            };
                            dc.Structures.Add(nTwn);
                            dc.SaveChanges();

                            strmap = new StructureMapping
                            {
                                Description = string.Empty,
                                PreviousStructureId = twn.Id,
                                StructureId = nTwn.Id
                            };
                            dc.StructureMappings.Add(strmap);
                            dc.SaveChanges();

                            #region Create Union Councils

                            var parentUCs = (from aa in dc.Structures.OfType<UnionCouncil>()
                                where aa.TownId == twn.Id && aa.ElectionId == parentElectionId
                                select aa).ToList();

                            foreach (var uc in parentUCs)
                            {
                                var nUC = new UnionCouncil
                                {
                                    Area = uc.Area,
                                    AverageHouseholdIncome = uc.AverageHouseholdIncome,
                                    Castes = uc.Castes,
                                    Code = uc.Code,
                                    CreatedBy = user,
                                    CreatedDate = DateTime.Now,
                                    ElectionId = newElectionId,
                                    EnglishTitle = uc.EnglishTitle,
                                    FemaleVoters = uc.FemaleVoters,
                                    GeneralTrivia = uc.GeneralTrivia,
                                    ImportantAreas = uc.ImportantAreas,
                                    ImportantPoliticalPersonalities = uc.ImportantPoliticalPersonalities,
                                    IsSeat = uc.IsSeat,
                                    Languages = uc.Languages,
                                    LiteracyRate = uc.LiteracyRate,
                                    MajorityIncomeSource = uc.MajorityIncomeSource,
                                    MaleVoters = uc.MaleVoters,
                                    Population = uc.Population,
                                    PrevStructureId = uc.Id,
                                    RuralAreaPer = uc.RuralAreaPer,
                                    TotalPollingStations = uc.TotalPollingStations,
                                    UrbanAreaPer = uc.UrbanAreaPer,
                                    Trivia = uc.Trivia,
                                    UrduTitle = uc.UrduTitle,
                                    Problems = uc.Problems,
                                    TownId = nTwn.Id
                                };

                                if (uc.AssemblyId != null)
                                {
                                    var newAsmb =
                                        assmblyMappings.FirstOrDefault(c => c.ParentAssemblyId == uc.AssemblyId);
                                    if (newAsmb != null) nUC.AssemblyId = newAsmb.NewAssemblyId;
                                }

                                dc.Structures.Add(nUC);
                                dc.SaveChanges();
                                strmap = new StructureMapping
                                {
                                    Description = string.Empty,
                                    PreviousStructureId = uc.Id,
                                    StructureId = nUC.Id
                                };
                                dc.StructureMappings.Add(strmap);
                                dc.SaveChanges();

                                #region Save Wards

                                var parentWards = (from aa in dc.Structures.OfType<Ward>()
                                    where aa.UnionCouncilId == uc.Id && aa.ElectionId == parentElectionId
                                    select aa).ToList();
                                foreach (var wrd in parentWards)
                                {
                                    var nWrd = new Ward
                                    {
                                        Area = wrd.Area,
                                        AverageHouseholdIncome = wrd.AverageHouseholdIncome,
                                        Castes = wrd.Castes,
                                        Code = wrd.Code,
                                        CreatedBy = user,
                                        CreatedDate = DateTime.Now,
                                        ElectionId = newElectionId,
                                        EnglishTitle = wrd.EnglishTitle,
                                        FemaleVoters = wrd.FemaleVoters,
                                        GeneralTrivia = wrd.GeneralTrivia,
                                        ImportantAreas = wrd.ImportantAreas,
                                        ImportantPoliticalPersonalities = wrd.ImportantPoliticalPersonalities,
                                        IsSeat = wrd.IsSeat,
                                        Languages = wrd.Languages,
                                        LiteracyRate = wrd.LiteracyRate,
                                        MajorityIncomeSource = wrd.MajorityIncomeSource,
                                        MaleVoters = wrd.MaleVoters,
                                        Population = wrd.Population,
                                        PrevStructureId = wrd.Id,
                                        RuralAreaPer = wrd.RuralAreaPer,
                                        TotalPollingStations = wrd.TotalPollingStations,
                                        UrbanAreaPer = wrd.UrbanAreaPer,
                                        Trivia = wrd.Trivia,
                                        UrduTitle = wrd.UrduTitle,
                                        Problems = wrd.Problems,
                                        UnionCouncilId = nUC.Id
                                    };

                                    if (wrd.AssemblyId != null)
                                    {
                                        var newAsmb = assmblyMappings.FirstOrDefault(
                                            c => c.ParentAssemblyId == wrd.AssemblyId);
                                        if (newAsmb != null) nWrd.AssemblyId = newAsmb.NewAssemblyId;
                                    }

                                    dc.Structures.Add(nWrd);
                                    dc.SaveChanges();

                                    strmap = new StructureMapping
                                    {
                                        Description = string.Empty,
                                        PreviousStructureId = wrd.Id,
                                        StructureId = nWrd.Id
                                    };
                                    dc.StructureMappings.Add(strmap);
                                    dc.SaveChanges();
                                }

                                #endregion Save Wards
                            }

                            #endregion Create Union Councils
                        }

                        #endregion Copy Towns

                        #region Copy National Assembly Halkas

                        // get all national assembly halkas of this district
                        var parentNAs = (from aa in dc.Structures.OfType<NationalAssemblyHalka>()
                            where aa.DistrictId == dis.Id && aa.ElectionId == parentElectionId
                            select aa).ToList();

                        foreach (var na in parentNAs)
                        {
                            var nNA = new NationalAssemblyHalka
                            {
                                Area = na.Area,
                                AverageHouseholdIncome = na.AverageHouseholdIncome,
                                Castes = na.Castes,
                                Code = na.Code,
                                CreatedBy = user,
                                CreatedDate = DateTime.Now,
                                ElectionId = newElectionId,
                                EnglishTitle = na.EnglishTitle,
                                FemaleVoters = na.FemaleVoters,
                                GeneralTrivia = na.GeneralTrivia,
                                ImportantAreas = na.ImportantAreas,
                                ImportantPoliticalPersonalities = na.ImportantPoliticalPersonalities,
                                IsSeat = na.IsSeat,
                                Languages = na.Languages,
                                LiteracyRate = na.LiteracyRate,
                                MajorityIncomeSource = na.MajorityIncomeSource,
                                MaleVoters = na.MaleVoters,
                                Population = na.Population,
                                PrevStructureId = na.Id,
                                RuralAreaPer = na.RuralAreaPer,
                                TotalPollingStations = na.TotalPollingStations,
                                UrbanAreaPer = na.UrbanAreaPer,
                                Trivia = na.Trivia,
                                UrduTitle = na.UrduTitle,
                                Problems = na.Problems,
                                DistrictId = nDis.Id
                            };
                            if (na.AssemblyId != null)
                            {
                                var newAsmb = assmblyMappings.FirstOrDefault(c => c.ParentAssemblyId == na.AssemblyId);
                                if (newAsmb != null) nNA.AssemblyId = newAsmb.NewAssemblyId;
                            }

                            dc.Structures.Add(nNA);
                            dc.SaveChanges();
                        }

                        #endregion Copy National Assembly Halkas

                        #region Copy Provincial Assembly Halkas

                        var parentPAs = (from aa in dc.Structures.OfType<ProvincialAssemblyHalka>()
                            where aa.DistrictId == dis.Id && aa.ElectionId == parentElectionId
                            select aa).ToList();
                        foreach (var pa in parentPAs)
                        {
                            var nPA = new ProvincialAssemblyHalka
                            {
                                Area = pa.Area,
                                AverageHouseholdIncome = pa.AverageHouseholdIncome,
                                Castes = pa.Castes,
                                Code = pa.Code,
                                CreatedBy = user,
                                CreatedDate = DateTime.Now,
                                ElectionId = newElectionId,
                                EnglishTitle = pa.EnglishTitle,
                                FemaleVoters = pa.FemaleVoters,
                                GeneralTrivia = pa.GeneralTrivia,
                                ImportantAreas = pa.ImportantAreas,
                                ImportantPoliticalPersonalities = pa.ImportantPoliticalPersonalities,
                                IsSeat = pa.IsSeat,
                                Languages = pa.Languages,
                                LiteracyRate = pa.LiteracyRate,
                                MajorityIncomeSource = pa.MajorityIncomeSource,
                                MaleVoters = pa.MaleVoters,
                                Population = pa.Population,
                                PrevStructureId = pa.Id,
                                RuralAreaPer = pa.RuralAreaPer,
                                TotalPollingStations = pa.TotalPollingStations,
                                UrbanAreaPer = pa.UrbanAreaPer,
                                Trivia = pa.Trivia,
                                UrduTitle = pa.UrduTitle,
                                Problems = pa.Problems,
                                DistrictId = nDis.Id
                            };

                            if (pa.AssemblyId != null)
                            {
                                var newAsmb = assmblyMappings.FirstOrDefault(c => c.ParentAssemblyId == pa.AssemblyId);
                                if (newAsmb != null) nPA.AssemblyId = newAsmb.NewAssemblyId;
                            }

                            dc.Structures.Add(nPA);
                            dc.SaveChanges();
                        }

                        #endregion Copy Provincial Assembly Halkas
                    }

                    #endregion Create Districts
                }

                #endregion Create Divisions
            }

            #endregion Create Administrative Units

            // Get Child Election Object
            var ce = (from aa in dc.Elections where aa.Id == newElectionId select aa).FirstOrDefault();
            if (ce != null)
            {
                ce.ParentElectionId = parentElectionId;
                ce.ModifiedBy = user;
                ce.ModifiedDate = DateTime.Now;
                dc.SaveChanges();
            }

            tran.Commit();
            return Task.FromResult("OK");
        }
        catch (Exception ex)
        {
            tran.Rollback();
            return Task.FromResult(ex.Message);
        }
    }

    public Task<string> CopyElection2(
        int electionId,
        string EnglishTitle,
        string UrduTitle,
        DateTime electionDate,
        int yearFrom,
        int yearTo,
        string user)
    {
        using var dc = _contextFactory.CreateDbContext();
        using (var tran = dc.Database.BeginTransaction())
        {
            try
            {
                //1. copy election and Election Phases
                var pElec = dc.Elections.Find(electionId);
                var elec = new Election
                {
                    CreatedBy = user,
                    CreatedDate = DateTime.Now,
                    Description = string.Empty,
                    UrduTitle = UrduTitle,
                    YearFrom = yearFrom,
                    YearTo = yearTo,
                    EnglishTitle = EnglishTitle,
                    ElectionPhases =
                        new List<ElectionPhase>
                        {
                            new()
                            {
                                CreatedBy = user,
                                CreatedDate = DateTime.Now,
                                StartDate = electionDate,
                                Title = "Phase 1"
                            }
                        }
                };
                dc.Elections.Add(elec);
                dc.SaveChanges();
                //2. Copy Structure
                //a. Copy provinces
                var qp = (from aa in dc.Structures.OfType<Province>() where aa.ElectionId == electionId select aa)
                    .ToList(
                    );
                // Copy each province of this election
                foreach (var p in qp)
                {
                    var np = new Province
                    {
                        Area = p.Area,
                        ElectionId = elec.Id,
                        AverageHouseholdIncome = p.AverageHouseholdIncome,
                        Castes = p.Castes,
                        ImportantAreas = p.ImportantAreas,
                        Code = p.Code,
                        CreatedBy = user,
                        CreatedDate = DateTime.Now,
                        EnglishTitle = p.EnglishTitle,
                        FemaleVoters = p.FemaleVoters,
                        GeneralTrivia = p.GeneralTrivia,
                        ImportantPoliticalPersonalities = p.ImportantPoliticalPersonalities,
                        IsSeat = p.IsSeat,
                        Languages = p.Languages,
                        LiteracyRate = p.LiteracyRate,
                        MajorityIncomeSource = p.MajorityIncomeSource,
                        MaleVoters = p.MaleVoters,
                        RuralAreaPer = p.RuralAreaPer,
                        Population = p.Population,
                        Problems = p.Problems,
                        TotalPollingStations = p.TotalPollingStations,
                        Trivia = p.Trivia,
                        UrbanAreaPer = p.UrbanAreaPer,
                        UrduTitle = p.UrduTitle,
                        PrevStructureId = p.Id
                    };

                    dc.Structures.Add(np);
                    dc.SaveChanges();

                    #region Copy Regions

                    //b. Copy regions
                    var qrs = (from aa in dc.Regions where aa.ProvinceId == p.Id select aa).ToList();

                    foreach (var r in qrs)
                    {
                        var nr = new Region
                        {
                            CreatedBy = user,
                            CreatedDate = DateTime.Now,
                            EnglishTitle = r.EnglishTitle,
                            ProvinceId = np.Id,
                            UrduTitle = r.UrduTitle
                        };
                        dc.Regions.Add(nr);
                        dc.SaveChanges();
                    }

                    #endregion Copy Regions

                    #region Copy Divisions

                    var divisions = (from aa in dc.Structures.OfType<Division>() where aa.ProvinceId == p.Id select aa)
                        .ToList(
                        );

                    foreach (var div in divisions)
                    {
                        var newDiv = new Division
                        {
                            Area = div.Area,
                            AverageHouseholdIncome = div.AverageHouseholdIncome,
                            Castes = div.Castes,
                            Code = div.Code,
                            CreatedBy = user,
                            CreatedDate = DateTime.Now,
                            ElectionId = elec.Id,
                            ImportantAreas = div.ImportantAreas,
                            EnglishTitle = div.EnglishTitle,
                            FemaleVoters = div.FemaleVoters,
                            GeneralTrivia = div.GeneralTrivia,
                            ImportantPoliticalPersonalities = div.ImportantPoliticalPersonalities,
                            IsSeat = div.IsSeat,
                            Languages = div.Languages,
                            LiteracyRate = div.LiteracyRate,
                            MajorityIncomeSource = div.MajorityIncomeSource,
                            MaleVoters = div.MaleVoters,
                            Population = div.Population,
                            PrevStructureId = div.Id,
                            Problems = div.Problems,
                            ProvinceId = p.Id,
                            RuralAreaPer = div.RuralAreaPer,
                            TotalPollingStations = div.TotalPollingStations,
                            Trivia = div.Trivia,
                            UrbanAreaPer = div.UrbanAreaPer,
                            Id = 0,
                            UrduTitle = div.UrduTitle
                        };
                        dc.Structures.Add(newDiv);
                        dc.SaveChanges();

                        #region Copy Districts

                        var districts = (from aa in dc.Structures.OfType<District>()
                            where aa.DivisionId == div.Id
                            select aa).ToList();

                        foreach (var dist in districts)
                        {
                            var newDist = new District
                            {
                                Area = dist.Area,
                                AverageHouseholdIncome = dist.AverageHouseholdIncome,
                                Castes = dist.Castes,
                                Code = dist.Code,
                                CreatedBy = user,
                                CreatedDate = DateTime.Now,
                                ElectionId = elec.Id,
                                ImportantAreas = dist.ImportantAreas,
                                EnglishTitle = dist.EnglishTitle,
                                FemaleVoters = dist.FemaleVoters,
                                GeneralTrivia = dist.GeneralTrivia,
                                ImportantPoliticalPersonalities = dist.ImportantPoliticalPersonalities,
                                IsSeat = dist.IsSeat,
                                Languages = dist.Languages,
                                LiteracyRate = dist.LiteracyRate,
                                MajorityIncomeSource = dist.MajorityIncomeSource,
                                MaleVoters = dist.MaleVoters,
                                Population = dist.Population,
                                PrevStructureId = dist.Id,
                                Problems = dist.Problems,
                                DivisionId = newDiv.Id,
                                RuralAreaPer = dist.RuralAreaPer,
                                TotalPollingStations = dist.TotalPollingStations,
                                Trivia = dist.Trivia,
                                UrbanAreaPer = dist.UrbanAreaPer,
                                UrduTitle = dist.UrduTitle,
                                RegionId = 1
                            };
                        }

                        #endregion Copy Districts
                    }

                    #endregion Copy Divisions
                }

                //b. Copy regions

                tran.Commit();
                return Task.FromResult("OK");
            }
            catch (Exception)
            {
                tran.Rollback();
                throw;
            }
        }
    }

    public Task<string> CreateElection(ElectionDTO elc, string user)
    {
        using var dc = _contextFactory.CreateDbContext();
        if (elc.Id == 0)
        {
            var obj = new Election
            {
                Description = elc.Description,
                EnglishTitle = elc.EnglishTitle,
                UrduTitle = elc.UrduTitle,
                YearFrom = elc.YearFrom,
                YearTo = elc.YearTo,
                ElectionType = elc.IsGeneral ? ElectionType.General : ElectionType.LocalBody,
                CandidatePoll = (CandidatePoll)elc.CandidatePoolId,
                CreatedBy = user,
                CreatedDate = DateTime.Now
            };
            obj.ElectionPhases = new List<ElectionPhase>();
            obj.ElectionPhases.Add(new ElectionPhase { Title = "Phase 1", StartDate = (DateTime)elc.StartDate });

            dc.Add(obj);
            dc.SaveChanges();
            return Task.FromResult("OK");
        }

        var eObj = (from aa in dc.Elections where aa.Id == elc.ElectionId select aa).FirstOrDefault();

        eObj.EnglishTitle = elc.EnglishTitle;
        eObj.Description = elc.Description;
        eObj.UrduTitle = elc.UrduTitle;
        eObj.YearFrom = elc.YearFrom;
        eObj.YearTo = elc.YearTo;
        eObj.ElectionType = elc.IsGeneral ? ElectionType.General : ElectionType.LocalBody;
        eObj.CandidatePoll = (CandidatePoll)elc.CandidatePoolId;
        eObj.ModifiedBy = user;
        eObj.ModifiedDate = DateTime.Now;
        dc.SaveChanges();

        var p = (from aa in dc.ElectionPhases where aa.Id == elc.Id select aa).FirstOrDefault();

        p.StartDate = (DateTime)elc.StartDate;
        p.Title = elc.PhaseTitle;
        dc.SaveChanges();
        return Task.FromResult("OK");
    }

    public Task<string> CreateElectionPhase(ElectionPhaseDTO phase, string user)
    {
        using var dc = _contextFactory.CreateDbContext();
        phase.Title = phase.Title.Trim();
        var exist = (from aa in dc.ElectionPhases
            where aa.ElectionId == phase.ElectionId && aa.Title == phase.Title
            select aa).Any();

        if (exist)
            throw new Exception("Phase already exist");

        var ph = new ElectionPhase
        {
            ElectionId = phase.ElectionId,
            Title = phase.Title,
            StartDate = phase.ElectionDate,
            CreatedBy = user,
            CreatedDate = DateTime.Now
        };
        dc.ElectionPhases.Add(ph);
        dc.SaveChanges();
        return Task.FromResult("OK");
    }

    public Task<string> CreatePhase(int electionId, DateTime startDate, string user)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.ElectionPhases where aa.ElectionId == electionId select aa).Count();

        var obj = new ElectionPhase { ElectionId = electionId, StartDate = startDate, Title = "Phase " + (q + 1) };
        dc.Add(obj);
        dc.SaveChanges();
        return Task.FromResult("OK");
    }

    public Task<string> ExcelExportData(int phaseId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var election = (from aa in dc.ElectionPhases
            where aa.Id == phaseId
            select new
            {
                PhaseId = aa.Id,
                Phase = aa.Title,
                aa.ElectionId,
                aa.StartDate,
                ElectionTitleEnglish = aa.Election.EnglishTitle,
                ElectionTitleUrdu = aa.Election.UrduTitle
            }).FirstOrDefault();
        var asmblies = (from aa in dc.ElectionAssemblies where aa.ElectionId == election.ElectionId select aa).ToList();
        ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
        foreach (var asmb in asmblies)
        {
            var file = new FileInfo(
                @"c:\temp\" + election.ElectionTitleEnglish + "-" + election.Phase + "-" + asmb.EnglishTitle + ".xlsx");
            using (var package = new ExcelPackage(file))
            {
                #region Save Constituencies

                var sheet = package.Workbook.Worksheets.Add("Constituencies");
                var cons = (from aa in dc.Structures.OfType<ProvincialAssemblyHalka>()
                    where aa.ElectionId == election.ElectionId && aa.AssemblyId == asmb.Id
                    select new
                    {
                        aa.Id,
                        aa.Code,
                        aa.EnglishTitle,
                        aa.UrduTitle,
                        aa.MaleVoters,
                        aa.FemaleVoters,
                        aa.TotalPollingStations,
                        aa.ImportantAreas,
                        aa.Problems,
                        District = aa.District.EnglishTitle,
                        DistrictUrdu = aa.District.UrduTitle,
                        Region = aa.District.Region.EnglishTitle,
                        RegionUrdu = aa.District.Region.UrduTitle,
                        Province = aa.District.Division.Province.EnglishTitle,
                        ProvinceUrdu = aa.District.Division.Province.UrduTitle
                    }).ToList();

                sheet.Cells[1, 1].Value = "Id";
                sheet.Cells[1, 2].Value = "Code";
                sheet.Cells[1, 3].Value = "Constituency (English)";
                sheet.Cells[1, 4].Value = "Constituency (Urdu)";
                sheet.Cells[1, 5].Value = "Male Voters";
                sheet.Cells[1, 6].Value = "Female Voters";
                sheet.Cells[1, 7].Value = "Register Voters";
                sheet.Cells[1, 8].Value = "Total Polling Stations";
                sheet.Cells[1, 9].Value = "Important Areas";
                sheet.Cells[1, 10].Value = "Problems";
                sheet.Cells[1, 11].Value = "District (English)";
                sheet.Cells[1, 12].Value = "District (Urdu)";
                sheet.Cells[1, 13].Value = "Region (English)";
                sheet.Cells[1, 14].Value = "Region (Urdu)";
                sheet.Cells[1, 15].Value = "Province (English)";
                sheet.Cells[1, 16].Value = "Province (Urdu)";
                var idx = 2;
                foreach (var cc in cons)
                {
                    sheet.Cells[idx, 1].Value = cc.Id;
                    sheet.Cells[idx, 2].Value = cc.Code;
                    sheet.Cells[idx, 3].Value = cc.EnglishTitle;
                    sheet.Cells[idx, 4].Value = cc.UrduTitle;
                    sheet.Cells[idx, 5].Value = cc.MaleVoters;
                    sheet.Cells[idx, 6].Value = cc.FemaleVoters;
                    sheet.Cells[idx, 7].Value = cc.MaleVoters ?? 0 + cc.FemaleVoters ?? 0;
                    sheet.Cells[idx, 8].Value = cc.TotalPollingStations;
                    sheet.Cells[idx, 9].Value = cc.ImportantAreas;
                    sheet.Cells[idx, 10].Value = cc.Problems;
                    sheet.Cells[idx, 11].Value = cc.District;
                    sheet.Cells[idx, 12].Value = cc.DistrictUrdu;
                    sheet.Cells[idx, 13].Value = cc.Region;
                    sheet.Cells[idx, 14].Value = cc.RegionUrdu;
                    sheet.Cells[idx, 15].Value = cc.Province;
                    sheet.Cells[idx, 16].Value = cc.ProvinceUrdu;
                    idx++;
                }

                #endregion Save Constituencies

                #region Nominations

                foreach (var cc in cons)
                {
                    var contsheet = package.Workbook.Worksheets.Add(cc.Code);
                    contsheet.Cells[1, 1].Value = "NominationId";
                    contsheet.Cells[1, 2].Value = "Candidate (English)";
                    contsheet.Cells[1, 3].Value = "Candidate (Urdu)";
                    contsheet.Cells[1, 4].Value = "Candidate (Gender)";
                    contsheet.Cells[1, 5].Value = "Candidate (Date Of Birth)";
                    contsheet.Cells[1, 6].Value = "Party (English)";
                    contsheet.Cells[1, 7].Value = "Party (Urdu)";
                    contsheet.Cells[1, 8].Value = "Party (Short)";
                    contsheet.Cells[1, 9].Value = "Symbol (English)";
                    contsheet.Cells[1, 10].Value = "Symbol (Urdu)";
                    contsheet.Cells[1, 11].Value = "Weight";
                    contsheet.Cells[1, 12].Value = "Is Withdraw";
                    contsheet.Cells[1, 13].Value = "Votes";
                    contsheet.Cells[1, 14].Value = "CandidateId";
                    contsheet.Cells[1, 15].Value = "PartyId";
                    contsheet.Cells[1, 16].Value = "SymbolId";

                    var noms = (from aa in dc.Nominations
                        where aa.StructureId == cc.Id &&
                              aa.ElectionPhaseId == phaseId &&
                              aa.ElectionAssemblyId == asmb.Id &&
                              aa.SeatTypeId == 1
                        select new
                        {
                            aa.Id,
                            NameEnglish = aa.Candidate.EnglishName,
                            NameUrdu = aa.Candidate.UrduName,
                            aa.Candidate.Gender,
                            aa.Candidate.DateOfBirth,
                            PartyEnglish = aa.Party.EnglishTitle,
                            PartyUrdu = aa.Party.UrduTitle,
                            PartyShort = aa.Party.ShortEnglishTitle,
                            SymbolEnglish = aa.Symbol != null ? aa.Symbol.EnglishTitle : string.Empty,
                            SymbolUrdu = aa.Symbol != null ? aa.Symbol.UrduTitle : string.Empty,
                            aa.Weight,
                            aa.IsWithdraw,
                            aa.Votes,
                            aa.CandidteId,
                            aa.PartyId,
                            aa.SymbolId
                        }).ToList();
                    var idx2 = 2;
                    foreach (var nom in noms)
                    {
                        contsheet.Cells[idx2, 1].Value = nom.Id;
                        contsheet.Cells[idx2, 2].Value = nom.NameEnglish;
                        contsheet.Cells[idx2, 3].Value = nom.NameUrdu;
                        contsheet.Cells[idx2, 4].Value = nom.Gender;
                        contsheet.Cells[idx2, 5].Value = nom.DateOfBirth;
                        contsheet.Cells[idx2, 6].Value = nom.PartyEnglish;
                        contsheet.Cells[idx2, 7].Value = nom.PartyUrdu;
                        contsheet.Cells[idx2, 8].Value = nom.PartyShort;
                        contsheet.Cells[idx2, 9].Value = nom.SymbolEnglish;
                        contsheet.Cells[idx2, 10].Value = nom.SymbolUrdu;
                        contsheet.Cells[idx2, 11].Value = nom.Weight;
                        contsheet.Cells[idx2, 12].Value = nom.IsWithdraw ? "Yes" : "No";
                        contsheet.Cells[idx2, 13].Value = nom.Votes;
                        ;
                        contsheet.Cells[idx2, 14].Value = nom.CandidteId;
                        ;
                        contsheet.Cells[idx2, 15].Value = nom.PartyId;
                        ;
                        contsheet.Cells[idx2, 16].Value = nom.SymbolId;
                        ;
                        idx2++;
                    }
                }

                #endregion Nominations

                #region Parties

                var partiesSheet = package.Workbook.Worksheets.Add("Parties");
                var parties = (from aa in dc.Nominations
                        where aa.ElectionAssemblyId == asmb.Id && aa.ElectionPhaseId == phaseId && aa.SeatTypeId == 1
                        select new
                        {
                            aa.PartyId, aa.Party.EnglishTitle, aa.Party.UrduTitle, aa.Party.ShortEnglishTitle
                        })
                    .Distinct(
                    )
                    .ToList();

                partiesSheet.Cells[1, 1].Value = "Id";
                partiesSheet.Cells[1, 2].Value = "Title (English)";
                partiesSheet.Cells[1, 3].Value = "Title (Urdu)";
                partiesSheet.Cells[1, 4].Value = "Title (Short)";

                var pidx = 2;
                foreach (var p in parties)
                {
                    partiesSheet.Cells[pidx, 1].Value = p.PartyId;
                    partiesSheet.Cells[pidx, 2].Value = p.EnglishTitle;
                    partiesSheet.Cells[pidx, 3].Value = p.UrduTitle;
                    partiesSheet.Cells[pidx, 4].Value = p.ShortEnglishTitle;
                    pidx++;
                }

                #endregion Parties

                #region Symbols

                var symbolSheet = package.Workbook.Worksheets.Add("Symbols");
                var symbols = (from aa in dc.Nominations
                        where aa.ElectionAssemblyId == asmb.Id &&
                              aa.ElectionPhaseId == phaseId &&
                              aa.SeatTypeId == 1 &&
                              aa.SymbolId != null
                        select new { aa.SymbolId, aa.Symbol.EnglishTitle, aa.Symbol.UrduTitle }).Distinct()
                    .ToList();

                symbolSheet.Cells[1, 1].Value = "Id";
                symbolSheet.Cells[1, 2].Value = "Title (English)";
                symbolSheet.Cells[1, 3].Value = "Title (Urdu)";

                pidx = 2;
                foreach (var p in symbols)
                {
                    symbolSheet.Cells[pidx, 1].Value = p.SymbolId;
                    symbolSheet.Cells[pidx, 2].Value = p.EnglishTitle;
                    symbolSheet.Cells[pidx, 3].Value = p.UrduTitle;
                    pidx++;
                }

                #endregion Symbols

                #region Candidates

                var cans = (from aa in dc.Nominations
                        where aa.ElectionAssemblyId == asmb.Id && aa.ElectionPhaseId == phaseId && aa.SeatTypeId == 1
                        select new
                        {
                            aa.CandidteId,
                            EnglishName = aa.Candidate.EnglishName.Trim(),
                            UrduName = aa.Candidate.UrduName.Trim(),
                            aa.Candidate.Gender,
                            aa.Candidate.DateOfBirth
                        }).Distinct()
                    .ToList();
                var canSheets = package.Workbook.Worksheets.Add("Candidates");
                canSheets.Cells[1, 1].Value = "Candidate Id";
                canSheets.Cells[1, 2].Value = "Name";
                canSheets.Cells[1, 3].Value = "Name (Urdu)";
                canSheets.Cells[1, 4].Value = "Gender";
                canSheets.Cells[1, 5].Value = "Date of Birth";

                pidx = 2;
                foreach (var c in cans)
                {
                    canSheets.Cells[pidx, 1].Value = c.CandidteId;
                    canSheets.Cells[pidx, 2].Value = c.EnglishName;
                    canSheets.Cells[pidx, 3].Value = c.UrduName;
                    canSheets.Cells[pidx, 4].Value = c.Gender;
                    canSheets.Cells[pidx, 5].Value = c.DateOfBirth;
                    pidx++;
                }

                {
                }

                #endregion Candidates

                package.Save();
            }
        }

        return Task.FromResult("OK");
    }

    public Task<List<ElectionDTO>> GetAllElections(string user)
    {
        using var dc = _contextFactory.CreateDbContext();
        var con = dc.Database.GetDbConnection();
        var pram = new { User = user };
        var res = con.Query<ElectionDTO>("app.GetAllElections", pram, commandType: CommandType.StoredProcedure)
            .ToList();
        return Task.FromResult(res);
    }

    public Task<List<ElectionDTO>> GetAll()
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.ElectionPhases
            orderby aa.Election.CandidatePoll == CandidatePoll.GeneralElection
                    ? "General"
                    : aa.Election.CandidatePoll == CandidatePoll.GilgitElection
                        ? "Gilgit Baltistan"
                        : aa.Election.CandidatePoll == CandidatePoll.KashmirElection
                            ? "Kashmir"
                            : aa.Election.CandidatePoll == CandidatePoll.LocalBodyElectionKPK
                                ? "Local-body KPK"
                                : aa.Election.CandidatePoll == CandidatePoll.Cantonment
                                    ? "Cantonment"
                                    : aa.Election.CandidatePoll == CandidatePoll.LocalBodyElectionSindh
                                        ? "Local-body Sindh"
                                        : aa.Election.CandidatePoll == CandidatePoll.LocalBodyElectionBalochistan
                                            ? "Local-body Balochistan"
                                            : aa.Election.CandidatePoll == CandidatePoll.LocalBodyElectionPunjab
                                                ? "Local-body Punjab"
                                                : aa.Election.CandidatePoll == CandidatePoll.LocalBodyAJK
                                                    ? "Local-body AJK"
                                                    : aa.Election.CandidatePoll == CandidatePoll.LocalBodyIslamabad
                                                        ? "Local-body Islamabad"
                                                        : string.Empty,
                aa.Election.EnglishTitle,
                aa.StartDate descending
            select new ElectionDTO
            {
                Id = aa.Id,
                ElectionId = aa.ElectionId,
                EnglishTitle = aa.Election.EnglishTitle,
                UrduTitle = aa.Election.UrduTitle,
                PhaseTitle = aa.Title,
                StartDate = aa.StartDate,
                Description = aa.Election.Description,
                YearFrom = aa.Election.YearFrom,
                YearTo = aa.Election.YearTo,
                ElectionType = aa.Election.ElectionType ?? ElectionType.General,
                Head =
                    aa.Election.CandidatePoll == CandidatePoll.GeneralElection
                        ? "General"
                        : aa.Election.CandidatePoll == CandidatePoll.GilgitElection
                            ? "Gilgit Baltistan"
                            : aa.Election.CandidatePoll == CandidatePoll.KashmirElection
                                ? "Kashmir"
                                : aa.Election.CandidatePoll == CandidatePoll.LocalBodyElectionKPK
                                    ? "Local-body KPK"
                                    : aa.Election.CandidatePoll == CandidatePoll.Cantonment
                                        ? "Cantonment"
                                        : aa.Election.CandidatePoll == CandidatePoll.LocalBodyElectionSindh
                                            ? "Local-body Sindh"
                                            : aa.Election.CandidatePoll == CandidatePoll.LocalBodyElectionBalochistan
                                                ? "Local-body Balochistan"
                                                : aa.Election.CandidatePoll == CandidatePoll.LocalBodyElectionPunjab
                                                    ? "Local-body Punjab"
                                                    : aa.Election.CandidatePoll == CandidatePoll.LocalBodyAJK
                                                        ? "Local-body AJK"
                                                        : aa.Election.CandidatePoll == CandidatePoll.LocalBodyIslamabad
                                                            ? "Local-body Islamabad"
                                                            : string.Empty
            }).ToList();

        foreach (var i in q)
            if (i.PhaseTitle == "Phase 1")
                i.CanDelete = false;
            else
                i.CanDelete = true;

        return Task.FromResult(q);
    }

    public Task<List<PieDataDTO>> GetAssemblyWiseNominations(int electionId, int phaseId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.Nominations
                where aa.ElectionPhaseId == phaseId && aa.ElectionPhase.ElectionId == electionId &&
                      aa.IsWithdraw == false
                select new { aa.ElectionAssembly.Id, aa.ElectionAssembly.EnglishTitle }).Distinct()
            .ToList();

        var op = new List<PieDataDTO>();
        foreach (var e in q)
        {
            var cnt = (from aa in dc.Nominations
                where aa.ElectionPhaseId == phaseId &&
                      aa.ElectionPhase.ElectionId == electionId &&
                      aa.ElectionAssemblyId == e.Id &&
                      aa.IsWithdraw == false
                select new { aa.ElectionAssembly.Id, aa.ElectionAssembly.EnglishTitle }).Count();
            op.Add(new PieDataDTO { xValue = e.EnglishTitle, yValue = cnt });
        }

        return Task.FromResult(op);
    }

    public Task<List<GeneralItemDTO>> GetChildElections()
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.Elections
            where aa.ParentElectionId == null &&
                  !dc.Structures.Any(c => c.ElectionId == aa.Id) &&
                  !dc.ElectionAssemblies.Any(c => c.ElectionId == aa.Id)
            select new GeneralItemDTO
            {
                Id = aa.Id,
                EnglishTitle = aa.EnglishTitle,
                Province = string.Empty,
                UrduTitle = aa.UrduTitle
            }).ToList();
        return Task.FromResult(q);
    }

    public Task<List<GeneralItemDTO>> GetParentElections()
    {
        using var dc = _contextFactory.CreateDbContext();
        var ge = new List<GeneralItemDTO>();
        // get all elections which are in used in child elections
        var childElections = (from aa in dc.Elections
            where aa.ParentElectionId != null
            select aa.ParentElectionId).ToList();

        var q = (from aa in dc.Elections
            where !childElections.Contains(aa.Id)
            //where aa.ParentElectionId == null && aa.Id==4
            //&& aa.Id==4
            orderby aa.EnglishTitle
            select new GeneralItemDTO
                { Id = aa.Id, EnglishTitle = aa.EnglishTitle, UrduTitle = aa.UrduTitle }).ToList();

        foreach (var e in q)
        {
            var ss = dc.Structures.Any(m => m.ElectionId == e.Id);
            if (ss) ge.Add(e);
        }

        return Task.FromResult(ge);
    }

    public Task<List<PieDataDTO>> GetPartyWiseNominations(int electionId, int phaseId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.Nominations
                where aa.ElectionPhaseId == phaseId && aa.ElectionPhase.ElectionId == electionId
                select new { aa.PartyId, aa.Party.EnglishTitle }).Distinct()
            .ToList();

        var op = new List<PieDataDTO>();
        foreach (var e in q)
        {
            var cnt = (from aa in dc.Nominations
                where aa.ElectionPhaseId == phaseId &&
                      aa.ElectionPhase.ElectionId == electionId &&
                      aa.PartyId == e.PartyId
                select aa).Count();
            op.Add(new PieDataDTO { xValue = e.EnglishTitle, yValue = cnt });
        }

        return Task.FromResult(op);
    }

    public Task<int> GetTotalAssemblies(int electionId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.ElectionAssemblies where aa.ElectionId == electionId select aa).Count();
        return Task.FromResult(q);
    }

    public Task<int> GetTotalCandidatges()
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.Candidates select aa).Count();
        return Task.FromResult(q);
    }

    public Task<int> GetTotalConstituencies(int phaseId)
    {
        //var q = (from aa in dc.Structures
        //			where aa.ElectionId == electionId &&
        //			aa.AssemblyId != null
        //			select aa).Count();
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.Nominations
                where
                    //aa.IsWithdraw == false &&
                    aa.Structure.IsPostponed == false && aa.ElectionPhaseId == phaseId //&&
                //aa.IsWithdraw == false
                select aa.StructureId).Distinct()
            .Count();

        return Task.FromResult(q);
    }

    public Task<int> GetTotalNamanigars()
    {
        using var dc = _contextFactory.CreateDbContext();
        return Task.FromResult(dc.Namanigars.Count());
    }

    public Task<int> GetTotalNominations(int electionId, int phaseId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.Nominations
            where aa.ElectionPhaseId == phaseId && aa.ElectionPhase.ElectionId == electionId && aa.IsWithdraw == false
            select aa).Count();
        return Task.FromResult(q);
    }

    public Task<int> GetTotalParties(int phaseId)
    {
        using var dc = _contextFactory.CreateDbContext();
        //return Task.FromResult(dc.Parties.Count());
        var pc = (from aa in dc.Nominations
                where aa.ElectionPhaseId == phaseId && aa.Structure.IsPostponed == false
                //&& aa.IsWithdraw == false
                select aa.PartyId).Distinct()
            .Count();
        return Task.FromResult(pc);
    }

    public Task<string> TranslateText(string input)
    {
        // Set the language from/to in the url (or pass it into this function)
        var url = string.Format(
            "https://translate.googleapis.com/translate_a/single?client=gtx&sl={0}&tl={1}&dt=t&q={2}",
            "en",
            "ur",
            Uri.EscapeUriString(input));
        var httpClient = new HttpClient();
        var result = httpClient.GetStringAsync(url).Result;
        var obj = JsonConvert.DeserializeObject(result);

        // Get all json data
        /*JArray jsonData = JArray.Parse( new JavaScriptSerializer().Deserialize<List<dynamic>>(result))*/
        var jsonArray = JArray.Parse(result);
        var translation = JArray.Parse(JArray.Parse(jsonArray[0].ToString())[0].ToString())[0].ToString();

        return Task.FromResult(translation);
    }

    public Task<string> UpdateElection(ElectionDTO obj, string user)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.Elections where aa.Id == obj.ElectionId select aa).FirstOrDefault();

        q.EnglishTitle = obj.EnglishTitle;
        q.Description = obj.Description;
        q.UrduTitle = obj.UrduTitle;
        q.YearFrom = obj.YearFrom;
        q.YearTo = obj.YearTo;
        dc.SaveChanges();

        var q2 = (from aa in dc.ElectionPhases where aa.Id == obj.Id select aa).FirstOrDefault();
        q2.StartDate = (DateTime)obj.StartDate;
        dc.SaveChanges();

        return Task.FromResult("OK");
    }

    public Task<List<ElectionDTO>> GetAllElectionDates(int electionId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var con = dc.Database.GetDbConnection();
        var pram = new { ElectionId = electionId };
        var res = con.Query<ElectionDTO>("app.GetAllElectionDates", pram, commandType: CommandType.StoredProcedure)
            .ToList();
        return Task.FromResult(res);
    }


    public Task<List<NominationCompDTO>> GetNominationStatistics(int phaseId)
    {
        var dc = _contextFactory.CreateDbContext();
        var db = dc.Database.GetDbConnection();
        var pram = new { PhaseId = phaseId };
        var res = db
            .Query<NominationCompDTO>("rpt.GetNominationStatistics", pram, commandType: CommandType.StoredProcedure)
            .ToList();
        return Task.FromResult(res);
    }
}