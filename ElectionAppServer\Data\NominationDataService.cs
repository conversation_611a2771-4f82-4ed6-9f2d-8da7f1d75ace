﻿using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Dapper;
using ElectionAppServer.DTO;
using ElectionAppServer.Model;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;

namespace ElectionAppServer.Data;

public class NominationDataService
{
    private readonly IDbContextFactory<ApplicationDbContext> _contextFactory;

    public NominationDataService(IDbContextFactory<ApplicationDbContext> contextFactory) // (ApplicationDbContext  dc)
    {
        //this.dc = dc;
        _contextFactory = contextFactory;
    }

    public Task<string> DeleteRecord(int Id, int electionId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var obj = dc.Nominations.Find(Id);
        var candidateId = obj.CandidteId;
        dc.Nominations.Remove(obj);
        dc.SaveChanges();
        UpdateCandidateSearchLog(candidateId, electionId, dc);
        return Task.FromResult("OK");
    }

    public Task<List<GeneralItemDTO>> GetAllElections()
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.Elections
            orderby aa.EnglishTitle
            select new GeneralItemDTO
            {
                Id = aa.Id,
                EnglishTitle = aa.EnglishTitle,
                UrduTitle = aa.UrduTitle
            }).ToList();
        return Task.FromResult(q);
    }

    public Task<List<GeneralItemDTO>> GetAssmblies(int electionId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.ElectionAssemblies
            where aa.ElectionId == electionId
            orderby aa.EnglishTitle
            select new GeneralItemDTO
            {
                Id = aa.Id,
                EnglishTitle = aa.EnglishTitle,
                Province = "",
                UrduTitle = aa.UrduTitle
            }).ToList();
        //q.Insert(0, new GeneralItemDTO { Id = 0, EnglishTitle = "Select - Assembly", UrduTitle = "", Province = "" });
        return Task.FromResult(q);
    }

    public Task<int?> GetCandidateCurrentParty(int candidateId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.PartyAffiliations
            where aa.CandidateId == candidateId
            orderby aa.DateFrom descending
            select aa).FirstOrDefault();
        int? op = null;
        if (q != null) op = q.PartyId;
        return Task.FromResult(op);
    }

    public Task<CandidateDTO> GetCandidateNominations(int electionId, int candidateId)
    {
        using var dc = _contextFactory.CreateDbContext();
        using var con = dc.Database.GetDbConnection();
        var prams = new { ElectionId = electionId, CandidateId = candidateId };
        var res = con.Query<CandidateDTO>("spGetCandidatsNominations", prams, commandType: CommandType.StoredProcedure)
            .FirstOrDefault();

        return Task.FromResult(res);
    }

    public Task<CandidateDTO> GetCandidateNominations2(int electionId, int candidateId)
    {
        //throw new NotImplementedException();
        using var dc = _contextFactory.CreateDbContext();

        SqlDataAdapter da = new("spGetCandidatsNominations", dc.Database.GetConnectionString());
        da.SelectCommand.CommandType = CommandType.StoredProcedure;
        da.SelectCommand.Parameters.AddWithValue("@ElectionId", electionId);
        da.SelectCommand.Parameters.AddWithValue("@CandidateId", candidateId);
        var dt = new DataTable();
        da.Fill(dt);
        //List<CandidateDTO> op = new();
        if (dt.Rows.Count > 0)
        {
            var r = dt.Rows[0];
            CandidateDTO cc = new()
            {
                Id = int.Parse(r["Id"].ToString()),
                FullName = r["FullName"].ToString(),
                CandidateTypeStr = r["CandidateType"].ToString(),
                EnglishName = r["EnglishName"].ToString(),
                Gender = int.Parse(r["Gender"].ToString() == "" ? "1" : r["Gender"].ToString()),
                UrduFatherName = r["UrduFatherName"].ToString(),
                UrduName = r["UrduName"].ToString(),
                CreatedBy = r["CreatedBy"].ToString(),
                ModifiedBy = r["ModifiedBy"].ToString(),
                CreatedOn = DateTime.Parse(r["CreatedOn"].ToString()).ToString("d MMM, yyyy h:mm"),
                Constituencies = r["Constituencies"].ToString(),
                DisableDelete = r["NominationCount"].ToString() != "0",
                District = r["District"].ToString()
            };
            cc.PictureURL = $"/media/candidates/{cc.Id}.jpg";
            cc.CreatedOn = r["CreatedOn"].ToString();
            cc.ModifiedOn = r["ModifiedOn"].ToString();
            cc.DateOfBirthStr = r["DateOfBirth"].ToString();
            cc.IsFreshStr = r["Status"].ToString();

            if (!File.Exists(Directory.GetCurrentDirectory() + $"\\wwwroot\\media\\candidates\\{cc.Id}.jpg"))
                //\wwwroot\media\candidates\Blank.jpg
                cc.PictureURL = "/media/candidates/Blank.jpg";
            else
                cc.PictureURL = $"/media/candidates/{cc.Id}.jpg";

            return Task.FromResult(cc);
        }

        return null;
    }

    internal Task<PanelistDTO> GetPanelist(int id)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.NominationPanelists
            where aa.Id == id
            select new PanelistDTO
            {
                Id = aa.Id,
                Gender = (int)aa.Gender,
                NameEnglish = aa.NameEnglish,
                NameUrdu = aa.NameUrdu,
                SeatTypeId = aa.SeatTypeId,
                SeatType = aa.SeatType.EnglishTitle,
                NominationId = aa.NominationId
            }).FirstOrDefault();
        return Task.FromResult(q);
    }

    public Task<List<GeneralItemDTO>> GetCandidatesList(int electionId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.Candidates
            //where aa.ElectionId == electionId
            orderby aa.EnglishName
            select new GeneralItemDTO
            {
                Id = aa.Id,
                EnglishTitle = $"{aa.Id} - {aa.EnglishName} - {aa.UrduName}" +
                               (aa.CandidateType == NominationType.Panel ? " (Panel)" : ""),
                UrduTitle = aa.UrduName
            }).ToList();
        foreach (var i in q)
        {
            var cp = (from aa in dc.PartyAffiliations
                where aa.CandidateId == i.Id
                orderby aa.DateFrom descending
                select new { aa.PartyId, Party = aa.Party.EnglishTitle }).FirstOrDefault();
            if (cp != null) i.EnglishTitle += " (Party: " + cp.Party + ")";
        }

        return Task.FromResult(q);
    }

    public Task<string> GetCandidateUrlById(int? candidateId)
    {
        if (candidateId == null)
            return Task.FromResult("/media/candidates/blank.jpg");
        if (File.Exists(Directory.GetCurrentDirectory() + $"\\wwwroot\\media\\candidates\\{candidateId}.jpg"))
            return Task.FromResult($"/media/candidates/{candidateId}.jpg");
        return Task.FromResult("/media/candidates/blank.jpg");
    }

    public Task<List<GeneralItemDTO>> GetConstituencies(int assemblyId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var con = dc.Database.GetDbConnection();
        var prams = new { AssemblyId = assemblyId };
        var res = con
            .Query<GeneralItemDTO>("app.spGetAssemblyConstituencies", prams, commandType: CommandType.StoredProcedure)
            .ToList();
        return Task.FromResult(res);
    }

    public Task<List<GeneralItemDTO>> GetConstituencies2(int assemblyId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var conNA = (from aa in dc.Structures.OfType<NationalAssemblyHalka>()
            where aa.AssemblyId == assemblyId
            orderby aa.EnglishTitle
            select new GeneralItemDTO
            {
                Id = aa.Id,
                EnglishTitle = $"{aa.Code} - {aa.EnglishTitle} - District {aa.District.EnglishTitle}"
            }).ToList();

        var conPA = (from aa in dc.Structures.OfType<ProvincialAssemblyHalka>()
            where aa.AssemblyId == assemblyId
            orderby aa.EnglishTitle
            select new GeneralItemDTO
            {
                Id = aa.Id,
                EnglishTitle = $"{aa.Code} - {aa.EnglishTitle} - District {aa.District.EnglishTitle}"
            }).ToList();

        var conTier1 = (from aa in dc.Structures.OfType<Town>()
            where aa.AssemblyId == assemblyId
            orderby aa.EnglishTitle
            select new GeneralItemDTO
            {
                Id = aa.Id,
                EnglishTitle = $"{aa.Code} - {aa.EnglishTitle} - District {aa.District.EnglishTitle}"
            }).ToList();

        var conTier2 = (from aa in dc.Structures.OfType<UnionCouncil>()
            where aa.AssemblyId == assemblyId
            orderby aa.EnglishTitle
            select new GeneralItemDTO
            {
                Id = aa.Id,
                EnglishTitle =
                    $"{aa.Code} - {aa.EnglishTitle} - {aa.Town.EnglishTitle}, {aa.Town.District.EnglishTitle}"
            }).ToList();

        var conTier3 = (from aa in dc.Structures.OfType<Ward>()
            where aa.AssemblyId == assemblyId
            orderby aa.EnglishTitle
            select new GeneralItemDTO
            {
                Id = aa.Id,
                EnglishTitle =
                    $"{aa.Code} - {aa.EnglishTitle} - {aa.UnionCouncil.EnglishTitle}, {aa.UnionCouncil.Town.EnglishTitle}, {aa.UnionCouncil.Town.District.EnglishTitle}"
            }).ToList();

        var cons = conNA.Union(conPA).Union(conTier1).Union(conTier2).Union(conTier3).OrderBy(k => k.EnglishTitle)
            .ToList();
        return Task.FromResult(cons);
    }

    public Task<List<GeneralItemDTO>> GetConstituencies3(int assemblyId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var qUCs = ( //from aa in dc.ElectionAssemblyConstituencies
            //join s in dc.Structures.OfType<UnionCouncil>() on aa.StructureId equals s.Id
            from s in dc.Structures.OfType<UnionCouncil>()
            orderby s.Town.EnglishTitle, s.EnglishTitle
            where s.AssemblyId == assemblyId &&
                  s.IsPostponed == false
            select new GeneralItemDTO
            {
                Id = s.Id,
                EnglishTitle =
                    $"{s.Code} - {s.EnglishTitle} ({s.Town.EnglishTitle} - District {s.Town.District.EnglishTitle})",
                UrduTitle = s.UrduTitle,
                Province = "Union Council"
            }).ToList();
        var qWards = ( //from aa in dc.ElectionAssemblyConstituencies
            //join s in dc.Structures.OfType<Ward>() on aa.StructureId equals s.Id
            from s in dc.Structures.OfType<Ward>()
            orderby s.UnionCouncil.Town.EnglishTitle, s.UnionCouncil.EnglishTitle, s.EnglishTitle
            where s.AssemblyId == assemblyId &&
                  s.IsPostponed == false
            select new GeneralItemDTO
            {
                Id = s.Id,
                EnglishTitle =
                    $"{s.Code} - {s.EnglishTitle} ({s.UnionCouncil.EnglishTitle} - {s.UnionCouncil.Town.EnglishTitle} - District {s.UnionCouncil.Town.District.EnglishTitle})",
                UrduTitle = s.UrduTitle,
                Province = "Ward"
            }).ToList();
        var qNAs = ( //from aa in dc.ElectionAssemblyConstituencies
            //join s in dc.Structures.OfType<NationalAssemblyHalka>() on aa.StructureId equals s.Id
            from s in dc.Structures.OfType<NationalAssemblyHalka>()
            orderby s.EnglishTitle
            where s.AssemblyId == assemblyId &&
                  s.IsPostponed == false
            select new GeneralItemDTO
            {
                Id = s.Id,
                EnglishTitle = $"{s.Code} - {s.EnglishTitle}",
                UrduTitle = s.UrduTitle,
                Province = "NA"
            }).ToList();
        var qPAs = ( //from aa in dc.ElectionAssemblyConstituencies
            //join s in dc.Structures.OfType<NationalAssemblyHalka>() on aa.StructureId equals s.Id
            from s in dc.Structures.OfType<ProvincialAssemblyHalka>()
            orderby s.EnglishTitle
            where s.AssemblyId == assemblyId &&
                  s.IsPostponed == false
            select new GeneralItemDTO
            {
                Id = s.Id,
                EnglishTitle = $"{s.Code} - {s.EnglishTitle}",
                UrduTitle = s.UrduTitle,
                Province = "PA"
            }).ToList();
        var cmb = new List<GeneralItemDTO>();
        foreach (var i in qUCs)
        {
            var exist = cmb.Any(c => c.Id == i.Id);
            if (!exist)
                cmb.Add(new GeneralItemDTO
                    { UrduTitle = i.UrduTitle, Id = i.Id, EnglishTitle = i.EnglishTitle, Province = i.Province });
        }

        foreach (var i in qWards)
        {
            var exist = cmb.Any(c => c.Id == i.Id);
            if (!exist)
                cmb.Add(new GeneralItemDTO
                    { UrduTitle = i.UrduTitle, Id = i.Id, EnglishTitle = i.EnglishTitle, Province = i.Province });
        }

        foreach (var i in qNAs)
        {
            var exist = cmb.Any(c => c.Id == i.Id);
            if (!exist)
                cmb.Add(new GeneralItemDTO
                    { UrduTitle = i.UrduTitle, Id = i.Id, EnglishTitle = i.EnglishTitle, Province = i.Province });
        }

        foreach (var i in qPAs)
        {
            var exist = cmb.Any(c => c.Id == i.Id);
            if (!exist)
                cmb.Add(new GeneralItemDTO
                    { UrduTitle = i.UrduTitle, Id = i.Id, EnglishTitle = i.EnglishTitle, Province = i.Province });
        }

        //cmb.Insert(0, new GeneralItemDTO { Id = 0, EnglishTitle = "Select Constituency", Province = "", UrduTitle = "" });
        cmb = (from aa in cmb
            orderby aa.EnglishTitle
            select aa).ToList();
        return Task.FromResult(cmb);
    }

    public Task<int> GetElectionId(int? assemblyId)
    {
        using var dc = _contextFactory.CreateDbContext();
        if (assemblyId == null)
            return Task.FromResult(0);
        if (assemblyId == 0)
            return Task.FromResult(0);
        var q = (from aa in dc.ElectionAssemblies
            where aa.Id == assemblyId
            select aa).FirstOrDefault();
        if (q != null)
            return Task.FromResult(q.ElectionId);
        throw new Exception("Assembly Id Not found");
    }

    public Task<string> GetFlagUrlById(int? partyId)
    {
        if (partyId == null)
            return Task.FromResult("/media/flags/missing-flag.png");
        if (File.Exists(Directory.GetCurrentDirectory() + $"\\wwwroot\\media\\flags\\{partyId}.jpg"))
            return Task.FromResult($"/media/flags/{partyId}.jpg");
        return Task.FromResult("/media/flags/missing-flag.png");
    }

    public Task<List<NominationDTO>> GetNominations(int electionId, int assemblyId, int phaseId, int structureId,
        int seatTypeId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.Nominations
            where aa.ElectionAssemblyId == assemblyId &&
                  aa.ElectionPhaseId == phaseId &&
                  aa.StructureId == structureId &&
                  aa.SeatTypeId == seatTypeId
            orderby aa.Weight == 0 ? 100000 : aa.Weight, aa.Candidate.EnglishName
            select new NominationDTO
            {
                ElectionId = aa.ElectionAssembly.ElectionId,
                AssemblyId = aa.ElectionAssemblyId,
                PhaseId = aa.ElectionPhaseId,
                ConstituencyId = aa.StructureId,
                SeatTypeId = aa.SeatTypeId,
                CandidateId = aa.CandidteId,
                PartyId = aa.PartyId,
                SymbolId = aa.SymbolId,
                EnglishName = aa.Candidate.EnglishName,
                UrduName = aa.Candidate.UrduName,
                Party = aa.Party.EnglishTitle,
                Symbol = aa.Symbol.EnglishTitle,
                Weight = aa.Weight,
                CreatedBy = aa.CreatedBy,
                CreatedOn = aa.CreatedDate.ToString("d MMM, yyyy h:mm"),
                ModifiedBy = aa.ModifiedBy ?? "",
                ModifiedOn = aa.ModifiedDate != null ? aa.ModifiedDate.Value.ToString("d MMM, yyyy h:mm") : "",
                Type = aa.Candidate.CandidateType == NominationType.Candidate ? "Candidate" : "Panel"
            }).ToList();
        return Task.FromResult(q);
    }

    public Task<List<NominationDTO>> GetNominations(int assemblyId, int structureId, int seatTypeId,
        int electionPhaseId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.Nominations
            orderby aa.Weight == 0 ? 100000 : aa.Weight, aa.Candidate.EnglishName
            where aa.ElectionAssemblyId == assemblyId &&
                  aa.ElectionPhaseId == electionPhaseId &&
                  aa.StructureId == structureId &&
                  aa.SeatTypeId == seatTypeId
            select new NominationDTO
            {
                Id = aa.Id,
                AssemblyId = aa.ElectionAssemblyId,
                CandidateId = aa.CandidteId,
                ElectionId = aa.ElectionPhase.ElectionId,
                PartyId = aa.PartyId,
                PhaseId = aa.ElectionPhaseId,
                SeatTypeId = aa.SeatTypeId,
                ConstituencyId = aa.StructureId,
                SymbolId = aa.SymbolId,
                Weight = aa.Weight,
                EnglishName = aa.Candidate.EnglishName,
                Party = aa.Party.EnglishTitle,
                Symbol = aa.Symbol.EnglishTitle,
                UrduName = aa.Candidate.UrduName,
                PartyUrdu = aa.Party.UrduTitle,
                SymbolUrdu = aa.Symbol.UrduTitle,
                CreatedBy = aa.CreatedBy,
                CreatedOn = aa.CreatedDate.ToString("d MMM, yyyy h:mm"),
                ModifiedBy = aa.ModifiedBy ?? "",
                ModifiedOn = aa.ModifiedDate != null ? aa.ModifiedDate.Value.ToString("d MMM, yyyy h:mm") : "",
                Type = aa.Candidate.CandidateType == NominationType.Candidate ? "Candidate" : "Panel",
                IsWithdraw = aa.IsWithdraw,
                Status = aa.IsWithdraw ? "Withdraw" : "Active",
                BilaMokabila = aa.IsBilaMokabilaWinner ? "Yes" : "No"
            }).ToList();
        var rnd = new Random().Next();
        foreach (var nom in q)
        {
            if (File.Exists(Directory.GetCurrentDirectory() + $"\\wwwroot\\media\\candidates\\{nom.CandidateId}.jpg"))
                nom.CandidateURL = $"/media/candidates/{nom.CandidateId}.jpg?id={rnd}";
            else
                nom.CandidateURL = "/media/candidates/blank.jpg";
            //C:\Users\<USER>\Source\repos\ElectionAppServer\ElectionAppServer\wwwroot\media\flags\5.jpg
            if (File.Exists(Directory.GetCurrentDirectory() + $"\\wwwroot\\media\\flags\\{nom.PartyId}.jpg"))
                nom.PartyFlagURL = $"/media/flags/{nom.PartyId}.jpg";
            else
                nom.PartyFlagURL = "/media/flags/missing-flag.png";
            if (nom.SymbolId == null)
                nom.SymbolURL = "/media/symbols/missing-symbol.png";
            else if (File.Exists(Directory.GetCurrentDirectory() + $"\\wwwroot\\media\\symbols\\{nom.SymbolId}.jpg"))
                nom.SymbolURL = $"/media/symbols/{nom.SymbolId}.jpg";
            else
                //C:\Users\<USER>\Source\repos\ElectionAppServer\ElectionAppServer\wwwroot\media\symbols\1.jpg
                nom.SymbolURL = "/media/symbols/missing-symbol.png";
        }

        return Task.FromResult(q);
    }

    public Task<List<GeneralItemDTO>> GetParties_list(int electionId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var pool = dc.Elections.FirstOrDefault(k => k.Id == electionId).CandidatePoll;
        var q = new List<GeneralItemDTO>();
        if (pool == CandidatePoll.GeneralElection)
            q = (from aa in dc.Parties
                where aa.IsGeneral
                orderby aa.EnglishTitle
                select new GeneralItemDTO
                {
                    Id = aa.Id,
                    EnglishTitle = aa.ShortEnglishTitle + " - " + aa.EnglishTitle + " - " + aa.UrduTitle,
                    Province = "",
                    UrduTitle = aa.UrduTitle
                }).ToList();
        else if (pool == CandidatePoll.GilgitElection)
            q = (from aa in dc.Parties
                where aa.IsGB
                orderby aa.EnglishTitle
                select new GeneralItemDTO
                {
                    Id = aa.Id,
                    EnglishTitle = aa.ShortEnglishTitle + " - " + aa.EnglishTitle + " - " + aa.UrduTitle,
                    Province = "",
                    UrduTitle = aa.UrduTitle
                }).ToList();
        else if (pool == CandidatePoll.KashmirElection)
            q = (from aa in dc.Parties
                where aa.IsAJK
                orderby aa.EnglishTitle
                select new GeneralItemDTO
                {
                    Id = aa.Id,
                    EnglishTitle = aa.ShortEnglishTitle + " - " + aa.EnglishTitle + " - " + aa.UrduTitle,
                    Province = "",
                    UrduTitle = aa.UrduTitle
                }).ToList();
        else if (pool == CandidatePoll.LocalBodyElectionKPK)
            q = (from aa in dc.Parties
                where aa.IsLB
                orderby aa.EnglishTitle
                select new GeneralItemDTO
                {
                    Id = aa.Id,
                    EnglishTitle = aa.ShortEnglishTitle + " - " + aa.EnglishTitle + " - " + aa.UrduTitle,
                    Province = "",
                    UrduTitle = aa.UrduTitle
                }).ToList();
        else if (pool == CandidatePoll.LocalBodyElectionSindh)
            q = (from aa in dc.Parties
                where aa.IsLBSindh
                orderby aa.EnglishTitle
                select new GeneralItemDTO
                {
                    Id = aa.Id,
                    EnglishTitle = aa.ShortEnglishTitle + " - " + aa.EnglishTitle + " - " + aa.UrduTitle,
                    Province = "",
                    UrduTitle = aa.UrduTitle
                }).ToList();
        else if (pool == CandidatePoll.LocalBodyElectionBalochistan)
            q = (from aa in dc.Parties
                where aa.IsLBBalochistan
                orderby aa.EnglishTitle
                select new GeneralItemDTO
                {
                    Id = aa.Id,
                    EnglishTitle = aa.ShortEnglishTitle + " - " + aa.EnglishTitle + " - " + aa.UrduTitle,
                    Province = "",
                    UrduTitle = aa.UrduTitle
                }).ToList();
        else if (pool == CandidatePoll.LocalBodyElectionSindh)
            q = (from aa in dc.Parties
                where aa.IsLBPunjab
                orderby aa.EnglishTitle
                select new GeneralItemDTO
                {
                    Id = aa.Id,
                    EnglishTitle = aa.ShortEnglishTitle + " - " + aa.EnglishTitle + " - " + aa.UrduTitle,
                    Province = "",
                    UrduTitle = aa.UrduTitle
                }).ToList();
        else if (pool == CandidatePoll.LocalBodyAJK)
            q = (from aa in dc.Parties
                where aa.IsLBAJK
                orderby aa.EnglishTitle
                select new GeneralItemDTO
                {
                    Id = aa.Id,
                    EnglishTitle = aa.ShortEnglishTitle + " - " + aa.EnglishTitle + " - " + aa.UrduTitle,
                    Province = "",
                    UrduTitle = aa.UrduTitle
                }).ToList();
        else if (pool == CandidatePoll.LocalBodyIslamabad)
            q = (from aa in dc.Parties
                where aa.IsLBIslamabad
                orderby aa.EnglishTitle
                select new GeneralItemDTO
                {
                    Id = aa.Id,
                    EnglishTitle = aa.ShortEnglishTitle + " - " + aa.EnglishTitle + " - " + aa.UrduTitle,
                    Province = "",
                    UrduTitle = aa.UrduTitle
                }).ToList();
        return Task.FromResult(q);
    }

    public Task<int?> GetPartySymbolId(int partyId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.Parties
            where aa.Id == partyId
            select aa).FirstOrDefault();
        int? op = null;
        if (q != null && q.SymbolId != null) op = q.SymbolId;
        return Task.FromResult(op);
    }

    public Task<List<GeneralItemDTO>> GetPhases(int electionId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.ElectionPhases
            where aa.ElectionId == electionId
            orderby aa.Title
            select new GeneralItemDTO
            {
                Id = aa.Id,
                EnglishTitle = aa.Title,
                Province = ""
            }).ToList();
        return Task.FromResult(q);
    }

    public Task<List<GeneralItemDTO>> GetSeatType(int assemblyId, int? constituencyId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q1 = (from aa in dc.Structures
            where aa.AssemblyId == assemblyId &&
                  aa.Id == constituencyId
            select aa.GetType()).FirstOrDefault();
        var ss = StructureType.UnionCouncil;
        if (q1 == typeof(UnionCouncil))
            ss = StructureType.UnionCouncil;
        else if (q1 == typeof(Ward))
            ss = StructureType.Ward;
        else if (q1 == typeof(NationalAssemblyHalka))
            ss = StructureType.NA;
        else if (q1 == typeof(ProvincialAssemblyHalka))
            ss = StructureType.PA;
        else if (q1 == typeof(Town)) ss = StructureType.Town;
        var q = (from aa in dc.ElectionAssemblySeats
            orderby aa.SeatType.EnglishTitle
            where aa.ElectionAssemblyId == assemblyId && aa.ConstituencyType == ss
            select new GeneralItemDTO
            {
                Id = aa.SeatTypeId,
                EnglishTitle = aa.SeatType.EnglishTitle,
                UrduTitle = aa.SeatType.UrduTitle
            }).Distinct().ToList();
        //q.Insert(0, new GeneralItemDTO { Id = 0, EnglishTitle = "Select Seat Type", UrduTitle = "", Province = "" });
        return Task.FromResult(q);
    }

    public Task<List<GeneralItemDTO>> GetSeatType2(int assemblyId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.ElectionAssemblySeats
            orderby aa.SeatType.EnglishTitle
            where aa.ElectionAssemblyId == assemblyId
            select new GeneralItemDTO
            {
                Id = aa.SeatTypeId,
                EnglishTitle = aa.SeatType.EnglishTitle,
                UrduTitle = aa.SeatType.UrduTitle
            }).Distinct().ToList();
        return Task.FromResult(q);
    }

    public Task<List<GeneralItemDTO>> GetSymbols_list()
    {
        using var dc = _contextFactory.CreateDbContext();
        var con = dc.Database.GetDbConnection();
        var res = con.Query<GeneralItemDTO>("app.GetSymbolsList", commandType: CommandType.StoredProcedure).ToList();
        return Task.FromResult(res);
        //var q = (from aa in dc.Symbols
        //	orderby aa.EnglishTitle
        //	select new GeneralItemDTO
        //	{
        //		Id = aa.Id,
        //		EnglishTitle = aa.EnglishTitle + " - " + aa.UrduTitle,
        //		Province = "",
        //		UrduTitle = aa.UrduTitle
        //	}).ToList();
        //return Task.FromResult(q);
    }

    public Task<string> UpdateConstituencyInfo(int constituencyId, int? maleVoters, int? femaleVoters,
        int? totalPollingStations, int? population, string userId, int phaseId, bool IsRePoll, int SeatTypeId,
        int? youthVoters, bool BigContest, int? rejectedVotes)
    {
        using var dc = _contextFactory.CreateDbContext();
        //var cc = (from aa in dc.Structures
        //	where aa.Id == constituencyId
        //	select aa).FirstOrDefault();
        var cc = (from a in dc.PollingSchemes
            where a.StructureId == constituencyId &&
                  a.PhaseId == phaseId
            select a).FirstOrDefault();

        //if (cc == null) return Task.FromResult("Record not found");
        if (cc == null)
        {
            cc = new PollingScheme
            {
                StructureId = constituencyId,
                PhaseId = phaseId,
                CreatedBy = userId,
                CreatedDate = DateTime.Now,
                MaleVoters = maleVoters,
                FemaleVoters = femaleVoters,
                Population = population,
                TotalPollingStations = totalPollingStations,
                YouthVoters = youthVoters,
                BigContest = BigContest,
                RejectedVotes = rejectedVotes ?? 0
            };
            dc.PollingSchemes.Add(cc);
            dc.SaveChanges();
        }

        {
            cc.MaleVoters = maleVoters;
            cc.FemaleVoters = femaleVoters;
            cc.TotalPollingStations = totalPollingStations;
            cc.Population = population;
            cc.ModifiedBy = userId;
            cc.ModifiedDate = DateTime.Now;
            cc.YouthVoters = youthVoters;
            cc.BigContest = BigContest;
            cc.RejectedVotes = rejectedVotes ?? 0;
            dc.SaveChanges();

            var rePollExist = (from aa in dc.RePollSeats
                where aa.ConstituencyId == constituencyId &&
                      aa.PhaseId == phaseId
                select aa).Any();

            if (!rePollExist && IsRePoll)
            {
                var rp = new RePollSeat
                {
                    ConstituencyId = constituencyId, PhaseId = phaseId, CreatedBy = userId, CreatedDate = DateTime.Now,
                    SeatTypeId = SeatTypeId
                };
                dc.RePollSeats.Add(rp);
                dc.SaveChanges();
            }

            if (!IsRePoll && rePollExist)
            {
                var rp = (from aa in dc.RePollSeats
                    where aa.ConstituencyId == constituencyId &&
                          aa.PhaseId == phaseId &&
                          aa.SeatTypeId == SeatTypeId
                    select aa).FirstOrDefault();

                dc.RePollSeats.Remove(rp);
                dc.SaveChanges();
            }

            return Task.FromResult("OK");
        }
    }

    public Task<string> GetSymbolUrlById(int? symbolId)
    {
        if (symbolId == null)
            return Task.FromResult("/media/symbols/missing-symbol.png");
        if (File.Exists(Directory.GetCurrentDirectory() + $"\\wwwroot\\media\\symbols\\{symbolId}.jpg"))
            return Task.FromResult($"/media/symbols/{symbolId}.jpg");
        return Task.FromResult("/media/symbols/missing-symbol.png");
    }

    public Task<string> PostponeElecOnConst(int constituencyId, int phaseId, string userId)
    {
        using var dc = _contextFactory.CreateDbContext();
        using (var tran = dc.Database.BeginTransaction())
        {
            try
            {
                var nom = from aa in dc.Nominations
                    where aa.StructureId == constituencyId &&
                          aa.ElectionPhaseId == phaseId
                    select aa;
                foreach (var n in nom)
                {
                    n.ModifiedBy = userId;
                    n.ModifiedDate = DateTime.Now;
                }

                dc.SaveChanges();
                tran.Commit();
                return Task.FromResult("OK");
            }
            catch (Exception ex)
            {
                var op = ex.Message;
                if (ex.InnerException != null)
                {
                    op += " - Detail: " + ex.InnerException.Message;
                    if (ex.InnerException.InnerException != null)
                        op += " - Detail: " + ex.InnerException.InnerException.Message;
                }

                return Task.FromResult(op);
            }
        }
    }

    internal Task<List<SearchCandidateDTO>> SearchCandidate3(string searchText, int candidateId, int electionId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var con = dc.Database.GetDbConnection();
        var parms = new { searchText, candidateId, electionId };
        var q = con.Query<SearchCandidateDTO>("app.spSearchCandidates", parms, commandType: CommandType.StoredProcedure)
            .Take(20).ToList();
        return Task.FromResult(q);
    }

    internal Task<List<SearchCandidateDTO>> SearchCandidate3a(string searchText, int candidateId, int electionId)
    {
        using var dc = _contextFactory.CreateDbContext();
        if (candidateId > 0)
        {
            var q = (from aa in dc.SearchCandidates
                where aa.ElectionId == electionId
                      && aa.CandidateId == candidateId
                orderby aa.EnglishName
                select new SearchCandidateDTO
                {
                    CreatedBy = aa.CreatedBy,
                    CreatedDate = aa.CreatedOn.ToString("d MMM, yyyy h:mm:ss"),
                    Disrict = aa.District,
                    EnglishName = aa.EnglishName,
                    Gender = (int)aa.Gender,
                    Id = aa.CandidateId,
                    ImgUrl = "",
                    LastConstituency = aa.Constituencies.Length >= 1021
                        ? aa.Constituencies.Substring(0, 1021) + "..."
                        : aa.Constituencies,
                    ModifiedBy = aa.ModifiedBy,
                    ModifiedDate = aa.ModifiedOn == null ? "" : aa.ModifiedOn.Value.ToString("d MMM, yyyy h:mm:ss"),
                    Party = aa.CurrentParty,
                    UrduFatherName = aa.UrduFatherName,
                    UrduName = aa.UrduName
                }).ToList();
            return Task.FromResult(q);
        }
        else
        {
            var q = (from aa in dc.SearchCandidates
                where aa.ElectionId == electionId && (
                    aa.EnglishName.Contains(searchText) ||
                    aa.UrduName.Contains(searchText) ||
                    aa.District.Contains(searchText))
                orderby aa.EnglishName.Trim()
                select new SearchCandidateDTO
                {
                    CreatedBy = aa.CreatedBy,
                    CreatedDate = aa.CreatedOn.ToString("d MMM, yyyy h:mm:ss"),
                    Disrict = aa.District,
                    EnglishName = aa.EnglishName,
                    Gender = (int)aa.Gender,
                    Id = aa.CandidateId,
                    ImgUrl = "",
                    LastConstituency = aa.Constituencies.Length >= 1021
                        ? aa.Constituencies.Substring(0, 1021) + "..."
                        : aa.Constituencies,
                    ModifiedBy = aa.ModifiedBy,
                    ModifiedDate = aa.ModifiedOn == null ? "" : aa.ModifiedOn.Value.ToString("d MMM, yyyy h:mm:ss"),
                    Party = aa.CurrentParty,
                    UrduFatherName = aa.UrduFatherName,
                    UrduName = aa.UrduName
                }).Take(20).ToList();
            return Task.FromResult(q);
        }
    }

    public Task<string> ReEnterNomination(int nominationId, string userId)
    {
        using var dc = _contextFactory.CreateDbContext();
        // Get Constituency Status
        // if its status is postponed then
        // do nothing and return mesage constituency is postponed
        var co = (from aa in dc.Nominations
                where aa.Id == nominationId &&
                      aa.Structure.IsPostponed
                select aa
            ).Any();
        if (co) return Task.FromResult("Constituency is postponed");
        var nom = (from aa in dc.Nominations
                where aa.Id == nominationId
                select aa
            ).FirstOrDefault();
        nom.ModifiedBy = userId;
        nom.ModifiedDate = DateTime.Now;
        nom.IsWithdraw = false;
        dc.SaveChanges();
        return Task.FromResult("OK");
    }

    private static void UpdateCandidateSearchLog(int candidateId, int electionId, ApplicationDbContext dc)
    {
        var currentElection = (from aa in dc.Elections
            where aa.Id == electionId
            select aa).FirstOrDefault();

        var elecs = (from aa in dc.Elections
            where aa.CandidatePoll == currentElection.CandidatePoll &&
                  aa.ElectionType == currentElection.ElectionType
            select aa).ToList();
        foreach (var el in elecs)
        {
            List<SqlParameter> parms = new()
            {
                new SqlParameter { ParameterName = "@ElectionId", Value = el.Id },
                new SqlParameter { ParameterName = "@CandidateId", Value = candidateId }
            };
            dc.Database.ExecuteSqlRaw("EXECUTE spUpdateCandidateSearch @ElectionId, @CandidateId", parms.ToArray());
        }
    }

    public Task<string> SaveNomination(NominationDTO obj, string user)
    {
        using var dc = _contextFactory.CreateDbContext();

        // Candidate Already exist on the constituency
        var exist = (from aa in dc.Nominations
            where aa.CandidteId == obj.CandidateId &&
                  aa.ElectionAssemblyId == obj.AssemblyId &&
                  aa.StructureId == obj.ConstituencyId &&
                  aa.ElectionPhaseId == obj.PhaseId &&
                  aa.SeatTypeId == obj.SeatTypeId &&
                  aa.Id != obj.Id
            select aa).Any();
        if (exist)
            throw new Exception("Candidate already exist on this constituency");

        // Remove any other duplication check on the nomimation

        //exist = (from aa in dc.Nominations
        //			where aa.Id != obj.Id &&
        //			aa.StructureId == obj.ConstituencyId &&
        //			aa.ElectionAssemblyId == obj.AssemblyId &&
        //			aa.ElectionPhaseId == obj.PhaseId &&
        //			aa.SeatTypeId == obj.SeatTypeId &&
        //			aa.SymbolId == obj.SymbolId &&
        //			aa.SymbolId != null &&
        //			aa.Symbol.EnglishTitle != "Independent"
        //			//aa.Party.EnglishTitle!= "Independent"
        //			select aa).Any();
        //if (exist)
        //	throw new Exception("Symbol already allocated to candidate");

        // Remove party duplication check on the nomiation
        //exist = (from aa in dc.Nominations
        //			where aa.Id != obj.Id &&
        //			aa.StructureId == obj.ConstituencyId &&
        //			aa.ElectionAssemblyId == obj.AssemblyId &&
        //			aa.ElectionPhaseId == obj.PhaseId &&
        //			aa.SeatTypeId == obj.SeatTypeId &&
        //			aa.PartyId == obj.PartyId &&
        //			aa.Party.EnglishTitle != "Independent"
        //			select aa).Any();
        //if (exist)
        //	throw new Exception("Party already exist on this constituency");
        /////////////////////////////////////////////////////////////////////

        //exist = (from aa in dc.Nominations
        //         where aa.StructureId == obj.StructureId &&
        //         aa.Id != obj.Id &&
        //         aa.SymbolId == obj.SymbolId
        //         select aa).Any();
        if (obj.Id == 0)
        {
            var nom = new Nomination
            {
                CandidteId = (int)obj.CandidateId,
                CreatedDate = DateTime.Now,
                CreatedBy = user,
                ElectionAssemblyId = obj.AssemblyId,
                ElectionPhaseId = obj.PhaseId,
                PartyId = (int)obj.PartyId,
                SeatTypeId = obj.SeatTypeId,
                SymbolId = obj.SymbolId,
                Weight = obj.Weight,
                StructureId = obj.ConstituencyId,
                Votes = 0
            };
            dc.Nominations.Add(nom);
            dc.SaveChanges();
            UpdateCandidateSearchLog((int)obj.CandidateId, obj.ElectionId, dc);
            return Task.FromResult("OK");
        }
        else
        {
            var nom = dc.Nominations.Find(obj.Id);
            nom.CandidteId = (int)obj.CandidateId;
            nom.ElectionAssemblyId = obj.AssemblyId;
            nom.ElectionPhaseId = obj.PhaseId;
            nom.PartyId = (int)obj.PartyId;
            nom.SeatTypeId = obj.SeatTypeId;
            nom.SymbolId = obj.SymbolId;
            nom.Weight = obj.Weight;
            nom.StructureId = obj.ConstituencyId;
            nom.ModifiedBy = user;
            nom.ModifiedDate = DateTime.Now;
            dc.SaveChanges();
            UpdateCandidateSearchLog((int)obj.CandidateId, obj.ElectionId, dc);
            return Task.FromResult("OK");
        }
    }

    public Task<List<SearchCandidateDTO>> SearchCandidate(int electionId, NominationType electionBasedOn)
    {
        using var dc = _contextFactory.CreateDbContext();
        var da = new SqlDataAdapter("spGetAllCandidats", dc.Database.GetConnectionString());
        da.SelectCommand.CommandType = CommandType.StoredProcedure;
        da.SelectCommand.Parameters.AddWithValue("@ElectionId", electionId);
        var dt = new DataTable();
        da.Fill(dt);
        var op = new List<SearchCandidateDTO>();
        for (var i = 0; i < dt.Rows.Count; i++)
        {
            var r = dt.Rows[i];
            var cc = new SearchCandidateDTO
            {
                Id = int.Parse(r["Id"].ToString()),
                //FullName = r["FullName"].ToString(),
                //CandidateTypeStr = r["CandidateType"].ToString(),
                EnglishName = r["EnglishName"].ToString(),
                //Gender = int.Parse(r["Gender"].ToString()),
                //UrduFatherName = r["UrduFatherName"].ToString(),
                Type = r["CandidateType"].ToString(),
                UrduName = r["UrduName"].ToString(),
                CreatedBy = r["CreatedBy"].ToString(),
                ModifiedBy = r["ModifiedBy"].ToString(),
                CreatedDate = DateTime.Parse(r["CreatedOn"].ToString()).ToString("d MMM, yyyy h:mm"),
                LastConstituency = r["Constituencies"].ToString(),
                ///DisableDelete = r["NominationCount"].ToString() != "0",
                Disrict = r["District"].ToString()
            };
            //cc.PictureURL = $"/media/candidates/{cc.Id}.jpg";
            cc.CreatedDate = r["CreatedOn"].ToString();
            cc.ModifiedDate = r["ModifiedOn"].ToString();
            //cc.date = r["DateOfBirth"].ToString();
            //cc.IsFreshStr = r["Status"].ToString();

            if (!File.Exists(Directory.GetCurrentDirectory() + $"\\wwwroot\\media\\candidates\\{cc.Id}.jpg"))
                //\wwwroot\media\candidates\Blank.jpg
                cc.ImgUrl = "/media/candidates/Blank.jpg";
            else
                cc.ImgUrl = $"/media/candidates/{cc.Id}.jpg";

            op.Add(cc);
        }

        return Task.FromResult(op);
    }

    public Task<List<SearchCandidateDTO>> SearchCandidate2(int electionId, NominationType electionBasedOn)
    {
        using var dc = _contextFactory.CreateDbContext();
        var elc = dc.Elections.Find(electionId);
        var q = (from aa in dc.Candidates
            //where aa.ElectionId == electionId
            orderby aa.EnglishName
            where aa.CandidateType == electionBasedOn
                  && aa.CandidatePoll == elc.CandidatePoll
            select new SearchCandidateDTO
            {
                Id = aa.Id,
                //Disrict = aa.StrDistrict,
                ImgUrl = $"media/candidates/{aa.Id}.jpg",
                LastConstituency = "",
                EnglishName = aa.EnglishName,
                UrduName = aa.UrduName,
                Party = "",
                Type = aa.CandidateType == NominationType.Candidate ? "Candidate" : "Panel",
                CreatedBy = aa.CreatedBy,
                CreatedDate = aa.CreatedDate.ToString("d MMM, yyyy h:mm"),
                ModifiedBy = aa.ModifiedBy ?? "",
                ModifiedDate = aa.ModifiedDate == null ? "" : aa.ModifiedDate.Value.ToString("d MMM, yyyy h:mm")
            }).ToList();
        /* Commit for search improvement */
        foreach (var i in q)
        {
            var dist = (from aa in dc.CandidateDistricts
                    where aa.CandidateId == i.Id &&
                          aa.ElectionId == electionId
                    select new
                    {
                        District = aa.District.EnglishTitle, Province = aa.District.Division.Province.EnglishTitle
                    })
                .FirstOrDefault();
            i.Disrict = dist != null ? $"{dist.District} - {dist.Province}" : "";
            i.ImgUrl = File.Exists(Directory.GetCurrentDirectory() + $@"\wwwroot\media\candidates\{i.Id}.jpg")
                ? $"/media/candidates/{i.Id}.jpg"
                : "/media/candidates/blank.jpg";
            var qp = (from aa in dc.PartyAffiliations
                orderby aa.DateFrom descending
                where aa.CandidateId == i.Id
                select new { aa.PartyId, aa.Party.EnglishTitle, aa.Party.SymbolId }).FirstOrDefault();
            if (qp != null)
            {
                i.Party = qp.EnglishTitle;
                i.PartyId = qp.PartyId;
                i.SymbolId = qp.SymbolId;
            }
            else
            {
                i.Party = "";
                i.PartyId = null;
                i.SymbolId = null;
            }

            var qe2 = (from aa in dc.Nominations
                where aa.CandidteId == i.Id
                orderby aa.ElectionPhase.StartDate descending
                select new
                {
                    Election = aa.ElectionAssembly.Election.EnglishTitle + $" ({aa.ElectionPhase.Title})",
                    Assembly = aa.ElectionAssembly.EnglishTitle,
                    Seat = aa.SeatType.EnglishTitle,
                    Constituency = aa.Structure.Code + " " + aa.Structure.EnglishTitle
                }).Take(3).ToList();
            foreach (var qe in qe2)
                i.LastConstituency =
                    $"{qe.Seat} {qe.Assembly} for {qe.Constituency} Constituency in {qe.Election} <br />";
        }

        return Task.FromResult(q);
    }

    public Task<string> WithdrawNomination(int nominationId, string userId)
    {
        using var dc = _contextFactory.CreateDbContext();
        // Get Constituency Status
        // if its status is postponed then
        // do nothing and return mesage constituency is postponed
        var co = (from aa in dc.Nominations
                where aa.Id == nominationId &&
                      aa.Structure.IsPostponed
                select aa
            ).Any();
        if (co) return Task.FromResult("Constituency is postponed");
        var nom = (from aa in dc.Nominations
                where aa.Id == nominationId
                select aa
            ).FirstOrDefault();
        nom.ModifiedBy = userId;
        nom.ModifiedDate = DateTime.Now;
        nom.IsWithdraw = true;
        dc.SaveChanges();
        return Task.FromResult("OK");
    }

    public Task<ConstDetailDTO> GetConstInfo(int? constituencyId, int? phaseId, int seatTypeId)
    {
        using var dc = _contextFactory.CreateDbContext();
        using (var con = dc.Database.GetDbConnection())
        {
            var parms = new { ConstituencyId = constituencyId, PhaseId = phaseId };
            var res = con
                .Query<ConstDetailDTO>("ep_ConstituencyDetail", parms, commandType: CommandType.StoredProcedure)
                .FirstOrDefault();
            res.IsRePoll = (from aa in dc.RePollSeats
                where aa.ConstituencyId == constituencyId &&
                      aa.SeatTypeId == seatTypeId &&
                      aa.PhaseId == phaseId
                select aa).Any();
            var ps = (from a in dc.PollingSchemes
                where a.StructureId == constituencyId && a.PhaseId == phaseId
                select a).FirstOrDefault();
            if (ps != null)
            {
                res.MaleVoters = ps.MaleVoters;
                res.FemaleVoters = ps.FemaleVoters;
                res.TotalPollingStations = ps.TotalPollingStations;
                res.Population = ps.Population;
                res.YouthVoters = ps.YouthVoters;
                res.BigContest = ps.BigContest;
                res.RejectedVotes = ps.RejectedVotes;
            }
            else
            {
                res.MaleVoters = 0;
                res.FemaleVoters = 0;
                res.TotalPollingStations = 0;
                res.Population = 0;
                res.YouthVoters = 0;
                res.BigContest = false;
                res.IsRePoll = false;
                res.RejectedVotes = 0;
            }

            return Task.FromResult(res);
        }
    }

    internal Task<ConstDetailDTO> GetConstInfo2(int? constituencyId, int? phaseId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var pp = (from aa in dc.ElectionPhases
            where aa.Id == phaseId
            select new ConstDetailDTO
            {
                Election = aa.Election.EnglishTitle,
                ElectionUrdu = aa.Election.UrduTitle,
                Phase = aa.Title,
                ElectionDate = aa.StartDate
            }).FirstOrDefault();
        var q = (from aa in dc.Structures
            where aa.Id == constituencyId
            select new ConstDetailDTO
            {
                Code = aa.Code,
                Name = aa.EnglishTitle,
                UrduName = aa.UrduTitle,
                //TotalVoters = aa.TotalVoters,
                MaleVoters = aa.MaleVoters,
                FemaleVoters = aa.FemaleVoters,
                TotalPollingStations = aa.TotalPollingStations,
                Assembly = aa.Assembly.EnglishTitle,
                AssemblyUrdu = aa.Assembly.UrduTitle,
                Population = aa.Population
            }).FirstOrDefault();
        q.Election = pp.Election;
        q.ElectionUrdu = pp.ElectionUrdu;
        q.Phase = pp.Phase;
        q.ElectionDate = pp.ElectionDate;
        var na = (from aa in dc.Structures.OfType<NationalAssemblyHalka>()
            where aa.Id == constituencyId
            select new
            {
                District = aa.District.EnglishTitle,
                //Division = aa.District.Division.EnglishTitle,
                Province = aa.District.Division.Province.EnglishTitle,
                Region = aa.District.Region.EnglishTitle
            }).FirstOrDefault();
        if (na != null)
        {
            q.District = na.District;
            //q.Division = na.Division;
            q.Province = na.Province;
            q.Region = na.Region;
        }
        else
        {
            var pa = (from aa in dc.Structures.OfType<ProvincialAssemblyHalka>()
                where aa.Id == constituencyId
                select new
                {
                    District = aa.District.EnglishTitle,
                    //Division = aa.District.Division.EnglishTitle,
                    Province = aa.District.Division.Province.EnglishTitle,
                    Region = aa.District.Region.EnglishTitle
                }).FirstOrDefault();
            if (pa != null)
            {
                q.District = pa.District;
                //q.Division = pa.Division;
                q.Province = pa.Province;
                q.Region = pa.Region;
            }
            else
            {
                var uc = (from aa in dc.Structures.OfType<UnionCouncil>()
                    where aa.Id == constituencyId
                    select new
                    {
                        Town = aa.Town.EnglishTitle,
                        District = aa.Town.District.EnglishTitle,
                        //Division = aa.Town.District.Division.EnglishTitle,
                        Region = aa.Town.District.Region.EnglishTitle,
                        Province = aa.Town.District.Division.Province.EnglishTitle
                    }).FirstOrDefault();
                if (uc != null)
                {
                    q.Town = uc.Town;
                    q.District = uc.District;
                    //q.Division = uc.Division;
                    q.Region = uc.Region;
                    q.Province = uc.Province;
                }
                else
                {
                    var wrd = (from aa in dc.Structures.OfType<Ward>()
                        where aa.Id == constituencyId
                        select new
                        {
                            UnionCouncil = aa.UnionCouncil.EnglishTitle,
                            Town = aa.UnionCouncil.Town.EnglishTitle,
                            District = aa.UnionCouncil.Town.District.EnglishTitle,
                            //Division = aa.UnionCouncil.Town.District.Division.EnglishTitle,
                            Region = aa.UnionCouncil.Town.District.Region.EnglishTitle,
                            Province = aa.UnionCouncil.Town.District.Region.Province.EnglishTitle
                        }).FirstOrDefault();
                    q.UnionCouncil = wrd.UnionCouncil;
                    q.Town = wrd.Town;
                    q.District = wrd.District;
                    //q.Division = wrd.Division;
                    q.Province = wrd.Province;
                }
            }
        }

        return Task.FromResult(q);
    }

    internal Task<NominationType> GetElectionBasedOn(int assemblyId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.ElectionAssemblies
            where aa.Id == assemblyId
            select aa).FirstOrDefault();
        if (q != null) return Task.FromResult(q.NominationType);
        throw new Exception("Assembly not found");
    }

    internal Task<NominationDTO> GetNominationById(int id)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.Nominations
            where aa.Id == id
            select new NominationDTO
            {
                Id = aa.Id,
                CandidateId = aa.CandidteId,
                AssemblyId = aa.ElectionAssemblyId,
                ElectionId = aa.ElectionPhase.ElectionId,
                PartyId = aa.PartyId,
                PhaseId = aa.ElectionPhaseId,
                SeatTypeId = aa.SeatTypeId,
                ConstituencyId = aa.StructureId,
                SymbolId = aa.SymbolId,
                Weight = aa.Weight,
                IsWithdraw = aa.IsWithdraw,
                CandidateName = aa.Candidate.EnglishName
            }).FirstOrDefault();
        if (q != null)
        {
            q.CandidateURL = GetCandidateUrlById(q.CandidateId).Result;
            q.PartyFlagURL = GetFlagUrlById(q.PartyId).Result;
            q.SymbolURL = GetSymbolUrlById(q.SymbolId).Result;
        }

        return Task.FromResult(q);
    }

    internal Task<string> MarkBilaMuqabala(int id, string userId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.Nominations
            where aa.Id == id
            select aa).FirstOrDefault();
        if (q == null) return Task.FromResult("Record not found");

        q.IsBilaMokabilaWinner = true;
        q.IsWinner = true;
        q.ModifiedBy = userId;
        q.ModifiedDate = DateTime.Now;
        dc.SaveChanges();
        return Task.FromResult("OK");
    }

    internal Task<string> RemoveBilaMoqbila(int id, string name)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.Nominations
            where aa.Id == id
            select aa).FirstOrDefault();
        if (q == null) return Task.FromResult("Record not found");

        q.IsWinner = false;
        q.IsBilaMokabilaWinner = false;
        q.ModifiedDate = DateTime.Now;
        q.ModifiedBy = name;
        dc.SaveChanges();
        return Task.FromResult("OK");
    }

    public Task<List<GeneralItemDTO>> GetAllSeatTypes()
    {
        using var dc = _contextFactory.CreateDbContext();

        var q = (from aa in dc.SeatTypes
            orderby aa.EnglishTitle
            select new GeneralItemDTO
            {
                Id = aa.Id,
                EnglishTitle = aa.EnglishTitle,
                UrduTitle = aa.UrduTitle
            }).ToList();

        return Task.FromResult(q);
    }

    public Task<List<PanelistDTO>> GetPanelistsOnNomination(int nominationId)
    {
        using var dc = _contextFactory.CreateDbContext();

        var q = (from aa in dc.NominationPanelists
            where aa.NominationId == nominationId
            select new PanelistDTO
            {
                Id = aa.Id,
                Gender = (int)aa.Gender,
                NameEnglish = aa.NameEnglish,
                NameUrdu = aa.NameUrdu,
                NominationId = aa.NominationId,
                SeatTypeId = aa.SeatTypeId,
                SeatType = aa.SeatType.EnglishTitle
            }).ToList();

        return Task.FromResult(q);
    }

    public Task<int> SavePanelistOnNomination(PanelistDTO obj, string userId)
    {
        using var dc = _contextFactory.CreateDbContext();

        if (obj.Id == 0)
        {
            var pp = new NominationPanelist
            {
                CreatedBy = userId,
                CreatedDate = DateTime.Now,
                Gender = (Gender)obj.Gender,
                NameEnglish = obj.NameEnglish,
                NameUrdu = obj.NameUrdu,
                NominationId = obj.NominationId,
                SeatTypeId = (int)obj.SeatTypeId
            };
            dc.NominationPanelists.Add(pp);
            dc.SaveChanges();
            return Task.FromResult(pp.Id);
        }

        var q = dc.NominationPanelists.Find(obj.Id);
        if (q != null)
        {
            q.NameEnglish = obj.NameEnglish;
            q.NameUrdu = obj.NameUrdu;
            q.SeatTypeId = (int)obj.SeatTypeId;
            q.Gender = (Gender)obj.Gender;
            q.ModifiedBy = userId;
            q.ModifiedDate = DateTime.Now;
            dc.SaveChanges();
            return Task.FromResult(obj.Id);
        }

        throw new Exception("Record not found");
    }

    public Task<string> DeletePanelistNomination(int id)
    {
        using var dc = _contextFactory.CreateDbContext();
        var obj = dc.NominationPanelists.Find(id);
        if (obj == null) return Task.FromResult("Not Found");

        dc.NominationPanelists.Remove(obj);
        dc.SaveChanges();
        return Task.FromResult("OK");
    }
}