﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace ElectionAppServer.Migrations
{
	public partial class DuplicationUCWardConstraints : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.AlterColumn<string>(
				 name: "EnglishTitle",
				 table: "Structures",
				 unicode: false,
				 maxLength: 200,
				 nullable: false,
				 oldClrType: typeof(string),
				 oldType: "nvarchar(200)",
				 oldMaxLength: 200);

			migrationBuilder.AlterColumn<string>(
				 name: "Code",
				 table: "Structures",
				 unicode: false,
				 maxLength: 20,
				 nullable: true,
				 oldClrType: typeof(string),
				 oldType: "nvarchar(max)",
				 oldNullable: true);

			migrationBuilder.CreateIndex(
				 name: "IX_Structures_Code_TownId",
				 table: "Structures",
				 columns: new[] { "Code", "TownId" },
				 unique: true,
				 filter: "[Code] IS NOT NULL AND [TownId] IS NOT NULL");

			migrationBuilder.CreateIndex(
				 name: "IX_Structures_EnglishTitle_TownId",
				 table: "Structures",
				 columns: new[] { "EnglishTitle", "TownId" },
				 unique: true,
				 filter: "[TownId] IS NOT NULL");

			migrationBuilder.CreateIndex(
				 name: "IX_Structures_UrduTitle_TownId",
				 table: "Structures",
				 columns: new[] { "UrduTitle", "TownId" },
				 unique: true,
				 filter: "[TownId] IS NOT NULL");
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropIndex(
				 name: "IX_Structures_Code_TownId",
				 table: "Structures");

			migrationBuilder.DropIndex(
				 name: "IX_Structures_EnglishTitle_TownId",
				 table: "Structures");

			migrationBuilder.DropIndex(
				 name: "IX_Structures_UrduTitle_TownId",
				 table: "Structures");

			migrationBuilder.AlterColumn<string>(
				 name: "EnglishTitle",
				 table: "Structures",
				 type: "nvarchar(200)",
				 maxLength: 200,
				 nullable: false,
				 oldClrType: typeof(string),
				 oldUnicode: false,
				 oldMaxLength: 200);

			migrationBuilder.AlterColumn<string>(
				 name: "Code",
				 table: "Structures",
				 type: "nvarchar(max)",
				 nullable: true,
				 oldClrType: typeof(string),
				 oldUnicode: false,
				 oldMaxLength: 20,
				 oldNullable: true);
		}
	}
}
