﻿@page "/pspr"

@attribute [Authorize(Roles = "Producer,IMM")]

@inject NavigationManager NavigationManager
@inject UserManager<ApplicationUser> _UserManager
@inject RoleManager<IdentityRole> _RoleManager
@inject AuthenticationStateProvider AuthenticationStateProvider
@inherits OwningComponentBase<ElectionResultsDataService>


<SfDialog Width="950px" @bind-Visible="@isDlgVisible" @ref="dlgForm" IsModal="true" ShowCloseIcon="true">
    <DialogEvents OnOpen="beforeOpen"></DialogEvents>
    <DialogTemplates>
        <Header>Detail</Header>
        <Content>
            <div class="row urdu-column" style="direction: rtl; text-align: right; background-color: #87cefa; padding: 5px; line-height: 1.7">

                @((MarkupString)detail.Ticker)


            </div>
            @*<div class="row">
                    <div class="col-md">Constituency: <b>@detail.EnglishTitle</b></div>
                    <div class="col-md" style="text-align: right">حلقہ: <b>@detail.UrduTitle</b></div>
                </div>
                <div class="row">
                    <div class="col-md">Register Voters: <b>@detail.RegisterVoters</b></div>
                    <div class="col-md">Total Polling Stations: <b>@detail.TotalPollingStations</b></div>
                </div>
                <div class="row">
                    <div class="col-md-2">PS #: <b>@detail.PSNumber</b></div>
                    <div class="col-md">Polling Stations: <b>@detail.PSEnglish</b></div>
                    <div class="col-md" style="text-align: right">پولنگ اسٹیشن: <b>@detail.PSUrdu</b></div>
                </div>
                <table class="table table-sm table-striped mt-2">
                    <thead class="thead-dark">
                    <tr>
                        <th>Candidate</th>
                        <th>Party</th>
                        <th>Votes (PS)</th>
                        <th>Votes (Overall)</th>
                    </tr>
                    </thead>
                    <tbody>
                    @foreach (var n in detail.Nominations)
                    {
                        <tr>
                            <td>@n.Candidate</td>
                            <td>@n.Party</td>
                            <td>@n.Votes</td>
                            <td>@n.VotesOA</td>
                        </tr>
                    }

                    </tbody>
                </table>*@
        </Content>
    </DialogTemplates>
</SfDialog>

@*<PageCaption Title="Polling Station Wise Live Results"></PageCaption>*@
<MudText Typo="Typo.h5">Polling Stations Wise Live Results</MudText>

<section>
    <table class="table table-sm">
        <thead class="thead-dark">
        <tr>
            @*<th>Assembly</th>*@
            <th>Constituency</th>
            <th>Polling Station</th>
            <th>Winner</th>
            <th>Winner Party</th>
            <th>Winner Votes</th>
            <th>Runner Up</th>
            <th>Runner Party</th>
            <th>Runner Votes</th>
            @*<th>PS %</th>
                    <th>TPS</th>
                    <th>RPS</th>*@
            <th>Status</th>
            <th>Updated Time</th>
            <th>&nbsp;</th>
        </tr>
        </thead>
        <tbody>
        @foreach (var r in results)
        {
            <tr class="@(r.Status == "Unpublished" ? "newresult" : "pubresult")">
                @*<td>@r.Assembly</td>*@
                <td>
                    <span style="cursor:pointer;color:blue" @onclick="@(() => DisplayDetail(r.PollingStationId, r.AssemblyId, r.ConstituencyId, r.SeatTypeId))">@r.Constituency</span>
                </td>
                <td>@r.PollingStation</td>
                <td>@r.WinnerName</td>
                <td>@r.WinnerParty</td>
                <td>@r.WinnerVotes</td>
                <td>@r.RunnerUpName</td>
                <td>@r.RunnerUpParty</td>
                <td>@r.RunnerUpVotes</td>
                @*<td>
                            @if (r.PercentageCompletedPollingStations < 100)
                            {
                                <span>@Math.Round(r.PercentageCompletedPollingStations, 2)</span><br />
                                <img src="/images/tp.png" alt="Partial" style="height: 18px" />
                            }
                            else
                            {
                                <img src="/images/tc.gif" alt="Completed" style="height: 22px" />
                            }
                        </td>
                        <td>@r.TotalPollingStations</td>
                        <td>@r.ResultOfPollingStations</td>*@
                <td>
                    @if (r.Status == "Unpublished")
                    {
                        <img src="/images/new.gif" alt="new"/>
                    }
                </td>
                <td>@r.LastUpdatedOn.ToString("d MMM, yyyy h:mm:ss")</td>
                <td>

                    <AuthorizeView Roles="Producer">
                        <SfButton IsPrimary="false" CssClass="e-primary" OnClick="@(() => MarkPublish(r))">Publish</SfButton>
                    </AuthorizeView>
                </td>
            </tr>
        }
        </tbody>
    </table>
</section>