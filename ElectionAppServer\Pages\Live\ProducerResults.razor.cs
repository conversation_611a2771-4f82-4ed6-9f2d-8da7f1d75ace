﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ElectionAppServer.Data;
using ElectionAppServer.DTO;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.SignalR.Client;
using Syncfusion.Blazor.Popups;

namespace ElectionAppServer.Pages.Live;

public partial class ProducerResults
{
    private SfDialog _dlgForm;
    private List<ResultDTO> _results = new();
    private string _tickerText = "";
    private HubConnection HubConnection { get; set; }
    private bool IsDlgVisible { get; set; }
    private LiveResultDetailDTO Detail { get; set; }

    [CascadingParameter] private Task<AuthenticationState> authenticationStateTask { get; set; }

    protected override async Task OnInitializedAsync()
    {
        IsDlgVisible = false;
        Detail = new LiveResultDetailDTO { Nominations = new List<LiveResultCandidateDetailDTO>() };

        HubConnection = new HubConnectionBuilder()
            .WithUrl(NavigationManager.ToAbsoluteUri("/resultshub"))
            .Build();


        HubConnection.On<ResultDTO>("ReceiveResult", async result =>
        {
            await _dlgForm.HideAsync();
            //var ok = result;
            //results.Add(result);
            var isExist = (from r in _results
                where r.ElectionId == result.ElectionId &&
                      r.AssemblyId == result.AssemblyId &&
                      r.ConstituencyId == result.ConstituencyId &&
                      r.SeatTypeId == result.SeatTypeId
                select r).FirstOrDefault();
            if (isExist != null)
            {
                isExist.WinnerCandidateId = result.WinnerCandidateId;
                isExist.WinnerName = result.WinnerName;
                isExist.WinnerParty = result.WinnerParty;
                isExist.WinnerVotes = result.WinnerVotes;

                isExist.RunnerUpCandidateId = result.RunnerUpCandidateId;
                isExist.RunnerUpName = result.RunnerUpName;
                isExist.RunnerUpParty = result.RunnerUpParty;
                isExist.RunnerUpVotes = result.RunnerUpVotes;
                isExist.OnAir = result.OnAir;
                isExist.Status = result.Status;
                isExist.LastUpdatedOn = result.LastUpdatedOn;
            }
            else
            {
                if (result.PhaseId == state.state.PhaseId)
                    _results.Add(result);
            }

            _results = (from r in _results
                orderby r.Status, r.LastUpdatedOn descending
                select r).ToList();


            //StateHasChanged();
            await InvokeAsync(StateHasChanged);
        });

        HubConnection.On<ResultDTO>("ReceiveNotification", result =>
        {
            var isExist = (from r in _results
                where r.ElectionId == result.ElectionId &&
                      r.AssemblyId == result.AssemblyId &&
                      r.ConstituencyId == result.ConstituencyId &&
                      r.SeatTypeId == result.SeatTypeId
                select r).FirstOrDefault();


            _results = (from r in _results
                orderby r.OnAir descending, r.LastUpdatedOn descending
                select r).ToList();


            InvokeAsync(StateHasChanged);
        });

        await HubConnection.StartAsync();
        await RefreshResults();

        await InvokeAsync(StateHasChanged);
        //await Task.CompletedTask;
    }

    private async Task RefreshResults()
    {
        try
        {
            _results = await Service.GetLiveResults(state.state.PhaseId);
        }
        catch (Exception)
        {
            // ignored
        }
    }

    private async Task DisplayDetail(int assemblyId, int constituencyId, int seatTypeId)
    {
        //detail = await Service.GetLiveResultDetail(state.state.ElectionId, assemblyId, state.state.PhaseId, constituencyId, seatTypeId);
        _tickerText = await Service.GetLiveResultDetailLB(state.state.ElectionId, assemblyId, state.state.PhaseId,
            constituencyId, seatTypeId);
        await _dlgForm.ShowAsync();
    }

    private async Task MarkPublish(int electionId, int phaseId, int assemblyId, int constituencyId, int seatTypeId)
    {
        var user = (await authenticationStateTask).User;
        var msg = await Service.MarkPublish(electionId, phaseId, assemblyId, constituencyId, seatTypeId,
            user.Identity!.Name);
        if (msg == "OK")
        {
            var res = (from r in _results
                where r.ConstituencyId == constituencyId &&
                      r.ElectionId == electionId &&
                      r.AssemblyId == assemblyId &&
                      r.PhaseId == phaseId
                select r).FirstOrDefault();
            //res.Status = "Yes";
            if (res != null)
                //res.OnAir = true;
                //res.Status = "Yes";
                _results.Remove(res);

            _results = (from aa in _results
                orderby aa.Status, aa.LastUpdatedOn descending
                select aa).ToList();
            var allowToPush = await confreader.GetSettingValue("AllowToPush");
            if (allowToPush == "Yes")
            {
                var immInfo = await Service.GetImmResult(electionId, phaseId, assemblyId, constituencyId, seatTypeId);
                await Translation.PostData(immInfo);
            }
        }

        await InvokeAsync(StateHasChanged);
    }


    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            var user = (await authenticationStateTask).User;
            if (user.Identity!.IsAuthenticated)
            {
                var dd = await localStorage.GetAsync<ElectionStateDTO>(await confreader.GetSettingValue("electionkey"));
                state.SetState(dd.Value);
                var info = await _UserManager.GetUserAsync(user);
                await Task.CompletedTask;

                StateHasChanged();
            }
            else
            {
                try
                {
                    await localStorage.DeleteAsync(await confreader.GetSettingValue("electionkey"));
                    state.SetState(new ElectionStateDTO
                        { ElectionId = 0, ElectionTitle = "", PhaseId = 0, PhaseTitle = "" });
                    StateHasChanged();
                }
                catch (Exception)
                {
                    // ignored
                }
            }
        }
    }
}