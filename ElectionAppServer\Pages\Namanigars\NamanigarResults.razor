﻿@layout ReportLayout
@page "/namanigar/results"
@inject NavigationManager NavigationManager
@inject UserManager<ApplicationUser> _UserManager
@inject RoleManager<IdentityRole> _RoleManager
@inject AuthenticationStateProvider AuthenticationStateProvider
@using FilterType = Syncfusion.Blazor.DropDowns.FilterType
@inherits OwningComponentBase<ElectionResultsDataService>

@*<a href="/myconstituencies">Back to Constituency Selection</a>*@
<SfToast @ref="ToastObj" Title="Alert" ShowProgressBar="true" Timeout="3000">
    <ToastPosition X="Right" Y="Bottom"></ToastPosition>
</SfToast>
<section style="font-size: 12px;padding: 5px !important">
    <AuthorizeView>
        <Authorized>
            <div style="display:flex; justify-content: space-between; align-items: center">
                <span>@context.User.Identity.Name</span>
                <form method="post" action="Identity/Account/LogOut">
                    <button type="submit" class="nav-link" style="border: 0; background-color: white;"><span class="oi oi-account-logout" aria-hidden="true"></span> Log out</button>
                </form>
            </div>

        </Authorized>
    </AuthorizeView>
    <div class="card border border-primary bg-primary mb-2 text-white">
        <div class="card-body text-black bg-light" style="color:black; padding: 8px;">
            <div class="row">
                <div class="col-md">
                    @if (assmblieslist != null)
                    {
                        @if (assmblieslist.Any())
                        {
                            <SfDropDownList Enabled="@(EditMode == "")" AllowFiltering="true" TItem="GeneralItemDTO" TValue="int?" Placeholder="Cantonment Board" DataSource="@assmblieslist" FloatLabelType="FloatLabelType.Always" ShowClearButton="true">
                                <DropDownListFieldSettings Value="Id" Text="EnglishTitle"></DropDownListFieldSettings>
                                <DropDownListEvents TValue="int?" TItem="GeneralItemDTO" ValueChange="@OnAssemblyChange"></DropDownListEvents>
                            </SfDropDownList>
                        }
                        else
                        {
                            <p>No constituency assigned to this user</p>
                        }
                    }
                    else
                    {
                        <p>Please wait...</p>
                    }
                </div>
            </div>
            <div class="row">

                <div class="col-md">
                    @if (constituencylist != null)
                    {
                        @if (constituencylist.Any())
                        {
                            <SfDropDownList Enabled="@(EditMode == "")" AllowFiltering="true" TItem="NamanigarConstituencyItemDTO" TValue="int?" Placeholder="Constituency" DataSource="@constituencylist" FloatLabelType="FloatLabelType.Always" ShowClearButton="true">
                                <DropDownListFieldSettings Value="Id" Text="EnglishTitle"></DropDownListFieldSettings>
                                <DropDownListEvents TValue="int?" TItem="NamanigarConstituencyItemDTO" ValueChange="@OnConstChange"></DropDownListEvents>
                            </SfDropDownList>
                        }
                        else
                        {
                            <p></p>
                        }
                    }
                    else
                    {
                        <p></p>
                    }
                </div>
                @if (state.state.ElectionType == ElectionType.LocalBody)
                {
                    <div class="col-md-3">
                        @if (seatypelist != null)
                        {
                            @if (seatypelist.Any())
                            {
                                <SfDropDownList TValue="int?" Enabled="@(EditMode == "")" TItem="GeneralItemDTO" Placeholder="Seat" DataSource="@seatypelist" FloatLabelType="FloatLabelType.Always" ShowClearButton="true"
                                                AllowFiltering="true" FilterType="FilterType.Contains">
                                    <DropDownListFieldSettings Value="Id" Text="EnglishTitle"></DropDownListFieldSettings>
                                    <DropDownListEvents TValue="int?" TItem="GeneralItemDTO" ValueChange="@OnSeatTypChange"></DropDownListEvents>
                                </SfDropDownList>
                            }
                            else
                            {
                                <p>No seat type is define on this assembly</p>
                            }
                        }
                        else
                        {
                            <p></p>
                        }
                    </div>
                }
                @*<div class="col-md">
                    @if (namanigarList != null)
                    {
                    @if (namanigarList.Any() && SelectedStructureId != null)
                    {
                    <SfComboBox Enabled="@(EditMode=="")" AllowFiltering="true" TValue="int?" TItem="NamanigarDTO" Placeholder="Namanigar" DataSource="@namanigarList" FloatLabelType="FloatLabelType.Always" @bind-Value="selectedNamanigarId">
                    <ComboBoxFieldSettings Value="Id" Text="Name"></ComboBoxFieldSettings>
                    </SfComboBox>
                    }
                    else
                    {
                    <p>No namanigar is defined on this constituency</p>
                    }
                    }
                    else
                    {
                    <p></p>
                    }
                    </div>*@
            </div>
            @if (result_detail is { Count: > 0 })
            {
                <div class="row">
                    <div class="col">
                        <table class="table table" style="background-color: #3498db; color: white; margin-bottom: 0;">
                            <tr>
                                <td>
                                    Voters: <b>@result_detail[0].VotersCount</b> <br/>
                                    Caste: <b>@(result_detail.Sum(c => c.Votes))</b>
                                </td>
                                <td>
                                    PS: <b>@result_detail[0].TotalPollingStations</b> <br/>
                                    Completed: <b>@NewResPollingStations</b> (@(Math.Round(1.0 * (float)NewResPollingStations / (float)result_detail[0].TotalPollingStations * 100.0, 2))%)
                                </td>

                            </tr>
                        </table>
                    </div>
                </div>
            }
        </div>
    </div>
    @if (SelectedAssemblyId != null && SelectedStructureId != null && SelectedSeatTypeId != null)
    {
        @if (result_detail == null || result_detail.Count == 0)
        {
            <h3>No nomination found on this constituency</h3>
        }
        else if (result_detail[0].TotalPollingStations == 0)
        {
            <h3>Total Polling Stations is not define on this constituency</h3>
        }
        else if (result_detail[0].VotersCount == 0)
        {
            <h3>Total Voters are not defined on this constituency</h3>
        }
        else
        {
            @if (EditMode == "")
            {
                <div class="row mb-2">
                    <div class="col">
                    </div>
                </div>
            }

            <div class="row">
                <div class="col-md">
                    <div class="card border border-success bg-success text-white mb-2">
                        <div class="card-body text-black bg-light" style="padding: 8px">
                            <table style="width: 100%">
                                <tr style="display:flex; align-items: center; gap: 2px; justify-content: space-between">
                                    <td>
                                        <SfNumericTextBox HtmlAttributes="@telInput" ShowSpinButton="false" Format="########" Decimals="0" Min="0" Placeholder="Polling Stations (count)" FloatLabelType="FloatLabelType.Always" @bind-Value="ChangePollingStations" Max="result_detail[0].TotalPollingStations"></SfNumericTextBox>
                                    </td>
                                    <td style="display:flex; gap: 4px;">
                                        @if (EditMode != "")
                                        {
                                            <SfButton style="font-size: 12px" CssClass="e-primary" OnClick="SaveRecord">Save</SfButton>
                                            <SfButton style="font-size: 12px" CssClass="e-info" OnClick="CancelRecord">Cancel</SfButton>
                                        }
                                        else
                                        {
                                            if (IsPostingLocked)
                                            {
                                                <p>All PS Completed</p>
                                            }
                                            else
                                            {
                                                <SfButton style="font-size: 12px" Disabled="@(EditMode != "")" CssClass="e-primary" OnClick="@(() => OpenForm("Add"))">Add</SfButton>
                                                <SfButton style="font-size: 12px" Disabled="@(EditMode != "")" CssClass="e-info" OnClick="@(() => OpenForm("Update"))">Update</SfButton>
                                            }

                                            <SfButton style="font-size: 12px" class="e-link" OnClick="OpenResultsLog">View Log</SfButton>
                                        }
                                    </td>
                                </tr>
                            </table>
                            <small class="urdu-sm">موصول شدہ پولنگ اسٹیشن کی تعداد</small>
                        </div>
                    </div>
                </div>
            </div>
            <table class="table table-sm table-striped">
                <thead>
                <tr class="bg-info text-white">
                    <th>Name</th>
                    <th>Party</th>
                    <th>Symbol</th>
                    <th>Votes</th>
                    @if (EditMode != "")
                    {
                        <th>@EditMode</th>
                    }

                    /*<th style="width:170px">Action</th>*/
                </tr>
                </thead>
                <tbody>
                @foreach (var res in result_detail)
                {
                    //var cImg = $"media/candidates/{ res.CandidateId }.jpg";
                    //var pImg = $"media/flags/{res.PartyId}.jpg";
                    //var sImg = $"media/symbols/{res.SymbolId}.jpg";
                    var stl = res.IsWithdraw ? "background-color:#dc35457a" : "";
                    <tr style="@stl">
                        <td style="vertical-align:central !important;vertical-align: middle">
                            @res.EnglishName<br/>
                            <span class="urdu-column">@res.UrduName</span>
                            @if (res.IsWithdraw)
                            {
                                <span style='color:red; font-weight:bold'> (Withdraw)</span>
                            }
                        </td>
                        <td style="vertical-align:central !important;vertical-align: middle">
                            @res.Party<br/>
                        </td>
                        <td style="width:65px; text-align:center !important; vertical-align: middle!important">
                            <img src="@res.symbolImg" style="width:50px" align="left" alt="BAT"/>
                        </td>
                        <td style="text-align: center; vertical-align: middle">@res.TotalVotes</td>
                        @if (EditMode != "")
                        {
                            <td style="vertical-align:central;vertical-align: middle">
                                <SfNumericTextBox HtmlAttributes="@telInput" Format="########" @bind-Value="res.ChangeVotes" Decimals="0" Min="0" Placeholder="Votes" ShowSpinButton="false" Width="70px"></SfNumericTextBox>
                            </td>
                        }

                        /*
                        <td>
                            <SfButton CssClass="e-info">Edit</SfButton>
                        </td>*/
                    </tr>
                }
                </tbody>
            </table>
        }
    }


</section>