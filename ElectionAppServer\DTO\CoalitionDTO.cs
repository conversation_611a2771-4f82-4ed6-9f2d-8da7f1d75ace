﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace ElectionAppServer.DTO;

public class CoalitionDTO
{
    //PartyIds = new List<int>();

    public int Id { get; set; }

    [Required] [StringLength(200)] public string EnglishTitle { get; set; }

    [Required] [StringLength(200)] public string UrduTitle { get; set; }

    public string Parties { get; set; }

    //public List<int> PartyIds { get; set; }

    public List<GeneralItemDTO> TeamParties { get; set; } = new();

    public string CreatedBy { get; set; }
    public string CreatedOn { get; set; }
    public string ModifiedBy { get; set; }
    public string ModifiedOn { get; set; }
}