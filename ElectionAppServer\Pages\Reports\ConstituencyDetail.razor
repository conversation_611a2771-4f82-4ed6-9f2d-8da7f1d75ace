﻿@layout ReportLayout

@page "/report/constituency/{consId:int}"
@inherits OwningComponentBase<ConstituencyService>

<style>
        body {
            user-select: none;
        }
    </style>

@if (info == null)
{
    <p>Loading...</p>
}
else
{
    if (info.PreviousConstId != null)
    {
        var trg = $"pv{info.PreviousConstId}";
        var pvl = $"/report/constituency/{info.PreviousConstId}";
        <h3>@info.Code - @info.EnglishTitle (Previous: <a style="color:blue;text-decoration: underline" target="@trg" href="@pvl">@info.PreviousConst</a>)</h3>
    }
    else
    {
        <h3>@info.Code - @info.EnglishTitle</h3>
    }

    <h4>@info.AssemblyEnglish</h4>
    <div class="card bg-info mb-3">
        <div class="card-header" style="display: flex; justify-content: space-between;align-items: center">
            <div>
                Constituency @info.Code - @info.EnglishTitle - @info.AssemblyEnglish
            </div>
            <div class="urdu-cap" style="text-align: right;direction: rtl">انتخابی حلقہ @info.Code @info.UrduTitle - @info.AssemblyUrdu</div>
        </div>
        <div class="card-body bg-light" style="color:black">
            <div class="row">
                <div class="col rptfield">
                    <div>Province: </div>
                    <div class="rptdata">
                        <b>@info.ProvinceEnglish - <span class="urdu-cap">@info.ProvinceUrdu</span></b>
                    </div>
                </div>
                @*<div class="col rptfield"><div>Division: </div><div class="rptdata"><b>@info.DivisionEnglish - <span class="urdu-cap">@info.DivisionUrdu</span></b></div></div>*@
                <div class="col rptfield">
                    <div>District: </div>
                    <div class="rptdata">
                        <b>@info.DistrictEnglish - <span class="urdu-cap">@info.DistrictUrdu</span></b>
                    </div>
                </div>
                <div class="col rptfield">
                    Region:
                    <div class="rptdata">
                        <b>@info.RegionEnglish - <span class="urdu-cap">@info.RegionUrdu</span></b>
                    </div>
                </div>
            </div>
            @if (state.state.ElectionType == ElectionType.LocalBody)
            {
                <div class="row">
                    <div class="col rptfield">
                        Town:
                        <div class="rptdata">
                            <b>@info.TownEnglish - <span class="urdu-cap">@info.TownUrdu</span></b>
                        </div>
                    </div>
                    <div class="col rptfield">
                        UC:
                        <div class="rptdata">
                            <b>@info.EnglishTitle - <span class="urdu-cap">@info.UrduTitle</span></b>
                        </div>
                    </div>
                </div>
            }
            <h5>Demographics</h5>
            @foreach (var dm in info.AllDemographic)
            {
                <h4>@dm.Phase</h4>
                <div class="row">
                    <div class="col rptfield">
                        Male Voters:
                        <div class="rptdata">
                            <b>@dm.MaleVoters</b>
                        </div>
                    </div>
                    <div class="col rptfield">
                        Female Voters:
                        <div class="rptdata">
                            <b>@dm.FemaleVoters</b>
                        </div>
                    </div>
                    <div class="col rptfield">
                        Total Voters:
                        <div class="rptdata">
                            <b>@dm.TotalVoters</b>
                        </div>
                    </div>
                    <div class="col rptfield">
                        Total Polling Stations:
                        <div class="rptdata">
                            <b>@dm.TotalPollingStations</b>
                        </div>
                    </div>
                    <div class="col rptfield">
                        Population:
                        <div class="rptdata">
                            <b>@dm.Population</b>
                        </div>
                    </div>
                </div>
                <hr/>

                @if (info.Economy != "")
                {
                    var mm = dm.Economy.Replace("\n", "<br />");
                    <div class="row">
                        <div class="col rptfield">
                            <div>Trivia (Economy):</div>
                            <div class="rptdata" style="direction:rtl;font-family: Urdu Nastaleeq;display:flex; text-align:right">@((MarkupString)mm)</div>
                        </div>
                    </div>
                }

                @if (info.GeneralTrivia != "")
                {
                    var mm = dm.GeneralTrivia.Replace("\n", "<br />");
                    <div class="row">
                        <div class="col rptfield">
                            <div>Trivia (General): </div>
                            <div class="rptdata" style="direction:rtl;font-family: Urdu Nastaleeq;display:flex; text-align:right">@((MarkupString)mm)</div>
                        </div>
                    </div>
                }

                @if (string.IsNullOrEmpty(dm.Profile) == false)
                {
                    var mm = dm.Profile.Replace("\n", "<br />");
                    <div class="row">
                        <div class="col rptfield" st>
                            <div>Profile: </div>
                            <div class="rptdata"
                                 style="direction:rtl;font-family: Urdu Nastaleeq;display:flex; text-align:right">
                                @((MarkupString)mm)

                            </div>
                        </div>
                    </div>
                }

                @if (dm.Problems != "")
                {
                    <div class="row">
                        <div class="col rptfield">
                            <div>Problems:</div>
                            <div class="rptdata"
                                 style="direction:rtl;font-family: Urdu Nastaleeq;display:flex; text-align:right">
                                @dm.Problems
                            </div>
                        </div>
                    </div>
                }

                <div class="row">
                    @if (dm.LanguageSpoken != "")
                    {
                        <div class="col rptfield">
                            <div>Spoken Languages: </div>
                            <div class="rptdata">@dm.LanguageSpoken</div>
                        </div>
                    }
                    @if (dm.Castes != "")
                    {
                        <div class="col rptfield">
                            <div>Castes: </div>
                            <div class="rptdata">@dm.Castes</div>
                        </div>
                    }
                    @if (dm.ImportantAreas != "")
                    {
                        <div class="col rptfield">
                            <div>Important Areas/Cities: </div>
                            <div class="rptdata" style="direction:rtl;font-family: Urdu Nastaleeq;display:flex; text-align:right">@dm.ImportantAreas</div>
                        </div>
                    }
                </div>
                <div class="row">
                    @if (dm.UrbanArea != null)
                    {
                        <div class="col rptfield">
                            <div>Urban Area: </div>
                            <div class="rptdata">
                                <b>@Math.Round((float)dm.UrbanArea, 2) %</b>
                            </div>
                        </div>
                    }
                    @if (dm.RuralArea != null)
                    {
                        <div class="col rptfield">
                            <div>Rural Area: </div>
                            <div class="rptdata">
                                <b>@Math.Round((float)dm.RuralArea, 2) %</b>
                            </div>
                        </div>
                    }
                    @if (dm.TotalArea != "")
                    {
                        <div class="col rptfield">
                            <div>Total Area: </div>
                            <div class="rptdata">
                                <b>@dm.TotalArea</b>
                            </div>
                        </div>
                    }
                </div>
                <div class="row">
                    @if (dm.MajorIncomeSource != "")
                    {
                        <div class="col rptfield">
                            <div>Major Income Source: </div>
                            <div class="rptdata">
                                <b>@dm.MajorIncomeSource</b>
                            </div>
                        </div>
                    }
                    @if (dm.LiteracyRate != "")
                    {
                        <div class="col rptfield">
                            <div>Literacy Rate: </div>
                            <div class="rptdata">
                                <b>@dm.LiteracyRate</b>
                            </div>
                        </div>
                    }
                    @if (dm.AverageHouseHoldIncome != "")
                    {
                        <div class="col rptfield">
                            <div>Average Household Income: </div>
                            <div class="rptdata">
                                <b>@dm.AverageHouseHoldIncome</b>
                            </div>
                        </div>
                    }
                </div>
            }
        </div>
    </div>
    <div class="card bg-secondary">
        @foreach (var i in info.Phases)
        {
            <div class="card-header">
                <h3 style="margin-bottom:0px;">@info.ElectionEnglish - @i.Title</h3>
            </div>
            <div class="card-body bg-light">
                @foreach (var st in i.Seats)
                {
                    <div class="card bg-primary text-white mb-3">
                        <div class="card-header" style="display: flex; justify-content: space-between;align-items: center">
                            <div>Nominations for @st.SeatTypeEnglish</div>
                            <div class="urdu-cap">امیدوار @st.SeatTypeUrdu</div>
                        </div>
                        <div class="card-body bg-light text-black" style="color:black">
                            <table class="table table-sm">
                                <thead>
                                <tr>
                                    <th>Pic</th>
                                    <th>Name</th>
                                    <th>Party</th>
                                    <th>Symbol</th>
                                    <th>Weight</th>
                                    <th>Votes</th>
                                </tr>
                                </thead>
                                <tbody>
                                @foreach (var nom in st.Nominations)
                                {
                                    //var ccId = $"/media/candidates/{nom.CandidateId}.jpg";
                                    //var ffId = $"/media/flags/{nom.PartyId}.jpg";
                                    //var ssId = $"/media/symbols/{nom.SymbolId}.jpg";
                                    <tr>
                                        <td>
                                            <img src="@nom.CandidateUrl" style="width:75px; height:100px" class="myimg" onerror="this.onerror=null; this.src='media/candidates/blank.jpg'"/>
                                        </td>
                                        <td>
                                            <a target="@($"cd{nom.CandidateId}")" href="report/candidate/@nom.CandidateId">@nom.NameEnglish</a><br/>
                                            <span class="urdu-cap">@nom.NameUrdu</span>
                                        </td>
                                        <td>
                                            <img src="@nom.PartyFlagUrl" class="myimg" align="left"/>
                                            <a target="pp@(nom.PartyId)" href="/report/party/@nom.PartyId">@nom.PartyEnglish</a><br>
                                            <span class="urdu-cap">@nom.PartyUrdu</span>
                                        </td>
                                        <td>
                                            <img src="@nom.SymbolUrl" class="myimg" align="left"/>@nom.SymbolEnglish<br>
                                            <span class="urdu-cap">@nom.SymbolUrdu</span>
                                        </td>
                                        <td>@nom.Weight</td>
                                        <td>@nom.VoteObtain</td>
                                    </tr>
                                }
                                </tbody>
                            </table>
                        </div>
                    </div>
                }
                @if (i.SubSeats is { Count: > 0 })
                {
                    @foreach (var stt in i.SubSeats)
                    {
                        <div class="card bg-success text-white mb-3">
                            <div class="card-header" style="display: flex; justify-content: space-between;align-items: center">
                                <div>Nominations for @stt.SeatTypeEnglish</div>
                                <div class="urdu-cap">امیدوار برائے @stt.SeatTypeUrdu</div>
                            </div>
                            <div class="card-body bg-light text-black" style="color:black">
                                <table class="table table-sm">
                                    <thead>
                                    <tr>
                                        <th>Pic</th>
                                        <th>Name</th>
                                        <th>Party</th>
                                        <th>Symbol</th>
                                        <th>Weight</th>
                                        <th>Votes</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    @foreach (var nom in stt.Nominations)
                                    {
                                        //var ccId = $"/media/candidates/{nom.CandidateId}.jpg";
                                        //var ffId = $"/media/flags/{nom.PartyId}.jpg";
                                        //var ssId = $"/media/symbols/{nom.SymbolId}.jpg";
                                        <tr>
                                            <td>
                                                <img src="@nom.CandidateUrl" style="width:75px; height:100px" class="myimg" onerror="this.onerror=null; this.src='media/candidates/blank.jpg'"/>
                                            </td>
                                            <td>
                                                @nom.NameEnglish<br/>
                                                <span class="urdu-cap">@nom.NameUrdu</span>
                                            </td>
                                            <td>
                                                <img src="@nom.PartyFlagUrl" class="myimg" align="left"/>@nom.PartyEnglish<br>
                                                <span class="urdu-cap">@nom.PartyUrdu</span>
                                            </td>
                                            <td>
                                                <img src="@nom.SymbolUrl" class="myimg" align="left"/>@nom.SymbolEnglish<br>
                                                <span class="urdu-cap">@nom.SymbolUrdu</span>
                                            </td>
                                            <td>@nom.Weight</td>
                                            <td>@nom.VoteObtain</td>
                                        </tr>
                                    }
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    }
                }
            </div>
        }
    </div>
}

@code {
    [Parameter] public int consId { get; set; }
    ConstInfoDTO info;

    protected override async Task OnParametersSetAsync()
    {
        info = await Service.GetConstituencyInfo(consId, state.state.PhaseId);
    }

}