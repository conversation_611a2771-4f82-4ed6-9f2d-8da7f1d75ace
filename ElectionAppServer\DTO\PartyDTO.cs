﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace ElectionAppServer.DTO;

public class PartyDTO
{
    public int Id { get; set; }

    [StringLength(200)] [Required] public string EnglishTitle { get; set; }

    [StringLength(200)] [Required] public string UrduTitle { get; set; }

    [StringLength(50)] [Required] public string ShortEnglishTitle { get; set; }

    [StringLength(50)] [Required] public string ShortUrduTitle { get; set; }

    public string FlagURL { get; set; }
    public string SymbolURL { get; set; }
    public string Symbol { get; set; }

    public int? SymbolId { get; set; }

    [StringLength(200)] public string Leader { get; set; }

    [StringLength(200)] public string Designation { get; set; }

    [StringLength(500)] public string Address { get; set; }

    [StringLength(1000)] public string Trivia { get; set; }

    public string CurrentLeader { get; set; }
    public string CurrentLeaderDesignation { get; set; }

    //public int? CoalitionId { get; set; }
    public string Coalition { get; set; }

    public string LeaderPicId { get; set; }
    public string CurrentLeaderPicId { get; set; }
    public string AuditInfo { get; set; }
    public DateTime? DateOfCreation { get; set; }
    public int TeamId { get; set; } = 0;
    public bool IsSelected { get; set; } = false;
    public bool DisabbledDelete { get; set; }
    public bool NominationExistOnThisElection { get; set; }

    public bool IsGeneral { get; set; }
    public bool IsGB { get; set; }
    public bool IsAJK { get; set; }
    public bool IsLB { get; set; }
    public bool IsLBSindh { get; set; }
    public bool IsLBBalochistan { get; set; }
    public bool IsLBAJK { get; set; }
    public bool IsLBIslamabad { get; set; }
    public bool IsLBPunjab { get; set; }

    public string ElectionPool
    {
        get
        {
            var op = new List<string>();
            if (IsGeneral) op.Add("General");
            if (IsGB) op.Add("Gilgit Baltistan");
            if (IsAJK) op.Add("AJK");
            if (IsLB) op.Add("LB KPK");
            if (IsLBBalochistan) op.Add("LB Balochista");
            if (IsLBSindh) op.Add("LB Sindh");
            if (IsLBPunjab) op.Add("LB Punjab");
            if (IsLBAJK) op.Add("LB AJK");
            if (IsLBIslamabad) op.Add("LB IslamabD");
            return string.Join(", ", op);
        }
    }
}

public class PartyPositionStatistic
{
    public string Party { get; set; }
    public double SeatsCount { get; set; }
    public string Text { get; set; }
    public string Fill { get; set; }
}

public class ChartData
{
    public string Country { get; set; }
    public double PPP { get; set; }
    public double PTI { get; set; }
    public double PMLN { get; set; }
}