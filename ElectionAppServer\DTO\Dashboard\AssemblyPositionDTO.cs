﻿using System;
using System.Collections.Generic;

namespace ElectionAppServer.DTO.Dashboard;

public class AssemblyPositionDTO
{
    public int AssemblyId { get; set; }
    public string Assembly { get; set; }
    public int ElectionId { get; set; }
    public string Election { get; set; }
    public int TotalSeats { get; set; }
    public int RegisterVoters { get; set; }
    public int VotePolled { get; set; }
    public float TurnOutPer { get; set; }
    public int ResultAnnounced { get; set; }
    public float MaleVoterPer { get; set; }
    public float FemaleVoterPer { get; set; }
    public int MaleVoters { get; set; }
    public int FemaleVoters { get; set; }

    public List<PartySeatDTO> PartyWiseSeatCounts { get; set; }
    public List<AssemblyConstituencyDetailDTO> Detail { get; set; }
}

public class PartySeatDTO
{
    public string Party { get; set; }
    public int Count { get; set; }
}

public class AssemblyConstituencyDetailDTO
{
    public string Constiteuncy { get; set; }
    public int RegisterVoters { get; set; }
    public int VotePolled { get; set; }
    public double TurnOutPer { get; set; }
    public int TPS { get; set; }
    public int RPS { get; set; }
    public double PSPer { get; set; }
    public string Winner { get; set; }
    public string WinnerParty { get; set; }
    public int WinnerVotes { get; set; }
    public double WinnerPer { get; set; }
    public string RunnerUp { get; set; }
    public string RunnerUpParty { get; set; }
    public int RunnerUpVotes { get; set; }
    public double RunnerUpPer { get; set; }
    public int VoteDiff { get; set; }
    public DateTime LastUpdatedOn { get; set; }
}