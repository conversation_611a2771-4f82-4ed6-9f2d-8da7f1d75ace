using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using ElectionAppServer.Data;
using ElectionAppServer.DTO;
using ElectionAppServer.Model;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Syncfusion.Blazor.Grids;
using Syncfusion.Blazor.Notifications;
using Syncfusion.Blazor.Popups;
using Action = Syncfusion.Blazor.Grids.Action;

namespace ElectionAppServer.Pages.Election;

public partial class Assemblies
{
    private readonly string FormTitle = "Create Assembly";

    private readonly List<GeneralItemDTO> nominationTypes = new()
    {
        new GeneralItemDTO { Id = 1, EnglishTitle = "Candidate" },
        new GeneralItemDTO { Id = 2, EnglishTitle = "Panel" }
    };

    //public string ToastContent = "Unable to delete Record, Related Record(s) Exist";
    private List<GeneralItemDTO> assemblyTypes;

    private SfDialog dlgForm;
    private bool isDlgVisible;

    private List<ElectionAssemblyDTO> objList;
    private string SaveButtonText = "Save";

    private List<GeneralItemDTO> seatConfigurationList = new()
    {
        new GeneralItemDTO { EnglishTitle = "Single Seat Per Constituency", Id = 1 },
        new GeneralItemDTO { EnglishTitle = "Multi Seat Per Constituency", Id = 2 },
        new GeneralItemDTO { EnglishTitle = "Multi Winner Per Constituency", Id = 3 }
    };

    private List<GeneralItemDTO> seatTypes;
    private ElectionAssemblyDTO selectedObj = new();
    private SfToast ToastObj;
    public SfGrid<ConSeatDTO> ConsSeatsGrid { get; set; }

    public int? constituencyTypeId { get; set; }

    public static List<ConstituencyTypeDTO> ConstituencyTypeList { get; set; } = new();
    public bool DisableButton { get; set; }

    [Parameter] public int electionId { get; set; }

    public string ElectionTitle { get; set; }

    public SfGrid<ElectionAssemblyDTO> Grid { get; set; }

    public List<int?> MultiVal { get; set; }

    public string provinceName { get; set; }

    public int? seatTypeId { get; set; }

    [CascadingParameter] private Task<AuthenticationState> authenticationStateTask { get; set; }

    public ElectionType electionType { get; set; }

    public async Task ActionBeginHandler(ActionEventArgs<ConSeatDTO> args)
    {
        var j = args.RequestType;
        var k = j;
        if (args.RequestType == Action.Cancel || args.RequestType == Action.Refresh ||
            args.RequestType == Action.Reorder || args.RequestType == Action.Delete || args.RequestType == Action.Save)
            DisableButton = false;
        else
            DisableButton = true;

        if (args.RequestType == Action.Delete)
        {
            var isNominationExist = await Service.IsNominationExist(selectedObj.Id, args.Data.SeatTypeId);
            if (isNominationExist)
            {
                args.Cancel = true;
                var mm = new ToastModel
                {
                    Title = "Error", Content = "You cannot delete this type, one or more nomination exist on it",
                    CssClass = "e-toast-danger", ShowProgressBar = true, Timeout = 5000, ShowCloseButton = true
                };
                //new ToastModel { Content = "Record saved", Title = "Results", CssClass = "e-toast-success", ShowProgressBar = true, Timeout = 5000, ShowCloseButton = true };
                await ToastObj.ShowAsync(mm);
            }
        }
        //var kk = args.RequestType;
    }

    public void CustomizeCell(QueryCellInfoEventArgs<ElectionAssemblyDTO> args)
    {
        if (args.Column.Field is "UrduTitle" or "AssemblyType" /*|| args.Column.Field == "SeatsList"*/)
        {
            args.Cell.AddClass(new[] { "urdu-column" });
            args.Cell.AddStyle(new[] { "font-size:18px;line-height: 1.4;text-align: right" });
        }
    }

    public async Task ExcelExport()
    {
        await Grid.ExportToExcelAsync();
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            var user = (await authenticationStateTask).User;
            if (user.Identity.IsAuthenticated)
            {
                //var dd  = await ProtectedSessionStore.GetAsync<ElectionStateDTO>("election");
                var dd = await localStorage.GetAsync<ElectionStateDTO>(await confreader.GetSettingValue("electionkey"));
                electionType = dd.Value.ElectionType.Value;
                state.SetState(dd.Value);
                await FillData();
                StateHasChanged();
            }
            else
            {
                try
                {
                    //await  ProtectedSessionStore.DeleteAsync("election");
                    await localStorage.DeleteAsync(await confreader.GetSettingValue("electionkey"));
                    state.SetState(new ElectionStateDTO
                        { ElectionId = 0, ElectionTitle = "", PhaseId = 0, PhaseTitle = "" });
                }
                catch (Exception)
                {
                    // ignored
                }

                NavigationManager.NavigateTo("");
            }

            StateHasChanged();
        }
    }

    protected override async Task OnParametersSetAsync()
    {
        await FillData();
    }

    private async Task FillData()
    {
        objList = await Service.GetList(electionId);
        ElectionTitle = await Service.GetElectionTitle(electionId);
        assemblyTypes = await Service.GetAssemblyTypes();
        seatTypes = await Service.GetSeatTypes();
        ConstituencyTypeList = await Service.GetConstituencyTypes(electionType);
    }

    //private void AddSelected(SelectEventArgs ev)
    //{
    //	var dd = ev.ItemData.ToString();
    //}

    private void beforeOpen(BeforeOpenEventArgs args)
    {
        args.MaxHeight = null;
    }

    private void ClearData()
    {
        selectedObj = new ElectionAssemblyDTO();
        selectedObj.ElectionId = electionId;
    }

    private async Task DeleteRecord(int Id)
    {
        var para = new string[1];
        para[0] = "Are you want to delete this record?";
        var res = await JSRuntime.InvokeAsync<bool>("confirm", para);
        if (res == false)
            return;
        try
        {
            var msg2 = await Service.Delete(Id);
            objList = await Service.GetList(electionId);
            await Grid.Refresh();
            ClearData();
        }
        catch (Exception ex)
        {
            var mm = new ToastModel
            {
                Title = "Error", Content = ex.Message, CssClass = "e-toast-danger", ShowProgressBar = true,
                Timeout = 5000,
                ShowCloseButton = true
            };
            await ToastObj.ShowAsync(mm);
        }
    }

    //private void onChange(ChangeEventArgs<int?> args)
    //{
    //	constituencyTypeId = args.Value;
    //	StateHasChanged();
    //}

    //private void onChangeSeatType(ChangeEventArgs<int?> args)
    //{
    //	seatTypeId = args.Value;
    //	StateHasChanged();
    //}

    private async Task OpenCreateForm()
    {
        selectedObj = new ElectionAssemblyDTO { ReserveSeats = 0, WomenSeats = 0 };
        await dlgForm.ShowAsync();
        SaveButtonText = "Create";
    }

    private async void OpenEditForm(ElectionAssemblyDTO st)
    {
        selectedObj = new ElectionAssemblyDTO
        {
            Id = st.Id,
            AssemblyType = st.AssemblyType,
            AssemblyTypeId = st.AssemblyTypeId,
            ElectionId = st.ElectionId,
            EnglishTitle = st.EnglishTitle,
            SeatsList = st.SeatsList,
            UrduTitle = st.UrduTitle,
            ReserveSeats = st.ReserveSeats,
            WomenSeats = st.WomenSeats,
            NominationTypeId = st.NominationTypeId,
            ConsSeatTypeList = new List<ConSeatDTO>()
            //SeatConfigurationId = st.SeatConfigurationId
        };
        foreach (var item in st.ConsSeatTypeList)
            selectedObj.ConsSeatTypeList.Add(new ConSeatDTO
            {
                ConstituencyType = "", ConstituencyTypeId = item.ConstituencyTypeId, IsDb = true, SeatType = "",
                SeatTypeId = item.SeatTypeId, SeatCount = item.SeatCount
            });

        selectedObj = await Service.GetElectionById(st.Id);

        StateHasChanged();
        await dlgForm.ShowAsync();
        try
        {
            if (electionType == ElectionType.LocalBody)
                await ConsSeatsGrid.Refresh();
        }
        catch (Exception)
        {
            // ignored
            //Console.WriteLine(e);
            //throw;
        }

        SaveButtonText = "Update";
    }

    private async Task RemoveConsSeat(ConSeatDTO obj)
    {
        await Task.CompletedTask;
    }

    private void RemoveSelected(object ev)
    {
    }

    private async Task SaveData()
    {
        var et = await Service.GetElectionTypeById(state.state.ElectionId);
        if (et == ElectionType.LocalBody)
            if (selectedObj.ConsSeatTypeList == null || selectedObj.ConsSeatTypeList.Count == 0)
            {
                var tc = new ToastModel
                {
                    Title = "Seat Configuration",
                    Content = "Please select at-least on seat type configuration for this assembly",
                    ShowProgressBar = true,
                    Timeout = 5000,
                    ShowCloseButton = true
                };
                await ToastObj.ShowAsync(tc);
                return;
            }

        var user = (await authenticationStateTask).User;
        try
        {
            selectedObj.ElectionId = state.state.ElectionId;
            var res = await Service.Save(selectedObj, user.Identity.Name);
            objList = await Service.GetList(electionId);
            await Grid.Refresh();
            await dlgForm.HideAsync();
        }
        catch (Exception ex)
        {
            var mm = new ToastModel
            {
                Title = "Error", Content = ex.Message, CssClass = "e-toast-danger", ShowProgressBar = true,
                Timeout = 5000,
                ShowCloseButton = true
            };
            if (ex.InnerException != null)
                mm.Content += " Detail: " + ex.InnerException.Message;
            await ToastObj.ShowAsync(mm);
        }
    }

    private async Task TranslateToUrdu()
    {
        try
        {
            if (string.IsNullOrEmpty(selectedObj.UrduTitle))
                selectedObj.UrduTitle = await Translation.TranslateText(selectedObj.EnglishTitle);
        }
        catch (Exception)
        {
            // ignored
        }
    }

    private async Task OnAssemblyTypeChange(object args)
    {
        var q = selectedObj.AssemblyTypeId;
        await Task.CompletedTask;
    }
}