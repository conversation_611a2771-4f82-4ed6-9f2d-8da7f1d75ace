﻿using System;

namespace ElectionAppServer.Model;

public class SearchCandidate
{
    public int ElectionId { get; set; }
    public int CandidateId { get; set; }
    public string FullName { get; set; }
    public string EnglishName { get; set; }
    public string UrduName { get; set; }
    public Gender? Gender { get; set; }
    public DateTime? DateOfBirth { get; set; }
    public string CreatedBy { get; set; }
    public DateTime CreatedOn { get; set; }
    public string ModifiedBy { get; set; }
    public DateTime? ModifiedOn { get; set; }
    public string UrduFatherName { get; set; }
    public string EnglishFatherName { get; set; }
    public int? DistrictId { get; set; }
    public string District { get; set; }
    public int NominationCount { get; set; }
    public string CandidateType { get; set; }
    public bool IsFresh { get; set; }
    public string Status { get; set; }
    public string CurrentParty { get; set; }
    public string Constituencies { get; set; }
    public CandidatePoll? CandidatePoll { get; set; }
}