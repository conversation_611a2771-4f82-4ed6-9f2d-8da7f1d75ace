﻿@page "/bbapproval"
@attribute [Authorize(Roles = "Producer,IMM")]

@inject NavigationManager NavigationManager
@inject UserManager<ApplicationUser> _UserManager
@inject RoleManager<IdentityRole> _RoleManager
@inject AuthenticationStateProvider AuthenticationStateProvider

@using FilterType = Syncfusion.Blazor.Grids.FilterType
@inherits OwningComponentBase<ElectionResultsDataService>

<SfDialog Width="950px" @bind-Visible="@isDlgVisible" @ref="dlgForm" IsModal="true" ShowCloseIcon="true">
    <DialogEvents OnOpen="beforeOpen"></DialogEvents>
    <DialogTemplates>
        <Header>Detail</Header>
        <Content>
            <div class="row">
                <div class="col urdu-column" style="direction: rtl; text-align: right; background-color: #87cefa; padding: 5px; line-height: 1.7">
                    @((MarkupString)detail.Ticker)
                </div>
            </div>

        </Content>
    </DialogTemplates>
</SfDialog>

<section >
    <div style="display: flex; align-items: center; gap:  5px;">

        <h3>Live Results</h3>

    </div>
    @if (results == null)
    {
        <p>Please wait...</p>
    }
    else if (results.Count == 0)
    {
        <p>No result is posted yet</p>
    }
    else
    {
        <div class="mb-2">

            <SfSwitch @bind-Checked="isPendingOnly" OnLabel="Pending Only" OffLabel="All Results"></SfSwitch>
            <span>@(isPendingOnly ? "Pending Approvals Only" : "Complete List")</span>
            <SfButton CssClass="e-primary" @onclick="ReloadResult">Refresh</SfButton>
        </div>


        <SfGrid @ref="pendingResultsGrid" DataSource="results" AllowFiltering="true" AllowSorting="true" AllowTextWrap="true" Width="100%" Height="calc(100vh - 190px)">
            <GridFilterSettings Type="FilterType.Excel"></GridFilterSettings>
            <GridColumns>
                <GridColumn HeaderText="Status" Field=@nameof(ResultDTO.BBStatus) AutoFit="true"></GridColumn>
                <GridColumn HeaderText="Assembly" Field=@nameof(ResultDTO.Assembly) AutoFit="true"></GridColumn>
                <GridColumn HeaderText="Code" Field=@nameof(ResultDTO.Constituency) AutoFit="true"></GridColumn>
                <GridColumn HeaderText="Seat Type" Field=@nameof(ResultDTO.SeatType) AutoFit="true"></GridColumn>
                <GridColumn HeaderText="Winner" Field=@nameof(ResultDTO.WinnerName) AutoFit="true">
                    <Template Context="mm">
                        @{
                            if (mm is ResultDTO obj)
                            {
                                if (obj.SeatTypeId != 5)
                                {
                                    var pic = "";
                                    if (File.Exists(Directory.GetCurrentDirectory() + "\\wwwroot\\media\\candidates\\" + obj.WinnerCandidateId + ".jpg"))
                                    {
                                        pic = "/media/candidates/" + obj.WinnerCandidateId + ".jpg";
                                    }
                                    else
                                    {
                                        pic = "/media/candidates/blank.jpg";
                                    }

                                    <div style="display: flex;gap:  5px;background: antiquewhite;border: 2px solid #c6b8a6;">
                                        <img src="@pic" style="max-width: 100px; max-height: 100px;" alt="@obj.WinnerName"/>
                                        <div>
                                            <b style="color: blue">@obj.WinnerName</b><br/>
                                            <span style="color: darkgreen">@obj.WinnerParty</span>
                                        </div>

                                    </div>
                                }
                                else
                                {
                                    var pic = "";
                                    if (File.Exists(Directory.GetCurrentDirectory() + "\\wwwroot\\media\\panelcandidates\\" + obj.WinnerCandidateId + ".jpg"))
                                    {
                                        pic = "/media/panelcandidates/" + obj.WinnerCandidateId + ".jpg";
                                    }
                                    else
                                    {
                                        pic = "/media/candidates/blank.jpg";
                                    }

                                    <div style="display: flex;gap:  5px; background-color:lightblue;border: 2px solid #84b3c3;">
                                        <img src="@pic" style="max-width: 100px; max-height: 100px;" alt="@obj.WinnerName"/>
                                        <div>
                                            <b style="color: blue">@obj.WinnerName (Chariman)</b><br/>
                                            <span style="color: darkgreen">@obj.WinnerParty</span>
                                        </div>

                                    </div>
                                }
                            }
                        }
                    </Template>
                </GridColumn>
                <GridColumn HeaderText="Runner-Up" Field=@nameof(ResultDTO.RunnerUpName) AutoFit="true">
                    <Template Context="mm">
                        @{
                            if (mm is ResultDTO obj)
                            {
                                if (obj.SeatTypeId != 5)
                                {
                                    var pic = "";
                                    if (File.Exists(Directory.GetCurrentDirectory() + "\\wwwroot\\media\\candidates\\" + obj.RunnerUpCandidateId + ".jpg"))
                                    {
                                        pic = "/media/candidates/" + obj.RunnerUpCandidateId + ".jpg";
                                    }
                                    else
                                    {
                                        pic = "/media/candidates/blank.jpg";
                                    }

                                    <div style="display: flex;gap:  5px;">
                                        <img src="@pic" style="max-width: 100px; max-height: 100px;" alt="@obj.RunnerUpName"/>
                                        <div>
                                            <b style="color:red">@obj.RunnerUpName</b><br/>
                                            <span style="color: darkgreen">@obj.RunnerUpParty</span>
                                        </div>

                                    </div>
                                }
                                else
                                {
                                    <div style="display: flex;gap:  5px;">
                                        <div>
                                            <b style="color:red">@obj.RunnerUpName</b><br/>
                                            <span style="color: darkgreen">@obj.RunnerUpParty</span>
                                        </div>

                                    </div>
                                }
                            }
                        }
                    </Template>
                </GridColumn>
                <GridColumn AutoFit="true">
                    <Template Context=mm>
                        @{
                            var cc = mm as ResultDTO;
                            if (cc != null && cc.BBStatus == "Pending")
                            {
                                <SfButton @onclick="() => SetAllowToBB(cc.ConstituencyId, cc.SeatTypeId)" CssClass="e-primary">Allow</SfButton>
                            }
                            else
                            {
                                <SfButton @onclick="() => SetBlockToBB(cc.ConstituencyId, cc.SeatTypeId)" CssClass="e-danger">Block</SfButton>
                            }
                        }
                    </Template>
                </GridColumn>
                <GridColumn HeaderText="Last Update" Field="@nameof(ResultDTO.LastUpdate)" AutoFit="true" Format="d MMM, yyyy h:mm:ss"></GridColumn>
            </GridColumns>
        </SfGrid>
    }
</section>