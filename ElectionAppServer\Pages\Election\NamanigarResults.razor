﻿@page "/nnresults"

@inject NavigationManager NavigationManager
@inject UserManager<ApplicationUser> _UserManager
@inject RoleManager<IdentityRole> _RoleManager
@inject AuthenticationStateProvider AuthenticationStateProvider

@using FilterType = Syncfusion.Blazor.Grids.FilterType
@inherits OwningComponentBase<ElectionResultsDataService>


<SfToast @ref="ToastObj" Title="Alert" ShowProgressBar="true" Timeout="3000">
    <ToastPosition X="Right" Y="Bottom"></ToastPosition>
</SfToast>

<SfDialog ShowCloseIcon="true" @bind-Visible="IsDetailOpen" IsModal="true" width="900px">
    <DialogTemplates>
        <Header>Results</Header>
        <Content>

            <div class="card">
                <div class="card-header bg-primary">Constituency Info</div>
                <div class="card-body">
                    <div class="row mb-2">
                        <div class="col-md">
                            Last Update On:<b> @obj.LastActionDate.ToString("d MMM, yyyy h:mm:ss") (@obj.LastAction)</b>
                        </div>
                    </div>
                    <div class="row mb-2">
                        <div class="col-md">
                            Assembly: <b>@obj.Assembly</b>
                        </div>
                        <div class="col-md">
                            Constituency: <b>@obj.Constituency</b>
                        </div>
                        <div class="col-md">
                            Seat: <b>@obj.SeatType</b>
                        </div>
                    </div>
                    <div class="row mb-2">
                        <div class="col-md">
                            Male Voters: <b>@obj.MaleVoters</b>
                        </div>
                        <div class="col-md">
                            Female Voters: <b>@obj.FemaleVoters</b>
                        </div>
                        <div class="col-md">
                            Register Voters: <b>@(obj.MaleVoters + obj.FemaleVoters)</b>
                        </div>
                    </div>
                    <div class="row mb-2">
                        <div class="col-md">Total PS: <b>@obj.TotalPollingStations</b></div>
                        <div class="col-md">Namanigar PS: <b>@obj.NamangiarPollingStations</b></div>
                        <div class="col-md">Current PS: <b>@obj.ResultPollingStations</b></div>
                    </div>
                </div>
            </div>


            <div class="card">
                <div class="card-header bg-info">
                    Result Detail
                </div>
                <div class="card-body">
                    <SfGrid DataSource="obj.Details" AllowMultiSorting="true" AllowFiltering="true" Width="100%">
                        <GridFilterSettings Type="FilterType.Excel"></GridFilterSettings>
                        <GridColumns>
                            <GridColumn AutoFit="true" Field="@nameof(NamanigarResultDetailDTO.Candidate)" HeaderText="Candidate"></GridColumn>
                            <GridColumn AutoFit="true" Field="@nameof(NamanigarResultDetailDTO.Party)" HeaderText="Party"></GridColumn>
                            @*<GridColumn AutoFit="true" Field="@nameof(NamanigarResultDetailDTO.Symbol)" HeaderText="Symbol" ></GridColumn>*@
                            <GridColumn AutoFit="true" Field="@nameof(NamanigarResultDetailDTO.NamanigarVotes)" HeaderText="Namanigar Votes" TextAlign="TextAlign.Right"></GridColumn>
                            <GridColumn AutoFit="true" Field="@nameof(NamanigarResultDetailDTO.Votes)" HeaderText="Current Votes" TextAlign="TextAlign.Right"></GridColumn>
                        </GridColumns>
                    </SfGrid>
                    <SfButton class="e-primary mt-2 mb-2" OnClick="@(() => ApproveResult())">Approve</SfButton>
                </div>

            </div>
        </Content>
    </DialogTemplates>
</SfDialog>

<section style="padding: 15px 1rem; margin-top: -20px;">
    <div style="display:flex; justify-content: space-between; align-items: center">
        <div style="flex:1">
            @*<PageCaption Title="Namanigar Results"></PageCaption>*@
            <MudText Typo="Typo.h5">Namanigar Results</MudText>

        </div>
        <SfButton CssClass="e-info" OnClick="@(() => RefreshData())">Refresh </SfButton>
    </div>
    <table class="table-responsive-md table-bordered table-sm">
        <thead>
        <tr class="text-white bg-black">
            <th>BatchId</th>
            <th>Assembly</th>
            <th>Constituency</th>
            <th>Namanigar</th>
            <th>Action</th>
            <th>Action Date</th>
            <th>Current PS</th>
            <th>Namanigar PS</th>
            <th>Status</th>
            <th>Viewed</th>
            <th>&nbsp;</th>
        </tr>
        </thead>
        <tbody>
        @foreach (var nn in NamanigarResultsList)
        {
            var clr = "green";
            if (nn.Status == "Pending")
            {
                if (nn.IsViewed)
                {
                    clr = "viewresult";
                }
                else
                {
                    clr = "newresult";
                }
            }
            else
            {
                clr = "pubresult";
            }

            <tr class=@clr>
                <td>@nn.BatchId</td>
                <td>@nn.Assembly</td>
                <td>@nn.Constituency</td>
                <td>@nn.Namanigar</td>
                <td>@nn.Action</td>
                <td>@nn.ActionDate.ToString("d MMM, yyyy h:mm:ss")</td>
                <td>@nn.CurrentPS</td>
                <td>@nn.PS</td>
                <td>@nn.Status</td>
                <td>@(nn.IsViewed ? "Yes" : "No")</td>
                <td>
                    @if (nn.Status == "Pending")
                    {
                        <SfButton CssClass="e-primary" OnClick="@(() => OpenLog(nn.ConstituencyId, nn.SeatTypeId, state.state.PhaseId))">View</SfButton>
                    }
                </td>
            </tr>
        }
        </tbody>
    </table>
    @*<SfGrid DataSource="NamanigarResultsList" AllowFiltering="true" AllowTextWrap="true"  Width="100%" AllowSorting="true"  >
	<GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Excel"></GridFilterSettings>
	
	<GridColumns>
		<GridColumn AllowSorting="true" AutoFit="true" Field="@nameof(NamanigarResultsListViewDTO.BatchId)" HeaderText="Id"></GridColumn>
		<GridColumn AllowSorting="true" AutoFit="true" Field="@nameof(NamanigarResultsListViewDTO.Assembly)" HeaderText="Assembly"></GridColumn>
		<GridColumn AllowSorting="true" AutoFit="true" Field="@nameof(NamanigarResultsListViewDTO.Constituency)" HeaderText="Constituency"></GridColumn>
		<GridColumn AllowSorting="true" AutoFit="true" Field="@nameof(NamanigarResultsListViewDTO.Namanigar)" HeaderText="Namanigar"></GridColumn>
		<GridColumn AllowSorting="true" AutoFit="true" Field="@nameof(NamanigarResultsListViewDTO.Action)" HeaderText="Action"></GridColumn>
		<GridColumn AllowSorting="true" AutoFit="true" Field="@nameof(NamanigarResultsListViewDTO.ActionDate)" HeaderText="Date Time" Format="d MMM, yyyy h:mm:ss"></GridColumn>
		<GridColumn AllowSorting="true" AutoFit="true" Field="@nameof(NamanigarResultsListViewDTO.CurrentPS)" HeaderText="Current PS" ></GridColumn>
		<GridColumn AllowSorting="true"  AutoFit="true" Field="@nameof(NamanigarResultsListViewDTO.PS)" HeaderText="PS" ></GridColumn>
		<GridColumn AllowSorting="true"  AutoFit="true" Field="@nameof(NamanigarResultsListViewDTO.Status)" HeaderText="PS" ></GridColumn>
		<GridColumn HeaderText="Action" Width="10px">
			<Template Context="mm">
				@{ 
					var obj = mm as NamanigarResultsListViewDTO;
					if(obj.Status == "Pending"){
						<SfButton CssClass="e-primary" OnClick="@(()=>OpenLog(obj.ConstituencyId, obj.SeatTypeId, state.state.PhaseId))">View Log</SfButton>
					}
					
				}
			</Template>
		</GridColumn>
	</GridColumns>
</SfGrid>*@
</section>