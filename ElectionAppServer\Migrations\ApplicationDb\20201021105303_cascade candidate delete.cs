﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace ElectionAppServer.Migrations.ApplicationDb
{
	public partial class cascadecandidatedelete : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropForeignKey(
				 name: "FK_ResultLogDetails_Candidates_CandidateId",
				 table: "ResultLogDetails");

			migrationBuilder.AddForeignKey(
				 name: "FK_ResultLogDetails_Candidates_CandidateId",
				 table: "ResultLogDetails",
				 column: "CandidateId",
				 principalTable: "Candidates",
				 principalColumn: "Id",
				 onDelete: ReferentialAction.Cascade);
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropForeignKey(
				 name: "FK_ResultLogDetails_Candidates_CandidateId",
				 table: "ResultLogDetails");

			migrationBuilder.AddForeignKey(
				 name: "FK_ResultLogDetails_Candidates_CandidateId",
				 table: "ResultLogDetails",
				 column: "CandidateId",
				 principalTable: "Candidates",
				 principalColumn: "Id",
				 onDelete: ReferentialAction.Restrict);
		}
	}
}
