﻿@using ValueType = Syncfusion.Blazor.Charts.ValueType
@inherits OwningComponentBase<PartyService>

<style>
        body {
            user-select: none;
        }
    </style>

@{
    var ph = "Overall Party Position";
}
<div class="row">
    <div class="col-md-3">
        <SfAccumulationChart Title="@ph">
            <AccumulationChartSeriesCollection>
                <AccumulationChartSeries DataSource="@partyPositionData" XName="Party" YName="SeatsCount"
                                         Name="Browser">
                    <AccumulationDataLabelSettings Visible="true" Name="Text"></AccumulationDataLabelSettings>
                </AccumulationChartSeries>
            </AccumulationChartSeriesCollection>

            <AccumulationChartLegendSettings Visible="true"></AccumulationChartLegendSettings>
        </SfAccumulationChart>
    </div>
    <div class="col-md">
        <SfChart>
            <ChartPrimaryXAxis ValueType="ValueType.Category"></ChartPrimaryXAxis>

            <ChartSeriesCollection>
                <ChartSeries DataSource="@MedalDetails" Name="PPP" XName="Country" Width="2" Opacity="1" YName="PPP" Type="ChartSeriesType.Column"> </ChartSeries>
                <ChartSeries DataSource="@MedalDetails" Name="PTI" XName="Country" Width="2" Opacity="1" YName="PTI" Type="ChartSeriesType.Column"> </ChartSeries>
                <ChartSeries DataSource="@MedalDetails" Name="PMLN" XName="Country" Width="2" Opacity="1" YName="PMLN" Type="ChartSeriesType.Column"> </ChartSeries>
            </ChartSeriesCollection>
        </SfChart>
    </div>
</div>