﻿using System;

namespace ElectionAppServer.DTO;

public class ConstDetailDTO
{
    public string Code { get; set; }
    public string Name { get; set; }
    public string UrduName { get; set; }

    public int TotalVoters => (MaleVoters ?? 0) + (FemaleVoters ?? 0);

    public int? MaleVoters { get; set; }
    public int? FemaleVoters { get; set; }
    public int? YouthVoters { get; set; }
    public int? TotalPollingStations { get; set; }
    public string Assembly { get; set; }
    public string Election { get; set; }
    public string Phase { get; set; }
    public DateTime ElectionDate { get; set; }
    public string District { get; set; }
    public string Division { get; set; }
    public string Province { get; set; }
    public string Region { get; set; }
    public string Town { get; set; }
    public string UnionCouncil { get; set; }
    public int? Population { get; set; }
    public string AssemblyUrdu { get; set; }
    public string ElectionUrdu { get; set; }
    public int Id { get; set; }
    public string EnglishTitle { get; set; }
    public string UrduTitle { get; set; }
    public bool IsRePoll { get; set; }
    public bool BigContest { get; set; }
    public int? RejectedVotes { get; set; }
}