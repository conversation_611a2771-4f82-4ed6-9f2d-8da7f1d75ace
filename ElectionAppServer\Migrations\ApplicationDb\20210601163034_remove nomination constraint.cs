﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace ElectionAppServer.Migrations.ApplicationDb
{
	public partial class removenominationconstraint : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropIndex(
				 name: "IX_Nominations_ElectionAssemblyId_StructureId_SeatTypeId_CandidteId_ElectionPhaseId",
				 table: "Nominations");

			migrationBuilder.CreateIndex(
				 name: "IX_Nominations_ElectionAssemblyId",
				 table: "Nominations",
				 column: "ElectionAssemblyId");
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropIndex(
				 name: "IX_Nominations_ElectionAssemblyId",
				 table: "Nominations");

			migrationBuilder.CreateIndex(
				 name: "IX_Nominations_ElectionAssemblyId_StructureId_SeatTypeId_CandidteId_ElectionPhaseId",
				 table: "Nominations",
				 columns: new[] { "ElectionAssemblyId", "StructureId", "SeatTypeId", "CandidteId", "ElectionPhaseId" },
				 unique: true);
		}
	}
}
