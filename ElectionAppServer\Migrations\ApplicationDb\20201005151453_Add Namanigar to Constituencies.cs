﻿using Microsoft.EntityFrameworkCore.Migrations;
using System;

namespace ElectionAppServer.Migrations.ApplicationDb
{
	public partial class AddNamanigartoConstituencies : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.CreateTable(
				 name: "ConstituencyNamanigars",
				 columns: table => new
				 {
					 NamanigarId = table.Column<string>(nullable: false),
					 ConstituencyId = table.Column<int>(nullable: false),
					 PSDetail = table.Column<string>(unicode: false, maxLength: 500, nullable: false),
					 CreatedDate = table.Column<DateTime>(nullable: false, defaultValueSql: "getdate()"),
					 CreatedBy = table.Column<string>(unicode: false, maxLength: 450, nullable: false),
					 ModifiedDate = table.Column<DateTime>(nullable: true),
					 ModifiedBy = table.Column<string>(unicode: false, maxLength: 450, nullable: true)
				 },
				 constraints: table =>
				 {
					 table.PrimaryKey("PK_ConstituencyNamanigars", x => new { x.NamanigarId, x.ConstituencyId });
					 table.ForeignKey(
							  name: "FK_ConstituencyNamanigars_Structures_ConstituencyId",
							  column: x => x.ConstituencyId,
							  principalTable: "Structures",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Cascade);
					 table.ForeignKey(
							  name: "FK_ConstituencyNamanigars_AspNetUsers_NamanigarId",
							  column: x => x.NamanigarId,
							  principalTable: "AspNetUsers",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Cascade);
				 });

			migrationBuilder.CreateIndex(
				 name: "IX_ConstituencyNamanigars_ConstituencyId",
				 table: "ConstituencyNamanigars",
				 column: "ConstituencyId");
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropTable(
				 name: "ConstituencyNamanigars");
		}
	}
}
