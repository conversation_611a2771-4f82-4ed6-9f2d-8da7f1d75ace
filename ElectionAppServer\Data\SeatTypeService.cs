﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ElectionAppServer.DTO;
using ElectionAppServer.Model;
using Microsoft.EntityFrameworkCore;

namespace ElectionAppServer.Data;

public class SeatTypeService
{
    private readonly IDbContextFactory<ApplicationDbContext>
        _contextFactory; // private  readonly  ApplicationDbContext dc;

    public SeatTypeService(IDbContextFactory<ApplicationDbContext> contextFactory) // (ApplicationDbContext  context)
    {
        _contextFactory = contextFactory; // dc  =  context;
    }

    public Task<SeatType2DTO> GetById(int Id)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.SeatTypes
            where aa.Id == Id
            select new SeatType2DTO
            {
                Id = aa.Id,
                EnglishTitle = aa.EnglishTitle,
                UrduTitle = aa.UrduTitle
            }).FirstOrDefault();
        return Task.FromResult(q);
    }

    public Task<List<SeatType2DTO>> GetAll()
    {
        //var res = dc.SeatTypes.OrderBy(c => c.EnglishTitle).ToList();
        //return Task.FromResult(res);
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.SeatTypes
            orderby aa.EnglishTitle
            select new SeatType2DTO
            {
                Id = aa.Id,
                EnglishTitle = aa.EnglishTitle,
                UrduTitle = aa.UrduTitle
            }).ToList();
        return Task.FromResult(q);
    }

    public Task<SeatType2DTO> Save(SeatType2DTO obj, string user)
    {
        using var dc = _contextFactory.CreateDbContext();
        //dc.SeatTypes.Add(obj);
        //dc.SaveChanges();
        //return Task.FromResult(obj);
        obj.UrduTitle = obj.UrduTitle.Trim();
        obj.EnglishTitle = obj.EnglishTitle.Trim();
        var isExist = (from aa in dc.SeatTypes
            where aa.Id != obj.Id &&
                  aa.EnglishTitle == obj.EnglishTitle
            select aa).Any();
        if (isExist) throw new Exception($"Seat Type {obj.EnglishTitle} already exist");

        isExist = (from aa in dc.SeatTypes
            where aa.Id != obj.Id &&
                  aa.UrduTitle == obj.UrduTitle
            select aa).Any();

        if (isExist) throw new Exception($"Seat Type {obj.UrduTitle} already exist");

        if (obj.Id == 0)
        {
            var st = new SeatType
            {
                CreatedBy = user, CreatedDate = DateTime.Now, EnglishTitle = obj.EnglishTitle, UrduTitle = obj.UrduTitle
            };
            dc.SeatTypes.Add(st);
            dc.SaveChanges();
            obj.Id = st.Id;
            return Task.FromResult(obj);
        }
        else
        {
            var st = (from aa in dc.SeatTypes
                where aa.Id == obj.Id
                select aa).FirstOrDefault();
            if (st == null)
                throw new Exception("Record not found");
            st.EnglishTitle = obj.EnglishTitle;
            st.UrduTitle = obj.UrduTitle;
            st.ModifiedBy = user;
            st.ModifiedDate = DateTime.Now;
            dc.SaveChanges();
            return Task.FromResult(obj);
        }
    }

    //public Task<SeatType> UpdateSeatType(SeatType obj)
    //{
    //   var st = (from aa in dc.SeatTypes
    //             where aa.Id == obj.Id
    //             select aa).FirstOrDefault();
    //   st.UrduTitle = obj.UrduTitle.Trim();
    //   st.EnglishTitle = obj.EnglishTitle.Trim();
    //   st.ModifiedBy = obj.ModifiedBy;
    //   st.ModifiedDate = DateTime.Now;
    //   dc.SaveChanges();
    //   return Task.FromResult(obj);
    //}

    public Task<string> Delete(int Id)
    {
        using var dc = _contextFactory.CreateDbContext();
        var isNominationExist = (from aa in dc.Nominations
            where aa.SeatTypeId == Id
            select aa).Any();
        if (isNominationExist)
            throw new Exception("One or more nominations exist on this seat type");

        var st = (from aa in dc.SeatTypes
            where aa.Id == Id
            select aa).FirstOrDefault();

        dc.SeatTypes.Remove(st);
        dc.SaveChanges();
        return Task.FromResult("OK");
        //var st = await (from aa in dc.SeatTypes
        //                where aa.Id == Id
        //                select aa).FirstOrDefaultAsync();
        //dc.SeatTypes.Remove(st);
        //await dc.SaveChangesAsync();
        //return await Task.FromResult(st);
    }
}