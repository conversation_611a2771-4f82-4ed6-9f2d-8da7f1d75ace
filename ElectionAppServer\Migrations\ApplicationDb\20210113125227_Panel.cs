﻿using Microsoft.EntityFrameworkCore.Migrations;
using System;

namespace ElectionAppServer.Migrations.ApplicationDb
{
	public partial class Panel : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.CreateTable(
				 name: "PannelNominations",
				 columns: table => new
				 {
					 Id = table.Column<int>(type: "int", nullable: false)
							.Annotation("SqlServer:Identity", "1, 1"),
					 PhaseId = table.Column<int>(type: "int", nullable: false),
					 AssemblyId = table.Column<int>(type: "int", nullable: false),
					 PanelId = table.Column<int>(type: "int", nullable: false),
					 CandidateUrduName = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
					 CandidateEnglishName = table.Column<string>(type: "varchar(200)", unicode: false, maxLength: 200, nullable: false),
					 SeatTypeId = table.Column<int>(type: "int", nullable: false),
					 CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "getdate()"),
					 CreatedBy = table.Column<string>(type: "varchar(450)", unicode: false, maxLength: 450, nullable: false),
					 ModifiedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
					 ModifiedBy = table.Column<string>(type: "varchar(450)", unicode: false, maxLength: 450, nullable: true)
				 },
				 constraints: table =>
				 {
					 table.PrimaryKey("PK_PannelNominations", x => x.Id);
					 table.ForeignKey(
							  name: "FK_PannelNominations_Candidates_PanelId",
							  column: x => x.PanelId,
							  principalTable: "Candidates",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Restrict);
					 table.ForeignKey(
							  name: "FK_PannelNominations_ElectionAssemblies_AssemblyId",
							  column: x => x.AssemblyId,
							  principalTable: "ElectionAssemblies",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Restrict);
					 table.ForeignKey(
							  name: "FK_PannelNominations_ElectionPhases_PhaseId",
							  column: x => x.PhaseId,
							  principalTable: "ElectionPhases",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Restrict);
					 table.ForeignKey(
							  name: "FK_PannelNominations_SeatTypes_SeatTypeId",
							  column: x => x.SeatTypeId,
							  principalTable: "SeatTypes",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Cascade);
				 });

			migrationBuilder.CreateIndex(
				 name: "IX_PannelNominations_AssemblyId",
				 table: "PannelNominations",
				 column: "AssemblyId");

			migrationBuilder.CreateIndex(
				 name: "IX_PannelNominations_PanelId",
				 table: "PannelNominations",
				 column: "PanelId");

			migrationBuilder.CreateIndex(
				 name: "IX_PannelNominations_PhaseId",
				 table: "PannelNominations",
				 column: "PhaseId");

			migrationBuilder.CreateIndex(
				 name: "IX_PannelNominations_SeatTypeId",
				 table: "PannelNominations",
				 column: "SeatTypeId");
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropTable(
				 name: "PannelNominations");
		}
	}
}
