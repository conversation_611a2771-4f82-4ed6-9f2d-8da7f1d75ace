﻿@page "/import"
@attribute [Authorize(Roles = "Administrators")]
@using FilterType = Syncfusion.Blazor.DropDowns.FilterType
@inherits OwningComponentBase<ElectionService>

<MudText Typo="Typo.h5">Import Election</MudText>

<section>
    <div class="row">
        <div class="col">
            <SfDropDownList @bind-Value="parentElectionId" TValue="int?" TItem="GeneralItemDTO"
                            PopupHeight="350px" Placeholder="Select Parent Election"
                            AllowFiltering="true" FilterType="FilterType.Contains"
                            DataSource="@parentElections" FloatLabelType="FloatLabelType.Always">
                <DropDownListFieldSettings Value="Id" Text="EnglishTitle"></DropDownListFieldSettings>
            </SfDropDownList>
        </div>
        <div class="col">
            <SfDropDownList @bind-Value="childElectionId" TValue="int?" TItem="GeneralItemDTO"
                            PopupHeight="350px" Placeholder="Select Child Election"
                            DataSource="@childElections" FloatLabelType="FloatLabelType.Always"
                            AllowFiltering="true" FilterType="FilterType.Contains">
                <DropDownListFieldSettings Value="Id" Text="EnglishTitle"></DropDownListFieldSettings>
            </SfDropDownList>
        </div>
    </div>
    <div class="row">
        <div class="col">
            @if (IsImporting)
            {
                <p>Please wait... Importing election data</p>
            }
            else
            {
                <SfButton OnClick="ImportElectionData" CssClass="e-primary">Import</SfButton>
                <SfButton OnClick="ImportStructureOnly" CssClass="e-primary">Import Structure Only</SfButton>
            }

            @if (!string.IsNullOrEmpty(ErrorMessage))
            {
                <h4>@ErrorMessage</h4>
            }
        </div>
    </div>
</section>

@code {

    [CascadingParameter] private Task<AuthenticationState> authenticationStateTask { get; set; }

    public int? parentElectionId { get; set; }
    public int? childElectionId { get; set; }
    private List<GeneralItemDTO> childElections { get; set; }
    private List<GeneralItemDTO> parentElections { get; set; }
    public bool IsImporting { get; set; }
    private string ErrorMessage { get; set; }

    protected override async Task OnInitializedAsync()
    {
        IsImporting = false;
        parentElections = await Service.GetParentElections();
        childElections = await Service.GetChildElections();
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            var user = (await authenticationStateTask).User;
            if (user.Identity.IsAuthenticated)
            {
                var dd = await localStorage.GetAsync<ElectionStateDTO>(await confreader.GetSettingValue("electionkey"));
                state.SetState(dd.Value);
            }
            else
            {
                try
                {
                    await localStorage.DeleteAsync(await confreader.GetSettingValue("electionkey"));
                    state.SetState(new ElectionStateDTO { ElectionId = 0, ElectionTitle = "", PhaseId = 0, PhaseTitle = "" });
                }
                catch (Exception)
                {
                    // ignored
                }
            }

            StateHasChanged();
        }
    }

    private async Task ImportStructureOnly()
    {
        ErrorMessage = "";
        if (parentElectionId == null || childElectionId == null)
        {
            ErrorMessage = "Please select Parent and Child Elections";
        }
        else
        {
            IsImporting = true;
            var frmElection = parentElections.FirstOrDefault(c => c.Id == (int)parentElectionId).EnglishTitle;
            var toElection = childElections.FirstOrDefault(c => c.Id == (int)childElectionId).EnglishTitle;
            var msg = $"Are you sure want to import election data from {frmElection} to {toElection}";
            var confirm = await JSRuntime.InvokeAsync<bool>("confirm", new[] { msg });
            if (confirm)
            {
                StateHasChanged();
                var user = (await authenticationStateTask).User;

                var res = await Service.CopyElectionStructureOnly((int)parentElectionId, (int)childElectionId, user.Identity.Name);
                if (res != "OK")
                {
                    ErrorMessage = res;
                    IsImporting = false;
                    StateHasChanged();
                }
                else
                {
                    parentElections = await Service.GetParentElections();
                    childElections = await Service.GetChildElections();

                    if (parentElections.Any())
                        parentElectionId = parentElections[0].Id;

                    if (childElections.Any())
                        childElectionId = childElections[0].Id;

                    await JSRuntime.InvokeVoidAsync("alert", "Election import process has completed successfully");
                    IsImporting = false;
                    parentElectionId = null;
                    childElectionId = null;

                    StateHasChanged();
                }
            }
            else
            {
                IsImporting = false;
                StateHasChanged();
            }
        }
    }

    private async Task ImportElectionData()
    {
        ErrorMessage = "";
        if (parentElectionId == null || childElectionId == null)
        {
            ErrorMessage = "Please select Parent and Child Elections";
        }
        else
        {
            IsImporting = true;
            var frmElection = parentElections.FirstOrDefault(c => c.Id == (int)parentElectionId).EnglishTitle;
            var toElection = childElections.FirstOrDefault(c => c.Id == (int)childElectionId).EnglishTitle;
            var msg = $"Are you sure want to import election data from {frmElection} to {toElection}";
            var confirm = await JSRuntime.InvokeAsync<bool>("confirm", new[] { msg });
            if (confirm)
            {
                StateHasChanged();
                var user = (await authenticationStateTask).User;

                var res = await Service.CopyElection((int)parentElectionId, (int)childElectionId, user.Identity.Name);
                if (res != "OK")
                {
                    ErrorMessage = res;
                    IsImporting = false;
                    StateHasChanged();
                }
                else
                {
                    parentElections = await Service.GetParentElections();
                    childElections = await Service.GetChildElections();

                    if (parentElections.Any())
                        parentElectionId = parentElections[0].Id;

                    if (childElections.Any())
                        childElectionId = childElections[0].Id;

                    await JSRuntime.InvokeVoidAsync("alert", "Election import process has completed successfully");
                    IsImporting = false;
                    parentElectionId = null;
                    childElectionId = null;

                    StateHasChanged();
                }
            }
            else
            {
                IsImporting = false;
                StateHasChanged();
            }
        }
    }

}