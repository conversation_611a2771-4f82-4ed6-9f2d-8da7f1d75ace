﻿using System;
using System.Collections.Generic;
using ElectionAppServer.Model;

namespace ElectionAppServer.DTO;

public class LiveResultDetailDTO
{
    public string Code { get; set; }
    public string EnglishTitle { get; set; }
    public string UrduTitle { get; set; }
    public int RegisterVoters { get; set; }
    public int TotalPollingStations { get; set; }
    public int ResultPollingStations { get; set; }
    public string Ticker { get; set; }
    public List<LiveResultCandidateDetailDTO> Nominations { get; set; }
    public string PSNumber { get; set; }
    public string PSEnglish { get; set; }
    public string PSUrdu { get; set; }
    public int PSRegisterVoters { get; set; }
    public string AssemblyType { get; internal set; }
    public string Assembly { get; internal set; }
}

public class LiveResultCandidateDetailDTO
{
    public string Party { get; set; }
    public string Candidate { get; set; }
    public int Votes { get; set; }
    public int VotesOA { get; set; }
    public string PartyUrdu { get; set; }
    public string CandidateUrdu { get; set; }
    public Gender Gender { get; set; }
    public DateTime? LastUpdate { get; internal set; }
    public string SeatType { get; internal set; }
    public string Ka<PERSON>ee { get; internal set; }
}