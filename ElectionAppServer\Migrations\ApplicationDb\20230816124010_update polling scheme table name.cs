﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace ElectionAppServer.Migrations.ApplicationDb
{
    /// <inheritdoc />
    public partial class updatepollingschemetablename : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_PollingScheme_ElectionPhases_PhaseId",
                table: "PollingScheme");

            migrationBuilder.DropForeignKey(
                name: "FK_PollingScheme_Structures_StructureId",
                table: "PollingScheme");

            migrationBuilder.DropPrimaryKey(
                name: "PK_PollingScheme",
                table: "PollingScheme");

            migrationBuilder.RenameTable(
                name: "PollingScheme",
                newName: "PollingSchemes");

            migrationBuilder.RenameIndex(
                name: "IX_PollingScheme_PhaseId",
                table: "PollingSchemes",
                newName: "IX_PollingSchemes_PhaseId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_PollingSchemes",
                table: "PollingSchemes",
                columns: new[] { "StructureId", "PhaseId" });

            migrationBuilder.AddForeignKey(
                name: "FK_PollingSchemes_ElectionPhases_PhaseId",
                table: "PollingSchemes",
                column: "PhaseId",
                principalTable: "ElectionPhases",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_PollingSchemes_Structures_StructureId",
                table: "PollingSchemes",
                column: "StructureId",
                principalTable: "Structures",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_PollingSchemes_ElectionPhases_PhaseId",
                table: "PollingSchemes");

            migrationBuilder.DropForeignKey(
                name: "FK_PollingSchemes_Structures_StructureId",
                table: "PollingSchemes");

            migrationBuilder.DropPrimaryKey(
                name: "PK_PollingSchemes",
                table: "PollingSchemes");

            migrationBuilder.RenameTable(
                name: "PollingSchemes",
                newName: "PollingScheme");

            migrationBuilder.RenameIndex(
                name: "IX_PollingSchemes_PhaseId",
                table: "PollingScheme",
                newName: "IX_PollingScheme_PhaseId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_PollingScheme",
                table: "PollingScheme",
                columns: new[] { "StructureId", "PhaseId" });

            migrationBuilder.AddForeignKey(
                name: "FK_PollingScheme_ElectionPhases_PhaseId",
                table: "PollingScheme",
                column: "PhaseId",
                principalTable: "ElectionPhases",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_PollingScheme_Structures_StructureId",
                table: "PollingScheme",
                column: "StructureId",
                principalTable: "Structures",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }
    }
}
