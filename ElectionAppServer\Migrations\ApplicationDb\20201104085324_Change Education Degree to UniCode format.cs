﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace ElectionAppServer.Migrations.ApplicationDb
{
	public partial class ChangeEducationDegreetoUniCodeformat : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropColumn(
				 name: "IsPostponed",
				 table: "Nominations");

			migrationBuilder.AlterColumn<string>(
				 name: "EducationDegree",
				 table: "Candidates",
				 type: "nvarchar(200)",
				 maxLength: 200,
				 nullable: true,
				 oldClrType: typeof(string),
				 oldType: "varchar(200)",
				 oldUnicode: false,
				 oldMaxLength: 200,
				 oldNullable: true);
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.AddColumn<bool>(
				 name: "IsPostponed",
				 table: "Nominations",
				 type: "bit",
				 nullable: false,
				 defaultValue: false);

			migrationBuilder.AlterColumn<string>(
				 name: "EducationDeg<PERSON>",
				 table: "Candidates",
				 type: "varchar(200)",
				 unicode: false,
				 maxLength: 200,
				 nullable: true,
				 oldClrType: typeof(string),
				 oldType: "nvarchar(200)",
				 oldMaxLength: 200,
				 oldNullable: true);
		}
	}
}
