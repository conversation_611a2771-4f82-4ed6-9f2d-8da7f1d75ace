﻿@inject UserManager<ApplicationUser> _UserManager
@inject RoleManager<IdentityRole> _RoleManager
<AuthorizeView>
    <Authorized>
        <MudNavMenu>
            @if (!string.IsNullOrEmpty(state.state.ElectionTitle))
            {
                var link1 = $"administrativeunits/{state.state.ElectionId}";
                var link2 = $"assemblies/{state.state.ElectionId}";

                if (roles != null && roles.Contains("Bureau"))
                {
                    <MudNavLink Href="/mgmlive" Match="NavLinkMatch.Prefix">Bureau Live Results</MudNavLink>
                    @* <li class="nav-item">
                            <a href="/mgmlive" class="nav-link">
                                <span class="oi oi-circle-check" aria-hidden="true"></span>
                                <p>Bureau Live Results</p>
                            </a>
                        </li> *@
                }

                if (roles != null && roles.Contains("Data Entry Operators"))
                {
                    <MudNavLink Icon="@Icons.Material.Filled.CheckCircle" Href="/results" Match="NavLinkMatch.Prefix">Election Results</MudNavLink>
                    <MudNavLink Icon="@Icons.Material.Filled.CheckCircle" Href="/resultsps" Match="NavLinkMatch.Prefix">Election Results (PS)</MudNavLink>
                }

                if (roles != null && (roles.Contains("Producer") || roles.Contains("IMM")))
                {
                    <MudNavLink Icon="@Icons.Material.Filled.MultilineChart" Href="/pr" Match="NavLinkMatch.Prefix">Live Results</MudNavLink>
                    <MudNavLink Icon="@Icons.Material.Filled.MultilineChart" Href="/pspr" Match="NavLinkMatch.Prefix">Live Results (PS)</MudNavLink>
                }

                if (roles != null && roles.Contains("Administrators"))
                {
                    <MudNavGroup Icon="@Icons.Material.Filled.Settings" Title="Setup" Expanded="false">

                        <MudNavLink Icon="@Icons.Material.Filled.ZoomInMap" Href="@link1" Match="NavLinkMatch.Prefix">Administrative Units</MudNavLink>
                        <MudNavLink Icon="@Icons.Material.Filled.Apartment" Href="@link2" Match="NavLinkMatch.Prefix">Assemblies</MudNavLink>
                        <MudNavLink Icon="@Icons.Material.Filled.Layers" Href="/regions" Match="NavLinkMatch.Prefix">Regions</MudNavLink>
                        <MudNavLink Icon="@Icons.Material.Filled.Layers" Href="/divisions" Match="NavLinkMatch.Prefix">Divisions</MudNavLink>
                        <MudNavLink Icon="@Icons.Material.Filled.LocationOn" Href="/search" Match="NavLinkMatch.Prefix">Constituencies</MudNavLink>
                        <MudNavLink Icon="@Icons.Material.Filled.PersonPin" Href="/nominations2" Match="NavLinkMatch.Prefix">Nominations</MudNavLink>
                        <MudNavLink Icon="@Icons.Material.Filled.Flag" Href="/parties" Match="NavLinkMatch.Prefix">Parties</MudNavLink>
                        <MudNavLink Icon="@Icons.Material.Filled.FlagCircle" Href="/teams" Match="NavLinkMatch.Prefix">Teams</MudNavLink>
                        <MudNavLink Icon="@Icons.Material.Filled.PersonPinCircle" Href="/candidates" Match="NavLinkMatch.Prefix">Candidates</MudNavLink>
                        <MudNavLink Icon="@Icons.Material.Filled.AdminPanelSettings" Href="/panels" Match="NavLinkMatch.Prefix">Panels</MudNavLink>
                        <MudNavLink Href="/namanigars" Match="NavLinkMatch.Prefix" Icon="@Icons.Material.Filled.ContactPhone">Namanigars</MudNavLink>
                        <MudNavLink Href="/seattypes" Match="NavLinkMatch.Prefix" Icon="@Icons.Material.Filled.EventSeat">Seat Types</MudNavLink>
                        <MudNavLink Href="/assemblytypes" Match="NavLinkMatch.Prefix" Icon="@Icons.Material.Filled.Domain">Assembly Types</MudNavLink>
                        <MudNavLink Href="/symbols" Match="NavLinkMatch.Prefix" Icon="@Icons.Material.Filled.Star">Symbols</MudNavLink>
                        <MudNavLink Href="/castes" Match="NavLinkMatch.Prefix" Icon="@Icons.Material.Filled.Diversity2">Castes</MudNavLink>
                        <MudNavLink Href="/languages" Match="NavLinkMatch.Prefix" Icon="@Icons.Material.Filled.Language">Languages</MudNavLink>
                        <MudNavLink Href="/professions" Match="NavLinkMatch.Prefix" Icon="@Icons.Material.Filled.Engineering">Professions</MudNavLink>
                        <MudNavLink Href="/educations" Match="NavLinkMatch.Prefix" Icon="@Icons.Material.Filled.School">Educations</MudNavLink>
                    </MudNavGroup>
                }

                if (roles != null && roles.Contains("News User"))
                {
                    <MudNavLink Icon="@Icons.Material.Filled.MultilineChart" Href="/otherresults" Match="NavLinkMatch.Prefix">Results from Other Sources</MudNavLink>
                }

                if (roles != null && roles.Contains("Data Viewer"))
                {
                    <MudNavLink Href="@link1" Match="NavLinkMatch.Prefix" Icon="@Icons.Material.Filled.ZoomInMap">Administrative Units</MudNavLink>
                    <MudNavLink Href="@link2" Match="NavLinkMatch.Prefix" Icon="@Icons.Material.Filled.Apartment">Assemblies</MudNavLink>
                    <MudNavLink Href="/regions" Match="NavLinkMatch.Prefix" Icon="@Icons.Material.Filled.Layers">Regions</MudNavLink>
                    <MudNavLink Href="/search" Match="NavLinkMatch.Prefix" Icon="@Icons.Material.Filled.LocationOn">Constituencies</MudNavLink>
                    <MudNavLink Href="/nominations2" Match="NavLinkMatch.Prefix" Icon="@Icons.Material.Filled.PersonPin">Nominations</MudNavLink>
                    <MudNavLink Href="/parties" Match="NavLinkMatch.Prefix" Icon="@Icons.Material.Filled.Flag">Parties</MudNavLink>
                    <MudNavLink Href="/candidates" Match="NavLinkMatch.Prefix" Icon="@Icons.Material.Filled.PersonPinCircle">Candidates</MudNavLink>
                }

                if (roles != null && roles.Contains("Ticker Desk"))
                {
                    <MudNavLink Href="/ticker" Match="NavLinkMatch.Prefix" Icon="@Icons.Material.Outlined.Newspaper">Live Tickers</MudNavLink>
                    <MudNavLink Href="/filterticker" Match="NavLinkMatch.Prefix" Icon="@Icons.Material.Filled.Newspaper">New Live Tickers</MudNavLink>
                }

                if (roles != null && roles.Contains("Anchors"))
                {
                    <MudNavLink Href="/livetickers" Match="NavLinkMatch.Prefix" Icon="@Icons.Material.Filled.Newspaper">Live Tickers (Anchors)</MudNavLink>
                }

                if (roles != null && roles.Contains("Report User"))
                {
                    <MudNavGroup Icon="@Icons.Material.Filled.Report" Title="Reports" Expanded="false">
                        <MudNavLink Icon="@Icons.Material.Filled.Numbers" Href="/reports/nominationcount" Match="NavLinkMatch.Prefix">Nomination Count</MudNavLink>
                        <MudNavLink Href="/nnresults" Match="NavLinkMatch.Prefix" Icon="@Icons.Material.Filled.Checklist">Namanigar Results</MudNavLink>
                        <MudNavLink Href="/resultslog" Match="NavLinkMatch.Prefix" Icon="@Icons.Material.Filled.WorkHistory">Results Logs</MudNavLink>
                        <MudNavLink Href="/olres" Match="NavLinkMatch.Prefix" Icon="@Icons.Material.Filled.WorkHistory">Other Sources Results</MudNavLink>
                    </MudNavGroup>
                }

                if (roles != null && roles.Contains("Administrators"))
                {
                    <MudNavGroup Icon="@Icons.Material.Filled.Security" Title="Security">
                        <MudNavLink Href="/users" Match="NavLinkMatch.Prefix" Icon="@Icons.Material.Filled.Group">Users Acwwcounts</MudNavLink>
                    </MudNavGroup>
                }
            }
            else
            {
                <MudNavLink Href="/" Match="NavLinkMatch.Prefix" Icon="@Icons.Material.Filled.FactCheck">Select Election</MudNavLink>
            }

            @* <MudNavLink Href="/" Match="NavLinkMatch.All">Dashboard</MudNavLink> *@
            @* <MudNavLink Href="/servers" Match="NavLinkMatch.Prefix">Servers</MudNavLink> *@
            @* <MudNavGroup Title="Settings" Expanded="true"> *@
            @* <MudNavLink Href="/users" Match="NavLinkMatch.Prefix">Users</MudNavLink> *@
            @* <MudNavLink Href="/security" Match="NavLinkMatch.Prefix">Security</MudNavLink> *@
            @* </MudNavGroup> *@
            @* <MudNavLink Href="/about" Match="NavLinkMatch.Prefix">About</MudNavLink> *@
        </MudNavMenu>
    </Authorized>
    <NotAuthorized>

        <MudNavMenu>
            <MudNavLink Href="/Identity/Account/Login" Match="NavLinkMatch.All">Login</MudNavLink>
        </MudNavMenu>
    </NotAuthorized>
</AuthorizeView>

@code {

    //protected override void OnInitialized()
    [CascadingParameter] private Task<AuthenticationState> authenticationStateTask { get; set; }
    private List<string> roles { get; set; }

    protected override async Task OnInitializedAsync()
    {
        state.OnChange += StateHasChanged;

        try
        {
            var CurrentUser = (await authenticationStateTask).User;
            // Resolve the user via their email
            var user = await _UserManager.FindByNameAsync(CurrentUser.Identity.Name);
            // Get the roles for the user
            var _roles = await _UserManager.GetRolesAsync(user);
            roles = new List<string>();
            foreach (var r in _roles)
            {
                roles.Add(r);
            }
        }
        catch (Exception)
        {
            // ignored
        }
    }

}