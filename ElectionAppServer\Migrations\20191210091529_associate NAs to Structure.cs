﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace ElectionAppServer.Migrations
{
	public partial class associateNAstoStructure : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropForeignKey(
				 name: "FK_Structures_Structures_Town_DistrictId",
				 table: "Structures");

			migrationBuilder.DropIndex(
				 name: "IX_Structures_Town_DistrictId",
				 table: "Structures");

			migrationBuilder.DropColumn(
				 name: "Town_DistrictId",
				 table: "Structures");

			migrationBuilder.AddColumn<int>(
				 name: "ProvincialAssemblyHalka_StructureId",
				 table: "Structures",
				 nullable: true);

			migrationBuilder.CreateIndex(
				 name: "IX_Structures_ProvincialAssemblyHalka_StructureId",
				 table: "Structures",
				 column: "ProvincialAssemblyHalka_StructureId");

			migrationBuilder.AddForeignKey(
				 name: "FK_Structures_Structures_ProvincialAssemblyHalka_StructureId",
				 table: "Structures",
				 column: "ProvincialAssemblyHalka_StructureId",
				 principalTable: "Structures",
				 principalColumn: "Id",
				 onDelete: ReferentialAction.Restrict);
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropForeignKey(
				 name: "FK_Structures_Structures_ProvincialAssemblyHalka_StructureId",
				 table: "Structures");

			migrationBuilder.DropIndex(
				 name: "IX_Structures_ProvincialAssemblyHalka_StructureId",
				 table: "Structures");

			migrationBuilder.DropColumn(
				 name: "ProvincialAssemblyHalka_StructureId",
				 table: "Structures");

			migrationBuilder.AddColumn<int>(
				 name: "Town_DistrictId",
				 table: "Structures",
				 type: "int",
				 nullable: true);

			migrationBuilder.CreateIndex(
				 name: "IX_Structures_Town_DistrictId",
				 table: "Structures",
				 column: "Town_DistrictId");

			migrationBuilder.AddForeignKey(
				 name: "FK_Structures_Structures_Town_DistrictId",
				 table: "Structures",
				 column: "Town_DistrictId",
				 principalTable: "Structures",
				 principalColumn: "Id",
				 onDelete: ReferentialAction.Restrict);
		}
	}
}
