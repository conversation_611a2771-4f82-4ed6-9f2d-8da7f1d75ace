﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace ElectionAppServer.Migrations.ApplicationDb
{
	public partial class Resultslog4 : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropForeignKey(
				 name: "FK_ResultLogs_AspNetUsers_CreatedByUserId",
				 table: "ResultLogs");

			migrationBuilder.DropIndex(
				 name: "IX_ResultLogs_CreatedByUserId",
				 table: "ResultLogs");

			migrationBuilder.AlterColumn<string>(
				 name: "CreatedByUserId",
				 table: "ResultLogs",
				 unicode: false,
				 maxLength: 450,
				 nullable: false,
				 oldClrType: typeof(string),
				 oldType: "nvarchar(450)",
				 oldNullable: true);

			migrationBuilder.AddColumn<string>(
				 name: "ApplicationUserId1",
				 table: "ResultLogs",
				 nullable: true);

			migrationBuilder.CreateIndex(
				 name: "IX_ResultLogs_ApplicationUserId1",
				 table: "ResultLogs",
				 column: "ApplicationUserId1");

			migrationBuilder.AddForeignKey(
				 name: "FK_ResultLogs_AspNetUsers_ApplicationUserId1",
				 table: "ResultLogs",
				 column: "ApplicationUserId1",
				 principalTable: "AspNetUsers",
				 principalColumn: "Id",
				 onDelete: ReferentialAction.Restrict);
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropForeignKey(
				 name: "FK_ResultLogs_AspNetUsers_ApplicationUserId1",
				 table: "ResultLogs");

			migrationBuilder.DropIndex(
				 name: "IX_ResultLogs_ApplicationUserId1",
				 table: "ResultLogs");

			migrationBuilder.DropColumn(
				 name: "ApplicationUserId1",
				 table: "ResultLogs");

			migrationBuilder.AlterColumn<string>(
				 name: "CreatedByUserId",
				 table: "ResultLogs",
				 type: "nvarchar(450)",
				 nullable: true,
				 oldClrType: typeof(string),
				 oldUnicode: false,
				 oldMaxLength: 450);

			migrationBuilder.CreateIndex(
				 name: "IX_ResultLogs_CreatedByUserId",
				 table: "ResultLogs",
				 column: "CreatedByUserId");

			migrationBuilder.AddForeignKey(
				 name: "FK_ResultLogs_AspNetUsers_CreatedByUserId",
				 table: "ResultLogs",
				 column: "CreatedByUserId",
				 principalTable: "AspNetUsers",
				 principalColumn: "Id",
				 onDelete: ReferentialAction.Restrict);
		}
	}
}
