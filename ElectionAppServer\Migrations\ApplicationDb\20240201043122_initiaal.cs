﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace ElectionAppServer.Migrations.ApplicationDb
{
    /// <inheritdoc />
    public partial class initiaal : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "ResultMode",
                table: "PollingSchemes",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<string>(
                name: "ResultModeChangedBy",
                table: "PollingSchemes",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "ResultModeChangedDate",
                table: "PollingSchemes",
                type: "datetime2",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ResultMode",
                table: "PollingSchemes");

            migrationBuilder.DropColumn(
                name: "ResultModeChangedBy",
                table: "PollingSchemes");

            migrationBuilder.DropColumn(
                name: "ResultModeChangedDate",
                table: "PollingSchemes");
        }
    }
}
