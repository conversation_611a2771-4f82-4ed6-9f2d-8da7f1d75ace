﻿using Microsoft.EntityFrameworkCore.Migrations;
using System;

namespace ElectionAppServer.Migrations.ApplicationDb
{
	public partial class addelectionidconstraintforpreventingduplicatcodeandnameinuc : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropIndex(
				 name: "IX_Structures_Code_TownId",
				 table: "Structures");

			migrationBuilder.DropIndex(
				 name: "IX_Structures_EnglishTitle_TownId",
				 table: "Structures");

			migrationBuilder.DropIndex(
				 name: "IX_Structures_UrduTitle_TownId",
				 table: "Structures");

			migrationBuilder.AlterColumn<DateTime>(
				 name: "CreatedDate",
				 table: "ElectionParties",
				 nullable: false,
				 defaultValue: new DateTime(2020, 9, 2, 16, 45, 57, 510, DateTimeKind.Local).AddTicks(8880),
				 oldClrType: typeof(DateTime),
				 oldType: "datetime2",
				 oldDefaultValue: new DateTime(2020, 8, 21, 16, 41, 7, 168, DateTimeKind.Local).AddTicks(2296));

			migrationBuilder.AlterColumn<DateTime>(
				 name: "CreatedDate",
				 table: "ElectionCandidates",
				 nullable: false,
				 defaultValue: new DateTime(2020, 9, 2, 16, 45, 57, 516, DateTimeKind.Local).AddTicks(1274),
				 oldClrType: typeof(DateTime),
				 oldType: "datetime2",
				 oldDefaultValue: new DateTime(2020, 8, 21, 16, 41, 7, 174, DateTimeKind.Local).AddTicks(9485));

			migrationBuilder.CreateIndex(
				 name: "IX_Structures_ElectionId_Code_TownId",
				 table: "Structures",
				 columns: new[] { "ElectionId", "Code", "TownId" },
				 unique: true,
				 filter: "[Code] IS NOT NULL AND [TownId] IS NOT NULL");

			migrationBuilder.CreateIndex(
				 name: "IX_Structures_ElectionId_EnglishTitle_TownId",
				 table: "Structures",
				 columns: new[] { "ElectionId", "EnglishTitle", "TownId" },
				 unique: true,
				 filter: "[EnglishTitle] IS NOT NULL AND [TownId] IS NOT NULL");

			migrationBuilder.CreateIndex(
				 name: "IX_Structures_ElectionId_UrduTitle_TownId",
				 table: "Structures",
				 columns: new[] { "ElectionId", "UrduTitle", "TownId" },
				 unique: true,
				 filter: "[TownId] IS NOT NULL");
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropIndex(
				 name: "IX_Structures_ElectionId_Code_TownId",
				 table: "Structures");

			migrationBuilder.DropIndex(
				 name: "IX_Structures_ElectionId_EnglishTitle_TownId",
				 table: "Structures");

			migrationBuilder.DropIndex(
				 name: "IX_Structures_ElectionId_UrduTitle_TownId",
				 table: "Structures");

			migrationBuilder.AlterColumn<DateTime>(
				 name: "CreatedDate",
				 table: "ElectionParties",
				 type: "datetime2",
				 nullable: false,
				 defaultValue: new DateTime(2020, 8, 21, 16, 41, 7, 168, DateTimeKind.Local).AddTicks(2296),
				 oldClrType: typeof(DateTime),
				 oldDefaultValue: new DateTime(2020, 9, 2, 16, 45, 57, 510, DateTimeKind.Local).AddTicks(8880));

			migrationBuilder.AlterColumn<DateTime>(
				 name: "CreatedDate",
				 table: "ElectionCandidates",
				 type: "datetime2",
				 nullable: false,
				 defaultValue: new DateTime(2020, 8, 21, 16, 41, 7, 174, DateTimeKind.Local).AddTicks(9485),
				 oldClrType: typeof(DateTime),
				 oldDefaultValue: new DateTime(2020, 9, 2, 16, 45, 57, 516, DateTimeKind.Local).AddTicks(1274));

			migrationBuilder.CreateIndex(
				 name: "IX_Structures_Code_TownId",
				 table: "Structures",
				 columns: new[] { "Code", "TownId" },
				 unique: true,
				 filter: "[Code] IS NOT NULL AND [TownId] IS NOT NULL");

			migrationBuilder.CreateIndex(
				 name: "IX_Structures_EnglishTitle_TownId",
				 table: "Structures",
				 columns: new[] { "EnglishTitle", "TownId" },
				 unique: true,
				 filter: "[EnglishTitle] IS NOT NULL AND [TownId] IS NOT NULL");

			migrationBuilder.CreateIndex(
				 name: "IX_Structures_UrduTitle_TownId",
				 table: "Structures",
				 columns: new[] { "UrduTitle", "TownId" },
				 unique: true,
				 filter: "[TownId] IS NOT NULL");
		}
	}
}
