﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace ElectionAppServer.Model;

public class ElectionAssembly
{
    public int Id { get; set; }
    public int ElectionId { get; set; }
    public virtual Election Election { get; set; }
    public int AssemblyTypeId { get; set; }
    public virtual AssemblyType AssemblyType { get; set; }
    public string EnglishTitle { get; set; }
    public string UrduTitle { get; set; }
    public ElectionAssemblyType? ElectionAssemblyType { get; set; }
    public virtual List<Nomination> Nominations { get; set; }
    public virtual List<UserConstituency> NamnigarConstituencies { get; set; }
    public int? ReserveSeats { get; set; }
    public int? WomenSeats { get; set; }
    public virtual List<Structure> Constituencies { get; set; }
    public virtual List<ResultLog> ResultLogs { get; set; }
    public NominationType NominationType { get; set; } = NominationType.Candidate;
    public virtual List<PanelNomination> PannelNominations { get; set; }
    //public SeatConfigurationType SeatConfigurationType { get; set; } = SeatConfigurationType.SingleSeat;
    #region Audit Log Fields
    public DateTime CreatedDate { get; set; } = DateTime.Now;
    public DateTime? ModifiedDate { get; set; }
    [StringLength(450)] public string CreatedBy { get; set; }
    [StringLength(450)] public string ModifiedBy { get; set; }
    #endregion Audit Log Fields
}

public enum ElectionAssemblyType
{
    UnionCouncil = 5,
    Ward = 6,
    National = 7,
    Provincial = 8
}

public enum SeatConfigurationType
{
    // One Constituency One Winner
    SingleSeat = 1,
    // One Constituency
    // Multiple Nomination on different Seat Types
    MultiSeat = 2,
    // One Type of Nomination
    // Multiple Winner on Seat Configuration
    MultiWinner = 3
}