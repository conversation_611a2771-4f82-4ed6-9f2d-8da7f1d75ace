﻿@inject NavigationManager NavigationManager
@inherits OwningComponentBase<DistrictService>

<SfAutoComplete TItem="GeneralItemDTO" TValue="string" Placeholder="Search Structure: e.g. <PERSON><PERSON><PERSON> (District)" DataSource="@Structures" PopupHeight="400px">
    <AutoCompleteEvents TValue="string" ValueChange="onChange" TItem="GeneralItemDTO"></AutoCompleteEvents>
    <AutoCompleteFieldSettings Value="EnglishTitle"></AutoCompleteFieldSettings>
</SfAutoComplete>

@code {

    private List<GeneralItemDTO> Structures;
    [Parameter] public int ElectionId { get; set; }

    protected override async Task OnParametersSetAsync()
    {
        Structures = await Service.GetAllStructure(ElectionId);
    }

    private void onChange(ChangeEventArgs<string, GeneralItemDTO> args)
    {
        var AutoVal = args.Value;
        try
        {
            var obj = (from aa in Structures
                where aa.EnglishTitle == AutoVal
                select aa).FirstOrDefault();
            NavigationManager.NavigateTo(obj.UrduTitle);
        }
        catch (Exception)
        {
            // ignored
        }
    }

}