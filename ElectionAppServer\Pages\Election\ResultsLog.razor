﻿@page "/resultslog"
@using FilterType = Syncfusion.Blazor.DropDowns.FilterType
@inherits OwningComponentBase<ElectionResultsDataService>
<div >
    <h3>Results Log</h3>
    <div class="row">
        <div class="col-md">
            @if (assemblyList.Any())
            {
                <SfDropDownList AllowFiltering="true" TItem="GeneralItemDTO" TValue="int?"
                                Placeholder="Select - Assembly" @bind-Value="@assemblyId"
                                DataSource="@assemblyList" FloatLabelType="FloatLabelType.Always"
                                FilterType="FilterType.Contains">
                    <DropDownListFieldSettings Value="Id" Text="EnglishTitle"></DropDownListFieldSettings>
                    <DropDownListEvents TItem="GeneralItemDTO" TValue="int?" ValueChange="OnAssemblySelect"></DropDownListEvents>
                </SfDropDownList>
            }
            else
            {
                <p>Please wait...</p>
            }

        </div>
        <div class="col-md">
            @if (assemblyId != null)
            {
                if (constituencyList.Any())
                {
                    <SfDropDownList AllowFiltering="true" TItem="GeneralItemDTO" TValue="int?"
                                    Placeholder="Select - Constituency" @bind-Value="@constituencyId"
                                    DataSource="@constituencyList" FloatLabelType="FloatLabelType.Always"
                                    FilterType="FilterType.Contains">
                        <DropDownListFieldSettings Value="Id" Text="EnglishTitle"></DropDownListFieldSettings>
                        <DropDownListEvents TItem="GeneralItemDTO" TValue="int?" ValueChange="OnConstituencySelect"></DropDownListEvents>
                    </SfDropDownList>
                }
                else
                {
                    <p>Please wait</p>
                }
            }
            else
            {
                <p></p>
            }

        </div>
    </div>
    @if (log != null && log.PS != null && log.PS.Any())
    {
        <div class="row">
            <div class="col">
                <h3>Polling Stations</h3>
                <table class="table table-striped table-sm">
                    <thead>
                    <tr>
                        @foreach (var item in log.PS)
                        {
                            <th>@item.Title</th>
                        }
                    </tr>
                    </thead>
                    <tbody>
                    <tr>
                        @foreach (var item in log.PS)
                        {
                            if (!string.IsNullOrEmpty(item.Action))
                            {
                                <td>@item.PSCount (@item.Action)</td>
                            }
                            else
                            {
                                <td></td>
                            }
                        }


                    </tr>
                    <tr>
                        @foreach (var item in log.PS)
                        {
                            if (!string.IsNullOrEmpty(item.Action))
                            {
                                <td>Namanigar: @item.Namanigar</td>
                            }
                        }


                    </tr>
                    <tr>
                        @foreach (var item in log.PS)
                        {
                            <td>

                                @if (item.ActionDateTime != null)
                                {
                                    <span>User: @item.User</span>
                                    <br/>
                                    <span>@item.ActionDateTime.Value.ToString("d MMM, yyyy h:mm:ss")</span>
                                }
                            </td>
                        }


                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <h3>Results</h3>
        <table class="table table-striped table-sm">
            <thead>
            <tr>
                <th>Candidate</th>
                <th>Current Votes</th>
                <th>1st Last</th>
                <th>2nd Last</th>
                <th>3rd Last</th>
            </tr>
            </thead>
            <tbody>
            @foreach (var vv in log.Votes)
            {
                <tr>
                    <td>@vv.Candidate</td>
                    <td>@vv.Current</td>
                    <td>@vv.FirstLast</td>
                    <td>@vv.SecondLast</td>
                    <td>@vv.ThirdLast</td>

                </tr>
            }

            </tbody>
        </table>
    }

</div>

@code {
    private int? constituencyId = 0;
    private List<GeneralItemDTO> constituencyList = new();
    private List<GeneralItemDTO> assemblyList = new();
    private int? electionId;
    private int? assemblyId;

    [CascadingParameter] private Task<AuthenticationState> authenticationStateTask { get; set; }

    private ResultLogDTO log = new();

    protected override async Task OnInitializedAsync()
    {
        await Task.CompletedTask;
    }


    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            var user = (await authenticationStateTask).User;
            if (user.Identity.IsAuthenticated)
            {
                var dd = await localStorage.GetAsync<ElectionStateDTO>(await confreader.GetSettingValue("electionkey"));

                electionId = dd.Value.ElectionId;
                state.SetState(dd.Value);
                assemblyList = await Service.GetAssemblyList(dd.Value.PhaseId);
                StateHasChanged();
            }
            else
            {
                try
                {
                    //await  ProtectedSessionStore.DeleteAsync("election");
                    await localStorage.DeleteAsync(await confreader.GetSettingValue("electionkey"));
                    state.SetState(new ElectionStateDTO { ElectionId = 0, ElectionTitle = "", PhaseId = 0, PhaseTitle = "" });
                }
                catch (Exception)
                {
                    // ignored
                }
                //NavigationManager.NavigateTo("");
            }

            StateHasChanged();
        }
    }

    public async Task OnConstituencySelect(ChangeEventArgs<int?, GeneralItemDTO> args)
    {
        if (assemblyId != null && constituencyId != null)
        {
            // need to update seat type logic when there are multiple position on same constituency
            log = await Service.GetLogData((int)constituencyId, state.state.PhaseId, (int)assemblyId, 2);
        }
    }

    public async Task OnAssemblySelect(ChangeEventArgs<int?, GeneralItemDTO> args)
    {
        if (assemblyId != null)
        {
            constituencyList = await Service.GetConstituenciesByAssembly((int)assemblyId, state.state.PhaseId);
        }
        else
        {
            constituencyList = null;
        }
    }

}