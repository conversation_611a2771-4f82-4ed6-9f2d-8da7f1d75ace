﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using ElectionAppServer.Data;
using ElectionAppServer.DTO;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Syncfusion.Blazor.Grids;
using Syncfusion.Blazor.Notifications;
using Syncfusion.Blazor.Popups;

namespace ElectionAppServer.Pages.Election;

public partial class PollingStations
{
    private readonly List<GeneralItemDTO> TypeList = new()
    {
        new GeneralItemDTO { Id = 1, EnglishTitle = "Male" },
        new GeneralItemDTO { Id = 2, EnglishTitle = "Female" },
        new GeneralItemDTO { Id = 3, EnglishTitle = "Combined" }
    };

    [CascadingParameter] private Task<AuthenticationState> authenticationStateTask { get; set; }

    [Parameter] public int ConstituencyId { get; set; }
    public SfToast ToastObj { get; set; }
    public SfDialog dlgForm { get; set; }
    public bool IsFormOpen { get; set; }
    public PollingStationInfoDTO info { get; set; }
    public PollingStationDetailDTO selectedObj { get; set; } = new();

    protected override async Task OnParametersSetAsync()
    {
        await base.OnParametersSetAsync();
        info = await Service.GetPollingStationInfo(ConstituencyId);
    }

    public async Task SavePhaseData()
    {
        //await Task.CompletedTask;
        var rs = await Service.SaveData(selectedObj);
        if (rs == "OK")
        {
            info = await Service.GetPollingStationInfo(ConstituencyId);
            IsFormOpen = false;
        }
        else
        {
            var mm = new ToastModel { Title = "Duplicate Name", Content = rs };
            await ToastObj.ShowAsync(mm);
        }

        StateHasChanged();
    }

    public void OpenCreateForm()
    {
        selectedObj = new PollingStationDetailDTO { ConstituencyId = ConstituencyId };
        IsFormOpen = true;
        StateHasChanged();
    }

    public async Task DeleteRecord(int id)
    {
        var res = await Service.Delete(id);
        info = await Service.GetPollingStationInfo(ConstituencyId);
    }

    public async Task OpenEditFormAsync(PollingStationDetailDTO obj)
    {
        selectedObj = await Service.GetPollingStation(obj.Id);
        IsFormOpen = true;
        StateHasChanged();
    }

    private async Task TranslateToUrdu()
    {
        try
        {
            if (string.IsNullOrEmpty(selectedObj.UrduTitle))
                selectedObj.UrduTitle = await Translation.TranslateText(selectedObj.EnglishTitle);
        }
        catch (Exception)
        {
            // ignored
        }
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        //return base.OnAfterRenderAsync(firstRender);
        if (firstRender)
        {
            var user = (await authenticationStateTask).User;
            if (user.Identity.IsAuthenticated)
            {
                //var dd =  await ProtectedSessionStore.GetAsync<ElectionStateDTO>("election");
                var dd = await localStorage.GetAsync<ElectionStateDTO>(await confreader.GetSettingValue("electionkey"));
                state.SetState(dd.Value);
            }
            else
            {
                try
                {
                    //await  ProtectedSessionStore.DeleteAsync("election");
                    await localStorage.DeleteAsync(await confreader.GetSettingValue("electionkey"));
                    state.SetState(new ElectionStateDTO
                        { ElectionId = 0, ElectionTitle = "", PhaseId = 0, PhaseTitle = "" });
                }
                catch (Exception)
                {
                    // ignored
                }

                NavigationManager.NavigateTo("");
            }

            StateHasChanged();
        }
    }

    public void CustomizeCell(QueryCellInfoEventArgs<PollingStationDetailDTO> args)
    {
        if (args.Column.Field == "UrduTitle" ||
            args.Column.Field == "AssemblyType" /*|| args.Column.Field == "SeatsList"*/)
        {
            args.Cell.AddClass(new[] { "urdu-column" });
            args.Cell.AddStyle(new[] { "font-size:16px;line-height: 1.4;text-align: right" });
        }
    }
}