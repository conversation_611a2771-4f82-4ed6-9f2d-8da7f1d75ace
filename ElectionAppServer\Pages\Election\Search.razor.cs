﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ElectionAppServer.Data;
using ElectionAppServer.DTO;
using ElectionAppServer.Model;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.JSInterop;
using Syncfusion.Blazor.DropDowns;
using Syncfusion.Blazor.Grids;
using Syncfusion.Blazor.Notifications;
using Syncfusion.Blazor.Popups;

namespace ElectionAppServer.Pages.Election;

public partial class Search
{
    private readonly bool InUrdu = false;
    private SfDialog dlgForm;

    [CascadingParameter] private Task<AuthenticationState> authenticationStateTask { get; set; }
    public bool IsPollingStationExist { get; set; }
    private SfToast ToastObj { get; set; }
    public int? electionId { get; set; }
    public int? electionAssemblyId { get; set; }
    public int? provinceId { get; set; }
    public int? divisionId { get; set; }
    public int? districtId { get; set; }
    public int? townId { get; set; }
    public int? unionCouncilId { get; set; }
    public int? wardId { get; set; }

    public ElectionType electionType { get; set; }

    public List<GeneralItemDTO> elections_list { get; set; }
    public List<GeneralItemDTO> assemblies_list { get; set; }
    public List<GeneralItemDTO> provinces_list { get; set; }
    public List<GeneralItemDTO> divisions_list { get; set; }
    public List<GeneralItemDTO> districts_list { get; set; }
    public List<GeneralItemDTO> towns_list { get; set; }
    public List<GeneralItemDTO> unionCouncils_list { get; set; }
    public List<GeneralItemDTO> wards_list { get; set; }
    public List<ConstituencyDTO> search_result { get; set; }
    public List<NamanigarDTO> allNamanigars { get; set; }
    public NamanigarDTO selectedNamanigar { get; set; } 
    public SfDialog dlgNamanigarForm { get; set; }
    public ConstituencyDTO selectedObj { get; set; }

    private SfGrid<ConstituencyDTO> Grid { get; set; }
    public bool IsDialogOpen { get; set; } = false;
    public bool isDlgNamaniagrVisible { get; set; } = false;

    private async Task FilterByProvince()
    {
        divisionId = null;
        districtId = null;
        townId = null;
        unionCouncilId = null;
        wardId = null;

        districts_list = new List<GeneralItemDTO>();
        towns_list = new List<GeneralItemDTO>();
        unionCouncils_list = new List<GeneralItemDTO>();
        wards_list = new List<GeneralItemDTO>();
        divisions_list = new List<GeneralItemDTO>();

        if (provinceId != null)
        {
            divisions_list = await Service.GetElectionDivisionsStructure((int)electionId, (int)provinceId);
            if (divisions_list.Any())
            {
                divisionId = divisions_list[0].Id;
                await FilterByDivision();
            }
        }
    }


    private async Task FilterByDivision()
    {
        districtId = null;
        townId = null;
        unionCouncilId = null;
        wardId = null;

        districts_list = new List<GeneralItemDTO>();
        towns_list = new List<GeneralItemDTO>();
        unionCouncils_list = new List<GeneralItemDTO>();
        wards_list = new List<GeneralItemDTO>();

        if (divisionId != null)
            districts_list = await Service.GetElectionDistrictsStructure((int)electionId, (int)divisionId);
    }


    private async Task FilterByDistrict()
    {
        townId = null;
        unionCouncilId = null;
        wardId = null;

        towns_list = new List<GeneralItemDTO>();
        unionCouncils_list = new List<GeneralItemDTO>();
        wards_list = new List<GeneralItemDTO>();

        if (districtId != null) towns_list = await Service.GetElectionTownsStructure((int)electionId, (int)districtId);
    }


    private async Task FilterByUC()
    {
        wardId = null;
        await Task.CompletedTask;
    }


    private async Task FilterByTown()
    {
        unionCouncilId = null;
        wardId = null;

        unionCouncils_list = new List<GeneralItemDTO>();
        wards_list = new List<GeneralItemDTO>();

        if (townId != null)
            unionCouncils_list = await Service.GetElectionUnionCouncilsStructure((int)electionId, (int)townId);
    }


    private async Task SearchData()
    {
        search_result = await Service.Search((int)electionId, electionAssemblyId, provinceId, divisionId, districtId,
            townId, unionCouncilId, wardId, InUrdu);
    }


    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        //return base.OnAfterRenderAsync(firstRender);
        if (firstRender)
        {
            var user = (await authenticationStateTask).User;
            if (user.Identity.IsAuthenticated)
            {
                var dd = await localStorage.GetAsync<ElectionStateDTO>(await confreader.GetSettingValue("electionkey"));
                electionType = dd.Value.ElectionType.Value;
                electionId = dd.Value.ElectionId;

                state.SetState(dd.Value);
                await FillAssemblies_n_Prov();
                allPrevConstuencies = await Service.GetPreviousConstituencies(state.state.ElectionId);
                StateHasChanged();
            }
            else
            {
                try
                {
                    //await  ProtectedSessionStore.DeleteAsync("election");
                    await localStorage.DeleteAsync(await confreader.GetSettingValue("electionkey"));
                    state.SetState(new ElectionStateDTO
                        { ElectionId = 0, ElectionTitle = "", PhaseId = 0, PhaseTitle = "" });
                }
                catch (Exception)
                {
                    // ignored
                }
                //NavigationManager.NavigateTo("");
            }

            StateHasChanged();
        }
    }


    private async Task FillAssemblies_n_Prov()
    {
        electionAssemblyId = null;
        provinceId = null;
        divisionId = null;
        districtId = null;
        townId = null;
        unionCouncilId = null;
        wardId = null;
        divisions_list = new List<GeneralItemDTO>();
        districts_list = new List<GeneralItemDTO>();
        towns_list = new List<GeneralItemDTO>();
        unionCouncils_list = new List<GeneralItemDTO>();
        wards_list = new List<GeneralItemDTO>();
        assemblies_list = new List<GeneralItemDTO>();
        provinces_list = new List<GeneralItemDTO>();

        if (electionId != null)
        {
            assemblies_list = await Service.GetElectionAssemblies((int)electionId);
            provinces_list = await Service.GetElectionProvincesStructure((int)electionId);
            //search_result = await Service.Search((int)electionId, electionAssemblyId, provinceId, divisionId, districtId, townId, unionCouncilId, wardId, InUrdu);
            search_result = new List<ConstituencyDTO>();
        }
        else
        {
            search_result = new List<ConstituencyDTO>();
        }
    }

    private async Task OpenEditForm(int structureId, int assemblyId)
    {
        //await dlgNomination.ShowAsync();
        //await nom.LoadData(1, 13, 14, 184, 4);
        //StateHasChanged();
        selectedObj = await Service.GetConstituency(structureId, assemblyId, state.state.PhaseId);
        StateHasChanged();
        await dlgForm.ShowAsync();
        StateHasChanged();
    }

    private async Task DeleteRecord(int structureId, int assemblyId)
    {
        var user = (await authenticationStateTask).User;
        var res = await JSRuntime.InvokeAsync<bool>("confirm", new[] { "Are you want to delete this record?" });

        if (res)
        {
            res = await JSRuntime.InvokeAsync<bool>("confirm", new[] { "Do you want to delete this structure too?" });

            await Service.DeleteStructure(structureId, assemblyId);
        }
        else
        {
            await Service.RemoveStructure(structureId, assemblyId, user.Identity.Name);
        }
        //search_result = await Service.Search((int)electionId, electionAssemblyId, provinceId, divisionId, districtId, townId, unionCouncilId, wardId, InUrdu);
        //try
        //{
        //    await Grid.Refresh();
        //}
        //catch (Exception)
        //{

        //}
    }


    private void OpenNominationForm(int strcId, int asmbId)
    {
        // need to update
        //await dlgNomination.ShowAsync();
        ////await nomination_form.SetParameters(asmbId, strcId);
        //await nomination_form.SetParameters(state.state.ElectionId, state.state.PhaseId, asmbId, strcId);
        //StateHasChanged();
    }


    private async Task SaveData()
    {
        var user = (await authenticationStateTask).User;
        try
        {
            var res = await Service.UpdateConstituency(selectedObj, user.Identity.Name, state.state.PhaseId);
            search_result = await Service.Search((int)electionId, electionAssemblyId, provinceId, divisionId,
                districtId, townId, unionCouncilId, wardId, InUrdu);
            await dlgForm.HideAsync();
            try
            {
                await Grid.Refresh();
            }
            catch (Exception)
            {
                // ignored
            }
        }
        catch (Exception ex)
        {
            var mm = new ToastModel
            {
                Title = "Error", Content = ex.Message, CssClass = "e-toast-danger", ShowProgressBar = true,
                Timeout = 5000, ShowCloseButton = true
            };
            await ToastObj.ShowAsync(mm);
        }
    }

    private async Task TranslateText()
    {
        try
        {
            if (string.IsNullOrEmpty(selectedObj.UrduTitle))
                selectedObj.UrduTitle = await Translation.TranslateText(selectedObj.EnglishTitle);
        }
        catch (Exception)
        {
            // ignored
        }
    }

    public void CustomizeCell(QueryCellInfoEventArgs<ConstituencyDTO> args)
    {
        if (InUrdu)
            if (args.Column.Field != "Action")
            {
                args.Cell.AddClass(new[] { "urdu-column" });
                args.Cell.AddStyle(new[] { "font-size:18px" });
            }
    }

    private async Task TranslateToUrdu()
    {
        try
        {
            if (string.IsNullOrEmpty(selectedObj.UrduTitle))
                selectedObj.UrduTitle = await Translation.TranslateText(selectedObj.EnglishTitle);
        }
        catch (Exception)
        {
            // ignored
        }
    }

    public void CustomizeCell(QueryCellInfoEventArgs<PrevConstituencyDTO> args)
    {
        if (args.Column.Field == "UrduTitle")
        {
            args.Cell.AddClass(new[] { "urdu-column" });
            args.Cell.AddStyle(new[] { "font-size:18px;line-height: 1.4;text-align: right" });
        }
    }


    private async Task OpenCreateNamaniagrForm()
    {
        allNamanigars = await Service.GetAllNamanigars();
        selectedNamanigar = new NamanigarDTO { PSDetail = "", ConstituencyId = selectedObj.Id };
        await dlgNamanigarForm.ShowAsync();
    }


    private async Task OpenEditNamaniagrForm(NamanigarDTO nn)
    {
        try
        {
            allNamanigars = await Service.GetAllNamanigars();
            selectedNamanigar = new NamanigarDTO();
            selectedNamanigar =
                await Service.GetNamanigarById((int)nn.Id,
                    selectedObj
                        .Id); //new NamanigarDTO { Code = nn.Code, Id = nn.Id, ConstituencyId = nn.ConstituencyId, Email = nn.Email, Name = nn.Name, Phone = nn.Phone, PSDetail = nn.PSDetail };
            selectedNamanigar.Id = nn.Id;
            StateHasChanged();
            await dlgNamanigarForm.ShowAsync();
        }
        catch (Exception ex)
        {
            var msg = new ToastModel
            {
                Content = ex.Message, Title = "Error", CssClass = "e-toast-danger", ShowProgressBar = true,
                Timeout = 5000, ShowCloseButton = true
            };
            await ToastObj.ShowAsync(msg);
        }
    }


    private async Task DeleteNamanigarForm(NamanigarDTO nn)
    {
        var res = await JSRuntime.InvokeAsync<bool>("confirm", new[] { "Are you sure want to remove this namanigar?" });
        if (res)
        {
            var msg = await Service.RemoveNamanigarFromConstituency((int)nn.Id, selectedObj.Id);
            if (msg != "OK")
            {
                var tt = new ToastModel
                {
                    Content = msg, Title = "Error", CssClass = "e-toast-danger", ShowProgressBar = true, Timeout = 5000,
                    ShowCloseButton = true
                };
                await ToastObj.ShowAsync(tt);
            }
            else
            {
                await dlgNamanigarForm.HideAsync();
                selectedObj.Namanigars = await Service.GetConstituencyNamanigars(selectedObj.Id);
            }
        }
    }


    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        allNamanigars = await Service.GetAllNamanigars();
        selectedNamanigar = new NamanigarDTO();
        selectedObj = new ConstituencyDTO
        {
            PrevConstituencies = new List<PrevConstituencyDTO>(),
            Namanigars = new List<NamanigarDTO>()
        };
    }


    private async Task GetNamanigarInfo(ChangeEventArgs<int?, NamanigarDTO> args)
    {
        var obj = await Service.GetNamanigarById(args.Value.Value, selectedObj.Id);
        selectedNamanigar = new NamanigarDTO
        {
            Code = obj.Code, Id = obj.Id, PSDetail = obj.PSDetail, ConstituencyId = obj.ConstituencyId,
            Email = obj.Email, Name = obj.Name, Phone = obj.Phone, District = obj.District
        };
        selectedNamanigar.Id = args.Value;
        StateHasChanged();
    }


    private async Task AddNamanigarToConstituency()
    {
        selectedNamanigar.ConstituencyId = selectedObj.Id;
        var user = (await authenticationStateTask).User;
        var res = await Service.AddNamanigarToConstituency(selectedNamanigar, user.Identity.Name);
        selectedObj.Namanigars = await Service.GetConstituencyNamanigars(selectedObj.Id);
        await dlgNamanigarForm.HideAsync();
    }

    private async Task GeneratePSs()
    {
        // if total polling station is zero then display error message by using synfusion toast
        if (selectedObj.TotalPollingStations == null || selectedObj.TotalPollingStations == 0)
        {
            var msg = new ToastModel
            {
                Content = "Total polling station is zero. Please enter valid value.", Title = "Error",
                CssClass = "e-toast-danger", ShowProgressBar = true, Timeout = 5000, ShowCloseButton = true
            };
            await ToastObj.ShowAsync(msg);
            return;
        }

        var resp = await JSRuntime.InvokeAsync<bool>("confirm",
            new[] { "Are you sure want to auto generate PSs on this constituency with 1000 register voters?" });
        if (resp)
            if (selectedObj.TotalVoters != 0 && selectedObj.TotalPollingStations != null &&
                selectedObj.TotalPollingStations.Value > 1)
            {
                var user = (await authenticationStateTask).User;
                var op = await Service.GeneratePSs(selectedObj.Id, selectedObj.TotalPollingStations.Value,
                    user.Identity.Name);
                if (op != "OK")
                {
                    var mm = new ToastModel
                    {
                        Title = "Error", Content = op, CssClass = "e-toast-danger", ShowProgressBar = true,
                        Timeout = 5000, ShowCloseButton = true
                    };
                    await ToastObj.ShowAsync(mm);
                }
            }
    }


    #region Previous Constituency Code Block

    public bool IsPreviousConstDialogOpen { get; set; }
    public SfDialog dlgPreviousConst { get; set; }
    public SfGrid<PrevConstituencyDTO> dlgPrevConGrid { get; set; }
    public PrevConstituencyDTO previousConst { get; set; } = new();
    private List<GeneralItemDTO> allPrevConstuencies { get; set; }

    private async Task SavePreContData()
    {
        var res = await Service.SavePreviousConstData(previousConst);
        selectedObj.PrevConstituencies = await Service.GetAssignedPreviousConstituencies(selectedObj.Id);
        await dlgPrevConGrid.Refresh();
        await dlgPreviousConst.HideAsync();
    }

    private async Task OpenAddPreviousConstituencyForm()
    {
        //allPrevConstuencies = await Service.GetPreviousConstituencies(selectedObj.Id);
        if (allPrevConstuencies.Any())
        {
            var previousElection = await Service.GetPreviousElection(selectedObj.Id);
            previousConst = new PrevConstituencyDTO
                { Description = "", Election = previousElection, ParentStructureId = selectedObj.Id };
            IsPreviousConstDialogOpen = true;
            await dlgPreviousConst.ShowAsync();
        }
        else
        {
            await JSRuntime.InvokeVoidAsync("alert", "No previous election defined");
        }
    }


    private async Task OpenEditPreviousConstituencyForm(int prevConId, string description)
    {
        var previousElection = await Service.GetPreviousElection(selectedObj.Id);
        previousConst = new PrevConstituencyDTO
        {
            Description = description, PreviousConstituencyId = prevConId, Election = previousElection,
            ParentStructureId = selectedObj.Id
        };
        IsPreviousConstDialogOpen = true;
        await dlgPreviousConst.ShowAsync();
    }

    private async Task DeletePreviousConstituency(int prevConId)
    {
        string[] para = { "Are you sure want to remove this mapping?" };
        var confirm = await JSRuntime.InvokeAsync<bool>("confirm", para);
        if (confirm)
        {
            var res = await Service.RemovePreviousConstituency(selectedObj.Id, prevConId);
            selectedObj.PrevConstituencies = await Service.GetAssignedPreviousConstituencies(selectedObj.Id);
            await dlgPrevConGrid.Refresh();
        }
    }

    #endregion
}