﻿using System;
using System.Collections.Generic;

namespace ElectionAppServer.DTO;

public class PartyRptDTO
{
    public int Id { get; set; }
    public string EnglishTitle { get; set; }
    public string UrduTitle { get; set; }
    public int? SymbolId { get; set; }
    public string Symbol { get; set; }
    public string Trivia { get; set; }
    public string ShortEnglishTitle { get; set; }
    public string Address { get; set; }
    public string CurrentLeader { get; set; }
    public string CurrentLeaderDesignation { get; set; }
    public string CurrentLeaderPicId { get; set; }
    public DateTime? DateOfCreation { get; set; }
    public string Designation { get; set; }

    public List<NominationRptDTO> Nominations { get; set; }
    public string Leader { get; internal set; }
    public string ShortUrduTitle { get; internal set; }
    public string LeaderPicId { get; internal set; }
    public List<CoalitionRptDTO> Coalition { get; internal set; }
}