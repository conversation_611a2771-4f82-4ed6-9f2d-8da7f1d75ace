﻿using System.Collections.Generic;

namespace ElectionAppServer.DTO;

public class AdminUnitRptDTO
{
    public int Id { get; set; }
    public int DivisionsCount { get; set; }
    public string EnglishTitle { get; set; }
    public int TownCount { get; set; }

    public string UrduTitle { get; set; }
    public List<GeneralItemDTO> SubAdminUnits { get; set; }
    public List<GeneralItemDTO> Regions { get; set; }
    public int DistrictCount { get; set; }
    public string RegionUrdu { get; set; }
    public string RegionEnglish { get; set; }
    public string DivisionEnglish { get; set; }
    public string DivisionUrdu { get; set; }
    public string ProvinceEnglish { get; set; }
    public string ProvinceUrdu { get; set; }
    public string DistrictUrdu { get; set; }
    public string DistrictEnglish { get; set; }
    public int UCCount { get; internal set; }
    public int WardCount { get; set; }
    public string TownUrdu { get; set; }
    public string TownEnglish { get; set; }
    public string UCUrdu { get; set; }
    public string UCEnglish { get; set; }
    public string Type { get; set; }
    public int RegionCount { get; set; }
    public string Code { get; set; }
    public List<NominationRptDTO> Nominations { get; set; }
    public List<AdminUnitDemographicDto> PollingSchemes { get; set; }
}

public class AdminUnitDemographicDto
{
    public int? FemaleVoters { get; set; }
    public int? YouthVoters { get; set; }
    public string GeneralTrivia { get; set; }
    public string Trivia { get; set; }
    public string ImportantAreas { get; set; }
    public string ImportantPoliticalPersonalities { get; set; }
    public string Languages { get; set; }
    public string LiteracyRate { get; set; }
    public string MajorityIncomeSource { get; set; }
    public int? MaleVoters { get; set; }
    public int? Population { get; set; }
    public string Problems { get; set; }
    public float? RuralAreaPer { get; set; }
    public float? UrbanAreaPer { get; set; }
    public int? TotalPollingStations { get; set; }
    public string Type { get; set; }
    public string Area { get; set; }
    public string AverageHouseholdIncome { get; set; }
    public string Castes { get; set; }
    public string Code { get; set; }
    public int TotalVoters => (MaleVoters ?? 0) + (FemaleVoters ?? 0);
    public string Phase { get; set; }
    public string Profile { get; set; }
}