﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace ElectionAppServer.Migrations.ApplicationDb
{
	public partial class Resultslog2 : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropForeignKey(
				 name: "FK_ResultLogs_AspNetUsers_NamanigarId",
				 table: "ResultLogs");

			migrationBuilder.AlterColumn<int>(
				 name: "NamanigarId",
				 table: "ResultLogs",
				 nullable: false,
				 oldClrType: typeof(string),
				 oldType: "nvarchar(450)",
				 oldNullable: true);

			migrationBuilder.AddColumn<string>(
				 name: "ApplicationUserId",
				 table: "ResultLogs",
				 nullable: true);

			migrationBuilder.AddColumn<int>(
				 name: "PollingStations",
				 table: "ResultLogs",
				 nullable: false,
				 defaultValue: 0);

			migrationBuilder.AddColumn<int>(
				 name: "Votes",
				 table: "ResultLogDetails",
				 nullable: false,
				 defaultValue: 0);

			migrationBuilder.CreateIndex(
				 name: "IX_ResultLogs_ApplicationUserId",
				 table: "ResultLogs",
				 column: "ApplicationUserId");

			migrationBuilder.AddForeignKey(
				 name: "FK_ResultLogs_AspNetUsers_ApplicationUserId",
				 table: "ResultLogs",
				 column: "ApplicationUserId",
				 principalTable: "AspNetUsers",
				 principalColumn: "Id",
				 onDelete: ReferentialAction.Restrict);

			migrationBuilder.AddForeignKey(
				 name: "FK_ResultLogs_Namanigars_NamanigarId",
				 table: "ResultLogs",
				 column: "NamanigarId",
				 principalTable: "Namanigars",
				 principalColumn: "Id",
				 onDelete: ReferentialAction.Cascade);
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropForeignKey(
				 name: "FK_ResultLogs_AspNetUsers_ApplicationUserId",
				 table: "ResultLogs");

			migrationBuilder.DropForeignKey(
				 name: "FK_ResultLogs_Namanigars_NamanigarId",
				 table: "ResultLogs");

			migrationBuilder.DropIndex(
				 name: "IX_ResultLogs_ApplicationUserId",
				 table: "ResultLogs");

			migrationBuilder.DropColumn(
				 name: "ApplicationUserId",
				 table: "ResultLogs");

			migrationBuilder.DropColumn(
				 name: "PollingStations",
				 table: "ResultLogs");

			migrationBuilder.DropColumn(
				 name: "Votes",
				 table: "ResultLogDetails");

			migrationBuilder.AlterColumn<string>(
				 name: "NamanigarId",
				 table: "ResultLogs",
				 type: "nvarchar(450)",
				 nullable: true,
				 oldClrType: typeof(int));

			migrationBuilder.AddForeignKey(
				 name: "FK_ResultLogs_AspNetUsers_NamanigarId",
				 table: "ResultLogs",
				 column: "NamanigarId",
				 principalTable: "AspNetUsers",
				 principalColumn: "Id",
				 onDelete: ReferentialAction.Restrict);
		}
	}
}
