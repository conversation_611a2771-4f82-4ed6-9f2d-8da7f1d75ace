﻿@page "/tickerlb"
@attribute [Authorize]

@inject NavigationManager NavigationManager
@inject UserManager<ApplicationUser> _UserManager
@inject RoleManager<IdentityRole> _RoleManager
@inject AuthenticationStateProvider AuthenticationStateProvider

@inherits OwningComponentBase<ElectionResultsDataService>
@inject ClipboardService ClipboardService



@*<PageCaption Title="Live Ticker"></PageCaption>*@
<MudText Typo="Typo.h5">Live Ticker</MudText>

<section>


    <SfTab>
        <TabItems>
            <TabItem>
                <ChildContent>
                    <TabHeader Text="City Councils"></TabHeader>
                </ChildContent>
                <ContentTemplate>
                    <div class="row" style="direction: rtl;">
                        @foreach (var r in results)
                        {
                            if (r.Assembly.Contains("City Council"))
                            {
                                <div class="col-12 urdu-column" style="direction: rtl; text-align: right; background-color: #87cefa; padding: 5px; line-height: 1.7; margin: 5px;">
                                    <img src=images/copy.png align="left" style="cursor:pointer" @onclick=@(() => CopyToClipboard(r.Ticker))/>
                                    @((MarkupString)r.Ticker)

                                </div>
                                <hr/>
                            }
                        }
                    </div>
                </ContentTemplate>
            </TabItem>

            <TabItem>
                <ChildContent>
                    <TabHeader Text="Tehsil Councils"></TabHeader>
                </ChildContent>
                <ContentTemplate>
                    <div class="row" style="direction: rtl;">

                        @foreach (var r in results)
                        {
                            if (r.Assembly.Contains("Tehsil Council"))
                            {
                                <div class="col-12 urdu-column" style="direction: rtl; text-align: right; background-color: #87cefa; padding: 5px; line-height: 1.7; margin: 5px;">
                                    <img src=images/copy.png align="left" style="cursor:pointer" @onclick=@(() => CopyToClipboard(r.Ticker))/>
                                    @((MarkupString)r.Ticker)

                                </div>
                                <hr/>
                            }
                        }
                    </div>
                </ContentTemplate>
            </TabItem>


            <TabItem>
                <ChildContent>
                    <TabHeader Text="Neighborhood Councils"></TabHeader>
                </ChildContent>
                <ContentTemplate>
                    <div class="row" style="direction: rtl;">

                        @foreach (var r in results)
                        {
                            if (r.Assembly.Contains("Naighborhood"))
                            {
                                <div class="col-12 urdu-column" style="direction: rtl; text-align: right; background-color: #87cefa; padding: 5px; line-height: 1.7; margin: 5px;">
                                    <img src=images/copy.png align="left" style="cursor:pointer" @onclick=@(() => CopyToClipboard(r.Ticker))/>
                                    @((MarkupString)r.Ticker)
                                </div>
                                <hr/>
                            }
                        }
                    </div>
                </ContentTemplate>
            </TabItem>

            <TabItem>
                <ChildContent>
                    <TabHeader Text="Village Councils"></TabHeader>
                </ChildContent>
                <ContentTemplate>
                    <div class="row" style="direction: rtl;">

                        @foreach (var r in results)
                        {
                            if (r.Assembly.Contains("Village"))
                            {
                                <div class="col-12 urdu-column" style="direction: rtl; text-align: right; background-color: #87cefa; padding: 5px; line-height: 1.7; margin: 5px;">
                                    <img src=images/copy.png align="left" style="cursor:pointer" @onclick=@(() => CopyToClipboard(r.Ticker))/>
                                    @((MarkupString)r.Ticker)
                                </div>
                                <hr/>
                            }
                        }
                    </div>
                </ContentTemplate>
            </TabItem>
        </TabItems>
    </SfTab>
    @*@if (results != null)
        {


        }
        else
        {
        <p>wait...</p>
        }*@

</section>