﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ElectionAppServer.DTO;
using ElectionAppServer.Model;
using Microsoft.EntityFrameworkCore;

namespace ElectionAppServer.Data;

public class TownService
{
    private readonly IDbContextFactory<ApplicationDbContext> fc; // private  readonly  ApplicationDbContext dc;

    public TownService(IDbContextFactory<ApplicationDbContext> contextFactory) // (ApplicationDbContext  context)
    {
        fc = contextFactory; // dc  =  context;
    }

    public Task<List<GeneralItemDTO>> GetList(int districtId)
    {
        using var dc = fc.CreateDbContext();
        var q = (from aa in dc.Structures.OfType<Town>()
            orderby aa.EnglishTitle
            where aa.DistrictId == districtId
            select new GeneralItemDTO
                { Id = aa.Id, EnglishTitle = aa.EnglishTitle, UrduTitle = aa.UrduTitle }).ToList();
        return Task.FromResult(q);
    }

    public Task<Town> Create(Town obj)
    {
        using var dc = fc.CreateDbContext();
        dc.Structures.Add(obj);
        dc.SaveChanges();
        return Task.FromResult(obj);
    }

    public Task<Town> Update(Town obj)
    {
        using var dc = fc.CreateDbContext();
        var st = (from aa in dc.Structures.OfType<Town>() where aa.Id == obj.Id select aa).FirstOrDefault();
        st.UrduTitle = obj.UrduTitle.Trim();
        st.EnglishTitle = obj.EnglishTitle.Trim();
        st.ModifiedBy = obj.ModifiedBy;
        st.ModifiedDate = DateTime.Now;
        st.DistrictId = obj.DistrictId;
        dc.SaveChanges();
        return Task.FromResult(obj);
    }

    public async Task<Town> Delete(int Id)
    {
        using var dc = fc.CreateDbContext();
        var st = await (from aa in dc.Structures.OfType<Town>() where aa.Id == Id select aa).FirstOrDefaultAsync();
        dc.Structures.Remove(st);
        await dc.SaveChangesAsync();
        return await Task.FromResult(st);
    }

    public Task<List<GeneralItemDTO>> GetStructure(int districtId)
    {
        using var dc = fc.CreateDbContext();
        var op = new List<GeneralItemDTO>();
        var q = (from aa in dc.Structures.OfType<District>()
            where aa.Id == districtId
            select new
            {
                DistrictId = aa.Id,
                District = aa.EnglishTitle,
                DivisionId = aa.Division.Id,
                Division = aa.Division.EnglishTitle,
                ProvinceId = aa.Division.Province.Id,
                Province = aa.Division.Province.EnglishTitle
            }).FirstOrDefault();
        op.Add(new GeneralItemDTO { Id = q.ProvinceId, EnglishTitle = q.Province, UrduTitle = "Province" });
        op.Add(new GeneralItemDTO { Id = q.DivisionId, EnglishTitle = q.Division, UrduTitle = "Division" });
        op.Add(new GeneralItemDTO { Id = q.DistrictId, EnglishTitle = q.District, UrduTitle = "District" });

        return Task.FromResult(op);
    }
}