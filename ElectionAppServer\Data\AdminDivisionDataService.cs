﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ElectionAppServer.DTO;
using ElectionAppServer.Model;
using ElectionAppServer.Model.Structures;
using Microsoft.EntityFrameworkCore;

namespace ElectionAppServer.Data;

public class AdminDivisionDataService
{
    private readonly IDbContextFactory<ApplicationDbContext> _contextFactory; // private  ApplicationDbContext  dc;

    public AdminDivisionDataService(
        IDbContextFactory<ApplicationDbContext> contextFactory) // (ApplicationDbContext  dc)
    {
        //this.dc = dc;
        _contextFactory = contextFactory;
    }

    public Task<string> Delete(int id)
    {
        using var dc = _contextFactory.CreateDbContext();
        var isDistExist = (from aa in dc.AdminDivisions
            where aa.Id == id &&
                  aa.Districts.Count > 0
            select aa).Any();
        if (isDistExist)
            throw new Exception("You cannot delete Division; one or more district exist on it");

        var reg = dc.AdminDivisions.Find(id);
        if (reg != null) dc.AdminDivisions.Remove(reg);
        dc.SaveChanges();
        return Task.FromResult("OK");
    }

    public Task<List<AdminDivisionDTO>> GetAll(int electionId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.AdminDivisions
            where aa.Province.ElectionId == electionId
            orderby aa.Province.EnglishTitle, aa.EnglishTitle
            select new AdminDivisionDTO
            {
                Id = aa.Id,
                EnglishTitle = aa.EnglishTitle,
                Province = aa.Province.EnglishTitle,
                ProvinceId = aa.ProvinceId,
                UrduTitle = aa.UrduTitle,
                CreatedBy = aa.CreatedBy,
                CreatedOn = aa.CreatedDate.ToString("d MMM, yyyy h:mm"),
                ModifiedBy = aa.ModifiedBy ?? "",
                ModifiedOn = aa.ModifiedDate != null ? aa.ModifiedDate.Value.ToString("d MMM, yyyy h:mm") : ""
            }).ToList();

        return Task.FromResult(q);
    }

    public Task<List<GeneralItemDTO>> GetProvinceList(int electionId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var q = (from aa in dc.Structures.OfType<Province>()
            where aa.ElectionId == electionId
            orderby aa.EnglishTitle
            select new GeneralItemDTO
            {
                Id = aa.Id,
                EnglishTitle = aa.EnglishTitle,
                UrduTitle = aa.UrduTitle
            }).ToList();

        return Task.FromResult(q);
    }

    public Task<AdminDivisionDTO> Save(AdminDivisionDTO obj, string userId)
    {
        using var dc = _contextFactory.CreateDbContext();
        var isExist = (from aa in dc.AdminDivisions
            where aa.Id != obj.Id
                  && aa.EnglishTitle == obj.EnglishTitle
                  && aa.ProvinceId == obj.ProvinceId
            select aa).Any();

        if (isExist)
            throw new Exception($"Division {obj.EnglishTitle} already exist");

        isExist = (from aa in dc.AdminDivisions
            where aa.Id != obj.Id
                  && aa.UrduTitle == obj.UrduTitle
                  && aa.ProvinceId == obj.ProvinceId
            select aa).Any();

        if (isExist)
            throw new Exception($"Division {obj.UrduTitle} already exist");

        if (obj.Id == 0)
        {
            var rr = new AdminDivision
            {
                CreatedDate = DateTime.Now,
                UrduTitle = obj.UrduTitle,
                ProvinceId = (int)obj.ProvinceId!,
                EnglishTitle = obj.EnglishTitle,
                CreatedBy = userId
            };
            dc.AdminDivisions.Add(rr);
            dc.SaveChanges();
            obj.Id = rr.Id;
            return Task.FromResult(obj);
        }
        else
        {
            var rr = dc.AdminDivisions.Find(obj.Id);
            rr.EnglishTitle = obj.EnglishTitle;
            rr.ModifiedBy = userId;
            rr.ModifiedDate = DateTime.Now;
            rr.ProvinceId = (int)obj.ProvinceId!;
            rr.UrduTitle = obj.UrduTitle;
            dc.SaveChanges();
            return Task.FromResult(obj);
        }
    }
}