﻿using System.ComponentModel.DataAnnotations;

namespace ElectionAppServer.DTO;

public class PrevConstituencyDTO
{
    public int ParentStructureId { get; set; }
    public string EnglishTitle { get; set; }
    public string UrduTitle { get; set; }

    [MaxLength(200)] [Required] public string Description { get; set; }

    public string Election { get; set; }

    [Required]
    [Display(Name = "Previous Constituency")]
    public int? PreviousConstituencyId { get; set; }
}