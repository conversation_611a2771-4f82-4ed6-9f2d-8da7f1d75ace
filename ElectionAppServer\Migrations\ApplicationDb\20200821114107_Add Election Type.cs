﻿using Microsoft.EntityFrameworkCore.Migrations;
using System;

namespace ElectionAppServer.Migrations.ApplicationDb
{
	public partial class AddElectionType : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.AddColumn<int>(
				 name: "ElectionType",
				 table: "Elections",
				 nullable: true);

			migrationBuilder.AlterColumn<DateTime>(
				 name: "CreatedDate",
				 table: "ElectionParties",
				 nullable: false,
				 defaultValue: new DateTime(2020, 8, 21, 16, 41, 7, 168, DateTimeKind.Local).AddTicks(2296),
				 oldClrType: typeof(DateTime),
				 oldType: "datetime2",
				 oldDefaultValue: new DateTime(2020, 8, 19, 18, 37, 48, 508, DateTimeKind.Local).AddTicks(8735));

			migrationBuilder.AlterColumn<DateTime>(
				 name: "CreatedDate",
				 table: "ElectionCandidates",
				 nullable: false,
				 defaultValue: new DateTime(2020, 8, 21, 16, 41, 7, 174, DateTimeKind.Local).AddTicks(9485),
				 oldClrType: typeof(DateTime),
				 oldType: "datetime2",
				 oldDefaultValue: new DateTime(2020, 8, 19, 18, 37, 48, 513, DateTimeKind.Local).AddTicks(7325));
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropColumn(
				 name: "ElectionType",
				 table: "Elections");

			migrationBuilder.AlterColumn<DateTime>(
				 name: "CreatedDate",
				 table: "ElectionParties",
				 type: "datetime2",
				 nullable: false,
				 defaultValue: new DateTime(2020, 8, 19, 18, 37, 48, 508, DateTimeKind.Local).AddTicks(8735),
				 oldClrType: typeof(DateTime),
				 oldDefaultValue: new DateTime(2020, 8, 21, 16, 41, 7, 168, DateTimeKind.Local).AddTicks(2296));

			migrationBuilder.AlterColumn<DateTime>(
				 name: "CreatedDate",
				 table: "ElectionCandidates",
				 type: "datetime2",
				 nullable: false,
				 defaultValue: new DateTime(2020, 8, 19, 18, 37, 48, 513, DateTimeKind.Local).AddTicks(7325),
				 oldClrType: typeof(DateTime),
				 oldDefaultValue: new DateTime(2020, 8, 21, 16, 41, 7, 174, DateTimeKind.Local).AddTicks(9485));
		}
	}
}
