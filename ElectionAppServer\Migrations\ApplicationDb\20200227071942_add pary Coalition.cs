﻿using Microsoft.EntityFrameworkCore.Migrations;
using System;

namespace ElectionAppServer.Migrations.ApplicationDb
{
	public partial class addparyCoalition : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.AddColumn<string>(
				 name: "CurrentLeader",
				 table: "Parties",
				 maxLength: 200,
				 nullable: true);

			migrationBuilder.AddColumn<string>(
				 name: "CurrentLeaderPicId",
				 table: "Parties",
				 maxLength: 200,
				 nullable: true);

			migrationBuilder.AddColumn<string>(
				 name: "LeaderPicId",
				 table: "Parties",
				 maxLength: 200,
				 nullable: true);

			migrationBuilder.CreateTable(
				 name: "Coalitions",
				 columns: table => new
				 {
					 Id = table.Column<int>(nullable: false)
							.Annotation("SqlServer:Identity", "1, 1"),
					 EnglishTitle = table.Column<string>(nullable: true),
					 UrduTitle = table.Column<string>(nullable: true),
					 CreatedDate = table.Column<DateTime>(nullable: false),
					 ModifiedDate = table.Column<DateTime>(nullable: true),
					 CreatedBy = table.Column<string>(maxLength: 450, nullable: true),
					 ModifiedBy = table.Column<string>(maxLength: 450, nullable: true)
				 },
				 constraints: table =>
				 {
					 table.PrimaryKey("PK_Coalitions", x => x.Id);
				 });

			migrationBuilder.CreateTable(
				 name: "PartyCoalition",
				 columns: table => new
				 {
					 ElectionId = table.Column<int>(nullable: false),
					 CoalitionId = table.Column<int>(nullable: false),
					 PartyId = table.Column<int>(nullable: false)
				 },
				 constraints: table =>
				 {
					 table.PrimaryKey("PK_PartyCoalition", x => new { x.PartyId, x.CoalitionId, x.ElectionId });
					 table.ForeignKey(
							  name: "FK_PartyCoalition_Coalitions_CoalitionId",
							  column: x => x.CoalitionId,
							  principalTable: "Coalitions",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Restrict);
					 table.ForeignKey(
							  name: "FK_PartyCoalition_Elections_ElectionId",
							  column: x => x.ElectionId,
							  principalTable: "Elections",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Restrict);
					 table.ForeignKey(
							  name: "FK_PartyCoalition_Parties_PartyId",
							  column: x => x.PartyId,
							  principalTable: "Parties",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Restrict);
				 });

			migrationBuilder.CreateIndex(
				 name: "IX_PartyCoalition_CoalitionId",
				 table: "PartyCoalition",
				 column: "CoalitionId");

			migrationBuilder.CreateIndex(
				 name: "IX_PartyCoalition_ElectionId",
				 table: "PartyCoalition",
				 column: "ElectionId");
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropTable(
				 name: "PartyCoalition");

			migrationBuilder.DropTable(
				 name: "Coalitions");

			migrationBuilder.DropColumn(
				 name: "CurrentLeader",
				 table: "Parties");

			migrationBuilder.DropColumn(
				 name: "CurrentLeaderPicId",
				 table: "Parties");

			migrationBuilder.DropColumn(
				 name: "LeaderPicId",
				 table: "Parties");
		}
	}
}
