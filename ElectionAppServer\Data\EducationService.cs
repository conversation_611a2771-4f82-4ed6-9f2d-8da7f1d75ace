﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using ElectionAppServer.Model;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace ElectionAppServer.Data;

public class EducationService(IDbContextFactory<ApplicationDbContext> contextFactory)
{
    // private  readonly  ApplicationDbContext dc;
    // dc  =  context;

    // (ApplicationDbContext  context)

    public Task<Education> CreateEducation(Education obj)
    {
        using var dc = contextFactory.CreateDbContext();
        dc.Educations.Add(obj);
        dc.SaveChanges();
        return Task.FromResult(obj);
    }

    public async Task<Education> DeleteEducation(int id)
    {
        await using var dc = await contextFactory.CreateDbContextAsync();
        var st = await (from aa in dc.Educations where aa.Id == id select aa).FirstOrDefaultAsync();
        dc.Educations.Remove(st);
        await dc.SaveChangesAsync();
        return await Task.FromResult(st);
    }

    public Task<List<Education>> GetList()
    {
        using var dc = contextFactory.CreateDbContext();
        var res = dc.Educations.OrderBy(c => c.EnglishTitle).ToList();
        return Task.FromResult(res);
    }

    public Task<string> TranslateText(string input)
    {
        // Set the language from/to in the url (or pass it into this function)
        var url = string.Format(
            "https://translate.googleapis.com/translate_a/single?client=gtx&sl={0}&tl={1}&dt=t&q={2}",
            "en",
            "ur",
            Uri.EscapeDataString(input));
        var httpClient = new HttpClient();
        var result = httpClient.GetStringAsync(url).Result;
        JsonConvert.DeserializeObject(result);

        // Get all json data
        /*JArray jsonData = JArray.Parse( new JavaScriptSerializer().Deserialize<List<dynamic>>(result))*/
        var jsonArray = JArray.Parse(result);
        var translation = JArray.Parse(JArray.Parse(jsonArray[0].ToString())[0].ToString())[0].ToString();

        return Task.FromResult(translation);
    }

    public Task<Education> UpdateEducation(Education obj)
    {
        using var dc = contextFactory.CreateDbContext();
        var st = (from aa in dc.Educations where aa.Id == obj.Id select aa).FirstOrDefault();
        if (st != null)
        {
            st.UrduTitle = obj.UrduTitle.Trim();
            st.EnglishTitle = obj.EnglishTitle.Trim();
            st.ModifiedBy = obj.ModifiedBy;
            st.ModifiedDate = DateTime.Now;
        }

        dc.SaveChanges();
        return Task.FromResult(obj);
    }
}