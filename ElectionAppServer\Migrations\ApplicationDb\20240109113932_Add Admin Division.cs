﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace ElectionAppServer.Migrations.ApplicationDb
{
    /// <inheritdoc />
    public partial class AddAdminDivision : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "AdminDivisionId",
                table: "Structures",
                type: "int",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "AdminDivisions",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    EnglishTitle = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    UrduTitle = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    ProvinceId = table.Column<int>(type: "int", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    ModifiedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    CreatedBy = table.Column<string>(type: "nvarchar(450)", maxLength: 450, nullable: true),
                    ModifiedBy = table.Column<string>(type: "nvarchar(450)", maxLength: 450, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AdminDivisions", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AdminDivisions_Structures_ProvinceId",
                        column: x => x.ProvinceId,
                        principalTable: "Structures",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateIndex(
                name: "IX_Structures_AdminDivisionId",
                table: "Structures",
                column: "AdminDivisionId");

            migrationBuilder.CreateIndex(
                name: "IX_AdminDivisions_EnglishTitle_ProvinceId",
                table: "AdminDivisions",
                columns: new[] { "EnglishTitle", "ProvinceId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_AdminDivisions_ProvinceId",
                table: "AdminDivisions",
                column: "ProvinceId");

            migrationBuilder.CreateIndex(
                name: "IX_AdminDivisions_UrduTitle_ProvinceId",
                table: "AdminDivisions",
                columns: new[] { "UrduTitle", "ProvinceId" },
                unique: true);

            migrationBuilder.AddForeignKey(
                name: "FK_Structures_AdminDivisions_AdminDivisionId",
                table: "Structures",
                column: "AdminDivisionId",
                principalTable: "AdminDivisions",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Structures_AdminDivisions_AdminDivisionId",
                table: "Structures");

            migrationBuilder.DropTable(
                name: "AdminDivisions");

            migrationBuilder.DropIndex(
                name: "IX_Structures_AdminDivisionId",
                table: "Structures");

            migrationBuilder.DropColumn(
                name: "AdminDivisionId",
                table: "Structures");
        }
    }
}
