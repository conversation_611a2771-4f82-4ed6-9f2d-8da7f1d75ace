﻿using System.ComponentModel.DataAnnotations;

namespace ElectionAppServer.DTO;

public class NamanigarPerformanceDTO
{
    [Required] public int? NamanigarId { get; set; }

    [Required] public int? ElectionPhaseId { get; set; }

    [Range(1, 10)] public int PreElectionPerformance { get; set; }

    [Range(1, 10)] public int Reachability { get; set; }

    [Range(1, 10)] public int DocumentEfficiency { get; set; }

    [Range(1, 10)] public int PreElectionInfoAccuracy { get; set; }

    [Range(1, 10)] public int ResultDayPerformance { get; set; }

    [Range(1, 10)] public int ResultAccuracy { get; set; }

    public bool WasAvailableDuringResult { get; set; }
    public bool HasBehavioralIssues { get; set; }

    [StringLength(1000)] public string BehavioralIssueDescription { get; set; }

    [StringLength(2000)] public string Trivia { get; set; }

    public int? ReportingFor { get; set; }
}