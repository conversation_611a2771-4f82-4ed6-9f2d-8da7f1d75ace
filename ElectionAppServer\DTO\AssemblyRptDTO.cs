﻿using System.Collections.Generic;
using ElectionAppServer.Model;

namespace ElectionAppServer.DTO;

public class AssemblyRptDTO
{
    public int Id { get; set; }
    public string EnglishTitle { get; set; }
    public string UrduTitle { get; set; }
    public int? ReserveSeats { get; set; }
    public int? WomenSeats { get; set; }
    public string TypeEnglish { get; set; }
    public string TypeUrdu { get; set; }
    public ElectionAssemblyType? ElectionAssemblyType { get; set; }
    public int ElectionId { get; set; }
    public string ElectionUrdu { get; set; }
    public string ElectionEnglish { get; set; }
    public List<SeatRptDTO> Seats { get; set; }
    public List<ConstDetailDTO> Constituencies { get; set; }
    public List<NominationRptDTO> Nominations { get; set; }
}