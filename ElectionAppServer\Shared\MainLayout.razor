﻿@inherits LayoutComponentBase
@using Syncfusion.Blazor.Buttons
<!-- Navbar -->

<AuthorizeView Roles="News Data Entry">
	<style>
		body{ user-select:none;}
	</style>
</AuthorizeView>

<MudThemeProvider Theme="MyCustomTheme" />
<MudDialogProvider />
<MudSnackbarProvider />
<Syncfusion.Blazor.Popups.SfDialogProvider />

<MudLayout>
	<MudAppBar Elevation="1" Color="Color.Transparent">
		<MudIconButton Icon="@Icons.Material.Filled.Menu" Color="Color.Inherit" Edge="Edge.Start"
							OnClick="@((e) => DrawerToggle())" />
		<MudText Typo="Typo.h5" Class="ml-3">Election Portal</MudText>
		<MudSpacer />
		<AuthorizeView>
			<Authorized>
				@if (!string.IsNullOrEmpty(state.state.ElectionTitle))
				{
					<MudButton Class="mr-2" Color="Color.Info" Size="MudBlazor.Size.Small" Variant="MudBlazor.Variant.Filled" StartIcon="@Icons.Material.Filled.ChangeCircle"
								  OnClick="NavigateToHome">Change</MudButton> <b>@state.state.ElectionTitle - @state.state.PhaseTitle</b>
							}
							else
							{
								<span>Please Select election</span>
							}
			</Authorized>
		</AuthorizeView>
		<MudSpacer />
		<AuthorizeView>
			<Authorized>
				<span class="mr-2">@context.User.Identity.Name</span>
				<form method="post" action="Identity/Account/LogOut">
					<MudButton Class="mud-theme-primary" Variant="MudBlazor.Variant.Text" ButtonType="MudBlazor.ButtonType.Submit" Size="MudBlazor.Size.Small" StartIcon="@Icons.Material.Filled.Logout"> Log out</MudButton>
				</form>
			</Authorized>
		</AuthorizeView>
		<MudIconButton Icon="@Icons.Material.Filled.MoreVert" Color="Color.Inherit" Edge="Edge.End" />
	</MudAppBar>
	<MudDrawer @bind-Open="_drawerOpen" ClipMode="DrawerClipMode.Always" Elevation="2">
		<SideMenu />
	</MudDrawer>
	<MudMainContent>
		<div style="margin-left:14px; margin-right:14px;padding-top:10px">

			@Body
		</div>
	</MudMainContent>
</MudLayout>

@* <nav class="main-header navbar navbar-expand navbar-white navbar-light" style="z-index:1"> *@
<!-- Left navbar links -->
@* <ul class="navbar-nav">
	<li class="nav-item">
	<a class="nav-link" data-widget="pushmenu" href="#" role="button"><i class="fas fa-bars"></i></a>
	</li>
	<li class="nav-item d-none d-sm-inline-block">
	<a href="/" class="nav-link">Home</a>
	</li>
	<li class="nav-item d-none d-sm-inline-block">
	<AuthorizeView>
	<Authorized>
	<a href="#" class="nav-link">@context.User.Identity.Name</a>
	</Authorized>
	</AuthorizeView>
	</li>
	<li class="nav-item d-none d-sm-inline-block">
	<AuthorizeView>
	<Authorized>
	<form method="post" action="Identity/Account/LogOut">
	<button type="submit" class="nav-link" style="border: 0; background-color: white;"><span class="oi oi-account-logout"
	aria-hidden="true"></span> Log out</button>
	</form>
	</Authorized>
	<NotAuthorized>
	<a href="/Identity/Account/Login" class="nav-link">Login</a>
	</NotAuthorized>
	</AuthorizeView>

	</li>
	</ul>

	<!-- Right navbar links -->
	<ul class="navbar-nav ml-auto">
	<li class="nav-item d-none d-sm-inline-block">
	<AuthorizeView>
	<Authorized>
	@if (!string.IsNullOrEmpty(state.state.ElectionTitle))
	{
	<MudButton Color="Color.Info" Size="MudBlazor.Size.Small" Variant="MudBlazor.Variant.Filled" StartIcon="@Icons.Material.Filled.ChangeCircle"
	OnClick="NavigateToHome">Change</MudButton> <b>@state.state.ElectionTitle - @state.state.PhaseTitle</b>
	}
	else
	{
	<span>Please Select election</span>
	}
	</Authorized>
	</AuthorizeView>
	</li>
	</ul> *@
@* </nav> *@
<!-- /.navbar -->
<!-- Main Sidebar Container -->
@* <aside class="main-sidebar sidebar-dark-primary elevation-4" style="height:100vh; z-index:20">
	<!-- Brand Logo -->
	<a href="/" class="brand-link" style=" background-color: #164a1f">
	<img src="/images/election2.jpg" alt="AdminLTE Logo" class="brand-image img-circle elevation-3" style="opacity: .8;
	height:40px">
	<span class="brand-text font-weight-light">Election Portal</span>
	</a>

	<!-- Sidebar -->
	<div class="sidebar">

	<!-- Sidebar Menu -->
	<nav class="mt-2">
	<SideMenu />
	</nav>
	<!-- /.sidebar-menu -->
	</div>
	<!-- /.sidebar -->
	</aside> *@

@*<div class="top-row px-4 auth">
	<LoginDisplay />
	</div>*@

@* <div class="content-wrapper">
	@Body
	</div> *@

@code {

	MudTheme MyCustomTheme = new MudTheme()
	{
		PaletteLight = new PaletteLight()
		{
			Primary = Colors.Blue.Default,
			Secondary = Colors.Green.Accent4,
			AppbarBackground = Colors.Red.Default,
		},
		PaletteDark = new PaletteDark()
		{
			Primary = Colors.Blue.Lighten1
		},

		LayoutProperties = new LayoutProperties()
		{
			DrawerWidthLeft = "260px",
			DrawerWidthRight = "300px"
		}
	};

	//private bool collapseNavMenu = true;
	//--> No in use //string ADMINISTRATION_ROLE = "Administrators";
	[CascadingParameter] private Task<AuthenticationState> authenticationStateTask { get; set; }

	bool _drawerOpen = true;

	void DrawerToggle()
	{
		_drawerOpen = !_drawerOpen;
	}
	protected override async Task OnInitializedAsync()
	{

		state.OnChange += StateHasChanged;

		var user = (await authenticationStateTask).User;

		if (user.Identity.IsAuthenticated)
		{
			if (user.IsInRole("Namanigar"))
			{
				NavigationManager.NavigateTo("/namanigar/results");
			}
			//var dd = await ProtectedSessionStore.GetAsync<ElectionStateDTO>("election");
			//state.SetState(dd.Value);
		}
	}

	private void NavigateToHome()
	{
		NavigationManager.NavigateTo("/");
		StateHasChanged();

	}
}