﻿using Microsoft.EntityFrameworkCore.Migrations;
using System;

namespace ElectionAppServer.Migrations
{
	public partial class Namanigar : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.CreateTable(
				 name: "Namanigars",
				 columns: table => new
				 {
					 Id = table.Column<int>(nullable: false)
							.Annotation("SqlServer:Identity", "1, 1"),
					 Name = table.Column<string>(nullable: true),
					 Code = table.Column<string>(nullable: true),
					 DistrictId = table.Column<int>(nullable: false),
					 CreatedDate = table.Column<DateTime>(nullable: false),
					 ModifiedDate = table.Column<DateTime>(nullable: true),
					 CreatedBy = table.Column<string>(maxLength: 450, nullable: true),
					 ModifiedBy = table.Column<string>(maxLength: 450, nullable: true)
				 },
				 constraints: table =>
				 {
					 table.PrimaryKey("PK_Namanigars", x => x.Id);
					 table.ForeignKey(
							  name: "FK_Namanigars_Structures_DistrictId",
							  column: x => x.DistrictId,
							  principalTable: "Structures",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Restrict);
				 });

			migrationBuilder.CreateTable(
				 name: "NamnigarConstituencies",
				 columns: table => new
				 {
					 NamanigarId = table.Column<int>(nullable: false),
					 ElectionAssemblyId = table.Column<int>(nullable: false),
					 StructureId = table.Column<int>(nullable: false)
				 },
				 constraints: table =>
				 {
					 table.PrimaryKey("PK_NamnigarConstituencies", x => new { x.ElectionAssemblyId, x.NamanigarId, x.StructureId });
					 table.ForeignKey(
							  name: "FK_NamnigarConstituencies_ElectionAssemblies_ElectionAssemblyId",
							  column: x => x.ElectionAssemblyId,
							  principalTable: "ElectionAssemblies",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Restrict);
					 table.ForeignKey(
							  name: "FK_NamnigarConstituencies_Namanigars_NamanigarId",
							  column: x => x.NamanigarId,
							  principalTable: "Namanigars",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Restrict);
					 table.ForeignKey(
							  name: "FK_NamnigarConstituencies_Structures_StructureId",
							  column: x => x.StructureId,
							  principalTable: "Structures",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Restrict);
				 });

			migrationBuilder.CreateIndex(
				 name: "IX_Namanigars_DistrictId",
				 table: "Namanigars",
				 column: "DistrictId");

			migrationBuilder.CreateIndex(
				 name: "IX_NamnigarConstituencies_NamanigarId",
				 table: "NamnigarConstituencies",
				 column: "NamanigarId");

			migrationBuilder.CreateIndex(
				 name: "IX_NamnigarConstituencies_StructureId",
				 table: "NamnigarConstituencies",
				 column: "StructureId");
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropTable(
				 name: "NamnigarConstituencies");

			migrationBuilder.DropTable(
				 name: "Namanigars");
		}
	}
}
