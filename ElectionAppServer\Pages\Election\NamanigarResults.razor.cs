using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using ElectionAppServer.DTO;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.SignalR.Client;
using Syncfusion.Blazor.Notifications;

namespace ElectionAppServer.Pages.Election;

public partial class NamanigarResults
{
    private int ElectionId;
    private HubConnection hubConnection;
    private HubConnection hubResultsConnection;
    private bool IsDetailOpen;

    private NamanigarResultDTO obj = new();
    [CascadingParameter] private Task<AuthenticationState> authenticationStateTask { get; set; }
    private SfToast ToastObj { get; set; }
    public bool IsConnected => hubConnection.State == HubConnectionState.Connected;

    private List<NamanigarResultsListViewDTO> NamanigarResultsList { get; set; } = new();

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        //return base.OnAfterRenderAsync(firstRender);
        if (firstRender)
        {
            var user = (await authenticationStateTask).User;
            if (user.Identity.IsAuthenticated)
            {
                var dd = await localStorage.GetAsync<ElectionStateDTO>(await confreader.GetSettingValue("electionkey"));

                ElectionId = dd.Value.ElectionId;

                state.SetState(dd.Value);

                NamanigarResultsList = await Service.GetNNResultsList(dd.Value.PhaseId);
                StateHasChanged();
            }
            else
            {
                try
                {
                    //await  ProtectedSessionStore.DeleteAsync("election");
                    await localStorage.DeleteAsync(await confreader.GetSettingValue("electionkey"));
                    state.SetState(new ElectionStateDTO
                        { ElectionId = 0, ElectionTitle = "", PhaseId = 0, PhaseTitle = "" });
                }
                catch (Exception)
                {
                    // ignored
                }
                //NavigationManager.NavigateTo("");
            }

            StateHasChanged();
        }
    }

    protected override async Task OnInitializedAsync()
    {
        hubConnection = new HubConnectionBuilder().WithUrl(NavigationManager.ToAbsoluteUri("/nnresultshub")).Build();
        hubResultsConnection =
            new HubConnectionBuilder().WithUrl(NavigationManager.ToAbsoluteUri("/resultshub")).Build();

        await hubConnection.StartAsync();
        await hubResultsConnection.StartAsync();


        hubConnection.On<int>("ReceiveNamanigarResult", async result =>
        {
            IsDetailOpen = false;
            NamanigarResultsList = await Service.GetNNResultsList(state.state.PhaseId);
            StateHasChanged();
        });
    }

    private async Task OpenLog(int constituencyId, int seatTypeId, int phaseId)
    {
        //await Task.CompletedTask;
        //var obj = await Service.GetNNConstituencyInfo(constituencyId, seatTypeId, phaseId);
        var res = await Service.MarkViewNamanigarResults(constituencyId, seatTypeId, phaseId);
        obj = await Service.GetNNResults(constituencyId, seatTypeId, phaseId);
        var kk = NamanigarResultsList.Find(j => j.SeatTypeId == seatTypeId &&
                                                j.ConstituencyId == constituencyId);
        kk.IsViewed = true;
        IsDetailOpen = true;
        StateHasChanged();
    }

    private async Task OpenResult(int batchId, int constituencyId, int seatTypeId)
    {
        await Task.CompletedTask;
    }

    private async Task ApproveResult()
    {
        var user = (await authenticationStateTask).User;
        if (obj != null)
        {
            string[] pars = { "Are you sure want to update this result" };
            var res = await JSRuntime.InvokeAsync<bool>("confirm", pars);
            if (res)
            {
                var op = await Service.ApproveNamanigarResult(obj, user.Identity.Name);
                if (op != null)
                {
                    //NamanigarResultsList = await Service.GetNNResultsList(state.state.PhaseId);
                    //ResultDTO kk = await Service.GetHubResult(obj.ConstituencyId, obj.AssemblyId, obj.PhaseId, obj.SeatTypeId);
                    await hubResultsConnection.SendAsync("SendResult", op);
                    IsDetailOpen = false;
                }
                else
                {
                    var tobj = new ToastModel
                    {
                        Title = "Error", Content = "Unable to save record", CssClass = "e-toast-danger",
                        ShowProgressBar = true, Timeout = 5000, ShowCloseButton = true
                    };
                    await ToastObj.ShowAsync(tobj);
                }
            }

            NamanigarResultsList = await Service.GetNNResultsList(state.state.PhaseId);
        }
    }

    private async Task RefreshData()
    {
        NamanigarResultsList = await Service.GetNNResultsList(state.state.PhaseId);
    }

    //public async ValueTask DisposeAsync()
    //{
    //	await hubConnection.DisposeAsync();
    //}
}