﻿@page "/assign"
@attribute [Authorize(Roles = "Administrators")]
@using ButtonType = MudBlazor.ButtonType
@using FilterType = Syncfusion.Blazor.DropDowns.FilterType
@using Size = MudBlazor.Size
@using SelectionType = Syncfusion.Blazor.Grids.SelectionType
@inherits OwningComponentBase<ConstituencyService>

<SfToast @ref="ToastObj" Title="Adaptive Tiles Meeting" Icon="e-meeting">
    <ToastPosition X="Right"></ToastPosition>
</SfToast>

<SfDialog IsModal="true" @ref="DlgConstituency" ShowCloseIcon="true" Width="700px" Visible="IsConstDlgOpen">
    <DialogTemplates>
        <Header>@ConstituencyTitle - <i>@AssemblyTitle</i></Header>
        <Content>
            <EditForm Model="BasicInfo" OnValidSubmit="UpdateConstituencyInfo">
                <div class="row">
                    <div class="col">
                        <DataAnnotationsValidator/>

                    </div>
                </div>
                <div class="row" style="margin-bottom: 10px; background-color: aliceblue; padding: 10px; border: 1px solid #aac7e0;">
                    <div class="col">Province <br/><b>@BasicInfo.Province</b></div>
                    <div class="col">District<br/><b>@BasicInfo.District</b></div>
                    @if (!string.IsNullOrEmpty(BasicInfo.Tier1))
                    {
                        <div class="col">Tier 1<br/><b>@BasicInfo.Tier1</b></div>
                    }
                    @if (!string.IsNullOrEmpty(BasicInfo.Tier2))
                    {
                        <div class="col">Tier 2<br/><b>@BasicInfo.Tier2</b></div>
                    }
                </div>
                <div class="row mb-2">
                    <div class="col-md-2">
                        <SfTextBox Placeholder="Code" FloatLabelType="FloatLabelType.Always"
                                   @bind-value="BasicInfo.Code" Readonly="true">
                        </SfTextBox>
                        <ValidationMessage For="@(() => BasicInfo.Code)"/>


                    </div>
                    <div class="col-md">
                        <SfTextBox Placeholder="Constituency" FloatLabelType="FloatLabelType.Always"
                                   @bind-value="BasicInfo.EnglishTitle" OnBlur="Translate">
                        </SfTextBox>
                        <ValidationMessage For="@(() => BasicInfo.EnglishTitle)"/>
                    </div>
                    <div class="col-md">
                        <SfTextBox dir="rtl" Placeholder="Constituency (Urdu)" FloatLabelType="FloatLabelType.Always"
                                   @bind-value="BasicInfo.UrduTitle">
                        </SfTextBox>
                        <ValidationMessage For="@(() => BasicInfo.UrduTitle)"/>
                    </div>
                </div>

                <div class="row md-2">
                    <div class="col-md">
                        <SfNumericTextBox TValue="int?" @bind-value="BasicInfo.MaleVoters" ShowSpinButton="false" Placeholder="Male Voters" FloatLabelType="FloatLabelType.Always"></SfNumericTextBox>
                    </div>
                    <div class="col-md">
                        <SfNumericTextBox TValue="int?" @bind-value="BasicInfo.FemaleVoters" ShowSpinButton="false" Placeholder="Female Voters" FloatLabelType="FloatLabelType.Always"></SfNumericTextBox>


                    </div>
                    <div class="col-md">
                        <SfNumericTextBox TValue="int?" @bind-value="BasicInfo.YouthVoters" ShowSpinButton="false" Placeholder="Youth Voters" FloatLabelType="FloatLabelType.Always"></SfNumericTextBox>
                    </div>
                    <div class="col-md">
                        <SfNumericTextBox TValue="int?" @bind-value="BasicInfo.TotalPollingStations" ShowSpinButton="false" Placeholder="Total Polling Stations" FloatLabelType="FloatLabelType.Always"></SfNumericTextBox>
                    </div>
                    <div class="col-md">
                        <SfNumericTextBox TValue="int?" @bind-value="BasicInfo.Population" ShowSpinButton="false" Placeholder="Population" FloatLabelType="FloatLabelType.Always"></SfNumericTextBox>
                    </div>

                </div>
                <div class="row mb-2">
                    <div class="col-md">
                        <SfTextBox Multiline="true" dir="rtl" @bind-value="BasicInfo.ImportantAreas" Placeholder="Major Areas" FloatLabelType="FloatLabelType.Always"></SfTextBox>
                    </div>
                </div>
                <div class="row mb-2">
                    <div class="col-md">
                        <SfTextBox Multiline="true" dir="rtl" @bind-value="BasicInfo.Problems" Placeholder="Problems" FloatLabelType="FloatLabelType.Always"></SfTextBox>
                    </div>
                </div>
                <div class="row mb-2">
                    <div class="col-md">
                        <SfTextBox Multiline="true" dir="rtl" @bind-value="BasicInfo.Trivia" Placeholder="Trivia" FloatLabelType="FloatLabelType.Always"></SfTextBox>
                    </div>
                </div>
                <div class="row mb-2">
                    <div class="col-md">
                        <SfTextBox Multiline="true" dir="rtl" @bind-value="BasicInfo.GeneralTrivia" Placeholder="General Trivia" FloatLabelType="FloatLabelType.Always"></SfTextBox>
                    </div>
                </div>
                <div class="row mb-2">
                    <div class="col">
                        <MudButton ButtonType="ButtonType.Submit" Size="Size.Small" Variant="Variant.Filled" Color="Color.Primary"
                                   StartIcon="@Icons.Material.Filled.Save">
                            Save
                        </MudButton>
                    </div>
                </div>
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>
<h3>Assign Assembly to Constituencies</h3>
<div>
    <div class="row">
        <div class="col">
            @if (DistrictsList == null)
            {
                <p>Please wait...</p>
            }
            else if (DistrictsList.Count == 0)
            {
                <p>No district found</p>
            }
            else
            {
                <SfDropDownList ShowClearButton="true" @bind-Value="DistrictId" DataSource="DistrictsList"
                                AllowFiltering="true" TItem="GeneralItemDTO" TValue="int?"
                                Placeholder="District" FloatLabelType="FloatLabelType.Always"
                                FilterType="FilterType.Contains">
                    <DropDownListFieldSettings Value="Id" Text="EnglishTitle"></DropDownListFieldSettings>
                    <DropDownListEvents TItem="GeneralItemDTO" TValue="int?" ValueChange="@OnDistrictChange"></DropDownListEvents>
                </SfDropDownList>
            }
        </div>
    </div>
    @if (DistrictId != null)
    {
        <div class="row" style="display: flex; align-items: baseline">
            <div class="col">
                @if (AssembliesList == null)
                {
                    <p>Please wait...</p>
                }
                else if (AssembliesList.Count == 0)
                {
                    <p>No assembly is defined</p>
                }
                else
                {
                    <SfDropDownList ShowClearButton="true" @bind-Value="SelectedAssemblyId" DataSource="AssembliesList" AllowFiltering="true" TItem="GeneralItemDTO" TValue="int?"
                                    Placeholder="Assembly" FloatLabelType="FloatLabelType.Always" FilterType="FilterType.Contains">
                        <DropDownListFieldSettings Value="Id" Text="EnglishTitle"></DropDownListFieldSettings>
                    </SfDropDownList>
                }
            </div>
            <div class="col-md-1">
                <MudButton Color="Color.Primary" Size="Size.Small" Variant="Variant.Filled"
                           StartIcon="@Icons.Material.Filled.Assignment"
                           Disabled="SelectedAssemblyId == null" OnClick="OnAssignButtonClick">
                    Assign
                </MudButton>
            </div>
        </div>

        <div class="row">
            <div class="col">
                @if (ConstituenciesList == null)
                {
                    <p>Please wait...</p>
                }
                else if (ConstituenciesList.Count == 0)
                {
                    <p>No Constituency is defined yet</p>
                }
                else
                {
                    <SfGrid @ref="_conGrid" FrozenColumns="2" DataSource="ConstituenciesList" AllowSorting="true" AllowFiltering="true" Width="100%" AllowSelection="true"
                            Height="calc(100vh - 265px)">
                        <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Excel"></GridFilterSettings>
                        <GridSelectionSettings Type="SelectionType.Multiple"></GridSelectionSettings>
                        <GridColumns>
                            <GridColumn Width="120px" HeaderText="Code" Field="@nameof(ConstituencyDTO.Code)"></GridColumn>
                            <GridColumn AllowSorting="true" Field=@nameof(ConstituencyDTO.EnglishTitle) AutoFit="true">
                                <Template Context="cc">
                                    @{
                                        if (cc is ConstituencyDTO co)
                                        {
                                            <SfButton CssClass="e-link" @onclick="() => OpenEditForm(co.Id)">@co.EnglishTitle</SfButton>
                                        }
                                    }
                                </Template>
                            </GridColumn>
                            <GridColumn AutoFit="true" HeaderText="LG Type" Field="@nameof(ConstituencyDTO.Type)"></GridColumn>
                            <GridColumn AutoFit="true" HeaderText="AssembliesList" Field="@nameof(ConstituencyDTO.Assembly)"></GridColumn>
                            <GridColumn AutoFit="true" HeaderText="Province" Field="@nameof(ConstituencyDTO.Province)"></GridColumn>
                            <GridColumn AutoFit="true" HeaderText="District" Field="@nameof(ConstituencyDTO.District)"></GridColumn>
                            <GridColumn AutoFit="true" HeaderText="LG Tier 1" Field="@nameof(ConstituencyDTO.Town)"></GridColumn>
                            <GridColumn AutoFit="true" HeaderText="LG Tier 2" Field="@nameof(ConstituencyDTO.UC)"></GridColumn>

                        </GridColumns>
                    </SfGrid>
                }
            </div>
        </div>
    }
</div>

@code {

    private SfToast ToastObj { get; set; }
    private List<GeneralItemDTO> AssembliesList { get; set; }
    private List<ConstituencyDTO> ConstituenciesList { get; set; }
    private SfGrid<ConstituencyDTO> _conGrid;
    private int? SelectedAssemblyId { get; set; }
    private int RowCount { get; set; }
    private SfDialog DlgConstituency { get; set; }
    private bool IsConstDlgOpen { get; } = false;
    private BasicConstituencyInfoDTO BasicInfo { get; set; } = new();
    private string ConstituencyTitle { get; set; }
    private string AssemblyTitle { get; set; }

    [CascadingParameter] private Task<AuthenticationState> authenticationStateTask { get; set; }

    protected override Task OnInitializedAsync()
    {
        return base.OnInitializedAsync();
    }

    private async Task Translate()
    {
        if (string.IsNullOrEmpty(BasicInfo.UrduTitle))
        {
            BasicInfo.UrduTitle = await Translation.TranslateText(BasicInfo.EnglishTitle);
        }
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            var user = (await authenticationStateTask).User;
            if (user.Identity is { IsAuthenticated: true })
            {
                var dd = await localStorage.GetAsync<ElectionStateDTO>(await confreader.GetSettingValue("electionkey"));
                state.SetState(dd.Value);
                await FillData();
                StateHasChanged();
            }
            else
            {
                try
                {
                    await localStorage.DeleteAsync(await confreader.GetSettingValue("electionkey"));
                    state.SetState(new ElectionStateDTO { ElectionId = 0, ElectionTitle = "", PhaseId = 0, PhaseTitle = "" });
                }
                catch (Exception)
                {
                    // ignore
                }

                NavigationManager.NavigateTo("");
            }

            StateHasChanged();
        }
    }

    private async Task FillData()
    {
        AssembliesList = await Service.GetThisElectionAssemblies(state.state.ElectionId);
        DistrictsList = await Service.GetAllDistricts(state.state.ElectionId);
    }


/*
    private void OnAssemblyChange(ChangeEventArgs<int?, GeneralItemDTO> obj)
    {
    }
*/

    private async Task OnAssignButtonClick(MouseEventArgs args)
    {
        var user = (await authenticationStateTask).User;

        var op = await Service.AssignAssemblyToConstituency(SelectedAssemblyId, _conGrid.SelectedRecords, user.Identity.Name);

        if (op == "OK" && DistrictId > 0)
        {
            ConstituenciesList = await Service.GetThisElectionConstituencies(state.state.ElectionId, (int)DistrictId);
        }
    }

    private async Task UpdateConstituencyInfo()
    {
        var user = (await authenticationStateTask).User;

        var res = await Service.SaveConstituencyBasicInfo(BasicInfo, user.Identity.Name);
        if (res == "OK")
        {
            var op = ConstituenciesList.Find(i => i.Id == BasicInfo.Id);
            if (op != null)
            {
                op.EnglishTitle = BasicInfo.EnglishTitle;
                op.UrduTitle = BasicInfo.UrduTitle;
            }

            await DlgConstituency.HideAsync();
        }
    }

    private async Task OpenEditForm(int coId)
    {
        BasicInfo = await Service.GetBasicConstituencyInfo(coId);
        AssemblyTitle = BasicInfo.AssemblyTitle;
        ConstituencyTitle = BasicInfo.Code + " - " + BasicInfo.EnglishTitle;
        await DlgConstituency.ShowAsync();
    }


    public List<GeneralItemDTO> DistrictsList { get; set; }

    public int? DistrictId { get; set; }

    private async Task OnDistrictChange(ChangeEventArgs<int?, GeneralItemDTO> args)
    {
        if (args.Value == null)
        {
            ConstituenciesList = new List<ConstituencyDTO>();
        }
        else
        {
            ConstituenciesList = await Service.GetThisElectionConstituencies(state.state.ElectionId, (int)DistrictId);
        }
    }


}