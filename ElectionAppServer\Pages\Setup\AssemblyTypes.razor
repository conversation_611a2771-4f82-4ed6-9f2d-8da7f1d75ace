﻿@page "/assemblytypes"

@using ButtonType = MudBlazor.ButtonType
@using FilterType = Syncfusion.Blazor.Grids.FilterType
@using Size = MudBlazor.Size
@inherits OwningComponentBase<AssemblyTypeService>
<PageCaption Title="Assembly/Local Body Types"></PageCaption>


<section>
    <SfDialog Width="500px" @bind-Visible="@_isDlgVisible" @ref="_dlgForm" IsModal="true" ShowCloseIcon="true">
        <DialogEvents OnOpen="BeforeOpen"></DialogEvents>
        <DialogTemplates>
            <Header>@FormTitle</Header>
            <Content>
                <EditForm Model="@_selectedObj" OnValidSubmit="@SaveAssemblyTypeData">
                    <DataAnnotationsValidator/>

                    <div class="row">
                        <div class="input-field col ">
                            <SfTextBox @bind-Value="_selectedObj.EnglishTitle" Placeholder="Title (English)" OnBlur="TranslateToUrdu" FloatLabelType="FloatLabelType.Always"></SfTextBox>
                            <ValidationMessage For="@(() => _selectedObj.EnglishTitle)"/>
                        </div>
                        <div class="input-field col ">
                            <SfTextBox @bind-Value="_selectedObj.UrduTitle" Placeholder="Title (Urdu)" FloatLabelType="FloatLabelType.Always"></SfTextBox>
                            <ValidationMessage For="@(() => _selectedObj.UrduTitle)"/>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col">
                            <MudButton ButtonType="ButtonType.Submit"
                                       Color="Color.Primary" Variant="Variant.Filled"
                                       Size="Size.Small">
                                @_saveButtonText
                            </MudButton>
                        </div>
                    </div>
                </EditForm>
            </Content>
        </DialogTemplates>
    </SfDialog>

    <SfToast @ref="_toastObj" Title="Alert" ShowProgressBar="true" Timeout="3000">
        <ToastPosition X="Right" Y="Bottom"></ToastPosition>
    </SfToast>

    <!-- AuthorizeView allows us to only show sections of the page -->
    <!-- based on the security on the current user -->
    <AuthorizeView>
        <!-- Show this section if the user is logged in -->
        <Authorized>

            @if (_objList == null)
            {
                <p>
                    <em>Loading...</em>
                </p>
            }
            else
            {
                <SfButton IsPrimary="true" CssClass="e-primary mb-2" OnClick="OpenCreateForm">Create Type</SfButton>
                <SfGrid DataSource="@_objList" AllowFiltering="true" ModelType="@_selectedObj" @ref="Grid" Width="100%">
                    <GridFilterSettings Type="FilterType.Excel"></GridFilterSettings>
                    <GridColumns>
                        <GridColumn AutoFit="true" Field="UrduTitle" HeaderText="Urdu Title" TextAlign="TextAlign.Right"></GridColumn>
                        <GridColumn AutoFit="true" Field="EnglishTitle" HeaderText="English Title"></GridColumn>
                        <GridColumn Width="150px" HeaderText="Actions" AllowFiltering="false">
                            <Template Context="ss">
                                @{
                                    if (ss is AssemblyType kk)
                                    {
                                        <SfButton IsPrimary="true" CssClass="e-danger" OnClick="@(() => DeleteRecord(kk.Id))">
                                            <i class="fas fa-trash-alt"></i>
                                        </SfButton>
                                        <SfButton IsPrimary="true" CssClass="e-primary" OnClick="@(() => OpenEditForm(kk))">
                                            <i class="fas fa-pencil-alt"></i>
                                        </SfButton>
                                    }
                                }
                            </Template>
                        </GridColumn>
                    </GridColumns>
                </SfGrid>
            }
        </Authorized>
        <!-- Show this section if the user is not logged in -->
        <NotAuthorized>
            <p>You're not signed in.</p>
        </NotAuthorized>
    </AuthorizeView>
</section>