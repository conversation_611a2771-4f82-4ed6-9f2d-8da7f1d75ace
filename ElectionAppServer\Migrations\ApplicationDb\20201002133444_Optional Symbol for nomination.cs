﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace ElectionAppServer.Migrations.ApplicationDb
{
	public partial class OptionalSymbolfornomination : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.AlterColumn<int>(
				 name: "SymbolId",
				 table: "Nominations",
				 nullable: true,
				 oldClrType: typeof(int),
				 oldType: "int");
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.AlterColumn<int>(
				 name: "SymbolId",
				 table: "Nominations",
				 type: "int",
				 nullable: false,
				 oldClrType: typeof(int),
				 oldNullable: true);
		}
	}
}
