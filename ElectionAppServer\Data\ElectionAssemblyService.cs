﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ElectionAppServer.DTO;
using ElectionAppServer.Model;
using Microsoft.EntityFrameworkCore;

namespace ElectionAppServer.Data;

public class ElectionAssemblyService(IDbContextFactory<ApplicationDbContext> contextFactory)
{
    public Task<string> Delete(int id)
    {
        using var dc = contextFactory.CreateDbContext();
        var exist = (from aa in dc.Nominations where aa.ElectionAssemblyId == id select aa).Any();

        if (exist)
            throw new Exception("Nomination exist on this assembly");

        var sts = (from aa in dc.ElectionAssemblySeats where aa.ElectionAssemblyId == id select aa).ToList();
        dc.ElectionAssemblySeats.RemoveRange(sts);
        dc.SaveChanges();

        var q = (from aa in dc.ElectionAssemblies where aa.Id == id select aa).First();
        dc.ElectionAssemblies.Remove(q);
        dc.SaveChanges();
        return Task.FromResult("OK");
    }

    public Task<List<GeneralItemDTO>> GetAssemblyTypes()
    {
        using var dc = contextFactory.CreateDbContext();
        var q = (from aa in dc.AssemblyTypes
            orderby aa.EnglishTitle
            select new GeneralItemDTO { Id = aa.Id, EnglishTitle = aa.EnglishTitle }).ToList();

        return Task.FromResult(q);
    }

    public Task<List<ConstituencyTypeDTO>> GetConstituencyTypes(ElectionType et)
    {
        using var dc = contextFactory.CreateDbContext();
        if (et == ElectionType.LocalBody)
        {
            List<ConstituencyTypeDTO> cc =
            [
                new() { Id = (int)StructureType.Town, Title = "LG Tier 1" },
                new() { Id = (int)StructureType.UnionCouncil, Title = "LG Tier 2" },
                new() { Id = (int)StructureType.Ward, Title = "LG Tier 3" }
            ];
            return Task.FromResult(cc);
        }
        else
        {
            List<ConstituencyTypeDTO> cc =
            [
                new() { Id = (int)StructureType.NA, Title = "National Assembly" },
                new() { Id =(int) StructureType.PA, Title = "Provincial Assembly" }
            ];
            return Task.FromResult(cc);
        }
    }

    public Task<string> GetElectionTitle(int electionId)
    {
        using var dc = contextFactory.CreateDbContext();
        var q = (from aa in dc.Elections where aa.Id == electionId select aa).FirstOrDefault();

        if (q != null)
            return Task.FromResult($"{q.EnglishTitle}");
        return Task.FromResult(string.Empty);
    }

    public Task<List<ElectionAssemblyDTO>> GetList(int electionId)
    {
        using var dc = contextFactory.CreateDbContext();
        var q = (from aa in dc.ElectionAssemblies
            where aa.ElectionId == electionId
            orderby aa.EnglishTitle
            select new ElectionAssemblyDTO
            {
                Id = aa.Id,
                EnglishTitle = aa.EnglishTitle,
                UrduTitle = aa.UrduTitle,
                AssemblyType = aa.AssemblyType.UrduTitle,
                AssemblyTypeId = aa.AssemblyTypeId,
                ReserveSeats = aa.ReserveSeats ?? 0,
                WomenSeats = aa.WomenSeats ?? 0,
                CreatedBy = aa.CreatedBy,
                CreatedOn = aa.CreatedDate.ToString("d MMM, yyyy h:mm"),
                ModifiedBy = aa.ModifiedBy ?? string.Empty,
                ModifiedOn =
                    aa.ModifiedDate != null ? aa.ModifiedDate.Value.ToString("d MMM, yyyy h:mm") : string.Empty,
                NominationType =
                    aa.NominationType == NominationType.Panel
                        ? "Panel"
                        : aa.NominationType == NominationType.Candidate
                            ? "Candidate"
                            : "Not defined",
                NominationTypeId = (int)aa.NominationType
                //SeatConfigurationId = (int)aa.SeatConfigurationType,
                //SeatConfiguration = aa.SeatConfigurationType == SeatConfigurationType.SingleSeat ? "Single Seat Per Constituency" :
                //                    aa.SeatConfigurationType == SeatConfigurationType.MultiSeat ? "Multi Seat Per Constituency" :
                //									 aa.SeatConfigurationType == SeatConfigurationType.MultiWinner ? "Multi Winner" : ""
            }).ToList();

        //foreach (var i in q)
        //{
        //	//var ss = (from aa in dc.ElectionAssemblySeats
        //	//          where aa.ElectionAssemblyId == i.Id
        //	//          select new { aa.SeatTypeId, aa.SeatType.UrduTitle, }).ToList();

        //	var ss = (from aa in dc.ElectionAssemblySeats
        //				 where aa.ElectionAssemblyId == i.Id
        //				 select new { aa.SeatTypeId, aa.ConstituencyType, aa.SeatType.EnglishTitle, aa.SeatCount }).ToList();

        //	var cntUC = (from aa in dc.Structures.OfType<UnionCouncil>()
        //					 where aa.AssemblyId == i.Id
        //					 select aa).Count();

        //	var cntWrd = (from aa in dc.Structures.OfType<Ward>()
        //					  where aa.AssemblyId == i.Id
        //					  select aa).Count();

        //	var cntNAs = (from aa in dc.Structures.OfType<NationalAssemblyHalka>()
        //					  where aa.AssemblyId == i.Id
        //					  select aa).Count();

        //	var cntPAs = (from aa in dc.Structures.OfType<ProvincialAssemblyHalka>()
        //					  where aa.AssemblyId == i.Id
        //					  select aa).Count();
        //	var cc = "";
        //	if (cntUC > 0) cc = $"{cntUC} UCs<br>";
        //	if (cntWrd > 0) cc += $"{cntWrd} Wards<br>"; ;
        //	if (cntNAs > 0) cc += $"{cntNAs} NAs<br>"; ;
        //	if (cntPAs > 0) cc += $"{cntPAs} PAs<br>"; ;
        //	var seats = "";
        //	foreach (var s in ss)
        //	{
        //		seats += $"{s.ConstituencyType.ToString()} -<br><i>{s.EnglishTitle}</i> (<b>{s.SeatCount ?? 0}</b>)<br><br>";
        //	}

        //	if (seats != "")
        //	{
        //		seats = seats.Substring(0, seats.Length - 8);
        //	}
        //	i.SeatsList = seats.Replace("UnionCouncil", "LG Tier 2").Replace("Ward", "LG Tier 3");
        //	i.Constituencies = cc;
        //}

        //foreach (var i in q)
        //{
        //	i.ConsSeatTypeList = (from aa in dc.ElectionAssemblySeats
        //								 where aa.ElectionAssemblyId == i.Id
        //								 select new ConSeatDTO
        //								 {
        //									 ConstituencyType = aa.ConstituencyType.ToString(),
        //									 ConstituencyTypeId = (int)aa.ConstituencyType,
        //									 IsDb = true,
        //									 SeatType = aa.SeatType.EnglishTitle,
        //									 SeatTypeId = aa.SeatTypeId,
        //									 SeatCount = aa.SeatCount ?? 0
        //								 }).ToList();
        //}

        return Task.FromResult(q);
    }

    public Task<List<GeneralItemDTO>> GetSeatTypes()
    {
        using var dc = contextFactory.CreateDbContext();
        var q = (from aa in dc.SeatTypes
            orderby aa.EnglishTitle
            select new GeneralItemDTO { Id = aa.Id, EnglishTitle = aa.EnglishTitle }).ToList();

        return Task.FromResult(q);
    }

    //   return Task.FromResult(electionId);
    //}
    public Task<bool> IsNominationExist(int assemblyId, int seatTypeId)
    {
        using var dc = contextFactory.CreateDbContext();
        var q = (from aa in dc.Nominations
            where aa.ElectionAssemblyId == assemblyId && aa.SeatTypeId == seatTypeId
            select aa).Any();

        return Task.FromResult(q);
    }

    public Task<string> Save(ElectionAssemblyDTO obj, string user)
    {
        using var dc = contextFactory.CreateDbContext();
        using var tran = dc.Database.BeginTransaction();
        try
        {
            if (obj.Id == 0)
            {
                var m = new ElectionAssembly
                {
                    AssemblyTypeId = (int)obj.AssemblyTypeId,
                    CreatedBy = user,
                    CreatedDate = DateTime.Now,
                    ElectionId = obj.ElectionId,
                    EnglishTitle = obj.EnglishTitle,
                    UrduTitle = obj.UrduTitle,
                    ReserveSeats = obj.ReserveSeats,
                    WomenSeats = obj.WomenSeats,
                    NominationType = (NominationType)obj.NominationTypeId
                    //SeatConfigurationType = (SeatConfigurationType)obj.SeatConfigurationId
                };
                dc.ElectionAssemblies.Add(m);
                dc.SaveChanges();
                if (obj.ConsSeatTypeList.Count == 0)
                {
                    var asmType = dc.AssemblyTypes.Find(obj.AssemblyTypeId).EnglishTitle;
                    var seatTypeObj = dc.SeatTypes.FirstOrDefault(st => st.EnglishTitle == "Member Assembly");
                    if (asmType == "National Assembly" && seatTypeObj != null)
                    {
                        var dd = new ElectionAssemblySeat
                        {
                            ElectionAssemblyId = m.Id,
                            CreatedBy = user,
                            CreatedDate = DateTime.Now,
                            ConstituencyType = StructureType.NA,
                            SeatTypeId = seatTypeObj.Id,
                            SeatCount = 1
                        };
                        dc.ElectionAssemblySeats.Add(dd);
                        dc.SaveChanges();
                    }
                    else if (asmType == "Provincial Assembly" && seatTypeObj != null)
                    {
                        var dd = new ElectionAssemblySeat
                        {
                            ElectionAssemblyId = m.Id,
                            CreatedBy = user,
                            CreatedDate = DateTime.Now,
                            ConstituencyType = StructureType.PA,
                            SeatTypeId = seatTypeObj.Id,
                            SeatCount = 1
                        };
                        dc.ElectionAssemblySeats.Add(dd);
                        dc.SaveChanges();
                    }
                    else if (asmType == "Senate" && seatTypeObj != null)
                    {
                        var dd = new ElectionAssemblySeat
                        {
                            ElectionAssemblyId = m.Id,
                            CreatedBy = user,
                            CreatedDate = DateTime.Now,
                            ConstituencyType = StructureType.PA,
                            SeatTypeId = seatTypeObj.Id,
                            SeatCount = 1
                        };
                        dc.ElectionAssemblySeats.Add(dd);
                        dc.SaveChanges();
                    }
                }

                foreach (var d in obj.ConsSeatTypeList)
                {
                    var dd = new ElectionAssemblySeat
                    {
                        ElectionAssemblyId = m.Id,
                        CreatedBy = user,
                        CreatedDate = DateTime.Now,
                        ConstituencyType = (StructureType)d.ConstituencyTypeId,
                        SeatTypeId = d.SeatTypeId,
                        SeatCount = d.SeatCount
                    };
                    dc.ElectionAssemblySeats.Add(dd);
                    dc.SaveChanges();
                }

                tran.Commit();
                return Task.FromResult("OK");
            }
            else
            {
                var m = (from aa in dc.ElectionAssemblies where aa.Id == obj.Id select aa).First();

                m.AssemblyTypeId = (int)obj.AssemblyTypeId;
                m.EnglishTitle = obj.EnglishTitle.Trim();
                m.ReserveSeats = obj.ReserveSeats;
                m.WomenSeats = obj.WomenSeats;
                m.ModifiedBy = user;
                m.ModifiedDate = DateTime.Now;
                m.UrduTitle = obj.UrduTitle.Trim();
                m.NominationType = (NominationType)obj.NominationTypeId;
                //m.SeatConfigurationType = (SeatConfigurationType)obj.SeatConfigurationId;
                dc.SaveChanges();

                // Remove previously created seat type association
                var rng = (from aa in dc.ElectionAssemblySeats where aa.ElectionAssemblyId == obj.Id select aa)
                    .ToList(
                    );
                dc.ElectionAssemblySeats.RemoveRange(rng);
                dc.SaveChanges();

                foreach (var d in obj.ConsSeatTypeList)
                {
                    var dd = new ElectionAssemblySeat
                    {
                        ElectionAssemblyId = m.Id,
                        CreatedBy = user,
                        CreatedDate = DateTime.Now,
                        ConstituencyType = (StructureType)d.ConstituencyTypeId,
                        SeatTypeId = d.SeatTypeId,
                        SeatCount = d.SeatCount
                    };
                    dc.ElectionAssemblySeats.Add(dd);
                    dc.SaveChanges();
                }

                tran.Commit();
                return Task.FromResult("OK");
            }
        }
        catch (Exception)
        {
            tran.Rollback();
            throw;
        }
    }

    public Task<ElectionType> GetElectionTypeById(int electionId)
    {
        using var dc = contextFactory.CreateDbContext();
        var q = (from aa in dc.Elections where aa.Id == electionId select aa).FirstOrDefault();
        if (q == null)
            throw new Exception("Election not found");
        if (q.ElectionType != null)
            return Task.FromResult(q.ElectionType.Value);
        throw new Exception("Election Type is not defined");
    }

    public Task<ElectionAssemblyDTO> GetElectionById(int id)
    {
        using var dc = contextFactory.CreateDbContext();

        var q = (from aa in dc.ElectionAssemblies
            where aa.Id == id
            select new ElectionAssemblyDTO
            {
                AssemblyType = aa.AssemblyType.EnglishTitle,
                AssemblyTypeId = aa.AssemblyTypeId,
                ElectionId = aa.ElectionId,
                EnglishTitle = aa.EnglishTitle,
                SeatsList = string.Empty,
                UrduTitle = aa.UrduTitle,
                ReserveSeats = aa.ReserveSeats,
                WomenSeats = aa.WomenSeats,
                NominationTypeId = (int)aa.NominationType,
                Id = id,
                ConsSeatTypeList =
                    (from cc in dc.ElectionAssemblySeats
                        where cc.ElectionAssemblyId == id
                        select new ConSeatDTO
                        {
                            ConstituencyTypeId = (int)cc.ConstituencyType,
                            IsDb = true,
                            SeatTypeId = cc.SeatTypeId,
                            SeatCount = cc.SeatCount ?? 0
                        }).ToList()
            }).FirstOrDefault();

        return Task.FromResult(q);
    }

    public Task<string> Save2(ElectionAssemblyDTO obj, string user)
    {
        return Task.FromResult("OK");

        //using (var tran = dc.Database.BeginTransaction())
        //{
        //   try
        //   {
        //      if (obj.Id == 0)
        //      {
        //         var e = new ElectionAssembly
        //         {
        //            Id = obj.Id,
        //            AssemblyTypeId = (int)obj.AssemblyTypeId,
        //            ElectionId = obj.ElectionId,
        //            EnglishTitle = obj.EnglishTitle,
        //            UrduTitle = obj.UrduTitle,
        //            CreatedBy = user,
        //            CreatedDate = DateTime.Now,
        //            //ModifiedBy = user,
        //            //ModifiedDate = DateTime.Now
        //         };
        //         dc.Add(e);
        //         dc.SaveChanges();
        //         obj.Id = e.Id;
        //      }
        //      else
        //      {
        //         var e = (from aa in dc.ElectionAssemblies
        //                  where aa.Id == obj.Id
        //                  select aa).FirstOrDefault();
        //         e.EnglishTitle = obj.EnglishTitle;
        //         e.UrduTitle = obj.UrduTitle;
        //         e.AssemblyTypeId = (int)obj.AssemblyTypeId;
        //         e.ModifiedBy = user;
        //         e.ModifiedDate = DateTime.Now;
        //         dc.SaveChanges();
        //      }

        //      // Get All Seats from DV against election assembly
        //      var seatsInDb = (from aa in dc.ElectionAssemblySeats
        //                       where aa.ElectionAssemblyId == obj.Id
        //                       select aa).ToList();
        //      // Check each seats
        //      foreach (var s in seatsInDb)
        //      {
        //         // if exist in database but user removed from selection
        //         var isExist = (from aa in obj
        //                        where aa.SeatTypeId == s.SeatTypeId
        //                        select aa).Any();
        //         if (!isExist)
        //         {
        //            // if this seat alreay used in nomination then it is not allowed to delete
        //            var isNominationOnSeat = (from aa in dc.Nominations
        //                                      where aa.ElectionAssemblyId == obj.Id
        //                                      && aa.SeatTypeId == s.SeatTypeId
        //                                      select aa).Any();
        //            if (isNominationOnSeat)
        //            {
        //               tran.Rollback();
        //               throw new Exception("You cannot remove Seat Type, Nomination exist on this seat type");
        //            }
        //         }
        //      }

        //      // Remove All Saved Seat Types
        //      var sts = (from aa in dc.ElectionAssemblySeats
        //                 where aa.ElectionAssemblyId == obj.Id
        //                 select aa).ToList();

        //      dc.ElectionAssemblySeats.RemoveRange(sts);
        //      dc.SaveChanges();

        //      foreach (var s in obj.Seats)
        //      {
        //         var kk = new ElectionAssemblySeat
        //         {
        //            ElectionAssemblyId = obj.Id,
        //            SeatTypeId = s.SeatTypeId,
        //            CreatedBy = user,
        //            CreatedDate = DateTime.Now
        //         };
        //         dc.ElectionAssemblySeats.Add(kk);
        //         dc.SaveChanges();
        //      }
        //      tran.Commit();
        //      return Task.FromResult("OK");
        //   }
        //   catch (Exception ex)
        //   {
        //      tran.Rollback();
        //      var msg = "";
        //      msg = ex.Message;
        //      if (ex.InnerException != null)
        //      {
        //         msg += " - Detail 1: " + ex.InnerException.Message;
        //         if (ex.InnerException.InnerException != null)
        //         {
        //            msg += " -- Detail 2: " + ex.InnerException.InnerException.Message;
        //         }
        //      }
        //      throw new Exception(msg);
        //   }
        //}
    }

    //public Task<int> GetElectionId(StructureType st, int Id)
    //{
    //   int electionId = 0;
    //   if (st == StructureType.Province)
    //   {
    //      electionId = (from aa in dc.Structures.OfType<Province>()
    //                    where aa.Id == Id
    //                    select aa.ElectionId).FirstOrDefault();
    //   }
    //   else if (st == StructureType.Division)
    //   {
    //      electionId = (from aa in dc.Structures.OfType<Division>()
    //                    where aa.Id == Id
    //                    select aa.Province.ElectionId).FirstOrDefault();
    //   }
    //   else if (st == StructureType.District)
    //   {
    //      electionId = (from aa in dc.Structures.OfType<District>()
    //                    where aa.Id == Id
    //                    select aa.Division.Province.ElectionId).FirstOrDefault();
    //   }
    //   else if (st == StructureType.Town)
    //   {
    //      electionId = (from aa in dc.Structures.OfType<Town>()
    //                    where aa.Id == Id
    //                    select aa.District.Division.Province.ElectionId).FirstOrDefault();
    //   }
    //   else if (st == StructureType.UnionCouncil)
    //   {
    //      electionId = (from aa in dc.Structures.OfType<UnionCouncil>()
    //                    where aa.Id == Id
    //                    select aa.Town.District.Division.Province.ElectionId).FirstOrDefault();
    //   }
    //   else if (st == StructureType.Ward)
    //   {
    //      electionId = (from aa in dc.Structures.OfType<Ward>()
    //                    where aa.Id == Id
    //                    select aa.UnionCouncil.Town.District.Division.Province.ElectionId).FirstOrDefault();
    //   }

    public Task<AssemblyRptDTO> GetAssemblyRptInfo(int assemblyId)
    {
        using var dc = contextFactory.CreateDbContext();
        var q = (from aa in dc.ElectionAssemblies
            where aa.Id == assemblyId
            select new AssemblyRptDTO
            {
                Id = aa.Id,
                EnglishTitle = aa.EnglishTitle,
                UrduTitle = aa.UrduTitle,
                ReserveSeats = aa.ReserveSeats,
                WomenSeats = aa.WomenSeats,
                TypeEnglish = aa.AssemblyType.EnglishTitle,
                TypeUrdu = aa.AssemblyType.UrduTitle,
                ElectionAssemblyType = aa.ElectionAssemblyType,
                ElectionId = aa.ElectionId,
                ElectionUrdu = aa.Election.UrduTitle,
                ElectionEnglish = aa.Election.EnglishTitle,
                Seats =
                    (from bb in dc.ElectionAssemblySeats
                        where bb.ElectionAssemblyId == assemblyId
                        select new SeatRptDTO
                        {
                            EnglishTitle = bb.SeatType.EnglishTitle,
                            UrduTitle = bb.SeatType.UrduTitle,
                            Count = bb.SeatCount
                        }).ToList(),
                Constituencies =
                    (from bb in dc.Structures
                        where bb.AssemblyId == assemblyId
                        orderby bb.EnglishTitle
                        select new ConstDetailDTO
                        {
                            Id = bb.Id,
                            Code = bb.Code,
                            EnglishTitle = $"{bb.EnglishTitle}<br><span class='urdu-sm'>{bb.UrduTitle}</span>",
                            TotalPollingStations = bb.TotalPollingStations,
                            MaleVoters = bb.MaleVoters,
                            FemaleVoters = bb.FemaleVoters,
                            Population = bb.Population
                        }).ToList(),
                Nominations =
                    (from bb in dc.Nominations
                        where bb.ElectionAssemblyId == assemblyId
                        orderby bb.Structure.EnglishTitle, bb.Structure.Code, bb.Candidate.EnglishName
                        select new NominationRptDTO
                        {
                            CandidateId = bb.CandidteId,
                            Phase = bb.ElectionPhase.Title,
                            SeatType =
                                $"{bb.SeatType.EnglishTitle}<br><span class='urdu-sm'>{bb.SeatType.UrduTitle}</span>",
                            Symbol = $"{bb.Symbol.EnglishTitle}<br><span class='urdu-sm'>{bb.Symbol.UrduTitle}</span>",
                            Votes = bb.Votes,
                            Constituency =
                                $"{bb.Structure.Code} - {bb.Structure.EnglishTitle}<br><span class='urdu-sm'>{bb.Structure.UrduTitle}</span>",
                            ConstituencyCode = bb.Structure.Code,
                            Date = bb.ElectionPhase.StartDate,
                            Weight = bb.Weight,
                            Candidate =
                                $"{bb.Candidate.EnglishName}<br><span class='urdu-sm'>{bb.Candidate.UrduName}</span>",
                            ConstituencyId = bb.StructureId,
                            Election =
                                $"{bb.ElectionPhase.Election.EnglishTitle}<br><span class='urdu-sm'>{bb.ElectionPhase.Election.UrduTitle}</span>"
                        }).ToList()
            }).FirstOrDefault();
        return Task.FromResult(q);
    }
}