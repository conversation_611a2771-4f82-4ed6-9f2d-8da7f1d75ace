﻿using Microsoft.EntityFrameworkCore.Migrations;
using System;

namespace ElectionAppServer.Migrations.ApplicationDb
{
	public partial class ElectionWiseCandidateDistrict : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.CreateTable(
				 name: "CandidateDistricts",
				 columns: table => new
				 {
					 ElectionId = table.Column<int>(type: "int", nullable: false),
					 DistrictId = table.Column<int>(type: "int", nullable: false),
					 CandidateId = table.Column<int>(type: "int", nullable: false),
					 CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "getdate()"),
					 CreatedBy = table.Column<string>(type: "varchar(450)", unicode: false, maxLength: 450, nullable: false),
					 ModifiedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
					 ModifiedBy = table.Column<string>(type: "varchar(450)", unicode: false, maxLength: 450, nullable: true)
				 },
				 constraints: table =>
				 {
					 table.PrimaryKey("PK_CandidateDistricts", x => new { x.CandidateId, x.DistrictId, x.ElectionId });
					 table.ForeignKey(
							  name: "FK_CandidateDistricts_Candidates_CandidateId",
							  column: x => x.CandidateId,
							  principalTable: "Candidates",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Cascade);
					 table.ForeignKey(
							  name: "FK_CandidateDistricts_Elections_ElectionId",
							  column: x => x.ElectionId,
							  principalTable: "Elections",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Cascade);
					 table.ForeignKey(
							  name: "FK_CandidateDistricts_Structures_DistrictId",
							  column: x => x.DistrictId,
							  principalTable: "Structures",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Restrict);
				 });

			migrationBuilder.CreateIndex(
				 name: "IX_CandidateDistricts_DistrictId",
				 table: "CandidateDistricts",
				 column: "DistrictId");

			migrationBuilder.CreateIndex(
				 name: "IX_CandidateDistricts_ElectionId",
				 table: "CandidateDistricts",
				 column: "ElectionId");
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropTable(
				 name: "CandidateDistricts");
		}
	}
}
