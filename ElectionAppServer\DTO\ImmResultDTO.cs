﻿using System.Collections.Generic;

namespace ElectionAppServer.DTO;

public class ImmResultDTO
{
    public string constituency_name { get; set; }
    public int constituency_id { get; set; }
    public int number_of_polling_station { get; set; }
    public string polling_station_name { get; set; }
    public int polling_station_id { get; set; }
    public List<ImmCandidateInfoDTO> candidate_info { get; set; }
}

public class ImmCandidateInfoDTO
{
    public int candidate_id { get; set; }
    public int votes { get; set; }
    public int party_id { get; set; }
    public int nomination_id { get; set; }
    public string status { get; set; }
}