﻿using ElectionAppServer.DTO;
using Microsoft.AspNetCore.SignalR;
using System.Threading.Tasks;

namespace ElectionAppServer.Hubs;

public class PSResultsHub : Hub
{
	public async Task SendPSResult(ResultDTO result)
	{
		await Clients.All.SendAsync("ReceivePSResult", result);
	}

	public async Task SendPSPublishNotification(ResultDTO result)
	{
		await Clients.All.SendAsync("ReceivePSNotification", result);
	}
}