﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace ElectionAppServer.Migrations.ApplicationDb
{
	public partial class updatepollingstations : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropForeignKey(
				 name: "FK_Candidates_Parties_PartyId",
				 table: "Candidates");

			migrationBuilder.DropIndex(
				 name: "IX_Structures_EnglishTitle_TownId",
				 table: "Structures");

			migrationBuilder.DropIndex(
				 name: "IX_Candidates_PartyId",
				 table: "Candidates");

			migrationBuilder.DropColumn(
				 name: "PartyId",
				 table: "Candidates");

			migrationBuilder.AlterColumn<string>(
				 name: "EnglishTitle",
				 table: "Symbols",
				 unicode: false,
				 maxLength: 200,
				 nullable: false,
				 oldClrType: typeof(string),
				 oldType: "nvarchar(200)",
				 oldMaxLength: 200);

			migrationBuilder.AlterColumn<string>(
				 name: "Code",
				 table: "Structures",
				 maxLength: 20,
				 nullable: true,
				 oldClrType: typeof(string),
				 oldType: "varchar(20)",
				 oldUnicode: false,
				 oldMaxLength: 20,
				 oldNullable: true);

			migrationBuilder.AlterColumn<string>(
				 name: "UrduTitle",
				 table: "Parties",
				 maxLength: 200,
				 nullable: false,
				 oldClrType: typeof(string),
				 oldType: "nvarchar(200)",
				 oldMaxLength: 200,
				 oldNullable: true);

			migrationBuilder.AlterColumn<string>(
				 name: "ShortEnglishTitle",
				 table: "Parties",
				 unicode: false,
				 maxLength: 50,
				 nullable: true,
				 oldClrType: typeof(string),
				 oldType: "nvarchar(50)",
				 oldMaxLength: 50,
				 oldNullable: true);

			migrationBuilder.AlterColumn<string>(
				 name: "EnglishTitle",
				 table: "Parties",
				 unicode: false,
				 maxLength: 200,
				 nullable: false,
				 oldClrType: typeof(string),
				 oldType: "nvarchar(200)",
				 oldMaxLength: 200,
				 oldNullable: true);

			migrationBuilder.AlterColumn<string>(
				 name: "UrduTitle",
				 table: "Coalitions",
				 maxLength: 200,
				 nullable: false,
				 oldClrType: typeof(string),
				 oldType: "nvarchar(max)",
				 oldNullable: true);

			migrationBuilder.AlterColumn<string>(
				 name: "EnglishTitle",
				 table: "Coalitions",
				 unicode: false,
				 maxLength: 200,
				 nullable: false,
				 oldClrType: typeof(string),
				 oldType: "nvarchar(max)",
				 oldNullable: true);

			migrationBuilder.CreateTable(
				 name: "PollingStation",
				 columns: table => new
				 {
					 Id = table.Column<int>(nullable: false)
							.Annotation("SqlServer:Identity", "1, 1"),
					 Code = table.Column<string>(unicode: false, maxLength: 200, nullable: false),
					 EnglishTitle = table.Column<string>(unicode: false, maxLength: 200, nullable: false),
					 UrduTitle = table.Column<string>(maxLength: 200, nullable: false),
					 MaleVoters = table.Column<int>(nullable: false),
					 FemaleVoters = table.Column<int>(nullable: false),
					 ConstituencyId = table.Column<int>(nullable: false)
				 },
				 constraints: table =>
				 {
					 table.PrimaryKey("PK_PollingStation", x => x.Id);
					 table.ForeignKey(
							  name: "FK_PollingStation_Structures_ConstituencyId",
							  column: x => x.ConstituencyId,
							  principalTable: "Structures",
							  principalColumn: "Id",
							  onDelete: ReferentialAction.Restrict);
				 });

			migrationBuilder.CreateIndex(
				 name: "IX_Structures_EnglishTitle_TownId",
				 table: "Structures",
				 columns: new[] { "EnglishTitle", "TownId" },
				 unique: true,
				 filter: "[EnglishTitle] IS NOT NULL AND [TownId] IS NOT NULL");

			migrationBuilder.CreateIndex(
				 name: "IX_PollingStation_ConstituencyId_Code",
				 table: "PollingStation",
				 columns: new[] { "ConstituencyId", "Code" },
				 unique: true);

			migrationBuilder.CreateIndex(
				 name: "IX_PollingStation_ConstituencyId_EnglishTitle",
				 table: "PollingStation",
				 columns: new[] { "ConstituencyId", "EnglishTitle" },
				 unique: true);

			migrationBuilder.CreateIndex(
				 name: "IX_PollingStation_ConstituencyId_UrduTitle",
				 table: "PollingStation",
				 columns: new[] { "ConstituencyId", "UrduTitle" },
				 unique: true);
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropTable(
				 name: "PollingStation");

			migrationBuilder.DropIndex(
				 name: "IX_Structures_EnglishTitle_TownId",
				 table: "Structures");

			migrationBuilder.AlterColumn<string>(
				 name: "EnglishTitle",
				 table: "Symbols",
				 type: "nvarchar(200)",
				 maxLength: 200,
				 nullable: false,
				 oldClrType: typeof(string),
				 oldUnicode: false,
				 oldMaxLength: 200);

			migrationBuilder.AlterColumn<string>(
				 name: "Code",
				 table: "Structures",
				 type: "varchar(20)",
				 unicode: false,
				 maxLength: 20,
				 nullable: true,
				 oldClrType: typeof(string),
				 oldMaxLength: 20,
				 oldNullable: true);

			migrationBuilder.AlterColumn<string>(
				 name: "UrduTitle",
				 table: "Parties",
				 type: "nvarchar(200)",
				 maxLength: 200,
				 nullable: true,
				 oldClrType: typeof(string),
				 oldMaxLength: 200);

			migrationBuilder.AlterColumn<string>(
				 name: "ShortEnglishTitle",
				 table: "Parties",
				 type: "nvarchar(50)",
				 maxLength: 50,
				 nullable: true,
				 oldClrType: typeof(string),
				 oldUnicode: false,
				 oldMaxLength: 50,
				 oldNullable: true);

			migrationBuilder.AlterColumn<string>(
				 name: "EnglishTitle",
				 table: "Parties",
				 type: "nvarchar(200)",
				 maxLength: 200,
				 nullable: true,
				 oldClrType: typeof(string),
				 oldUnicode: false,
				 oldMaxLength: 200);

			migrationBuilder.AlterColumn<string>(
				 name: "UrduTitle",
				 table: "Coalitions",
				 type: "nvarchar(max)",
				 nullable: true,
				 oldClrType: typeof(string),
				 oldMaxLength: 200);

			migrationBuilder.AlterColumn<string>(
				 name: "EnglishTitle",
				 table: "Coalitions",
				 type: "nvarchar(max)",
				 nullable: true,
				 oldClrType: typeof(string),
				 oldUnicode: false,
				 oldMaxLength: 200);

			migrationBuilder.AddColumn<int>(
				 name: "PartyId",
				 table: "Candidates",
				 type: "int",
				 nullable: true);

			migrationBuilder.CreateIndex(
				 name: "IX_Structures_EnglishTitle_TownId",
				 table: "Structures",
				 columns: new[] { "EnglishTitle", "TownId" },
				 unique: true,
				 filter: "[TownId] IS NOT NULL");

			migrationBuilder.CreateIndex(
				 name: "IX_Candidates_PartyId",
				 table: "Candidates",
				 column: "PartyId");

			migrationBuilder.AddForeignKey(
				 name: "FK_Candidates_Parties_PartyId",
				 table: "Candidates",
				 column: "PartyId",
				 principalTable: "Parties",
				 principalColumn: "Id",
				 onDelete: ReferentialAction.Restrict);
		}
	}
}
