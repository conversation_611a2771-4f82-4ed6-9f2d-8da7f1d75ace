﻿@page "/tickerpp"
@attribute [Authorize(Roles = "Ticker Desk")]

@inject NavigationManager NavigationManager
@inject UserManager<ApplicationUser> _UserManager
@inject RoleManager<IdentityRole> _RoleManager
@inject AuthenticationStateProvider AuthenticationStateProvider

@inherits OwningComponentBase<ElectionResultsDataService>
@inject ClipboardService ClipboardService



@*<PageCaption Title="Live Ticker"></PageCaption>*@
<MudText Typo="Typo.h5">Live Ticker</MudText>

<section>


    <SfTab>
        <TabItems>
            @foreach (var dis in DistrictList)
            {
                <TabItem>
                    <ChildContent>
                        <TabHeader Text="@dis.EnglishTitle"></TabHeader>
                    </ChildContent>
                    <ContentTemplate>
                        <div class="row" style="direction: rtl;">
                            @foreach (var r in results)
                            {
                                if (r.District == dis.EnglishTitle)
                                {
                                    <div class="col-12 urdu-column ticker-block">
                                        <img src=images/copy.png align="left" style="cursor:pointer" @onclick=@(() => CopyToClipboard(r.Ticker))/>
                                        @((MarkupString)r.Ticker)

                                    </div>
                                    <hr/>
                                }
                            }
                        </div>
                    </ContentTemplate>
                </TabItem>
            }


        </TabItems>
    </SfTab>

</section>