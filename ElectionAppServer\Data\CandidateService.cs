﻿using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using Dapper;
using ElectionAppServer.DTO;
using ElectionAppServer.Model;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace ElectionAppServer.Data;

public class CandidateService(IDbContextFactory<ApplicationDbContext> contextFactory)
{
    // private  readonly  ApplicationDbContext dc;
    // dc  =  context;

    // (ApplicationDbContext  context)

    public Task<string> Delete(int id, int electionId)
    {
        using var dc = contextFactory.CreateDbContext();
        var q = (from aa in dc.Nominations
            where aa.CandidteId == id
            select aa).Any();

        if (q)
            throw new Exception("You cannot delete this record, Candidate has nomination profile");


        var pas = (from aa in dc.PartyAffiliations
            where aa.CandidateId == id
            select aa).ToList();
        dc.RemoveRange(pas);
        dc.SaveChanges();

        var pas2 = (from aa in dc.PoliticalCareers
            where aa.CandidateId == id
            select aa).ToList();

        dc.RemoveRange(pas2);
        dc.SaveChanges();


        var c = (from aa in dc.Candidates
            where aa.Id == id
            select aa).First();

        dc.Remove(c);
        dc.SaveChanges();
        UpdateCandidateSearchLog(id, electionId, dc);

        return Task.FromResult("OK");
    }

    public Task<CandidateDTO> GetCandidateNominations(int electionId, int candidateId)
    {
        //throw new NotImplementedException();
        using var dc = contextFactory.CreateDbContext();

        SqlDataAdapter da = new("spGetCandidatsNominations", dc.Database.GetConnectionString());
        da.SelectCommand.CommandType = CommandType.StoredProcedure;
        da.SelectCommand.Parameters.AddWithValue("@ElectionId", electionId);
        da.SelectCommand.Parameters.AddWithValue("@CandidateId", candidateId);
        var dt = new DataTable();
        da.Fill(dt);
        //List<CandidateDTO> op = new();
        if (dt.Rows.Count > 0)
        {
            var r = dt.Rows[0];
            CandidateDTO cc = new()
            {
                Id = int.Parse(r["Id"].ToString()),
                FullName = r["FullName"].ToString(),
                CandidateTypeStr = r["CandidateType"].ToString(),
                EnglishName = r["EnglishName"].ToString(),
                Gender = int.Parse(r["Gender"].ToString() == "" ? "1" : r["Gender"].ToString()),
                UrduFatherName = r["UrduFatherName"].ToString(),
                UrduName = r["UrduName"].ToString(),
                CreatedBy = r["CreatedBy"].ToString(),
                ModifiedBy = r["ModifiedBy"].ToString(),
                CreatedOn = DateTime.Parse(r["CreatedOn"].ToString()).ToString("d MMM, yyyy h:mm"),
                Constituencies = r["Constituencies"].ToString(),
                DisableDelete = r["NominationCount"].ToString() != "0",
                District = r["District"].ToString()
            };
            cc.PictureURL = $"/media/candidates/{cc.Id}.jpg";
            cc.CreatedOn = r["CreatedOn"].ToString();
            cc.ModifiedOn = r["ModifiedOn"].ToString();
            cc.DateOfBirthStr = r["DateOfBirth"].ToString();
            cc.IsFreshStr = r["Status"].ToString();

            //\wwwroot\media\candidates\Blank.jpg
            cc.PictureURL = !File.Exists(Directory.GetCurrentDirectory() + $@"\wwwroot\media\candidates\{cc.Id}.jpg")
                ? "/media/candidates/Blank.jpg"
                : $"/media/candidates/{cc.Id}.jpg";

            return Task.FromResult(cc);
        }

        return null;
    }


    public Task<List<CandidateDTO>> GetAllCandidates4(int electionId, int candidateId, string name)
    {
        using var dc = contextFactory.CreateDbContext();
        var pram = new { ElectionId = electionId, CandidateId = candidateId, Name = name };
        var con = dc.Database.GetDbConnection();
        var res = con.Query<CandidateDTO>("spGetAllCandidats", pram, commandType: CommandType.StoredProcedure).ToList();

        foreach (var cc in res)
            cc.PictureURL = !File.Exists(Directory.GetCurrentDirectory() + $"\\wwwroot\\media\\candidates\\{cc.Id}.jpg")
                ? "/media/candidates/Blank.jpg"
                : $"/media/candidates/{cc.Id}.jpg";
        return Task.FromResult(res);
    }

    public Task<List<CandidateDTO>> GetAllCandidates(int electionId, int candidateId, string name)
    {
        using var dc = contextFactory.CreateDbContext();
        SqlDataAdapter da = new("spGetAllCandidats", dc.Database.GetConnectionString());
        da.SelectCommand.CommandType = CommandType.StoredProcedure;
        da.SelectCommand.Parameters.AddWithValue("@ElectionId", electionId);
        da.SelectCommand.Parameters.AddWithValue("@candidateId", candidateId);
        da.SelectCommand.Parameters.AddWithValue("@name", name);
        var dt = new DataTable();
        da.Fill(dt);
        List<CandidateDTO> op = new();
        for (var i = 0; i < dt.Rows.Count; i++)
        {
            var r = dt.Rows[i];
            CandidateDTO cc = new()
            {
                Id = int.Parse(r["Id"].ToString()),
                FullName = r["FullName"].ToString(),
                CandidateTypeStr = r["CandidateType"].ToString(),
                EnglishName = r["EnglishName"].ToString(),
                Gender = int.Parse(r["Gender"].ToString() == "" ? "1" : r["Gender"].ToString()),
                UrduFatherName = r["UrduFatherName"].ToString(),
                UrduName = r["UrduName"].ToString(),
                CreatedBy = r["CreatedBy"].ToString(),
                ModifiedBy = r["ModifiedBy"].ToString(),
                CreatedOn = DateTime.Parse(r["CreatedOn"].ToString()).ToString("d MMM, yyyy h:mm"),
                Constituencies = r["Constituencies"].ToString(),
                DisableDelete = r["NominationCount"].ToString() != "0",
                District = r["District"].ToString(),
                IsBigName = r["IsBigName"].ToString() == "Yes"
            };
            cc.PictureURL = $"/media/candidates/{cc.Id}.jpg";
            cc.CreatedOn = r["CreatedOn"].ToString();
            cc.ModifiedOn = r["ModifiedOn"].ToString();
            cc.DateOfBirthStr = r["DateOfBirth"].ToString();
            cc.IsFreshStr = r["Status"].ToString();

            //\wwwroot\media\candidates\Blank.jpg
            cc.PictureURL = !File.Exists(Directory.GetCurrentDirectory() + $"\\wwwroot\\media\\candidates\\{cc.Id}.jpg")
                ? "/media/candidates/Blank.jpg"
                : $"/media/candidates/{cc.Id}.jpg";

            op.Add(cc);
        }

        return Task.FromResult(op);
    }

    public Task<List<CandidateDTO>> GetAllCandidates2(int electionId, ElectionType electionType)
    {
        using var dc = contextFactory.CreateDbContext();
        var candPoll = dc.Elections.Find(electionId).CandidatePoll;
        var q = new List<CandidateDTO>();

        q = (from aa in dc.Candidates
            orderby aa.EnglishName.Trim()
            where (electionType != ElectionType.General || aa.CandidateType == NominationType.Candidate) &&
                  aa.CandidatePoll == candPoll
            select new CandidateDTO
            {
                Id = aa.Id,
                FullName = aa.EnglishName.Trim() + "<br /> <span class='urdu-column' style='font-size:16px'>" +
                           aa.UrduName +
                           "</span>",
                CandidateType = ((int)aa.CandidateType).ToString(),
                CandidateTypeStr = aa.CandidateType == NominationType.Candidate ? "Candidate" : "Panel",

                EnglishName = aa.EnglishName.Trim(),
                DateOfBirth = aa.DateOfBirth,
                //DistrictId = aa.DistrictId,

                Gender = (int?)aa.Gender,

                UrduFatherName = aa.FatherUrduName ?? "",
                UrduName = aa.UrduName,

                IsFresh = aa.IsFresh ? 1 : 2,
                CurrentParty =
                    (from mm in dc.PartyAffiliations
                        where mm.CandidateId == aa.Id
                        orderby mm.DateFrom descending
                        select mm.Party.EnglishTitle).FirstOrDefault() ?? "",
                CreatedBy = aa.CreatedBy,
                CreatedOn = aa.CreatedDate.ToString("d MMM, yyyy h:mm"),
                ModifiedBy = aa.ModifiedBy ?? "",
                ModifiedOn = aa.ModifiedDate != null ? aa.ModifiedDate.Value.ToString("d MMM, yyyy h:mm") : "",

                Constituencies = "",
                DisableDelete = (from bb in dc.Nominations
                    where bb.CandidteId == aa.Id
                    select bb).Any()
            }).ToList();
        foreach (var cc in q)
        {
            cc.Constituencies = GetCandidateNominations(cc.Id);
            var rnd = new Random().Next().ToString();
            if (!File.Exists(Directory.GetCurrentDirectory() + $"\\wwwroot\\media\\candidates\\{cc.Id}.jpg"))
                //\wwwroot\media\candidates\Blank.jpg
                cc.PictureURL = "/media/candidates/Blank.jpg?rnd=" + rnd;
            else
                cc.PictureURL = $"/media/candidates/{cc.Id}.jpg?rnd=" + rnd;

            var dist = (from bb in dc.CandidateDistricts
                where bb.CandidateId == cc.Id &&
                      bb.ElectionId == electionId
                select new
                {
                    bb.DistrictId,
                    District = $"{bb.District.EnglishTitle} - {bb.District.Division.Province.EnglishTitle}"
                }).FirstOrDefault();

            if (dist != null)
            {
                cc.District = dist.District;
                cc.DistrictId = dist.DistrictId;
            }
            else
            {
                cc.District = "";
                //cc.DistrictId = 0;
            }
        }

        return Task.FromResult(q);
    }


    public Task<List<Caste>> GetAllCastes()
    {
        using var dc = contextFactory.CreateDbContext();
        var q = (from aa in dc.Castes
            orderby aa.EnglishTitle
            select aa).ToList();

        return Task.FromResult(q);
    }

    public Task<List<GeneralItemDTO>> GetElectionWiseDistricts(int electionId)
    {
        using var dc = contextFactory.CreateDbContext();
        var q = (from aa in dc.Structures.OfType<District>()
            orderby aa.EnglishTitle
            where aa.ElectionId == electionId
            select new GeneralItemDTO
            {
                Id = aa.Id,
                EnglishTitle = $"{aa.EnglishTitle} - {aa.Division.Province.EnglishTitle}",
                UrduTitle = $"{aa.UrduTitle} - {aa.Division.Province.UrduTitle}",
                Province = aa.Division.Province.EnglishTitle
            }).ToList();

        return Task.FromResult(q);
    }

    public Task<List<GeneralItemDTO>> GetAllDistricts()
    {
        using var dc = contextFactory.CreateDbContext();
        var q = (from aa in dc.Structures.OfType<District>()
            orderby aa.Division.Province.EnglishTitle, aa.Division.EnglishTitle, aa.EnglishTitle
            select new GeneralItemDTO
            {
                Id = aa.Id,
                EnglishTitle = aa.EnglishTitle + " - " + aa.Division.EnglishTitle + " division - " +
                               aa.Division.Province.EnglishTitle
            }).ToList();

        return Task.FromResult(q);
    }

    private string GetCandidateNominations(int candidateId)
    {
        using var dc = contextFactory.CreateDbContext();
        var str = "";
        // Member Assembly of NA-04 (National Assembly - Phase 1)
        var q = (from aa in dc.Nominations
            orderby aa.ElectionPhase.StartDate descending
            where aa.CandidteId == candidateId
            select new
            {
                Test =
                    $"<b>{aa.Structure.Code} - {aa.Structure.EnglishTitle}</b><br><span style=\'font-size:10px\'>{aa.SeatType.EnglishTitle} {aa.ElectionAssembly.EnglishTitle} - {aa.ElectionPhase.Election.EnglishTitle} - {aa.ElectionPhase.Title}</span> <br>",
                PhaweId = aa.ElectionPhaseId,
                aa.ElectionPhase.ElectionId,
                AssmblyId = aa.ElectionAssemblyId,
                aa.SeatTypeId,
                CandidateId = aa.CandidteId,
                aa.IsWinner
            }).ToList();
        foreach (var i in q)
            if (i.IsWinner)
                str += "<img src='/images/badge.png' alt='Winner' />" + i.Test;
            else
                str += i.Test;

        return str;
    }

    public Task<List<GeneralItemDTO>> GetAllEducations()
    {
        using var dc = contextFactory.CreateDbContext();
        var q = (from aa in dc.Educations
            orderby aa.EnglishTitle
            select new GeneralItemDTO
            {
                Id = aa.Id,
                EnglishTitle = aa.EnglishTitle
            }).ToList();

        return Task.FromResult(q);
    }

    public Task<List<Language>> GetAllLanguages()
    {
        using var dc = contextFactory.CreateDbContext();
        var q = (from aa in dc.Languages
            orderby aa.EnglishTitle
            select aa).ToList();

        return Task.FromResult(q);
    }

    public Task<List<Party>> GetAllParties()
    {
        using var dc = contextFactory.CreateDbContext();
        var q = (from aa in dc.Parties
            orderby aa.EnglishTitle
            select aa).ToList();
        return Task.FromResult(q);
    }

    public Task<List<Profession>> GetAllProfessions()
    {
        using var dc = contextFactory.CreateDbContext();
        var q = (from aa in dc.Professions
            orderby aa.EnglishTitle
            select aa).ToList();

        return Task.FromResult(q);
    }

    public Task<CandidateDTO> GetCandidateInfo(int candidateId, int electionId)
    {
        using var dc = contextFactory.CreateDbContext();
        var q = (from aa in dc.Candidates
            orderby aa.EnglishName
            where aa.Id == candidateId
            select new CandidateDTO
            {
                IsBigName = aa.IsBigName,
                HasRealDOB = aa.HasRealDOB,
                Id = aa.Id,
                CandidateType = ((int)aa.CandidateType).ToString(),
                CasteId = aa.CasteId,
                EnglishName = aa.EnglishName,
                Caste = aa.Caste.EnglishTitle,
                DateOfBirth = aa.DateOfBirth,
                Year = aa.Year,
                Month = aa.Month,
                Day = aa.Day,
                //DistrictId = aa.DistrictId,
                Education = aa.Education.EnglishTitle,
                EducationId = aa.EducationId,
                EnglishFatherName = aa.FatherEnglishName,
                Gender = (int?)aa.Gender,
                Language = aa.Language.EnglishTitle,
                LanguageId = aa.LanguageId,
                CandidateProfession = aa.Profession.EnglishTitle,
                ProfessionId = aa.ProfessionId,
                Trivia = aa.Trivia,
                UrduFatherName = aa.FatherUrduName,
                UrduName = aa.UrduName,
                ContactNo = aa.ContactNumber,
                TotalAssets = aa.TotalAssets,
                IsFresh = aa.IsFresh ? 1 : 2,
                EducationDegree = aa.EducationDegree,
                District = aa.StrDistrict,
                CreatedBy = aa.CreatedBy,
                CreatedOn = aa.CreatedDate.ToString("d MMM, yyyy h:mm"),
                ModifiedBy = aa.ModifiedBy ?? "",
                ModifiedOn = aa.ModifiedDate != null ? aa.ModifiedDate.Value.ToString("d MMM, yyyy h:mm") : "",
                CandidateTypeStr = aa.CandidateType == NominationType.Panel ? "Panel" : "Candidate",
                CurrentParty =
                    (from mm in dc.PartyAffiliations
                        where mm.CandidateId == aa.Id
                        orderby mm.DateFrom descending
                        select mm.Party.EnglishTitle).FirstOrDefault() ?? "",
                Constituencies = "",
                //CurrentParty=aa.CurrentParty == null ? "" : aa.CurrentParty.EnglishTitle,
                //CurrentPartyId=aa.CurrentPartyId,
                PartyAffiliations = (from pa in aa.PartyAffiliations
                    orderby pa.DateFrom descending
                    select new PartyAffiliationDTO
                    {
                        Id = pa.Id,
                        DateFrom = pa.DateFrom,
                        DateTo = pa.DateTo,
                        Party = pa.Party.EnglishTitle,
                        PartyId = pa.PartyId,
                        Position = pa.Position
                        //TillToday = pa.TillToday
                    }).ToList(),
                PoliticalCareers = (from pc in aa.CandidtePoliticalCareers
                    orderby pc.DateFrom descending
                    select new PoliticalCareerDTO
                    {
                        Id = pc.Id,
                        DateFrom = pc.DateFrom,
                        DateTo = pc.DateTo,
                        Position = pc.Position,
                        TillToday = pc.TillToday
                    }).ToList()
            }).FirstOrDefault();

        var dist = (from aa in dc.CandidateDistricts
            where aa.CandidateId == candidateId
                  && aa.ElectionId == electionId
            select new
            {
                aa.DistrictId,
                District = $"{aa.District.EnglishTitle} - {aa.District.Division.Province.EnglishTitle}"
            }).FirstOrDefault();

        if (dist != null)
        {
            q.DistrictId = dist.DistrictId;
            q.District = dist.District;
        }

        q.CandidateElections = (from mm in dc.Nominations
            where mm.CandidteId == candidateId
            select new CandidateElection
            {
                Election = $"{mm.ElectionPhase.Election.EnglishTitle} - {mm.ElectionPhase.Title}",
                Assembly = mm.ElectionAssembly.EnglishTitle,
                SeatType = mm.SeatType.EnglishTitle,
                Constituency = $"{mm.Structure.Code} - {mm.Structure.EnglishTitle}",
                Party = mm.Party.EnglishTitle,
                Symbol = mm.Symbol.EnglishTitle,
                Votes = mm.Votes,
                Status = ""
            }).ToList();
        q.ElectionWiseDistricts = (from aa in dc.CandidateDistricts
            where aa.CandidateId == candidateId
            select new CandidateElectionWiseDistrictDTO
            {
                District = aa.District.EnglishTitle,
                Province = aa.District.Division.Province.EnglishTitle,
                Election = aa.Election.EnglishTitle
            }).ToList();
        var kk = (from aa in dc.Nominations
            orderby aa.ElectionPhase.StartDate descending
            where aa.CandidteId == q.Id
            select new
            {
                Assmbly = aa.ElectionAssembly.EnglishTitle,
                Phase = aa.ElectionPhase.Title,
                Election = aa.ElectionPhase.Election.EnglishTitle,
                Constituency = aa.Structure.Code + " " + aa.Structure.EnglishTitle,
                SeatType = aa.SeatType.EnglishTitle
            }).Take(3).ToList();
        var op = "<span style='font-size:10px'>";
        foreach (var k in kk)
            op += $"{k.SeatType} for <b>{k.Constituency}</b> [<i>{k.Assmbly}</i>] ({k.Phase}) <br />";
        op += "</span>";
        q.Constituencies = op;

        return Task.FromResult(q);
    }

    public Task<CandidateDTO> SaveCandidate(CandidateDTO d, string user, int electionId)
    {
        using var dc = contextFactory.CreateDbContext();
        var candPoll = dc.Elections.Find(electionId).CandidatePoll;
        if (d.Id == 0)
        {
            using var tran = dc.Database.BeginTransaction();
            try
            {
                Candidate c = new()
                {
                    Id = 0,
                    CandidateType = d.CandidateType == "1" ? NominationType.Candidate : NominationType.Panel,
                    CasteId = d.CasteId,
                    CreatedBy = user,
                    CreatedDate = DateTime.Now,
                    DateOfBirth = d.Year != null ? new DateTime(d.Year.Value, d.Month ?? 1, d.Day ?? 1) : null,
                    Year = d.Year,
                    Month = d.Month,
                    Day = d.Day,
                    EducationId = d.EducationId,
                    EnglishName = d.EnglishName,
                    FatherEnglishName = d.EnglishFatherName,
                    FatherUrduName = d.UrduFatherName,
                    Gender = (Gender)(int)d.Gender,
                    LanguageId = d.LanguageId,
                    ProfessionId = d.ProfessionId,
                    Trivia = d.Trivia,
                    UrduName = d.UrduName,
                    TotalAssets = d.TotalAssets,
                    ContactNumber = d.TotalAssets,
                    IsFresh = d.IsFresh == 1,
                    EducationDegree = (d.EducationDegree ?? "").Trim(),
                    StrDistrict = (d.District ?? "").Trim(),
                    CandidatePoll = candPoll,
                    IsBigName = d.IsBigName,
                    HasRealDOB = d.HasRealDOB
                };

                //c.DistrictId = d.DistrictId;
                //c.CurrentPartyId=d.CurrentPartyId;

                dc.Candidates.Add(c);
                dc.SaveChanges();
                d.Id = c.Id;
                foreach (var pc in d.PoliticalCareers)
                {
                    CandidtePoliticalCareer cac = new()
                    {
                        CandidateId = c.Id,
                        CreatedBy = user,
                        CreatedDate = DateTime.Now,
                        DateFrom = pc.DateFrom,
                        DateTo = pc.DateTo,
                        Position = pc.Position,
                        TillToday = false
                    };

                    dc.PoliticalCareers.Add(cac);
                    dc.SaveChanges();
                }

                foreach (var pa in d.PartyAffiliations)
                {
                    var obj = new CandidatePartyAffiliation
                    {
                        CandidateId = c.Id,
                        CreatedBy = user,
                        CreatedDate = DateTime.Now,
                        DateFrom = pa.DateFrom,
                        DateTo = pa.DateTo,
                        PartyId = pa.PartyId,
                        Position = pa.Position,
                        TillToday = false
                    };

                    dc.PartyAffiliations.Add(obj);
                    dc.SaveChanges();
                }


                #region Candidate District

                var prvDis = (from aa in dc.CandidateDistricts
                    where aa.ElectionId == electionId
                          && aa.CandidateId == c.Id
                    select aa).ToList();
                dc.CandidateDistricts.RemoveRange(prvDis);
                dc.SaveChanges();

                if (d.DistrictId != null)
                {
                    var cd = new CandidateDistrict
                    {
                        CandidateId = c.Id,
                        ElectionId = electionId,
                        DistrictId = (int)d.DistrictId,
                        CreatedDate = DateTime.Now,
                        CreatedBy = user
                    };
                    dc.CandidateDistricts.Add(cd);
                    dc.SaveChanges();
                }

                #endregion Candidate District

                UpdateCandidateSearchLog(d.Id, electionId, dc);

                tran.Commit();
                var op = new GeneralItemDTO { Id = c.Id, EnglishTitle = "Record has been created successfully" };
                return Task.FromResult(d);
            }
            catch (Exception ex)
            {
                tran.Rollback();
                var op = new GeneralItemDTO { Id = 0, EnglishTitle = ex.Message };
                throw;
            }
        }
        else
        {
            using var tran = dc.Database.BeginTransaction();
            try
            {
                var cp = (from aa in dc.Candidates
                    where aa.Id == d.Id
                    select aa).FirstOrDefault();

                cp.CandidateType = d.CandidateType == "1" ? NominationType.Candidate : NominationType.Panel;
                cp.CasteId = d.CasteId;
                //cp.DateOfBirth = d.DateOfBirth;
                //cp.DistrictId = d.DistrictId;
                cp.Year = d.Year;
                cp.Month = d.Month;
                cp.Day = d.Day;

                if (d.Year != null)
                    cp.DateOfBirth = new DateTime(d.Year.Value, d.Month ?? 1, d.Day ?? 1);
                else
                    cp.DateOfBirth = null;
                cp.EducationId = d.EducationId;
                cp.EnglishName = d.EnglishName;
                cp.FatherEnglishName = d.EnglishFatherName;
                cp.FatherUrduName = d.UrduFatherName;
                cp.Gender = (Gender)(int)d.Gender;
                cp.LanguageId = d.LanguageId;
                cp.ModifiedBy = user;
                cp.ModifiedDate = DateTime.Now;
                cp.ProfessionId = d.ProfessionId;
                cp.Trivia = d.Trivia;
                cp.UrduName = d.UrduName;
                cp.TotalAssets = d.TotalAssets;
                cp.ContactNumber = d.ContactNo;
                cp.IsFresh = d.IsFresh == 1;
                cp.EducationDegree = (d.EducationDegree ?? "").Trim();
                cp.StrDistrict = (d.District ?? "").Trim();
                cp.IsBigName = d.IsBigName;
                cp.HasRealDOB = d.HasRealDOB;
                //cp.CurrentPartyId = d.CurrentPartyId;
                dc.SaveChanges();

                #region Remove Party Affiliation if not found in new list

                var cpas = (from aa in dc.PartyAffiliations
                    where aa.CandidateId == d.Id
                    select aa).ToList();

                foreach (var cc in cpas)
                {
                    var exist = (from aa in d.PartyAffiliations
                        where aa.Id == cc.Id
                        select aa).FirstOrDefault();
                    if (exist == null)
                    {
                        dc.PartyAffiliations.Remove(cc);
                        dc.SaveChanges();
                    }
                }

                #endregion Remove Party Affiliation if not found in new list

                foreach (var pa in d.PartyAffiliations)
                {
                    var q = (from aa in dc.PartyAffiliations
                        where aa.Id == pa.Id
                        select aa).FirstOrDefault();

                    if (q == null)
                    {
                        q = new CandidatePartyAffiliation
                        {
                            CandidateId = d.Id,
                            CreatedBy = user,
                            CreatedDate = DateTime.Now,
                            DateFrom = pa.DateFrom,
                            PartyId = pa.PartyId,
                            Position = pa.Position,
                            DateTo = pa.DateTo,
                            TillToday = false
                        };
                        dc.PartyAffiliations.Add(q);
                        dc.SaveChanges();
                    }
                    else
                    {
                        q.DateFrom = pa.DateFrom;
                        q.DateTo = pa.DateTo;
                        q.ModifiedBy = user;
                        q.ModifiedDate = DateTime.Now;
                        q.Position = pa.Position;
                        q.TillToday = false;
                        q.PartyId = pa.PartyId;
                        dc.SaveChanges();
                    }
                }

                #region Candidate District

                var prvDis = (from aa in dc.CandidateDistricts
                    where aa.ElectionId == electionId
                          && aa.CandidateId == d.Id
                    select aa).ToList();
                dc.CandidateDistricts.RemoveRange(prvDis);
                dc.SaveChanges();

                if (d.DistrictId != null)
                {
                    var cd = new CandidateDistrict
                    {
                        CandidateId = d.Id,
                        ElectionId = electionId,
                        DistrictId = (int)d.DistrictId,
                        CreatedBy = user,
                        CreatedDate = DateTime.Now
                    };
                    dc.CandidateDistricts.Add(cd);
                    dc.SaveChanges();
                }

                #endregion Candidate District

                UpdateCandidateSearchLog(d.Id, electionId, dc);
                tran.Commit();
                var op = new GeneralItemDTO { Id = d.Id, EnglishTitle = "Update Successfully" };
                return Task.FromResult(d);
            }
            catch (Exception ex)
            {
                tran.Rollback();
                var op = new GeneralItemDTO { Id = 0, EnglishTitle = ex.Message };
                throw;
            }
        }
    }

    private static void UpdateCandidateSearchLog(int candidateId, int electionId, ApplicationDbContext dc)
    {
        var currentElection = (from aa in dc.Elections
            where aa.Id == electionId
            select aa).FirstOrDefault();

        var elecs = (from aa in dc.Elections
            where aa.CandidatePoll == currentElection.CandidatePoll &&
                  aa.ElectionType == currentElection.ElectionType
            select aa).ToList();
        foreach (var el in elecs)
        {
            List<SqlParameter> parms =
            [
                new() { ParameterName = "@ElectionId", Value = el.Id },
                new() { ParameterName = "@CandidateId", Value = candidateId }
            ];
            dc.Database.ExecuteSqlRaw("EXECUTE spUpdateCandidateSearch @ElectionId, @CandidateId", parms.ToArray());
        }
    }


    public Task<string> TranslateText(string input)
    {
        using var dc = contextFactory.CreateDbContext();
        // Set the language from/to in the url (or pass it into this function)
        var url = string.Format
        ("https://translate.googleapis.com/translate_a/single?client=gtx&sl={0}&tl={1}&dt=t&q={2}",
            "en", "ur", Uri.EscapeDataString(input));
        var httpClient = new HttpClient();
        var result = httpClient.GetStringAsync(url).Result;
        var obj = JsonConvert.DeserializeObject(result);

        // Get all json data
        /*JArray jsonData = JArray.Parse( new JavaScriptSerializer().Deserialize<List<dynamic>>(result))*/
        var jsonArray = JArray.Parse(result);
        var translation = JArray.Parse(JArray.Parse(jsonArray[0].ToString())[0].ToString())[0].ToString();

        return Task.FromResult(translation);
    }

    public Task<CandidateDetailDTO> GetCandidateRptInfo(int candidateId)
    {
        using var dc = contextFactory.CreateDbContext();
        var q = (from aa in dc.Candidates
            where aa.Id == candidateId
            select new CandidateDetailDTO
            {
                Id = aa.Id,
                UrduName = aa.UrduName,
                NominationType = aa.CandidateType,
                CandidateTypeStr = aa.CandidateType == NominationType.Panel ? "Panel" : "Candidate",
                Caste = aa.CasteId == null ? "N/A" : $"{aa.Caste.EnglishTitle} ({aa.Caste.UrduTitle})",
                Language = aa.LanguageId == null ? "N/A" : $"{aa.Language.EnglishTitle} ({aa.Language.UrduTitle})",
                District = aa.DistrictId == null ? "N/A" : $"{aa.District.EnglishTitle} ({aa.District.UrduTitle})",
                Profession = aa.ProfessionId == null
                    ? "N/A"
                    : $"{aa.Profession.EnglishTitle} ({aa.Profession.UrduTitle})",
                Education = aa.EducationId == null ? "N/A" : $"{aa.Education.EnglishTitle} ({aa.Education.UrduTitle})",
                ContactNumber = aa.ContactNumber,
                DateOfBirth = aa.DateOfBirth,
                EducationDegree = aa.EducationDegree,
                EnglishName = aa.EnglishName,
                FatherEnglishName = aa.FatherEnglishName,
                FatherUrduName = aa.FatherUrduName,
                Gender = aa.Gender,
                IsFresh = aa.IsFresh,
                StrDistrict = aa.StrDistrict,
                TotalAssets = aa.TotalAssets,
                Trivia = aa.Trivia,
                PartyAffiliations = (from bb in dc.PartyAffiliations
                    where bb.CandidateId == candidateId
                    orderby bb.DateFrom descending
                    select new PartyAffDTO
                    {
                        Party = $"{bb.Party.EnglishTitle} ({bb.Party.UrduTitle})",
                        Position = bb.Position
                    }).ToList(),
                PoliticalCareers = (from bb in dc.PoliticalCareers
                    where bb.CandidateId == candidateId
                    orderby bb.DateFrom descending
                    select new PoliticalCareerDTO
                    {
                        DateFrom = bb.DateFrom,
                        DateTo = bb.DateTo,
                        Position = bb.Position
                    }).ToList(),
                Nominations = (from bb in dc.Nominations
                    where bb.CandidteId == candidateId
                    orderby bb.ElectionPhase.StartDate descending
                    select new NominationDTO
                    {
                        Election =
                            $"{bb.ElectionPhase.Election.EnglishTitle} - {bb.ElectionPhase.Title} <br><span class='urdu-cap' style='font-size:18px'>{bb.ElectionPhase.Election.UrduTitle}</span>",
                        Assembly =
                            $"{bb.ElectionAssembly.EnglishTitle} <br><span class='urdu-cap' style='font-size:18px'>{bb.ElectionAssembly.UrduTitle}</span>",
                        Constituency =
                            $"{bb.Structure.Code} {bb.Structure.EnglishTitle} <br><span class='urdu-cap' style='font-size:18px'>{bb.Structure.UrduTitle}</span>",
                        SeatType =
                            $"{bb.SeatType.EnglishTitle} <br><span class='urdu-cap' style='font-size:18px'>{bb.SeatType.UrduTitle}</span>",
                        Party =
                            $"{bb.Party.EnglishTitle} <br><span class='urdu-cap' style='font-size:18px'>{bb.Party.UrduTitle}</span>",
                        Symbol =
                            $"{bb.Symbol.EnglishTitle} <br><span class='urdu-cap' style='font-size:18px'>{bb.Symbol.UrduTitle}</span>",
                        Weight = bb.Weight,
                        Votes = bb.Votes,
                        StartDate = bb.ElectionPhase.StartDate,
                        IsWinner = bb.IsWinner
                    }).ToList()
            }).FirstOrDefault();
        return Task.FromResult(q);
    }

    public Task<List<GeneralItemDTO>> GetCandidateList()
    {
        using var dc = contextFactory.CreateDbContext();
        var q = (from a in dc.Candidates
            orderby a.EnglishName
            select new GeneralItemDTO
            {
                Id = a.Id,
                EnglishTitle = $"{a.Id} - {a.EnglishName} - {a.UrduName}"
            }).ToList();
        return Task.FromResult(q);
    }

    public Task<string> GetCandidateName(int candidateId)
    {
        using var dc = contextFactory.CreateDbContext();
        var q = (from a in dc.Candidates
            where a.Id == candidateId
            select new { a.EnglishName, a.Id }).FirstOrDefault();
        if (q == null)
            return Task.FromResult("");
        return Task.FromResult($"{q.Id} - {q.EnglishName}");
    }

    private List<string> GenerateCombinations(string[] list)
    {
        var count = Math.Pow(2, list.Length);
        var op = new List<string>();
        for (var i = 1; i <= count - 1; i++)
        {
            var str = Convert.ToString(i, 2).PadLeft(list.Length, '0');

            var x = "";
            for (var j = 0; j < str.Length; j++)
                if (str[j] == '1')
                    //Console.Write(list[j] + " ");
                    x += list[j] + " ";
            var v = x.Trim().Split(' ').ToList();
            if (v.Count > 1)
            {
                var pp = "";
                for (var ii = 0; ii < v.Count; ii++) pp += v[ii] + ", ";
                if (pp.Length > 0)
                    pp = pp.Substring(0, pp.Length - 2);
                op.Add(pp);
            }
            //Console.WriteLine();
        }

        return op;
    }

    private List<string> GenerateCombinations2(string[] names)
    {
        var combinations = new List<string>();

        GenerateCombinationsHelper(names, 0, "", combinations);

        return combinations;
    }

    private void GenerateCombinationsHelper(string[] names, int index, string current, List<string> combinations)
    {
        if (index >= names.Length)
        {
            combinations.Add(current.TrimEnd());
            return;
        }

        GenerateCombinationsHelper(names, index + 1, current + names[index] + " ", combinations);

        for (var i = index + 1; i < names.Length; i++)
            if (names[i] != names[index])
                GenerateCombinationsHelper(names, i, current + names[i] + " ", combinations);
    }

    public Task<List<GeneralItemDTO>> GetPossibleDuplicate(int candidateId)
    {
        using var dc = contextFactory.CreateDbContext();
        var con = dc.Database.GetDbConnection();
        var op = new List<GeneralItemDTO>();
        var q = (from a in dc.Candidates
            where a.Id == candidateId
            select new { a.Id, a.UrduName, a.EnglishName }).FirstOrDefault();

        if (q != null)
        {
            var EngNames = q.EnglishName.Split(' ');
            var UrduNames = q.UrduName.Split(' ');

            if (EngNames.Length > 1)
            {
                var comb = GenerateCombinations(EngNames);
                foreach (var item in comb)
                {
                    var prams = new { names = item };
                    var res = con.Query<GeneralItemDTO>("app.FindDuplicateCandidate", prams,
                        commandType: CommandType.StoredProcedure).ToList();
                    foreach (var rr in res)
                        if (!op.Any(m => m.Id == rr.Id))
                            op.Add(new GeneralItemDTO
                                { Id = rr.Id, EnglishTitle = rr.EnglishTitle, UrduTitle = rr.UrduTitle });
                }
            }

            if (UrduNames.Length > 1)
            {
                var comb = GenerateCombinations(UrduNames);
                foreach (var item in comb)
                {
                    var prams = new { names = item };
                    var res = con.Query<GeneralItemDTO>("app.FindDuplicateCandidate", prams,
                        commandType: CommandType.StoredProcedure).ToList();
                    foreach (var rr in res)
                        if (!op.Any(m => m.Id == rr.Id))
                            op.Add(new GeneralItemDTO
                                { Id = rr.Id, EnglishTitle = rr.EnglishTitle, UrduTitle = rr.UrduTitle });
                }
            }

            foreach (var m in op) m.EnglishTitle = m.Id + " " + m.EnglishTitle;
        }

        return Task.FromResult(op);
    }

    public Task<string> MergeCandidate(int? keepCandidateId, int? removeCandidateId, string identityName)
    {
        var response = "OK";
        using var dc = contextFactory.CreateDbContext();
        var con = dc.Database.GetDbConnection();

        var pram = new
            { KeepCandidateId = keepCandidateId, RemoveCandidateId = removeCandidateId, UserId = identityName };
        try
        {
            var res = con.Execute("dbo.MergeCandidates", pram, commandType: CommandType.StoredProcedure);
        }
        catch (Exception e)
        {
            response = e.Message;
        }

        return Task.FromResult(response);
    }

    public Task<bool> CandidateExist(int removeCandidateId)
    {
        using var dc = contextFactory.CreateDbContext();
        var exist = dc.Candidates.Any(m => m.Id == removeCandidateId);
        return Task.FromResult(exist);
    }
}