﻿@attribute [Authorize(Roles = "Administrators,Data Viewer")]
@page "/constituencies/{eaId:int}"
@using ButtonType = MudBlazor.ButtonType
@using FilterType = Syncfusion.Blazor.Grids.FilterType
@using Size = MudBlazor.Size
@inherits OwningComponentBase<ConstituencyService>

@*<PageCaption Title="@($"{AssemblyTitle} - Constituencies" )"></PageCaption>*@
<MudText Typo="Typo.h5">@($"{AssemblyTitle} - Constituencies")</MudText>


<SfToast @ref="ToastObj" Title="Alert" ShowProgressBar="true" Timeout="3000">
    <ToastPosition X="Right" Y="Bottom"></ToastPosition>
</SfToast>

<SfDialog Width="1200" @bind-Visible="@isSelectDlgVisible" @ref="dlgSelectForm" IsModal="true" ShowCloseIcon="true">
    <DialogEvents OnOpen="beforeOpen"></DialogEvents>
    <DialogTemplates>
        <Header>Create @StructureTitle</Header>
        <Content>

            <div class="row">
                <div class="col-1">
                    Type
                </div>
                <div class="col-2">
                    @if (constTypesList != null && constTypesList.Any())
                    {
                        <SfComboBox TItem="GeneralItemDTO" TValue="int?" @bind-Value="@constTypeId" Placeholder="Select Constituency Type" DataSource="@constTypesList">
                            <ComboBoxEvents TValue="int?" TItem="GeneralItemDTO" ValueChange="@FillConstituencies"></ComboBoxEvents>
                            <ComboBoxFieldSettings Value="Id" Text="EnglishTitle"></ComboBoxFieldSettings>
                        </SfComboBox>
                    }
                    else
                    {
                        <p>No constituency is defined on this election</p>
                    }

                </div>
            </div>

            <div class="row">
                <div class="col">
                    <SfGrid @ref="AvailableConGrid" AllowSorting="true" AllowFiltering="true" DataSource="GroupA" AllowPaging="true" AllowTextWrap="true" Width="100%">
                        <GridFilterSettings Type="FilterType.Excel"></GridFilterSettings>
                        <GridPageSettings PageSize="5"></GridPageSettings>
                        <GridColumns>
                            <GridColumn HeaderText="" AutoFit="true">
                                <Template Context="ec">
                                    @{
                                        if (ec is ConstituencyDTO obj)
                                        {
                                            <SfCheckBox @bind-Checked="obj.IsSelected"></SfCheckBox>
                                        }
                                    }
                                </Template>
                            </GridColumn>
                            <GridColumn AutoFit="true" HeaderText="Code" Field="Code"></GridColumn>
                            <GridColumn AutoFit="true" HeaderText="Constituency" Field="EnglishTitle" DisableHtmlEncode="false"></GridColumn>
                            <GridColumn AutoFit="true" HeaderText="Province" Field="Province" DisableHtmlEncode="false"></GridColumn>
                            <GridColumn AutoFit="true" Visible="state.state.ElectionType == ElectionType.LocalBody" HeaderText="Division" Field="Division" DisableHtmlEncode="false"></GridColumn>
                            <GridColumn AutoFit="true" HeaderText="District" Field="District" DisableHtmlEncode="false"></GridColumn>
                            <GridColumn AutoFit="true" HeaderText="Town" Field="Town" DisableHtmlEncode="false" Visible="@(constTypeId == (int)StructureType.UnionCouncil || constTypeId == (int)StructureType.Ward)"></GridColumn>
                            <GridColumn AutoFit="true" HeaderText="UC" Field="UC" DisableHtmlEncode="false" Visible="@(constTypeId == (int)StructureType.Ward)"></GridColumn>
                        </GridColumns>
                    </SfGrid>
                </div>
            </div>
            <AuthorizeView Roles="Administrators">
                <div class="row">
                    <div class="col">
                        <MudButton Size="Size.Small"
                                   Color="Color.Primary"
                                   Variant="Variant.Filled"
                                   StartIcon="@Icons.Material.Filled.AddCircle"
                                   OnClick="SaveConsToAssmb">
                            Add To Assembly
                        </MudButton>
                    </div>
                </div>
            </AuthorizeView>
        </Content>
    </DialogTemplates>
</SfDialog>

<SfDialog Width="900px" @bind-Visible="@isDlgVisible" @ref="dlgForm" IsModal="true" ShowCloseIcon="true">
    <DialogEvents OnOpen="beforeOpen"></DialogEvents>
    <DialogTemplates>
        <Header>Constituency Detail @(formData.Id != 0 ? $" - Code: {formData.Id}" : "")</Header>
        <Content>
            <EditForm Model="formData" OnValidSubmit="SaveStructure" Context="ef">
                @*<FluentValidator TValidator="CreateStructureDTOValidator"></FluentValidator>*@
                <FluentValidationValidator/>
                <ValidationSummary/>
                <div class="tabheader mb2">Basic Information:</div>
                <div class="row mb-2">
                    <div class="col">
                        <SfComboBox Readonly="formData.Id != 0" Enabled="formData.Id == 0" TItem="GeneralItemDTO" TValue="int?" @bind-Value="@formData.ConstTypeId" Placeholder="Select Constituency Type" DataSource="@constTypesList">
                            @*<ComboBoxEvents TValue="int?" ValueChange="@SetFormTitle"></ComboBoxEvents>*@
                            <ComboBoxFieldSettings Value="Id" Text="EnglishTitle"></ComboBoxFieldSettings>
                        </SfComboBox>
                        <ValidationMessage For="@(() => formData.ConstTypeId)"></ValidationMessage>
                    </div>
                </div>
                <div class="row">
                    <div class="input-field col-2">
                        <SfTextBox Placeholder="Code" @bind-Value="@formData.Code" FloatLabelType="FloatLabelType.Always">
                        </SfTextBox>
                        <ValidationMessage For="@(() => formData.Code)"/>
                    </div>
                    <div class="input-field col">
                        <SfTextBox Placeholder="Title (English)" @bind-Value="@formData.EnglishTitle" OnBlur="TranslateToUrdu" FloatLabelType="FloatLabelType.Always"></SfTextBox>
                        <ValidationMessage For="@(() => formData.EnglishTitle)"/>
                    </div>
                    <div class="input-field col">
                        <SfTextBox Placeholder="Title (Urdu)" @bind-Value="@formData.UrduTitle" FloatLabelType="FloatLabelType.Always">
                        </SfTextBox>
                        <ValidationMessage For="@(() => formData.UrduTitle)"/>
                    </div>
                </div>
                <div class="row">

                    <div class="input-field col">
                        @if (provinceList != null && provinceList.Any())
                        {
                            <SfComboBox TItem="GeneralItemDTO" TValue="int?" @bind-Value="@formData.ProvinceId" Placeholder="Province" PopupWidth="300px" DataSource="@provinceList" FloatLabelType="FloatLabelType.Always">
                                <ComboBoxEvents TValue="int?" TItem="GeneralItemDTO" ValueChange="@FillDivisions2"></ComboBoxEvents>
                                <ComboBoxFieldSettings Value="Id" Text="EnglishTitle"></ComboBoxFieldSettings>
                            </SfComboBox>
                            <ValidationMessage For="@(() => formData.ProvinceId)"/>
                        }
                    </div>
                    @if (state.state.ElectionType == ElectionType.LocalBody)
                    {
                        <div class="input-field col">
                            @if (divisionList != null && divisionList.Any())
                            {
                                <SfComboBox TItem="GeneralItemDTO" TValue="int?" @bind-Value="@formData.DivisionId" Placeholder="Division" PopupWidth="300px" DataSource="@divisionList" FloatLabelType="FloatLabelType.Always">
                                    <ComboBoxEvents TValue="int?" TItem="GeneralItemDTO" ValueChange="@FillDistricts2"></ComboBoxEvents>
                                    <ComboBoxFieldSettings Value="Id" Text="EnglishTitle"></ComboBoxFieldSettings>
                                </SfComboBox>
                                <ValidationMessage For="@(() => formData.DivisionId)"/>
                            }
                        </div>
                    }
                </div>
                <div class="row">
                    <div class="input-field col">
                        @if (districtList != null && districtList.Any())
                        {
                            <SfComboBox TItem="GeneralItemDTO" TValue="int?" @bind-Value="@formData.DistrictId" Placeholder="District" PopupWidth="300px" DataSource="@districtList" FloatLabelType="FloatLabelType.Always">
                                <ComboBoxEvents TValue="int?" TItem="GeneralItemDTO" ValueChange="@FillTowns2"></ComboBoxEvents>
                                <ComboBoxFieldSettings Value="Id" Text="EnglishTitle"></ComboBoxFieldSettings>
                            </SfComboBox>
                            <ValidationMessage For="@(() => formData.DistrictId)"/>
                        }
                    </div>
                    @if (formData.ConstTypeId == (int)StructureType.UnionCouncil || formData.ConstTypeId == (int)StructureType.Ward)
                    {
                        <div class="input-field col">
                            @if (townList != null && townList.Any())
                            {
                                <SfComboBox TItem="GeneralItemDTO" TValue="int?" @bind-Value="@formData.TownId" Placeholder="Town" PopupWidth="300px" DataSource="@townList" FloatLabelType="FloatLabelType.Always">
                                    <ComboBoxEvents TValue="int?" TItem="GeneralItemDTO" ValueChange="@FillUnionCounciles2"></ComboBoxEvents>
                                    <ComboBoxFieldSettings Value="Id" Text="EnglishTitle"></ComboBoxFieldSettings>
                                </SfComboBox>
                                <ValidationMessage For="@(() => formData.TownId)"/>
                            }
                        </div>
                    }
                </div>
                <div class="row">
                    @if (formData.ConstTypeId == (int)StructureType.Ward)
                    {
                        <div class="input-field col">
                            @if (unionCouncilList != null && unionCouncilList.Any())
                            {
                                <SfComboBox TItem="GeneralItemDTO" TValue="int?" @bind-Value="@formData.UCId" Placeholder="Union Council" PopupWidth="300px" DataSource="@unionCouncilList" FloatLabelType="FloatLabelType.Always">
                                    <ComboBoxFieldSettings Value="Id" Text="EnglishTitle"></ComboBoxFieldSettings>
                                </SfComboBox>
                                <ValidationMessage For="@(() => formData.UCId)"/>
                            }
                        </div>
                    }
                    <div class="input-field col">
                    </div>
                </div>
                @if (formData.Id != 0)
                {
                    <div class="row">
                        <div class="col">
                            <SfSwitch @bind-Checked="formData.IsPostponed"></SfSwitch>
                            <label>Status: @(formData.IsPostponed ? "Postponed" : "Active")</label>
                        </div>

                        <div class="col">
                            <SfSwitch @bind-Checked="formData.HasBilaMokablia"></SfSwitch>
                            <label>Bila Muqabala: @(formData.HasBilaMokablia ? "Yes" : "No")</label>
                        </div>
                    </div>
                }

                <div class="tabheader mb2">Voters/Polling Stations</div>

                <div class="row">

                    <div class="col">
                        <SfNumericTextBox ShowSpinButton="false" Decimals="0" Format="########" @bind-Value="@formData.MaleVoters" Min="1" FloatLabelType="FloatLabelType.Always" Placeholder="Male Voters"></SfNumericTextBox>
                        <ValidationMessage For="@(() => formData.MaleVoters)"/>
                    </div>
                    <div class="col">
                        <SfNumericTextBox ShowSpinButton="false" Decimals="0" Format="########" @bind-Value="@formData.FemaleVoters" Min="1" FloatLabelType="FloatLabelType.Always" Placeholder="Female Voters"></SfNumericTextBox>
                        <ValidationMessage For="@(() => formData.FemaleVoters)"/>
                    </div>
                    <div class="col">
                        Total Voters<br/>
                        <b>@formData.TotalVoters</b>
                    </div>
                </div>
                <div class="row">
                    <div class="col">
                        <SfNumericTextBox ShowSpinButton="false" Decimals="0" Format="########" @bind-Value="@formData.Population" Min="1" FloatLabelType="FloatLabelType.Always" Placeholder="Poulation"></SfNumericTextBox>
                        <ValidationMessage For="@(() => formData.Population)"/>
                    </div>
                    <div class="col">
                        <SfNumericTextBox ShowSpinButton="false" Decimals="0" Format="########" @bind-Value="@formData.TotalPollingStations" Min="1" FloatLabelType="FloatLabelType.Always" Placeholder="Total Polling Stations"></SfNumericTextBox>
                        <ValidationMessage For="@(() => formData.TotalPollingStations)"/>
                    </div>
                </div>
                <div class="tabheader mb2">Demographics</div>

                <div class="row">
                    <div class="col">
                        <SfTextBox Multiline="true" @bind-Value="formData.Trivia" FloatLabelType="FloatLabelType.Always" Placeholder="Economy: (Trivia)"></SfTextBox>
                        <ValidationMessage For="@(() => formData.Trivia)"/>
                    </div>
                </div>
                <div class="row">
                    <div class="col">
                        <SfTextBox Multiline="true" @bind-Value="formData.GeneralTrivia" FloatLabelType="FloatLabelType.Always" Placeholder="General: (Trivia)"></SfTextBox>
                        <ValidationMessage For="@(() => formData.Trivia)"/>
                    </div>
                </div>

                <div class="row">
                    <div class="col">
                        <SfTextBox Multiline="true" @bind-Value="formData.Profile" FloatLabelType="FloatLabelType.Always" Placeholder="Profile"></SfTextBox>
                    </div>
                </div>

                <div class="row">
                    <div class="col">
                        <SfTextBox @bind-Value="formData.Languages" Placeholder="Languages Spoken" FloatLabelType="FloatLabelType.Always"></SfTextBox>
                        <ValidationMessage For="@(() => formData.Languages)"></ValidationMessage>
                    </div>
                    <div class="col">
                        <SfTextBox @bind-Value="formData.Castes" Placeholder="Castes" FloatLabelType="FloatLabelType.Always"></SfTextBox>
                        <ValidationMessage For="@(() => formData.Castes)"></ValidationMessage>
                    </div>
                </div>

                <div class="row">
                    <div class="col">
                        <SfTextBox @bind-Value="formData.ImportantPoliticalPersonalities" FloatLabelType="FloatLabelType.Always" Placeholder="Important Political Personalities"></SfTextBox>
                        <ValidationMessage For="@(() => formData.ImportantPoliticalPersonalities)"/>
                    </div>
                </div>
                <div class="row">
                    <div class="col">
                        <SfTextBox Multiline="true" @bind-Value="formData.Problems" FloatLabelType="FloatLabelType.Always" Placeholder="Problems"></SfTextBox>
                        <ValidationMessage For="@(() => formData.Problems)"/>
                    </div>
                    <div class="col">
                        <SfTextBox Multiline="true" @bind-Value="formData.ImportantAreas" FloatLabelType="FloatLabelType.Always" Placeholder="Important Cities/Areas"></SfTextBox>
                    </div>
                </div>

                <div class="row">
                    <div class="col">
                        @*<SfTextBox @bind-Value="selectedObj.UrbanAreaPer" FloatLabelType="FloatLabelType.Always" Placeholder="Urban Area (%)"></SfTextBox>*@
                        <SfNumericTextBox ShowSpinButton="false" @bind-Value="@formData.UrbanAreaPer" FloatLabelType="FloatLabelType.Always" Placeholder="Urban Area (%)" Min="0" Max="100"></SfNumericTextBox>
                    </div>
                    <div class="col">
                        @*<SfTextBox @bind-Value="selectedObj.RuralAreaPer" FloatLabelType="FloatLabelType.Always" Placeholder="Rural Area (%)"></SfTextBox>*@
                        <SfNumericTextBox ShowSpinButton="false" @bind-Value="@formData.RuralAreaPer" FloatLabelType="FloatLabelType.Always" Placeholder="Rural Area (%)" Min="0" Max="100"></SfNumericTextBox>
                    </div>
                    <div class="col">
                        <SfTextBox @bind-Value="@formData.Area" FloatLabelType="FloatLabelType.Always" Placeholder="Total Area"></SfTextBox>
                        <ValidationMessage For="@(() => formData.Problems)"/>
                    </div>
                </div>
                <div class="row">
                    <div class="col">
                        <SfTextBox @bind-Value="@formData.MajorityIncomeSource" FloatLabelType="FloatLabelType.Always" Placeholder="Majority Income Source"></SfTextBox>
                        <ValidationMessage For="@(() => formData.MajorityIncomeSource)"/>
                    </div>
                    <div class="col">
                        <SfTextBox @bind-Value="@formData.LiteracyRate" FloatLabelType="FloatLabelType.Always" Placeholder="Literacy Rate"></SfTextBox>
                        <ValidationMessage For="@(() => formData.LiteracyRate)"/>
                    </div>
                    <div class="col">
                        <SfTextBox @bind-Value="@formData.AverageHouseholdIncome" FloatLabelType="FloatLabelType.Always" Placeholder="Average Household Income"></SfTextBox>
                        <ValidationMessage For="@(() => formData.AverageHouseholdIncome)"/>
                    </div>
                </div>
                <AuthorizeView Roles="Administrators">
                    <div class="row">
                        <div class="col">
                            <MudButton ButtonType="ButtonType.Submit" Color="Color.Primary" Size="Size.Small" Variant="Variant.Filled" StartIcon="@Icons.Material.Filled.Save">Save</MudButton>
                        </div>
                    </div>
                </AuthorizeView>
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>

<section>
    @*<a href="/elections">Elections</a> <span>></span>*@
    <a href="/assemblies/@ElectionAssemblyId">Assemblies</a> <span>></span>
    Constituencies
    <div class="row">
        <AuthorizeView Roles="Administrators">
            <MudButton Color="Color.Primary"
                       Size="Size.Small"
                       Variant="Variant.Filled"
                       StartIcon="@Icons.Material.Filled.ImportExport"
                       Class="mb-2 mr-2" OnClick="ShowSelectConstDlg">
                Import Constituencies
            </MudButton>
            <MudButton Color="Color.Error"
                       Size="Size.Small"
                       Variant="Variant.Filled"
                       StartIcon="@Icons.Material.Filled.RemoveCircle"
                       Class="mb-2 mr-2" OnClick="RemoveSelectedCons" Disabled="@(GroupB.Where(c => c.IsSelected).Count() == 0)">
                Exclude Constituencies
            </MudButton>
            <MudButton Color="Color.Info"
                       Size="Size.Small"
                       Variant="Variant.Filled"
                       StartIcon="@Icons.Material.Filled.Add"
                       Class="mb-2 mr-2" OnClick="CreateNewStructure">
                Create Constituency
            </MudButton>
        </AuthorizeView>
        <SfGrid @ref="Grid" AllowSorting="true" AllowFiltering="true" DataSource="GroupB" AllowPaging="true" AllowTextWrap="true" Width="100%">
            <GridFilterSettings Type="FilterType.Excel"></GridFilterSettings>
            <GridPageSettings PageSize="10"></GridPageSettings>
            <GridColumns>

                <GridColumn HeaderText="" AutoFit="true">
                    <Template Context="ec">
                        @{
                            var obj = ec as ConstituencyDTO;
                            @if (obj.IsNominationExist)
                            {
                                <span>Nomination Exists</span>
                            }
                            else
                            {
                                <SfCheckBox @bind-Checked="obj.IsSelected"></SfCheckBox>
                            }
                        }
                    </Template>
                </GridColumn>

                <GridColumn AutoFit="true" HeaderText="Code" Field="@nameof(ConstituencyDTO.Code)"></GridColumn>
                <GridColumn AutoFit="true" HeaderText="Constituency" Field="@nameof(ConstituencyDTO.EnglishTitle)" DisableHtmlEncode="false"></GridColumn>
                <GridColumn AutoFit="true" HeaderText="Province" Field="@nameof(ConstituencyDTO.Province)" DisableHtmlEncode="false"></GridColumn>
                <GridColumn AutoFit="true" Visible="state.state.ElectionType == ElectionType.LocalBody" HeaderText="Division" Field="@nameof(ConstituencyDTO.Division)" DisableHtmlEncode="false"></GridColumn>
                <GridColumn AutoFit="true" HeaderText="District" Field="@nameof(ConstituencyDTO.District)" DisableHtmlEncode="false"></GridColumn>
                <GridColumn AutoFit="true" HeaderText="Region" Field="@nameof(ConstituencyDTO.Region)" DisableHtmlEncode="false"></GridColumn>
                @*<GridColumn HeaderText="Town"  Field="Town" DisableHtmlEncode="false"></GridColumn>
                    <GridColumn HeaderText="UC"  Field="UC" DisableHtmlEncode="false"></GridColumn>*@
                <GridColumn HeaderText="Town/Tehsil/City" AutoFit="true" Field="@nameof(ConstituencyDTO.Town)" DisableHtmlEncode="false"></GridColumn>
                <GridColumn HeaderText="Tier 2" AutoFit="true" Field="@nameof(ConstituencyDTO.UC)" DisableHtmlEncode="false" Visible="@(constTypeId == (int)StructureType.Ward)"></GridColumn>
                <GridColumn HeaderText="" Width="110px">
                    <Template Context="cc">
                        @{
                            var obj = cc as ConstituencyDTO;
                            <AuthorizeView Roles="Administrators">
                                <MudFab Size="Size.Small" Color="Color.Primary"
                                        StartIcon="@Icons.Material.Filled.Edit"
                                        OnClick="@(() => OpenEditForm(obj.Id))">
                                </MudFab>
                                <MudFab Color="Color.Error"
                                        Size="Size.Small"
                                        StartIcon="@Icons.Material.Filled.Delete"
                                        OnClick="@(() => DeleteConstituency(obj.Id))">
                                </MudFab>
                            </AuthorizeView>
                        }
                    </Template>
                </GridColumn>
            </GridColumns>
        </SfGrid>
    </div>
</section>


<style>
    #listbox1 {
        width: 48%;
        float: left;
    }

    #listbox2 {
        width: 48%;
        float: right;
    }
</style>