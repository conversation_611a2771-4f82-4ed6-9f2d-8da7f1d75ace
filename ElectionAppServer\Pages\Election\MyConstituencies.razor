﻿@page "/myconstituencies"
@inject UserManager<ApplicationUser> _UserManager
@inject RoleManager<IdentityRole> _RoleManager
@attribute [Authorize(Roles = "Administrators")]
@using FilterType = Syncfusion.Blazor.Grids.FilterType
@inherits OwningComponentBase<ElectionResultsDataService>
@*<PageCaption Title="Constituencies"></PageCaption>*@
<MudText Typo="Typo.h5">Constituencies</MudText>


<SfToast @ref="ToastObj" Title="Alert" ShowProgressBar="true" Timeout="3000">
    <ToastPosition X="Right" Y="Bottom"></ToastPosition>
</SfToast>

<section>

    @if (objList == null)
    {
        <p>
            <em>Loading...</em>
        </p>
    }
    else
    {
        <SfGrid DataSource="@objList" AllowPaging="true" AllowSorting="true" AllowFiltering="true" @ref="Grid" AllowTextWrap="true" Width="100%">
            @*<GridEvents QueryCellInfo="CustomizeCell" TValue="ConstituencyDTO"></GridEvents>*@
            <GridFilterSettings Type="FilterType.Excel"></GridFilterSettings>
            <GridTextWrapSettings WrapMode="WrapMode.Content"></GridTextWrapSettings>
            <GridColumns>
                <GridColumn AutoFit="true" HeaderText="Assembly" Field="Assembly"></GridColumn>
                <GridColumn AutoFit="true" HeaderText="Constituecy" Field="Constituecy"></GridColumn>
                @*<GridColumn HeaderText="Code" Field="Code"></GridColumn>*@
                <GridColumn AutoFit="true" HeaderText="Seat Type" Field="SeatType"></GridColumn>
                <GridColumn AutoFit="true" HeaderText="Male Voters" Field="MaleVoters"></GridColumn>
                <GridColumn AutoFit="true" HeaderText="Female Voters" Field="FemaleVoters"></GridColumn>
                <GridColumn AutoFit="true" HeaderText="Total Votes" Field="TotalVotes"></GridColumn>
                <GridColumn AutoFit="true" HeaderText="Polling Statiosn" Field="TotalPollingStatiosn"></GridColumn>
                <GridColumn AutoFit="true" HeaderText="Nominations" Field="TotalNominations"></GridColumn>
                <GridColumn Width="100px" HeaderText="Action">
                    <Template Context="cc">
                        @{
                            if (cc is MyConstitencyDTO obj)
                            {
                                var link = $"/results/{obj.AssemblyId}/{obj.StructureId}/{obj.SeatTypeId}";
                                <a href="@link">Results</a>
                            }
                        }
                    </Template>
                </GridColumn>
            </GridColumns>
        </SfGrid>
    }
</section>

@code {

    // AuthenticationState is available as a CascadingParameter
    [CascadingParameter] private Task<AuthenticationState> authenticationStateTask { get; set; }

    private SfToast ToastObj;
    private List<MyConstitencyDTO> objList = new();
    public SfGrid<MyConstitencyDTO> Grid { get; set; }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        //return base.OnAfterRenderAsync(firstRender);
        var user = (await authenticationStateTask).User;
        //var users = _UserManager.Users.Where(cc => cc.UserName == user.Identity.Name).Select(x => new ApplicationUserDTO
        //{
        //   Id = x.Id,
        //   UserNameId = x.UserName,
        //   FullName = x.FullName,
        //   Address = x.Address,
        //   PhoneNumber = x.PhoneNumber,
        //   PasswordHash = "*****",
        //   Gender = x.Gender,
        //   District = x.District
        //}).ToList();

        if (firstRender)
        {
            if (user.Identity is { IsAuthenticated: true })
            {
                var userInfo = (from aa in _UserManager.Users
                    where aa.UserName == user.Identity.Name
                    select new ApplicationUserDTO
                    {
                        Id = aa.Id
                    }).FirstOrDefault();

                //var dd =  await ProtectedSessionStore.GetAsync<ElectionStateDTO>("election");
                var dd = await localStorage.GetAsync<ElectionStateDTO>(await confreader.GetSettingValue("electionkey"));
                if (userInfo != null && dd.Value != null)
                    objList = await Service.GetMyConstituencies(userInfo.Id, dd.Value.ElectionId, dd.Value.PhaseId);
                state.SetState(dd.Value);
                try
                {
                    await Grid.Refresh();
                }
                catch (Exception)
                {
                    // ignored
                }
            }
            else
            {
                try
                {
                    //await ProtectedSessionStore.DeleteAsync("election");

                    await localStorage.DeleteAsync(await confreader.GetSettingValue("electionkey"));
                    state.SetState(new ElectionStateDTO { ElectionId = 0, ElectionTitle = "", PhaseId = 0, PhaseTitle = "" });
                }
                catch (Exception)
                {
                    // ignored
                }

                NavigationManager.NavigateTo("");
            }

            StateHasChanged();
        }
    }

    //protected override async Task OnInitializedAsync()
    //{

    //}

}