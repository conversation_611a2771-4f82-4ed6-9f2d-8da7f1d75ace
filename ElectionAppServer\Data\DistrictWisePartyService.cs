﻿using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using Dapper;
using ElectionAppServer.DTO;
using Microsoft.EntityFrameworkCore;

namespace ElectionAppServer.Data;

public class DistrictWisePartyService
{
    private readonly IDbContextFactory<ApplicationDbContext> dcf;

    public DistrictWisePartyService(IDbContextFactory<ApplicationDbContext> dcf)
    {
        this.dcf = dcf;
    }

    public Task<List<GeneralItemDTO>> GetElectionDistricts(int phaseId)
    {
        using var dc = dcf.CreateDbContext();
        var con = dc.Database.GetDbConnection();
        var parm = new { PhaseId = phaseId };
        var res = con.Query<GeneralItemDTO>(
                "app.GetPhaseWiseDistricts",
                parm,
                commandType: CommandType.StoredProcedure)
            .ToList();
        return Task.FromResult(res);
    }

    public Task<List<GeneralItemDTO>> GetPhaseSeatTypes(int phaseId)
    {
        using var dc = dcf.CreateDbContext();
        var con = dc.Database.GetDbConnection();
        var parm = new { PhaseId = phaseId };
        var res = con.Query<GeneralItemDTO>(
                "app.GetPhaseSeatTypes",
                parm,
                commandType: CommandType.StoredProcedure)
            .ToList();
        return Task.FromResult(res);
    }


    public Task<List<GeneralItemDTO>> GetDistrictParies(int phaseId, int districtid)
    {
        using var dc = dcf.CreateDbContext();
        var con = dc.Database.GetDbConnection();
        var parm = new { PhaseId = phaseId, DistrictId = districtid };
        var res = con.Query<GeneralItemDTO>(
                "app.GetDistrictParties",
                parm,
                commandType: CommandType.StoredProcedure)
            .ToList();
        return Task.FromResult(res);
    }

    public Task<List<DistrictPartyDto>> GetCurrentDistrictWiseParties(int phaseId, int districtId, int seatTypeId)
    {
        using var dc = dcf.CreateDbContext();
        var con = dc.Database.GetDbConnection();
        var parm = new { PhaseId = phaseId, DistrictId = districtId, SeatTypeId = seatTypeId };
        var res = con.Query<DistrictPartyDto>(
                "app.GetCurrentDistrictWiseParites",
                parm,
                commandType: CommandType.StoredProcedure)
            .ToList();
        return Task.FromResult(res);
    }

    public Task<string> UpdatePartySortOrder(int id, int sortOrder)
    {
        using var dc = dcf.CreateDbContext();
        var con = dc.Database.GetDbConnection();
        var pram = new { Id = id, SortOrder = sortOrder };
        con.Execute("app.UpdatePartySortOrder", pram, commandType: CommandType.StoredProcedure);
        //con.Query("app.UpdatePartySortOrder", pram, commandType: System.Data.CommandType.StoredProcedure);
        return Task.FromResult("OK");
    }
}