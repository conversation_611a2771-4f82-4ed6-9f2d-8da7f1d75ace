﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace ElectionAppServer.Migrations
{
	public partial class UpdateNomination : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropPrimaryKey(
				 name: "PK_Nominations",
				 table: "Nominations");

			migrationBuilder.AddColumn<int>(
				 name: "ElectionPhaseId",
				 table: "Nominations",
				 nullable: false,
				 defaultValue: 0);

			migrationBuilder.AddColumn<int>(
				 name: "PartyId",
				 table: "Nominations",
				 nullable: false,
				 defaultValue: 0);

			migrationBuilder.AddColumn<int>(
				 name: "SymbolId",
				 table: "Nominations",
				 nullable: false,
				 defaultValue: 0);

			migrationBuilder.AddColumn<int>(
				 name: "Votes",
				 table: "Nominations",
				 nullable: true);

			migrationBuilder.AddColumn<int>(
				 name: "Weight",
				 table: "Nominations",
				 nullable: true);

			migrationBuilder.AddPrimaryKey(
				 name: "PK_Nominations",
				 table: "Nominations",
				 columns: new[] { "ElectionAssemblyId", "StructureId", "SeatTypeId", "CandidteId", "ElectionPhaseId" });

			migrationBuilder.CreateIndex(
				 name: "IX_Nominations_ElectionPhaseId",
				 table: "Nominations",
				 column: "ElectionPhaseId");

			migrationBuilder.CreateIndex(
				 name: "IX_Nominations_PartyId",
				 table: "Nominations",
				 column: "PartyId");

			migrationBuilder.CreateIndex(
				 name: "IX_Nominations_SymbolId",
				 table: "Nominations",
				 column: "SymbolId");

			migrationBuilder.AddForeignKey(
				 name: "FK_Nominations_ElectionPhases_ElectionPhaseId",
				 table: "Nominations",
				 column: "ElectionPhaseId",
				 principalTable: "ElectionPhases",
				 principalColumn: "Id",
				 onDelete: ReferentialAction.Cascade);

			migrationBuilder.AddForeignKey(
				 name: "FK_Nominations_Parties_PartyId",
				 table: "Nominations",
				 column: "PartyId",
				 principalTable: "Parties",
				 principalColumn: "Id",
				 onDelete: ReferentialAction.Restrict);

			migrationBuilder.AddForeignKey(
				 name: "FK_Nominations_Symbols_SymbolId",
				 table: "Nominations",
				 column: "SymbolId",
				 principalTable: "Symbols",
				 principalColumn: "Id",
				 onDelete: ReferentialAction.Restrict);
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropForeignKey(
				 name: "FK_Nominations_ElectionPhases_ElectionPhaseId",
				 table: "Nominations");

			migrationBuilder.DropForeignKey(
				 name: "FK_Nominations_Parties_PartyId",
				 table: "Nominations");

			migrationBuilder.DropForeignKey(
				 name: "FK_Nominations_Symbols_SymbolId",
				 table: "Nominations");

			migrationBuilder.DropPrimaryKey(
				 name: "PK_Nominations",
				 table: "Nominations");

			migrationBuilder.DropIndex(
				 name: "IX_Nominations_ElectionPhaseId",
				 table: "Nominations");

			migrationBuilder.DropIndex(
				 name: "IX_Nominations_PartyId",
				 table: "Nominations");

			migrationBuilder.DropIndex(
				 name: "IX_Nominations_SymbolId",
				 table: "Nominations");

			migrationBuilder.DropColumn(
				 name: "ElectionPhaseId",
				 table: "Nominations");

			migrationBuilder.DropColumn(
				 name: "PartyId",
				 table: "Nominations");

			migrationBuilder.DropColumn(
				 name: "SymbolId",
				 table: "Nominations");

			migrationBuilder.DropColumn(
				 name: "Votes",
				 table: "Nominations");

			migrationBuilder.DropColumn(
				 name: "Weight",
				 table: "Nominations");

			migrationBuilder.AddPrimaryKey(
				 name: "PK_Nominations",
				 table: "Nominations",
				 columns: new[] { "ElectionAssemblyId", "StructureId", "SeatTypeId", "CandidteId" });
		}
	}
}
