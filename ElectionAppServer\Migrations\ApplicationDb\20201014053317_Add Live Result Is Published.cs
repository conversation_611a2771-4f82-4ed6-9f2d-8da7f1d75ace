﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace ElectionAppServer.Migrations.ApplicationDb
{
	public partial class AddLiveResultIsPublished : Migration
	{
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.AddColumn<bool>(
				 name: "IsPublished",
				 table: "LiveResults",
				 type: "bit",
				 nullable: false,
				 defaultValue: false);
		}

		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropColumn(
				 name: "IsPublished",
				 table: "LiveResults");
		}
	}
}
