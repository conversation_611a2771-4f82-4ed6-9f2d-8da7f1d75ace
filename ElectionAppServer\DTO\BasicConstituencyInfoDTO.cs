﻿using System.ComponentModel.DataAnnotations;

namespace ElectionAppServer.DTO;

public class BasicConstituencyInfoDTO
{
    public int Id { get; set; }

    [Required] [StringLength(10)] public string Code { get; set; }

    [Required] [StringLength(200)] public string EnglishTitle { get; set; }

    [Required] [StringLength(200)] public string UrduTitle { get; set; }

    public string Province { get; set; }
    public string District { get; set; }
    public string Tier1 { get; set; }
    public string Tier2 { get; set; }
    public int? MaleVoters { get; set; }
    public int? FemaleVoters { get; set; }
    public int? YouthVoters { get; set; }
    public int? TotalPollingStations { get; set; }
    public int? Population { get; set; }
    public string ImportantAreas { get; set; }
    public string Problems { get; set; }
    public string Trivia { get; set; }
    public string GeneralTrivia { get; set; }
    public string AssemblyTitle { get; set; }
}