﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace ElectionAppServer.Migrations.ApplicationDb
{
    /// <inheritdoc />
    public partial class AddPhasewisePollingSchemes : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "PollingScheme",
                columns: table => new
                {
                    PhaseId = table.Column<int>(type: "int", nullable: false),
                    StructureId = table.Column<int>(type: "int", nullable: false),
                    MaleVoters = table.Column<int>(type: "int", nullable: true),
                    FemaleVoters = table.Column<int>(type: "int", nullable: true),
                    TotalPollingStations = table.Column<int>(type: "int", nullable: true),
                    Languages = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    Castes = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    Trivia = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: true),
                    GeneralTrivia = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: true),
                    ImportantPoliticalPersonalities = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: true),
                    Problems = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: true),
                    ImportantAreas = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: true),
                    RuralAreaPer = table.Column<float>(type: "real", nullable: true),
                    UrbanAreaPer = table.Column<float>(type: "real", nullable: true),
                    Population = table.Column<int>(type: "int", nullable: true),
                    MajorityIncomeSource = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: true),
                    LiteracyRate = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: true),
                    AverageHouseholdIncome = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: true),
                    Area = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    ModifiedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    CreatedBy = table.Column<string>(type: "nvarchar(450)", maxLength: 450, nullable: true),
                    ModifiedBy = table.Column<string>(type: "nvarchar(450)", maxLength: 450, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PollingScheme", x => new { x.StructureId, x.PhaseId });
                    table.ForeignKey(
                        name: "FK_PollingScheme_ElectionPhases_PhaseId",
                        column: x => x.PhaseId,
                        principalTable: "ElectionPhases",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_PollingScheme_Structures_StructureId",
                        column: x => x.StructureId,
                        principalTable: "Structures",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateIndex(
                name: "IX_PollingScheme_PhaseId",
                table: "PollingScheme",
                column: "PhaseId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "PollingScheme");
        }
    }
}
